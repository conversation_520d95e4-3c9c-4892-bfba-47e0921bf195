using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace Levelbuild.Domain.StorageEntities.ContextFactories;

/// <summary>
/// This is here ONLY to support the generation of migrations via the ef command line utility.
/// IT SHALL NOT BE USED FROM WITHIN ANY OTHER CODE.
/// </summary>
[Obsolete("Please use PostgresStorageDatabaseContextFactory instead.", true)]
public class DesignPostgresStorageDatabaseContextFactory : IDesignTimeDbContextFactory<PostgresStorageDatabaseContext>
{
	public PostgresStorageDatabaseContext CreateDbContext(string[] args)
	{
		var optionsBuilder = new DbContextOptionsBuilder<PostgresStorageDatabaseContext>();
		optionsBuilder.UseNpgsql("YourConnectionStringHere");

		return new PostgresStorageDatabaseContext(optionsBuilder.Options);
	}
}
