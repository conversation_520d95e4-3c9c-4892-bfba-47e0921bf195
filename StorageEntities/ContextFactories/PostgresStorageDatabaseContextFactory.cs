using Levelbuild.Domain.StorageEntities.Features.Enum;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.DependencyInjection;

namespace Levelbuild.Domain.StorageEntities.ContextFactories;

public class PostgresStorageDatabaseContextFactory : IDbContextFactory<StorageDatabaseContext>
{
	private IServiceProvider _serviceProvider;

	public PostgresStorageDatabaseContextFactory(IServiceProvider serviceProvider)
	{
		_serviceProvider = serviceProvider;
	}

	public StorageDatabaseContext CreateDbContext()
	{
		return ActivatorUtilities.CreateInstance<StorageDatabaseContext>(_serviceProvider, SqlType.Postgres);
	}
}