using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics.CodeAnalysis;
using System.Security.Cryptography;
using System.Text;
using Newtonsoft.Json;
using SqlKata;

namespace Levelbuild.Domain.StorageEntities.Features.Dto;

public class StorageSchemaChangeError
{
	[Key]
	[ExcludeFromCodeCoverage]
	[System.ComponentModel.DataAnnotations.Key]
	[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
	public long Id { get; set; }

	[ForeignKey("Hash")]
	public string StorageSchemaChangeHash { get; set; }

	[ForeignKey("Identifier")]
	public string StorageContextIdentifier { get; set; }

	public DateTime DateTime { get; set; }

	#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
	protected StorageSchemaChangeError()
		#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
	{
	}

	public StorageSchemaChangeError(string storageSchemaChangeHash, string storageContextIdentifier)
	{
		StorageSchemaChangeHash = storageSchemaChangeHash;
		StorageContextIdentifier = storageContextIdentifier;
		DateTime = DateTime.UtcNow;
	}
}