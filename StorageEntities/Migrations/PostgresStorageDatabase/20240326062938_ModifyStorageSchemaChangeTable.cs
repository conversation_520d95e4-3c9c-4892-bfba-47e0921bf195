using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StorageEntities.Migrations.PostgresStorageDatabase
{
	/// <inheritdoc />
	public partial class ModifyStorageSchemaChangeTable : Migration
	{
		/// <inheritdoc />
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.RenameColumn(
				name: "ChangeUser",
				table: "StorageSchemaChange",
				newName: "User");

			migrationBuilder.RenameColumn(
				name: "ChangeHash",
				table: "StorageSchemaChange",
				newName: "Hash");

			migrationBuilder.RenameColumn(
				name: "ChangeDateTime",
				table: "StorageSchemaChange",
				newName: "DateTime");
		}

		/// <inheritdoc />
		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.RenameColumn(
				name: "User",
				table: "StorageSchemaChange",
				newName: "ChangeUser");

			migrationBuilder.RenameColumn(
				name: "Hash",
				table: "StorageSchemaChange",
				newName: "ChangeHash");

			migrationBuilder.RenameColumn(
				name: "DateTime",
				table: "StorageSchemaChange",
				newName: "ChangeDateTime");
		}
	}
}