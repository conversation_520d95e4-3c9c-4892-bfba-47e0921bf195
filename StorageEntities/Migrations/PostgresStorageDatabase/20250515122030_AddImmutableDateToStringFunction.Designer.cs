// <auto-generated />
using System;
using Levelbuild.Domain.StorageEntities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Levelbuild.Domain.StorageEntities.Migrations.PostgresStorageDatabase
{
    [DbContext(typeof(PostgresStorageDatabaseContext))]
    [Migration("20250515122030_AddImmutableDateToStringFunction")]
    partial class AddImmutableDateToStringFunction
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "uuid-ossp");
            NpgsqlModelBuilderExtensions.UseIdentityAlwaysColumns(modelBuilder);

            modelBuilder.Entity("Levelbuild.Domain.StorageEntities.Features.Dto.StorageContextOrm", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("DatabaseConnectionString")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("DatabaseType")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("ElasticConnectionString")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FilestoreConnectionString")
                        .HasColumnType("text");

                    b.Property<string>("FilestoreContainer")
                        .HasColumnType("text");

                    b.Property<string>("FilestorePassword")
                        .HasColumnType("text");

                    b.Property<int>("FilestoreType")
                        .HasColumnType("integer");

                    b.Property<string>("FilestoreUsername")
                        .HasColumnType("text");

                    b.Property<string>("Identifier")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Language")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("MongoDbConnectionString")
                        .HasColumnType("text");

                    b.Property<bool>("S3PathStyleUrl")
                        .HasColumnType("boolean");

                    b.Property<string>("StoragePath")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TemporaryPath")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("StorageContext");
                });

            modelBuilder.Entity("Levelbuild.Domain.StorageEntities.Features.Dto.StorageFieldDefinitionOrm", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<bool>("CustomerSpecific")
                        .HasColumnType("boolean");

                    b.Property<int>("DecimalPlaces")
                        .HasColumnType("integer");

                    b.Property<string>("DefaultValueJson")
                        .HasColumnType("text");

                    b.Property<bool>("Encrypted")
                        .HasColumnType("boolean");

                    b.Property<bool>("ExcludeFromFulltext")
                        .HasColumnType("boolean");

                    b.Property<string>("ExternalName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("FulltextIndexingType")
                        .HasColumnType("integer");

                    b.Property<string[]>("Languages")
                        .IsRequired()
                        .HasColumnType("text[]");

                    b.Property<int>("Length")
                        .HasColumnType("integer");

                    b.Property<bool>("LookupCheck")
                        .HasColumnType("boolean");

                    b.Property<string>("LookupSource")
                        .HasColumnType("text");

                    b.Property<string>("LookupSourceField")
                        .HasColumnType("text");

                    b.Property<string>("LookupSourceMappingTable")
                        .HasColumnType("text");

                    b.Property<bool>("MultiValue")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("Nullable")
                        .HasColumnType("boolean");

                    b.Property<bool>("PrimaryKey")
                        .HasColumnType("boolean");

                    b.Property<bool>("Readonly")
                        .HasColumnType("boolean");

                    b.Property<long>("StorageIndexDefinitionId")
                        .HasColumnType("bigint");

                    b.Property<bool>("SystemField")
                        .HasColumnType("boolean");

                    b.Property<bool>("Translatable")
                        .HasColumnType("boolean");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<bool>("Unique")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("StorageIndexDefinitionId", "Name")
                        .IsUnique();

                    b.ToTable("StorageFieldDefinition");
                });

            modelBuilder.Entity("Levelbuild.Domain.StorageEntities.Features.Dto.StorageIndexDefinition", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<long?>("CurrentStoragePathId")
                        .HasColumnType("bigint");

                    b.Property<bool>("CustomerSpecific")
                        .HasColumnType("boolean");

                    b.Property<string>("ExternalName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("FulltextSearch")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsView")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("StoreFileContent")
                        .HasColumnType("boolean");

                    b.Property<bool>("StoreRevisions")
                        .HasColumnType("boolean");

                    b.Property<string>("ViewDefinition")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CurrentStoragePathId");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("StorageIndexDefinition");
                });

            modelBuilder.Entity("Levelbuild.Domain.StorageEntities.Features.Dto.StorageLink", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("text");

                    b.Property<string>("ElementId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("LinkedElementId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("LinkedIndexDefinitionId")
                        .HasColumnType("bigint");

                    b.Property<long?>("LinkedRevisionNumber")
                        .HasColumnType("bigint");

                    b.Property<long>("StorageIndexDefinitionId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("StorageLink");
                });

            modelBuilder.Entity("Levelbuild.Domain.StorageEntities.Features.Dto.StorageMigration", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<int>("Id"));

                    b.Property<string>("MigrationDesc")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("MigrationDone")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("MigrationDoneTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MigrationKey")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("MigrationKey")
                        .IsUnique();

                    b.ToTable("StorageMigration");
                });

            modelBuilder.Entity("Levelbuild.Domain.StorageEntities.Features.Dto.StoragePath", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreateDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("StorageIndexDefinitionId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("StorageIndexDefinitionId", "Path")
                        .IsUnique();

                    b.ToTable("StoragePath");
                });

            modelBuilder.Entity("Levelbuild.Domain.StorageEntities.Features.Dto.StorageSchemaChange", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("DataSourceName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DestinationObject")
                        .HasColumnType("text");

                    b.Property<string>("Hash")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Operation")
                        .HasColumnType("integer");

                    b.Property<string>("SourceObject")
                        .HasColumnType("text");

                    b.Property<string>("User")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("StorageSchemaChange");
                });

            modelBuilder.Entity("Levelbuild.Domain.StorageEntities.Features.Dto.StorageSchemaChangeError", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("StorageContextIdentifier")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("StorageSchemaChangeHash")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("StorageSchemaChangeError");
                });

            modelBuilder.Entity("Levelbuild.Domain.StorageEntities.Features.Dto.StorageTempFile", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreateDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ElementId")
                        .HasColumnType("text");

                    b.Property<DateTime>("FileDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FileHash")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FileId")
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("character varying(40)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("FileType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("RevisionNumber")
                        .HasColumnType("bigint");

                    b.Property<long>("StorageIndexDefinitionId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("StorageIndexDefinitionId");

                    b.ToTable("StorageTempFile");
                });

            modelBuilder.Entity("Levelbuild.Domain.StorageEntities.Features.Dto.StorageFieldDefinitionOrm", b =>
                {
                    b.HasOne("Levelbuild.Domain.StorageEntities.Features.Dto.StorageIndexDefinition", null)
                        .WithMany("Fields")
                        .HasForeignKey("StorageIndexDefinitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Levelbuild.Domain.StorageEntities.Features.Dto.StorageIndexDefinition", b =>
                {
                    b.HasOne("Levelbuild.Domain.StorageEntities.Features.Dto.StoragePath", "CurrentStoragePath")
                        .WithMany()
                        .HasForeignKey("CurrentStoragePathId");

                    b.Navigation("CurrentStoragePath");
                });

            modelBuilder.Entity("Levelbuild.Domain.StorageEntities.Features.Dto.StoragePath", b =>
                {
                    b.HasOne("Levelbuild.Domain.StorageEntities.Features.Dto.StorageIndexDefinition", null)
                        .WithMany("StoragePaths")
                        .HasForeignKey("StorageIndexDefinitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Levelbuild.Domain.StorageEntities.Features.Dto.StorageTempFile", b =>
                {
                    b.HasOne("Levelbuild.Domain.StorageEntities.Features.Dto.StorageIndexDefinition", "StorageIndexDefinition")
                        .WithMany()
                        .HasForeignKey("StorageIndexDefinitionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("StorageIndexDefinition");
                });

            modelBuilder.Entity("Levelbuild.Domain.StorageEntities.Features.Dto.StorageIndexDefinition", b =>
                {
                    b.Navigation("Fields");

                    b.Navigation("StoragePaths");
                });
#pragma warning restore 612, 618
        }
    }
}
