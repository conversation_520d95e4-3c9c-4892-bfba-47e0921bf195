using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Domain.StorageEntities.Migrations.PostgresStorageDatabase
{
    /// <inheritdoc />
    public partial class AddImmutableDateToStringFunction : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
			migrationBuilder.Sql("""
								 CREATE OR REPLACE FUNCTION immutable_date_to_string(timestamp without time zone) RETURNS text AS
								 $$ select TO_CHAR($1, 'YYYY MM DD'); $$
								 LANGUAGE sql immutable;
								 """);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
