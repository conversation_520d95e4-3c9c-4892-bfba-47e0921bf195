using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace StorageEntities.Migrations.PostgresStorageDatabase
{
	/// <inheritdoc />
	public partial class AddStorageSchemaChange : Migration
	{
		/// <inheritdoc />
		protected override void Up(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.CreateTable(
				name: "StorageSchemaChange",
				columns: table => new
				{
					Id = table.Column<long>(type: "bigint", nullable: false)
						.Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
					ChangeHash = table.Column<string>(type: "text", nullable: false),
					Operation = table.Column<int>(type: "integer", nullable: false),
					DataSourceName = table.Column<string>(type: "text", nullable: false),
					SourceObject = table.Column<string>(type: "text", nullable: true),
					DestinationObject = table.Column<string>(type: "text", nullable: true),
					ChangeDateTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
					ChangeUser = table.Column<string>(type: "text", nullable: false)
				},
				constraints: table => { table.PrimaryKey("PK_StorageSchemaChange", x => x.Id); });
		}

		/// <inheritdoc />
		protected override void Down(MigrationBuilder migrationBuilder)
		{
			migrationBuilder.DropTable(
				name: "StorageSchemaChange");
		}
	}
}