<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        
        <RootNamespace>Levelbuild.Migrations.Postgres</RootNamespace>
        <Company>levelbuild AG</Company>
        <PackageId>levelbuild.Postgres</PackageId>
        
        <GenerateDocumentationFile>true</GenerateDocumentationFile>
    </PropertyGroup>


    <!-- local solution reference -->
    <ItemGroup>
        <ProjectReference Include="..\..\WebAppEntities\WebAppEntities.csproj" />
    </ItemGroup>

    
    <ItemGroup>
      <PackageReference Include="ClosedXML" Version="0.105.0-rc" />
      <PackageReference Include="ExcelDataReader" Version="3.8.0-develop00474" />
      <PackageReference Include="ExcelDataReader.DataSet" Version="3.8.0-develop00474" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.11">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
    </ItemGroup>

</Project>
