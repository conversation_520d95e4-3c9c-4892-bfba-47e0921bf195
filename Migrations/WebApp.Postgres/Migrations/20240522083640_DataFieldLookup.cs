using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class DataFieldLookup : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DataFields_DataFields_LookupSourceId",
                table: "DataFields");

            migrationBuilder.DropColumn(
                name: "IsLookupField",
                table: "DataFields");

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "PageViews",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255);

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "Pages",
                type: "text",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255,
                oldNullable: true);

            migrationBuilder.AddColumn<int>(
                name: "FieldType",
                table: "DataFields",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<Guid>(
                name: "LookupDisplayFieldId",
                table: "DataFields",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "VirtualDataFieldId",
                table: "DataFields",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "VirtualLookupFieldId",
                table: "DataFields",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_DataFields_LookupDisplayFieldId",
                table: "DataFields",
                column: "LookupDisplayFieldId");

            migrationBuilder.CreateIndex(
                name: "IX_DataFields_VirtualDataFieldId",
                table: "DataFields",
                column: "VirtualDataFieldId");

            migrationBuilder.CreateIndex(
                name: "IX_DataFields_VirtualLookupFieldId",
                table: "DataFields",
                column: "VirtualLookupFieldId");

            migrationBuilder.AddForeignKey(
                name: "FK_DataFields_DataFields_LookupDisplayFieldId",
                table: "DataFields",
                column: "LookupDisplayFieldId",
                principalTable: "DataFields",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_DataFields_DataFields_VirtualDataFieldId",
                table: "DataFields",
                column: "VirtualDataFieldId",
                principalTable: "DataFields",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_DataFields_DataFields_VirtualLookupFieldId",
                table: "DataFields",
                column: "VirtualLookupFieldId",
                principalTable: "DataFields",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_DataFields_DataSources_LookupSourceId",
                table: "DataFields",
                column: "LookupSourceId",
                principalTable: "DataSources",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DataFields_DataFields_LookupDisplayFieldId",
                table: "DataFields");

            migrationBuilder.DropForeignKey(
                name: "FK_DataFields_DataFields_VirtualDataFieldId",
                table: "DataFields");

            migrationBuilder.DropForeignKey(
                name: "FK_DataFields_DataFields_VirtualLookupFieldId",
                table: "DataFields");

            migrationBuilder.DropForeignKey(
                name: "FK_DataFields_DataSources_LookupSourceId",
                table: "DataFields");

            migrationBuilder.DropIndex(
                name: "IX_DataFields_LookupDisplayFieldId",
                table: "DataFields");

            migrationBuilder.DropIndex(
                name: "IX_DataFields_VirtualDataFieldId",
                table: "DataFields");

            migrationBuilder.DropIndex(
                name: "IX_DataFields_VirtualLookupFieldId",
                table: "DataFields");

            migrationBuilder.DropColumn(
                name: "FieldType",
                table: "DataFields");

            migrationBuilder.DropColumn(
                name: "LookupDisplayFieldId",
                table: "DataFields");

            migrationBuilder.DropColumn(
                name: "VirtualDataFieldId",
                table: "DataFields");

            migrationBuilder.DropColumn(
                name: "VirtualLookupFieldId",
                table: "DataFields");

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "PageViews",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "Pages",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "text",
                oldNullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsLookupField",
                table: "DataFields",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddForeignKey(
                name: "FK_DataFields_DataFields_LookupSourceId",
                table: "DataFields",
                column: "LookupSourceId",
                principalTable: "DataFields",
                principalColumn: "Id");
        }
    }
}
