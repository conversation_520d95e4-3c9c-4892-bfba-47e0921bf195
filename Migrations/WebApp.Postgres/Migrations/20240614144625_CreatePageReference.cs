using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class CreatePageReference : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "CreatePageId",
                table: "MultiDataPage",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_MultiDataPage_CreatePageId",
                table: "MultiDataPage",
                column: "CreatePageId");

            migrationBuilder.AddForeignKey(
                name: "FK_MultiDataPage_CreatePage_CreatePageId",
                table: "MultiDataPage",
                column: "CreatePageId",
                principalTable: "CreatePage",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MultiDataPage_CreatePage_CreatePageId",
                table: "MultiDataPage");

            migrationBuilder.DropIndex(
                name: "IX_MultiDataPage_CreatePageId",
                table: "MultiDataPage");

            migrationBuilder.DropColumn(
                name: "CreatePageId",
                table: "MultiDataPage");
        }
    }
}
