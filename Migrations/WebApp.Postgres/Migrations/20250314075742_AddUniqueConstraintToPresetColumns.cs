using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class AddUniqueConstraintToPresetColumns : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ExcelPresetColumns_DataFieldId",
                table: "ExcelPresetColumns");

            migrationBuilder.CreateIndex(
                name: "IX_ExcelPresetColumns_DataFieldId_DataExcelPresetId",
                table: "ExcelPresetColumns",
                columns: new[] { "DataFieldId", "DataExcelPresetId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ExcelPresetColumns_DataFieldId_DataExcelPresetId",
                table: "ExcelPresetColumns");

            migrationBuilder.CreateIndex(
                name: "IX_ExcelPresetColumns_DataFieldId",
                table: "ExcelPresetColumns",
                column: "DataFieldId");
        }
    }
}
