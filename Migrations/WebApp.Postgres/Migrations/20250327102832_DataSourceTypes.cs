using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class DataSourceTypes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "AnnotationGroupByFieldId",
                table: "DataSources",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "ParentDataSourceId",
                table: "DataSources",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Type",
                table: "DataSources",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<bool>(
                name: "AutoGenerated",
                table: "DataFields",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateIndex(
                name: "IX_DataSources_AnnotationGroupByFieldId",
                table: "DataSources",
                column: "AnnotationGroupByFieldId");

            migrationBuilder.CreateIndex(
                name: "IX_DataSources_ParentDataSourceId",
                table: "DataSources",
                column: "ParentDataSourceId");

            migrationBuilder.AddForeignKey(
                name: "FK_DataSources_DataFields_AnnotationGroupByFieldId",
                table: "DataSources",
                column: "AnnotationGroupByFieldId",
                principalTable: "DataFields",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_DataSources_DataSources_ParentDataSourceId",
                table: "DataSources",
                column: "ParentDataSourceId",
                principalTable: "DataSources",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DataSources_DataFields_AnnotationGroupByFieldId",
                table: "DataSources");

            migrationBuilder.DropForeignKey(
                name: "FK_DataSources_DataSources_ParentDataSourceId",
                table: "DataSources");

            migrationBuilder.DropIndex(
                name: "IX_DataSources_AnnotationGroupByFieldId",
                table: "DataSources");

            migrationBuilder.DropIndex(
                name: "IX_DataSources_ParentDataSourceId",
                table: "DataSources");

            migrationBuilder.DropColumn(
                name: "AnnotationGroupByFieldId",
                table: "DataSources");

            migrationBuilder.DropColumn(
                name: "ParentDataSourceId",
                table: "DataSources");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "DataSources");

            migrationBuilder.DropColumn(
                name: "AutoGenerated",
                table: "DataFields");
        }
    }
}
