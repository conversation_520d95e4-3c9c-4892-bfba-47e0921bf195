using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class AddMultiPageFilterFieldEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "MultiPageFilterFields",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PageId = table.Column<Guid>(type: "uuid", nullable: false),
                    FieldId = table.Column<Guid>(type: "uuid", nullable: false),
                    Position = table.Column<int>(type: "integer", nullable: false),
                    DisplayInPanel = table.Column<bool>(type: "boolean", nullable: false),
                    MultiValue = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MultiPageFilterFields", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MultiPageFilterFields_DataFields_FieldId",
                        column: x => x.FieldId,
                        principalTable: "DataFields",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MultiPageFilterFields_MultiDataPage_PageId",
                        column: x => x.PageId,
                        principalTable: "MultiDataPage",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_MultiPageFilterFields_FieldId",
                table: "MultiPageFilterFields",
                column: "FieldId");

            migrationBuilder.CreateIndex(
                name: "IX_MultiPageFilterFields_PageId",
                table: "MultiPageFilterFields",
                column: "PageId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MultiPageFilterFields");
        }
    }
}
