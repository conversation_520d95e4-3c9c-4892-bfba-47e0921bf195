using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class CreatePageConfig : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ButtonOneLabel",
                table: "CreatePage");

            migrationBuilder.RenameColumn(
                name: "ButtonTwoLabel",
                table: "CreatePage",
                newName: "SaveButtonLabel");

            migrationBuilder.AddColumn<Guid>(
                name: "Revision",
                table: "PageViews",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "Revision",
                table: "Pages",
                type: "uuid",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Revision",
                table: "PageViews");

            migrationBuilder.DropColumn(
                name: "Revision",
                table: "Pages");

            migrationBuilder.RenameColumn(
                name: "SaveButtonLabel",
                table: "CreatePage",
                newName: "ButtonTwoLabel");

            migrationBuilder.AddColumn<string>(
                name: "ButtonOneLabel",
                table: "CreatePage",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");
        }
    }
}
