using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class SetNullToDueDateInWorkflows : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Workflows_DataFields_DueDateFieldId",
                table: "Workflows");

            migrationBuilder.AddForeignKey(
                name: "FK_Workflows_DataFields_DueDateFieldId",
                table: "Workflows",
                column: "DueDateFieldId",
                principalTable: "DataFields",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Workflows_DataFields_DueDateFieldId",
                table: "Workflows");
			
            migrationBuilder.AddForeignKey(
                name: "FK_Workflows_DataFields_DueDateFieldId",
                table: "Workflows",
                column: "DueDateFieldId",
                principalTable: "DataFields",
                principalColumn: "Id");
        }
    }
}
