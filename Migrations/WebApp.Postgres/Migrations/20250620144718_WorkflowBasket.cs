using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class WorkflowBasket : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_WorkflowNodes_Customers_CustomerId",
                table: "WorkflowNodes");

            migrationBuilder.DropIndex(
                name: "IX_WorkflowNodes_CustomerId",
                table: "WorkflowNodes");

            migrationBuilder.DropColumn(
                name: "CustomerId",
                table: "WorkflowNodes");

            migrationBuilder.AddColumn<Guid>(
                name: "CustomerId",
                table: "Workflows",
                type: "uuid",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "DueDateFieldId",
                table: "Workflows",
                type: "uuid",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Icon",
                table: "WorkflowNodes",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(100)",
                oldMaxLength: 100);

            migrationBuilder.AddColumn<string>(
                name: "Details",
                table: "WorkflowNodes",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TaskName",
                table: "WorkflowNodes",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "MainFieldId",
                table: "DataSources",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "WorkflowEntries",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    WorkflowNodeId = table.Column<Guid>(type: "uuid", nullable: false),
                    ReferenceDataSourceId = table.Column<Guid>(type: "uuid", nullable: false),
                    ReferenceElementId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    ReferenceDisplayValue = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    Details = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    DueDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ReceivedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ExecutionDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ExecutionUserId = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkflowEntries", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WorkflowEntries_DataSources_ReferenceDataSourceId",
                        column: x => x.ReferenceDataSourceId,
                        principalTable: "DataSources",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_WorkflowEntries_Users_ExecutionUserId",
                        column: x => x.ExecutionUserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_WorkflowEntries_WorkflowNodes_WorkflowNodeId",
                        column: x => x.WorkflowNodeId,
                        principalTable: "WorkflowNodes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "UserEntityWorkflowEntryEntity",
                columns: table => new
                {
                    RecipientsId = table.Column<Guid>(type: "uuid", nullable: false),
                    WorkflowEntryEntityId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserEntityWorkflowEntryEntity", x => new { x.RecipientsId, x.WorkflowEntryEntityId });
                    table.ForeignKey(
                        name: "FK_UserEntityWorkflowEntryEntity_Users_RecipientsId",
                        column: x => x.RecipientsId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UserEntityWorkflowEntryEntity_WorkflowEntries_WorkflowEntry~",
                        column: x => x.WorkflowEntryEntityId,
                        principalTable: "WorkflowEntries",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Workflows_CustomerId",
                table: "Workflows",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_Workflows_DueDateFieldId",
                table: "Workflows",
                column: "DueDateFieldId");

            migrationBuilder.CreateIndex(
                name: "IX_DataSources_MainFieldId",
                table: "DataSources",
                column: "MainFieldId");

            migrationBuilder.CreateIndex(
                name: "IX_UserEntityWorkflowEntryEntity_WorkflowEntryEntityId",
                table: "UserEntityWorkflowEntryEntity",
                column: "WorkflowEntryEntityId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkflowEntries_ExecutionUserId",
                table: "WorkflowEntries",
                column: "ExecutionUserId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkflowEntries_ReferenceDataSourceId",
                table: "WorkflowEntries",
                column: "ReferenceDataSourceId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkflowEntries_WorkflowNodeId",
                table: "WorkflowEntries",
                column: "WorkflowNodeId");

            migrationBuilder.AddForeignKey(
                name: "FK_DataSources_DataFields_MainFieldId",
                table: "DataSources",
                column: "MainFieldId",
                principalTable: "DataFields",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Workflows_Customers_CustomerId",
                table: "Workflows",
                column: "CustomerId",
                principalTable: "Customers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Workflows_DataFields_DueDateFieldId",
                table: "Workflows",
                column: "DueDateFieldId",
                principalTable: "DataFields",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DataSources_DataFields_MainFieldId",
                table: "DataSources");

            migrationBuilder.DropForeignKey(
                name: "FK_Workflows_Customers_CustomerId",
                table: "Workflows");

            migrationBuilder.DropForeignKey(
                name: "FK_Workflows_DataFields_DueDateFieldId",
                table: "Workflows");

            migrationBuilder.DropTable(
                name: "UserEntityWorkflowEntryEntity");

            migrationBuilder.DropTable(
                name: "WorkflowEntries");

            migrationBuilder.DropIndex(
                name: "IX_Workflows_CustomerId",
                table: "Workflows");

            migrationBuilder.DropIndex(
                name: "IX_Workflows_DueDateFieldId",
                table: "Workflows");

            migrationBuilder.DropIndex(
                name: "IX_DataSources_MainFieldId",
                table: "DataSources");

            migrationBuilder.DropColumn(
                name: "CustomerId",
                table: "Workflows");

            migrationBuilder.DropColumn(
                name: "DueDateFieldId",
                table: "Workflows");

            migrationBuilder.DropColumn(
                name: "Details",
                table: "WorkflowNodes");

            migrationBuilder.DropColumn(
                name: "TaskName",
                table: "WorkflowNodes");

            migrationBuilder.DropColumn(
                name: "MainFieldId",
                table: "DataSources");

            migrationBuilder.AlterColumn<string>(
                name: "Icon",
                table: "WorkflowNodes",
                type: "character varying(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(100)",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "CustomerId",
                table: "WorkflowNodes",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_WorkflowNodes_CustomerId",
                table: "WorkflowNodes",
                column: "CustomerId");

            migrationBuilder.AddForeignKey(
                name: "FK_WorkflowNodes_Customers_CustomerId",
                table: "WorkflowNodes",
                column: "CustomerId",
                principalTable: "Customers",
                principalColumn: "Id");
        }
    }
}
