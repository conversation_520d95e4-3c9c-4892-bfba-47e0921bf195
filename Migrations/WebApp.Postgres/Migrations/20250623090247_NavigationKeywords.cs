using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class NavigationKeywords : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string[]>(
                name: "Keywords",
                table: "Pages",
                type: "text[]",
                nullable: false,
                defaultValue: new string[0]);

            migrationBuilder.AddColumn<string[]>(
                name: "Keywords",
                table: "Modules",
                type: "text[]",
                nullable: false,
                defaultValue: new string[0]);
			
			migrationBuilder.AddColumn<string[]>(
				name: "Keywords",
				table: "Products",
				type: "text[]",
				nullable: false,
				defaultValue: new string[0]);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Keywords",
                table: "Pages");

            migrationBuilder.DropColumn(
                name: "Keywords",
                table: "Modules");
			
			migrationBuilder.DropColumn(
				name: "Keywords",
				table: "Products");
        }
    }
}
