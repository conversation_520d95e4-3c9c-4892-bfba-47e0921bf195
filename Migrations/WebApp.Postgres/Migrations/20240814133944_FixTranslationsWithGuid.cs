using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class FixTranslationsWithGuid : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
			/*
			 * Fix wrong Translations by removing GUID from key column
			 * Remove duplicates before executing the real update migration
			 */
			migrationBuilder.Sql(@"
				DELETE FROM
				    public.""Translations"" a
				        USING public.""Translations"" b
				WHERE
				    a.""Id"" < b.""Id""
   					AND a.""CultureId"" = b.""CultureId""
					AND a.""SystemTranslation"" = false AND b.""SystemTranslation"" = false
					AND a.""Key"" ~ '^/(\w+?)/.{8}-.{4}-.{4}-.{4}-.{12}/(.*)$'
				    AND regexp_replace(a.""Key"", '/(\w+?)/.{8}-.{4}-.{4}-.{4}-.{12}/(.*)', '/\1/\2', 'g') = regexp_replace(b.""Key"", '/(\w+?)/.{8}-.{4}-.{4}-.{4}-.{12}/(.*)', '/\1/\2', 'g');

				UPDATE public.""Translations"" as MainTranslation SET ""Key"" = regexp_replace(""Key"", '/(\w+?)/.{8}-.{4}-.{4}-.{4}-.{12}/(.*)', '/\1/\2', 'g')
				WHERE MainTranslation.""Key"" ~ '^/(\w+?)/.{8}-.{4}-.{4}-.{4}-.{12}/(.*)$' and 
					MainTranslation.""SystemTranslation"" = false and 
					0 = (
						SELECT COUNT(*)
						FROM public.""Translations"" as SubTranslation 
						WHERE SubTranslation.""CultureId"" = MainTranslation.""CultureId"" AND 
							SubTranslation.""Key"" = regexp_replace(MainTranslation.""Key"", '^/(\w+?)/.{8}-.{4}-.{4}-.{4}-.{12}/(.*)$', '/\1/\2', 'g')
					);

				DELETE FROM public.""Translations""
				WHERE ""Key"" ~ '^/(\w+?)/.{8}-.{4}-.{4}-.{4}-.{12}/(.*)$' and ""SystemTranslation"" = false;
			");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
