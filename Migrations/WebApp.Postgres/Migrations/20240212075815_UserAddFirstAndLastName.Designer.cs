// <auto-generated />
using System;
using System.Collections.Generic;
using Levelbuild.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    [DbContext(typeof(CoreDatabaseContext))]
    [Migration("20240212075815_UserAddFirstAndLastName")]
    partial class UserAddFirstAndLastName
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "uuid-ossp");
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("CustomerEntityUserEntity", b =>
                {
                    b.Property<Guid>("CustomersId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UsersId")
                        .HasColumnType("uuid");

                    b.HasKey("CustomersId", "UsersId");

                    b.HasIndex("UsersId");

                    b.ToTable("CustomerEntityUserEntity");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Customer.CustomerEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RemoteId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Customers");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataField.DataFieldEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DataSourceId")
                        .IsRequired()
                        .HasColumnType("uuid");

                    b.Property<int>("DecimalPlaces")
                        .HasColumnType("integer");

                    b.Property<int>("Length")
                        .HasColumnType("integer");

                    b.Property<bool>("Mandatory")
                        .HasColumnType("boolean");

                    b.Property<bool>("Multi")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("Nullable")
                        .HasColumnType("boolean");

                    b.Property<bool>("Reference")
                        .HasColumnType("boolean");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("SystemField")
                        .HasColumnType("boolean");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<bool>("Unique")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("DataSourceId");

                    b.ToTable("DataFields");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataSource.DataSourceEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("DataStoreId")
                        .IsRequired()
                        .HasColumnType("uuid");

                    b.Property<bool>("Encryption")
                        .HasColumnType("boolean");

                    b.Property<bool>("FulltextSearch")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Responsible")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("StoragePath")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("StoreRevision")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("DataStoreId");

                    b.ToTable("DataSources");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataStoreConfig.DataStoreConfigEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("Enabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Dictionary<string, object>>("Options")
                        .HasColumnType("jsonb");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("DataStoreConfigs");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Localization.CultureEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Cultures");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Localization.TranslationEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("Created")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("CultureId")
                        .HasColumnType("uuid");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModified")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("Responsible")
                        .HasColumnType("text");

                    b.Property<bool>("SystemTranslation")
                        .HasColumnType("boolean");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CultureId", "Key")
                        .IsUnique();

                    b.ToTable("Translations");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.LoggerConfig.LoggerConfigEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<int>("Level")
                        .HasColumnType("integer");

                    b.Property<string>("LogFilePath")
                        .HasColumnType("text");

                    b.Property<bool>("LogToFile")
                        .HasColumnType("boolean");

                    b.Property<string>("LoggerSource")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("SourceIsGroup")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("LoggerSource")
                        .IsUnique();

                    b.ToTable("LoggerConfigs");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.User.UserEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CultureId")
                        .HasColumnType("uuid");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("MainCustomerId")
                        .HasColumnType("uuid");

                    b.Property<string>("RemoteId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CultureId");

                    b.HasIndex("MainCustomerId");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("CustomerEntityUserEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Customer.CustomerEntity", null)
                        .WithMany()
                        .HasForeignKey("CustomersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.User.UserEntity", null)
                        .WithMany()
                        .HasForeignKey("UsersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataField.DataFieldEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataSource.DataSourceEntity", "DataSource")
                        .WithMany()
                        .HasForeignKey("DataSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataSource");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataSource.DataSourceEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataStoreConfig.DataStoreConfigEntity", "DataStore")
                        .WithMany()
                        .HasForeignKey("DataStoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataStore");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Localization.TranslationEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Localization.CultureEntity", "Culture")
                        .WithMany("Translations")
                        .HasForeignKey("CultureId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Culture");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.User.UserEntity", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Localization.CultureEntity", "Culture")
                        .WithMany("Users")
                        .HasForeignKey("CultureId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Levelbuild.Entities.Features.Customer.CustomerEntity", "MainCustomer")
                        .WithMany()
                        .HasForeignKey("MainCustomerId");

                    b.Navigation("Culture");

                    b.Navigation("MainCustomer");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Localization.CultureEntity", b =>
                {
                    b.Navigation("Translations");

                    b.Navigation("Users");
                });
#pragma warning restore 612, 618
        }
    }
}
