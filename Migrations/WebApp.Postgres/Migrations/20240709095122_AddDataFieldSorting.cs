using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class AddDataFieldSorting : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Value",
                table: "Translations",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255);

            migrationBuilder.CreateTable(
                name: "DataFieldSortings",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PageId = table.Column<Guid>(type: "uuid", nullable: false),
                    FieldId = table.Column<Guid>(type: "uuid", nullable: false),
                    Position = table.Column<int>(type: "integer", nullable: false),
                    Direction = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataFieldSortings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DataFieldSortings_DataFields_FieldId",
                        column: x => x.FieldId,
                        principalTable: "DataFields",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DataFieldSortings_MultiDataPage_PageId",
                        column: x => x.PageId,
                        principalTable: "MultiDataPage",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_DataFieldSortings_FieldId",
                table: "DataFieldSortings",
                column: "FieldId");

            migrationBuilder.CreateIndex(
                name: "IX_DataFieldSortings_PageId",
                table: "DataFieldSortings",
                column: "PageId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DataFieldSortings");

            migrationBuilder.AlterColumn<string>(
                name: "Value",
                table: "Translations",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");
        }
    }
}
