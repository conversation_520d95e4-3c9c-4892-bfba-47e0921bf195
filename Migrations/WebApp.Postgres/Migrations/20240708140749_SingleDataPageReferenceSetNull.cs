using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class SingleDataPageReferenceSetNull : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MultiDataPage_CreatePage_CreatePageId",
                table: "MultiDataPage");

            migrationBuilder.DropForeignKey(
                name: "FK_MultiDataPage_SingleDataPage_DetailPageId",
                table: "MultiDataPage");

            migrationBuilder.AddForeignKey(
                name: "FK_MultiDataPage_CreatePage_CreatePageId",
                table: "MultiDataPage",
                column: "CreatePageId",
                principalTable: "CreatePage",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_MultiDataPage_SingleDataPage_DetailPageId",
                table: "MultiDataPage",
                column: "DetailPageId",
                principalTable: "SingleDataPage",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MultiDataPage_CreatePage_CreatePageId",
                table: "MultiDataPage");

            migrationBuilder.DropForeignKey(
                name: "FK_MultiDataPage_SingleDataPage_DetailPageId",
                table: "MultiDataPage");

            migrationBuilder.AddForeignKey(
                name: "FK_MultiDataPage_CreatePage_CreatePageId",
                table: "MultiDataPage",
                column: "CreatePageId",
                principalTable: "CreatePage",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_MultiDataPage_SingleDataPage_DetailPageId",
                table: "MultiDataPage",
                column: "DetailPageId",
                principalTable: "SingleDataPage",
                principalColumn: "Id");
        }
    }
}
