using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Levelbuild.Migrations.Postgres.Migrations
{
    /// <inheritdoc />
    public partial class AddFileIdToFileUploads : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "FileId",
                table: "FileUploads",
                type: "serial",
                nullable: false);
			
			// Populate the FileId column for existing rows
			migrationBuilder.Sql(@"
                DO $$
                BEGIN
                    -- Update existing rows with sequential values
                    ALTER SEQUENCE IF EXISTS ""FileUploads_FileId_seq"" RESTART WITH 1;
                    UPDATE ""FileUploads""
                    SET ""FileId"" = nextval('""FileUploads_FileId_seq""');
                END
                $$;
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "FileId",
                table: "FileUploads");
        }
    }
}
