// <auto-generated />
using System;
using Levelbuild.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Levelbuild.Migrations.SqlServer.Migrations
{
    [DbContext(typeof(CoreDatabaseContext))]
    [Migration("20231123133017_AddDataSource")]
    partial class AddDataSource
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0-preview.7.23375.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("CustomerUser", b =>
                {
                    b.Property<Guid>("CustomersId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UsersId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("CustomersId", "UsersId");

                    b.HasIndex("UsersId");

                    b.ToTable("CustomerUser");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.Customer.Customer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RemoteId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Customers");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataSource.DataSource", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("DataStoreId")
                        .IsRequired()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Encryption")
                        .HasColumnType("bit");

                    b.Property<bool>("FulltextSearch")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Responsible")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StoragePath")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("StoreRevision")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("DataStoreId");

                    b.ToTable("DataSources");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataStoreConfig.DataStoreConfig", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Enabled")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Options")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("DataStoreConfigs");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.LoggerConfig.LoggerConfig", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int>("Level")
                        .HasColumnType("int");

                    b.Property<string>("LogFilePath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("LogToFile")
                        .HasColumnType("bit");

                    b.Property<string>("LoggerSource")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("SourceIsGroup")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("LoggerSource")
                        .IsUnique();

                    b.ToTable("LoggerConfigs");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.User.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("MainCustomerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("RemoteId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("MainCustomerId");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("CustomerUser", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Customer.Customer", null)
                        .WithMany()
                        .HasForeignKey("CustomersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Levelbuild.Entities.Features.User.User", null)
                        .WithMany()
                        .HasForeignKey("UsersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.DataSource.DataSource", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.DataStoreConfig.DataStoreConfig", "DataStore")
                        .WithMany()
                        .HasForeignKey("DataStoreId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataStore");
                });

            modelBuilder.Entity("Levelbuild.Entities.Features.User.User", b =>
                {
                    b.HasOne("Levelbuild.Entities.Features.Customer.Customer", "MainCustomer")
                        .WithMany()
                        .HasForeignKey("MainCustomerId");

                    b.Navigation("MainCustomer");
                });
#pragma warning restore 612, 618
        }
    }
}
