namespace Levelbuild.Domain.GoogleStorageFile;

/// <summary>
/// This class provides two streams: <see cref="WritableStream"/>, <see cref="ReadableStream"/>.
/// They have a shared buffer, the <see cref="WritableStream"/> can only be written to, the <see cref="ReadableStream"/> can only be read.
/// Everything that is being written to the <see cref="WritableStream"/> can be read from the <see cref="ReadableStream"/>.
/// If one of those streams is being closed, the other one will be closed as well.
/// The Close of the <see cref="WritableStream"/> will block until everything that was written to the buffer beforehand was read through the <see cref="ReadableStream"/>.
/// Both streams have to be used in different threads, otherwise deadlocks may occur if the buffer is full and someone tries to write to it or if the buffer is empty and someone tries to read from it.
/// </summary>
public class RingBufferStream
{
	private readonly byte[] _buffer;
	private readonly int _bufferSize;
	private readonly SemaphoreSlim _writeSemaphore;
	private readonly SemaphoreSlim _readSemaphore;
	private int _writePosition;
	private int _readPosition;
	private readonly object _lockObject = new();
	private bool _isWriteStreamClosed;
	private bool _isReadStreamClosed;
	
	/// <summary>
	///
	/// </summary>
	/// <param name="size">The size of the buffer that should be used between <see cref="WritableStream"/> and <see cref="ReadableStream"/></param>
	public RingBufferStream(int size)
	{
		_bufferSize = size;
		_buffer = new byte[size];
		_writeSemaphore = new SemaphoreSlim(size, size);
		_readSemaphore = new SemaphoreSlim(0, size);
		WritableStream = new WritableStreamImpl(this);
		ReadableStream = new ReadableStreamImpl(this);
	}
	
	/// <summary>
	/// Provides a stream where data can only be written to.
	/// Everything that is written here can be read from the <see cref="ReadableStream"/> afterward.
	/// </summary>
	public readonly Stream WritableStream;
	
	/// <summary>
	/// Provides a stream all data that was written to the <see cref="WritableStream"/> can be read from.
	/// </summary>
	public readonly Stream ReadableStream;
	
	private void CheckStreamStatus()
	{
		if (_isWriteStreamClosed && _isReadStreamClosed)
		{
			// Both streams are closed
			_writeSemaphore.Dispose();
			_readSemaphore.Dispose();
		}
	}
	
	private class WritableStreamImpl : Stream
	{
		private readonly RingBufferStream _parent;
		
		public WritableStreamImpl(RingBufferStream parent)
		{
			this._parent = parent;
		}
		
		public override void Write(byte[] buffer, int offset, int count)
		{
			if (_parent._isWriteStreamClosed || _parent._isReadStreamClosed)
				throw new InvalidOperationException("Cannot write to a closed stream");
			while (count > 0)
			{
				int bytesToWrite = Math.Min(count, _parent._bufferSize);
				
				for (int i = 0; i < bytesToWrite; i++)
				{
					_parent._writeSemaphore.Wait();
				}
				
				int spaceAtEnd = _parent._bufferSize - _parent._writePosition;
				int firstPart = Math.Min(bytesToWrite, spaceAtEnd);
				int secondPart = bytesToWrite - firstPart;
				
				Array.Copy(buffer, offset, _parent._buffer, _parent._writePosition, firstPart);
				Array.Copy(buffer, offset + firstPart, _parent._buffer, 0, secondPart);
				
				_parent._writePosition = (_parent._writePosition + bytesToWrite) % _parent._bufferSize;
				offset += bytesToWrite;
				count -= bytesToWrite;
				
				_parent._readSemaphore.Release(bytesToWrite);
			}
		}
		
		public override void Close()
		{
			base.Close();
			
			_parent._isWriteStreamClosed = true;
			
			// allow readable stream to finish
			((ReadableStreamImpl)_parent.ReadableStream).SignalFinishAndWaitForClose();
		}
		
		public override bool CanWrite => true;
		
		public override void Flush()
		{
			
		}
		
		public override long Length => throw new NotSupportedException();
		
		public override long Position
		{
			get => throw new NotSupportedException();
			set => throw new NotSupportedException();
		}
		
		public override bool CanRead => false;
		public override bool CanSeek => false;
		public override long Seek(long offset, SeekOrigin origin) => throw new NotSupportedException();
		public override void SetLength(long value) => throw new NotSupportedException();
		public override int Read(byte[] buffer, int offset, int count) => throw new NotSupportedException();
	}
	
	private class ReadableStreamImpl : Stream
	{
		private readonly RingBufferStream _parent;
		private readonly List<TaskCompletionSource> _closeEvent = new List<TaskCompletionSource>();
		private bool _interruptSend;
		
		public ReadableStreamImpl(RingBufferStream parent)
		{
			this._parent = parent;
		}
		
		public override int Read(byte[] buffer, int offset, int count)
		{
			int totalBytesRead = 0;
			
			while (count > 0)
			{
				if (_parent._isWriteStreamClosed && _parent._readSemaphore.CurrentCount == 0)
				{
					return totalBytesRead;
				}
				
				_parent._readSemaphore.Wait();
				
				int currentCount;
				bool interruptSend;
				
				lock (_parent._lockObject)
				{
					currentCount = _parent._readSemaphore.CurrentCount;
					interruptSend = _interruptSend;
				}
				
				int bytesToRead = Math.Min(count - 1, currentCount);
				
				for (int i = 0; i < bytesToRead; i++)
				{
					_parent._readSemaphore.Wait();
				}
				
				if (!interruptSend)
					bytesToRead++;
				
				if (bytesToRead == 0)
					continue;
				
				int spaceAtEnd = _parent._bufferSize - _parent._readPosition;
				int firstPart = Math.Min(bytesToRead, spaceAtEnd);
				int secondPart = bytesToRead - firstPart;
				
				Array.Copy(_parent._buffer, _parent._readPosition, buffer, offset, firstPart);
				Array.Copy(_parent._buffer, 0, buffer, offset + firstPart, secondPart);
				
				_parent._readPosition = (_parent._readPosition + bytesToRead) % _parent._bufferSize;
				offset += bytesToRead;
				count -= bytesToRead;
				totalBytesRead += bytesToRead;
				
				if (!_parent._isWriteStreamClosed)
					_parent._writeSemaphore.Release(bytesToRead);
			}
			
			return totalBytesRead;
		}
		
		public override void Close()
		{
			base.Close();
			lock (_parent._lockObject)
			{
				_parent._isReadStreamClosed = true;
				foreach (var taskCompletionSource in _closeEvent)
				{
					taskCompletionSource.TrySetResult();
				}
				_closeEvent.Clear();
				if (!_parent._isWriteStreamClosed)
				{
					_parent.CheckStreamStatus();
				}
			}
		}
		
		public override bool CanRead => true;
		
		public override void Flush()
		{
		}
		
		public void SignalFinishAndWaitForClose()
		{
			var tcs = new TaskCompletionSource();
			lock(_parent._lockObject)
			{
				if (_parent._isReadStreamClosed)
					return;
				_closeEvent.Add(tcs);
				
				if (_parent._readSemaphore.CurrentCount == 0)
				{
					_interruptSend = true;
					_parent._readSemaphore.Release(); // Ensure the read stream can finish reading remaining data}}
				}
			}
			
			var task = tcs.Task;
			task.ConfigureAwait(false);
			task.Wait();
		}
		
		public override long Length => throw new NotSupportedException();
		
		public override long Position
		{
			get => throw new NotSupportedException();
			set => throw new NotSupportedException();
		}
		
		public override bool CanWrite => false;
		public override bool CanSeek => false;
		public override long Seek(long offset, SeekOrigin origin) => throw new NotSupportedException();
		public override void SetLength(long value) => throw new NotSupportedException();
		public override void Write(byte[] buffer, int offset, int count) => throw new NotSupportedException();
	}
}