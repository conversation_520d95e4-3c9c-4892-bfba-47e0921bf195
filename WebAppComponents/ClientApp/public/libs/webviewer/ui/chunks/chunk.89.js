(window.webpackJsonp=window.webpackJsonp||[]).push([[89],{1969:function(e,t,a){"use strict";a.r(t);a(60),a(44);var n=a(0),l=a.n(n),s=a(6),i=a(84),o=a(5),r=a(4),c=a.n(r),u=a(2),p=a(68),m=a(71);function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e}).apply(this,arguments)}var y={type:c.a.string,isFlyoutItem:c.a.bool,style:c.a.object,className:c.a.string},b=Object(n.forwardRef)((function(e,t){var a=e.isFlyoutItem,n=e.type,r=e.style,c=e.className,y=Object(s.d)(),b=m.b.cellAdjustment,f=b.dataElement,g=b.icon,j=b.title,w=function(){y(u.a.setFlyoutToggleElement(f)),y(u.a.toggleElement(o.a.CELL_ADJUSTMENT_FLYOUT))};return a?l.a.createElement(p.a,d({},e,{ref:t,onClick:w,additionalClass:""})):l.a.createElement(i.a,{key:n,isActive:!1,onClick:w,dataElement:f,title:j,img:g,ariaPressed:!1,style:r,className:c})}));b.propTypes=y,b.displayName="CellAdjustmentButton",t.default=b}}]);
//# sourceMappingURL=chunk.89.js.map