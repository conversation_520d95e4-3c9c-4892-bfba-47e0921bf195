(window.webpackJsonp=window.webpackJsonp||[]).push([[12],{1534:function(n,t,o){"use strict";var i=o(52),r=o(136),e=o(250),f=o(70),u=o(128),a=o(85),c=o(290),l=o(577),s=o(108),p=e&&e.prototype;if(i({target:"Promise",proto:!0,real:!0,forced:!!e&&f((function(){p.finally.call({then:function(){}},(function(){}))}))},{finally:function(n){var t=c(this,u("Promise")),o=a(n);return this.then(o?function(o){return l(t,n()).then((function(){return o}))}:n,o?function(o){return l(t,n()).then((function(){throw o}))}:n)}}),!r&&a(e)){var h=u("Promise").prototype.finally;p.finally!==h&&s(p,"finally",h,{unsafe:!0})}}}]);
//# sourceMappingURL=chunk.12.js.map