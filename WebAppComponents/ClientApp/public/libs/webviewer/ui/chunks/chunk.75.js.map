{"version": 3, "sources": ["webpack:///./src/ui/src/components/SaveModal/SaveModal.scss?34c4", "webpack:///./src/ui/src/components/SaveModal/SaveModal.scss", "webpack:///./src/ui/src/components/SaveModal/SaveModal.js", "webpack:///./src/ui/src/components/SaveModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "PAGE_RANGES", "FILE_TYPES", "OFFICE", "label", "extension", "workerTypes", "PDF", "IMAGE", "OFFICE_EDITOR", "SPREADSHEET_EDITOR", "CORRUPTED_OFFICE_EXTENSIONS", "SaveModal", "store", "useStore", "dispatch", "useDispatch", "t", "useTranslation", "isOpen", "useSelector", "state", "selectors", "isElementOpen", "DataElements", "SAVE_MODAL", "activeDocumentViewerKey", "getActiveDocumentViewerKey", "isSpreadsheetEditorMode", "isSpreadsheetEditorModeEnabled", "initalFileTypes", "useState", "fileTypes", "setFileTypes", "filename", "setFilename", "filetype", "setFiletype", "pageRange", "setPageRange", "specifiedPages", "setSpecifiedPages", "includeAnnotations", "setIncludeAnnotations", "includeComments", "setIncludeComments", "pageCount", "setPageCount", "errorText", "setErrorText", "useEffect", "updateFile", "core", "getDocument", "getFilename", "newFilename", "substring", "lastIndexOf", "type", "getType", "array", "split", "includes", "getDocumentCompletePromise", "getTotalPages", "documentUnloaded", "actions", "closeElement", "addEventListener", "undefined", "removeEventListener", "isOfficeEditorMode", "closeModal", "closeModalWithOnFocusClose", "useFocusOnClose", "clearError", "hasTyped", "setHasTyped", "saveDisabled", "optionsDisabled", "customPagesLabelElement", "className", "classNames", "error", "PageNumberInput", "selectedPageNumbers", "onBlurHandler", "onSelectedPageNumbersChange", "pageNumbers", "onError", "pageNumberError", "open", "closed", "data-element", "ModalWrapper", "title", "<PERSON><PERSON><PERSON><PERSON>", "onCloseClick", "swipeToClose", "htmlFor", "Input", "id", "data-testid", "onChange", "e", "target", "value", "<PERSON><PERSON><PERSON><PERSON>", "padMessageText", "messageText", "message", "Dropdown", "labelledById", "items", "map", "onClickItem", "find", "currentSelectionKey", "classList", "contains", "onSubmit", "preventDefault", "Choice", "checked", "radio", "name", "<PERSON><PERSON>", "disabled", "onClick", "pages", "getCurrentPage", "range", "downloadPdf", "downloadType", "console", "warn"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,ggQAAigQ,KAG1hQ0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,+xBCTvB,8lGAAA3B,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4YAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,4SAAAA,IAAA,2OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAoBA,IAAM4B,EACC,MADDA,EAEU,cAFVA,EAGU,cAHVA,EAIK,UAELC,EAAa,CACjBC,OAAQ,CAAEC,MAAO,gCAAiCC,UAAWC,IAAYH,QACzEI,IAAK,CAAEH,MAAO,cAAeC,UAAWC,IAAYC,KACpDC,MAAO,CAAEJ,MAAO,cAAeC,UAAW,OAC1CI,cAAe,CAAEL,MAAO,yBAA0BC,UAAWC,IAAYH,QACzEO,mBAAoB,CAAEN,MAAO,0BAA2BC,UAAWC,IAAYI,qBAG3EC,EAA8B,CAAC,OAAQ,QA6Q9BC,EA3QG,WAChB,IAAMC,EAAQC,cACRC,EAAWC,cACTC,EAAMC,cAAND,EACFE,EAASC,aAAY,SAACC,GAAK,OAAKC,IAAUC,cAAcF,EAAOG,IAAaC,eAC5EC,EAA0BN,aAAY,SAACC,GAAK,OAAKC,IAAUK,2BAA2BN,MACtFO,EAA0BR,YAAYE,IAAUO,gCAEhDC,EAAkB,CAAC5B,EAAWK,IAAKL,EAAWM,OACO,IAAzBuB,mBAASD,GAAgB,GAApDE,EAAS,KAAEC,EAAY,KACc,IAAZF,mBAAS,IAAG,GAArCG,EAAQ,KAAEC,EAAW,KAC0B,IAAtBJ,mBAASC,EAAU,IAAG,GAA/CI,EAAQ,KAAEC,EAAW,KAC+B,IAAzBN,mBAAS9B,GAAgB,GAApDqC,EAAS,KAAEC,EAAY,KACwB,IAAVR,qBAAU,GAA/CS,EAAc,KAAEC,EAAiB,KAC0B,IAAdV,oBAAS,GAAK,GAA3DW,EAAkB,KAAEC,EAAqB,KACa,KAAfZ,oBAAS,GAAM,GAAtDa,GAAe,MAAEC,GAAkB,MACG,KAAXd,mBAAS,GAAE,GAAtCe,GAAS,MAAEC,GAAY,MACgB,KAAZhB,mBAAS,IAAG,GAAvCiB,GAAS,MAAEC,GAAY,MAE9BC,qBAAU,WACR,IAAMC,EAAU,eAxDpB,EAwDoB,GAxDpB,EAwDoB,UAAG,8FACyC,KAApDzE,EAAW0E,IAAKC,YAAY3B,IACpB,CAAF,gBAMqB,GAL/BW,EAAYnC,EAAWK,KACvB0B,EAAaH,GACPI,EAAWxD,EAAS4E,cACpBC,EAAcrB,EAASsB,UAAU,EAAGtB,EAASuB,YAAY,OAASvB,EACxEC,EAAYoB,IACNG,EAAOhF,EAASiF,aACTrD,IAAYH,OAAM,iBAK5B,OAJKyD,EAAQ1B,EAAS2B,MAAM,KACvBxD,EAAY,IAAH,OAAOuD,EAAMA,EAAM7E,OAAS,IACtC4B,EAA4BmD,SAASzD,IACxC4B,EAAa,GAAD,OAAKH,EAAiB,CAAA5B,EAAWC,UAC9C,UACKzB,EAASqF,6BAA4B,gCAClCL,IAASpD,IAAYG,eAC9BwB,EAAa,CACX/B,EAAWO,cACXP,EAAWK,MAEb8B,EAAYnC,EAAWO,gBACdiD,IAASpD,IAAYI,qBAC9BuB,EAAa,CACX/B,EAAWQ,mBACXR,EAAWK,MAEb8B,EAAYnC,EAAWQ,qBACxB,QACDqC,GAAaK,IAAKY,cAActC,IAA0B,2CArFlE,+KAuFK,kBA/Be,mCAgCVuC,EAAmB,WACvB9B,EAAY,IACZY,GAAa,GACbd,EAAaH,GACbO,EAAYP,EAAgB,IAC5Bf,EAASmD,IAAQC,aAAa3C,IAAaC,cAK7C,OAHA0B,IACAC,IAAKgB,iBAAiB,mBAAoBH,OAAkBI,EAAW3C,GACvE0B,IAAKgB,iBAAiB,iBAAkBjB,OAAYkB,EAAW3C,GACxD,WACL0B,IAAKkB,oBAAoB,mBAAoBL,EAAkBvC,GAC/D0B,IAAKkB,oBAAoB,iBAAkBnB,EAAYzB,MAExD,CAACA,IAEJwB,qBAAU,WACR,IAAMxE,EAAW0E,IAAKC,YAAY3B,GAElC,GAAI6C,eAAwB7F,EAAU,CACpC2D,EAAYnC,EAAWO,eACvB,IAAMyB,EAAWxD,EAAS4E,cACpBC,EAAcrB,EAASsB,UAAU,EAAGtB,EAASuB,YAAY,OAASvB,EACxEC,EAAYoB,MAEb,CAACpC,IAGJ,IAAMqD,GAAa,WAAH,OAASzD,EAASmD,IAAQC,aAAa3C,IAAaC,cAE9DgD,GAA6BC,YAAgBF,IAuB7CG,GAAa,WAAH,OAAS1B,GAAa,KA8CS,KAAflB,oBAAS,GAAM,GAAxC6C,GAAQ,MAAEC,GAAW,MACtBC,IAAgB9B,KAAc4B,KAAatC,IAAcrC,IAAwBiC,EAEjF6C,GAAyC,WAAvB3C,EAAS/B,WAA0BkE,eAAwB3C,EAE7EoD,GACJ,yBAAKC,UAAWC,IAAW,8BAA+B,CAAEC,QAASnC,MACnE,2BAAOiC,UAAW,2BAChB,8BACGhE,EAAE,8BAEJqB,IAAcrC,GAAuB,0BAAMgF,UAAU,4BAA2B,KAC5EhE,EAAE,mDAGRqB,IAAcrC,GACb,kBAACmF,EAAA,EAAe,CACdC,oBAAqB7C,EACrBM,UAAWA,GACXwC,cAAe7C,EACf8C,4BAhEwB,SAACC,GAC1BZ,IACHC,IAAY,GAGVW,EAAYzG,OAAS,GACvB4F,MA2DIc,QAlEQ,WAAH,OAASxC,GAAahC,EAAE,uBAAyB6B,KAmEtD4C,gBAAiB1C,MAMzB,OACE,yBAAKiC,UAAWC,IAAW,YAAa,CAAES,KAAMxE,EAAQyE,QAASzE,IAAW0E,eAAcrE,IAAaC,YACrG,kBAACqE,EAAA,EAAY,CACX3E,OAAQA,EACR4E,MAAO9E,EAAE,oBACT+E,aAAcxB,GACdyB,aAAczB,GACd0B,cAAY,GACZ,yBAAKjB,UAAU,cACb,yBAAKA,UAAU,SAAShE,EAAE,sBAC1B,yBAAKgE,UAAU,mBACb,2BAAOkB,QAAQ,gBAAgBlB,UAAU,SAAShE,EAAE,uBACpD,kBAACmF,EAAA,EAAK,CACJ1C,KAAK,OACL2C,GAAG,gBACHC,cAAY,gBACZC,SA/Ga,SAACC,GAAM,MAC9BrE,EAAYqE,SAAS,QAAR,EAADA,EAAGC,cAAM,WAAR,EAAD,EAAWC,QA+GbA,MAAOxE,EACPyE,UAAU,QACVC,gBAAgB,EAChBC,YAA0B,KAAb3E,EAAkBjB,EAAE,mCAAqC,GACtE6F,QAAsB,KAAb5E,EAAkB,UAAY,aAG3C,yBAAK+C,UAAU,mBACb,yBAAKA,UAAU,QAAQoB,GAAG,4BAA4BpF,EAAE,uBACxD,kBAAC8F,EAAA,EAAQ,CACPV,GAAG,mBACHW,aAAa,2BACbC,MAAOjF,EAAUkF,KAAI,SAAC7I,GAAC,OAAKA,EAAE+B,SAC9B+G,YA1Ha,SAACX,GACxBnE,EAAYL,EAAUoF,MAAK,SAAC/I,GAAC,OAAKA,EAAE+B,QAAUoG,MAC1CA,IAAMtG,EAAWC,OAAOC,OAC1BmC,EAAatC,IAwHLoH,oBAAqBjF,EAAShC,UAGhC2E,IAAoB,oCACpB,yBAAKE,UAAU,SAAShE,EAAE,wBAC1B,0BAAMgE,UAAU,kBAAkBsB,SA1HlB,SAACC,GACrBA,EAAEC,OAAOa,UAAUC,SAAS,uBAGhChF,EAAaiE,EAAEC,OAAOC,OAClB1D,KACF6B,IAAY,GACZF,QAmHqE6C,SApIlD,SAAChB,GAAC,OAAKA,EAAEiB,mBAqIpB,yBAAKxC,UAAU,qBACb,kBAACyC,EAAA,EAAM,CACLC,QAASrF,IAAcrC,EACvB2H,OAAK,EACLC,KAAK,oBACLzH,MAAOa,EAAE,iBACTyF,MAAOzG,IAET,kBAACyH,EAAA,EAAM,CACLC,QAASrF,IAAcrC,EACvB2H,OAAK,EACLC,KAAK,oBACLzH,MAAOa,EAAE,yBACTyF,MAAOzG,KAGX,yBAAKgF,UAAU,wCACb,kBAACyC,EAAA,EAAM,CACLC,QAASrF,IAAcrC,EACvB2H,OAAK,EACLC,KAAK,oBACLzH,MAAO4E,GACP0B,MAAOzG,MAIb,yBAAKgF,UAAU,SAAShE,EAAE,yBAC1B,yBAAKgE,UAAU,sBACb,kBAACyC,EAAA,EAAM,CACLC,QAASjF,EACTmF,KAAK,4BACLzH,MAAOa,EAAE,+BACTsF,SAjJsB,WAAH,OAAS5D,GAAuBD,MAmJrD,kBAACgF,EAAA,EAAM,CACLC,QAAS/E,GACTiF,KAAK,yBACLzH,MAAOa,EAAE,6BACTsF,SAtJmB,WAAH,OAAS1D,IAAoBD,UA2JrD,yBAAKqC,UAAU,UACb,kBAAC6C,EAAA,EAAM,CAACC,SAAUjD,GAAckD,QAhJzB,WACb,IAWIC,EAXM7E,IAAKC,YAAY3B,GAOtBQ,IAMH+F,EADE3F,IAAcrC,EACRuC,WAAgBzD,OAASyD,EAAiB,CAACY,IAAK8E,eAAexG,IAC9DY,IAAcrC,GAEdqC,IAAcrC,EADf,CAACmD,IAAK8E,eAAexG,IAIrByG,IAAM,EAAG/E,IAAKY,cAActC,GAA2B,EAAG,GAGpE0G,YAAYrH,EAAU,CACpB2B,qBACAE,mBACAV,SAAUA,GAAY,WACtBmG,aAAcjG,EAAS/B,UACvB4H,QACApH,SACCa,GAEH+C,MA5BE6D,QAAQC,KAAK,2BA4IwCnI,MAAOa,EAAE,wBCvSrDL", "file": "chunks/chunk.75.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./SaveModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.SaveModal{visibility:visible}.closed.SaveModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.SaveModal .footer .modal-button.confirm:hover,.SaveModal .modal-container .footer button:hover:not(:disabled){background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.SaveModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.SaveModal .footer .modal-button.confirm.disabled,.SaveModal .modal-container .footer button:disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.SaveModal .footer .modal-button.confirm.disabled span,.SaveModal .modal-container .footer button:disabled span{color:var(--primary-button-text)}.SaveModal .footer .modal-button.cancel:hover,.SaveModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.SaveModal .footer .modal-button.cancel,.SaveModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.SaveModal .footer .modal-button.cancel.disabled,.SaveModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.SaveModal .footer .modal-button.cancel.disabled span,.SaveModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.SaveModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.SaveModal .modal-container .wrapper .modal-content{padding:10px}.SaveModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.SaveModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.SaveModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.SaveModal .footer .modal-button.confirm{margin-left:4px}.SaveModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SaveModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SaveModal .footer .modal-button{padding:23px 8px}}.SaveModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SaveModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .SaveModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SaveModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SaveModal .swipe-indicator{width:32px}}.SaveModal{flex-direction:column}.SaveModal .modal-container{overflow:visible;display:flex;flex-direction:column;justify-content:space-evenly;height:auto;width:480px;padding:0}.SaveModal .modal-container .header{border-bottom:1px solid var(--gray-5);padding:16px;height:64px;width:100%;display:flex;align-items:center;justify-content:space-between}.SaveModal .modal-container .header .header-text{font-size:var(--font-size-large);font-weight:var(--font-weight-bold)}.SaveModal .modal-container .header .Button{width:32px;height:32px}.SaveModal .modal-container .modal-body{padding:16px;display:flex;flex-direction:column;font-size:var(--font-size-default);font-family:var(--font-family);grid-gap:12px;gap:12px}.SaveModal .modal-container .modal-body .title{line-height:16px;font-weight:var(--font-weight-bold)}.SaveModal .modal-container .modal-body .input-container{align-items:baseline;display:flex;grid-gap:16px;gap:16px;height:32px;margin-bottom:20px}.SaveModal .modal-container .modal-body .input-container .label{min-width:60px}.SaveModal .modal-container .modal-body .input-container .ui__input{border-color:var(--border)}.SaveModal .modal-container .modal-body .input-container .ui__input.ui__input--focused{box-shadow:none;border-color:var(--focus-border)}.SaveModal .modal-container .modal-body .input-container .ui__input.ui__input--message-warning{border-color:var(--error-border-color)}.SaveModal .modal-container .modal-body .input-container .ui__input.ui__input--message-warning .ui__icon svg{fill:var(--error-border-color)}.SaveModal .modal-container .modal-body .input-container .ui__input__messageText{color:var(--error-text-color);margin:8px 0;font-size:13px}.SaveModal .modal-container .modal-body .input-container input{padding:8px;height:32px;font-size:var(--font-size-default);flex:1 1 auto}.SaveModal .modal-container .modal-body .input-container .Dropdown__wrapper{height:32px;flex:1 1 auto}.SaveModal .modal-container .modal-body .input-container .Dropdown__wrapper .Dropdown{height:100%;width:100%!important}.SaveModal .modal-container .modal-body .input-container .Dropdown__wrapper .Dropdown .picked-option .picked-option-text{width:auto;flex:none}.SaveModal .modal-container .modal-body .input-container .Dropdown__wrapper .Dropdown .picked-option .arrow{flex:none}.SaveModal .modal-container .modal-body .input-container .Dropdown__wrapper .Dropdown__items{width:100%!important}.SaveModal .modal-container .modal-body .radio-container{grid-gap:8px;gap:8px;height:90px;display:grid;grid-template-columns:repeat(2,1fr)}.SaveModal .modal-container .modal-body .radio-container .ui__choice--checked .ui__choice__input__check{border-color:var(--blue-5)}.SaveModal .modal-container .modal-body .radio-container .ui__choice__input__check{border-color:var(--gray-7)}.SaveModal .modal-container .modal-body .radio-container .page-number-input-container.error .page-number-input{border:1px solid var(--error-border-color)}.SaveModal .modal-container .modal-body .radio-container .page-number-input-container .page-number-input{width:208px}.SaveModal .modal-container .modal-body .radio-container .page-number-input-container .specifyPagesChoiceLabel{display:flex;margin-bottom:8px}.SaveModal .modal-container .modal-body .radio-container .page-number-input-container .specifyPagesChoiceLabel .specifyPagesExampleLabel{margin-left:4px;color:var(--faded-text)}.SaveModal .modal-container .modal-body .radio-container .page-range-column{display:grid;grid-gap:16px;gap:16px;align-content:baseline}.SaveModal .modal-container .modal-body .radio-container .page-range-column.custom-page-ranges .ui__choice{align-items:unset}.SaveModal .modal-container .modal-body .checkbox-container{display:grid;grid-template-columns:repeat(2,1fr)}.SaveModal .modal-container .footer{padding:16px;display:flex;justify-content:flex-end;border-top:1px solid var(--gray-5)}.SaveModal .modal-container .footer button{border:none;border-radius:4px;background:var(--primary-button)!important;width:82px;height:32px;color:var(--primary-button-text)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useEffect, useState, } from 'react';\nimport { useSelector, useDispatch, useStore } from 'react-redux';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport { useTranslation } from 'react-i18next';\nimport DataElements from 'constants/dataElement';\nimport Button from 'components/Button';\nimport { Choice, Input } from '@pdftron/webviewer-react-toolkit';\nimport core from 'core';\nimport classNames from 'classnames';\nimport Dropdown from 'components/Dropdown';\nimport PageNumberInput from 'components/PageReplacementModal/PageNumberInput';\nimport downloadPdf from 'helpers/downloadPdf';\nimport { isOfficeEditorMode } from 'helpers/officeEditor';\nimport { workerTypes } from 'constants/types';\nimport range from 'lodash/range';\nimport ModalWrapper from 'components/ModalWrapper';\nimport useFocusOnClose from 'hooks/useFocusOnClose';\n\nimport './SaveModal.scss';\n\nconst PAGE_RANGES = {\n  ALL: 'all',\n  CURRENT_PAGE: 'currentPage',\n  CURRENT_VIEW: 'currentView',\n  SPECIFY: 'specify'\n};\nconst FILE_TYPES = {\n  OFFICE: { label: 'OFFICE (*.pptx,*.docx,*.xlsx)', extension: workerTypes.OFFICE },\n  PDF: { label: 'PDF (*.pdf)', extension: workerTypes.PDF },\n  IMAGE: { label: 'PNG (*.png)', extension: 'png' },\n  OFFICE_EDITOR: { label: 'Word Document (*.docx)', extension: workerTypes.OFFICE },\n  SPREADSHEET_EDITOR: { label: 'Excel Document (*.xlsx)', extension: workerTypes.SPREADSHEET_EDITOR },\n};\n// These legacy office extensions return corrupted file data from the workers if downloaded as OFFICE\nconst CORRUPTED_OFFICE_EXTENSIONS = ['.ppt', '.xls'];\n\nconst SaveModal = () => {\n  const store = useStore();\n  const dispatch = useDispatch();\n  const { t } = useTranslation();\n  const isOpen = useSelector((state) => selectors.isElementOpen(state, DataElements.SAVE_MODAL));\n  const activeDocumentViewerKey = useSelector((state) => selectors.getActiveDocumentViewerKey(state));\n  const isSpreadsheetEditorMode = useSelector(selectors.isSpreadsheetEditorModeEnabled);\n\n  const initalFileTypes = [FILE_TYPES.PDF, FILE_TYPES.IMAGE];\n  const [fileTypes, setFileTypes] = useState(initalFileTypes);\n  const [filename, setFilename] = useState('');\n  const [filetype, setFiletype] = useState(fileTypes[0]);\n  const [pageRange, setPageRange] = useState(PAGE_RANGES.ALL);\n  const [specifiedPages, setSpecifiedPages] = useState();\n  const [includeAnnotations, setIncludeAnnotations] = useState(true);\n  const [includeComments, setIncludeComments] = useState(false);\n  const [pageCount, setPageCount] = useState(1);\n  const [errorText, setErrorText] = useState('');\n\n  useEffect(() => {\n    const updateFile = async () => {\n      const document = core.getDocument(activeDocumentViewerKey);\n      if (document) {\n        setFiletype(FILE_TYPES.PDF);\n        setFileTypes(initalFileTypes);\n        const filename = document.getFilename();\n        const newFilename = filename.substring(0, filename.lastIndexOf('.')) || filename;\n        setFilename(newFilename);\n        const type = document.getType();\n        if (type === workerTypes.OFFICE) {\n          const array = filename.split('.');\n          const extension = `.${array[array.length - 1]}`;\n          if (!CORRUPTED_OFFICE_EXTENSIONS.includes(extension)) {\n            setFileTypes([...initalFileTypes, FILE_TYPES.OFFICE]);\n          }\n          await document.getDocumentCompletePromise();\n        } else if (type === workerTypes.OFFICE_EDITOR) {\n          setFileTypes([\n            FILE_TYPES.OFFICE_EDITOR,\n            FILE_TYPES.PDF\n          ]);\n          setFiletype(FILE_TYPES.OFFICE_EDITOR);\n        } else if (type === workerTypes.SPREADSHEET_EDITOR) {\n          setFileTypes([\n            FILE_TYPES.SPREADSHEET_EDITOR,\n            FILE_TYPES.PDF\n          ]);\n          setFiletype(FILE_TYPES.SPREADSHEET_EDITOR);\n        }\n        setPageCount(core.getTotalPages(activeDocumentViewerKey));\n      }\n    };\n    const documentUnloaded = () => {\n      setFilename('');\n      setPageCount(0);\n      setFileTypes(initalFileTypes);\n      setFiletype(initalFileTypes[0]);\n      dispatch(actions.closeElement(DataElements.SAVE_MODAL));\n    };\n    updateFile();\n    core.addEventListener('documentUnloaded', documentUnloaded, undefined, activeDocumentViewerKey);\n    core.addEventListener('documentLoaded', updateFile, undefined, activeDocumentViewerKey);\n    return () => {\n      core.removeEventListener('documentUnloaded', documentUnloaded, activeDocumentViewerKey);\n      core.removeEventListener('documentLoaded', updateFile, activeDocumentViewerKey);\n    };\n  }, [activeDocumentViewerKey]);\n\n  useEffect(() => {\n    const document = core.getDocument(activeDocumentViewerKey);\n\n    if (isOfficeEditorMode() && document) {\n      setFiletype(FILE_TYPES.OFFICE_EDITOR);\n      const filename = document.getFilename();\n      const newFilename = filename.substring(0, filename.lastIndexOf('.')) || filename;\n      setFilename(newFilename);\n    }\n  }, [isOpen]);\n\n  // One is passed to the modalWrapper which uses onFocusClose\n  const closeModal = () => dispatch(actions.closeElement(DataElements.SAVE_MODAL));\n  // One is used when we close the modal after save, we want to transfer focus back\n  const closeModalWithOnFocusClose = useFocusOnClose(closeModal);\n  const preventDefault = (e) => e.preventDefault();\n  const onFilenameChange = (e) => {\n    setFilename(e?.target?.value);\n  };\n  const onFiletypeChange = (e) => {\n    setFiletype(fileTypes.find((i) => i.label === e));\n    if (e === FILE_TYPES.OFFICE.label) {\n      setPageRange(PAGE_RANGES.ALL);\n    }\n  };\n  const onPageRangeChange = (e) => {\n    if (e.target.classList.contains('page-number-input')) {\n      return;\n    }\n    setPageRange(e.target.value);\n    if (errorText) {\n      setHasTyped(false);\n      clearError();\n    }\n  };\n  const onIncludeAnnotationsChanged = () => setIncludeAnnotations(!includeAnnotations);\n  const onIncludeCommentsChanged = () => setIncludeComments(!includeComments);\n  const clearError = () => setErrorText('');\n  const onError = () => setErrorText(t('saveModal.pageError') + pageCount);\n  const onSpecifiedPagesChanged = (pageNumbers) => {\n    if (!hasTyped) {\n      setHasTyped(true);\n    }\n\n    if (pageNumbers.length > 0) {\n      clearError();\n    }\n  };\n  const onSave = () => {\n    let doc = core.getDocument(activeDocumentViewerKey);\n\n    if (!doc) {\n      console.warn('Document is not loaded');\n      return;\n    }\n\n    if (!filename) {\n      return;\n    }\n\n    let pages;\n    if (pageRange === PAGE_RANGES.SPECIFY) {\n      pages = specifiedPages?.length ? specifiedPages : [core.getCurrentPage(activeDocumentViewerKey)];\n    } else if (pageRange === PAGE_RANGES.CURRENT_PAGE) {\n      pages = [core.getCurrentPage(activeDocumentViewerKey)];\n    } else if (pageRange === PAGE_RANGES.CURRENT_VIEW) {\n      pages = [core.getCurrentPage(activeDocumentViewerKey)];\n    } else {\n      pages = range(1, core.getTotalPages(activeDocumentViewerKey) + 1, 1);\n    }\n\n    downloadPdf(dispatch, {\n      includeAnnotations,\n      includeComments,\n      filename: filename || 'untitled',\n      downloadType: filetype.extension,\n      pages,\n      store,\n    }, activeDocumentViewerKey);\n\n    closeModalWithOnFocusClose();\n  };\n\n  const [hasTyped, setHasTyped] = useState(false);\n  const saveDisabled = (errorText || !hasTyped) && pageRange === PAGE_RANGES.SPECIFY || !filename;\n\n  const optionsDisabled = filetype.extension === 'office' || isOfficeEditorMode() || isSpreadsheetEditorMode;\n\n  const customPagesLabelElement = (\n    <div className={classNames('page-number-input-container', { error: !!errorText })}>\n      <label className={'specifyPagesChoiceLabel'}>\n        <span>\n          {t('option.print.specifyPages')}\n        </span>\n        {pageRange === PAGE_RANGES.SPECIFY && <span className='specifyPagesExampleLabel'>\n          - {t('option.thumbnailPanel.multiSelectPagesExample')}\n        </span>}\n      </label>\n      {pageRange === PAGE_RANGES.SPECIFY &&\n        <PageNumberInput\n          selectedPageNumbers={specifiedPages}\n          pageCount={pageCount}\n          onBlurHandler={setSpecifiedPages}\n          onSelectedPageNumbersChange={onSpecifiedPagesChanged}\n          onError={onError}\n          pageNumberError={errorText}\n        />\n      }\n    </div>\n  );\n\n  return (\n    <div className={classNames('SaveModal', { open: isOpen, closed: !isOpen })} data-element={DataElements.SAVE_MODAL}>\n      <ModalWrapper\n        isOpen={isOpen}\n        title={t('saveModal.saveAs')}\n        closeHandler={closeModal}\n        onCloseClick={closeModal}\n        swipeToClose>\n        <div className='modal-body'>\n          <div className='title'>{t('saveModal.general')}</div>\n          <div className='input-container'>\n            <label htmlFor='fileNameInput' className='label'>{t('saveModal.fileName')}</label>\n            <Input\n              type='text'\n              id='fileNameInput'\n              data-testid=\"fileNameInput\"\n              onChange={onFilenameChange}\n              value={filename}\n              fillWidth=\"false\"\n              padMessageText={true}\n              messageText={filename === '' ? t('saveModal.fileNameCannotBeEmpty') : ''}\n              message={filename === '' ? 'warning' : 'default'}\n            />\n          </div>\n          <div className='input-container'>\n            <div className='label' id=\"file-type-dropdown-label\">{t('saveModal.fileType')}</div>\n            <Dropdown\n              id=\"fileTypeDropdown\"\n              labelledById='file-type-dropdown-label'\n              items={fileTypes.map((i) => i.label)}\n              onClickItem={onFiletypeChange}\n              currentSelectionKey={filetype.label}\n            />\n          </div>\n          {!optionsDisabled && (<>\n            <div className='title'>{t('saveModal.pageRange')}</div>\n            <form className='radio-container' onChange={onPageRangeChange} onSubmit={preventDefault}>\n              <div className='page-range-column'>\n                <Choice\n                  checked={pageRange === PAGE_RANGES.ALL}\n                  radio\n                  name='page-range-option'\n                  label={t('saveModal.all')}\n                  value={PAGE_RANGES.ALL}\n                />\n                <Choice\n                  checked={pageRange === PAGE_RANGES.CURRENT_PAGE}\n                  radio\n                  name='page-range-option'\n                  label={t('saveModal.currentPage')}\n                  value={PAGE_RANGES.CURRENT_PAGE}\n                />\n              </div>\n              <div className='page-range-column custom-page-ranges'>\n                <Choice\n                  checked={pageRange === PAGE_RANGES.SPECIFY}\n                  radio\n                  name='page-range-option'\n                  label={customPagesLabelElement}\n                  value={PAGE_RANGES.SPECIFY}\n                />\n              </div>\n            </form>\n            <div className='title'>{t('saveModal.properties')}</div>\n            <div className='checkbox-container'>\n              <Choice\n                checked={includeAnnotations}\n                name='include-annotation-option'\n                label={t('saveModal.includeAnnotation')}\n                onChange={onIncludeAnnotationsChanged}\n              />\n              <Choice\n                checked={includeComments}\n                name='include-comment-option'\n                label={t('saveModal.includeComments')}\n                onChange={onIncludeCommentsChanged}\n              />\n            </div>\n          </>)}\n        </div>\n        <div className='footer'>\n          <Button disabled={saveDisabled} onClick={onSave} label={t('saveModal.save')} />\n        </div>\n      </ModalWrapper>\n    </div >\n  );\n};\n\nexport default SaveModal;\n", "import SaveModal from './SaveModal';\n\nexport default SaveModal;\n"], "sourceRoot": ""}