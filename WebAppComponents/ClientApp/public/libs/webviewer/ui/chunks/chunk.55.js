(window.webpackJsonp=window.webpackJsonp||[]).push([[55],{1760:function(t,e,n){var o=n(32),r=n(1854);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[t.i,r,""]]);var a={insert:function(t){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(t);let e;e=document.getElementsByTagName("apryse-webviewer"),e.length||(e=function t(e,n=document){const o=[];return n.querySelectorAll(e).forEach(t=>o.push(t)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...t(e,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<e.length;o++){const r=e[o];if(0===o)r.shadowRoot.appendChild(t),t.onload=function(){n.length>0&&n.forEach(e=>{e.innerHTML=t.innerHTML})};else{const e=t.cloneNode(!0);r.shadowRoot.appendChild(e),n.push(e)}}},singleton:!1};o(r,a);t.exports=r.locals||{}},1852:function(t,e,n){var o=n(32),r=n(1853);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[t.i,r,""]]);var a={insert:function(t){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(t);let e;e=document.getElementsByTagName("apryse-webviewer"),e.length||(e=function t(e,n=document){const o=[];return n.querySelectorAll(e).forEach(t=>o.push(t)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...t(e,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<e.length;o++){const r=e[o];if(0===o)r.shadowRoot.appendChild(t),t.onload=function(){n.length>0&&n.forEach(e=>{e.innerHTML=t.innerHTML})};else{const e=t.cloneNode(!0);r.shadowRoot.appendChild(e),n.push(e)}}},singleton:!1};o(r,a);t.exports=r.locals||{}},1853:function(t,e,n){(e=t.exports=n(33)(!1)).push([t.i,".Watermark{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.Watermark .modal-container .wrapper .modal-content{padding:10px}.Watermark .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.Watermark .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.Watermark .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.Watermark .footer .modal-button.confirm{margin-left:4px}.Watermark .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .footer .modal-button{padding:23px 8px}}.Watermark .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .swipe-indicator{width:32px}}.open.Watermark{visibility:visible}.closed.Watermark{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.Watermark .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.Watermark .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.Watermark .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.Watermark .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.Watermark .footer .modal-button.cancel:hover,.Watermark .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.Watermark .footer .modal-button.cancel,.Watermark .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.Watermark .footer .modal-button.cancel.disabled,.Watermark .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.Watermark .footer .modal-button.cancel.disabled span,.Watermark .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.Watermark{z-index:110}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .modal-container{overflow:visible}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .modal-container{overflow:visible}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .h2{font-size:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .h2{font-size:16px}}.Watermark .StylePopup{position:absolute;margin-top:-140px;background:var(--preset-background);display:flex;flex-direction:column;justify-content:center;border-radius:0 0 4px 4px;box-shadow:0 0 3px 0 var(--box-shadow);align-items:center;z-index:85}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.Watermark .StylePopup{margin-top:0}}.Watermark .StylePopup .ColorPalette{padding:10px}.Watermark .style-container{display:flex}.Watermark .style-container .Button{margin-right:8px}.Watermark .style-container .Button:hover{background:var(--popup-button-hover)}.Watermark .style-container .Button.active{background:var(--popup-button-active)}.Watermark .form-content-container{display:flex;flex-direction:row;justify-content:space-between;align-items:center;padding:16px;width:100%}.Watermark .form-content-container form{width:auto}@media(min-width:315px)and (max-width:480px){.Watermark .form-content-container form{width:auto}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .form-content-container{flex-direction:column;padding:16px 16px 0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .form-content-container{flex-direction:column;padding:16px 16px 0}}.Watermark .font-form-fields{display:flex;flex-direction:column;margin:0}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .font-form-fields{flex-direction:row}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .font-form-fields{flex-direction:row}}.Watermark .font-form-fields .form-font-type{margin-top:16px;margin-left:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .font-form-fields .form-font-type{width:160px;margin-left:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .font-form-fields .form-font-type{width:160px;margin-left:0}}.Watermark .font-form-fields .form-font-type label{font-weight:700}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .font-form-fields .form-font-type label{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .font-form-fields .form-font-type label{font-size:13px}}.Watermark .font-form-fields .form-font-type .Dropdown__wrapper{margin-top:8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .font-form-fields .form-font-type .Dropdown__wrapper{width:160px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .font-form-fields .form-font-type .Dropdown__wrapper{width:160px}}.Watermark .font-form-fields .form-font-type .Dropdown__wrapper .Dropdown{text-align:left;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .font-form-fields .form-font-type .Dropdown__wrapper .Dropdown{height:28px;width:160px;z-index:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .font-form-fields .form-font-type .Dropdown__wrapper .Dropdown{height:28px;width:160px;z-index:0}}.Watermark .font-form-fields .form-font-type .Dropdown__wrapper .Dropdown__items{width:100%;top:0}.Watermark .font-form-fields .form-font-type .Dropdown__wrapper#location .Dropdown__items{z-index:101}.Watermark .font-form-fields .form-font-size{margin-top:16px;margin-left:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .font-form-fields .form-font-size{width:160px;margin-left:8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .font-form-fields .form-font-size{width:160px;margin-left:8px}}.Watermark .font-form-fields .form-font-size label{font-weight:700}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .font-form-fields .form-font-size label{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .font-form-fields .form-font-size label{font-size:13px}}.Watermark .font-form-fields .form-font-size .FontSizeDropdown{padding:2px;height:32px;margin-top:8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .font-form-fields .form-font-size .FontSizeDropdown{height:28px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .font-form-fields .form-font-size .FontSizeDropdown{height:28px}}.Watermark .font-form-fields .form-font-size .FontSizeDropdown .icon-button{cursor:pointer;width:14px;height:14px}.Watermark .font-form-fields .form-font-size .FontSizeDropdown .icon-button>.Icon{color:var(--icon-color);width:12px;height:16px;margin-top:2px}.Watermark .watermark-settings{display:flex;flex-direction:column;width:330px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .watermark-settings{width:100%;height:374px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .watermark-settings{width:100%;height:374px}}.Watermark .watermark-settings .form-field{display:flex;flex-direction:column;margin:16px 0 0 16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .watermark-settings .form-field{margin:0;width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .watermark-settings .form-field{margin:0;width:100%}}.Watermark .watermark-settings .form-field label{margin-bottom:8px;font-weight:700}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .watermark-settings .form-field label{margin-top:16px;font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .watermark-settings .form-field label{margin-top:16px;font-size:13px}}.Watermark .watermark-settings .form-field .text-input,.Watermark .watermark-settings .form-field select{height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .watermark-settings .form-field .text-input,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .watermark-settings .form-field select{height:28px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .watermark-settings .form-field .text-input,.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .watermark-settings .form-field select{height:28px}}.Watermark .watermark-settings .form-field.opacity-slider{display:flex;margin-bottom:0}.Watermark .watermark-settings .form-field.opacity-slider .slider-property{font-weight:700}.Watermark .watermark-settings .form-field.opacity-slider .slider-svg-container{margin:0;height:30px}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.Watermark .watermark-settings .form-field.opacity-slider{align-items:stretch}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .watermark-settings .form-field.opacity-slider{font-size:13px;margin-top:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .watermark-settings .form-field.opacity-slider{font-size:13px;margin-top:16px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .watermark-settings .form-field .Dropdown__wrapper{width:328px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .watermark-settings .form-field .Dropdown__wrapper{width:328px}}.Watermark .watermark-settings .form-field .Dropdown__wrapper button{width:314px}.Watermark .watermark-settings .form-field .Dropdown__wrapper .Dropdown{text-align:left;width:314px;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .watermark-settings .form-field .Dropdown__wrapper .Dropdown{height:28px;z-index:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .watermark-settings .form-field .Dropdown__wrapper .Dropdown{height:28px;z-index:0}}.Watermark .watermark-settings .form-field .Dropdown__wrapper .Dropdown__items{width:100%;top:0}.Watermark .watermark-settings .form-field .Dropdown__wrapper#location .Dropdown__items{z-index:101}.Watermark .watermark-settings .colorSelect{width:18px;height:18px;margin:7px 8px 7px 7px;border-radius:10000000px}.Watermark .watermark-settings .white-color{border:1px solid var(--white-color-palette-border)}.Watermark .watermark-settings .slider-input-field{height:32px}.Watermark .canvas-container{display:flex;justify-content:center;align-items:center;width:500px;height:436px;background-color:var(--file-preview-background);border:1px solid var(--lighter-border)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .canvas-container{display:block;width:328px;height:261px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .canvas-container{display:block;width:328px;height:261px}}.Watermark .canvas-container canvas{width:325px!important}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .canvas-container canvas{width:328px;height:261px;-o-object-fit:contain;object-fit:contain;background-color:var(--file-preview-background)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .canvas-container canvas{width:328px;height:261px;-o-object-fit:contain;object-fit:contain;background-color:var(--file-preview-background)}}.Watermark .divider{height:1px;width:100%;background:var(--divider)}.Watermark .divider.separator{margin-top:16px}.Watermark .button-container{display:flex;justify-content:space-between;align-items:baseline;width:100%;padding:16px}.Watermark .button-container .button{display:flex;justify-content:center;align-items:center;padding:6px 18px;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;border:0;height:32px;cursor:pointer;font-size:13px}.Watermark .button-container .button.ok{color:var(--primary-button-text);background:var(--primary-button)}.Watermark .button-container .button.ok:hover{background:var(--primary-button-hover)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .button-container .button.ok{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .button-container .button.ok{font-size:13px}}.Watermark .button-container .button.cancel{color:var(--secondary-button-text);background:none}.Watermark .button-container .button.cancel:hover{color:var(--secondary-button-hover)}.Watermark .button-container .reset-settings{background-color:transparent;cursor:pointer;background:none;border:0;color:var(--secondary-button-text);display:flex;font-size:13px;padding:6px 18px}:host(:not([data-tabbing=true])) .Watermark .button-container .reset-settings,html:not([data-tabbing=true]) .Watermark .button-container .reset-settings{outline:none}.Watermark .button-container .reset-settings:hover{color:var(--secondary-button-hover)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .Watermark .button-container .reset-settings{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .Watermark .button-container .reset-settings{font-size:13px}}",""]),e.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1854:function(t,e,n){(e=t.exports=n(33)(!1)).push([t.i,".open.PrintModal{visibility:visible}.closed.PrintModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.PrintModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.PrintModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.PrintModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.PrintModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.PrintModal .footer .modal-button.cancel:hover,.PrintModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.PrintModal .footer .modal-button.cancel,.PrintModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.PrintModal .footer .modal-button.cancel.disabled,.PrintModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.PrintModal .footer .modal-button.cancel.disabled span,.PrintModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.PrintModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.PrintModal .modal-container .wrapper .modal-content{padding:10px}.PrintModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.PrintModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.PrintModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.PrintModal .footer .modal-button.confirm{margin-left:4px}.PrintModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PrintModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .PrintModal .footer .modal-button{padding:23px 8px}}.PrintModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PrintModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .PrintModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PrintModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .PrintModal .swipe-indicator{width:32px}}.PrintModal .modal-container{width:480px;overflow:hidden}.PrintModal .modal-container .settings{display:flex;flex-direction:column;width:100%;padding:24px}.PrintModal .modal-container .settings .ui__choice{margin:10px 4px 4px 0}.PrintModal .modal-container .settings .ui__choice--disabled{opacity:.5}.PrintModal .modal-container .settings .ui__input{border:none}.PrintModal .modal-container .settings .ui__input.ui__input--focused{box-shadow:none}.PrintModal .modal-container .settings .settings-form{margin-bottom:0}.PrintModal .modal-container .settings .specifyPagesChoiceLabel{display:flex}.PrintModal .modal-container .settings .specifyPagesChoiceLabel .specifyPagesExampleLabel{margin-left:4px;color:var(--faded-text)}.PrintModal .modal-container .settings .page-number-input-container{margin-top:8px}.PrintModal .modal-container .divider{height:1px;width:100%;background:var(--divider)}.PrintModal .modal-container .section{padding-bottom:16px}.PrintModal .modal-container .section.watermark-section{padding-bottom:0}.PrintModal .modal-container .section .section-label{font-weight:700}.PrintModal .modal-container .section .hidden{display:none}.PrintModal .modal-container label.section-label,.PrintModal .modal-container label.ui__choice__label{padding:2px 0 2px 4px}.PrintModal .modal-container .print-quality-section-label{padding:0;margin-bottom:8px}.PrintModal .modal-container #printQualityOptions button.Dropdown{height:32px;margin-top:8px}.PrintModal .modal-container #printQualityOptions.Dropdown__wrapper .Dropdown{text-align:left}.PrintModal .modal-container #printQualityOptions.Dropdown__wrapper .Dropdown__items{width:274px;left:0}.PrintModal .modal-container input[type=text]{width:100%;height:32px;border:1px solid var(--border);border-radius:4px;color:var(--text-color);padding:4px 42px 6px 8px;margin-top:6px}.PrintModal .modal-container input[type=text]:focus{outline:none;border:1px solid var(--focus-border)}.PrintModal .modal-container input[type=text]::-moz-placeholder{color:var(--placeholder-text)}.PrintModal .modal-container input[type=text]::placeholder{color:var(--placeholder-text)}.PrintModal .modal-container input[type=text].page-number-input--error{border-color:var(--error-border-color)}.PrintModal .modal-container .total{display:flex;flex-direction:row;padding-bottom:16px;color:var(--text-color)}.PrintModal .modal-container .background{width:100%;height:10px;transform:translateY(-50%);fill:#e2f3fe}.PrintModal .modal-container .progress{height:10px;transform:translateY(-50%);fill:#077bc5}.PrintModal .modal-container .buttons{display:flex;flex-direction:row;justify-content:flex-end;width:100%;padding:20px}.PrintModal .modal-container .buttons .button{background-color:transparent;display:flex;justify-content:center;align-items:center;color:var(--primary-button-text);padding:6px 18px;width:auto;width:-moz-fit-content;width:fit-content;background:var(--primary-button);border-radius:4px;border:0;height:32px;cursor:pointer;font-size:13px}:host(:not([data-tabbing=true])) .PrintModal .modal-container .buttons .button,html:not([data-tabbing=true]) .PrintModal .modal-container .buttons .button{outline:none}.PrintModal .modal-container .buttons .button:hover:not(:disabled){background:var(--primary-button-hover)}.PrintModal .modal-container .buttons .button:disabled{opacity:.5;cursor:auto}.PrintModal .modal-container .specify-pages-choice{margin-bottom:18px}.PrintModal .modal-container .specify-pages-choice input{width:195px;margin-top:0}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PrintModal .modal-container .specify-pages-choice input{width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .PrintModal .modal-container .specify-pages-choice input{width:100%}}.PrintModal .modal-container .specify-pages-choice label{display:grid}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PrintModal .modal-container .specify-pages-choice label{width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .PrintModal .modal-container .specify-pages-choice label{width:100%}}.PrintModal .modal-container .specify-pages-choice.ui__choice--checked{align-items:baseline;height:80px}.PrintModal .modal-container .specify-pages-choice.ui__choice--checked .ui__choice__input{top:3px}.PrintModal .modal-container .apply-watermark{border:none;background-color:transparent;cursor:pointer;margin-top:10px;background:none;border:1px solid var(--secondary-button-text);border-radius:4px;color:var(--secondary-button-text);padding:6px 16px;height:32px;display:flex;align-items:center;justify-content:center;font-size:13px}:host(:not([data-tabbing=true])) .PrintModal .modal-container .apply-watermark,html:not([data-tabbing=true]) .PrintModal .modal-container .apply-watermark{outline:none}.PrintModal .modal-container .apply-watermark:hover{color:var(--secondary-button-hover)}",""]),e.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1985:function(t,e,n){"use strict";n.r(e);n(23),n(8),n(24),n(10),n(56),n(9),n(11),n(19),n(12),n(13),n(14),n(16),n(15),n(20),n(18),n(22),n(64),n(65),n(66),n(67),n(37),n(39),n(40),n(63);var o,r,a=n(0),i=n.n(a),l=n(2),c=n(3),s=n(6),d=n(5),p=n(1),m=n(160),u=n(237),f=n(280),h=(n(36),n(30),n(25),n(96),n(110),n(17)),b=n.n(h),g=n(4),y=n.n(g),w=n(1506),x=n(543),v=(n(44),n(82),n(28),n(45),n(47),n(46),n(60),n(107),n(49),n(53),n(153),n(89),n(1851),n(114),n(26),n(27),n(221)),k=n(441),E=n(1464),P=n(48),O=n(556),W=["Arial","Times New Roman","Tahoma ","Trebuchet MS","Courier","Verdana","Georgia","Palatino","Comic Sans MS","Arial Black","Impact"],C=(n(1852),n(341)),S=n(76),A=n(80),_=n(38);function M(t){return(M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function j(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function L(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?j(Object(n),!0).forEach((function(e){B(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function T(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */T=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},r="function"==typeof Symbol?Symbol:{},a=r.iterator||"@@iterator",i=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function c(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,n){return t[e]=n}}function s(t,e,n,r){var a=e&&e.prototype instanceof m?e:m,i=Object.create(a.prototype),l=new O(r||[]);return o(i,"_invoke",{value:v(t,n,l)}),i}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=s;var p={};function m(){}function u(){}function f(){}var h={};c(h,a,(function(){return this}));var b=Object.getPrototypeOf,g=b&&b(b(W([])));g&&g!==e&&n.call(g,a)&&(h=g);var y=f.prototype=m.prototype=Object.create(h);function w(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,e){var r;o(this,"_invoke",{value:function(o,a){function i(){return new e((function(r,i){!function o(r,a,i,l){var c=d(t[r],t,a);if("throw"!==c.type){var s=c.arg,p=s.value;return p&&"object"==M(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){o("next",t,i,l)}),(function(t){o("throw",t,i,l)})):e.resolve(p).then((function(t){s.value=t,i(s)}),(function(t){return o("throw",t,i,l)}))}l(c.arg)}(o,a,r,i)}))}return r=r?r.then(i,i):i()}})}function v(t,e,n){var o="suspendedStart";return function(r,a){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw a;return C()}for(n.method=r,n.arg=a;;){var i=n.delegate;if(i){var l=k(i,n);if(l){if(l===p)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=d(t,e,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===p)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function k(t,e){var n=e.method,o=t.iterator[n];if(void 0===o)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,k(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),p;var r=d(o,t.iterator,e.arg);if("throw"===r.type)return e.method="throw",e.arg=r.arg,e.delegate=null,p;var a=r.arg;return a?a.done?(e[t.resultName]=a.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function W(t){if(t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,r=function e(){for(;++o<t.length;)if(n.call(t,o))return e.value=t[o],e.done=!1,e;return e.value=void 0,e.done=!0,e};return r.next=r}}return{next:C}}function C(){return{value:void 0,done:!0}}return u.prototype=f,o(y,"constructor",{value:f,configurable:!0}),o(f,"constructor",{value:u,configurable:!0}),u.displayName=c(f,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===u||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,f):(t.__proto__=f,c(t,l,"GeneratorFunction")),t.prototype=Object.create(y),t},t.awrap=function(t){return{__await:t}},w(x.prototype),c(x.prototype,i,(function(){return this})),t.AsyncIterator=x,t.async=function(e,n,o,r,a){void 0===a&&(a=Promise);var i=new x(s(e,n,o,r),a);return t.isGeneratorFunction(n)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},w(y),c(y,l,"Generator"),c(y,a,(function(){return this})),c(y,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var o in e)n.push(o);return n.reverse(),function t(){for(;n.length;){var o=n.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},t.values=W,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(P),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function o(n,o){return i.type="throw",i.arg=t,e.next=n,o&&(e.method="next",e.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r],i=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,p):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),P(n),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var o=n.completion;if("throw"===o.type){var r=o.arg;P(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:W(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),p}},t}function N(t,e,n,o,r,a,i){try{var l=t[a](i),c=l.value}catch(t){return void n(t)}l.done?e(c):Promise.resolve(c).then(o,r)}function D(t){return function(){var e=this,n=arguments;return new Promise((function(o,r){var a=t.apply(e,n);function i(t){N(a,o,r,i,l,"next",t)}function l(t){N(a,o,r,i,l,"throw",t)}i(void 0)}))}}function I(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,H(o.key),o)}}function z(t,e){return(z=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function R(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,o=G(t);if(e){var r=G(this).constructor;n=Reflect.construct(o,arguments,r)}else n=o.apply(this,arguments);return F(this,n)}}function F(t,e){if(e&&("object"===M(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return V(t)}function V(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function G(t){return(G=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function B(t,e,n){return(e=H(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function H(t){var e=function(t,e){if("object"!==M(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==M(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===M(e)?e:String(e)}var U={CENTER:"center",TOP_LEFT:"topLeft",TOP_RIGHT:"topRight",TOP_CENTER:"topCenter",BOT_LEFT:"bottomLeft",BOT_RIGHT:"bottomRight",BOT_CENTER:"bottomCenter"},Q="fontSize",q="text",Y="color",K="opacity",J="font",$="isBolded",X="isItalic",Z="isUnderlined",tt=(B(o={},"location",U.CENTER),B(o,Q,48),B(o,q,""),B(o,Y,new window.Core.Annotations.Color(228,66,52)),B(o,K,100),B(o,J,W[0]),B(o,$,!1),B(o,X,!1),B(o,Z,!1),o),et=(B(r={},U.CENTER,"diagonal"),B(r,U.TOP_LEFT,"headerLeft"),B(r,U.TOP_RIGHT,"headerRight"),B(r,U.TOP_CENTER,"headerCenter"),B(r,U.BOT_LEFT,"footerLeft"),B(r,U.BOT_RIGHT,"footerRight"),B(r,U.BOT_CENTER,"footerCenter"),r),nt=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&z(t,e)}(a,t);var e,n,o,r=R(a);function a(t){var e;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,a),B(V(e=r.call(this,t)),"handleWatermarkOnVisibilityChanged",(function(){e.props.isVisible?e.setState({locationSettings:e.state.previousLocationSettings},D(T().mark((function t(){return T().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,p.a.getWatermark();case 2:e.preExistingWatermark=t.sent,e.addWatermarks();case 4:case"end":return t.stop()}}),t)})))):(e.removeWatermarkCreatedByModal(),p.a.setWatermark(e.preExistingWatermark))})),B(V(e),"addWatermarks",(function(){var t=e.createWatermarks(),n=e.props.t;p.a.setWatermark(t);var o=p.a.getPageHeight(e.props.pageIndexToView+1),r=300/p.a.getPageWidth(e.props.pageIndexToView+1),a=300/o,i=Math.min(a,r),l=e.props.pageIndexToView+1;p.a.getDocument().loadCanvas({pageNumber:l,zoom:i,drawComplete:function(t){var o=e.canvasContainerRef.current.childNodes;o&&o.length>0&&e.canvasContainerRef.current.removeChild(o[0]),t.style.border=e.canvasContainerRef.current.style.border,t.style.height=e.canvasContainerRef.current.style.height,t.style.backgroundColor=e.canvasContainerRef.current.style.backgroundColor,t.setAttribute("role","img"),t.setAttribute("aria-label","".concat(n("action.page")," ").concat(l)),e.canvasContainerRef.current.appendChild(t)}})})),B(V(e),"constructWatermarkOption",(function(t){var e=[];return t.isBolded&&e.push(p.a.getFontStyles().BOLD),t.isItalic&&e.push(p.a.getFontStyles().ITALIC),t.isUnderlined&&e.push(p.a.getFontStyles().UNDERLINE),{fontSize:t.fontSize,fontFamily:t.font,color:t.color.toString(),opacity:t.opacity,text:t.text,fontStyles:e}})),B(V(e),"createWatermarks",(function(){var t={};return Object.keys(U).forEach((function(n){var o=e.constructWatermarkOption(e.state.locationSettings[n]);t[et[U[n]]]=o})),t})),B(V(e),"removeWatermarkCreatedByModal",(function(){p.a.setWatermark({})})),B(V(e),"closeModal",(function(){e.props.modalClosed()})),B(V(e),"handleInputChange",(function(t,n){var o=L({},e.state.locationSettings),r=e.getCurrentSelectedLocation();o[r]=L(L({},o[r]),{},B({},t,n)),e.setState({locationSettings:o},(function(){e.addWatermarks()}))})),B(V(e),"resetForm",(function(t){t.preventDefault();var n=e.resetLocationSettings();e.setState({locationSettings:n},(function(){return e.addWatermarks()}))})),B(V(e),"onOkPressed",(function(){e.setState({previousLocationSettings:e.state.locationSettings},(function(){e.props.modalClosed();var t=e.createWatermarks();e.props.formSubmitted(t)}))})),B(V(e),"setColorPaletteVisibility",(function(t){e.setState({isColorPaletteVisible:t})})),B(V(e),"onLocationChanged",(function(t){var n=L({},e.state.locationSettings);Object.keys(n).forEach((function(e){var o=n[e];o=L(L({},o),{},{isSelected:t===e}),n[e]=o})),e.setState({locationSettings:n},(function(){e.addWatermarks()}))})),B(V(e),"resetLocationSettings",(function(){var t={};return Object.keys(U).forEach((function(e){var n=L(L({},Object.assign({},(function(t){if(null==t)throw new TypeError("Cannot destructure "+t)}(tt),tt))),{},{isSelected:U[e]===tt.location});t[e]=n})),t})),B(V(e),"initializeLocationSettings",(function(){var t=e.resetLocationSettings();return e.props.watermarkLocations&&Object.keys(U).forEach((function(n){var o,r,a=U[n],i=null!==(o=e.props.watermarkLocations[et[a]])&&void 0!==o&&o;if(i){var l=e.constructWatermarkOption(i);t[n].text=l.text;var c=i.color.slice(5).replace(")","").split(","),s=new window.Core.Annotations.Color(c[0],c[1],c[2],c[3]);t[n].color=s,t[n].opacity=l.opacity,t[n].fontSize=l.fontSize,i.fontStyles&&(t[n].isBolded=i.fontStyles.includes("BOLD"),t[n].isItalic=i.fontStyles.includes("ITALIC"),t[n].isUnderlined=i.fontStyles.includes("UNDERLINE"));var d=null!==(r=i.fontFamily)&&void 0!==r&&r;d&&0!==d.trim().length||(t[n]=tt.font)}})),t})),B(V(e),"getKeyByValue",(function(t,e){return Object.keys(t).find((function(n){return t[n]===e}))})),B(V(e),"getCurrentSelectedLocation",(function(){return Object.keys(e.state.locationSettings).find((function(t){return e.state.locationSettings[t].isSelected}))})),B(V(e),"onColorChanged",(function(t){var n=e.getCurrentSelectedLocation(),o=e.state.locationSettings[n];o[Y]=new window.Core.Annotations.Color(t.R,t.G,t.B);var r=L({},e.state.locationSettings);o[q]||Object.keys(U).forEach((function(e){var n=r[e];n[q]||(n[Y]=new window.Core.Annotations.Color(t.R,t.G,t.B))})),e.setState({locationSettings:r},(function(){e.addWatermarks()}))}));var n=e.initializeLocationSettings();return e.preExistingWatermark=void 0,e.state={isColorPaletteVisible:!1,locationSettings:n,previousLocationSettings:n,lockFocus:!1},e.canvasContainerRef=i.a.createRef(),e}return e=a,(n=[{key:"componentDidUpdate",value:function(t){var e=this;p.a.addEventListener("documentLoaded",this.closeModal),this.props.isVisible!==t.isVisible&&(this.props.isVisible?this.setState({lockFocus:!0}):this.setState({lockFocus:!1}),this.setState({isColorPaletteVisible:!1},(function(){return e.handleWatermarkOnVisibilityChanged()})))}},{key:"render",value:function(){var t=this;if(!this.props.isVisible)return null;var e=this.props,n=e.t,o=e.isCustomizableUI,r=this.getCurrentSelectedLocation(),a=this.state.locationSettings[r],l=a[Y].toHexString(),c=Object(_.k)()?160:314,s=Object(_.k)()?328:314;return i.a.createElement(S.a,{className:"Modal Watermark",id:"watermarkModal","data-element":"watermarkModal"},i.a.createElement(C.a,{isOpen:this.state.lockFocus,title:"option.watermark.addWatermark",closeButtonDataElement:"watermarkModalCloseButton",onCloseClick:this.closeModal,swipeToClose:!0,closeHandler:this.closeModal},i.a.createElement("div",{className:"swipe-indicator"}),i.a.createElement("div",{className:"form-content-container"},i.a.createElement("div",{className:"canvas-container",ref:this.canvasContainerRef}),i.a.createElement("div",{className:"watermark-settings"},i.a.createElement("form",{id:"form",onSubmit:function(t){return t.preventDefault()}},i.a.createElement("div",{className:"form-field"},i.a.createElement("label",{className:"section-label print-quality-section-label",htmlFor:"location",id:"watermark-location-dropdown-label"},n("option.watermark.location")),i.a.createElement(A.a,{id:"location",labelledById:"watermark-location-dropdown-label",dataElement:"watermarkLocation",items:Object.keys(U),getTranslationLabel:function(t){return n("option.watermark.locations.".concat(U[t]))},currentSelectionKey:r,onClickItem:this.onLocationChanged,width:s}),i.a.createElement("div",{className:"separator divider"})),i.a.createElement("div",{className:"form-field"},i.a.createElement("label",{htmlFor:"textInput"},n("option.watermark.text")),i.a.createElement("input",{className:"text-input",id:"textInput",value:a[q],onChange:function(e){return t.handleInputChange(q,e.target.value)},type:"text"})),i.a.createElement("div",{className:"font-form-fields"},i.a.createElement("div",{className:"form-font-type"},i.a.createElement("label",{htmlFor:"fonts",id:"watermark-font-dropdown-label"},n("option.watermark.font")),i.a.createElement(A.a,{id:"fonts",labelledById:"watermark-font-dropdown-label",dataElement:"watermarkFont",items:W,currentSelectionKey:a[J],onClickItem:function(e){return t.handleInputChange(J,e)},width:c})),i.a.createElement("div",{className:"form-font-size"},i.a.createElement("label",{htmlFor:"fontSize"},n("option.watermark.size")),i.a.createElement(O.a,{fontSize:a[Q],key:"fontSize",fontUnit:"pt",onFontSizeChange:function(e){return t.handleInputChange(Q,Number.parseInt(e))},maxFontSize:1600,initialFontValue:1,initialMaxFontValue:512,width:c}))),i.a.createElement("div",{className:"form-field opacity-slider",id:"opacitySlider"},i.a.createElement(k.a,{dataElement:"watermarkOpacitySlider",property:"Opacity",displayProperty:"opacity",min:0,max:100,step:1,value:a[K],getDisplayValue:function(t){return"".concat(Math.round(t),"%")},withInputField:o,inputFieldType:"number",onSliderChange:function(){},onStyleChange:function(e,n){return t.handleInputChange(K,Math.round(100*n))},getLocalValue:function(t){return parseInt(t)/100}})),i.a.createElement("div",{className:"form-field"},i.a.createElement("label",null,n("option.watermark.style")),i.a.createElement("div",{className:"style-container"},i.a.createElement(P.a,{id:"currentColorCell",className:"colorSelect ".concat("#FFFFFF"===l?"white-color":""),ariaLabel:"colorSelectButton",style:{backgroundColor:l},onClick:function(){return t.setColorPaletteVisibility(!t.state.isColorPaletteVisible)}}),i.a.createElement("div",{className:"style-container"},i.a.createElement(P.a,{dataElement:"boldText",img:"icon-text-bold",isActive:a[$],onClick:function(){return t.handleInputChange($,!a[$])}}),i.a.createElement(P.a,{dataElement:"italicizeText",img:"icon-text-italic",isActive:a[X],onClick:function(){return t.handleInputChange(X,!a[X])}}),i.a.createElement(P.a,{dataElement:"underlineText",img:"icon-text-underline",isActive:a[Z],onClick:function(){return t.handleInputChange(Z,!a[Z])}}))),this.state.isColorPaletteVisible&&i.a.createElement("div",{className:"Popup StylePopup",id:"stylePopup",onClick:function(){return t.setColorPaletteVisibility(!1)}},i.a.createElement(v.a,{color:a[Y],property:"TextColor",onStyleChange:function(e,n){t.onColorChanged(n),t.setColorPaletteVisibility(!1)}})))))),i.a.createElement("div",{className:"divider"}),i.a.createElement("div",{className:"button-container"},i.a.createElement("button",{className:"reset-settings",id:"reset",onClick:this.resetForm},n("option.watermark.resetAllSettings")),i.a.createElement("button",{className:"ok button",id:"submit",onClick:this.onOkPressed},n("action.add")))))}}])&&I(e.prototype,n),o&&I(e,o),Object.defineProperty(e,"prototype",{writable:!1}),a}(i.a.PureComponent);B(nt,"propTypes",{isVisible:y.a.bool,pageIndexToView:y.a.number,watermarkLocations:y.a.object,modalClosed:y.a.func,formSubmitted:y.a.func,t:y.a.func.isRequired,isCustomizableUI:y.a.bool});var ot=Object(E.a)()(nt),rt=n(288),at=n(429),it=n(442),lt=n(99);n(1760);function ct(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,s=!1;try{if(a=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){s=!0,r=t}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return st(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return st(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function st(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var dt=function t(e){var n=e.isDisabled,o=e.isOpen,r=e.isApplyWatermarkDisabled,m=e.isFullAPIEnabled,u=e.currentPage,f=e.printQuality,h=e.isGrayscale,g=e.setIsGrayscale,v=e.setIsCurrentView,k=e.isCurrentViewDisabled,E=e.includeAnnotations,O=e.setIncludeAnnotations,W=e.includeComments,_=e.setIncludeComments,M=e.isWatermarkModalVisible,j=e.setIsWatermarkModalVisible,L=e.watermarkModalOptions,T=e.existingWatermarksRef,N=e.setAllowWatermarkModal,D=e.closePrintModal,I=e.createPagesAndPrint,z=e.pagesToPrint,R=e.setPagesToPrint,F=e.count,V=e.isPrinting,G=e.layoutMode,B=e.useEmbeddedPrint,H=e.pageLabels;t.propTypes={isDisabled:y.a.bool,isOpen:y.a.bool,isApplyWatermarkDisabled:y.a.bool,isFullAPIEnabled:y.a.bool,currentPage:y.a.number,printQuality:y.a.number,isGrayscale:y.a.bool,setIsGrayscale:y.a.func,setIsCurrentView:y.a.func,isCurrentViewDisabled:y.a.bool,includeAnnotations:y.a.bool,setIncludeAnnotations:y.a.func,includeComments:y.a.bool,setIncludeComments:y.a.func,isWatermarkModalVisible:y.a.bool,setIsWatermarkModalVisible:y.a.func,watermarkModalOptions:y.a.object,existingWatermarksRef:y.a.object,setAllowWatermarkModal:y.a.func,closePrintModal:y.a.func,createPagesAndPrint:y.a.func,pagesToPrint:y.a.array,setPagesToPrint:y.a.func,count:y.a.number,isPrinting:y.a.bool,layoutMode:y.a.string,useEmbeddedPrint:y.a.bool,pageLabels:y.a.array};var U=Object(s.d)(),Q=ct(Object(at.a)(),1)[0],q=Object(a.useRef)(),Y=Object(a.useRef)(),K=Object(a.useRef)(),J=Object(a.useRef)(),$=Object(a.useRef)(),X=ct(Object(a.useState)(!1),2),Z=X[0],tt=X[1],et=ct(Object(a.useState)([]),2),nt=et[0],st=et[1],dt=ct(Object(a.useState)(""),2),pt=dt[0],mt=dt[1],ut=ct(Object(a.useState)(!1),2),ft=ut[0],ht=ut[1],bt=Object(s.e)((function(t){var e;return null===(e=c.a.getFeatureFlags(t))||void 0===e?void 0:e.customizableUI})),gt={1:"".concat(Q("option.print.qualityNormal")),2:"".concat(Q("option.print.qualityHigh"))},yt=function(t){j(t)},wt=Object(w.a)("Modal PrintModal",{isOpen:o}),xt=i.a.createElement(i.a.Fragment,null,i.a.createElement("label",{htmlFor:"specifyPagesInput",className:"specifyPagesChoiceLabel"},i.a.createElement("span",null,Q("option.print.specifyPages")),ft&&i.a.createElement("span",{className:"specifyPagesExampleLabel"},"- ",Q("option.thumbnailPanel.multiSelectPagesExample"))),ft&&i.a.createElement("div",{className:b()("page-number-input-container",{error:!!pt})},i.a.createElement(it.a,{id:"specifyPagesInput",selectedPageNumbers:nt,pageCount:p.a.getTotalPages(),onSelectedPageNumbersChange:function(t){t.length>0&&(mt(""),st(t))},onBlurHandler:st,onError:function(t){t&&mt("".concat(Q("message.errorPageNumber")," ").concat(p.a.getTotalPages()))},pageNumberError:pt,customPageLabels:H,enablePageLabels:!0})));Object(a.useEffect)((function(){vt()}),[nt]);var vt=function(){var t,e,n,o,r,a,i,l=[];if(v(null===(t=$.current)||void 0===t?void 0:t.checked),ht(null===(e=K.current)||void 0===e?void 0:e.checked),null!==(n=q.current)&&void 0!==n&&n.checked||null!==(o=$.current)&&void 0!==o&&o.checked&&Z)for(var c=1;c<=p.a.getTotalPages();c++)l.push(c);else if(null!==(r=Y.current)&&void 0!==r&&r.checked){var s=p.a.getTotalPages();switch(G){case x.a.FacingCover:case x.a.FacingCoverContinuous:1===u||u===s&&s%2==0?l.push(u):l=u%2?[u-1,u]:[u,u+1];break;case x.a.FacingContinuous:case x.a.Facing:u===s&&s%2==1?l.push(u):l=u%2?[u,u+1]:[u-1,u];break;default:l.push(u)}}else null!==(a=K.current)&&void 0!==a&&a.checked?l=nt:null!==(i=$.current)&&void 0!==i&&i.checked&&(l=[u]);R(l)};Object(a.useEffect)((function(){return vt(),p.a.getWatermark().then((function(t){N(null==t||0===Object.keys(t).length),T.current=t})),function(){p.a.setWatermark(T.current),j(!1)}}),[]),Object(a.useEffect)((function(){"xod"!==p.a.getDocument().getType()&&B?tt(!0):tt(!1)}),[B]);var kt=Object(lt.a)((function(){V||yt(!0)}));return n?null:i.a.createElement(i.a.Fragment,null,i.a.createElement(ot,{isVisible:!(!o||!M),pageIndexToView:u-1,modalClosed:yt,formSubmitted:function(t){return U(l.a.setWatermarkModalOptions(t))},watermarkLocations:L,isCustomizableUI:bt}),i.a.createElement("div",{className:wt,"data-element":d.a.PRINT_MODAL},i.a.createElement(C.a,{isOpen:o&&!M,title:"option.print.printSettings",containerOnClick:function(t){return t.stopPropagation()},onCloseClick:D,closeButtonDataElement:"printModalCloseButton",swipeToClose:!0,closeHandler:D},i.a.createElement("div",{className:"swipe-indicator"}),i.a.createElement("div",{className:"settings"},i.a.createElement("div",{className:"section"},i.a.createElement("div",{className:"section-label"},"".concat(Q("option.print.pages"),":")),i.a.createElement("form",{className:"settings-form",onChange:vt,onSubmit:I},i.a.createElement(rt.a,{dataElement:"allPagesPrintOption",ref:q,id:"all-pages",name:"pages",radio:!0,label:Q("option.print.all"),defaultChecked:!0,disabled:V,center:!0}),i.a.createElement(rt.a,{dataElement:"currentPagePrintOption",ref:Y,id:"current-page",name:"pages",radio:!0,label:Q("option.print.current"),disabled:V,center:!0}),i.a.createElement(rt.a,{dataElement:"currentViewPrintOption",ref:$,id:"current-view",name:"pages",radio:!0,label:Q("option.print.view"),disabled:k,center:!0,title:Q("option.print.printCurrentDisabled")}),i.a.createElement(rt.a,{dataElement:"customPagesPrintOption",ref:K,id:"custom-pages",name:"pages",className:"specify-pages-choice",radio:!0,label:xt,disabled:V,center:!0}),i.a.createElement(rt.a,{dataElement:"annotationsPrintOption",id:"include-annotations",name:"annotations",label:Q("option.print.includeAnnotations"),disabled:V,onChange:function(){return O((function(t){return!t}))},checked:E,center:!0}),Z&&i.a.createElement(i.a.Fragment,null,m&&i.a.createElement(i.a.Fragment,null,i.a.createElement(rt.a,{dataElement:"grayscalePrintOption",id:"print-grayscale",name:"grayscale",label:Q("option.print.printGrayscale"),disabled:V,onChange:function(){return g((function(t){return!t}))},checked:h,center:!0}),i.a.createElement(rt.a,{dataElement:"commentsPrintOption",ref:J,id:"include-comments",name:"comments",label:Q("option.print.includeComments"),onChange:function(){return _((function(t){return!t}))},disabled:V,checked:W,center:!0}))),!Z&&i.a.createElement(i.a.Fragment,null,i.a.createElement(rt.a,{dataElement:"grayscalePrintOption",id:"print-grayscale",name:"grayscale",label:Q("option.print.printGrayscale"),disabled:V,onChange:function(){return g((function(t){return!t}))},checked:h,center:!0}),i.a.createElement(rt.a,{dataElement:"commentsPrintOption",ref:J,id:"include-comments",name:"comments",label:Q("option.print.includeComments"),onChange:function(){return _((function(t){return!t}))},disabled:V,checked:W,center:!0})))),!Z&&i.a.createElement(S.a,{className:"section",dataElement:d.a.PRINT_QUALITY},i.a.createElement("label",{className:"section-label print-quality-section-label",htmlFor:"printQualityOptions",id:"print-quality-options-label"},"".concat(Q("option.print.pageQuality"),":")),i.a.createElement(A.a,{id:"printQualityOptions",labelledById:"print-quality-options-label",dataElement:"printQualityOptions",items:Object.keys(gt),getDisplayValue:function(t){return gt[t]},onClickItem:function(t){U(l.a.setPrintQuality(Number(t)))},currentSelectionKey:null==f?void 0:f.toString(),width:274})),i.a.createElement("div",{className:"total"},V?i.a.createElement("div",null,"".concat(Q("message.processing")," ").concat(F,"/").concat(z.length)):i.a.createElement("div",null,Q("message.printTotalPageCount",{count:z.length}))),!r&&i.a.createElement(S.a,{className:"section watermark-section",dataElement:d.a.PRINT_WATERMARK},i.a.createElement("div",{className:"section-label"},Q("option.watermark.title")),i.a.createElement("button",{"data-element":"applyWatermark",className:"apply-watermark",disabled:V,onClick:kt},Q("option.watermark.addNew")))),i.a.createElement("div",{className:"divider"}),i.a.createElement("div",{className:"buttons"},i.a.createElement(P.a,{className:"button",onClick:I,label:Q("action.print"),ariaLabel:Q("action.print")})))))},pt=n(172);function mt(t){return(mt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ut(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ut=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},r="function"==typeof Symbol?Symbol:{},a=r.iterator||"@@iterator",i=r.asyncIterator||"@@asyncIterator",l=r.toStringTag||"@@toStringTag";function c(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,n){return t[e]=n}}function s(t,e,n,r){var a=e&&e.prototype instanceof m?e:m,i=Object.create(a.prototype),l=new O(r||[]);return o(i,"_invoke",{value:v(t,n,l)}),i}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=s;var p={};function m(){}function u(){}function f(){}var h={};c(h,a,(function(){return this}));var b=Object.getPrototypeOf,g=b&&b(b(W([])));g&&g!==e&&n.call(g,a)&&(h=g);var y=f.prototype=m.prototype=Object.create(h);function w(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,e){var r;o(this,"_invoke",{value:function(o,a){function i(){return new e((function(r,i){!function o(r,a,i,l){var c=d(t[r],t,a);if("throw"!==c.type){var s=c.arg,p=s.value;return p&&"object"==mt(p)&&n.call(p,"__await")?e.resolve(p.__await).then((function(t){o("next",t,i,l)}),(function(t){o("throw",t,i,l)})):e.resolve(p).then((function(t){s.value=t,i(s)}),(function(t){return o("throw",t,i,l)}))}l(c.arg)}(o,a,r,i)}))}return r=r?r.then(i,i):i()}})}function v(t,e,n){var o="suspendedStart";return function(r,a){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw a;return C()}for(n.method=r,n.arg=a;;){var i=n.delegate;if(i){var l=k(i,n);if(l){if(l===p)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=d(t,e,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===p)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function k(t,e){var n=e.method,o=t.iterator[n];if(void 0===o)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,k(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),p;var r=d(o,t.iterator,e.arg);if("throw"===r.type)return e.method="throw",e.arg=r.arg,e.delegate=null,p;var a=r.arg;return a?a.done?(e[t.resultName]=a.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function P(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function O(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function W(t){if(t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,r=function e(){for(;++o<t.length;)if(n.call(t,o))return e.value=t[o],e.done=!1,e;return e.value=void 0,e.done=!0,e};return r.next=r}}return{next:C}}function C(){return{value:void 0,done:!0}}return u.prototype=f,o(y,"constructor",{value:f,configurable:!0}),o(f,"constructor",{value:u,configurable:!0}),u.displayName=c(f,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===u||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,f):(t.__proto__=f,c(t,l,"GeneratorFunction")),t.prototype=Object.create(y),t},t.awrap=function(t){return{__await:t}},w(x.prototype),c(x.prototype,i,(function(){return this})),t.AsyncIterator=x,t.async=function(e,n,o,r,a){void 0===a&&(a=Promise);var i=new x(s(e,n,o,r),a);return t.isGeneratorFunction(n)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},w(y),c(y,l,"Generator"),c(y,a,(function(){return this})),c(y,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var o in e)n.push(o);return n.reverse(),function t(){for(;n.length;){var o=n.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},t.values=W,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(P),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function o(n,o){return i.type="throw",i.arg=t,e.next=n,o&&(e.method="next",e.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var a=this.tryEntries[r],i=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=t,i.arg=e,a?(this.method="next",this.next=a.finallyLoc,p):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),P(n),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var o=n.completion;if("throw"===o.type){var r=o.arg;P(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:W(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),p}},t}function ft(t,e,n,o,r,a,i){try{var l=t[a](i),c=l.value}catch(t){return void n(t)}l.done?e(c):Promise.resolve(c).then(o,r)}function ht(t){return function(){var e=this,n=arguments;return new Promise((function(o,r){var a=t.apply(e,n);function i(t){ft(a,o,r,i,l,"next",t)}function l(t){ft(a,o,r,i,l,"throw",t)}i(void 0)}))}}function bt(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,a,i,l=[],c=!0,s=!1;try{if(a=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=a.call(n)).done)&&(l.push(o.value),l.length!==e);c=!0);}catch(t){s=!0,r=t}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw r}}return l}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return gt(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return gt(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function gt(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var yt=function(){var t=Object(s.d)(),e=bt(Object(s.e)((function(t){return[c.a.isElementDisabled(t,d.a.PRINT_MODAL),c.a.isElementOpen(t,d.a.PRINT_MODAL),c.a.isElementDisabled(t,"applyWatermark"),c.a.getCurrentPage(t),c.a.getPrintQuality(t),c.a.getDefaultPrintOptions(t),c.a.getPageLabels(t,"pageLabels"),c.a.getSortStrategy(t),c.a.getColorMap(t),c.a.getDisplayMode(t),c.a.getPrintedNoteDateFormat(t),c.a.getCurrentLanguage(t),c.a.getWatermarkModalOptions(t),c.a.getTimezone(t),c.a.isEmbedPrintSupported(t,"useEmbeddedPrint")]}),s.c),15),n=e[0],o=e[1],r=e[2],h=e[3],b=e[4],g=e[5],y=e[6],w=e[7],x=e[8],v=e[9],k=e[10],E=e[11],P=e[12],O=e[13],W=e[14],C=Object(a.useRef)(),S=bt(Object(a.useState)(!1),2),A=S[0],_=S[1],M=bt(Object(a.useState)(-1),2),j=M[0],L=M[1],T=bt(Object(a.useState)(!1),2),N=T[0],D=T[1],I=bt(Object(a.useState)([]),2),z=I[0],R=I[1],F=bt(Object(a.useState)(!1),2),V=F[0],G=F[1],B=bt(Object(a.useState)(!1),2),H=B[0],U=B[1],Q=bt(Object(a.useState)(!0),2),q=Q[0],Y=Q[1],K=bt(Object(a.useState)(!1),2),J=K[0],$=K[1],X=bt(Object(a.useState)(!1),2),Z=X[0],tt=X[1],et=bt(Object(a.useState)(!1),2),nt=et[0],ot=et[1];Object(a.useEffect)((function(){var t,e,n;g&&(Y(null!==(t=g.includeAnnotations)&&void 0!==t?t:q),$(null!==(e=g.includeComments)&&void 0!==e?e:J),D(null!==(n=g.maintainPageOrientation)&&void 0!==n?n:N))}),[g]);var rt=j>=0;Object(a.useEffect)((function(){o&&(W&&at(),t(l.a.closeElements([d.a.SIGNATURE_MODAL,d.a.LOADING_MODAL,d.a.PROGRESS_MODAL,d.a.ERROR_MODAL])))}),[o,t]);var at=function(){if(!nt){var t=p.a.getDisplayModeObject().getVisiblePages(0,0);(null==t?void 0:t.length)>1?ot(!0):ot(!1)}},it=function(){var t=ht(ut().mark((function t(){var e,n,o;return ut().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e={isCurrentView:Z,includeAnnotations:q,includeComments:J,watermarkModalOptions:P,pagesToPrint:z},n=p.a.getDocument(),o=p.a.getAnnotationManager(),t.t0=f.c,t.next=6,Object(f.d)(e,n,o);case 6:t.t1=t.sent,(0,t.t0)(t.t1);case 8:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),lt=function(t){if(t.preventDefault(),!(z.length<1)){L(0),A?p.a.setWatermark(P):p.a.setWatermark(C.current);var e={includeComments:J,includeAnnotations:q,maintainPageOrientation:N,printQuality:b,sortStrategy:w,colorMap:x,printedNoteDateFormat:k,isCurrentView:Z,language:E,timezone:O,createCanvases:!1,isGrayscale:V},n=Object(u.b)(z,e,void 0);n.forEach(function(){var t=ht(ut().mark((function t(e){return ut().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e;case 2:L(j<z.length&&(-1!==j?j+1:j));case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()),Promise.all(n).then((function(t){Object(m.b)(t),st()})).catch((function(t){console.error(t),L(-1)}))}},ct=function(){L(-1),t(l.a.closeElement(d.a.PRINT_MODAL))},st=Object(pt.a)(ct);return i.a.createElement(dt,{isDisabled:n,isOpen:o,isApplyWatermarkDisabled:r,isFullAPIEnabled:p.a.isFullPDFEnabled(),currentPage:h,printQuality:b,isGrayscale:V,setIsGrayscale:G,setIsCurrentView:tt,isCurrentViewDisabled:nt,checkCurrentView:at,includeAnnotations:q,setIncludeAnnotations:Y,includeComments:J,setIncludeComments:$,isWatermarkModalVisible:H,setIsWatermarkModalVisible:U,watermarkModalOptions:P,existingWatermarksRef:C,setAllowWatermarkModal:_,closePrintModal:ct,createPagesAndPrint:function(t){var e=p.a.getDocument().getType();W&&"xod"===e&&console.warn("Falling back to raster printing, XOD files and Embedded Printing is not supported"),W&&"xod"!==e?it():lt(t)},pagesToPrint:z,setPagesToPrint:R,count:j,isPrinting:rt,pageLabels:y,layoutMode:v,useEmbeddedPrint:W})};e.default=yt}}]);
//# sourceMappingURL=chunk.55.js.map