{"version": 3, "sources": ["webpack:///./src/ui/src/components/ModularComponents/PresetButton/buttons/SheetEditor/CellDecoratorButton.js"], "names": ["propTypes", "styleType", "PropTypes", "oneOf", "Object", "values", "STYLE_TOGGLE_OPTIONS", "isRequired", "isFlyoutItem", "bool", "style", "object", "className", "string", "CellDecoratorButton", "forwardRef", "props", "ref", "lowerCaseStyleType", "char<PERSON>t", "toLowerCase", "slice", "menuItems", "dataElement", "icon", "title", "handleClick", "onClick", "additionalClass", "aria<PERSON>urrent", "isActive", "img", "displayName"], "mappings": "kaAOA,IAAMA,EAAY,CAChBC,UAAWC,IAAUC,MAAMC,OAAOC,OAAOC,MAAuBC,WAChEC,aAAcN,IAAUO,KACxBC,MAAOR,IAAUS,OACjBC,UAAWV,IAAUW,QAGjBC,EAAsBC,sBAAW,SAACC,EAAOC,GAC7C,IAAQT,EAA8CQ,EAA9CR,aAAcP,EAAgCe,EAAhCf,UAAWS,EAAqBM,EAArBN,MAAOE,EAAcI,EAAdJ,UAGlCM,EAAqBjB,EAAUkB,OAAO,GAAGC,cAAgBnB,EAAUoB,MAAM,GAC/E,EAAqCC,IAAU,GAAD,OAAIJ,EAAkB,WAA5DK,EAAW,EAAXA,YAAaC,EAAI,EAAJA,KAAMC,EAAK,EAALA,MAErBC,EAAc,aAIpB,OACElB,EACE,kBAAC,IAAmB,KACdQ,EAAK,CACTC,IAAKA,EACLU,QAASD,EACTE,gBAAuC,MAGvC,kBAAC,IAAY,CACXC,aAnBS,EAoBTC,UApBS,EAqBTP,YAAaA,EACbE,MAAOA,EACPM,IAAKP,EACLG,QAASD,EACThB,MAAOA,EACPE,UAAWA,OAMrBE,EAAoBd,UAAYA,EAChCc,EAAoBkB,YAAc,sBAEnBlB", "file": "chunks/chunk.90.js", "sourcesContent": ["import React, { forwardRef } from 'react';\nimport ActionButton from 'components/ActionButton';\nimport PropTypes from 'prop-types';\nimport { STYLE_TOGGLE_OPTIONS } from 'constants/officeEditor';\nimport FlyoutItemContainer from '../../../FlyoutItemContainer';\nimport { menuItems } from '../../../Helpers/menuItems';\n\nconst propTypes = {\n  styleType: PropTypes.oneOf(Object.values(STYLE_TOGGLE_OPTIONS)).isRequired,\n  isFlyoutItem: PropTypes.bool,\n  style: PropTypes.object,\n  className: PropTypes.string,\n};\n\nconst CellDecoratorButton = forwardRef((props, ref) => {\n  const { isFlyoutItem, styleType, style, className } = props;\n  const isActive = false;\n\n  const lowerCaseStyleType = styleType.charAt(0).toLowerCase() + styleType.slice(1);\n  const { dataElement, icon, title } = menuItems[`${lowerCaseStyleType}Button`];\n\n  const handleClick = () => {\n    // handle button click\n  };\n\n  return (\n    isFlyoutItem ?\n      <FlyoutItemContainer\n        {...props}\n        ref={ref}\n        onClick={handleClick}\n        additionalClass={isActive ? 'active' : ''}\n      />\n      : (\n        <ActionButton\n          ariaCurrent={isActive}\n          isActive={isActive}\n          dataElement={dataElement}\n          title={title}\n          img={icon}\n          onClick={handleClick}\n          style={style}\n          className={className}\n        />\n      )\n  );\n});\n\nCellDecoratorButton.propTypes = propTypes;\nCellDecoratorButton.displayName = 'CellDecoratorButton';\n\nexport default CellDecoratorButton;"], "sourceRoot": ""}