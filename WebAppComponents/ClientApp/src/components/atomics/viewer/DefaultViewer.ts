import type { Viewer } from '@/components/atomics/viewer/Viewer'
import type { Dropdown } from '@/components/dropdown/Dropdown'
import WebViewer, { Core, WebViewerInstance } from '@pdftron/webviewer'
import { WEBVIEWER_LIBRARY_LINK } from '@/shared/component-html'
import { css, html } from 'lit'
import { ColorState } from '@/enums/color-state'
import { ButtonType } from '@/components/atomics/button/Button'
import { Size } from '@/enums/size.ts'
import { when } from 'lit/directives/when.js'
import { CipherTool } from '@/shared/crypto'
import DialogFactory from '@/shared/dialog-service'

type Tools = typeof Core.Tools

type ToolType = {
	tool: Core.Tools.ToolNames
	icon: string,
	title: string
}

const LEFT_PANEL_ELEMENTS = [ 'signatureListPanel', 'stylePanel' ]
const RIGHT_PANEL_ELEMENTS = [ 'notesPanel', 'redactionPanel' ]

const LICENSE_KEY = 'levelbuild AG:OEM:LevelBuild::B+:AMS(20261228):A767BFD01FD784D0933352184F716F2F2292AC65A502C6111C0C08D644823DF5C7' as const
const SIGNATURE_ENCRYPTION_KEY = '+l3v3lbu!ldW!llF!ndY0u!+' as const
const SIGNATURE_STORAGE_KEY = 'lvl:component:viewer:signatures' as const

export class DefaultViewer {

	static styles = css`
		.web-viewer {
			display: grid;
			grid-template-rows: max-content auto;
			height: 100%;

			& iframe {
				pointer-events: none;
			}

			.no-drag {
				pointer-events: auto;
			}

			& .viewer__header {
				display: flex;
				flex-wrap: nowrap;
				justify-content: space-between;
				align-items: center;
				height: 4rem;
				padding: 0 var(--size-spacing-m);
				background-color: var(--cp-clr-background-lvl-0);
				box-shadow: 0 0 0.2rem 0 var(--cp-clr-shadow-weak);
				z-index: 1;

				& > * {
					display: flex;
					flex-wrap: nowrap;
				}
			}

			& [data-dropdown="zoomDropdown"] {
				--min-width: 7rem;
			}

			& .viewer__toolbar {
				position: absolute;
				bottom: var(--size-spacing-l);
				left: 50%;
				translate: -50% 0;
				z-index: 1;
				height: 4rem;

				display: flex;
				align-items: center;
				gap: var(--size-spacing-m);

				background-color: var(--cp-clr-background-lvl-0);
				border-radius: var(--size-radius-l);
				padding: 0 var(--size-spacing-s);
				box-shadow: 0 0 0.8rem 0 var(--cp-clr-shadow-medium);

				transition: opacity var(--animation-time-slow) ease-in-out;
				opacity: 0;
			}

			& .with-separator {
				position: relative;
			}

			& .with-separator::after {
				content: "";
				position: absolute;
				right: calc(-1 * var(--size-spacing-s));
				top: 50%;
				translate: 0 -50%;
				height: 2rem;
				width: 1px;
				background-color: var(--cp-clr-border-medium);
			}

			&:hover .viewer__toolbar, .viewer__toolbar:has([data-popup-open]) {
				opacity: 1;
			}
		}
	`

	private viewer: Viewer

	private webViewerLoading: boolean = false

	private webViewerInstance?: WebViewerInstance

	private documentViewer?: Core.DocumentViewer

	private tools?: Tools

	private currentTool?: Core.Tools.ToolNames

	private annotations: ToolType[] = []

	private currentAnnotation?: ToolType

	private shapes: ToolType[] = []

	private currentShape?: ToolType

	private zoom: number = -1

	private leftPanelVisible: boolean = false

	private rightPanelVisible: boolean = false

	private mutationObserver?: MutationObserver

	constructor(viewer: Viewer) {
		this.viewer = viewer
	}

	public render() {
		return html`
			<div class="web-viewer">
				${this.renderHeader()}
				<div class="viewer__body ${this.webViewerLoading ? 'loading' : ''}"></div>
				${this.viewer.readonly ? '' : this.renderToolbar()}
			</div>
		`
	}

	private renderHeader() {
		return html`
			<div class="viewer__header">
				<div class="header-left">
					<lvl-button icon="bars" tooltip="${this.viewer.localize('menu')}" tooltip-placement="bottom" data-dropdown="menuDropdown"></lvl-button>
					<lvl-dropdown name="menuDropdown" placement="bottom-start" offset-block-y="4">
						<lvl-menu size="medium">
							<lvl-menu-item icon-left="arrow-down-to-line" @click="${() => this.downloadDocument(true)}">${this.viewer.localize('download')}</lvl-menu-item>
							<lvl-menu-item icon-left="print" @click="${() => this.printDocument(true)}">${this.viewer.localize('print')}</lvl-menu-item>
						</lvl-menu>
					</lvl-dropdown>
					<lvl-button data-action="zoom-select" label="${this.zoom == -1 ? this.viewer.localize('zoom.auto') : Math.round(this.zoom * 100) + ' %'}"
											tooltip="${this.viewer.localize('zoom')}"
											tooltip-placement="bottom" data-dropdown="zoomDropdown" show-dropdown-anchor ?disabled="${this.viewer.loading}"></lvl-button>
					<lvl-dropdown name="zoomDropdown" placement="bottom-start" offset-block-y="4">
						<lvl-menu size="medium">
							<lvl-menu-item icon-left="" @click="${() => this.handleZoomClick(10)}">10 %</lvl-menu-item>
							<lvl-menu-item icon-left="" @click="${() => this.handleZoomClick(25)}">25 %</lvl-menu-item>
							<lvl-menu-item icon-left="" @click="${() => this.handleZoomClick(50)}">50 %</lvl-menu-item>
							<lvl-menu-item icon-left="" @click="${() => this.handleZoomClick(75)}">75 %</lvl-menu-item>
							<lvl-menu-item icon-left="" @click="${() => this.handleZoomClick(100)}">100 %</lvl-menu-item>
							<lvl-menu-item icon-left="arrow-up-right-and-arrow-down-left-from-center" @click="${() => this.handleZoomClick(-1)}">
								${this.viewer.localize('zoom.automatic')}
							</lvl-menu-item>
						</lvl-menu>
					</lvl-dropdown>
					<lvl-button data-action="zoom-out" icon="circle-minus" tooltip="${this.viewer.localize('zoom.out')}" tooltip-placement="bottom" data-action="zoom-out"
											@click="${() => this.handleZoom(0.5)}" ?disabled="${this.viewer.loading}"></lvl-button>
					<lvl-button data-action="zoom-in" icon="circle-plus" tooltip="${this.viewer.localize('zoom.in')}" tooltip-placement="bottom" data-action="zoom-in"
											@click="${() => this.handleZoom(2)}" ?disabled="${this.viewer.loading}"></lvl-button>
					<lvl-button data-action="fullscreen" icon="${this.viewer.fullscreenMode ? 'compress' : 'expand'}"
											tooltip="${this.viewer.fullscreenMode ? this.viewer.localize('fullscreen.off') : this.viewer.localize('fullscreen.on')}"
											tooltip-placement="bottom" @click="${() => this.viewer.toggleFullscreen()}" ?disabled="${this.viewer.loading}"></lvl-button>
					<lvl-button icon="ellipsis-vertical" bold tooltip="${this.viewer.localize('additionalActions')}" tooltip-placement="bottom" disabled
											data-tooltip="toolsTooltip"></lvl-button>
					<lvl-tooltip name="toolsTooltip" placement="bottom" orbit>${this.viewer.localize('mvpTooltip', this.viewer.localize('menu'))}</lvl-tooltip>
				</div>
				<div class="header-right">
					${when(!this.viewer.loading && this.leftPanelVisible, () => html`
						<lvl-button icon="sidebar" tooltip="${this.viewer.localize('closePanelLeft')}" tooltip-placement="bottom"
												@click="${() => this.handlePanelLeftButtonClick()}"></lvl-button>
					`)}
					${when(!this.viewer.loading && this.rightPanelVisible, () => html`
						<lvl-button icon="sidebar-flip" tooltip="${this.viewer.localize('closePanelLeft')}" tooltip-placement="bottom"
												@click="${() => this.handlePanelRightButtonClick()}"></lvl-button>
					`)}
				</div>
			</div>
		`
	}

	private renderToolbar() {
		if (this.viewer.loading || this.viewer.fileExtension != 'pdf' || this.tools == null)
			return ''
		const historyManager = this.documentViewer?.getAnnotationHistoryManager()
		return html`
			<div class="viewer__toolbar">
				<lvl-button class="with-separator" type="${this.currentTool == this.tools!.ToolNames.PAN ? ButtonType.Primary : ButtonType.Tertiary}" icon="hand"
										@click="${() => this.handleToolButtonClick(this.tools?.ToolNames.PAN)}"></lvl-button>
				<lvl-button icon="arrow-turn-left" @click="${() => this.handleUndoButtonClick()}" ?disabled="${!historyManager?.canUndo()}"></lvl-button>
				<lvl-button class="with-separator" icon="arrow-turn-right" @click="${() => this.handleRedoButtonClick()}"
										?disabled="${!historyManager?.canRedo()}"></lvl-button>
				<div class="flex--centered">
					<lvl-button data-action="annotate" icon="${this.currentAnnotation?.icon}"
											type="${this.currentAnnotation?.tool == this.currentTool ? ButtonType.Primary : ButtonType.Tertiary}"
											tooltip="${this.currentAnnotation?.title}" @click="${() => this.handleToolButtonClick(this.currentAnnotation?.tool)}"></lvl-button>
					<lvl-button show-dropdown-anchor data-dropdown="annotationDropdown" tooltip="${this.viewer.localize('chooseAnnotation')}"
											tooltip-placement="top"></lvl-button>
					<lvl-dropdown name="annotationDropdown" placement="top-start" offset-block-y="4">
						<lvl-menu size="${Size.Medium}">
							${Object.values(this.annotations).map(annotation => html`
								<lvl-menu-item icon-left="${annotation.icon}" value="${annotation.tool}"
															 ?selected="${this.currentAnnotation?.tool == annotation.tool}"
															 @click="${() => this.handleAnnotationChange(annotation)}"
								>${annotation.title}
								</lvl-menu-item>
							`)}
							</lvl-select-list>
						</lvl-menu>
					</lvl-dropdown>
				</div>
				<div class="flex--centered">
					<lvl-button data-action="shape" icon="${this.currentShape?.icon}"
											type="${this.currentShape?.tool == this.currentTool ? ButtonType.Primary : ButtonType.Tertiary}"
											tooltip="${this.currentShape?.title}" @click="${() => this.handleToolButtonClick(this.currentShape?.tool)}"></lvl-button>
					<lvl-button show-dropdown-anchor data-dropdown="shapeDropdown" tooltip="${this.viewer.localize('chooseShape')}"
											tooltip-placement="top"></lvl-button>
					<lvl-dropdown name="shapeDropdown" placement="top-start" offset-block-y="4">
						<lvl-menu size="${Size.Medium}">
							${Object.values(this.shapes).map(shape => html`
								<lvl-menu-item icon-left="${shape.icon}" value="${shape.tool}"
															 ?selected="${this.currentShape?.tool == shape.tool}"
															 @click="${() => this.handleShapeChange(shape)}">${shape.title}
								</lvl-menu-item>
							`)}
						</lvl-menu>
					</lvl-dropdown>
				</div>
				<lvl-button type="${this.currentTool == this.tools!.ToolNames.SIGNATURE ? ButtonType.Primary : ButtonType.Tertiary}" icon="signature"
										@click="${() => this.handleToolButtonClick(this.tools?.ToolNames.SIGNATURE)}"></lvl-button>
				<lvl-button class="with-separator" icon="ellipsis-vertical" bold tooltip="${this.viewer.localize('additionalActions')}" tooltip-placement="bottom"
										disabled data-tooltip="toolsTooltip"></lvl-button>
				<lvl-tooltip name="toolsTooltip" placement="bottom" orbit>${this.viewer.localize('mvpTooltip', this.viewer.localize('menu'))}</lvl-tooltip>
				<lvl-button class="with-min-size" icon="save" label="${this.viewer.localize('save')}" color="${ColorState.Active}"
										?disabled="${!this.viewer.hasChanges}" ?loading="${this.viewer.uploading}" @click="${() => this.handleSaveButtonClick()}"></lvl-button>
			</div>
		`
	}

	//#region public methods

	/**
	 * creates a new apryse viewer instance and loads the document
	 * @param url
	 */
	public async loadWebViewer(url: string) {
		let target = this.viewer._viewerWrapper.querySelector<HTMLElement>('.viewer__body')!
		if (this.webViewerLoading || !target)
			return

		this.webViewerLoading = true
		if (!this.webViewerInstance) {
			await WebViewer.Iframe({
				path: `${WEBVIEWER_LIBRARY_LINK}`, // path to the Apryse 'lib' folder on your server
				css: '/assets/apryse/main.css',
				licenseKey: LICENSE_KEY,
				fullAPI: true,
				enableRedaction: false,
				enableAnnotations: true,
				annotationUser: this.viewer.user,
				isReadOnly: this.viewer.readonly || this.viewer.fileExtension != 'pdf',
				disabledElements: [
					'default-top-header',
					'tools-header',
					'page-nav-floating-header',
					'contextMenuPopup'
				],
			}, target).then((instance) => {
				const { documentViewer } = instance.Core
				const UIEvents = instance.UI.Events
				const { Tools } = instance.Core

				const finishedLoadingAction = () => {
					const iframe = target.querySelector('iframe')
					if (!iframe)
						return
					iframe.dataset['cy'] = 'apryse-webviewer'
					this.webViewerLoading = false
					this.viewer.requestUpdate()

					// add event listeners so that the viewer can be dropped into / used normally
					let viewer = this.viewer._viewerWrapper
					viewer?.addEventListener("mousemove", () => {
						iframe.classList.add('no-drag');
					});
					viewer?.addEventListener("mouseleave", () => {
						iframe.classList.remove('no-drag');
					});
				}

				documentViewer.addEventListener('documentLoaded', () => finishedLoadingAction())
				instance.UI.addEventListener(UIEvents.LOAD_ERROR, () => finishedLoadingAction())

				documentViewer.addEventListener('annotationsLoaded', async () => {
					let annotationChangedTimeout: NodeJS.Timeout
					instance.Core.annotationManager.addEventListener('annotationChanged', (_annotation, _action, { isUndoRedo }) => {
						clearTimeout(annotationChangedTimeout)
						annotationChangedTimeout = setTimeout(() => {
							if (isUndoRedo) {
								this.viewer.hasChanges = true
								return
							}

							const hadAlreadyChanges = this.viewer.hasChanges
							this.viewer.hasChanges = true

							// trigger lit to rerender the history navigation
							if (hadAlreadyChanges)
								this.viewer.requestUpdate()
						}, 500)
					})

					const signatureTool = documentViewer.getTool('AnnotationCreateSignature') as Core.Tools.SignatureCreateTool
					signatureTool.addEventListener('signatureSaved', () => this.persistSignatures())
					signatureTool.addEventListener('signatureDeleted', () => this.persistSignatures())

					const savedSignaturesAsJson = await CipherTool.loadAndDecryptMessage(SIGNATURE_STORAGE_KEY, SIGNATURE_ENCRYPTION_KEY)
					if (savedSignaturesAsJson) {
						try {
							const signatures = JSON.parse(savedSignaturesAsJson)
							if (typeof signatures == 'object' && Array.isArray(signatures) && signatures.length > 0)
								signatureTool.importSignatures(signatures)
						} catch (e) {
							console.error('Exception occurred while importing signatures', e)
							// clean up wrong encoded signatures
							localStorage.removeItem(SIGNATURE_STORAGE_KEY)
						}
					} else {
						// clean up wrong encoded signatures
						localStorage.removeItem(SIGNATURE_STORAGE_KEY)
					}
				}, { once: true })

				instance.UI.setPrintQuality(2)
				instance.UI.setTheme(document.documentElement.dataset['scheme'] === 'dark' ? instance.UI.Theme.DARK : instance.UI.Theme.LIGHT)
				instance.UI.enableDesktopOnlyMode()
				instance.UI.setToolMode(Tools.ToolNames.PAN)

				this.webViewerInstance = instance
				this.documentViewer = documentViewer
				this.tools = Tools
				this.currentTool = Tools.ToolNames.PAN

				this.initTools(Tools)
				this.initInternalEventHandling(target)
			})
		} else
			this.viewer.requestUpdate()

		this.webViewerInstance!.UI.loadDocument(url, { extension: this.viewer.fileExtension, filename: this.viewer.filename })
	}

	/**
	 * Removes the apryse viewer instance and there iframe out of the dom
	 */
	public async unloadWebViewer() {
		if (!this.webViewerInstance)
			return

		this.mutationObserver?.disconnect()
		await this.webViewerInstance.UI.dispose()
		this.viewer._viewerWrapper.querySelector('iframe')?.remove()
		this.webViewerInstance = undefined
		this.documentViewer = undefined
		this.tools = undefined
	}

	/**
	 * Is an instance of apryse currently initiated?
	 */
	public isActive(): boolean {
		return this.webViewerInstance != null
	}

	/**
	 * Returns the current document incl Annotations
	 */
	public async getDocument(includeAnnotations: boolean) {
		if (!this.documentViewer)
			return null

		const document = this.documentViewer.getDocument()
		const options: any = {}
		if (includeAnnotations) {
			// sign annotation? ask to print

			options.xfdfString = await this.webViewerInstance?.Core.annotationManager?.exportAnnotations()
		}
		const data = await document.getFileData(options)
		return new Blob([ new Uint8Array(data) ], { type: 'application/pdf' })
	}

	public downloadDocument(withAnnotations: boolean) {
		if (!this.webViewerInstance)
			return

		const options = {
			includeAnnotations: withAnnotations,
			flatten: false, // Set to “true” if annotations need to be printed as images only
		}

		return this.webViewerInstance.UI.downloadPdf(options)
	}

	public printDocument(withAnnotations: boolean) {
		if (!this.webViewerInstance)
			return

		return this.webViewerInstance.UI.printInBackground({ includeAnnotations: withAnnotations })
	}

	//#endregion

	//#region private methods

	private handleZoom(factor: number) {
		if (!this.webViewerInstance)
			return

		let currentZoom = this.zoom
		if (currentZoom < 0)
			currentZoom = this.webViewerInstance.UI.getZoomLevel()
		this.zoom = Math.round((currentZoom * factor) * 100) / 100
		this.webViewerInstance.UI.setZoomLevel(this.zoom)
		this.viewer.requestUpdate()
	}

	private handleZoomClick(zoomLevel: number) {
		if (!this.webViewerInstance || !this.documentViewer)
			return

		if (zoomLevel < 0) {
			this.zoom = -1
			this.documentViewer.setFitMode(this.documentViewer.FitMode.FitPage)
			this.viewer.requestUpdate()
			return
		}

		this.zoom = zoomLevel / 100
		this.webViewerInstance.UI.setZoomLevel(this.zoom)
		this.viewer.requestUpdate()
	}

	private handleToolButtonClick(tool?: Core.Tools.ToolNames) {
		if (!this.documentViewer || !tool)
			return

		this.currentTool = tool
		this.documentViewer.setToolMode(this.documentViewer.getTool(tool))
		this.viewer.requestUpdate()
	}

	private async handleUndoButtonClick() {
		if (!this.documentViewer)
			return

		const historyManager = this.documentViewer.getAnnotationHistoryManager()
		if (!historyManager.canUndo())
			return

		await historyManager.undo()
		this.viewer.requestUpdate()
	}

	private async handleRedoButtonClick() {
		if (!this.documentViewer)
			return

		const historyManager = this.documentViewer.getAnnotationHistoryManager()
		if (!historyManager.canRedo())
			return

		await historyManager.redo()
		this.viewer.requestUpdate()
	}

	private handleAnnotationChange(tool: ToolType) {
		this.currentAnnotation = tool
		this.handleDropdownToolChange(tool.tool, 'annotationDropdown')
	}

	private handleShapeChange(tool: ToolType) {
		this.currentShape = tool
		this.handleDropdownToolChange(tool.tool, 'shapeDropdown')
	}

	private handleDropdownToolChange(tool: Core.Tools.ToolNames, dropdownName: string) {
		this.handleToolButtonClick(tool)

		const dropdown = this.viewer._viewerWrapper.querySelector<Dropdown>(`[name="${dropdownName}"]`)
		if (dropdown)
			dropdown.open = false
	}

	private async handleSaveButtonClick() {
		if (!this.documentViewer || !this.webViewerInstance)
			return

		const annotationManager = this.documentViewer.getAnnotationManager()
		const annotationList = annotationManager.getAnnotationsList()

		// confirm redactions if there are some of them
		if (annotationList.some(annotation => annotation instanceof this.webViewerInstance!.Core.Annotations.RedactionAnnotation)) {
			if (!await DialogFactory.showConfirmationDialog(this.viewer.localize('redactionConfirmHeading'), this.viewer.localize('redactionConfirmText')))
				return
			await this.documentViewer?.getAnnotationManager().applyRedactions()
		}

		// confirm signatures if there are some of them
		const signatures = annotationList.filter(annotation => annotation.isFullSignature() || annotation.isInitials())
		if (signatures.length > 0) {
			if (!await DialogFactory.showConfirmationDialog(this.viewer.localize('signatureConfirmHeading'), this.viewer.localize('signatureConfirmText')))
				return

			await this.flattenSignatures(signatures)
			//signatures.forEach(signature => (signature as Core.Annotations.StampAnnotation).flatten())
		}

		await this.viewer.uploadDocument()
		this.viewer.dispatchEvent(new CustomEvent('viewer-save:click'))
	}

	private handlePanelLeftButtonClick() {
		if (!this.webViewerInstance)
			return

		this.webViewerInstance.UI.closeElements(LEFT_PANEL_ELEMENTS)
		this.leftPanelVisible = false

		// signature is a special panel -> without it no signatures can be made
		if (this.currentTool == this.tools?.ToolNames.SIGNATURE) {
			this.handleToolButtonClick(this.tools?.ToolNames.PAN)
			return
		}

		this.viewer.requestUpdate()
	}

	private handlePanelRightButtonClick() {
		if (!this.webViewerInstance)
			return

		this.webViewerInstance.UI.closeElements(RIGHT_PANEL_ELEMENTS)
		this.rightPanelVisible = false
		this.viewer.requestUpdate()
	}

	private initTools(tools: Tools) {
		this.annotations = [
			{ tool: tools.ToolNames.STICKY, icon: 'note', title: this.viewer.localize('sticky') },
			{ tool: tools.ToolNames.HIGHLIGHT, icon: 'highlighter', title: this.viewer.localize('highlighter') },
			{ tool: tools.ToolNames.UNDERLINE, icon: 'underline', title: this.viewer.localize('underline') },
			{ tool: tools.ToolNames.FREETEXT, icon: 'text', title: this.viewer.localize('freetext') },
			// TODO: Uncomment when license will support this feature
			// { tool: tools.ToolNames.REDACTION, icon: "subtitles-slash", title: this.viewer.localize("redaction") },
		]
		this.currentAnnotation = this.annotations[0]

		this.shapes = [
			{ tool: tools.ToolNames.RECTANGLE, icon: 'rectangle', title: this.viewer.localize('rectangle') },
			{ tool: tools.ToolNames.ELLIPSE, icon: 'circle', title: this.viewer.localize('circle') },
			{ tool: tools.ToolNames.LINE, icon: 'slash-forward', title: this.viewer.localize('line') },
			{ tool: tools.ToolNames.ARROW, icon: 'arrow-up-right', title: this.viewer.localize('arrow') },
		]
		this.currentShape = this.shapes[0]
	}


	/*
	 * Observes apryse iframe whether the left or right panel opens  
	 */
	private initInternalEventHandling(wrapperElement: HTMLElement) {
		const iframeDocument = wrapperElement.querySelector<HTMLIFrameElement>('iframe')?.contentDocument
		if (!iframeDocument)
			return

		const contentWrapper = iframeDocument.querySelector('.content')
		if (!contentWrapper)
			return

		// mutation observer for left and right panel handling
		this.mutationObserver = new MutationObserver(mutationList => {

			let renderUI = false
			for (const mutation of mutationList) {
				if (mutation.type !== 'childList')
					continue

				const nodes = Object.values(mutation.addedNodes)

				if (nodes.some(addedNode => LEFT_PANEL_ELEMENTS.includes((addedNode as HTMLElement).dataset['element'] ?? ''))) {
					this.leftPanelVisible = true
					renderUI = true
				}

				if (nodes.some(addedNode => RIGHT_PANEL_ELEMENTS.includes((addedNode as HTMLElement).dataset['element'] ?? ''))) {
					this.rightPanelVisible = true
					renderUI = true
				}
			}

			if (renderUI)
				this.viewer.requestUpdate()
		})
		this.mutationObserver.observe(contentWrapper, { childList: true })

		// scroll event handling
		let scrollTimeout: NodeJS.Timeout
		contentWrapper.querySelector('[data-element="documentContainer"]')?.addEventListener('wheel', (event: Event) => {
			if (!(event as WheelEvent).ctrlKey || !this.documentViewer)
				return

			clearTimeout(scrollTimeout)
			scrollTimeout = setTimeout(() => {
				this.zoom = this.documentViewer!.getZoomLevel()
				this.viewer.requestUpdate()
			}, 100)
		}, { passive: true })
	}

	private persistSignatures() {
		setTimeout(async () => {
			const iframeDocument = this.viewer._viewerWrapper.querySelector<HTMLIFrameElement>('iframe')?.contentDocument
			if (!iframeDocument)
				return

			const signatureListElement = iframeDocument.querySelector('.signature-list')
			if (!signatureListElement)
				return

			const signatureImages = signatureListElement.querySelectorAll<HTMLImageElement>('.signature-row img')
			const base64Files = Object.values(signatureImages).map(image => image.getAttribute('src'))

			if (base64Files.length > 0)
				await CipherTool.storeMessageWithEncryption(SIGNATURE_STORAGE_KEY, JSON.stringify(base64Files), SIGNATURE_ENCRYPTION_KEY)
			else
				localStorage.removeItem(SIGNATURE_STORAGE_KEY)
		}, 100)
	}

	private async flattenSignatures(signatures: Core.Annotations.Annotation[]) {
		if (!this.documentViewer)
			return

		const { PDFNet } = this.webViewerInstance!.Core
		const doc = await this.documentViewer.getDocument().getPDFDoc();

		// export annotations from the document
		const annotationManager = this.documentViewer.getAnnotationManager()
		const exportedAnnotations = await annotationManager.exportAnnotations({ annotationList: signatures });

		// Run PDFNet methods with memory management
		await PDFNet.runWithCleanup(async () => {

			// lock the document before a write operation
			// runWithCleanup will auto unlock when complete
			await doc.lock();

			// import annotations to PDFNet
			const fdf_doc = await PDFNet.FDFDoc.createFromXFDF(exportedAnnotations);
			await doc.fdfUpdate(fdf_doc);

			// burn-in all signatures
			await doc.flattenAnnotations()

			// clear the original annotations
			annotationManager.deleteAnnotations(signatures);

			// import annotations from PDFNet
			const fdfDoc = await doc.fdfExtract(PDFNet.PDFDoc.ExtractFlag.e_both);
			const xfdf = await fdfDoc.saveAsXFDFAsString();
			await annotationManager.importAnnotations(xfdf);
		}, LICENSE_KEY);

		// clear the cache (rendered) data with the newly updated document
		this.documentViewer.refreshAll();

		// Update viewer to render with the new document
		this.documentViewer.updateView();

		// Refresh searchable and selectable text data with the new document
		this.documentViewer.getDocument().refreshTextData();
	}

	//#endregion
}