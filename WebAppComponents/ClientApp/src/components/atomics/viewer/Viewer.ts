import { css, html, LitElement, PropertyValues } from 'lit'
import { customElement, property, state } from 'lit/decorators.js'
import * as styles from '@/shared/component-styles.ts'
import { query } from 'lit/decorators/query.js'
import { renderIcon } from '@/shared/component-html.ts'
import { StringLocalizer } from '@/shared/string-localizer.ts'
import { DeepzoomImageInfo, FileMetaData, PinSourceInfo } from '@/shared/types.ts'
import { fontAwesome } from '@/shared/font-awesome.ts'
import { BlueprintViewer, PinMoveEvent } from '@/components/atomics/blueprint-viewer/BlueprintViewer.ts'
import { PinSource } from '@/components/atomics/pin-source/PinSource.ts'
import CommunicationServiceProvider, { CommunicationResponseType } from '@/services/communication-service'
import { Toaster } from '@/components/toaster/Toaster.ts'
import { DropdownMenuItem, MessageType } from '@/components'
import { Pin, PinPositionType } from '@/components/atomics/pin/Pin.ts'
import { Operator } from '@/enums/operator.ts'
import { DefaultViewer } from '@/components/atomics/viewer/DefaultViewer.ts'
import { MicrosoftViewer } from '@/components/atomics/viewer/MicrosoftViewer.ts'
import { ZipViewer } from '@/components/atomics/viewer/ZipViewer.ts'
import { NotifyController } from '@/controllers/notify-controller.ts'
import { MailViewer } from '@/components/viewers/mail-viewer/MailViewer.ts'
import { ButtonType } from '@/components/atomics/button/Button.ts'
import { ColorState } from '@/enums/color-state'

export type ViewerType = {
	fileExtension?: string
	dataSourceId?: string
	elementId?: string
	fileId?: string
	filename?: string
	annotationCreate?: (values: Record<string, any>) => Promise<boolean>
	user?: string
	readonly: boolean
}

enum ViewerButtonFormat {
	Big = 'big',
	Small = 'small',
	Hidden = 'hidden',
}

const enum ViewerRenderType {
	Default = 'default',
	Mail = 'mail',
	Office = 'office',
	Blueprint = 'blueprint',
	Archive = 'archive'
}

type ViewerButtonType = {
	name: string,
	icon: string,
	onClick?: string,
	dataAction?: string,
	type?: ButtonType,
	color?: ColorState,
	format?: ViewerButtonFormat,
	width?: number,
	disabled?: boolean
}

@customElement('lvl-viewer')
export class Viewer extends LitElement implements ViewerType {

	static styles = [
		styles.base,
		styles.color,
		styles.vanishingScrollbar,
		styles.animation,
		fontAwesome,
		DefaultViewer.styles,
		MicrosoftViewer.styles,
		ZipViewer.styles,
		css`
			:host {
				display: block;
				width: 100%;
				height: 100%;
			}

			.viewer-wrapper {
				position: relative;
				height: 100%;
				min-height: 5rem;
				width: 100%;
			}

			/* Loading icon */

			.loading::before,
			.loading::after {
				content: "\\f3f4";

				position: absolute;
				left: 50%;
				top: 50%;
				translate: -50% -50%;
				pointer-events: none;

				font-family: "Font Awesome 6 Duotone", system-ui;
				font-weight: 900;
				font-size: 4rem;

				color: var(--cp-clr-state-active);

				animation-name: fa-spin;
				animation-duration: var(--fa-animation-duration, 2s);
				animation-iteration-count: var(--fa-animation-iteration-count, infinite);
				animation-timing-function: var(--fa-animation-timing, linear);
			}

			.loading::after {
				content: "\\f3f4\\f3f4";
				opacity: var(--fa-secondary-opacity, .4);
			}

			.loading > * {
				visibility: hidden;
				color: var(--fa-secondary-color, inherit);
			}

			.mail-viewer {
				width: 100%;
				height: 100%;
				display: flex;
				flex-direction: column;
				background-color: var(--cp-clr-background-lvl-0);
			}

			.mail-header {
				width: 100%;
				display: grid;
				grid-template-columns: auto 1fr 1fr;
				padding: var(--size-spacing-l);
				border-bottom: 1px solid var(--cp-clr-border-medium);
				gap: var(--size-spacing-l);

				& > div {
					display: flex;
				}

				& .header-left {
					& .mail-initials {
						width: 3.6rem;
						height: 3.6rem;
						border-radius: 50%;
						background: var(--clr-blue-100);
						color: var(--cp-clr-state-active);
						align-content: center;
						text-align: center;
						font-size: var(--size-text-l);
					}
				}

				& .header-main {
					display: flex;
					flex-direction: column;

					& .subject-wrapper, .sender-wrapper {
						margin-bottom: var(--size-spacing-m);
						font-size: var(--size-text-l);
					}

					& .sub-header {
						display: flex;
						flex-direction: column;
						gap: var(--size-spacing-s);
					}

					& .recipient-wrapper, .cc-wrapper, .bcc-wrapper {
						font-size: var(--size-text-m);
						line-height: var(--size-text-l);
					}
				}

				& .header-right {
					overflow: auto;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					align-items: flex-end;

					& > div {
						display: flex;
					}

					& .mail-buttons {
						flex-direction: row-reverse;
						gap: var(--size-spacing-s);
					}
				}
			}

			.mail-body {
				padding: var(--size-spacing-l) var(--size-spacing-l) 0.1rem var(--size-spacing-l);
				overflow-y: auto;
				overflow-x: auto;

				& p.MsoNormal > span {
					display: inline-block;
				}
			}

			span.bold {
				font-weight: 600;
			}

			.no-entries {
				width: 100%;
				height: 100%;
				inset: 0;
				display: grid;
				justify-items: center;
				align-content: center;
				text-align: center;
				color: var(--cp-clr-text-secondary);

				& * {
					background: transparent;
					padding: var(--size-spacing-m);
				}

				& .no-entries__title {
					font-size: var(--size-text-l);
				}

				& .no-entries__icon {
					font-size: 3.6rem;
				}
			}
		`,
	]

	//#region attributes

	@property({ attribute: 'file-id', reflect: true })
	fileId: string = ''

	@property({ attribute: 'file-extension', reflect: true })
	fileExtension: string = ''

	@property()
	filename?: string

	@property({ attribute: 'data-source-id', reflect: true })
	dataSourceId: string = ''

	@property()
	elementId?: string

	@property({ attribute: 'show-placeholder' })
	showPlaceholder: boolean = true

	@property({ reflect: true })
	loading: boolean = false

	@property({ attribute: false })
	deepzoomImageInfo?: DeepzoomImageInfo

	@property({ attribute: false })
	pinSource?: PinSourceInfo

	@property({ type: Boolean, reflect: true, attribute: 'allow-file-options' })
	allowFileOptions: boolean = false

	@property({ type: Boolean, reflect: true })
	readonly: boolean = false

	@property()
	user?: string

	@state()
	fullscreenMode: boolean = false
	
	@state()
	uploading: boolean = false

	//#endregion

	//#region private properties

	@query('div.viewer-wrapper')
	public _viewerWrapper!: HTMLElement

	@query('div.viewer-wrapper > iframe')
	private _iframe?: HTMLIFrameElement

	@query('div.header-right')
	private _mailViewerButtonWrapper?: HTMLElement

	annotationCreate?: (values: Record<string, any>) => Promise<boolean>

	//#region states
	@state()
	public hasChanges: boolean = false

	private mailViewerLoading: boolean = false
	
	
	private resizeObserver?: ResizeObserver

	private pdfViewer: DefaultViewer = new DefaultViewer(this)

	private microsoftViewer: MicrosoftViewer = new MicrosoftViewer(this)

	private zipViewer: ZipViewer = new ZipViewer(this)

	private blueprintViewer?: BlueprintViewer

	@query('lvl-mail-viewer')
	private mailViewer?: MailViewer

	public abortController: AbortController = new AbortController()
	
	private fileChangeController: AbortController = new AbortController()

	private static readonly _baseLocalizer: StringLocalizer = new StringLocalizer('Viewer')

	private notifyController = new NotifyController(this)
	private buttons: ViewerButtonType[] = [
		{
			name: 'download',
			icon: 'arrow-down-to-line',
			onClick: 'downloadButtonClick',
			type: ButtonType.Primary,
			disabled: true,
		},
		{
			name: 'saveAttachments',
			icon: 'file-arrow-down',
			onClick: 'downloadAttachmentsButtonClick',
			type: ButtonType.Tertiary,
			disabled: true,
		},
		{
			name: 'forwardMail',
			icon: 'share-from-square',
			onClick: 'forwardButtonClick',
			type: ButtonType.Tertiary,
			disabled: true,

		},
		{
			name: 'open',
			icon: 'arrow-up-right-from-square',
			onClick: 'openButtonClick',
			disabled: true,
		},
		{
			name: 'print',
			icon: 'arrow-down-to-line',
			onClick: 'printButtonClick',
			disabled: true,
		},
	]

	private get viewerType(): ViewerRenderType | null {
		if (this.fileExtension == 'eml' || this.fileExtension == 'msg')
			return ViewerRenderType.Mail

		if (this.fileExtension == 'zip' || this.fileExtension == 'rar' || this.fileExtension == '7z' || 
			this.fileExtension == 'tar' || this.fileExtension == 'gz' || this.fileExtension == 'bz2' || this.fileExtension == 'xz')
			return ViewerRenderType.Archive

		if (this.microsoftViewer.isOfficeFile())
			return ViewerRenderType.Office

		if (this.fileExtension == 'pdf' && this.deepzoomImageInfo)
			return ViewerRenderType.Blueprint

		return this.fileExtension != '' ? ViewerRenderType.Default : null
	}

	public localize(key: string, ...replacements: any[]) {
		return Viewer._baseLocalizer.localize(key, ...replacements)
	}

	public renderActionButtons() {
		// Return action buttons that can be used by MicrosoftViewer
		return html`
			<lvl-menu-item icon-left="download" @click="${(e: MouseEvent) => this.handleActionButtonClick(e.target as DropdownMenuItem, 'download')}">
				${this.localize('download')}
			</lvl-menu-item>
			<lvl-menu-item icon-left="share" @click="${(e: MouseEvent) => this.handleActionButtonClick(e.target as DropdownMenuItem, 'share')}">
				${this.localize('shareDocument')}
			</lvl-menu-item>
		`
	}

	//#endregion

	// Called when the web component is rendered. Returns the HTML appearance for our web component.
	render() {
		return html`
			<div class="viewer-wrapper ${this.loading ? 'loading' : ''}">
				${this.renderViewerContent()}
				${this.showPlaceholder ? this.renderPlaceholder() : ''}
			</div>
		`
	}
	
	private renderPlaceholder() {
		return html`
			<div id="noEntriesPlaceholder" class="no-entries">
				${renderIcon('file-magnifying-glass', { additionalClasses: [ 'no-entries__icon' ] })}
				<span class="no-entries__title">${this.localize('noEntries')}</span>
			</div>
		`
	}

	private renderViewerContent() {
		switch (this.viewerType) {
			case ViewerRenderType.Mail:
				return this.renderMailViewer()
			case ViewerRenderType.Office:
				return this.microsoftViewer.render()
			case ViewerRenderType.Archive:
				return this.zipViewer.render()
			case ViewerRenderType.Default:
				return this.pdfViewer.render()
		}
		return ''
	}

	//#region lifecycle callbacks

	connectedCallback() {
		super.connectedCallback()

		// detect iframe click and trigger click on window (to close things like popups correctly)
		window.addEventListener('blur', (event) => {
			setTimeout(() => {
				if (this._iframe && document.activeElement == this)
					window.dispatchEvent(new Event('click', event))
			})
		}, { signal: this.abortController.signal })
	}

	protected async firstUpdated(_changedProperties: PropertyValues) {
		super.firstUpdated(_changedProperties)
		await this.loadDocument()
		this._viewerWrapper.addEventListener('fullscreenchange', () => {
			this.fullscreenMode = document.fullscreenElement != null
		})
		this.setAttribute('initdone', '')
	}

	protected async willUpdate(_changedProperties: PropertyValues) {
		super.willUpdate(_changedProperties)
		if (_changedProperties.has('allowFileOptions')) {
			let buttons = this.buttons.filter(button => button.name != 'delete' && button.name != 'update')
			if (this.allowFileOptions) {
				buttons.push(
					{
						name: 'updateButton',
						icon: 'arrows-repeat',
						dataAction: 'viewer-update',
						color: ColorState.Warning,
					},
					{
						name: 'deleteButton',
						icon: 'trash',
						dataAction: 'viewer-delete',
						color: ColorState.Error,
					},
				)
			}
			this.buttons = buttons
		}
	}

	protected updated(_changedProperties: PropertyValues) {
		super.updated(_changedProperties)

		if (_changedProperties.has('fileExtension') && (this.fileExtension == 'eml' || this.fileExtension == 'msg'))
			this.mailViewerLoading = true
	}

	public disconnectedCallback() {
		this.pdfViewer.unloadWebViewer()
		this.microsoftViewer.unloadOfficeDocument()
		this.resizeObserver?.disconnect()
		this.abortController.abort('disconnected')
	}

	//#endregion

	//#region public methods

	public async loadNewDocument(dataSourceId: string, fileId: string, fileExtension: string) {
		this.dataSourceId = dataSourceId
		this.fileId = fileId
		this.fileExtension = fileExtension?.toLowerCase()
		this.fileChangeController.abort()
		this.fileChangeController = new AbortController()

		await this.loadDocument();
	}

	public async loadDocument() {
		if (!this.dataSourceId || !this.fileId || !this.fileExtension)
			return

		this.loading = true
		this.showPlaceholder = false
		await this.updateComplete
		let url = `/Api/DataSources/${this.dataSourceId}/Files/${this.fileId}`

		// remove old blueprint & pdf viewer instance
		this.blueprintViewer?.remove()
		if (this.viewerType != ViewerRenderType.Default)
			await this.pdfViewer.unloadWebViewer()
		switch (this.viewerType) {
			case ViewerRenderType.Mail:
				if(this.mailViewer != null) {
					this.mailViewer.url = url
					this.mailViewer.allowFileOptions = this.allowFileOptions
				}
				break
			case ViewerRenderType.Blueprint:
				await this.loadBlueprintViewer(url)
				break
			case ViewerRenderType.Office:
				await this.microsoftViewer.loadOfficeDocument()
				break
			case ViewerRenderType.Archive:
				await this.loadArchiveViewer()
				break
			default:
				await this.pdfViewer.loadWebViewer(url)
		}

		requestAnimationFrame(async () => {
			if (this._mailViewerButtonWrapper && this.resizeObserver)
				this.resizeObserver.observe(this._mailViewerButtonWrapper)

			if (this.mailViewerLoading) {
				this.mailViewerLoading = false
				await this.updateComplete
				this.calculateButtonsSize()
			}
		})
		this.loading = false
	}

	public toggleFullscreen() {
		if (this.fullscreenMode)
			document.exitFullscreen()
		else
			this._viewerWrapper.requestFullscreen({ navigationUI: 'hide' })
	}

	public async uploadDocument() {
		if (!this.elementId)
			return

		this.uploading = true
		const newDocument = await this.getDocument()
		if (!newDocument) {
			this.uploading = false
			return
		}

		const formData = new FormData()
		const name = this.filename ?? `myDocument.${this.fileExtension}`
		formData.append('file', newDocument, name)

		// upload new document
		const uploadResult = await CommunicationServiceProvider.post<FileMetaData>(`/Api/DataSources/${this.dataSourceId}/Files/`, null, { body: formData })
		if (uploadResult.state != CommunicationResponseType.Ok || uploadResult.data?.fileId == null) {
			this.notifyController.writeMessage({
				type: MessageType.Error,
				heading: this.localize('file.upload.error.heading'),
				content: uploadResult.state == CommunicationResponseType.Error ? uploadResult.error.errorMessage : '',
			})
			this.uploading = false
			return
		}

		// use fileUploadId from previous response and attach it to the current record
		const patchResult = await CommunicationServiceProvider.patch<FileMetaData>(`/Api/DataSources/${this.dataSourceId}/Elements/${this.elementId}`, null, { searchParams: { fileUploadId: uploadResult.data!.fileId } })
		if (patchResult.state != CommunicationResponseType.Ok) {
			this.notifyController.writeMessage({
				type: MessageType.Error,
				heading: this.localize('file.upload.error.heading'),
				content: patchResult.state == CommunicationResponseType.Error ? patchResult.error.errorMessage : '',
			})
			this.uploading = false
			return
		}

		this.fileId = uploadResult.data?.fileId!
		this.notifyController.writeSimpleMessage({ type: MessageType.Success, heading: this.localize('file.save.success.heading') })
		this.uploading = false
		this.hasChanges = false
	}

	//#endregion

	//#region private methods

	private renderMailViewer() {
		return html`<lvl-mail-viewer url="" allow-file-options="${this.allowFileOptions}"></lvl-mail-viewer>`
	}

	private getDocument(): Promise<Blob | null> | null {
		if (this.viewerType === ViewerRenderType.Default)
			return this.pdfViewer.getDocument(true)
		return null
	}

	private handleActionButtonClick(menuItem?: DropdownMenuItem, dataAction?: string) {
		if (!menuItem || menuItem.disabled || !dataAction)
			return
		this.dispatchEvent(new CustomEvent('action-button:click', { detail: dataAction }))
	}

	private async loadBlueprintViewer(url: string) {
		if (!this.deepzoomImageInfo)
			return

		let target = this._viewerWrapper
		this.blueprintViewer = document.createElement('lvl-blueprint')
		this.blueprintViewer.url = `${url}/DeepZoom/SubFiles/`
		this.blueprintViewer.allowNewPin = true
		this.blueprintViewer.autoMerge = true
		this.blueprintViewer.loading = this.deepzoomImageInfo.state === 'Caching'
		this.blueprintViewer.loadingError = this.deepzoomImageInfo.state === 'Failed'
		if (!this.blueprintViewer.loading && !this.blueprintViewer.loadingError) {
			this.blueprintViewer.imageWidth = this.deepzoomImageInfo.width
			this.blueprintViewer.imageHeight = this.deepzoomImageInfo.height
			this.blueprintViewer.tileSize = this.deepzoomImageInfo.tileSize ?? 256
		} else if (this.blueprintViewer.loading)
			this.awaitBlueprintRendering(url)
		if (this.deepzoomImageInfo.fileFormat)
			this.blueprintViewer.format = this.deepzoomImageInfo.fileFormat
		if (this.pinSource?.id) {
			this.blueprintViewer.allowDiscard = this.pinSource.allowDiscard === true
			this.blueprintViewer.dataUrl = `/Api/DataSources/${this.pinSource.id}/Annotations`
			this.blueprintViewer.dataUrlOptions = {
				searchParams: {
					filters: [
						{
							filterColumn: this.pinSource.keyFieldName,
							operator: Operator.Equals,
							compareValue: window.Page.getFormData().Id,
						},
					],
				},
			}
			this.blueprintViewer.detailPageSlug = this.pinSource.detailPageSlug
		}
		if (this.pinSource?.types) {
			this.pinSource.types.map(pinSourceInfo => {
				let pinSource = new PinSource()
				pinSource.id = pinSourceInfo.id
				pinSource.label = pinSourceInfo.label
				pinSource.icon = pinSourceInfo.icon
				pinSource.hidden = pinSourceInfo.hidden == true
				this.blueprintViewer?.pinSources.push(pinSource)
			})
		}

		// handle Pin creation
		this.blueprintViewer.annotationCreate = async (position: PinPositionType) => {
			if (!position || typeof this.annotationCreate !== 'function' || !this.pinSource)
				return false

			const createValues = {
				[this.pinSource.keyFieldName]: window.Page.getFormData().Id,
				AnnotationX: Math.round(position.x),
				AnnotationY: Math.round(position.y),
			}
			return this.annotationCreate(createValues)
		}

		// handle Pin move
		this.blueprintViewer.addEventListener('pin:move:end', async (event: Event & CustomEventInit<PinMoveEvent>) => {
			if (!this.pinSource || !event.detail || !event.detail.pin || !event.detail.targetPosition)
				return event.preventDefault()

			let oldPosition = event.detail.pin.position
			const response = await CommunicationServiceProvider.patch(`/Api/DataSources/${this.pinSource.id}/Elements/${event.detail.pin.id}`, null, {
				body: {
					'AnnotationX': Math.round(event.detail.targetPosition.x),
					'AnnotationY': Math.round(event.detail.targetPosition.y),
				},
				headers: { 'Content-Type': 'application/json' },
			})

			// revert position 
			if (response.state != CommunicationResponseType.Ok) {
				event.detail.pin.position = oldPosition

				const toaster = document.querySelector<Toaster>('#toaster')
				toaster?.notifySimple({ heading: this.localize('annotationMoveFailed'), type: MessageType.Error })
				return
			}

			// dispatch global datasource update event
			this.dispatchEvent(new CustomEvent<string>('dataSource:elementsChanged', { detail: this.pinSource.id, bubbles: true }))
		})

		// handle pin create
		this.blueprintViewer.addEventListener('pin:created', async (event: Event & CustomEventInit<Pin>) => {
			if (!event.detail)
				return
			
			// dispatch global datasource update event
			this.dispatchEvent(new CustomEvent<string>('dataSource:elementsChanged', { detail: this.pinSource?.id, bubbles: true }))
		})
		
		// handle pin update
		this.blueprintViewer.addEventListener('pin:updated', async (event: Event & CustomEventInit<Pin>) => {
			if (!event.detail)
				return
			
			// dispatch global datasource update event
			this.dispatchEvent(new CustomEvent<string>('dataSource:elementsChanged', { detail: this.pinSource?.id, bubbles: true }))
		})

		// handle pin delete
		this.blueprintViewer.addEventListener('pin:delete', async (event: Event & CustomEventInit<Pin>) => {
			if (!event.detail)
				return

			// disable while deleting
			event.detail.disabled = true

			const response = await CommunicationServiceProvider.patch(`/Api/DataSources/${this.pinSource?.id}/Elements/${event.detail.id}/Action`, null, {
				body: {
					type: 'inactive',
					value: true,
				},
				headers: { 'Content-Type': 'application/json' },
			})

			if (response.state == CommunicationResponseType.Ok) {
				this.blueprintViewer?.removePin(event.detail)

				// dispatch global datasource update event
				this.dispatchEvent(new CustomEvent<string>('dataSource:elementsChanged', { detail: this.pinSource?.id, bubbles: true }))
				
			} else {
				const toaster = document.querySelector<Toaster>('#toaster')
				toaster?.notifySimple({ heading: this.localize('annotationDeleteFailed'), type: MessageType.Error })
				event.detail.disabled = false
			}
		})

		target.append(this.blueprintViewer)
		this.loading = false
	}

	private calculateButtonsSize() {
		if (!this._mailViewerButtonWrapper || !this._mailViewerButtonWrapper.querySelector('.mail-buttons'))
			return
		const elements = Array.from(this._mailViewerButtonWrapper.querySelector('.mail-buttons')!.children)
		elements.forEach((element) => {
			if (element.tagName.toLowerCase() !== 'lvl-button')
				return
			let buttonIndex = this.buttons.findIndex(button => button.name === element.getAttribute('name'))
			if (buttonIndex < 0)
				return
			this.buttons[buttonIndex].width = Math.ceil(element.getClientRects().item(0)?.width ?? 0)
		})
	}

	private async loadArchiveViewer() {
		this.zipViewer.dataSourceId = this.dataSourceId || ''
		this.zipViewer.fileId = this.fileId || ''
		this.zipViewer.filename = this.filename || ''
		await this.zipViewer.loadZipContents()
	}

	private async awaitBlueprintRendering(blueprintUrl: string, iteration: number = 0): Promise<void> {
		if (!this.blueprintViewer)
			return Promise.reject()

		let fetchData = await CommunicationServiceProvider.get<DeepzoomImageInfo>(`${blueprintUrl}/DeepZoomInfo`, { signal: this.fileChangeController.signal })
		if (fetchData.state == CommunicationResponseType.Ok) {
			this.deepzoomImageInfo = fetchData.data!
			this.blueprintViewer.imageWidth = this.deepzoomImageInfo.width
			this.blueprintViewer.imageHeight = this.deepzoomImageInfo.height
			this.blueprintViewer.tileSize = this.deepzoomImageInfo.tileSize ?? 256
			this.blueprintViewer.loading = false
			this.blueprintViewer.loadingError = false

			// update blueprintConfig on page
			const blueprintConfig = window.Page.getBlueprintConfig()
			window.Page.setBlueprintConfig({
				imageInfo: {
					width: this.deepzoomImageInfo.width,
					height: this.deepzoomImageInfo.height,
					tileSize: this.deepzoomImageInfo.tileSize ?? 256,
					fileId: this.deepzoomImageInfo.fileId,
					state: this.deepzoomImageInfo.state
				},
				pinSource: blueprintConfig?.pinSource
			})
			
		} else if (fetchData.state == CommunicationResponseType.Error && fetchData.error.errorCode == 503 && iteration < 10)
			return this.awaitBlueprintRendering(blueprintUrl, iteration + 1)
		else if (fetchData.state != CommunicationResponseType.Aborted)
			this.blueprintViewer.loadingError = true

		return Promise.resolve()
	}

	//#endregion
}

// Define a connection between html element tag and LitElement class
declare global {
	interface HTMLElementTagNameMap {
		'lvl-viewer': Viewer
	}
}