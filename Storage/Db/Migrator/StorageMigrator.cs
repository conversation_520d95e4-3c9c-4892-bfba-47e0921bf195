using FluentMigrator;
using Levelbuild.Domain.Storage.Helper;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Serilog;

namespace Levelbuild.Domain.Storage.Db.Migrator;

public class StorageMigrator
{
	private StorageConnection _connection;
	private readonly ILogger _logger;

	public StorageMigrator(StorageConnection connection, ILogger logger)
	{
		_logger = logger;
		_connection = connection;
	}

	public void Migrate()
	{
		if (_connection.Db.CustomerContext != null)
			_logger.Information($"Starting migration for connection {_connection.Db.CustomerContext.Identifier}...");
		else
			_logger.Information($"Starting migration for connection Main Context...");

		try
		{
			Migrations migrations = new Migrations(_connection, _logger);
			var dbMigrations = GetDatabaseMigrationList();
			var codeMigrations = migrations.MigrationList;

			_logger.Information($"Found {dbMigrations.Count} database migration entries. ");
			_logger.Information($"Found {codeMigrations.Count} code migration entries. ");

			AddMigrationsAndExecute(dbMigrations, codeMigrations);
		}
		catch (Exception e)
		{
			_logger.Error(e, "Problem in self migrations. ");
		}
	}

	private List<StorageMigration> GetDatabaseMigrationList()
	{
		return _connection.WithDbContext(db => db.StorageMigration.ToList());
	}

	private void AddMigrationsAndExecute(List<StorageMigration> dbMigrations, IDictionary<StorageMigration, Action<Migration>> codeMigrations)
	{
		foreach (var codeMigration in codeMigrations)
		{
			var dbMigration = dbMigrations.FirstOrDefault(it => it.MigrationKey == codeMigration.Key.MigrationKey);
			if (dbMigration == null)
			{
				_logger.Information($"Start migration {codeMigration.Key.MigrationKey}... ");
				bool migrated = Execute(codeMigration);
				_logger.Information($"Code migration {codeMigration.Key.MigrationKey} returned status: {migrated}. ");

				_connection.WithDbContext(db =>
				{
					db.StorageMigration.Add(codeMigration.Key);
					return db.SaveChanges();
				});
			}
			else if (!dbMigration.MigrationDone)
			{
				_logger.Information($"Start {codeMigration.Key.MigrationKey} - retry... ");
				bool migrated = Execute(codeMigration);
				_logger.Information($"Code migration {codeMigration.Key.MigrationKey} returned status: {migrated}. ");

				_connection.WithDbContext(db =>
				{
					var migration = db.StorageMigration.First(it => it.MigrationKey == codeMigration.Key.MigrationKey);
					migration.MigrationDone = codeMigration.Key.MigrationDone;
					migration.MigrationDoneTime = codeMigration.Key.MigrationDoneTime;
					return db.SaveChanges();
				});
			}
		}
	}

	/// <summary>
	/// Executes the wrapped migration and sets <see cref="StorageMigration.MigrationDone"/> and <see cref="StorageMigration.MigrationDoneTime"/> accordingly.
	/// </summary>
	/// <returns><c>true</c> if the migration was executed successfully, <c>false</c> otherwise.</returns>
	private bool Execute(KeyValuePair<StorageMigration, Action<Migration>> codeMigration)
	{
		try
		{
			_connection.Db.Migrator.Migrate(codeMigration.Value, null);
			codeMigration.Key.MigrationDone = true;
			codeMigration.Key.MigrationDoneTime = DateTime.UtcNow;
			return true;
		}
		catch (Exception e)
		{
			_logger.Error(e, $"Error in migration '{codeMigration.Key.MigrationKey}' in database '{_connection.Db.ConnectionHelper.GetDatabaseName()}'");
		}

		return false;
	}

	public void MigrateMongoDb()
	{
		if (_connection.Db.CustomerContext == null)
			return;
		_logger.Information($"Starting migration for connection {_connection.Db.CustomerContext.Identifier}...");

		if (!string.IsNullOrWhiteSpace(_connection.Db.CustomerContext.MongoDbConnectionString))
		{
			MongoDbMigrationHelper.MigrateMongoDb(_connection);
		}
	}
}