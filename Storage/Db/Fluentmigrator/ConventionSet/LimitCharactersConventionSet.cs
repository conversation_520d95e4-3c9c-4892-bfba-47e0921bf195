using FluentMigrator.Runner;
using FluentMigrator.Runner.Conventions;

namespace Levelbuild.Domain.Storage.Db.Fluentmigrator.ConventionSet;

/// <inheritdoc />
public class LimitCharactersConventionSet : IConventionSet
{
	/// <inheritdoc />
	public LimitCharactersConventionSet()
		: this(new DefaultConventionSet())
	{
	}

	/// <summary>
	/// Postgres identifier names are limited to 63 characters. 
	/// </summary>
	/// <param name="innerConventionSet"></param>
	public LimitCharactersConventionSet(IConventionSet innerConventionSet)
	{
		ForeignKeyConventions = [..innerConventionSet.ForeignKeyConventions, new ShortenForeignKeyNameConvention()];
		ColumnsConventions = [..innerConventionSet.ColumnsConventions, new ShortenPrimaryKeyNameConvention()];
		ConstraintConventions = [..innerConventionSet.ConstraintConventions, new ShortenConstraintNameConvention()];
		IndexConventions = [..innerConventionSet.IndexConventions, new ShortenIndexNameConvention()];
		SequenceConventions = innerConventionSet.SequenceConventions;
		AutoNameConventions = innerConventionSet.AutoNameConventions;
		SchemaConvention = innerConventionSet.SchemaConvention;
		RootPathConvention = innerConventionSet.RootPathConvention;
	}

	/// <inheritdoc />
	public IRootPathConvention RootPathConvention { get; }

	/// <inheritdoc />
	public DefaultSchemaConvention SchemaConvention { get; }

	/// <inheritdoc />
	public IList<IColumnsConvention> ColumnsConventions { get; }

	/// <inheritdoc />
	public IList<IConstraintConvention> ConstraintConventions { get; }

	/// <inheritdoc />
	public IList<IForeignKeyConvention> ForeignKeyConventions { get; }

	/// <inheritdoc />
	public IList<IIndexConvention> IndexConventions { get; }

	/// <inheritdoc />
	public IList<ISequenceConvention> SequenceConventions { get; }

	/// <inheritdoc />
	public IList<IAutoNameConvention> AutoNameConventions { get; }
}