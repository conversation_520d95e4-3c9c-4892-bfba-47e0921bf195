using System.Collections.Concurrent;
using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;
using Levelbuild.Domain.Storage.Db.Postgres;
using Levelbuild.Domain.StorageEntities;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Levelbuild.Domain.StorageEntities.Features.Enum;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Serilog;

namespace Levelbuild.Domain.Storage.Db;

/// <summary>
/// Factory for <see cref="Db"/>.
/// </summary>
public class DbFactory
{
	private readonly ILogger _logger;
	private readonly FeatureFlags _featureFlags;

	private readonly ConcurrentDictionary<string, PooledDbContextFactory<PostgresStorageDatabaseContext>> _connectionPools = new();

	/// <summary>
	/// Factory constructor
	/// </summary>
	/// <param name="logger"></param>
	/// <param name="featureFlags"></param>
	public DbFactory(ILogger logger, FeatureFlags featureFlags)
	{
		_logger = logger;
		_featureFlags = featureFlags;
	}

	/// <summary>
	/// Returns a Db instance with the connection pool for the database used.
	/// </summary>
	/// <param name="dbConnectionString"></param>
	/// <param name="customerContext"></param>
	/// <param name="dataStoreAuthentication"></param>
	/// <returns></returns>
	public Db GetDb(string dbConnectionString, StorageContextOrm? customerContext, IDataStoreAuthentication? dataStoreAuthentication = null)
	{
		var failedMigrationTables = StorageConnectionFactory.FailedMigrationTables.GetOrAdd(dbConnectionString, new HashSet<string>());
		return new Db(SqlType.Postgres, _featureFlags, dbConnectionString, customerContext, dataStoreAuthentication, new PostgresConnectionHelper(dbConnectionString, _logger),
					  GetDbConnection(dbConnectionString), failedMigrationTables, _logger);
	}

	private PooledDbContextFactory<PostgresStorageDatabaseContext> GetDbConnection(string dbConnectionString)
	{
		return _connectionPools.GetOrAdd(dbConnectionString, connectionString =>
		{
			var options = new DbContextOptionsBuilder<PostgresStorageDatabaseContext>()
				.UseNpgsql(connectionString)
				.Options;

			return new PooledDbContextFactory<PostgresStorageDatabaseContext>(options);
		});
	}
}