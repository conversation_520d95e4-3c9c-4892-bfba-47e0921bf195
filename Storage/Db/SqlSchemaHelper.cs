using System.Data;
using System.Data.Common;
using Levelbuild.Domain.StorageEntities.Features.Dto;

namespace Levelbuild.Domain.Storage.Db;

public abstract class SqlSchemaHelper
{
	protected abstract HashSet<string> GetUniqueCols(DbConnection connection, string storageIndexDefinition);

	protected abstract void AddColsDefault(DbConnection connection, string storageIndexDefinition,
										   List<StorageFieldDefinitionOrm> fields);
}