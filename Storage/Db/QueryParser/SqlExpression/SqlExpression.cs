using Levelbuild.Core.DataStoreInterface.Enum;

namespace Levelbuild.Domain.Storage.Db.QueryParser.SqlExpression;

/// <summary>
/// Specific part of a field expression
/// </summary>
public abstract class SqlExpression
{
	/// <summary>
	/// The database helper
	/// </summary>
	protected readonly Db Db;
	
	/// <summary>
	/// If this expression resolves to a MultiValue
	/// </summary>
	public bool MultiValue;
	
	/// <summary>
	/// The type of which this expression resolves to
	/// </summary>
	public DataStoreFieldType Type;
	
	/// <summary>
	/// Whether this expression resolves to a Nullable object
	/// </summary>
	public bool Nullable;
	
	/// <summary>
	/// The DefaultValue
	/// </summary>
	public object? DefaultValue;
	
	/// <summary>
	/// Whether this expression is squashed to a single value. If MultiValue == true and AggregateFunction is set, they both cancel each other out.
	/// </summary>
	public DataStoreColumnAggregationFunction? AggregateFunction = null;

	/// <summary>
	/// Internal constructor
	/// </summary>
	/// <param name="db"></param>
	protected SqlExpression(Db db)
	{
		Db = db;
	}

	/// <summary>
	/// Retrieves all child ColumnExpressions.
	/// </summary>
	/// <returns></returns>
	protected internal abstract List<ColumnExpression> GetColumnExpressions();
	
	/// <summary>
	/// After this call, based on the attributes of the children or ones own, the attributes MultiValue, Type, Nullable, DefaultValue &amp; AggregateFunction should be set.
	/// </summary>
	protected internal abstract void DetermineType();

	/// <summary>
	/// Provides a database queryable string representation of this object.
	/// </summary>
	/// <returns></returns>
	public abstract string AsString();

	/// <summary>
	/// Use AsString instead
	/// </summary>
	/// <returns></returns>
	/// <exception cref="NotSupportedException"></exception>
	[Obsolete("use AsString instead", true)]
	public override string? ToString()
	{
		return base.ToString();
	}
}