using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;

namespace Levelbuild.Domain.Storage.Db.QueryParser.SqlExpression;

/// <inheritdoc/>
public class IsNullExpression : SqlExpression
{
	/// <summary>
	/// The nullable expression
	/// </summary>
	private SqlExpression First { get; }
	
	/// <summary>
	/// The fallback expression
	/// </summary>
	private SqlExpression Second { get; }
	
	/// <inheritdoc/>
	public IsNullExpression(Db db, SqlExpression first, SqlExpression second): base(db)
	{
		First = first;
		Second = second;
	}

	/// <inheritdoc/>
	protected internal override List<ColumnExpression> GetColumnExpressions()
	{
		return First.GetColumnExpressions();
	}

	/// <inheritdoc/>
	public override string AsString() => Db.DataHelper.IsNullRaw(First.AsString(), Second.AsString());

	/// <inheritdoc/>
	protected internal override void DetermineType()
	{
		if (First.MultiValue != Second.MultiValue)
			throw new DataStoreQueryException($"Coalesce can only be applied if both fields are MultiValue or none: '{this}'");

		var greatestType = GreatestType(First.Type, Second.Type);
		if (greatestType is null)
			throw new DataStoreQueryException($"Unable to Coalesce types {First.Type} and {Second.Type}: '{this}'");
		
		
		MultiValue = First.MultiValue;
		Type = greatestType.Value;
		Nullable = First.Nullable && Second.Nullable;
		DefaultValue = "";
	}

	private static DataStoreFieldType? GreatestType(DataStoreFieldType firstType, DataStoreFieldType secondType)
	{
		if (firstType == secondType)
			return firstType;

		// field types are no longer identical, so there have to be at least two different types in them
		if (firstType is DataStoreFieldType.Integer or DataStoreFieldType.Double or DataStoreFieldType.Long &&
			secondType is DataStoreFieldType.Integer or DataStoreFieldType.Double or DataStoreFieldType.Long)
		{
			if (firstType is DataStoreFieldType.Double || secondType is DataStoreFieldType.Double)
				return DataStoreFieldType.Double;
			return DataStoreFieldType.Long;
		}
		
		if (firstType is DataStoreFieldType.String or DataStoreFieldType.Text && secondType is DataStoreFieldType.String or DataStoreFieldType.Text)
		{
			return DataStoreFieldType.Text;
		}

		if (firstType is DataStoreFieldType.Date or DataStoreFieldType.DateTime && secondType is DataStoreFieldType.Date or DataStoreFieldType.DateTime)
		{
			return DataStoreFieldType.DateTime;
		}
		
		return null;
	}
}