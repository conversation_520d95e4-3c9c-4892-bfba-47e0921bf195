using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Aggregations;
using Elastic.Clients.Elasticsearch.Core.MSearch;
using Elastic.Clients.Elasticsearch.Core.Search;
using Elastic.Clients.Elasticsearch.QueryDsl;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.StorageInterface.Dto.SearchSuggestions;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Levelbuild.Domain.StorageEntities.Features.Enum;
using Serilog;
using SharpCompress;

namespace Levelbuild.Domain.Storage.Db;

public class FilterParserElastic
{
	private readonly Db _db;
	private readonly ILogger _logger;

	public FilterParserElastic(Db db, ILogger logger)
	{
		_db = db;
		_logger = logger;
	}


	public IList<StorageSuggestionElement> ParseQuery(StorageIndexDefinition sid, StorageSuggestionQuery dataStoreQuery)
	{
		if (!sid.FulltextSearch)
			throw new DataStoreQueryException($"Fulltext search is not enabled for DataSource '{sid.Name}'");

		if (_db.CustomerContext == null)
			throw new DataStoreQueryException($"No customer context available to query on '{sid.Name}'");

		if (_db.ElasticClient == null)
			throw new DataStoreQueryException($"Elastic client is not initialized for customer context '{_db.CustomerContext.Identifier}'");

		var fields = sid.Fields.ToDictionary(it => $"{it.Name}");

		var indexName = $"{sid.ElasticName(_db.CustomerContext.ToDto())}";
		var queryParam = dataStoreQuery.FulltextSearchFilter.CompareValue!.ToString()!;

		var filters = new List<Query>();
		if (_db.Authentication != null)
		{
			var groups = _db.Authentication.Groups;
			if (groups != null)
				filters.Add(new TermsQuery
				{
					Field = StorageSystemField.SysGroups.ToString(),
					Terms = new TermsQueryField(groups.Select(value => FieldValue.String(value)).ToArray())
				});
		}

		if (filters.Count == 0)
			filters.Add(new MatchAllQuery());

		var searchableFields = fields.Where(field =>
												(field.Value.Type == DataStoreFieldType.String || field.Value.Type == DataStoreFieldType.Text) &&
												!field.Value.ExcludeFromFulltext).ToDictionary();

		var searches = new List<SearchRequestItem>
		{
			new SearchRequestItem(new MultisearchHeader
			{
				Index = indexName
			}, new MultisearchBody
			{
				MinScore = 0.1,
				Source = new(false),
				Fields = searchableFields
					.Where(f => f.Value.FulltextIndexingType == FulltextIndexingType.Name)
					.Select(it => new FieldAndFormat()
					{
						Field = it.Key
					}).ToList(),
				Query = new BoolQuery
				{
					Should = new List<Query>
					{
						new MultiMatchQuery()
						{
							Query = queryParam,
							Fields = searchableFields
								.Where(f => f.Value.FulltextIndexingType == FulltextIndexingType.Name)
								.Select(f => f.Key).ToArray(),
							Type = TextQueryType.BoolPrefix
						}
					},
					Filter = filters
				}
			}),
			new SearchRequestItem(new MultisearchHeader
			{
				Index = indexName
			}, new MultisearchBody
			{
				MinScore = 0.1,
				Source = new(false),
				Size = 5,
				Query = new BoolQuery
				{
					Should = new List<Query>
					{
						new MultiMatchQuery()
						{
							Query = queryParam,
							Fields = searchableFields.Keys.ToArray(),
							Type = TextQueryType.BoolPrefix
						}
					},
					Filter = filters
				},
				Highlight = new Highlight()
				{
					Fields = searchableFields.Keys.ToDictionary(it => new Field(it), it => new HighlightField()
					{
						Type = HighlighterType.Unified
					})
				}
			})
		};

		var queryWords = Regex.Split(queryParam, "[^\\w]");
		var lastQueryWord = queryWords[queryWords.Length - 1];
		foreach (var field in searchableFields
					 .Where(it => it.Value.FulltextIndexingType == FulltextIndexingType.Summary))
		{
			var searchRequestItem = new SearchRequestItem(new MultisearchHeader()
			{
				Index = indexName
			}, new MultisearchBody()
			{
				MinScore = 0.1,
				Size = 0,
				Timeout = "500ms",
				Query = new BoolQuery
				{
					Should = new List<Query>
					{
						new PrefixQuery(field.Key)
						{
							Value = queryParam
						},
					},
					Filter = filters
				},
				Aggregations = new AggregationDictionary()
				{
					{
						"suggestions", new TermsAggregation("")
						{
							Field = field.Key + ".words",
							Include = new TermsInclude($"{lastQueryWord}.*"),
							Size = 10
						}
					}
				}
			});
			searches.Add(searchRequestItem);
		}

		var request = new MultiSearchRequest(indexName)
		{
			Searches = searches
		};

		var response = _db.ElasticClient.MultiSearch<dynamic>(request);
		if (response.ApiCallDetails.RequestBodyInBytes != null)
		{
			var requestJson = Encoding.UTF8.GetString(response.ApiCallDetails.RequestBodyInBytes);
			_logger.Verbose($"Request Json: {requestJson}");
		}

		if (response.ApiCallDetails.ResponseBodyInBytes != null)
		{
			var responseJson = Encoding.UTF8.GetString(response.ApiCallDetails.ResponseBodyInBytes);
			_logger.Verbose($"Response Json: {responseJson}");
		}

		if (!response.IsSuccess())
			throw new DataStoreQueryException($"Elastic multisearch request failed. Response: '{response}'");
		var searchResponses = new List<MultiSearchItem<dynamic>>();
		response.Responses.ForEach(it => it.Match(item => searchResponses.Add(item), error => _logger.Error(error.ToString()!)));
		var result = new List<StorageSuggestionElement>();

		// parse response of first query
		var fieldsQuery = searchResponses[0];
		var fieldSuggestionsDict = new Dictionary<string, HashSet<string>>();

		if (fieldsQuery.Status == 200 && fieldsQuery.Total > 0)
		{
			foreach (var hit in fieldsQuery.Hits)
			{
				foreach (var f in hit.Fields!)
				{
					var value = ((JsonElement)f.Value).EnumerateArray().ToArray()[0].GetString();
					if (value != null && value.Contains(queryParam))
					{
						if (!fieldSuggestionsDict.ContainsKey(f.Key))
							fieldSuggestionsDict[f.Key] = new HashSet<string>();
						fieldSuggestionsDict[f.Key].Add(value);
					}
				}
			}
		}

		foreach (var suggestions in fieldSuggestionsDict)
		{
			foreach (var suggestion in suggestions.Value)
			{
				result.Add(new StorageSuggestionElement(StorageSuggestionElement.SuggestionType.Field, new[] { suggestion }, fields[suggestions.Key].ToDto()));
			}
		}

		// parse response of second query
		var elementsQuery = searchResponses[1];
		if (elementsQuery.Status == 200 && elementsQuery.Total > 0)
		{
			foreach (var hit in elementsQuery.Hits)
			{
				if (hit.Highlight == null)
					continue;
				var words = new List<string>();
				hit.Highlight.ForEach(it => words.AddRange(it.Value));
				result.Add(new StorageSuggestionElement(StorageSuggestionElement.SuggestionType.Document, words.ToArray(), null, hit.Id));
			}
		}

		// parse response of other queries
		HashSet<String> examples = new HashSet<string>();
		for (int i = 2; i < searchResponses.Count; i++)
		{
			var res = searchResponses[i];
			if (res.Status == 200)
			{
				var agg = (StringTermsAggregate)res.Aggregations!["suggestions"];
				agg.Buckets.ForEach(it => examples.Add((string)it.Key.Value!));
			}
		}

		examples.ForEach(ex => result.Add(new StorageSuggestionElement(StorageSuggestionElement.SuggestionType.Word, new[] { ex })));

		return result;
	}
}