using System.Collections.Concurrent;
using System.Diagnostics;
using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;
using Levelbuild.Domain.Storage.Db;
using Levelbuild.Domain.Storage.Helper;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Serilog;

namespace Levelbuild.Domain.Storage;

public class StorageConnectionFactory
{
	private readonly FeatureFlags _featureFlags;
	private readonly DbFactory _dbFactory;
	private readonly ILogger _logger;
	private readonly MigrationHelperFactory _migrationHelperFactory;
	private readonly ActivitySource _activitySource;
	internal static readonly ConcurrentDictionary<string, HashSet<string>> FailedMigrationTables = new();

	public StorageConnectionFactory(FeatureFlags featureFlags, ILogger logger, MigrationHelperFactory migrationHelperFactory, DbFactory dbFactory, ActivitySource activitySource)
	{
		_featureFlags = featureFlags;
		_logger = logger;
		_migrationHelperFactory = migrationHelperFactory;
		_dbFactory = dbFactory;
		_activitySource = activitySource;
	}

	/// <summary>
	/// Connections with customer contexts
	/// </summary>
	/// <param name="customerContext"></param>
	/// <param name="dataStoreAuthentication"></param>
	/// <returns></returns>
	public StorageConnection GetConnection(StorageContextOrm customerContext, IDataStoreAuthentication? dataStoreAuthentication = null)
	{
		var db = _dbFactory.GetDb(customerContext.DatabaseConnectionString, customerContext, dataStoreAuthentication);
		return new StorageConnection(_featureFlags, db, _migrationHelperFactory.CreateMigrationHelper(db), _logger, customerContext.DatabaseConnectionString,
									 this, FailedMigrationTables.GetOrAdd(customerContext.DatabaseConnectionString, new HashSet<string>()), _activitySource);
	}
	
	/// <summary>
	/// Connections to the main db only
	/// </summary>
	/// <param name="connectionString"></param>
	/// <param name="dataStoreAuthentication"></param>
	/// <returns></returns>
	public StorageConnection GetConnection(string connectionString, IDataStoreAuthentication? dataStoreAuthentication = null)
	{
		var db = _dbFactory.GetDb(connectionString, null, dataStoreAuthentication);
		return new StorageConnection(_featureFlags, db, _migrationHelperFactory.CreateMigrationHelper(db), _logger, connectionString, 
									 this, new HashSet<string>(), _activitySource);
	}
}