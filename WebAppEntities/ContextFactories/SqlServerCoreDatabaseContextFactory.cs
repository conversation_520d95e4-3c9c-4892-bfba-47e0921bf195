using Levelbuild.Core.EntityInterface.Constants;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Entities.ContextFactories;

public class SqlServerCoreDatabaseContextFactory : IDbContextFactory<CoreDatabaseContext>
{
	private readonly IServiceProvider _serviceProvider;
	
	public SqlServerCoreDatabaseContextFactory(IServiceProvider serviceProvider)
	{
		_serviceProvider = serviceProvider;
	}
	
	public CoreDatabaseContext CreateDbContext()
	{
		return ActivatorUtilities.CreateInstance<CoreDatabaseContext>(_serviceProvider, DatabaseProvider.SqlServer);
	}
}