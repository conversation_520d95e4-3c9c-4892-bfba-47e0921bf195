using Levelbuild.Core.EntityInterface.Attributes;

namespace Levelbuild.Entities.Interfaces;

/// <summary>
/// Interface to convert an entity to a data transfer object
/// </summary>
/// <typeparam name="T"></typeparam>
public interface IConvertibleEntity<T>
{
	/// <summary>
	/// Method declaration to convert an entity to a data transfer object
	/// </summary>
	/// <param name="excludedProperties">should some properties be excluded from the dto?</param>
	/// <param name="handledObjects">which objects where already handled inside this run? (prevents stackoverflow)</param>
	/// <returns></returns>
	public T? ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null);
}

/// <summary>
/// Interface to convert an entity to a specific object
/// </summary>
/// <typeparam name="TReturn">Type of the returned object</typeparam>
/// <typeparam name="TParam">Type of the passed parameters</typeparam>
public interface IConvertibleEntity<TReturn, TParam>
{
	/// <summary>
	/// Method declaration to convert an entity to a specific object
	/// </summary>
	/// <param name="additionalData">contains additional values which are not part of the entity but should be part of the dto</param>
	/// <param name="excludedProperties">should some properties be excluded from the dto?</param>
	/// <param name="handledObjects">which objects where already handled inside this run? (prevents stackoverflow)</param>
	/// <returns></returns>
	public TReturn? ToExtendedDto(TParam additionalData, string[]? excludedProperties = null, List<string>? handledObjects = null);
	
	/// <summary>
	/// Retrieves all columns of an entity which are transferred to the list frontend
	/// </summary>
	/// <returns></returns>
	public static virtual IList<string> GetHeaderColumnNames()
	{
		return typeof(TReturn).GetProperties()
			.Where(property => Attribute.IsDefined(property, typeof(HeaderValueAttribute)))
			.Select(propertyInfo => propertyInfo.Name)
			.ToList();
	}
}