using Levelbuild.Core.DataStoreInterface;
using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;
using Levelbuild.Core.StorageInterface;
using Levelbuild.Core.StorageInterface.Dto;
using Levelbuild.Entities.Features.DataStoreContext;
using Levelbuild.Entities.Helpers.DataStoreConfig;

namespace Levelbuild.Entities.Helpers.DataStoreContext;

/// <summary>
/// Helper class that provides methods for Storage-specific DataStoreContext actions.
/// </summary>
public class StorageDataStoreContextConnectionHelper
{
	
	/// <summary>
	/// The data store's context.
	/// </summary>
	protected DataStoreContextEntity DataStoreContextEntity;
	
	/// <summary>
	/// The data store's connection helper.
	/// </summary>
	protected DataStoreConnectionHelper DataStoreConnection;
	
	/// <summary>
	/// Constructor.
	/// </summary>
	/// <param name="dataStoreContextEntity">The data store context.</param>
	public StorageDataStoreContextConnectionHelper(DataStoreContextEntity dataStoreContextEntity)
	{
		DataStoreContextEntity = dataStoreContextEntity;
		DataStoreConnection = dataStoreContextEntity.DataStore.Connection;
	}
	
	#region Methods
	
	/// <inheritdoc cref="IDataStoreConnection.GetContext"/>
	internal IDataStoreContext GetContext()
	{
		var context = DataStoreConnection.DataStoreInstance.GetContext(DataStoreContextEntity.Id.ToString());
		
		if (context == null)
			throw new NullReferenceException();
		
		return context;
	}
	
	/// <inheritdoc cref="IStorageConnection.CreateContext"/>
	internal StorageContext CreateContext()
	{
		return ExecuteOperation(connection =>
		{
			if (DataStoreContextEntity.Id == Guid.Empty)
				DataStoreContextEntity.Id = Guid.NewGuid();
			var context = connection.CreateContext(new StorageContext(DataStoreContextEntity.Id.ToString(), DataStoreContextEntity.Options!));
			DataStoreContextEntity.Options = context.Config.ToDictionary();
			return context;
		});
	}
	
	/// <inheritdoc cref="IStorageConnection.UpdateContext"/>
	internal StorageContext UpdateContext()
	{
		return ExecuteOperation(connection =>
		{
			var context = connection.UpdateContext(new StorageContext(DataStoreContextEntity.Id.ToString(), DataStoreContextEntity.Options!));
			DataStoreContextEntity.Options = context.Config.ToDictionary();
			return context;
		});
	}
	
	/// <inheritdoc cref="IStorageConnection.RemoveContext"/>
	internal void RemoveContext(bool forceDeleteDb)
	{
		ExecuteOperation(connection => connection.RemoveContext(DataStoreContextEntity.Id.ToString(), forceDeleteDb));
	}
	
	private void ExecuteOperation(Action<IStorageConnection> operation)
	{
		IStorageConnection? connection = null;
		
		try
		{
			connection = (IStorageConnection)DataStoreConnection.OpenConnection();
			
			operation(connection);
		}
		finally
		{
			connection?.Dispose();
		}
	}
	
	private T ExecuteOperation<T>(Func<IStorageConnection, T> operation)
	{
		T result;
		IStorageConnection? connection = null;
		
		try
		{
			connection = (IStorageConnection)DataStoreConnection.OpenConnection();
			
			result = operation(connection);
		}
		finally
		{
			connection?.Dispose();
		}
		
		return result;
	}
	
	#endregion
}