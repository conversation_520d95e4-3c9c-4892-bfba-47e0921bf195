using System.ComponentModel.DataAnnotations.Schema;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Entities.Features.PageView.ListView;

/// <summary>
/// a list view configuration is used to configure a list of multiple datasets which later on get rendered into a table
/// </summary>
public class ListViewEntity : PageViewEntity, IAdministrationEntity<ListViewEntity, ListViewDto>
{
	/// <summary>
	/// should the view display a document preview (aka thumbnails)
	/// </summary>
	public bool ShowPreview { get; set; }
	
	/// <summary>
	/// are users allowed to show/hide columns
	/// </summary>
	public bool AllowDisplayColumns { get; set; } = true;

	/// <summary>
	/// how much column should be displayed sticky? (Works only in expert view)
	/// </summary>
	public int StickyColumnCount { get; set; } = 1;

	/// <summary>
	/// Determines how the ListView has to be displayed
	/// </summary>
	public ListViewDisplayType DisplayType { get; set; } = ListViewDisplayType.Default;
	
	/// <summary>
	/// reference id to a field that contains the id of the parent record
	/// </summary>
	public Guid? ParentFieldId { get; set; }
	
	/// <summary>
	/// reference to a field that contains the id of the parent record
	/// </summary>
	[SetNull]
	[ForeignKey(nameof(ParentFieldId))]
	public DataFieldEntity? ParentField { get; set; }

	/// <summary>
	/// list of (table) columns available inside this view
	/// </summary>
	public virtual ICollection<ListViewColumnEntity> Columns { get; } = new List<ListViewColumnEntity>();
	
	/// <inheritdoc />
	public ListViewEntity()
	{
		Type = PageViewType.List;
	}

	private ListViewEntity(ListViewDto dto, UserEntity? createdBy, CoreDatabaseContext? context = null) : base(dto, createdBy, context)
	{
		ShowPreview = dto.ShowPreview ?? false;
		AllowDisplayColumns = dto.AllowDisplayColumns ?? true;
		ExpertMode = dto.ExpertMode ?? false;
		if (dto.StickyColumnCount != null)
			StickyColumnCount = dto.StickyColumnCount.Value;
		
		if (dto.DisplayType.HasValue)
			DisplayType = dto.DisplayType.Value;
		
		if (dto.ParentFieldId != null && dto.ParentFieldId != Guid.Empty)
		{
			var field = context?.DataFields.Find(dto.ParentFieldId);
			ParentField = field ?? throw new ArgumentException($"Property 'ParentField' in ListView is not valid: {dto.ParentFieldId}");
			ParentFieldId = field.Id;
		}
	}
	
	/// <inheritdoc />
	public static ListViewEntity FromDto(ListViewDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new ListViewEntity(entityInfo, createdBy, context);
	}
	
	/// <inheritdoc />
	public new ListViewDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		Localizer ??= StringLocalizerFactory?.Create("PageViews","", true);
		TypeLocalizer ??= StringLocalizerFactory?.Create("PageViewType", "");
		return new ListViewDto(this, excludedProperties, handledObjects)
		{
			NameTranslated = Localizer != null ? Localizer[Name] : string.Empty,
			TypeName = TypeLocalizer != null ? TypeLocalizer[Type.ToString()] : "",
			Icon = Icon ?? Type.GetIcon(),
			DisplayType = DisplayType,
			ParentFieldId = ParentFieldId,
			ParentField = ParentField?.ToDto()
		};
	}
	
	/// <inheritdoc />
	public void UpdatePartial(ListViewDto dto, UserEntity? modifiedBy = null)
	{
		base.UpdatePartial(dto, modifiedBy);
		if (dto.ShowPreview != null)
			ShowPreview = dto.ShowPreview.Value;
		if (dto.AllowDisplayColumns != null)
			AllowDisplayColumns = dto.AllowDisplayColumns.Value;
		if (dto.StickyColumnCount != null)
			StickyColumnCount = dto.StickyColumnCount.Value;
		if (dto.DisplayType != null)
			DisplayType = dto.DisplayType.Value;
		if (dto.ParentFieldId != null)
			ParentFieldId = dto.ParentFieldId == Guid.Empty ? null : dto.ParentFieldId;
	}
	
	/// <inheritdoc />
	public override void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider)
	{
		modelBuilder.Entity<ListViewEntity>().ToTable("ListViews");
		
		base.ConfigureModel(modelBuilder, databaseProvider);
	}
}