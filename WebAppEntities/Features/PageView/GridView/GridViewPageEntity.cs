using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;
using System.ComponentModel.DataAnnotations.Schema;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Entities.Features.Page;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.IdentityModel.Tokens;
using Serilog;

namespace Levelbuild.Entities.Features.PageView.GridView;

/// <summary>
/// configuration entity which allows us to embed a PageEntity directly into a GridViewEntity or into a GridViewSection (which is then embedded into the GridView)
/// </summary>
public class GridViewPageEntity : RevisedPersistentEntity<GridViewPageEntity>, ISortableEntity, IAdministrationEntity<GridViewPageEntity, GridViewPageDto>, IStartupMigration
{
	#region embedded into GridView
	
	/// <summary>
	/// reference to the GridViewEntity the page is embedded into
	/// </summary>
	public Guid? GridViewId { get; set; }
	
	/// <summary>
	/// GridViewEntity the page is embedded into
	/// </summary>
	[CascadeDelete]
	[ForeignKey(nameof(GridViewId))]
	public GridViewEntity? GridView { get; set; }
	
	/// <summary>
	/// column the page is embedded into
	/// </summary>
	public int GridViewColumn { get; set; }
	
	/// <summary>
	/// determines the display order inside the column
	/// </summary>
	public int Position { get; set; }
	
	#endregion embedded into GridView
	
	#region embedded into GridViewSection
	
	/// <summary>
	/// reference to the GridViewSectionEntity the page is embedded into
	/// </summary>
	public Guid? SectionId { get; set; }
	
	/// <summary>
	/// GridViewSectionEntity the page is embedded into
	/// </summary>
	[CascadeDelete]
	[ForeignKey(nameof(SectionId))]
	public GridViewSectionEntity? Section { get; set; }
	
	/// <summary>
	/// row number where the element starts
	/// </summary>
	public int RowStart { get; set; }
	
	/// <summary>
	/// row number where the element ends
	/// </summary>
	public int RowEnd { get; set; }
	
	/// <summary>
	/// column number where the element starts
	/// </summary>
	public int ColStart { get; set; }
	
	/// <summary>
	/// column number where the element ends
	/// </summary>
	public int ColEnd { get; set; }
	
	#endregion embedded into GridViewSection

	/// <summary>
	/// is this an embedded page or a tile with the count of the elements in the embedded page?
	/// </summary>
	public GridViewPageType GridViewPageType { get; set; } = GridViewPageType.Page;
	
	/// <summary>
	/// reference to the PageEntity which should be embedded
	/// </summary>
	public Guid? EmbeddedPageId { get; set; }
	
	/// <summary>
	/// PageEntity which should be embedded
	/// </summary>
	[SetNull]
	[ForeignKey(nameof(EmbeddedPageId))]
	public PageEntity? EmbeddedPage { get; set; }
	
	/// <summary>
	/// reference to a PageViewEntity of the PageEntity which should be displayed
	/// </summary>
	public Guid? EmbeddedViewId { get; set; }
	
	/// <summary>
	/// PageViewEntity of the PageEntity which should be displayed
	/// </summary>
	[SetNull]
	[ForeignKey(nameof(EmbeddedViewId))]
	public PageViewEntity? EmbeddedView { get; set; }
	
	/// <summary>
	/// reference to a PageViewSectionEntity of the PageEntity which should be embedded
	/// </summary>
	public Guid? EmbeddedSectionId { get; set; }
	
	/// <summary>
	/// PageViewSectionEntity of the PageEntity which should be embedded
	/// </summary>
	[SetNull]
	[ForeignKey(nameof(EmbeddedSectionId))]
	public GridViewSectionEntity? EmbeddedSection { get; set; }

	/// <summary>
	/// is the embedded page directly connected to the current dataset or is it connected to a referenced dataset?
	/// </summary>
	public GridViewPageReferenceType ReferenceType { get; set; } = GridViewPageReferenceType.Self;
	
	/// <summary>
	/// if the embedded page is connected to another dataset, which fk is used to get to this dataset?
	/// </summary>
	public Guid? ReferenceFieldId { get; set; }
	
	/// <summary>
	/// DataFieldEntity of the foreign key field inside the current definition referencing the dataset the embedded page is connected to 
	/// </summary>
	[SetNull]
	[ForeignKey(nameof(ReferenceFieldId))]
	public DataFieldEntity? ReferenceField { get; set; }
	
	/// <summary>
	/// the connection between the embedded page and the current / referenced dataset is defined via a lookup field of the referenced page (or more precise the data source behind this page)
	/// </summary>
	public Guid? KeyFieldId { get; set; }
	
	/// <summary>
	/// DataFieldEntity of the key field used to define the connection between the embedded page and the current / referenced dataset
	/// </summary>
	[SetNull]
	[ForeignKey(nameof(KeyFieldId))]
	public DataFieldEntity? KeyField { get; set; }
	
	/// <summary>
	/// optional title used as a headline for the embedded page (if empty, the translated name of the embedded page is used instead)
	/// </summary>
	public string? Title { get; set; }
	
	/// <summary>
	/// optional maximum height for the embedded page
	/// </summary>
	public int? MaxHeight { get; set; }
	
	/// <summary>
	/// if set to false, the page gets embedded without rendering a headline above
	/// </summary>
	public bool ShowTitle { get; set; } = true;
	
	/// <summary>
	/// is the user allowed to collapse the page so that just the headline stays visible
	/// </summary>
	public bool AllowMinimize { get; set; } = true;
	
	/// <summary>
	/// should the page be displayed collapsed when the user enters the page?
	/// </summary>
	public bool StartMinimized { get; set; }
	
	/// <summary>
	/// is the user allowed to create new datasets inside the embedded page?
	/// </summary>
	public bool AllowCreate { get; set; }
	
	/// <summary>
	/// is the user allowed to open the page in a new browser tab?
	/// </summary>
	public bool AllowMaximize { get; set; }

	#region Tile
	
	/// <summary>
	/// what kind of tile is it
	/// </summary>
	public GridViewPageTileType? TileType { get; set; }
	
	/// <summary>
	/// optional icon
	/// </summary>
	public string? Icon { get; set; }
	
	/// <summary>
	/// allow the embedded page to be opened in a new tab on click
	/// </summary>
	public bool? AllowOpenNewTab { get; set; }
	
	/// <summary>
	/// if the Tile is an aggregation tile, this is the field id to do the aggregation on
	/// </summary>
	public Guid? AggregationFieldId { get; set; }
	
	/// <summary>
	/// if the Tile is an aggregation tile, this is the field to do the aggregation on
	/// </summary>
	[SetNull]
	[ForeignKey(nameof(AggregationFieldId))]
	public DataFieldEntity? AggregationField { get; set; }
	
	/// <summary>
	/// what kind of operation should be done on the aggregation field
	/// </summary>
	public AggregationMethod? AggregationMethod { get; set; }
	
	/// <summary>
	/// optional usage of thousand separators
	/// </summary>
	public bool? WithThousandSeparators { get; set; }
	
	/// <summary>
	/// optional divider
	/// </summary>
	public int? Divider { get; set; }
	
	/// <summary>
	/// if divider != 1 this is inserted between the value and the unit (e.g. 10 thsd. €)
	/// </summary>
	public string? DividerUnit { get; set; }
	
	/// <summary>
	/// Amount of decimal places to depict the value with
	/// </summary>
	public int? DecimalPlaces { get; set; }
	
	/// <summary>
	/// Upper Range value when the tile should be marked as positive
	/// </summary>
	public double? UpperThresholdPositive { get; set; }
	
	/// <summary>
	/// Lower Range value when the tile should be marked as positive
	/// </summary>
	public double? LowerThresholdPositive { get; set; }
	
	/// <summary>
	/// Upper Range value when the tile should be marked as warning
	/// </summary>
	public double? UpperThresholdWarning { get; set; }
	
	/// <summary>
	/// Lower Range value when the tile should be marked as warning
	/// </summary>
	public double? LowerThresholdWarning { get; set; }
	
	/// <summary>
	/// Upper Range value when the tile should be marked as danger
	/// </summary>
	public double? UpperThresholdDanger { get; set; }
	
	/// <summary>
	/// Lower Range value when the tile should be marked as danger
	/// </summary>
	public double? LowerThresholdDanger { get; set; }

	#endregion
	
	
	/// <summary>
	/// filter conditions 
	/// </summary>
	public ICollection<GridViewPageFilterEntity> Filters { get; set; } = new List<GridViewPageFilterEntity>();
	
	private IStringLocalizer? _localizer;
	private IStringLocalizer? _headerLocalizer;
	private IStringLocalizer? _pageLocalizer;
	
	/// <summary>
	/// constructor
	/// </summary>
	public GridViewPageEntity()
	{
	}
	
	private GridViewPageEntity(GridViewPageDto dto, CoreDatabaseContext? databaseContext)
	{
		GridViewPageType = dto.GridViewPageType;
		GridViewColumn = dto.GridViewColumn;
		Position = dto.Position ?? 0;
		SectionId = dto.SectionId;
		if(SectionId == null)
			GridViewId = dto.GridViewId!.Value;
		RowStart = dto.RowStart;
		RowEnd = dto.RowEnd;
		ColStart = dto.ColStart;
		ColEnd = dto.ColEnd;
		if (dto.ReferenceType != null)
			ReferenceType = dto.ReferenceType.Value;
		if (ReferenceType == GridViewPageReferenceType.ForeignElement)
			ReferenceFieldId = dto.ReferenceFieldId;
		
		EmbeddedPageId = dto.EmbeddedPageId;
		KeyFieldId = dto.KeyFieldId;
		EmbeddedViewId = dto.EmbeddedViewId;
		EmbeddedSectionId = dto.EmbeddedSectionId;
		
		Title = dto.Title;
		
		if (dto.AllowMaximize != null)
			AllowMaximize = dto.AllowMaximize.Value;

		if (dto.GridViewPageType == GridViewPageType.Page)
		{
			MaxHeight = dto.MaxHeight;
			ShowTitle = dto.ShowTitle != false;
			if (dto.AllowMinimize != null)
				AllowMinimize = dto.AllowMinimize.Value;
			if (dto.StartMinimized != null)
				StartMinimized = dto.StartMinimized.Value;
			if (dto.AllowCreate != null)
				AllowCreate = dto.AllowCreate.Value;
		}

		if (dto.GridViewPageType == GridViewPageType.Tile)
		{
			TileType = dto.TileType;
			Icon = dto.Icon;
			WithThousandSeparators = dto.WithThousandSeparators == true;
			
			if (dto.TileType == GridViewPageTileType.Count)
			{
				AllowOpenNewTab = dto.AllowOpenNewTab == true;
			}

			if (dto.TileType == GridViewPageTileType.Aggregation)
			{
				AggregationFieldId = dto.AggregationFieldId;
				AggregationMethod = dto.AggregationMethod;

				if (dto.Divider != null && dto.Divider.Value != 0)
				{
					Divider = dto.Divider.Value;
				}
				else
					Divider = 1;
				
				DividerUnit = Divider != 1 ? dto.DividerUnit : null;
				DecimalPlaces = dto.DecimalPlaces;
				UpperThresholdPositive = dto.UpperThresholdPositive;
				LowerThresholdPositive = dto.LowerThresholdPositive;
				UpperThresholdWarning = dto.UpperThresholdWarning;
				LowerThresholdWarning = dto.LowerThresholdWarning;
				UpperThresholdDanger = dto.UpperThresholdDanger;
				LowerThresholdDanger = dto.LowerThresholdDanger;
			}
		}

		if (databaseContext != null)
		{
			SetContext(databaseContext);
			Touch();
		}
	}
	
	/// <inheritdoc />
	public static GridViewPageEntity FromDto(GridViewPageDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new GridViewPageEntity(entityInfo, context);
	}
	
	/// <inheritdoc />
	public GridViewPageDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		var pageDto = new GridViewPageDto(this, excludedProperties, handledObjects);
		
		_localizer ??= StringLocalizerFactory?.Create("GridViewPage", "");
		_headerLocalizer ??= StringLocalizerFactory?.Create("GridViewPage", "", true);
		_pageLocalizer ??= StringLocalizerFactory?.Create("Pages", "", true);
		if (_localizer == null || _pageLocalizer == null || _headerLocalizer == null)
			return pageDto;

		var newPageTitle = _localizer["newEmbeddedPage"];
		switch (GridViewPageType)
		{
			case GridViewPageType.Page:
				pageDto.Flag = _localizer["embeddedPage"];
				break;
			case GridViewPageType.Tile:
				pageDto.Flag = _localizer["embeddedTile"];
				newPageTitle = _localizer["newEmbeddedTile"];
				break;
			default:
				pageDto.Flag = _localizer["embeddedPage"];
				break;
		}
		
		if (pageDto.EmbeddedPage?.Name != null && pageDto.EmbeddedPage.Name != "")
			pageDto.Flag += " - " + pageDto.EmbeddedPage.Name;
		
		pageDto.TitleTranslated = !Title.IsNullOrEmpty() ? _headerLocalizer[Title!] :
								  pageDto.EmbeddedPage?.Name != null ? _pageLocalizer[pageDto.EmbeddedPage.Name] : newPageTitle;

		if(!DividerUnit.IsNullOrEmpty())
			pageDto.DividerUnitTranslated =_headerLocalizer[DividerUnit!];
		
		pageDto.Type = GridViewSectionElementType.Page;
		return pageDto;
	}
	
	/// <inheritdoc />
	public void UpdatePartial(GridViewPageDto dto, UserEntity? modifiedBy = null)
	{
		// Column set = placed outside section
		if (dto.GridViewColumn >= 0)
		{
			if (dto.GridViewId != null)
				GridViewId = dto.GridViewId.Value;
			GridViewColumn = dto.GridViewColumn;
			SectionId = null;
			RowStart = -1;
			RowEnd = -1;
			ColStart = -1;
			ColEnd = -1;
		}
		
		// section set = placed inside section
		if (dto.SectionId != null)
		{
			SectionId = dto.SectionId;
			GridViewId = null;
			GridViewColumn = -1;
			Position = 0;
		}

		if (dto.Position != null)
			Position = dto.Position.Value;
		if (dto.EmbeddedPageId != null)
			EmbeddedPageId = dto.EmbeddedPageId;
		if (dto.ReferenceType != null)
			ReferenceType = dto.ReferenceType.Value;
		if (dto.ReferenceFieldId != null)
			ReferenceFieldId = dto.ReferenceFieldId;
		if (dto.KeyFieldId != null)
			KeyFieldId = dto.KeyFieldId;
		if (dto.EmbeddedViewId != null)
			EmbeddedViewId = dto.EmbeddedViewId;
		if (dto.EmbeddedSectionId != null)
			EmbeddedSectionId = dto.EmbeddedSectionId;
		if (dto.Title != null)
			Title = dto.Title;
		if (dto.MaxHeight != null)
			MaxHeight = dto.MaxHeight;
		if (dto.ShowTitle != null)
			ShowTitle = dto.ShowTitle != false;
		if (dto.AllowMinimize != null)
			AllowMinimize = dto.AllowMinimize != false;
		if (dto.StartMinimized != null)
			StartMinimized = dto.StartMinimized == true;
		if (dto.AllowCreate != null)
			AllowCreate = dto.AllowCreate == true;
		if (dto.AllowMaximize != null)
			AllowMaximize = dto.AllowMaximize == true;
		if (dto.RowStart > -1)
			RowStart = dto.RowStart;
		if (dto.RowEnd > -1)
			RowEnd = dto.RowEnd;
		if (dto.ColStart > -1)
			ColStart = dto.ColStart;
		if (dto.ColEnd > -1)
			ColEnd = dto.ColEnd;
		if (dto.AllowOpenNewTab != null)
			AllowOpenNewTab = dto.AllowOpenNewTab;
		if (dto.Icon != null)
			Icon = dto.Icon;
		if (dto.WithThousandSeparators != null)
			WithThousandSeparators = dto.WithThousandSeparators == true;
		if (dto.Divider != null && dto.Divider != 0)
			Divider = dto.Divider;
		else
			Divider = 1;
		if (Divider != 1)
		{
			if(dto.DividerUnit != null)
				DividerUnit = dto.DividerUnit;
		}
		else
			DividerUnit = null;
		
		if(dto.DecimalPlaces > -1)
			DecimalPlaces = dto.DecimalPlaces;
		if(dto.TileType != null)
			TileType = dto.TileType;
		if(dto.AggregationFieldId != null)
			AggregationFieldId = dto.AggregationFieldId;
		if(dto.AggregationMethod != null)
			AggregationMethod = dto.AggregationMethod;
		
		UpperThresholdPositive = dto.UpperThresholdPositive;
		LowerThresholdPositive = dto.LowerThresholdPositive;
		UpperThresholdWarning = dto.UpperThresholdWarning;
		LowerThresholdWarning = dto.LowerThresholdWarning;
		UpperThresholdDanger = dto.UpperThresholdDanger;
		LowerThresholdDanger = dto.LowerThresholdDanger;
		
		Touch();
	}
	
	/// <inheritdoc />
	public override void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider)
	{
		modelBuilder.Entity<GridViewPageEntity>()
			.HasOne(page => page.Section)
			.WithMany(section => section.Pages);
		
		base.ConfigureModel(modelBuilder, databaseProvider);
	}
	
	/// <summary>
	/// Touch the parent view to mark it as changed
	/// </summary>
	public new void Touch()
	{
		base.Touch();
		DbContext?.PageViews.Find(GridViewId)?.Touch();
		DbContext?.GridViewSections.Find(SectionId)?.Touch();
	}
	
	#region Startup MIgrations

	/// <inheritdoc />
	public static void RegisterMigrations(ref readonly Dictionary<string, Func<ILogger, CoreDatabaseContext, IServiceProvider, Task>> migrationRegistry)
	{
		migrationRegistry.Add("SetTileToCountTile", SetTileToCountTile);
	}

	private static async Task SetTileToCountTile (ILogger logger, CoreDatabaseContext databaseContext, IServiceProvider serviceProvider)
	{
		var existingTiles = await databaseContext.GridViewPages
								 .Where(page => page.GridViewPageType == GridViewPageType.Tile)
								 .ToListAsync();

		foreach (var tile in existingTiles)
		{
			tile.TileType = GridViewPageTileType.Count;
		}
		await databaseContext.SaveChangesAsync();
	}

	#endregion
}