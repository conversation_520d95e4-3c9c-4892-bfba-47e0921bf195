using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos.CachedDeepZoom;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Entities.Interfaces;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace Levelbuild.Entities.Features.DeepZoom;

/// <summary>
/// Entity reflecting cached thumbnail files
/// </summary>
public class CachedDeepZoomEntity : CachedFileEntity<CachedDeepZoomEntity>, IConvertibleEntity<CachedDeepZoomDto>
{
	/// <summary>
	/// Metadata of the file on amount of pixel overlap between sub images
	/// </summary>
	public int? Overlap { get; set; }

	/// <summary>
	/// Metadata of the file on the size of the tiling
	/// </summary>
	public int? TileSize { get; set; }

	/// <summary>
	/// Metadata of the file on the dpi of the file in case it's a pdf file
	/// </summary>
	public int? Dpi { get; set; } = 300;

	/// <summary>
	/// Width of the image
	/// </summary>
	public int? Width { get; set; }

	/// <summary>
	/// height of the image
	/// </summary>
	public int? Height { get; set; }

	/// <summary>
	/// The files by name and their start position in the file and the length
	/// </summary>
	public Dictionary<string, (long, long)>? FilePositions { get; set; }
	
	/// <summary>
	/// State of the caching process of the file
	/// </summary>
	public CachedDeepZoomState State { get; set; } = CachedDeepZoomState.Caching;


	/// <inheritdoc/>
	public override string GetDirectoryName()
	{
		return "DeepZoomImages";
	}

	/// <summary>
	/// Latest Error Message when caching process failed
	/// </summary>
	public string? ErrorMessage { get; set; }

	/// <summary>
	/// Amount of times the generation/caching has failed
	/// </summary>
	public int ErrorCount { get; set; }
	
	/// <summary>
	/// Updates properties
	/// <param name="overlap"></param>
	/// <param name="tileSize"></param>
	/// <param name="dpi"></param>
	/// <param name="width"></param>
	/// <param name="height"></param>
	/// </summary>
	public void SetMetaData(int overlap, int tileSize, int dpi, int width, int height, Dictionary<string, (long, long)>? filePositions)
	{
		Width = width;
		Height = height;
		Overlap = overlap;
		TileSize = tileSize;
		Dpi = dpi;
		FilePositions = filePositions;
	}

	/// <inheritdoc/>
	public CachedDeepZoomEntity()
	{
		//empty
	}

	/// <inheritdoc/>
	public CachedDeepZoomEntity(string cachedFileId, string fileId, int overlap, int tileSize, int dpi, int width, int height) : base(cachedFileId, fileId)
	{
		Width = width;
		Height = height;
		Overlap = overlap;
		TileSize = tileSize;
		Dpi = dpi;
	}

	/// <inheritdoc/>
	public CachedDeepZoomDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		return new CachedDeepZoomDto(this, excludedProperties, handledObjects);
	}

	/// <inheritdoc />
	public override void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider)
	{
		modelBuilder.Entity<CachedDeepZoomEntity>()
			.Property(entity => entity.FilePositions)
			.HasConversion(
				dictionary => JsonConvert.SerializeObject(dictionary),
				json => JsonConvert.DeserializeObject<Dictionary<string, (long, long)>>(json));
	}
}