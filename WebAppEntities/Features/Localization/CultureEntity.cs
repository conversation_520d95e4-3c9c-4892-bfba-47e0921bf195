using System.ComponentModel.DataAnnotations;
using System.Globalization;
using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos.Localization;
using Levelbuild.Entities.Attributes;
using Levelbuild.Entities.Extensions;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

namespace Levelbuild.Entities.Features.Localization;

/// <summary>
/// represents a culture/language a user can choose to localize the system
/// </summary>
[Slug(nameof(Name))]
public class CultureEntity : PersistentEntity<CultureEntity>, IAdministrationEntity<CultureEntity, CultureDto>
{
	/// <summary>
	/// unique slug which is used as part of the url while editing the culture
	/// </summary>
	[Required]
	public string Slug { get; private init; }
	
	private string _name = "";

	/// <summary>
	/// name of the culture/language
	/// </summary>
	[Required]
	public string Name
	{
		get => _name;
		init
		{
			_name = value;
			Slug = _name.ToSlug();
		}
	}

	/// <summary>
	/// all translations associated to this culture
	/// </summary>
	public virtual ICollection<TranslationEntity> Translations { get; } = new List<TranslationEntity>();

	/// <summary>
	/// all users who have configured this culture as their current culture
	/// </summary>
	public virtual ICollection<UserEntity> Users { get; } = new List<UserEntity>();

	private IStringLocalizer? _localizer;

	/// <inheritdoc />
	public CultureEntity()
	{
		// nothing
	}

	private CultureEntity(CultureDto dto)
	{
		Name = dto.Name;
	}

	/// <inheritdoc />
	public static CultureEntity FromDto(CultureDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new CultureEntity(entityInfo);
	}

	/// <inheritdoc />
	public CultureDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		return new CultureDto(this, excludedProperties, handledObjects)
		{
			DisplayName = ToInfo().DisplayName,
			TranslationCount = Translations.Count,
			UserCount = Users.Count
		};
	}

	/// <summary>
	/// convert the CultureEntity into a CultureInfo
	/// </summary>
	public CultureInfo ToInfo()
	{
		return new CultureInfo(Name);
	}

	/// <inheritdoc />
	public void UpdatePartial(CultureDto dto, UserEntity? modifiedBy = null)
	{
		// currently nothing to do here because the name may not be changed afterward (and therefore the slug isn't changing either)
	}

	/// <inheritdoc />
	public override void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider)
	{
		modelBuilder.Entity<CultureEntity>()
			.HasIndex(c => c.Name)
			.IsUnique();
		
		base.ConfigureModel(modelBuilder, databaseProvider);
	}
}