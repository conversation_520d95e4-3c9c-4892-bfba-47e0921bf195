using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.News;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Entities.Features.Customer;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;
using Microsoft.Extensions.Localization;

namespace Levelbuild.Entities.Features.News;

/// <summary>
/// Entity for data source aggregation and configuration
/// </summary>
public class NewsCategoryEntity : PersistentEntity<NewsCategoryEntity>, IAdministrationEntity<NewsCategoryEntity, NewsCategoryDto>
{
	/// <summary>
	/// category name
	/// </summary>
	[Required]
	public required string Name { get; set; }
	
	/// <summary>
	/// category icon
	/// </summary>
	[Required]
	public required string Icon { get; set; }
	
	/// <summary>
	/// id of the default image for this category
	/// </summary>
	public required Guid ImageId { get; set; }
	
	/// <summary>
	/// Image entity containing image metadata
	/// </summary>
	[CascadeDelete]
	[ForeignKey(nameof(ImageId))]
	public NewsImageEntity? Image { get; set; }
	
	/// <summary>
	/// some categories may be specific to a certain customer
	/// </summary>
	public Guid? CustomerId  { get; set; }
	
	/// <summary>
	/// some categories may be specific to a certain customer
	/// </summary>
	public CustomerEntity? Customer { get; set; }
	
	/// <summary>
	/// creation date
	/// </summary>
	public DateTime? Created { get; init; }

	/// <summary>
	/// name of the user who created the category
	/// </summary>
	public string? CreatedBy { get; init; }
	
	/// <summary>
	/// last changed date
	/// </summary>
	public DateTime? LastModified { get; set; }

	/// <summary>
	/// name of the user who last edited the category
	/// </summary>
	public string? LastModifiedBy { get; set; }
	
	private IStringLocalizer? _localizer;

	/// <inheritdoc />
	public NewsCategoryEntity()
	{
	}
	
	private NewsCategoryEntity(NewsCategoryDto dto, UserEntity? createdBy)
	{
		if (dto.CustomerId != Guid.Empty)
			CustomerId = dto.CustomerId;
		
		Created = DateTime.Now.ToUniversalTime();
		CreatedBy = createdBy?.DisplayName;
		LastModified = DateTime.Now.ToUniversalTime();
		LastModifiedBy = createdBy?.DisplayName;
	}

	/// <inheritdoc />
	public static NewsCategoryEntity FromDto(NewsCategoryDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		if (entityInfo.Name == null || entityInfo.Icon == null || entityInfo.ImageId == null)
			throw new Exception("Name, Icon and Image are required to be able to create a new news category.");
		
		return new NewsCategoryEntity(entityInfo, createdBy)
		{
			Name = entityInfo.Name,
			Icon = entityInfo.Icon,
			ImageId = entityInfo.ImageId.Value
		};
	}

	/// <inheritdoc />
	public NewsCategoryDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		_localizer ??= StringLocalizerFactory?.Create("NewsCategory", "", true);
		return new NewsCategoryDto(this, excludedProperties, handledObjects)
		{
			NameTranslated = _localizer != null ? _localizer[Name] : string.Empty,
			CustomerName = Customer?.DisplayName
		};
	}

	/// <inheritdoc />
	public void UpdatePartial(NewsCategoryDto dto, UserEntity? modifiedBy)
	{
		if (dto.CustomerId != null)
			CustomerId = dto.CustomerId == Guid.Empty ? null : dto.CustomerId;
		if (!string.IsNullOrEmpty(dto.Name))
			Name = dto.Name;
		if (!string.IsNullOrEmpty(dto.Icon))
			Icon = dto.Icon;
		if (dto.ImageId != null && dto.ImageId != Guid.Empty)
			ImageId = dto.ImageId.Value;
		
		LastModified = DateTime.Now.ToUniversalTime();
		LastModifiedBy = modifiedBy?.DisplayName;
	}
}