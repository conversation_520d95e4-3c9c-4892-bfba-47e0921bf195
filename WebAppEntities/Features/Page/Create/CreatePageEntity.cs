using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Entities.Features.PageView.GridView;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Entities.Features.Page.Create;

/// <summary>
/// PageEntity used to create a new dataset
/// </summary>
public class CreatePageEntity : PageEntity, IAdministrationEntity<CreatePageEntity, CreatePageDto>
{
	/// <summary>
	/// specific save button label (if not set, the ux defaults to "save")
	/// </summary>
	public string SaveButtonLabel { get; set; } = "";
	
	/// <inheritdoc />
	public CreatePageEntity()
	{
		Type = PageType.SingleData;
	}

	private CreatePageEntity(CreatePageDto dto, UserEntity? createdBy, CoreDatabaseContext? databaseContext) : base(dto, createdBy, databaseContext)
	{
		Views.Add( new GridViewEntity
		{
			Name = "Form",
			Icon = "clipboard-list",
			SystemView = true,
			Position = -1
		});
	}
	
	/// <inheritdoc />
	public static CreatePageEntity FromDto(CreatePageDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new CreatePageEntity(entityInfo, createdBy, context);
	}
	
	/// <inheritdoc />
	public new CreatePageDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		return new CreatePageDto(base.ToDto(excludedProperties, handledObjects))
		{
			SaveButtonLabel = SaveButtonLabel
		};
	}
	
	/// <inheritdoc />
	public void UpdatePartial(CreatePageDto dto, UserEntity? modifiedBy = null)
	{
		base.UpdatePartial(dto, modifiedBy);
		if (dto.SaveButtonLabel != null)
			SaveButtonLabel = dto.SaveButtonLabel;
	}
	
	/// <inheritdoc />
	public override void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider)
	{
		modelBuilder.Entity<CreatePageEntity>().ToTable("CreatePage");
		
		base.ConfigureModel(modelBuilder, databaseProvider);
	}
}