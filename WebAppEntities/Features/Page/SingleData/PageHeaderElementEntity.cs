using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace Levelbuild.Entities.Features.Page.SingleData;

/// <summary>
/// entity representing one element inside the page header
/// </summary>
public class PageHeaderElementEntity : RevisedPersistentEntity<PageHeaderElementEntity>, IAdministrationEntity<PageHeaderElementEntity, PageHeaderElementDto>
{
	/// <summary>
	/// Parent entity ID
	/// </summary>
	[Required]
	public Guid PageId { get; init; }

	/// <summary>
	/// Parent entity
	/// </summary>
	[CascadeDelete]
	[ForeignKey(nameof(PageId))]
	public SingleDataPageEntity? Page { get; init; }
	
	/// <summary>
	/// Icon displayed before the actual information
	/// </summary>
	[ShortString]
	public string Icon { get; set; } = "";
	
	/// <summary>
	/// Label describing the value
	/// </summary>
	public string Label { get; set; } = "";

	/// <summary>
	/// value to display (contains ##placeholders##)
	/// </summary>
	public string Value { get; set; } = "";
	
	/// <summary>
	/// position of the element inside the header
	/// </summary>
	public int Position { get; set; }

	/// <summary>
	/// is the header entry enabled aka visible to the user?
	/// </summary>
	public bool Enabled { get; set; } = true;
	
	private IStringLocalizer? _localizer;

	/// <inheritdoc />
	public PageHeaderElementEntity()
	{
		
	}

	private PageHeaderElementEntity(PageHeaderElementDto dto, CoreDatabaseContext? databaseContext)
	{
		PageId = dto.PageId;
		Icon = dto.Icon ?? "";
		Label = dto.Label ?? "";
		Value = dto.Value ?? "";
		Enabled = dto.Enabled == true;
		Position = dto.Position ?? -1;

		DbContext ??= databaseContext;
		Touch();
	}
	
	/// <inheritdoc />
	public static PageHeaderElementEntity FromDto(PageHeaderElementDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new PageHeaderElementEntity(entityInfo, context);
	}
	
	/// <inheritdoc />
	public PageHeaderElementDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		_localizer ??= StringLocalizerFactory?.Create("PageHeaderElement", $"{PageId.ToString()}", true);
		return new PageHeaderElementDto(this, excludedProperties, handledObjects)
		{
			LabelTranslated = _localizer != null ? _localizer[Label] : string.Empty
		};
	}
	
	/// <inheritdoc />
	public void UpdatePartial(PageHeaderElementDto dto, UserEntity? modifiedBy = null)
	{
		if (dto.Icon != null)
			Icon = dto.Icon;
		if (dto.Label != null)
			Label = dto.Label;
		if (dto.Value != null)
			Value = dto.Value;
		if (dto.Enabled != null)
			Enabled = dto.Enabled.Value;
		if (dto.Position != null)
			Position = dto.Position.Value;
		Touch();
	}
	
	/// <inheritdoc />
	public override void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider)
	{
		modelBuilder.Entity<PageHeaderElementEntity>().ToTable("PageHeaderElements");
		
		base.ConfigureModel(modelBuilder, databaseProvider);
	}
	
	/// <summary>
	/// Touch the page to mark it as changed
	/// </summary>
	private new void Touch()
	{
		base.Touch();
		if (Page != null)
			Page.Touch(true);
		else
			DbContext?.Pages.Find(PageId)?.Touch(true);
	}
}