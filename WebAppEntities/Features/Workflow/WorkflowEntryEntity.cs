using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.EntityInterface.Constants;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Workflow;
using Levelbuild.Entities.Attributes;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;
using Microsoft.EntityFrameworkCore;

#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

namespace Levelbuild.Entities.Features.Workflow;

/// <summary>
/// Entity for workflow entries
/// </summary>
[Slug(nameof(WorkflowNodeId), nameof(ReferenceElementId))]
public class WorkflowEntryEntity : PersistentEntity<WorkflowEntryEntity>, IAdministrationEntity<WorkflowEntryEntity, WorkflowEntryDto>
{
	#region Properties

	/// <summary>
	/// The id of the workflow node
	/// </summary>
	[Required]
	public Guid WorkflowNodeId { get; set; }

	/// <summary>
	/// The workflow node
	/// </summary>
	[ForeignKey(nameof(WorkflowNodeId))]
	public WorkflowNodeEntity? WorkflowNode { get; set; }

	/// <summary>
	/// The id of the reference definition
	/// </summary>
	[Required]
	public Guid ReferenceDataSourceId { get; set; }

	/// <summary>
	/// The referenced definition
	/// </summary>
	[ForeignKey(nameof(ReferenceDataSourceId))]
	public DataSourceEntity? ReferenceDataSource { get; set; }

	/// <summary>
	/// ElementId of the record where the work flow action was triggered
	/// </summary>
	public string ReferenceElementId { get; set; }

	/// <summary>
	/// Display value of the referenced record
	/// </summary>
	public string? ReferenceDisplayValue { get; set; }
	
	/// <summary>
	/// more detailed description of the entry
	/// </summary>
	public string? Details { get; set; }

	/// <summary>
	/// the date when the workflow is due
	/// </summary>
	public DateTime? DueDate { get; set; }

	/// <summary>
	/// Datetime the workflow was set to this state 
	/// </summary>
	public DateTime? ReceivedDate { get; set; }

	/// <summary>
	/// Datetime when the workflow state was changed
	/// </summary>
	public DateTime? ExecutionDate { get; set; }

	/// <summary>
	/// Id of the user that changed the workflow state
	/// </summary>
	public Guid? ExecutionUserId { get; set; }

	/// <summary>
	/// the user that changed the workflow state
	/// </summary>
	[SetNull]
	[ForeignKey(nameof(ExecutionUserId))]
	public UserEntity? ExecutionUser { get; set; }

	/// <summary>
	/// List of users associated with this workflow entry
	/// </summary>
	public ICollection<UserEntity> Recipients { get; set; } = new List<UserEntity>();

	#endregion

	#region Constructors

	/// <inheritdoc />
	public WorkflowEntryEntity()
	{
	}

	private WorkflowEntryEntity(WorkflowEntryDto dto, UserEntity? createdBy, CoreDatabaseContext? databaseContext)
	{
		ReferenceElementId = dto.ReferenceElementId ?? string.Empty;
		ReferenceDisplayValue = dto.ReferenceDisplayValue ?? string.Empty;
		DueDate = dto.DueDate;
		ReceivedDate = dto.ReceivedDate;
		ExecutionDate = dto.ExecutionDate;

		var referenceDataSource = databaseContext?.DataSources.FirstOrDefault(dataSource => dataSource.Id == dto.ReferenceDataSourceId);
		ReferenceDataSource = referenceDataSource ??
							  throw new ArgumentException($"Property 'ReferenceDataSource' in WorkflowEntry is not valid: {dto.ReferenceDataSourceId}");
		ReferenceDataSourceId = dto.ReferenceDataSourceId ?? Guid.Empty;

		var workflowNode = databaseContext?.WorkflowNodes.FirstOrDefault(node => node.Id == dto.WorkflowNodeId);
		WorkflowNode = workflowNode ?? throw new ArgumentException($"Property 'WorkflowNode' in WorkflowEntry is not valid: {dto.WorkflowNodeId}");
		WorkflowNodeId = dto.WorkflowNodeId ?? Guid.Empty;

		var executionUser = databaseContext?.Users.FirstOrDefault(user => user.Id == dto.ExecutionUserId);
		ExecutionUser = executionUser;
		ExecutionUserId = dto.ExecutionUserId == Guid.Empty ? null : dto.ExecutionUserId;
	}

	#endregion

	#region Methods

	/// <inheritdoc />
	public override void ConfigureModel(ModelBuilder modelBuilder, DatabaseProvider databaseProvider)
	{
		modelBuilder.Entity<WorkflowEntryEntity>()
			.HasMany(workflowEntry => workflowEntry.Recipients)
			.WithMany();

		modelBuilder.Entity<WorkflowEntryEntity>()
			.HasOne(workflowEntry => workflowEntry.ReferenceDataSource);
		
		modelBuilder.Entity<WorkflowEntryEntity>()
			.HasOne(workflowEntry => workflowEntry.WorkflowNode);
		
		modelBuilder.Entity<WorkflowEntryEntity>()
			.HasOne(workflowEntry => workflowEntry.ExecutionUser);
		
		modelBuilder.Entity<WorkflowEntryEntity>()
			.HasOne(workflowEntry => workflowEntry.ExecutionUser);
		
		base.ConfigureModel(modelBuilder, databaseProvider);
	}

	/// <inheritdoc />
	public WorkflowEntryDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		var dto = new WorkflowEntryDto(this, excludedProperties, handledObjects)
		{
			TaskName = WorkflowNode?.TaskName,
			Details = Details,
			ReferenceDisplayValue = ReferenceDisplayValue,
			ReferenceDataSourceName = ReferenceDataSource?.Name,
			ExecutionUserName = ExecutionUser?.DisplayName,
			DefaultDetailPageSlug = ReferenceDataSource?.DefaultDetailPage?.Slug,
			ReferenceIcon = ReferenceDataSource?.MainField?.FieldType switch
			{
				DataFieldType.DataField => ReferenceDataSource?.Icon,
				DataFieldType.LookupField => ReferenceDataSource?.MainField?.LookupSource?.Icon,
				DataFieldType.VirtualField => ReferenceDataSource?.MainField?.LookupSource?.Icon,
				_ => null
			}
		};

		return dto;
	}

	/// <inheritdoc />
	public static WorkflowEntryEntity FromDto(WorkflowEntryDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new WorkflowEntryEntity(entityInfo, createdBy, context);
	}

	/// <inheritdoc />
	public void UpdatePartial(WorkflowEntryDto dto, UserEntity? modifiedBy = null)
	{
		if(!string.IsNullOrEmpty(dto.ReferenceElementId) && dto.ReferenceElementId != ReferenceElementId)
			ReferenceElementId = dto.ReferenceElementId;
		if(!string.IsNullOrEmpty(dto.ReferenceDisplayValue) && dto.ReferenceDisplayValue != ReferenceDisplayValue)
			ReferenceDisplayValue = dto.ReferenceDisplayValue;
		if(dto.DueDate != null && dto.DueDate != DueDate)
			DueDate = dto.DueDate;
		if(dto.ReceivedDate != null && dto.ReceivedDate != ReceivedDate)
			ReceivedDate = dto.ReceivedDate;
		if(dto.ExecutionDate != null && dto.ExecutionDate != ExecutionDate)
			ExecutionDate = dto.ExecutionDate;
		if(dto.WorkflowNodeId != null && dto.WorkflowNodeId != WorkflowNodeId)
			WorkflowNodeId = dto.WorkflowNodeId.Value;
		if(dto.ExecutionUserId != null && dto.ExecutionUserId != ExecutionUserId)
			ExecutionUserId = dto.ExecutionUserId.Value;
		if(dto.ReferenceDataSourceId != null && dto.ReferenceDataSourceId != ReferenceDataSourceId)
			ReferenceDataSourceId = dto.ReferenceDataSourceId.Value;
	}
	
	

	#endregion
}