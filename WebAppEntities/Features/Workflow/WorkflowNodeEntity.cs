using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Levelbuild.Core.EntityInterface.Attributes;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Workflow;
using Levelbuild.Entities.Extensions;
using Levelbuild.Entities.Features.Customer;
using Levelbuild.Entities.Features.User;
using Levelbuild.Entities.Interfaces;
using Microsoft.EntityFrameworkCore;
using Serilog;

// ReSharper disable EntityFramework.ModelValidation.UnlimitedStringLength
#pragma warning disable CS8618, CS9264

namespace Levelbuild.Entities.Features.Workflow;

/// <summary>
/// Entity of a workflow status node
/// </summary>
public class WorkflowNodeEntity : PersistentEntity<WorkflowNodeEntity>, IAdministrationEntity<WorkflowNodeEntity, WorkflowNodeDto>, IStartupMigration
{
	#region Properties
	
	/// <summary>
	/// Identifier for browser url
	/// </summary>
	[Required]
	public string Slug { get; private set; }
	
	private string _name;

	/// <summary>
	/// Name of the node
	/// </summary>
	[Required]
	public string Name
	{
		get => _name;
		set
		{
			_name = value;
			Slug = value.ToSlug();
		}
	}
	
	/// <summary>
	/// The id of the parent workflow
	/// </summary>
	[Required]
	public Guid WorkflowId { get; set; }
	
	/// <summary>
	/// The parent workflow
	/// </summary>
	[ForeignKey(nameof(WorkflowId))]
	public WorkflowEntity? Workflow { get; set; }

	/// <summary>
	/// The icon
	/// </summary>
	[ShortString]
	public string? Icon { get; set; }
	
	/// <summary>
	/// the name of the task
	/// </summary>
	public string? TaskName { get; set; }
	
	/// <summary>
	/// A more detailed description of the task
	/// </summary>
	public string? Details { get; set; }
	
	/// <summary>
	/// The node's sorting
	/// </summary>
	public short Sorting { get; set; }

	/// <summary>
	/// The node's state
	/// </summary>
	public WorkflowNodeState State { get; set; } = WorkflowNodeState.InProgress;
	
	#endregion
	
	#region Constructors
	
	/// <inheritdoc />
	public WorkflowNodeEntity()
	{
	}
	
	private WorkflowNodeEntity(WorkflowNodeDto dto, UserEntity? createdBy, CoreDatabaseContext? databaseContext)
	{
		Name = dto.Name ?? throw new ArgumentException($"Property 'Name' in WorkflowNode is not valid: {dto.Name}");
		
		var workflow = databaseContext?.Workflows.FirstOrDefault(workflow => workflow.Id == dto.WorkflowId);
		Workflow = workflow ?? throw new ArgumentException($"Property 'Workflow' in WorkflowNode is not valid: {dto.WorkflowId}");
		WorkflowId = dto.WorkflowId!.Value;
		
		Sorting = dto.Sorting ?? 0;
		
		State = dto.State.GetValueOrDefault();
		
		if (dto.Icon == null)
		{
			Icon = State switch
			{
				WorkflowNodeState.Start => "rocket-launch",
				WorkflowNodeState.InProgress => "arrow-progress",
				WorkflowNodeState.Positive => "badge-check",
				WorkflowNodeState.Negative => "octagon-exclamation",
				_ => string.Empty
			};
		}
		else
		{
			Icon = dto.Icon;
		}
		
		TaskName = dto.TaskName;
		Details = dto.Details;
	}
	
	#endregion
	
	#region Methods
	
	/// <inheritdoc />
	public static WorkflowNodeEntity FromDto(WorkflowNodeDto entityInfo, UserEntity? createdBy = null, CoreDatabaseContext? context = null)
	{
		return new WorkflowNodeEntity(entityInfo, createdBy, context);
	}
	
	/// <inheritdoc />
	public WorkflowNodeDto ToDto(string[]? excludedProperties = null, List<string>? handledObjects = null)
	{
		var localizer = StringLocalizerFactory?.Create("Workflow", WorkflowId.ToString(), true);
		return new WorkflowNodeDto(this, excludedProperties, handledObjects)
		{
			NameTranslated = localizer?[Name] ?? string.Empty
		};
	}
	
	/// <inheritdoc />
	public void UpdatePartial(WorkflowNodeDto dto, UserEntity? modifiedBy = null)
	{
		if(!string.IsNullOrEmpty(dto.Name)) 
			Name = dto.Name;
		if(!string.IsNullOrEmpty(dto.TaskName)) 
			TaskName = dto.TaskName;
		if(!string.IsNullOrEmpty(dto.Details)) 
			Details = dto.Details;
		if(dto.Icon != null)
			Icon = dto.Icon;
		if(dto.State != null && dto.State != State)
			State = dto.State.Value;
		if(dto.Sorting != null && dto.Sorting != Sorting)
			Sorting = dto.Sorting.Value;
	}
	
	#endregion
	
	#region Migrations

	/// <inheritdoc />
	public static void RegisterMigrations(ref readonly Dictionary<string, Func<ILogger, CoreDatabaseContext, IServiceProvider, Task>> migrationRegistry)
	{
		migrationRegistry.Add(nameof(ResetCustomWorkflowNodeIcons), ResetCustomWorkflowNodeIcons);
	}

	private static async Task ResetCustomWorkflowNodeIcons(ILogger logger, CoreDatabaseContext databaseContext, IServiceProvider serviceProvider)
	{
		var existingNodes = await databaseContext.WorkflowNodes
									.ToListAsync();

		// Reset all custom icons to default icons to satisfy UI/UX feedback
		foreach (var node in existingNodes)
		{
			node.Icon = node.State switch
			{
				WorkflowNodeState.Start => "rocket-launch",
				WorkflowNodeState.InProgress => "arrow-progress",
				WorkflowNodeState.Positive => "badge-check",
				WorkflowNodeState.Negative => "octagon-exclamation",
				_ => string.Empty
			};
			await databaseContext.SaveChangesAsync();
		}
	}

	#endregion
}