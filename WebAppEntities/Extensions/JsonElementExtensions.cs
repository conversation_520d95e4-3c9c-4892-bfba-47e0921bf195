using System.Text.Json;

namespace Levelbuild.Entities.Extensions;

/// <summary>
/// Extension class for <see cref="JsonElement"/>.
/// </summary>
public static class JsonElementExtensions
{
	/// <summary>
	/// Retrieves the real value of the element based on its real type.
	/// </summary>
	/// <param name="jsonElement"></param>
	/// <returns></returns>
	/// <exception cref="Exception"></exception>
	public static object? GetRealValue(this JsonElement jsonElement)
	{
		if (!TryGetRealValue(jsonElement, out var result))
			throw new Exception($"Cannot get value for element {jsonElement}");

		return result;
	}
	
	/// <summary>
	/// Tries to retrieve the real value of the element based on its real type.
	/// </summary>
	/// <param name="jsonElement"></param>
	/// <param name="value"></param>
	/// <returns></returns>
	public static bool TryGetRealValue(this JsonElement jsonElement, out object? value)
	{
		try
		{
			value = jsonElement.ValueKind switch
			{
				JsonValueKind.Null => null,
				JsonValueKind.Number => jsonElement.GetDouble(),
				JsonValueKind.False => false,
				JsonValueKind.True => true,
				JsonValueKind.Undefined => null,
				JsonValueKind.String => jsonElement.GetString(),
				JsonValueKind.Object => jsonElement,
				JsonValueKind.Array => jsonElement.EnumerateArray().Select(o => o.GetRealValue()).ToArray(),
				_ => null
			};
			
			return true;
		}
		catch (Exception)
		{
			value = null;
			return false;
		}
	}
}