using System.Security.Cryptography;
using Levelbuild.Entities.Helpers;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.Helpers;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class CryptoHelperTests
{
	[Theory]
	[InlineData("IAmATestString")]
	[InlineData("kmUla2TIzpsOlDoSMelUsKzFs0h__OZ3bMIeUm7MEWSjOva9fOPpuuEoa9TmdR7GKGMJIiE")]
	[InlineData("Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Ste")]
	public void TestDecrypt(string stringToEncrypt)
	{
		var encryptionKey = Convert.ToBase64String(RandomNumberGenerator.GetBytes(32));
		var salt = CryptoHelper.GenerateSalt();
		
		var encryptedString = CryptoHelper.Encrypt(stringToEncrypt, encryptionKey, salt);
		Assert.NotEqual(stringToEncrypt, encryptedString);
		
		var decryptedString = CryptoHelper.Decrypt(encryptedString, encryptionKey, salt);
		Assert.Equal(stringToEncrypt, decryptedString);
	}
}