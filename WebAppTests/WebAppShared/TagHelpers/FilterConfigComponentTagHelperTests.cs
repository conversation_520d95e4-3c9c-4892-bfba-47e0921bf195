using Levelbuild.Frontend.WebApp.Shared.TagHelpers;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.TagHelpers;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class FilterConfigComponentTagHelperTests : TagHelperTests
{
	private TagHelperContext _context;

	private TagHelperOutput _output;

	private readonly TagHelper _tagHelper;

	public FilterConfigComponentTagHelperTests()
	{
		CreateTagContextAndOutput(out _context, out _output);
		_tagHelper = new FilterConfigComponentTagHelper();
	}

	[Trait("Category", "FilterConfigComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes to TagHelper Output")]
	public void WriteAttributesToFilterConfig()
	{
		List<TagHelperTestParameter> arguments =
		[
			TagHelperTestParameter.GetInstance("Url", "/some/base/url"),
			TagHelperTestParameter.GetInstance("CreateUrl", "/some/create/url"),
			TagHelperTestParameter.GetInstance("FieldUrl", "/some/field/url"),
			TagHelperTestParameter.GetInstance("ParentElementKey", "DataFieldId"),
			TagHelperTestParameter.GetInstance("ParentElementValue", "1234")
		];

		WriteAttributesToTagHelper(_tagHelper, arguments);
		ProcessAndEvalTagHelper(_tagHelper, arguments, ref _context, ref _output);

		Assert.Equal("lvl-filter-config", _output.TagName);
	}

	[Trait("Category", "FilterConfigComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes from context to TagHelper output")]
	public void TestWriteExtraAttributesFromContext()
	{
		TestExtraAttributesFromContext(_tagHelper);
	}
}