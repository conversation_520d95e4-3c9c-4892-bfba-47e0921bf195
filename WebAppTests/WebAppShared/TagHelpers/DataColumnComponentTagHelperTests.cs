using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Frontend.WebApp.Shared;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.TagHelpers;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class DataColumnComponentTagHelperTests : TagHelperTests
{
	private TagHelperContext _context;
	
	private TagHelperOutput _output;
	
	private readonly TagHelper _tagHelper;
	
	public DataColumnComponentTagHelperTests()
	{
		CreateTagContextAndOutput(out _context, out _output);
		_tagHelper = new DataColumnComponentTagHelper();
	}
	
	[Trait("Category", "DataColumnComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes to TagHelper Output")]
	public void TestWriteAttributesToDataColumn()
	{
		List<DataColumnValueDefinition> values = [new("foo"), new("bar", "bares")];
		List<TagHelperTestParameter> arguments = new()
		{
			TagHelperTestParameter.GetInstance("Position", 9),
			TagHelperTestParameter.GetInstance("Name", "GoodName"),
			TagHelperTestParameter.GetInstance("Label", "The best Label"),
			TagHelperTestParameter.GetInstance("MinWidth", 100),
			TagHelperTestParameter.GetInstance("MaxWidth", 420),
			TagHelperTestParameter.GetInstance("DecimalPlaces", 4),
			TagHelperTestParameter.GetInstance("Sign", "€"),
			TagHelperTestParameter.GetInstance("Type", InputDataType.String, InputDataType.String.GetTypeAsString()),
			TagHelperTestParameter.GetInstance("TextAlign", Alignment.Center, Alignment.Center.GetAlignmentAsString()),
			TagHelperTestParameter.GetInstance("Hidden", true),
			TagHelperTestParameter.GetInstance("LiveEditable", true),
			TagHelperTestParameter.GetInstance("UseThousandSeparator", true),
			TagHelperTestParameter.GetAlternativeInstance("ReferenceField", "ref", "type"),
			TagHelperTestParameter.GetInstance("Values", values, JsonSerializer.Serialize(values, ConfigHelper.DefaultJsonOptions)),
		};
		
		WriteAttributesToTagHelper(_tagHelper, arguments);
		ProcessAndEvalTagHelper(_tagHelper, arguments, ref _context, ref _output);
		
		Assert.Equal("lvl-data-column", _output.TagName);
	}
	
	[Trait("Category", "DataColumnComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes from context to TagHelper output")]
	public void TestWriteExtraAttributesFromContext()
	{
		TestExtraAttributesFromContext(_tagHelper);
	}
}