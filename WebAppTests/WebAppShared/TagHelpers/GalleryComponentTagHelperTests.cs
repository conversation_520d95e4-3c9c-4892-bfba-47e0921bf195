using System.Text.Json;
using Levelbuild.Frontend.WebApp.Shared;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.TagHelpers;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class GalleryComponentTagHelperTests : TagHelperTests
{
	private TagHelperContext _context;
	
	private TagHelperOutput _output;
	
	private readonly TagHelper _tagHelper;
	
	public GalleryComponentTagHelperTests()
	{
		CreateTagContextAndOutput(out _context, out _output);
		_tagHelper = new GalleryComponentTagHelper();
	}
	
	[Trait("Category", "GalleryComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes to TagHelper Output")]
	public void WriteAttributesToInput()
	{
		var rows = new List<Dictionary<string, object>>
		{
			new()
			{
				{ "ID", "abcd-1234" },
				{ "Number", 1234 },
				{ "Name", "<PERSON>" },
			},
			new()
			{
				{ "ID", "vwxz-7890" },
				{ "Number", 1597 },
				{ "Name", "Boris" },
			},
			new()
			{
				{ "ID", "azyb-1057" },
				{ "Number", 7842 },
				{ "Name", "Linda" },
			}
		};
		List<TagHelperTestParameter> arguments = [
			TagHelperTestParameter.GetInstance("Rows", rows, JsonSerializer.Serialize(rows, ConfigHelper.JsonOptionsCamel)),
			TagHelperTestParameter.GetInstance("IdentityColumn", "id"),
			TagHelperTestParameter.GetInstance("Skeleton", true),
			TagHelperTestParameter.GetInstance("Embedded", true),
			TagHelperTestParameter.GetInstance("AllowCreate", true),
			TagHelperTestParameter.GetInstance("Clickable", true),
			TagHelperTestParameter.GetInstance("Selectable", true),
			TagHelperTestParameter.GetInstance("AllowFavorite", true),
			TagHelperTestParameter.GetInstance("AllowInactive", true),
			TagHelperTestParameter.GetInstance("DisplayWorkflow", true),
			TagHelperTestParameter.GetInstance("Readonly", true),
		];
		
		WriteAttributesToTagHelper(_tagHelper, arguments);
		ProcessAndEvalTagHelper(_tagHelper, arguments, ref _context, ref _output);
		
		Assert.Equal("lvl-gallery", _output.TagName);
	}
	
	[Trait("Category", "GalleryComponentTagHelperTests")]
	[Fact(DisplayName = "Converting attributes from context to TagHelper output")]
	public void TestWriteExtraAttributesFromContext()
	{
		TestExtraAttributesFromContext(_tagHelper);
	}
}