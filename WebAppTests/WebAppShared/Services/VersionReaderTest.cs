using Levelbuild.Frontend.WebApp.Shared.Services;

namespace Levelbuild.Domain.WebAppTests.WebAppShared.Services;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class VersionReaderTest
{
	private VersionReader _versionReader = new();
	
	[Fact(DisplayName = "Read valid version")]
	public void TestReadValidVersion()
	{
		_versionReader.Path = "validTestVersion.json";
		var version = _versionReader.GetVersion();
		var semanticVersion = _versionReader.GetSemanticVersion();
		
		Assert.Equal(new Version(0, 1, 8, 1801), version);
		Assert.Equal("0.1.8-unstable+1801", semanticVersion);
	}
	
	[Fact(DisplayName = "Read invalid version")]
	public void TestReadInvalidVersion()
	{
		_versionReader.Path = "invalidTestVersion.json";
		Assert.Throws<KeyNotFoundException>(() => _versionReader.GetVersion());
		Assert.Throws<KeyNotFoundException>(() => _versionReader.GetSemanticVersion());
	}
	
	[Fact(DisplayName = "Read non existent version")]
	public void TestReadMissingVersion()
	{
		_versionReader.Path = "bla/bla.json";
		Assert.Throws<DirectoryNotFoundException>(() => _versionReader.GetVersion());
		Assert.Throws<DirectoryNotFoundException>(() => _versionReader.GetSemanticVersion());
	}
}