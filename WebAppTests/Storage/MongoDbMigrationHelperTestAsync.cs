using Levelbuild.Domain.Storage.Helper;
using Microsoft.Extensions.Configuration;
using MongoDB.Bson;
using MongoDB.Driver;

namespace Levelbuild.Domain.WebAppTests.Storage;

[ExcludeFromCodeCoverage]
public class MongoDbMigrationHelperTestAsync : IDisposable
{
	private string _randomDatabaseName;
	private string _randomTableName;
	private IConfiguration _config;
	private string _mongoDbConnectionString;
	private MongoClient? _mongoClient;

	public MongoDbMigrationHelperTestAsync()
	{
		_randomDatabaseName = "MongoDbMigrationHelperTest_DataBase";
		_randomTableName = "MongoDbMigrationHelperTest_Collection";

		_config = new ConfigurationBuilder()
			.SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
			.AddJsonFile("testsettings.json")
			.Build()
			.GetSection("WebApp");

		_mongoDbConnectionString = _config.GetSection("Storage")
			.GetSection("CustomerContext")
			.GetSection("ConnectionStrings")["MongoDbConnectionString"];

		_mongoClient = new MongoClient(MongoClientSettings.FromUrl(new MongoUrl(_mongoDbConnectionString)));

		_mongoClient.DropDatabase(_randomDatabaseName);

		_mongoClient.GetDatabase(_randomDatabaseName);
	}

	[Trait("Category", "Revision Tests")]
	[Fact(DisplayName = "Check if Index exists or create index")]
	public async Task CheckIndexOrCreateTest()
	{
		int timeoutInSeconds = 5;
		
		var indexes = new List<string>();
		indexes.Add("_id"); // Autogenerated
		indexes.Add("RevElementId_Asc_Revision_Desc");


		// Create Collection
		var collection = _mongoClient.GetDatabase(_randomDatabaseName).GetCollection<BsonDocument>(_randomTableName);

		var existingIndexesBegin = collection.Indexes.List().ToList();
		var startTime = DateTime.UtcNow;
		// Waiting for asyncron task to finish creating
		while (existingIndexesBegin.Count != 0 && DateTime.UtcNow.Subtract(startTime).TotalSeconds <= timeoutInSeconds)
		{
			Thread.Sleep(10);
			existingIndexesBegin = collection.Indexes.List().ToList();
		}
		Assert.True(existingIndexesBegin.Count == 0);

		
		// First Run to create Indexes -> Check for count Index Count
		await MongoDbMigrationHelper.CheckIndexOrCreate(_mongoClient.GetDatabase(_randomDatabaseName), _randomTableName);

		var existingIndexesCurrent = collection.Indexes.List().ToList();
		startTime = DateTime.UtcNow;
		while (existingIndexesCurrent.Count != indexes.Count && DateTime.UtcNow.Subtract(startTime).TotalSeconds <= timeoutInSeconds)
		{
			Thread.Sleep(10);
			existingIndexesCurrent = collection.Indexes.List().ToList();
		}
		Assert.True(existingIndexesCurrent.Count == indexes.Count);
		
		
		// Second Run to create Indexes -> Check for count Index Count >> do we have a issue if we create new indexes?
		await MongoDbMigrationHelper.CheckIndexOrCreate(_mongoClient.GetDatabase(_randomDatabaseName), _randomTableName); 

		existingIndexesCurrent = collection.Indexes.List().ToList();
		startTime = DateTime.UtcNow;
		while (existingIndexesCurrent.Count != indexes.Count && DateTime.UtcNow.Subtract(startTime).TotalSeconds <= timeoutInSeconds)
		{
			Thread.Sleep(10);
			existingIndexesCurrent = collection.Indexes.List().ToList();
		}
		Assert.True(existingIndexesCurrent.Count == indexes.Count);
	}

	public void Dispose()
	{
		// TODO release managed resources here
		_mongoClient.GetDatabase(_randomDatabaseName).GetCollection<BsonDocument>(_randomTableName).Indexes.DropAll();
		_mongoClient.DropDatabase(_randomDatabaseName);
	}
}