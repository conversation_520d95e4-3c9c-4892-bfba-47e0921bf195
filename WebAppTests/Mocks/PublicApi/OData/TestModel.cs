using Levelbuild.Core.DataStoreInterface.Dto;
using Microsoft.OData.Edm;
#pragma warning disable CS8618, CS9264

namespace Levelbuild.Domain.WebAppTests.Mocks.PublicApi.OData;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

/// <summary>
/// TestModel for OData tests
/// </summary>
public class TestModel
{
	#region Properties

	public EdmModel EdmModel { get; private init; }
	
	// ReSharper disable once CollectionNeverQueried.Global
	public List<DataStoreQueryField> Fields { get; private init; }

	#endregion
	
	#region Attributes

	private const string Namespace = "TestModel";
	
	private EdmEntityContainer _defaultContainer;
	
	private EdmEntityType _employee;
	
	#endregion

	#region Init

	/// <summary>
	/// Constructor.
	/// </summary>
	public TestModel()
	{
		EdmModel = new EdmModel();
		Fields = new List<DataStoreQueryField>();
		
		InitEdmModel();
		InitFields();
	}

	private void InitFields()
	{
		foreach (var property in _employee.DeclaredProperties)
		{
			Fields.Add(new(property.Name));
		}
	}
	
	private void InitEdmModel()
	{
		_employee = new EdmEntityType(Namespace, "Employee");
		
		_employee.AddKeys(_employee.AddStructuralProperty("Id", EdmPrimitiveTypeKind.Guid, false));
		_employee.AddStructuralProperty("FirstName", EdmPrimitiveTypeKind.String, false);
		_employee.AddStructuralProperty("LastName", EdmPrimitiveTypeKind.String, false);
		_employee.AddStructuralProperty("Age", EdmPrimitiveTypeKind.Int32, false);
		_employee.AddStructuralProperty("Birthday", EdmPrimitiveTypeKind.Date, false);
		_employee.AddStructuralProperty("Salary", EdmPrimitiveTypeKind.Double, false);
		_employee.AddStructuralProperty("IsActive", EdmPrimitiveTypeKind.Boolean, false);
		
		EdmModel.AddElement(_employee);
		
		_defaultContainer = new(Namespace, "DefaultContainer");
		EdmModel.AddElement(_defaultContainer);
		
		_defaultContainer.AddEntitySet("Employees", _employee);
	}

	#endregion
}