using Google.Protobuf.WellKnownTypes;
using Levelbuild.Core.ZitadelApiInterface;
using Zitadel.User.V2;

namespace Levelbuild.Domain.WebAppTests.Mocks.ZitadelApi;

public class MockZitadelUserServiceApiClient : IZitadelUserServiceApiClient
{
	public PasskeyRegistrationCode CreatePasskeyRegistrationCode(string userId)
	{
		return new PasskeyRegistrationCode()
		{
			Id = Guid.NewGuid().ToString(),
			Code = "123456",
		};
	}
	
	public (string PasskeyId, Struct Credentials) GetPasskeyRegistrationCredentials(string userId, PasskeyRegistrationCode code, string domain)
	{
		return (Guid.NewGuid().ToString(), new Struct());
	}
	
	public (bool Success, string? Message) VerifyPasskeyRegistration(string userId, string passkeyId, string passkeyName, Struct publicKeyCredential)
	{
		throw new NotImplementedException();
	}

	public IList<Passkey> ListPasskeys(string userId)
	{
		throw new NotImplementedException();
	}

	public void RemovePasskey(string userId, string passkeyId)
	{
		throw new NotImplementedException();
	}
}