using Levelbuild.Core.DataStoreInterface;
using Levelbuild.Entities.Features.LoggerConfig;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Serilog;
using Serilog.Events;
using Xunit.Abstractions;

namespace Levelbuild.Domain.WebAppTests.Mocks.LoggerConfig;

/// <summary>
/// Mock for <see cref="ILogManager"/>
/// </summary>
internal sealed class MockLogManager : ILogManager
{
	private readonly ILogger _logger;

	/// <summary>
	/// Constructor.
	/// </summary>
	/// <param name="output"></param>
	public MockLogManager(ITestOutputHelper output)
	{
		_logger = new TestLogger(output);
	}
	
	/// <inheritdoc />
	public ILogger GetLoggerForClass<T>(LogEventLevel? defaultLevel = null, string? groupName = null)
	{
		return _logger;
	}

	public ILogger GetLoggerForClass(Type type, LogEventLevel? defaultLevel = null, string? groupName = null)
	{
		return _logger;
	}

	/// <inheritdoc />
	public ILogger GetLoggerForDataStore<T>(LogEventLevel? defaultLevel = null, string? groupName = null) where T : IDataStore
	{
		return _logger;
	}
	
	/// <inheritdoc />
	public ILogger GetLoggerForDataStore(Type type, LogEventLevel? defaultLevel = null, string? groupName = null)
	{
		return _logger;
	}
	
	/// <inheritdoc />
	public LogEventLevel? GetLogLevel(string className)
	{
		return null;
	}
	
	/// <inheritdoc />
	public void UpdateLogger(LoggerConfigEntity loggerConfigEntity)
	{
		// nothing
	}
}