import { defineConfig } from 'cypress'

import webAppConfig from './webapp.config'
import * as fs from 'fs'

export default defineConfig({
	defaultCommandTimeout: 10000,
	watchForFileChanges: false,
	e2e: {
		baseUrl: webAppConfig.url,
		viewportWidth: 1920,
		viewportHeight: 1000,
		experimentalOriginDependencies: true,
		experimentalMemoryManagement: true,
		video: webAppConfig.videoRecording,
		numTestsKeptInMemory: webAppConfig.numTestsKeptInMemory,
		setupNodeEvents(on) {
			// implement node event listeners here
			on('before:browser:launch', (browser, launchOptions) => {
				if (browser.family === 'chromium' && browser.name !== 'electron') {
					launchOptions.preferences.default.intl = { accept_languages: 'en' }

					if (browser.isHeadless) {
						launchOptions.args.push("--no-sandbox");
						launchOptions.args.push("--disable-gl-drawing-for-tests");
						launchOptions.args.push("--disable-gpu");
					}

					launchOptions.args.push("--disable-dev-shm-usage");
					launchOptions.args.push("--js-flags=--max-old-space-size=3500")

					return launchOptions
				}
			})

			on('after:spec', (spec: Cypress.Spec, results: CypressCommandLine.RunResult) => {
				if (results && results.video) {
					// Do we have failures for any retry attempts?
					const failures = results.tests.some((test) =>
						test.attempts.some((attempt) => attempt.state === 'failed'),
					)
					if (!failures) {
						// delete the video if the spec passed and no tests retried
						fs.unlinkSync(results.video)
					}
				}
			})
		},
	},
})
