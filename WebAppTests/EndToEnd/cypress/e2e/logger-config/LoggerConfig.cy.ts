describe('Logger Configuration', () => {
	it('navigates to logger config entry point', () => {
		// go to LoggerConfig via Home
		cy.visit('/')
		cy.get('lvl-nav-button').as('navButton').click()
		cy.wait(150)
		cy.get('@navButton').shadow().find('#menu-container a[href="/Admin/LoggerConfigs"]').as('navLink').should('be.visible')
		cy.get('@navLink').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/LoggerConfigs'))
		})
	})

	it('CRUDs a logger config item', () => {
		// remove example if exists
		removeLoggerConfigByName('Example')

		// remove renamed example if exists
		removeLoggerConfigByName('Renamed Example')

		createConfigItem()
		cy.wait(100)
		updateConfigItem()
		cy.wait(200)
		deleteConfigItem()
	})

	it('try to open logger config without authorization/authentication', () => {
		cy.visitWithoutUser('/Admin/LoggerConfigs')
		cy.visitWithoutAdminRights('/Admin/LoggerConfigs')
	})

	it('some additional navigation tests', () => {
		// open list view
		cy.visit('/Admin/LoggerConfigs')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/LoggerConfigs'))
		})
		cy.title().should('contain', 'Logging')

		// open create mask
		cy.get('[data-action=add]').click({ force: true })
		cy.url().then(url => {
			expect(url.endsWith('/Admin/LoggerConfigs/Create'))
		})
		cy.title().should('contain', 'New')

		// close create mask via abort button
		cy.get('.side-panel > [data-action=cancel]').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/LoggerConfigs'))
		})
		cy.title().should('contain', 'Logging')

		// open create mask again and reload page
		cy.get('[data-action=add]').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/LoggerConfigs/Create'))
		})
		cy.title().should('contain', 'New')
		cy.reload()

		cy.get('.side-panel').shadow().find('#slider').should('have.attr', 'open')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/LoggerConfigs/Create'))
		})

		// close create mask again
		cy.get('.side-panel > [data-action=cancel]').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/LoggerConfigs'))
		})
		cy.title().should('contain', 'Logging')

		// create example
		createExampleViaRest()
		cy.reload()

		// open entry
		cy.get('lvl-list').shadow().find('lvl-list-line:not([skeleton])').contains("Example").as('row').should('exist')
		cy.get('@row').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/LoggerConfigs/example'))
		})

		cy.get('#logger-config-form[initdone]').should('exist')
		// refresh entry
		cy.reload()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/LoggerConfigs/example'))

			cy.get('#logger-config-form[initdone]').should('exist') // form should be visible before we check the page title
		})
		cy.title().should('contain', 'Example')
		

		// back button should bring us back to the list view
		cy.go('back')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/LoggerConfigs'))
		})
		cy.title().should('contain', 'Logging')
		cy.get('lvl-list').should('exist')

		// refresh should keep us at the list view
		cy.reload()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/LoggerConfigs'))
		})
		cy.title().should('contain', 'Logging')

		// use breadcrumb navigation
		cy.visit('/Admin/LoggerConfigs/Example')
		cy.get('lvl-breadcrumb')
			.shadow()
			.find('#breadcrumbs').as('breadcrumbs')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/LoggerConfigs/example'))
		})

		cy.get('@breadcrumbs').contains('Logging').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/LoggerConfigs/example'))
		})
		cy.get('@breadcrumbs')
			.find('li')
			.should('have.length', 2)

		cy.get('@breadcrumbs')
			.find('#home').click()
		cy.title().should('contain', 'Welcome')
		cy.get('@breadcrumbs')
			.find('li')
			.should('have.length', 1)

		// remove example
		removeLoggerConfigByName('Example')
	})

	function createConfigItem() {
		// open list view
		cy.visit('/Admin/LoggerConfigs')

		// if example item already exists, remove it before starting the test run!
		cy.request({ url: '/Admin/LoggerConfigs/example', failOnStatusCode: false }).then((resp) => {
			if (resp.status == 200) {
				cy.visit('/Admin/LoggerConfigs/example')
				cy.get('#content-buttons [data-action=delete]:not([hidden]):not([skeleton])').click({ force: true })
			}
		})

		// open create panel
		cy.get('[data-action=add]').click()
		cy.get('#logger-config-form').as('form').should('be.visible')

		// url should have changed to /Admin/LoggerConfigs/Create
		cy.url().then(url => {
			expect(url.endsWith('/Admin/LoggerConfigs/Create'))
		})

		// input form data
		cy.get('@form')
			.find('#logger-config-level').shadow().as('type')
			.find('input').focus().clear().type('Warning')
		cy.get('@type').find('.dropdown-content .content-container tbody tr:first-child').click()
		cy.get('@form')
			.find('#logger-config-is-active')
			.click({ force: true })
		cy.get('@form')
			.find('#logger-config-is-active')
			.click({ force: true })

		cy.wait(100)

		// submit
		cy.get('.side-panel > [data-action=save]')
			.click()

		// warning should be visible that there are empty required fields
		cy.get('lvl-toaster').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini').should('exist')
		cy.get('lvl-toaster').shadow().find('lvl-toast-mini[open]').as('toast').should('exist')
		cy.get('@toast').shadow().find('.toast__content').should('contain.text', 'required')

		cy.get('@form')
			.find('#logger-config-logger-source').shadow()
			.find('input').as('nameInput').focus().clear()
			.type('Example')

		cy.get('@form')
			.find('#logger-config-log-file-path').shadow()
			.find('input').as('filePathInput').focus().clear()
			.type('ExampleFilePath')

		// submit -> CREATE
		cy.get('.side-panel > [data-action=save]')
			.click()

		// dialog should be closed
		cy.get('.side-panel').shadow().find('#slider').should('not.have.attr', 'open')

		// check reset of the form
		cy.get('[data-action=add]').click()
		cy.get('#logger-config-logger-source').shadow().find('input').should('be.empty')
		cy.get('#logger-config-level').shadow().find('input').should('be.empty')
		cy.get('#logger-config-is-active').shadow().find('input').should('have.not.attr', 'value', 'true')
		cy.get('#logger-config-log-to-file').shadow().find('input').should('have.not.attr', 'value', 'true')
		cy.get('#logger-config-log-file-path').shadow().find('input').should('be.empty')
	}

	function updateConfigItem() {
		// list view -> click on first logger config
		cy.visit('/Admin/LoggerConfigs/')

		// edit view and return with back button
		cy.get('lvl-list').shadow().find('lvl-list-line:not([skeleton]):first-child').as('row')
			.find('[data-name=loggerSource]').then((cell) => {
			let loggerName = cell[0].innerText
			cy.get('@row').click()
			cy.url().then(url => {
				expect(url.endsWith('/Admin/LoggerConfigs/' + loggerName))
			})
		})
		cy.go('back')
		cy.url().then(url => {
			expect(url.endsWith('/Admin/LoggerConfigs'))
		})

		cy.wait(50)

		// edit example view -> change input and save
		cy.visit('/Admin/LoggerConfigs/example')
		cy.get('#logger-config-logger-source').shadow()
			.find('input').focus().clear()
			.type('Renamed Example')
		cy.wait(500)
		cy.get('#content-buttons [data-action=save]:not([hidden]):not([skeleton])').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/LoggerConfigs'))
		})
		cy.title().should('contain', 'Logging')

		// item should be accessible
		cy.request({ url: '/Admin/LoggerConfigs/renamed-example', failOnStatusCode: false }).then((resp) => {
			expect(resp.status, 'StatusCode should be 200').to.equal(200)
		})

		// change to origin
		cy.visit('/Admin/LoggerConfigs/renamed-example')
		cy.get('#logger-config-logger-source').shadow()
			.find('input').focus().clear()
			.type('Example')
		cy.get('#content-buttons [data-action=save]:not([hidden]):not([skeleton])').click()
	}

	function deleteConfigItem() {
		// edit sample data and delete item
		cy.visit('/Admin/LoggerConfigs/example')
		cy.get('#content-buttons [data-action=delete]:not([hidden]):not([skeleton])').click({ force: true })
		cy.url().then(url => {
			expect(url.endsWith('/Admin/LoggerConfigs'))
		})

		// item should not be accessible
		cy.request({ url: '/Api/LoggerConfigs/example', failOnStatusCode: false }).then((resp) => {
			expect(resp.status, 'StatusCode should be 404').to.equal(404)
		})
	}


	function removeLoggerConfigByName(name: string) {
		cy.request({ url: '/Api/LoggerConfigs/', failOnStatusCode: false, method: 'GET' }).then((resp) => {
			expect(resp.status).to.equal(200, 'Get temporary logger config: Example')
			const loggerConfig = resp.body.data.rows.find((row: Record<string, any>) => row.loggerSource === name)
			if (loggerConfig != null) {
				cy.request({ url: '/Api/LoggerConfigs/' + loggerConfig.id, failOnStatusCode: false, method: 'DELETE' }).then((resp) => {
					expect(resp.status).to.equal(200, 'Delete temporary logger config: Example')
				})
			}
		})
	}

	function createExampleViaRest() {
		cy.request({ url: '/Api/LoggerConfigs/', failOnStatusCode: false, method: 'POST', body: { loggerSource: 'Example', level: 'warning' } }).then((resp) => {
			expect(resp.status === 200 || resp.status === 400, 'Create temporary logger config: Example').to.be.true
		})
		cy.wait(50)
	}
})