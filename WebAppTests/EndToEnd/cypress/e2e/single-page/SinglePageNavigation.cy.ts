describe('Single Page Navigation ', () => {
	const uuid = () => Cypress._.random(0, 1e6)
	let numberOfEntries = 20
	let guid = 0

	beforeEach(() => {
		guid = uuid()

		// fetch Storage connection
		cy.createDataStore(guid, true).as('dataStore')
		cy.createDataSource('@dataStore', 'DataSource_' + guid).as('dataSource')
		cy.createField('@dataSource', 'WordValue', 'String', { length: 255 }).as('stringField')
		cy.createField('@dataSource', 'ToggleValue', 'Boolean', {}).as('booleanField')
		cy.createPage('@dataSource', 'TestEditPage', 'SingleData', { breadcrumbLabel: '##WordValue##' }).as('detailPage')
		cy.createPageHeader('@detailPage', 'header1', 'icons', '##WordValue##')
		cy.createPageView('@detailPage', 'TestForm', { type: 'Grid' }).as('singleDataForm')
		cy.setDefaultView('@detailPage', '@singleDataForm')
		cy.addSection('@singleDataForm', 0, 0, 'Section Form').as('section')
		cy.addInputField('@section', '@stringField', 'String', 1, 2, 1, 25)
		cy.addInputField('@section', '@booleanField', 'Boolean', 2, 3, 1, 25)
		cy.get<Record<string, any>>('@detailPage').then(detailPage => {
			cy.createPage('@dataSource', 'TestPage', 'MultiData', { detailPageId: detailPage.id }).as('multiPage')
		})
		cy.createPageView('@multiPage', 'TestView', { type: 'List', display: true }).as('listView')

		cy.get<Record<string, any>>('@listView').then(listView => {
			cy.forEachField('@dataSource', true, (field) => {
				cy.createColumn(listView.id, field.id)
			})
		})

		for (let i = 0; i <= numberOfEntries; i++) {
			cy.createRecord('@dataSource', {
				values: {
					WordValue: `word_${i.toString().padStart(3, '0')}`,
					ToggleValue: i % 2 == 0,
				},
				groups: [ 'testgroup' ],
			})
		}
	})

	it('opens the list page view, enters different records and tests the navigation between them', () => {
		cy.visit('/Public/Pages/testpage')
		cy.get('.page-view')
			.should('have.attr', 'data-view-type', 'List').as('listViewSection')
			.find('> *').should('exist')

		cy.get('@listViewSection').find('lvl-table').shadow().find('.table__head').as('header')
		cy.get('@header').find('.table__cell[data-dropdown="column-WordValue-dropdown"]').click()
		cy.get('@header').find('lvl-dropdown[data-column="WordValue"]').find('lvl-menu-item[sorting="asc"]').click({ force: true })

		// Test navigation at the start of the list
		cy.get('@listViewSection').find('lvl-table').shadow().find('.table__body').find('lvl-table-row[data-position=0]:not([skeleton])').should('exist')

		cy.get('@listViewSection').find('lvl-table').shadow().find('.table__body').find('lvl-table-row[data-position=0]').as('row0')
		cy.get('@row0').click()
		waitForReadyPage('SinglePage')

		cy.get('lvl-button#goto-previous.navigation-button', { timeout: 10000 }).as('prevButton').should('exist')
		cy.get('lvl-button#goto-next.navigation-button').as('nextButton').should('exist')
		cy.get('lvl-breadcrumb').shadow().find('li.breadcrumb__item').last().find('.breadcrumb__label').as('breadcrumbLabel').should('exist')

		// switch to TestForm
		cy.get('lvl-side-nav-item[label="[TestForm]"]').click({ force: true })

		cy.get<Record<string, any>>('@singleDataForm').then(pageView => {
			cy.get(`lvl-form#edit-form-${pageView.id}`, { timeout: 10000 }).as('form').should('exist')
		})
		cy.get('@form').find('lvl-input[name=WordValue]').as('stringInput').should('exist')
		cy.get('@form').find('lvl-toggle[name=ToggleValue]').as('booleanInput').should('exist')

		cy.get('@prevButton').should('have.attr', 'disabled')
		cy.get('@nextButton').should('not.have.attr', 'disabled')
		cy.get('@breadcrumbLabel').contains('word_000')
		cy.get('@stringInput').should('have.attr', 'value', 'word_000')
		cy.get('@booleanInput').should('have.attr', 'value', 'true')

		cy.get('@nextButton').should('not.have.prop', 'loading', true)
		cy.get('@nextButton').click()
		cy.get('@stringInput').should('not.have.attr', 'value', 'word_000')

		cy.get('@prevButton').should('not.have.attr', 'disabled')
		cy.get('@nextButton').should('not.have.attr', 'disabled')
		cy.get('@breadcrumbLabel').contains('word_001')
		cy.get('@stringInput').should('have.attr', 'value', 'word_001')
		cy.get('@booleanInput').should('have.attr', 'value', 'false')

		cy.get('@nextButton').should('not.have.prop', 'loading', true)
		cy.get('@nextButton').click()
		cy.get('@stringInput').should('not.have.attr', 'value', 'word_001')

		cy.get('@prevButton').should('not.have.attr', 'disabled')
		cy.get('@nextButton').should('not.have.attr', 'disabled')
		cy.get('@breadcrumbLabel').contains('word_002')
		cy.get('@stringInput').should('have.attr', 'value', 'word_002')
		cy.get('@booleanInput').should('have.attr', 'value', 'true')

		cy.get('@prevButton').should('not.have.prop', 'loading', true)
		cy.get('@prevButton').click()
		cy.get('@stringInput').should('not.have.attr', 'value', 'word_002')

		cy.get('@prevButton').should('not.have.attr', 'disabled')
		cy.get('@nextButton').should('not.have.attr', 'disabled')
		cy.get('@breadcrumbLabel').contains('word_001')
		cy.get('@stringInput').should('have.attr', 'value', 'word_001')
		cy.get('@booleanInput').should('have.attr', 'value', 'false')

		cy.get('@prevButton').should('not.have.prop', 'loading', true)
		cy.get('@prevButton').click()
		cy.get('@stringInput').should('not.have.attr', 'value', 'word_001')

		cy.get('@prevButton').should('have.attr', 'disabled')
		cy.get('@nextButton').should('not.have.attr', 'disabled')
		cy.get('@breadcrumbLabel').contains('word_000')
		cy.get('@stringInput').should('have.attr', 'value', 'word_000')
		cy.get('@booleanInput').should('have.attr', 'value', 'true')

		cy.go('back')

		// Test navigation at the middle of the list
		cy.get('.page-view')
			.should('have.attr', 'data-view-type', 'List').as('listViewSection')
			.find('> *').should('exist')

		cy.get('@listViewSection').find('lvl-table').shadow().find('.table__body').find('lvl-table-row[data-position=10]:not(.skeleton)').should('exist')
		cy.get('@listViewSection').find('lvl-table').shadow().find('.table__body').find('lvl-table-row[data-position=10]').as('row10')
		cy.get('@row10').click()
		waitForReadyPage('SinglePage')

		cy.get('lvl-button#goto-previous.navigation-button', { timeout: 10000 }).as('prevButton').should('exist')
		cy.get('lvl-button#goto-next.navigation-button').as('nextButton').should('exist')
		cy.get('lvl-breadcrumb').shadow().find('li.breadcrumb__item').last().find('.breadcrumb__label').as('breadcrumbLabel').should('exist')

		// switch to TestForm
		cy.get('lvl-side-nav-item[label="[TestForm]"]').click({ force: true })

		cy.get<Record<string, any>>('@singleDataForm').then(pageView => {
			cy.get(`lvl-form#edit-form-${pageView.id}`, { timeout: 10000 }).as('form').should('exist')
			cy.get('@form').should('have.attr', 'initdone')
		})
		cy.get('@form').find('lvl-input[name=WordValue]').as('stringInput').should('exist')
		cy.get('@form').find('lvl-toggle[name=ToggleValue]').as('booleanInput').should('exist')

		cy.get('@prevButton').should('not.have.attr', 'disabled')
		cy.get('@nextButton').should('not.have.attr', 'disabled')
		cy.get('@breadcrumbLabel').contains('word_010')
		cy.get('@stringInput').should('have.attr', 'value', 'word_010')
		cy.get('@booleanInput').should('have.attr', 'value', 'true')

		cy.get('@prevButton').should('not.have.prop', 'loading', true)
		cy.get('@prevButton').click()
		cy.get('@stringInput').should('not.have.attr', 'value', 'word_010')

		cy.get('@prevButton').should('not.have.attr', 'disabled')
		cy.get('@nextButton').should('not.have.attr', 'disabled')
		cy.get('@breadcrumbLabel').contains('word_009')
		cy.get('@stringInput').should('have.attr', 'value', 'word_009')
		cy.get('@booleanInput').should('have.attr', 'value', 'false')

		cy.get('@nextButton').should('not.have.prop', 'loading', true)
		cy.get('@nextButton').click()
		cy.get('@stringInput').should('not.have.attr', 'value', 'word_009')
		cy.get('@nextButton').should('not.have.prop', 'loading', true)
		cy.get('@nextButton').click()
		cy.get('@stringInput').should('not.have.attr', 'value', 'word_010')

		cy.get('@prevButton').should('not.have.attr', 'disabled')
		cy.get('@nextButton').should('not.have.attr', 'disabled')
		cy.get('@breadcrumbLabel').contains('word_011')
		cy.get('@stringInput').should('have.attr', 'value', 'word_011')
		cy.get('@booleanInput').should('have.attr', 'value', 'false')

		cy.go('back')

		// Test navigation at the end of the list
		cy.get('.page-view')
			.should('have.attr', 'data-view-type', 'List').as('listViewSection')
			.find('> *').should('exist')

		cy.get('@listViewSection').find('lvl-table').shadow().find('.table__body').find(`lvl-table-row[data-position=${numberOfEntries}]:not([skeleton])`).should('exist')
		cy.get('@listViewSection').find('lvl-table').shadow().find('.table__body').find(`lvl-table-row[data-position=${numberOfEntries}]`).as(`row${numberOfEntries}`)
		cy.get(`@row${numberOfEntries}`).click()
		waitForReadyPage('SinglePage')

		cy.get('lvl-button#goto-previous.navigation-button', { timeout: 10000 }).as('prevButton').should('exist')
		cy.get('lvl-button#goto-next.navigation-button').as('nextButton').should('exist')
		cy.get('lvl-breadcrumb').shadow().find('li.breadcrumb__item').last().find('.breadcrumb__label').as('breadcrumbLabel').should('exist')

		// switch to TestForm
		cy.get('lvl-side-nav-item[label="[TestForm]"]').click({ force: true })

		cy.get<Record<string, any>>('@singleDataForm').then(pageView => {
			cy.get(`lvl-form#edit-form-${pageView.id}`, { timeout: 10000 }).as('form').should('exist')
			cy.get('@form').should('have.attr', 'initdone')
		})
		cy.get('@form').find('lvl-input[name=WordValue]').as('stringInput').should('exist')
		cy.get('@form').find('lvl-toggle[name=ToggleValue]').as('booleanInput').should('exist')

		cy.get('@prevButton').should('not.have.attr', 'disabled')
		cy.get('@nextButton').should('have.attr', 'disabled')
		cy.get('@breadcrumbLabel').contains(`word_${numberOfEntries.toString().padStart(3, '0')}`)
		cy.get('@stringInput').should('have.attr', 'value', `word_${numberOfEntries.toString().padStart(3, '0')}`)
		cy.get('@booleanInput').should('have.attr', 'value', `${numberOfEntries % 2 == 0}`)

		cy.get('@prevButton').should('not.have.prop', 'loading', true)
		cy.get('@prevButton').click()
		cy.get('@stringInput').should('not.have.attr', 'value', `word_${numberOfEntries.toString().padStart(3, '0')}`)

		cy.get('@prevButton').should('not.have.attr', 'disabled')
		cy.get('@nextButton').should('not.have.attr', 'disabled')
		cy.get('@breadcrumbLabel').contains(`word_${(numberOfEntries - 1).toString().padStart(3, '0')}`)
		cy.get('@stringInput').should('have.attr', 'value', `word_${(numberOfEntries - 1).toString().padStart(3, '0')}`)
		cy.get('@booleanInput').should('have.attr', 'value', `${(numberOfEntries - 1) % 2 == 0}`)

		cy.get('@nextButton').should('not.have.prop', 'loading', true)
		cy.get('@nextButton').click()
		cy.get('@stringInput').should('not.have.attr', 'value', `word_${(numberOfEntries - 1).toString().padStart(3, '0')}`)

		cy.get('@prevButton').should('not.have.attr', 'disabled')
		cy.get('@nextButton').should('have.attr', 'disabled')
		cy.get('@breadcrumbLabel').contains(`word_${numberOfEntries.toString().padStart(3, '0')}`)
		cy.get('@stringInput').should('have.attr', 'value', `word_${numberOfEntries.toString().padStart(3, '0')}`)
		cy.get('@booleanInput').should('have.attr', 'value', `${numberOfEntries % 2 == 0}`)

		// add more entries to test get of addition rows
		const oldNumberOfEntries = numberOfEntries
		numberOfEntries = 100
		for (let i = oldNumberOfEntries + 1; i <= numberOfEntries; i++) {
			cy.createRecord('@dataSource', {
				values: {
					WordValue: `word_${i.toString().padStart(3, '0')}`,
					ToggleValue: i % 2 == 0,
				},
				groups: [ 'testgroup' ],
			})
		}

		cy.go('back')
		// Test loading for more
		cy.get('.page-view')
			.should('have.attr', 'data-view-type', 'List').as('listViewSection')
			.find('> *').should('exist')

		cy.get('@listViewSection').find('lvl-table').shadow().find('.table__body').find('lvl-table-row[data-position=0]:not([skeleton])').should('exist')
		cy.get('@listViewSection').find('lvl-table').shadow().find('.table__body').find('lvl-table-row').last().invoke('attr', 'data-position').as('lastRowIndex')
		cy.get<string>('@lastRowIndex').then(lastRowIndex => {
			const index = parseInt(lastRowIndex)

			cy.get('@listViewSection').find('lvl-table').shadow().find('.table__body').find(`lvl-table-row[data-position=${index}]`).as(`row${index}`)
			cy.get(`@row${index}`).click()
			waitForReadyPage('SinglePage')

			cy.get('lvl-button#goto-previous.navigation-button', { timeout: 10000 }).as('prevButton').should('exist')
			cy.get('lvl-button#goto-next.navigation-button').as('nextButton').should('exist')
			cy.get('lvl-breadcrumb').shadow().find('li.breadcrumb__item').last().find('.breadcrumb__label').as('breadcrumbLabel').should('exist')

			// switch to TestForm
			cy.get('lvl-side-nav-item[label="[TestForm]"]').click({ force: true })

			cy.get<Record<string, any>>('@singleDataForm').then(pageView => {
				cy.get(`lvl-form#edit-form-${pageView.id}`, { timeout: 10000 }).as('form').should('exist')
				cy.get('@form').should('have.attr', 'initdone')
			})
			cy.get('@form').find('lvl-input[name=WordValue]').as('stringInput').should('exist')
			cy.get('@form').find('lvl-toggle[name=ToggleValue]').as('booleanInput').should('exist')

			cy.get('@prevButton').should('not.have.attr', 'disabled')
			cy.get('@nextButton').should('not.have.attr', 'disabled')
			cy.get('@breadcrumbLabel').contains(`word_${index.toString().padStart(3, '0')}`)
			cy.get('@stringInput').should('have.attr', 'value', `word_${index.toString().padStart(3, '0')}`)
			cy.get('@booleanInput').should('have.attr', 'value', `${index % 2 == 0}`)

			cy.get('@nextButton').should('not.have.prop', 'loading', true)
			cy.get('@nextButton').click()
			cy.get('@stringInput').should('not.have.attr', 'value', `word_${index.toString().padStart(3, '0')}`)

			cy.get('@prevButton').should('not.have.attr', 'disabled')
			cy.get('@nextButton').should('not.have.attr', 'disabled')
			cy.get('@breadcrumbLabel').contains(`word_${(index + 1).toString().padStart(3, '0')}`)
			cy.get('@stringInput').should('have.attr', 'value', `word_${(index + 1).toString().padStart(3, '0')}`)
			cy.get('@booleanInput').should('have.attr', 'value', `${(index + 1) % 2 == 0}`)

			cy.get('@nextButton').should('not.have.prop', 'loading', true)
			cy.get('@nextButton').click()
			cy.get('@stringInput').should('not.have.attr', 'value', `word_${(index + 1).toString().padStart(3, '0')}`)

			cy.get('@prevButton').should('not.have.attr', 'disabled')
			cy.get('@nextButton').should('not.have.attr', 'disabled')
			cy.get('@breadcrumbLabel').contains(`word_${(index + 2).toString().padStart(3, '0')}`)
			cy.get('@stringInput').should('have.attr', 'value', `word_${(index + 2).toString().padStart(3, '0')}`)
			cy.get('@booleanInput').should('have.attr', 'value', `${(index + 2) % 2 == 0}`)

			cy.get('@prevButton').should('not.have.prop', 'loading', true)
			cy.get('@prevButton').click()
			cy.get('@stringInput').should('not.have.attr', 'value', `word_${(index + 2).toString().padStart(3, '0')}`)
			cy.get('@prevButton').should('not.have.prop', 'loading', true)
			cy.get('@prevButton').click()
			cy.get('@stringInput').should('not.have.attr', 'value', `word_${(index + 1).toString().padStart(3, '0')}`)
			cy.get('@prevButton').should('not.have.prop', 'loading', true)
			cy.get('@prevButton').click()
			cy.get('@stringInput').should('not.have.attr', 'value', `word_${(index).toString().padStart(3, '0')}`)

			cy.get('@prevButton').should('not.have.attr', 'disabled')
			cy.get('@nextButton').should('not.have.attr', 'disabled')
			cy.get('@breadcrumbLabel').contains(`word_${(index - 1).toString().padStart(3, '0')}`)
			cy.get('@stringInput').should('have.attr', 'value', `word_${(index - 1).toString().padStart(3, '0')}`)
			cy.get('@booleanInput').should('have.attr', 'value', `${(index - 1) % 2 == 0}`)
		})
	})

	afterEach(() => {
		// remove dataStore (this will delete the dataSource as well)
		cy.request({ url: '/Api/DataStores/', failOnStatusCode: false, method: 'GET' }).then((resp) => {
			expect(resp.status).to.equal(200, 'Get temporary data store: TestStore')
			const dataStore = resp.body.data.rows.find((row: Record<string, any>) => row.name === 'TestStore_' + guid)
			cy.request({ url: '/Api/DataStores/' + dataStore.id, failOnStatusCode: false, method: 'DELETE' }).then((resp) => {
				expect(resp.status).to.equal(200, 'Delete temporary data store: TestStore')
			})
		})
	})

	type PageType = 'MultiPage' | 'SinglePage'

	function waitForReadyPage(type: PageType) {
		if (type === 'MultiPage') {
			cy.get('.multi_page_content').should('exist')
			cy.get('lvl-multi-data-view').should('exist')
		} else if (type === 'SinglePage') {
			cy.get('.single_page_content').should('exist')
		}
	}
})