describe('ListView Configuration', () => {
	const uuid = () => Cypress._.random(0, 1e6)
	let guid = 0
	
	beforeEach(() => {
		guid = uuid()
		
		cy.createDataStore(guid, false).as('dataStore')
		cy.createDataSource('@dataStore', 'TestSource_' + guid).as('dataSource')
		
		// create a view fields 
		cy.createField('@dataSource', 'StringField', 'String', { length: 255 })
		cy.createField('@dataSource', 'IntField', 'Integer')
		cy.createField('@dataSource', 'DoubleField', 'Double', { decimalPlaces: 2 })
		cy.createField('@dataSource', 'DateField', 'Date')
	})

	afterEach(() => {
		// remove dataStore (this will delete the dataSource as well)
		cy.removeDataStore(guid)
	})

	it('check list view', () => {

		// go to Pages via Home
		cy.visit('/')
		cy.get('lvl-nav-button').as('navButton').click()
		cy.wait(150)
		cy.get('@navButton').shadow().find('#menu-container a[href="/Admin/Pages"]').as('navLink').should('be.visible')
		cy.get('@navLink').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/Pages'))
		})

		// select Backend
		cy.get('#page-menu').as('dataStoreMenu').should('have.attr', 'initDone')
		cy.get('@dataStoreMenu').find('lvl-side-nav-item[label="TestStore_' + guid + '"]').click()

		cy.get('[data-action=add]').as('addButton').shadow().should('not.be.null')
		cy.get('@addButton').click()
		cy.get('#page-form').as('form')
		cy.get('@form').find('#page-data-source-id').as('dataSourceField').shadow().find('input').focus().clear().type('TestSource').blur({ force: true })
		cy.get('@dataSourceField').invoke('attr', 'value').should('not.be.empty')
		cy.get('@form').find('#page-type').invoke('attr', 'value', 'MultiData')
		cy.get('@form').find('#page-name').invoke('attr', 'value', 'MultiPage')
		cy.get('@form').find('[name=breadcrumbLabel]').invoke('attr', 'value', 'MultiPage')

		cy.get('.side-panel > [data-action=save]').click()

		// wait for the new page to be part of the page list -> then enter it
		cy.get('#page-list').find('lvl-list').shadow().find('lvl-list-line:not([skeleton])').contains("MultiPage").as('row').should('exist')
		cy.get('@row').click()
		cy.url().then(url => {
			expect(url.endsWith('/Admin/DataStores/teststore' + guid + '/Pages/multipage'))
		})

		cy.get('#page-form').find('[name="name"][initdone]').should('have.attr', 'value', 'MultiPage')

		// wait until page has been loaded properly and view list is loaded
		cy.get('#page-detail').should('exist')
		cy.get('#page-view-list').shadow().find('.no-data').should('exist')

		// create list view
		cy.get('#create-panel').should('have.attr', 'initDone')
		cy.get('[data-action=add]').as('viewAddButton').shadow().should('not.be.null')
		cy.get('@viewAddButton').click()
		cy.get('#page-view-form').as('form')
		cy.get('@form').find('[name="name"][initdone]').invoke('attr', 'value', 'TestView')
		cy.get('@form').find('[name="type"][initdone]').invoke('attr', 'value', 'List')
		cy.get('@form').find('[name="parentField"][initdone]').as('parentField').should('not.be.visible')
		cy.get('@form').find('[name="displayType"][initdone] lvl-button[value="ChildParentNesting"]').click()
		cy.get('@parentField').should('be.visible')
		cy.get('@parentField').shadow().find('input').focus().clear().type('StringField').blur({ force: true })
		cy.get('@parentField').invoke('attr', 'value').should('not.be.empty')
		cy.get('.side-panel > [data-action=save]:not([hidden])').click()
		cy.get('#page-view-list > *').shadow().find('lvl-list-line:not([skeleton]):first-child').should('exist')
		
		// view should be visible inside the list -> enter it
		cy.get('#page-view-list > *').shadow().find('lvl-list-line:not([skeleton]):first-child').as('row')
			.then((cell) => {
			let viewName = cell[0].innerText
			cy.get('@row').click()
			cy.url().then(url => {
				expect(url.endsWith('/Admin/DataStores/teststore' + guid + '/Pages/' + viewName.toLowerCase()))
			})
		})
		
		cy.get('#page-view-detail').should('exist')
		
		// update tree to default
		cy.intercept( 'PATCH','Api/PageViews/*').as('pageViewUpdateResponse')
		cy.reload()
		cy.get('#page-view-form').as('form').should('exist')
		cy.get('@form').find('[name="parentField"][initdone]').as('parentField').should('be.visible')
		cy.get('@parentField').invoke('get', 0).its('displayValue').should('equal', 'StringField')
		cy.get('@form').find('[name="displayType"][initdone] lvl-button[value="ChildParentNesting"]').should('have.attr', 'selected')
		cy.get('@form').find('[name="displayType"][initdone] lvl-button[value="Default"]').click()
		cy.get('@parentField').should('not.be.visible')
		cy.get('#content-buttons [data-action="save"]').click()
		cy.wait('@pageViewUpdateResponse')
		cy.reload()
		cy.get('@form').find('[name="displayType"][initdone] lvl-button[value="Default"]').should('have.attr', 'selected')

		// switch to columns tab
		cy.get('#page-view-menu').as('pageViewMenu').should('have.attr', 'initDone')
		cy.get('@pageViewMenu').find('lvl-side-nav-item[value=ViewDesigner]').as('columnsTab').should('not.have.attr', 'skeleton')
		cy.get('@columnsTab').click()

		// expert functions should not be visible
		cy.get('[name="stickyColumnCount"]').should('exist').should('not.be.visible')
		
		// add a few columns
		addColumn('StringField')
		addColumn('IntField')
		addColumn('DoubleField')
		addColumn('DateField')

		cy.get('#list-view-column-list > *').shadow().find('lvl-list-line').eq(0).click()
		cy.get('#list-view-column-form').as('form')
		cy.get('@form').find('[name="display"]').should('not.be.visible')
		cy.get('@form').find('[name="allowEdit"]').should('not.be.visible')
	})
	
	it('create expert list view', () => {
		cy.intercept( 'POST','Api/PageViews').as('pageViewCreateResponse')
		cy.intercept( 'PATCH','Api/PageViews/*').as('pageViewUpdateResponse')
		cy.intercept( 'POST','Api/ListViewColumns').as('columnCreateResponse')
		cy.intercept( 'PATCH','Api/ListViewColumns/*').as('columnUpdateResponse')
		cy.intercept( 'GET','Api/ListViewColumns?*').as('columnListResponse')
		cy.createPage('@dataSource', 'MultiPage_' + guid, 'MultiData').as('multiPage')
		
		// create view
		cy.visit('/Admin/DataStores/teststore' + guid + '/Pages/multipage' + guid)
		cy.get('#create-panel').should('have.attr', 'initDone')
		cy.get('[data-action=add]').as('viewAddButton').shadow().should('not.be.null')
		cy.get('@viewAddButton').click()
		cy.get('#page-view-form').as('form')
		cy.get('@form').find('[name="name"][initdone]').invoke('attr', 'value', 'ExpertList')
		cy.get('@form').find('[name="type"][initdone]').invoke('attr', 'value', 'List')
		cy.get('@form').find('[name="stickyColumnCount"][initdone]').should('not.be.visible')
		cy.get('@form').find('[name="expertMode"][initdone]').click()
		cy.get('@form').find('[name="stickyColumnCount"][initdone]').should('be.visible')
		cy.get('@form').find('[name="stickyColumnCount"][initdone]').find('lvl-button[value="3"]').click()
		cy.get('.side-panel > [data-action=save]:not([hidden])').click()
		cy.wait('@pageViewCreateResponse')

		// update
		cy.visit('/Admin/DataStores/teststore' + guid + '/Pages/multipage' + guid + '/Views/expertlist')
		cy.get('#page-view-menu').as('pageViewMenu').should('have.attr', 'initDone')
		cy.get('@pageViewMenu').find('lvl-side-nav-item[value=ViewDesigner]').as('columnsTab').should('not.have.attr', 'skeleton')
		cy.get('@columnsTab').click()
		cy.wait('@columnListResponse')
		cy.get('[name="stickyColumnCount"]').should('be.visible')
		cy.get('[name="stickyColumnCount"]').find('lvl-button[value="3"]').should('have.attr', 'selected')
		cy.get('[name="stickyColumnCount"]').find('lvl-button[value="2"]').click()
		cy.get('#content-buttons [data-action="save"]').click()
		cy.wait('@pageViewUpdateResponse')
		cy.reload()
		cy.wait('@columnListResponse')
		cy.get('[name="stickyColumnCount"]').find('lvl-button[value="2"]').should('have.attr', 'selected')
		
		addColumn('StringField')
		cy.wait('@columnCreateResponse')
		cy.wait('@columnListResponse')
		cy.get('#list-view-column-list > *').shadow().find('lvl-list-line:not([skeleton])').eq(0).click()
		cy.get('#edit-panel:not([skeleton])').as('editSlideOut').should('be.visible')
		cy.get('#list-view-column-form').as('form')
		cy.get('@form').find('[name="display"]').should('be.visible')
		cy.get('@form').find('[name="display"]').click()
		cy.get('@form').find('[name="allowEdit"]').should('be.visible')
		cy.get('#edit-panel').find('[data-action="save"]').click()
		cy.wait('@columnUpdateResponse')
		cy.wait('@columnListResponse')
		cy.get('#list-view-column-list > *').shadow().find('lvl-list-line:not([skeleton])').should('exist')
		cy.get('#list-view-column-list > *').shadow().find('lvl-list-line:not([skeleton])').eq(0).click({ force: true })
		cy.get('@editSlideOut').find('[data-action="delete"][initdone]').should('be.visible')
		cy.get('@editSlideOut').find('[data-action="delete"][initdone]').click({ force: true })
		cy.wait('@columnListResponse')
		cy.get('#list-view-column-list > *').shadow().find('lvl-list-line').should('not.exist')
	})

	function addColumn(columnName: string) {
		// dialog should be closed before opening it again
		cy.get('#data-field-list').shadow().find('lvl-list-line:not([skeleton])').contains(columnName).click()
		cy.get('#list-view-column-list > *').shadow().find('lvl-list-line:not([skeleton])').contains(columnName).should('exist')
	}
})