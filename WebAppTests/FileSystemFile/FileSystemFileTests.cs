using Levelbuild.Core.WebAppCoreTests.FileInterface;
using Levelbuild.Domain.FileSystemFile.Dto;

namespace Levelbuild.Domain.WebAppTests.FileSystemFile;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public class FileSystemFileTests : FileInterfaceTests, IDisposable
{
	private static string _path = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
	public FileSystemFileTests() : base(new FileSystemStore(new FileSystemFileStoreConfig(_path)))
	{
	}
	
	public void Dispose()
	{
		try
		{
			Directory.Delete(_path, true);
		}
		catch (Exception)
		{
			// ignored
		}
	}
}