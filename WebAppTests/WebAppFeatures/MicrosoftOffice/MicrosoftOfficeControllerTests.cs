using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.Controllers;
using Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.Models;
using Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.Services;
using Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.Utils;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.MicrosoftOffice;

// Define a delegate for the TryGetValue callback
public delegate void TryGetValueCallback(object key, out object value);

[ExcludeFromCodeCoverage]
[Collection("PostgresDatabaseCollection")]
public class PostgresMicrosoftOfficeControllerTests(PostgresDatabaseFixture fixture)
    : MicrosoftOfficeControllerTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
    // Implementation is in the base class
}

[ExcludeFromCodeCoverage]
public abstract class MicrosoftOfficeControllerTests : IntegrationTest
{
    private readonly MicrosoftOfficeController _controllerInstance;
	private readonly RedisFileTrackingService _redisFileTrackingService;

	// Define a service injection action to add logging services
    private static readonly Action<IServiceCollection> ServiceInjection = services =>
    {
        // Add logging services
        services.AddLogging();
    };

    protected MicrosoftOfficeControllerTests(DatabaseFixture fixture) : base(fixture, ServiceInjection)
    {
		// Setup mocks for dependencies
        var mockHttpClientFactory = new Mock<IHttpClientFactory>();
        var mockMemoryCache = new Mock<IMemoryCache>();

		// Set up HttpClient factory to return a real HttpClient
		mockHttpClientFactory.Setup(f => f.CreateClient(It.IsAny<string>())).Returns(new HttpClient());

        // Create services using the correct logger types
        var loggerFactory = Fixture.ServiceProvider.GetRequiredService<ILoggerFactory>();
        var wopiDiscoveryLogger = loggerFactory.CreateLogger<WopiDiscoveryService>();
        var controllerLogger = loggerFactory.CreateLogger<MicrosoftOfficeController>();

		// Create a real WopiDiscoveryService
		var wopiDiscoveryService = new WopiDiscoveryService(mockMemoryCache.Object, wopiDiscoveryLogger, mockHttpClientFactory.Object);

		// Setup memory cache mock
		object storedValue = new WopiDiscovery();
        mockMemoryCache.Setup(m => m.TryGetValue(It.IsAny<object>(), out It.Ref<object>.IsAny!))
            .Callback(new TryGetValueCallback((object _, out object value) =>
            {
                value = storedValue;
            }))
            .Returns(true);

        // Create RedisFileTrackingService using the existing RedisAccessService from IntegrationTest
        _redisFileTrackingService = new RedisFileTrackingService(RedisAccessService);

        // Create a controller instance using the UserManager from IntegrationTest base class
        _controllerInstance = new MicrosoftOfficeController(
			LogManager,
			VersionReader,
            UserManager,
			Fixture.Context,
			_redisFileTrackingService
		); // UserManager is available from IntegrationTest base class

        // Setup HttpContext
		if (HttpContextAccessor.HttpContext == null) return;
		_controllerInstance.ControllerContext = new ControllerContext
		{
			HttpContext = HttpContextAccessor.HttpContext
		};

		// Register WopiDiscoveryService to be available via HttpContext.RequestServices
		var serviceProvider = new ServiceCollection()
			.AddSingleton(wopiDiscoveryService)
			.BuildServiceProvider();

		HttpContextAccessor.HttpContext.RequestServices = serviceProvider;
	}


	#region Tests

    [Fact(DisplayName = "Controller can be instantiated")]
    public void ControllerInstantiationTest()
    {
        // Assert that the controller was created successfully
        Assert.NotNull(_controllerInstance);
        Assert.IsType<MicrosoftOfficeController>(_controllerInstance);

        // Assert that we have the basic components
        Assert.NotNull(LogManager);
        Assert.NotNull(VersionReader);
        Assert.NotNull(_redisFileTrackingService);
        Assert.NotNull(Fixture.Context);
        Assert.NotNull(UserManager);
    }

    [Fact(DisplayName = "IndexApi returns BadRequest when data source not found")]
    public async Task IndexApi_DataSourceNotFound_ReturnsBadRequest()
    {
        // Arrange
        var nonExistentDataSourceId = Guid.NewGuid();
        const string testFileId = "test_file_123";
        const string viewMode = "edit";

        // Act
        var result = await _controllerInstance.IndexApi(nonExistentDataSourceId, testFileId, viewMode);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<BadRequestObjectResult>(result.Result);
    }

    [Fact(DisplayName = "IndexApi returns BadRequest when file ID is invalid")]
    public async Task IndexApi_InvalidFileId_ReturnsBadRequest()
    {
        // Arrange
        var dataSourceId = Guid.NewGuid();
        const string invalidFileId = "";
        const string viewMode = "edit";

        // Act
        var result = await _controllerInstance.IndexApi(dataSourceId, invalidFileId, viewMode);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<BadRequestObjectResult>(result.Result);
    }
	
    #endregion
}
