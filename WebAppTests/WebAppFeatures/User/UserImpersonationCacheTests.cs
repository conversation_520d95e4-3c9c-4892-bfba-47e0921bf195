using Google.Api.Gax;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Frontend.WebApp.Features.Auth.Dtos;
using Levelbuild.Frontend.WebApp.Features.Auth.Services;
using Levelbuild.Frontend.WebApp.Shared.Constants;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using StackExchange.Redis;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.User;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

[ExcludeFromCodeCoverage]
public class PostgresUserImpersonationCacheTests(PostgresDatabaseFixture fixture) : UserImpersonationCacheTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

[ExcludeFromCodeCoverage]
public abstract class UserImpersonationCacheTests : IDisposable
{
	private readonly DatabaseFixture _fixture;
	private readonly UserImpersonationCache _userImpersonationCache;
	
	private readonly Guid _actorId;
	private readonly Guid _subjectId;
	private readonly UserImpersonationInfoDto _entry;

	protected UserImpersonationCacheTests(DatabaseFixture fixture)
	{
		_fixture = fixture;
		
		_fixture.Services.AddKeyedSingleton<IConnectionMultiplexer>(RedisConstants.RedisWriteConnection, ConnectionMultiplexer.Connect(_fixture.Config.GetConnectionString("Redis")!));
		_fixture.Services.AddKeyedSingleton<IConnectionMultiplexer>(RedisConstants.RedisReadConnection, ConnectionMultiplexer.Connect(_fixture.Config.GetConnectionString("Redis")!));
		_fixture.Services.AddSingleton<IRedisAccessService, RedisAccessService>();
		_fixture.Services.AddSingleton<UserImpersonationCache>();
		
		_fixture.BuildServices();
		
		_userImpersonationCache = _fixture.ServiceProvider.GetRequiredService<UserImpersonationCache>();
		
		_actorId = Guid.NewGuid();
		_subjectId = Guid.NewGuid();
		_entry = new()
		{
			ActorId = _actorId,
			SubjectId = _subjectId,
			AccessToken = string.Empty,
			IdToken = string.Empty,
			RefreshToken = string.Empty,
			ExpiryDate = DateTime.UnixEpoch
		};
			
		_userImpersonationCache.AddAsync(_entry).WaitWithUnwrappedExceptions();
	}
	
	#region Tests
	
	[Fact(DisplayName = "Test if actor is saved correctly.")]
	public async Task TestIsActor()
	{
		Assert.True(await _userImpersonationCache.IsActorAsync(_actorId));
	}
	
	[Fact(DisplayName = "Test if subject is detected as actor.")]
	public async Task TestIsActorWithSubject()
	{
		Assert.False(await _userImpersonationCache.IsActorAsync(_subjectId));
	}
	
	[Fact(DisplayName = "Test if random id is detected as actor.")]
	public async Task TestIsActorWithUnknownId()
	{
		var guid = Guid.NewGuid();
		Assert.False(await _userImpersonationCache.IsActorAsync(guid));
	}
	
	[Fact(DisplayName = "Test getting a valid entry.")]
	public async Task TestGet()
	{
		var entry = await _userImpersonationCache.GetAsync(_actorId);
		Assert.NotNull(entry);
		Assert.Equivalent(_entry, entry);
	}
	
	[Fact(DisplayName = "Test getting a subject instead of actor.")]
	public async Task TestGetWithSubject()
	{
		Assert.Null(await _userImpersonationCache.GetAsync(_subjectId));
	}
	
	[Fact(DisplayName = "Test getting a random id.")]
	public async Task TestGetWithUnknownId()
	{
		var guid = Guid.NewGuid();
		Assert.Null(await _userImpersonationCache.GetAsync(guid));
	}
	
	[Fact(DisplayName = "Test try-getting a valid entry.")]
	public async Task TestTryGet()
	{
		var result = await _userImpersonationCache.TryGetAsync(_actorId);
		Assert.True(result.Success);
		Assert.NotNull(result.UserImpersonationInfo);
		Assert.Equivalent(_entry, result.UserImpersonationInfo);
	}
	
	[Fact(DisplayName = "Test try-getting a subject instead of actor.")]
	public async Task TestTryGetWithSubject()
	{
		var result = await _userImpersonationCache.TryGetAsync(_subjectId);
		Assert.False(result.Success);
		Assert.Null(result.UserImpersonationInfo);
	}
	
	[Fact(DisplayName = "Test try-getting a random id.")]
	public async Task TestTryGetWithUnknownId()
	{
		var result = await _userImpersonationCache.TryGetAsync(Guid.NewGuid());
		Assert.False(result.Success);
		Assert.Null(result.UserImpersonationInfo);
	}
	
	[Fact(DisplayName = "Test removing a valid entry.")]
	public async Task TestRemove()
	{
		Assert.NotNull(await _userImpersonationCache.GetAsync(_actorId));
		
		await _userImpersonationCache.RemoveAsync(_actorId);
		
		Assert.Null(await _userImpersonationCache.GetAsync(_actorId));
	}
	
	[Fact(DisplayName = "Test removing subject instead of actor.")]
	public async Task TestRemoveSubject()
	{
		Assert.NotNull(await _userImpersonationCache.GetAsync(_actorId));
		
		await _userImpersonationCache.RemoveAsync(_subjectId);
		
		Assert.NotNull(await _userImpersonationCache.GetAsync(_actorId));
		Assert.Null(await _userImpersonationCache.GetAsync(_subjectId));
	}
	
	[Fact(DisplayName = "Test removing a random Id.")]
	public async Task TestRemoveUnknownId()
	{
		var guid = Guid.NewGuid();
		Assert.Null(await _userImpersonationCache.GetAsync(guid));
		
		await _userImpersonationCache.RemoveAsync(guid);
		
		Assert.NotNull(await _userImpersonationCache.GetAsync(_actorId));
		Assert.Null(await _userImpersonationCache.GetAsync(guid));
	}
	
	#endregion
	
	public void Dispose()
	{
		_fixture.CleanUp();
	}
}