using Google.Api.Gax;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.Page;
using Levelbuild.Entities.Features.Page.MultiData;
using Levelbuild.Entities.Features.Page.SingleData;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Services;
using Levelbuild.Frontend.WebApp.Features.Page.Controllers;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Localization;
using Microsoft.Extensions.DependencyInjection;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.Page;

[ExcludeFromCodeCoverage]
public abstract class AdminPageControllerTests : AdminControllerTest<AdminPageController, PageEntity, PageDto>
{
	private static readonly Action<IServiceCollection> ServiceInjection = services =>
	{
		services.AddLocalization();
		services.ConfigureOptions<CustomRequestLocalizationOptions>();
	};

	#region Test Data Properties

	protected override PageDto CreateDto
	{
		get
		{
			using var databaseContext = Fixture.Context;
			var currentCustomer = UserManager.GetCurrentCustomerAsync().ResultWithUnwrappedExceptions();
			var source = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id);

			return new()
			{
				Name = "Schlumpf Page",
				DataSourceId = source.Id,
				DataSourceName = source.Name,
				Description = "Schlumpfbeeren ballern!",
				LastModifiedText = "-",
				Created = DateTime.Now.ToUniversalTime(),
				CreatedBy = UserManager.GetCurrentUserAsync().ResultWithUnwrappedExceptions().DisplayName
			};
		}
	}

	protected override PageDto InvalidCreateDto => new()
	{
		Name = null,
		TypeName = "Typ Schlumpf"
	};

	protected override PageDto InvalidUpdateDto => new()
	{
		Id = Guid.Empty,
		Name = null,
		TypeName = string.Empty
	};
	
	protected override QueryParamsDto QueryParams => new()
	{
		Sortings = new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Asc
			}
		}
	};
	
	protected override QueryParamsDto QueryParamsWithLimitAndOffset => new()
	{
		Limit = 3,
		Offset = 1,
		Sortings = new List<QueryParamSortingDto>
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Asc
			}
		}
	};
	
	protected override QueryParamsDto QueryParamsWithSorting => new()
	{
		Sortings = new List<QueryParamSortingDto>
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Desc
			}
		}
	};

	#endregion

	protected AdminPageControllerTests(DatabaseFixture fixture) : base(fixture, ServiceInjection)
	{
		Fixture.InitStorageDataBase();
		
		var logManager = Fixture.ServiceProvider.GetRequiredService<LogManager>();
		var userManager = Fixture.ServiceProvider.GetRequiredService<UserManager>();
		var localizerFactory = Fixture.ServiceProvider.GetRequiredService<IExtendedStringLocalizerFactory>();
		ControllerInstance = new AdminPageController(logManager, Fixture.ContextFactory, userManager, localizerFactory, VersionReader);
	}

	#region Data Preparation

	protected async Task UpdateDtoAsync(PageDto page)
	{
		page.Name = "Foo";
		page.LastModified = DateTime.Now;
		page.LastModifiedBy = (await UserManager.GetCurrentUserAsync()).DisplayName;
		var localizer = ControllerInstance!.StringLocalizerFactory.Create("Page", "List");
		page.LastModifiedText = localizer["lastModifiedValue", page.LastModified.Value.ToString("g"), page.LastModifiedBy!];
	}
	
	protected override async Task PrepareQueryDataAsync()
	{
		await using var databaseContext = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();
		var source = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id);
		
		QueryData.Add(new MultiDataPageDto()
		{
			Name = "MultiPage1",
			DataSource = source.ToDto(),
			DataSourceId = source.Id,
			Description = "test1",
			Type = PageType.MultiData,
		});

		QueryData.Add(new MultiDataPageDto()
		{
			Name = "MultiPage2",
			DataSource = source.ToDto(),
			DataSourceId = source.Id,
			Description = "test2",
			Type = PageType.MultiData,
		});
		
		QueryData.Add(new MultiDataPageDto()
		{
			Name = "MultiPage3",
			DataSource = source.ToDto(),
			DataSourceId = source.Id,
			Description = "test3",
			Type = PageType.MultiData,
		});
		
		QueryData.Add(new SingleDataPageDto()
		{
			Name = "SinglePage1",
			DataSource = source.ToDto(),
			DataSourceId = source.Id,
			Description = "test1",
			Type = PageType.SingleData
		});

		QueryData.Add(new SingleDataPageDto()
		{
			Name = "SinglePage2",
			DataSource = source.ToDto(),
			DataSourceId = source.Id,
			Description = "test2",
			Type = PageType.SingleData
		});
		
		QueryData.Add(new SingleDataPageDto()
		{
			Name = "SinglePage3",
			DataSource = source.ToDto(),
			DataSourceId = source.Id,
			Description = "test3",
			Type = PageType.SingleData
		});
		
		foreach (PageDto dto in QueryData)
		{
			switch(dto.Type)
			{
				case PageType.SingleData:
					databaseContext.SingleDataPages.Add(SingleDataPageEntity.FromDto((SingleDataPageDto)dto, null, databaseContext));
					break;
				
				case PageType.MultiData:
					databaseContext.MultiDataPages.Add(MultiDataPageEntity.FromDto((MultiDataPageDto)dto, null, databaseContext));
					break;
			}
		}

		await databaseContext.SaveChangesAsync();
	}
		
	#endregion

	#region Assertions

	protected override void AssertIsAsExpected(PageDto expected, PageDto actual)
	{
		Assert.Equal(expected.Name, actual.Name);
		Assert.False((actual.Created!.Value - expected.Created!.Value).TotalSeconds < -1 || actual.Created >= DateTime.Now.ToUniversalTime());
		Assert.Equal(expected.CreatedBy, actual.CreatedBy);
		Assert.Equal(expected.DataSourceName, actual.DataSourceName);
		Assert.Equal(expected.Description, actual.Description);
		Assert.False(expected.Created >= actual.LastModified || actual.LastModified >= DateTime.Now);
		Assert.Equal(expected.LastModifiedBy, actual.LastModifiedBy);
		Assert.Equal(expected.LastModifiedText, actual.LastModifiedText);
		Assert.Equal(expected.TypeName, actual.TypeName);
		Assert.Equal(expected.Type, actual.Type);
		Assert.Equal(expected.GetType(), actual.GetType());
		Assert.Equal(expected.DataSourceId, actual.DataSourceId);
	}
	
	protected override void AssertExists(PageDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.Pages.Any(page => page.Name == dto.Name));
	}
	
	protected override void AssertDoesNotExist(PageDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.Pages.All(page => page.Name != dto.Name));
	}
	
	protected override bool DeleteSuccessful(PageEntity entity)
	{
		using var databaseContext = Fixture.Context;
		return databaseContext.Pages.Find(entity.Id) == null;
	}
	
	protected override bool CheckQueryResult(ConfigQueryResultDto<PageDto> queryResultDto, int limit = 0, int offset = 0)
	{
		var count = QueryData.Count;
		var mutatedNamesList = queryResultDto.Rows.ToList().ConvertAll(config => config.Name);
		var expectedList = new List<string>();
		for(var i = offset; i < count && i <= (count - limit); i++)
		{
			expectedList.Add(QueryData[i].Name!);
		}
		
		if (queryResultDto.CountTotal != count)
			return false;
		if (queryResultDto.Rows.Count != (count - limit))
			return false;
		
		for (var i = 0; i < mutatedNamesList.Count; i++)
		{
			if (expectedList[i] != mutatedNamesList[i])
				return false;
		}

		return true;
	}
	
	protected override bool CheckSortingQueryResult(ConfigQueryResultDto<PageDto> queryResultDto)
	{
		var count = QueryData.Count;
		var mutatedNamesList = queryResultDto.Rows.ToList().ConvertAll(config => config.Name);
		var expectedList = QueryData.OrderByDescending(page => page.Name).ThenBy(page => page.Description).ToList();
		
		if (queryResultDto.CountTotal != count)
			return false;
		if (queryResultDto.Rows.Count != count)
			return false;
		
		for (var i = 0; i < mutatedNamesList.Count; i++)
		{
			if (expectedList[i].Name != mutatedNamesList[i])
				return false;
		}

		return true;
	}
	
	protected override bool CheckFilterQueryResult(ConfigQueryResultDto<PageDto> queryResultDto)
	{
		if (queryResultDto.CountTotal != 1)
			return false;
		if (queryResultDto.Rows.Count != 1)
			return false;

		int queryIndex = 0;
		switch (queryResultDto.Rows.FirstOrDefault()?.Type)
		{
			case PageType.SingleData:
				queryIndex = 4;
				break;
			
			case PageType.MultiData:
				queryIndex = 1;
				break;
		}
		
		return QueryData[queryIndex].Name == queryResultDto.Rows.FirstOrDefault()?.Name;
	}
	
	#endregion
	
}