using Google.Api.Gax;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.PageView.ListView;
using Levelbuild.Frontend.WebApp.Features.ListViewColumn.Controllers;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using IServiceCollection = Microsoft.Extensions.DependencyInjection.IServiceCollection;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.ListViewColumn;

[ExcludeFromCodeCoverage]
[Collection("PostgresDatabaseCollection")]
public class PostgresListViewColumnControllerTests(PostgresDatabaseFixture fixture)
	: ListViewColumnControllerTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

/*[ExcludeFromCodeCoverage]
[Collection("SqlServerDatabaseCollection")]
public class SqlServerListViewColumnControllerTests : ListViewColumnControllerTests, IClassFixture<SqlServerDatabaseFixture>
{
	public SqlServerListViewColumnControllerTests(SqlServerDatabaseFixture fixture) : base(fixture)
	{
		// nothing
	}
}*/

[ExcludeFromCodeCoverage]
public abstract class ListViewColumnControllerTests : AdminControllerTest<ListViewColumnController, ListViewColumnEntity, ListViewColumnDto>
{
	private static readonly Action<IServiceCollection> ServiceInjection = services =>
	{
		services.AddLocalization();
		services.ConfigureOptions<CustomRequestLocalizationOptions>();
	};
	
	#region Test Data Properties
	
	protected override ListViewColumnDto CreateDto
	{
		get
		{
			using var databaseContext = Fixture.Context;
			var currentCustomer = UserManager.GetCurrentCustomerAsync().ResultWithUnwrappedExceptions();
			var listView = EntityCreation.CreateListView(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id);
			var dataField = EntityCreation.CreateDataField(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id);
			
			return new()
			{
				ListViewId = listView.Id,
				FieldId = dataField.Id,
				Position = 1
			};
		}
	}
	
	protected override ListViewColumnDto InvalidCreateDto => new()
	{
		ListViewId = new Guid("00000000-1234-1234-1234-000000099999"),
		FieldId = new Guid("00000000-1234-1234-1234-000000099999")
	};
	
	protected override ListViewColumnDto InvalidUpdateDto => new()
	{
		ListViewId = new Guid("00000000-1234-1234-1234-000000099999"),
		FieldId = new Guid("00000000-1234-1234-1234-000000099999")
	};
	
	protected override QueryParamsDto QueryParams => new()
	{
		Sortings = new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "listViewId",
				Direction = SortDirection.Asc
			}
		}
	};
	
	protected override QueryParamsDto QueryParamsWithLimitAndOffset => new()
	{
		Limit = 2,
		Offset = 1,
		Sortings = new List<QueryParamSortingDto>
		{
			new()
			{
				OrderColumn = "listViewId",
				Direction = SortDirection.Asc
			}
		}
	};
	
	protected override QueryParamsDto QueryParamsWithSorting => new()
	{
		Sortings = new List<QueryParamSortingDto>
		{
			new()
			{
				OrderColumn = "listViewId",
				Direction = SortDirection.Desc
			}
		}
	};

	protected override QueryParamsDto QueryParamsWithFilter
	{
		get
		{
			using var databaseContext = Fixture.Context;
			var currentCustomer = UserManager.GetCurrentCustomerAsync().ResultWithUnwrappedExceptions();
			return new()
			{
				Filters = new List<QueryParamFilterDto>
				{
					new()
					{
						FilterColumn = "listViewId",
						Operator = QueryParamFilterOperator.Equals,
						CompareValue = EntityCreation.CreateListView(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id).Id
					}
				}
			};
		}
	}

	private QueryParamsDto QueryParamsDtoDataSource => new()
	{
		Sortings = new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Asc
			}
		}
	};

	private QueryParamsDto QueryParamsDtoWithLimitAndOffsetDataSource => new()
	{
		Limit = 1,
		Offset = 1,
		Sortings = new List<QueryParamSortingDto>
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Asc
			}
		}
	};

	private QueryParamsDto QueryParamsDtoWithSortingDataSource => new()
	{
		Sortings = new List<QueryParamSortingDto>
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Desc
			}
		}
	};

	private QueryParamsDto QueryParamsDtoWithFilterDataSource => new()
	{
		Filters = new List<QueryParamFilterDto>
		{
			new()
			{
				FilterColumn = "name",
				Operator = QueryParamFilterOperator.Equals,
				CompareValue = QueryData[4].FieldName
			}
		}
	};
	
	#endregion

	protected ListViewColumnControllerTests(DatabaseFixture fixture) : base(fixture, ServiceInjection)
	{
		Fixture.InitStorageDataBase();
		
		ControllerInstance = new ListViewColumnController(LogManager, Fixture.ContextFactory, UserManager, LocalizerFactory, VersionReader);
	}
	
	#region Data Preparation
	
	protected override ListViewColumnEntity PrepareSingleEntity()
	{
		using var databaseContext = Fixture.Context;
		var entry = databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(CreateDto, null, databaseContext));
		databaseContext.SaveChanges();
		
		entry.Entity.SetStringLocalizerFactory(ControllerInstance!.StringLocalizerFactory);
		
		return entry.Entity;
	}
	
	protected override ListViewColumnDto GetUpdateDto(ListViewColumnEntity entity)
	{
		var dto = entity.ToDto();
		dto.Position = 420;
		return dto;
	}
	
	protected override async Task PrepareQueryDataAsync()
	{
		await using var databaseContext = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();
		var listView = EntityCreation.CreateListView(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id);
		var dataField = EntityCreation.CreateDataField(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id);
		var anotherDataField = EntityCreation.CreateDataField(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id, "Brent Spiner");
		
		QueryData.Add(new ListViewColumnDto()
		{
			ListViewId = listView.Id,
			FieldId = dataField.Id,
			Position = 1
		});
		
		QueryData.Add(new ListViewColumnDto()
		{
			ListViewId = listView.Id,
			FieldId = dataField.Id,
			Position = 2
		});
		
		QueryData.Add(new ListViewColumnDto()
		{
			ListViewId = listView.Id,
			FieldId = anotherDataField.Id,
			Position = 3
		});
		
		QueryData.Add(new ListViewColumnDto()
		{
			ListViewId = listView.Id,
			FieldId = anotherDataField.Id,
			Position = 4
		});
		
		foreach (var dto in QueryData)
		{
			databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(dto, null, databaseContext));
		}
		
		await databaseContext.SaveChangesAsync();
		
		var entities = databaseContext.ListViewColumns.ToList();
		for (var i = 0; i < QueryData.Count; i++)
		{
			QueryData[i] = entities[i].ToDto();
		}
	}

	private async Task PrepareDataSourceQueryDataAsync()
	{
		await using var databaseContext = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();
		var listView = EntityCreation.CreateListView(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id);
		var anotherListView = EntityCreation.CreateListView(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id, "Example2");
		var dataField = EntityCreation.CreateDataField(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id, "Brent Spiner");
		var anotherDataField = EntityCreation.CreateDataField(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id);
		var unusedDataField = EntityCreation.CreateDataField(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id, "B-4");
		var anotherUnusedDataField = EntityCreation.CreateDataField(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id, "Lal");
		var thirdUnusedDataField = EntityCreation.CreateDataField(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id, "Lore");
		
		QueryData.Add(new ListViewColumnDto()
		{
			ListViewId = listView.Id,
			FieldId = dataField.Id,
			FieldName = dataField.Name,
			Position = 1
		});
		
		QueryData.Add(new ListViewColumnDto()
		{
			ListViewId = listView.Id,
			FieldId = anotherDataField.Id,
			FieldName = anotherDataField.Name,
			Position = 2
		});
		
		QueryData.Add(new ListViewColumnDto()
		{
			ListViewId = anotherListView.Id,
			FieldId = dataField.Id,
			FieldName = dataField.Name,
			Position = 1
		});
		
		QueryData.Add(new ListViewColumnDto()
		{
			ListViewId = anotherListView.Id,
			FieldId = unusedDataField.Id,
			FieldName = unusedDataField.Name,
			Position = 2
		});
		
		QueryData.Add(new ListViewColumnDto()
		{
			ListViewId = anotherListView.Id,
			FieldId = anotherUnusedDataField.Id,
			FieldName = anotherUnusedDataField.Name,
			Position = 3
		});
		
		QueryData.Add(new ListViewColumnDto()
		{
			ListViewId = anotherListView.Id,
			FieldId = thirdUnusedDataField.Id,
			FieldName = thirdUnusedDataField.Name,
			Position = 4
		});
		
		foreach (var dto in QueryData)
		{
			databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(dto, null, databaseContext));
		}
		
		await databaseContext.SaveChangesAsync();
	}
	
	#endregion
	
	#region Assertions
	
	protected override void AssertIsAsExpected(ListViewColumnDto expected, ListViewColumnDto actual)
	{
		Assert.Equal(expected.FieldId, actual.FieldId);
		Assert.Equal(expected.ListViewId, actual.ListViewId);
		Assert.Equal(expected.Position, actual.Position);
	}
	
	protected override void AssertExists(ListViewColumnDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.ListViewColumns.Any(entity => entity.FieldId == dto.FieldId && entity.ListViewId == dto.ListViewId));
	}
	
	protected override void AssertDoesNotExist(ListViewColumnDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.ListViewColumns.All(listViewColumn => listViewColumn.Id != dto.Id));
	}
	
	protected override bool DeleteSuccessful(ListViewColumnEntity entity)
	{
		using var databaseContext = Fixture.Context;
		return databaseContext.ListViewColumns.Find(entity.Id) == null;
	}
	
	protected override bool CheckQueryResult(ConfigQueryResultDto<ListViewColumnDto> queryResultDto, int limit = 0, int offset = 0)
	{
		var count = QueryData.Count;
		var mutatedListViewColumnIdsList = queryResultDto.Rows.ToList().ConvertAll(config => config.Id);
		var expectedList = new List<Guid>();
		for (var i = offset; i < count && i <= (count - limit); i++)
		{
			expectedList.Add((Guid)QueryData[i].Id!);
		}
		
		if (queryResultDto.CountTotal != count)
			return false;
		if (queryResultDto.Rows.Count != (count - limit))
			return false;
		
		for (var i = 0; i < mutatedListViewColumnIdsList.Count; i++)
		{
			if (expectedList[i] != mutatedListViewColumnIdsList[i])
				return false;
		}
		
		return true;
	}
	
	protected override bool CheckSortingQueryResult(ConfigQueryResultDto<ListViewColumnDto> queryResultDto)
	{
		var count = QueryData.Count;
		var mutatedListViewColumnIdsList = queryResultDto.Rows.ToList().ConvertAll(config => config.Id);
		var expectedList = QueryData.OrderByDescending(listViewColumn => listViewColumn.FieldName).ThenBy(listViewColumn => listViewColumn.FieldId).ToList();
		
		if (queryResultDto.CountTotal != count)
			return false;
		if (queryResultDto.Rows.Count != count)
			return false;
		
		using var databaseContext = Fixture.Context;
		for (var i = 0; i < mutatedListViewColumnIdsList.Count; i++)
		{
			var contextColumn = databaseContext.ListViewColumns.Find(mutatedListViewColumnIdsList[i]);
			if (expectedList[i].ListViewId != contextColumn!.ListViewId)
				return false;
		}
		
		return true;
	}
	
	protected override bool CheckFilterQueryResult(ConfigQueryResultDto<ListViewColumnDto> queryResultDto)
	{
		if (queryResultDto.CountTotal < 1)
			return false;
		
		using var databaseContext = Fixture.Context;
		var column = databaseContext.ListViewColumns.Find(queryResultDto.Rows.FirstOrDefault()?.Id);
		
		return QueryData[1].ListViewId == column!.ListViewId;
	}

	private bool CheckDataSourceQueryResult(AutocompleteQueryResultDto queryResultDto, int limit = 0, int offset = 0)
	{
		var count = QueryData.Count;
		var mutatedList = queryResultDto.Rows.ToList();
		var expectedList = new List<string>();
		for (var i = 3 + offset; i < count && i < (count - limit); i++)
		{
			expectedList.Add(QueryData[i].FieldName);
		}
		
		if (mutatedList.Count != expectedList.Count)
			return false;

		return !mutatedList.Where((t, i) => expectedList[i] != (string)t.Label!).Any();
	}

	private bool CheckDataSourceFilterQueryResult(AutocompleteQueryResultDto queryResultDto)
	{
		if (queryResultDto.CountTotal < 1)
			return false;
		
		return QueryData[4].FieldName == (string)queryResultDto.Rows.FirstOrDefault()!.Label!;
	}

	private bool CheckDataSourceSortingQueryResult(AutocompleteQueryResultDto queryResultDto)
	{
		var count = QueryData.Count;
		var mutatedList = queryResultDto.Rows.ToList();
		var expectedList = new List<string>();
		for (var i = 3; i < count; i++)
		{
			expectedList.Add(QueryData[i].FieldName);
		}
		
		expectedList = expectedList.OrderDescending().ToList();
		
		if (mutatedList.Count != expectedList.Count)
			return false;
		
		for (var i = 0; i < mutatedList.Count; i++)
		{
			if (expectedList[i] != (string)mutatedList[i].Label!)
				return false;
		}
		
		return true;
	}
	
	#endregion
	
	#region Tests
	
	[Fact(DisplayName = "Get possible DataSources for ListViewColumn")]
	public async Task DataSourcesQueryTest()
	{
		await PrepareDataSourceQueryDataAsync();
		var listViewId = QueryData[0].ListViewId;
		var result = (ControllerInstance?.QueryDataFields(listViewId, QueryParamsDtoDataSource).Result as ObjectResult)!;
		Assert.NotNull(result);
		Assert.Equal(200, result.StatusCode);
		
		var data = result.Value is FrontendResponse<AutocompleteQueryResultDto> response ? response.Data : null;
		Assert.NotNull(data);
		Assert.True(CheckDataSourceQueryResult(data));
	}
	
	[Fact(DisplayName = "Get possible DataSources for ListViewColumn with limit and offset")]
	public async Task DataSourcesQueryLimitAndOffsetTest()
	{
		await PrepareDataSourceQueryDataAsync();
		var listViewId = QueryData[0].ListViewId;
		var result = (ControllerInstance?.QueryDataFields(listViewId, QueryParamsDtoWithLimitAndOffsetDataSource).Result as ObjectResult)!;
		Assert.NotNull(result);
		Assert.Equal(200, result.StatusCode);
		
		var data = result.Value is FrontendResponse<AutocompleteQueryResultDto> response ? response.Data : null;
		Assert.NotNull(data);
		Assert.True(CheckDataSourceQueryResult(data, QueryParamsDtoWithLimitAndOffsetDataSource.Limit, QueryParamsDtoWithLimitAndOffsetDataSource.Offset));
	}
	
	[Fact(DisplayName = "Get possible DataSources for ListViewColumn with sorting")]
	public async Task DataSourcesQuerySortingTest()
	{
		await PrepareDataSourceQueryDataAsync();
		var listViewId = QueryData[0].ListViewId;
		var result = (ControllerInstance?.QueryDataFields(listViewId, QueryParamsDtoWithSortingDataSource).Result as ObjectResult)!;
		Assert.NotNull(result);
		Assert.Equal(200, result.StatusCode);
		
		var data = result.Value is FrontendResponse<AutocompleteQueryResultDto> response ? response.Data : null;
		Assert.NotNull(data);
		Assert.True(CheckDataSourceSortingQueryResult(data));
	}
	
	[Fact(DisplayName = "Get possible DataSources for ListViewColumn with filtering")]
	public async Task DataSourcesQueryFilteringTest()
	{
		await PrepareDataSourceQueryDataAsync();
		var listViewId = QueryData[0].ListViewId;
		var result = (ControllerInstance?.QueryDataFields(listViewId, QueryParamsDtoWithFilterDataSource).Result as ObjectResult)!;
		Assert.NotNull(result);
		Assert.Equal(200, result.StatusCode);
		
		var data = result.Value is FrontendResponse<AutocompleteQueryResultDto> response ? response.Data : null;
		Assert.NotNull(data);
		Assert.True(CheckDataSourceFilterQueryResult(data));
	}
	
	[Fact(DisplayName = "Get possible DataSources for ListViewColumn with invalid Id")]
	public async Task DataSourcesQueryFailTest()
	{
		await PrepareDataSourceQueryDataAsync();
		var result = (ControllerInstance?.QueryDataFields(Guid.Empty, new QueryParamsDto()).Result as ObjectResult)!;
		Assert.NotNull(result);
		Assert.Equal(400, result.StatusCode);
		
		var data = result.Value is FrontendResponse<AutocompleteQueryResultDto> response ? response.Data : null;
		Assert.Null(data);
	}
	
	#endregion
}