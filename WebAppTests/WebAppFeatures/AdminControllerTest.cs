using System.Text.Json;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities;
using Levelbuild.Entities.Interfaces;
using Levelbuild.Frontend.WebApp.Shared;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures;

public abstract class AdminControllerTest<TController, TEntity, TDto> : IntegrationTest
	where TController : IAdminController<TDto>
	where TEntity : PersistentEntity<TEntity>, IConvertibleEntity<TDto>
	where TDto : EntityDto
{
	protected IList<TDto> QueryData = new List<TDto>();

	protected TController? ControllerInstance;

	#region Test Data Properties

	/// <summary>
	/// Contains the DTO used to create a entity.
	/// </summary>
	protected abstract TDto CreateDto { get; }

	/// <summary>
	/// Contains the DTO used to provoke a failed entity creation.
	/// </summary>
	protected abstract TDto InvalidCreateDto { get; }

	/// <summary>
	/// Contains the DTO used to provoke a failed entity update.
	/// </summary>
	protected abstract TDto InvalidUpdateDto { get; }

	/// <summary>
	/// Contains basic query params.
	/// </summary>
	protected abstract QueryParamsDto QueryParams { get; }

	/// <summary>
	/// Contains query params with limit and offset.
	/// </summary>
	protected abstract QueryParamsDto QueryParamsWithLimitAndOffset { get; }

	/// <summary>
	/// Contains query params with sorting.
	/// </summary>
	protected abstract QueryParamsDto QueryParamsWithSorting { get; }

	/// <summary>
	/// Contains query params with filter.
	/// </summary>
	protected abstract QueryParamsDto QueryParamsWithFilter { get; }

	#endregion
	
	/// <summary>
	/// Constructor.
	/// 
	/// The _controllerInstance field must be assigned in the constructor of the derived test class.
	/// </summary>
	/// <param name="fixture"></param>
	/// <param name="additionalServiceInjection"></param>
	/// <param name="initStorage"></param>
	protected AdminControllerTest(DatabaseFixture fixture, Action<IServiceCollection>? additionalServiceInjection = null, bool initStorage = false) : base(fixture, additionalServiceInjection, initStorage)
	{
		
	}

	#region Data Preparation

	/// <summary>
	/// Creates & saves a single entity.
	/// </summary>
	/// <returns>The entity.</returns>
	protected abstract TEntity PrepareSingleEntity();

	/// <summary>
	/// Returns the DTO that is expected after the creation of an entity
	/// </summary>
	/// <returns></returns>
	protected virtual TDto? GetExpectedCreateDto()
	{
		return null;
	}

	/// <summary>
	/// Provides the DTO used for updating an entity based on that entity.
	/// </summary>
	/// <param name="entity"></param>
	/// <returns>The DTO.</returns>
	protected abstract TDto GetUpdateDto(TEntity entity);

	/// <summary>
	/// Creates & saves multiple entities used for query tests.
	/// </summary>
	protected abstract Task PrepareQueryDataAsync();

	#endregion

	#region Assertions

	/// <summary>
	/// Checks if the resulting DTO is as expected.
	/// </summary>
	/// <param name="expected"></param>
	/// <param name="actual"></param>
	/// <returns>True if everything is as expected.</returns>
	protected abstract void AssertIsAsExpected(TDto expected, TDto actual);

	/// <summary>
	/// Checks if an entity exists based on a given DTO.
	/// </summary>
	/// <param name="dto"></param>
	/// <returns>True if the entity exists.</returns>
	protected abstract void AssertExists(TDto dto);

	/// <summary>
	/// Checks if an entity exists based on a given DTO.
	/// </summary>
	/// <param name="dto"></param>
	/// <returns>True if the entity does not exist.</returns>
	protected abstract void AssertDoesNotExist(TDto dto);

	/// <summary>
	/// Checks if a deletion was successful.
	///
	/// Needs to be held separately, since we may need to check for more than the pure existence of the entity.
	/// </summary>
	/// <param name="entity"></param>
	/// <returns>True if the deletion was successful.</returns>
	protected abstract bool DeleteSuccessful(TEntity entity);

	/// <summary>
	/// Compares the result of a query request (including optional limit & offset) with the expected result of said query.
	/// </summary>
	/// <param name="queryResultDto"></param>
	/// <param name="limit"></param>
	/// <param name="offset"></param>
	/// <returns>True if the query's result matches the expectation.</returns>
	protected abstract bool CheckQueryResult(ConfigQueryResultDto<TDto> queryResultDto, int limit = 0, int offset = 0);

	/// <summary>
	/// Compares the result of a query request with sortings with the expected result of said query.
	/// </summary>
	/// <param name="queryResultDto"></param>
	/// <returns>True if the query's result matches the expectation.</returns>
	protected abstract bool CheckSortingQueryResult(ConfigQueryResultDto<TDto> queryResultDto);

	/// <summary>
	/// Compares the result of a query request with filters with the expected result of said query.
	/// </summary>
	/// <param name="queryResultDto"></param>
	/// <returns>True if the query's result matches the expectation.</returns>
	protected abstract bool CheckFilterQueryResult(ConfigQueryResultDto<TDto> queryResultDto);

	#endregion

	#region Tests

	#region Create

	[Fact(DisplayName = "Create Entity from DTO")]
	public async Task CreateTest()
	{
		var requestDto = CreateDto;
		var result = await ControllerInstance!.Create(requestDto);
		Assert.NotNull(result);
		
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);
		Assert.True(objectResult.Value!.GetType().IsAssignableTo(typeof(FrontendResponse)));

		dynamic? dynamicValue = objectResult.Value;
		Assert.NotNull(dynamicValue);
		
		var resultingInstance = dynamicValue!.Data;
		Assert.NotNull(resultingInstance);
		Assert.True(resultingInstance.GetType().IsAssignableTo(typeof(TDto)));
		AssertIsAsExpected(GetExpectedCreateDto() ?? requestDto, resultingInstance);
	}

	[Fact(DisplayName = "Fail to create Entity from invalid DTO")]
	public async Task InvalidDtoCreateTest()
	{
		var result = await ControllerInstance!.Create(InvalidCreateDto);
		Assert.NotNull(result);
		
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(400, objectResult.StatusCode);
		
		Assert.True(objectResult.Value!.GetType().IsAssignableTo(typeof(FrontendResponse)));
		AssertDoesNotExist(InvalidCreateDto);
	}

	[Fact(DisplayName = "Fail to create Entity that already exists")]
	public async Task AlreadyExistingCreateTest()
	{
		await using var databaseContext = Fixture.Context;
		
		await ControllerInstance!.Create(CreateDto);
		var result = await ControllerInstance!.Create(CreateDto);
		Assert.NotNull(result);
		databaseContext.ChangeTracker.Clear();

		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(400, objectResult.StatusCode);
		
		Assert.True(objectResult.Value!.GetType().IsAssignableTo(typeof(FrontendResponse)));
		AssertExists(CreateDto);
	}

	#endregion

	#region Update

	[Fact(DisplayName = "Update Entity from DTO")]
	public virtual async Task UpdateTest()
	{
		var entity = PrepareSingleEntity();
		var dto = GetUpdateDto(entity);

		var result = await ControllerInstance!.Update(entity.Id, dto);
		Assert.NotNull(result);
		
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);
		Assert.True(objectResult.Value!.GetType().IsAssignableTo(typeof(FrontendResponse)));

		dynamic? dynamicValue = objectResult.Value;
		Assert.NotNull(dynamicValue);
		
		var resultingInstance = dynamicValue!.Data;
		Assert.NotNull(resultingInstance);
		Assert.True(resultingInstance.GetType().IsAssignableTo(typeof(TDto)));
		AssertIsAsExpected(dto, resultingInstance);
	}

	[Fact(DisplayName = "Fail to update Entity that does not exist")]
	public async Task NonExistentEntityUpdateTest()
	{
		var result = await ControllerInstance!.Update(Guid.Empty, InvalidUpdateDto);
		Assert.NotNull(result);
		
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(404, objectResult.StatusCode);
		
		Assert.True(objectResult.Value!.GetType().IsAssignableTo(typeof(FrontendResponse)));
		AssertDoesNotExist(InvalidUpdateDto);
	}

	#endregion

	#region Delete

	[Fact(DisplayName = "Delete Entity")]
	public void DeleteTest()
	{
		using var databaseContext = Fixture.Context;
		
		var entity = PrepareSingleEntity();
		var result = ControllerInstance!.Delete(entity.Id);
		Assert.NotNull(result);
		databaseContext.ChangeTracker.Clear();
		
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);
		
		Assert.True(objectResult.Value!.GetType().IsAssignableTo(typeof(FrontendResponse)));
		Assert.True(DeleteSuccessful(entity));
	}

	[Fact(DisplayName = "Fail to delete Entity that does not exist")]
	public void NonExistentEntityDeleteTest()
	{
		var result = ControllerInstance!.Delete(Guid.NewGuid());
		Assert.NotNull(result);
		
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(404, objectResult.StatusCode);
		
		Assert.True(objectResult.Value!.GetType().IsAssignableTo(typeof(FrontendResponse)));
	}

	#endregion

	#region Query

	[Fact(DisplayName = "Query Entity items")]
	public async Task QueryTest()
	{
		await PrepareQueryDataAsync();

		var result = ControllerInstance!.Query(QueryParams);
		Assert.NotNull(result);

		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);
		
		JsonElement? json = objectResult.Value is FrontendResponse<JsonElement> response ? response.Data : null;
		var queryResult = json?.Deserialize<ConfigQueryResultDto<TDto>>(ConfigHelper.JsonOptionsCamel);
		Assert.NotNull(queryResult);
		Assert.True(CheckQueryResult(queryResult));
	}

	[Fact(DisplayName = "Query Entity items with limit and offset")]
	public async Task QueryLimitAndOffsetTest()
	{
		await PrepareQueryDataAsync();

		var result = ControllerInstance!.Query(QueryParamsWithLimitAndOffset);
		Assert.NotNull(result);

		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);
		
		JsonElement? json = objectResult.Value is FrontendResponse<JsonElement> response ? response.Data : null;
		var queryResult = json?.Deserialize<ConfigQueryResultDto<TDto>>(ConfigHelper.JsonOptionsCamel);
		Assert.NotNull(queryResult);
		Assert.True(CheckQueryResult(queryResult, QueryParamsWithLimitAndOffset.Limit, QueryParamsWithLimitAndOffset.Offset));
	}

	[Fact(DisplayName = "Query Entity items with sorting")]
	public async Task QuerySortingTest()
	{
		await PrepareQueryDataAsync();

		var result = ControllerInstance!.Query(QueryParamsWithSorting);
		Assert.NotNull(result);

		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);
		
		JsonElement? json = objectResult.Value is FrontendResponse<JsonElement> response ? response.Data : null;
		var queryResult = json?.Deserialize<ConfigQueryResultDto<TDto>>(ConfigHelper.JsonOptionsCamel);
		Assert.NotNull(queryResult);
		Assert.True(CheckSortingQueryResult(queryResult));
	}

	[Fact(DisplayName = "Query Entity items with filtering")]
	public async Task QueryFilterTest()
	{
		await PrepareQueryDataAsync();
		
		var result = ControllerInstance!.Query(QueryParamsWithFilter);
		Assert.NotNull(result);
		
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);
		
		JsonElement? json = objectResult.Value is FrontendResponse<JsonElement> response ? response.Data : null;
		var queryResult = json?.Deserialize<ConfigQueryResultDto<TDto>>(ConfigHelper.JsonOptionsCamel);
		Assert.NotNull(queryResult);
		Assert.True(CheckFilterQueryResult(queryResult));
	}

	[Fact(DisplayName = "Query Entity items without Parameters")]
	public async Task QueryFailTest()
	{
		await PrepareQueryDataAsync();
		
		var result = ControllerInstance!.Query(null!);
		Assert.NotNull(result);
		
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(400, objectResult.StatusCode);
		
		JsonElement? json = objectResult.Value is FrontendResponse<JsonElement> response ? response.Data : null;
		var queryResult = json?.Deserialize<ConfigQueryResultDto<TDto>>(ConfigHelper.JsonOptionsCamel);
		Assert.Null(queryResult);
	}

	#endregion

	#region Get

	[Fact(DisplayName = "Get Entity by Id")]
	public void GetTest()
	{
		var entity = PrepareSingleEntity();
		var result = ControllerInstance?.Get(entity.Id);
		Assert.NotNull(result);
		
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);
		
		dynamic? dynamicValue = objectResult.Value;
		var resultDto = dynamicValue?.Data;
		Assert.NotNull(resultDto);
		
		dynamic dEntity = entity;
		var expectedDto = dEntity.ToDto();
		AssertIsAsExpected(expectedDto, resultDto);
	}

	[Fact(DisplayName = "Get a non existing Entity by Id")]
	public void GetNonExistingTest()
	{
		var result = ControllerInstance!.Get(Guid.Empty);
		Assert.NotNull(result);
		
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(404, objectResult.StatusCode);
		
		var error = (objectResult.Value as FrontendResponse)?.Error;
		Assert.NotNull(error);
		Assert.NotEmpty(error.Value.ErrorMessage);
	}

	#endregion

	#endregion
}