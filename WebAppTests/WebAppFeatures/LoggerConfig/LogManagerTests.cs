using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.LoggerConfig;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Services;
using Microsoft.Extensions.DependencyInjection;
using Serilog.Events;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.LoggerConfig;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

[ExcludeFromCodeCoverage]
public class PostgresLogManagerTests(PostgresDatabaseFixture fixture) : LogManagerTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

/*[ExcludeFromCodeCoverage]
public class SqlServerLogManagerTests : LogManagerTests, IClassFixture<SqlServerDatabaseFixture>
{
	public SqlServerLogManagerTests(SqlServerDatabaseFixture fixture) : base(fixture)
	{
		// nothing
	}
}*/

[ExcludeFromCodeCoverage]
public abstract class LogManagerTests : IDisposable
{
	private readonly DatabaseFixture _fixture;

	private readonly CoreDatabaseContext _context;

	private readonly LogManager _logManager;

	protected LogManagerTests(DatabaseFixture fixture)
	{
		_fixture = fixture;

		_fixture.Services.AddSingleton<LogManager>();

		_fixture.BuildServices();

		_context = _fixture.Context;
		_logManager = _fixture.ServiceProvider.GetRequiredService<LogManager>();
	}

	[Trait("Category", "Logger Configuration Tests")]
	[Fact(DisplayName = "Creating Logger with no given Config")]
	public void CreateLoggerNoConfigGiven()
	{
		var logger = _logManager.GetLoggerForClass<LogManagerTests>();

		Assert.NotNull(logger);
		Assert.Equal(_logManager.DefaultLogLevel, _logManager.GetLogLevel(typeof(LogManagerTests).FullName!));
	}

	[Trait("Category", "Logger Configuration Tests")]
	[Fact(DisplayName = "Creating Logger with given Config")]
	public void CreateLoggerConfigGiven()
	{
		_context.Add(new LoggerConfigEntity()
		{
			LoggerSource = typeof(LogManagerTests).FullName!,
			Level = LogEventLevel.Error,
			IsActive = true
		});
		_context.SaveChanges();

		var logger = _logManager.GetLoggerForClass<LogManagerTests>();

		Assert.NotNull(logger);
		Assert.Equal(LogEventLevel.Error, _logManager.GetLogLevel(typeof(LogManagerTests).FullName!));
	}

	[Trait("Category", "Logger Configuration Tests")]
	[Fact(DisplayName = "Creating Logger with given Config for Namespace")]
	public void CreateLoggerNamespaceConfigGiven()
	{
		_context.Add(new LoggerConfigEntity
		{
			LoggerSource = "Levelbuild.Domain.WebAppTests.WebAppFeatures.LoggerConfig",
			Level = LogEventLevel.Debug,
			IsActive = true
		});
		_context.SaveChanges();

		var logger = _logManager.GetLoggerForClass<LogManagerTests>();

		Assert.NotNull(logger);
		Assert.Equal(LogEventLevel.Debug, _logManager.GetLogLevel(typeof(LogManagerTests).FullName!));
	}

	[Trait("Category", "Logger Configuration Tests")]
	[Fact(DisplayName = "Creating Logger with given Config for Group")]
	public void CreateLoggerGroupConfigGiven()
	{
		_context.Add(new LoggerConfigEntity
		{
			LoggerSource = "LoggerTests",
			Level = LogEventLevel.Debug,
			IsActive = true,
			SourceIsGroup = true
		});
		_context.SaveChanges();

		var logger = _logManager.GetLoggerForClass<LogManagerTests>(groupName: "LoggerTests");

		Assert.NotNull(logger);
		Assert.Equal(LogEventLevel.Debug, _logManager.GetLogLevel(typeof(LogManagerTests).FullName!));
	}

	[Trait("Category", "Logger Configuration Tests")]
	[Fact(DisplayName = "Getting an already created Logger")]
	public void GetCreatedLogger()
	{
		var logManagerLogger1 = _logManager.GetLoggerForClass<LogManagerTests>(LogEventLevel.Warning);

		var logManagerLogger2 = _logManager.GetLoggerForClass<LogManagerTests>(LogEventLevel.Debug);

		Assert.NotNull(logManagerLogger1);
		Assert.NotNull(logManagerLogger2);
		Assert.Equal(1, _logManager.LoggerCount);
		Assert.Equal(LogEventLevel.Warning, _logManager.GetLogLevel(typeof(LogManagerTests).FullName!));
	}

	[Trait("Category", "Logger Configuration Tests")]
	[Fact(DisplayName = "LogManager creates a new group config in the database")]
	public void CreateLoggerFromGroup()
	{
		var logManagerLogger1 = _logManager.GetLoggerForClass<PostgresDatabaseFixture>(LogEventLevel.Warning, "NewGroup");

		Assert.NotNull(logManagerLogger1);
		Assert.Equal(1, _logManager.LoggerCount);
		Assert.Equal(LogEventLevel.Warning, _logManager.GetLogLevel(typeof(PostgresDatabaseFixture).FullName!));
	}

	public void Dispose()
	{
		_context.Dispose();
		_fixture.CleanUp();
	}
}