using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.DataField;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.PageView.ListView;
using Levelbuild.Frontend.WebApp.Features.MultiPage.Controllers;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using ClosedXML.Excel;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos.ExcelPresets;
using Levelbuild.Entities.Features.FileUpload;
using Levelbuild.Entities;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.Progress.Interfaces;
using Microsoft.Extensions.Primitives;
using Moq;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.MultiPage;

[ExcludeFromCodeCoverage]
[Collection("PostgresDatabaseCollection")]
public class PostgresMultiPageControllerTests(PostgresDatabaseFixture fixture) : MultiPageControllerTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

// [ExcludeFromCodeCoverage]
// [Collection("SqlServerDatabaseCollection")]
// public class SqlServerMultiPageControllerTests : MultiPageControllerTests, IClassFixture<SqlServerDatabaseFixture>
// {
// 	public SqlServerMultiPageControllerTests(SqlServerDatabaseFixture fixture) : base(fixture)
// 	{
// 		// nothing
// 	}
// }

public abstract class MultiPageControllerTests : IntegrationTest
{
	private readonly MultiPageController _controllerInstance;

	private readonly List<DataElementDto> _data =
	[
		new DataElementDto("1", new Dictionary<string, object?>
		{
			{ "CreateDate", new DateTime(2024, 4, 4) }, { "Filename", "Program.cs" }, { "InSync", true }, { "Comment", "no comment" }
		})
		{
			Favorite = true
		},
		new DataElementDto("2", new Dictionary<string, object?>
		{
			{ "CreateDate", new DateTime(2022, 4, 4) }, { "Filename", "Detail.cs" }, { "InSync", false }, { "Comment", "a comment" }
		}),
		new DataElementDto("3", new Dictionary<string, object?>
		{
			{ "CreateDate", new DateTime(2023, 8, 3) }, { "Filename", "Baldur's Gate 3.exe" }, { "InSync", true }, { "Comment", "a bad comment" }
		}),
		new DataElementDto("4", new Dictionary<string, object?>
		{
			{ "CreateDate", new DateTime(1990, 1, 14) }, { "Filename", "Image.png" }, { "InSync", true }, { "Comment", "a good comment" }
		}),
		new DataElementDto("5", new Dictionary<string, object?>
		{
			{ "CreateDate", new DateTime(2016, 12, 5) }, { "Filename", "stderr.log" }, { "InSync", false }, { "Comment", "comment a comment" }
		}),
		new DataElementDto("6", new Dictionary<string, object?>
		{
			{ "CreateDate", new DateTime(2025, 4, 10) }, { "Filename", "InactiveDreams.mkv" }, { "InSync", true }, { "Comment", "discard a comment" }
		})
		{
			Inactive = true
		}
	];

	private readonly List<DataElementDto> _referenceData =
	[
		new DataElementDto("1", new Dictionary<string, object?> { { "ReferenceField", "Hallo" } }),
		new DataElementDto("2", new Dictionary<string, object?> { { "ReferenceField", "Schlumpf" } })
	];
	
	private readonly List<DataElementDto> _aggregationData =
	[
		new DataElementDto("1", new Dictionary<string, object?> { { "ReferenceField", "Hallo" } }),
		new DataElementDto("2", new Dictionary<string, object?> { { "ReferenceField", "Schlumpf" } })
	];

	protected MultiPageControllerTests(DatabaseFixture fixture) : base(fixture, null, true)
	{
		// Create a mock Redis Progress Service
		var mockRedisProgressService = new Mock<IRedisProgressService>();
		_controllerInstance = new MultiPageController(LogManager, UserManager, Fixture.ContextFactory, VersionReader, LocalizerFactory);
	}

	#region Tests
	
	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Aggregate elements Sum")]
	public async Task AggregationSumTest()
	{
		var idTupel = await PrepareAggregationQuery("source", "field");

		var response = await _controllerInstance.Aggregation(idTupel.sourceId, idTupel.fieldId, "Sum", new QueryParamsDto());
		var result = response.Result as ObjectResult; 
		var dto = (result?.Value as FrontendResponse<AggregationQueryResultDto>)?.Data;
		
		Assert.NotNull(response);
		Assert.NotNull(result);
		Assert.Equal(200, result.StatusCode);
		Assert.NotNull(dto);
		Assert.Equal(3, dto.Value);
	}
	
	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Aggregate elements Average")]
	public async Task AggregationAverageTest()
	{
		var idTupel = await PrepareAggregationQuery("source", "field");

		var response = await _controllerInstance.Aggregation(idTupel.sourceId, idTupel.fieldId, "Average", new QueryParamsDto());
		var result = response.Result as ObjectResult; 
		var dto = (result?.Value as FrontendResponse<AggregationQueryResultDto>)?.Data;
		
		Assert.NotNull(response);
		Assert.NotNull(result);
		Assert.Equal(200, result.StatusCode);
		Assert.NotNull(dto);
		Assert.Equal(1.5, dto.Value);
	}
	
	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Aggregate elements Min")]
	public async Task AggregationMinTest()
	{
		var idTupel = await PrepareAggregationQuery("source", "field");

		var response = await _controllerInstance.Aggregation(idTupel.sourceId, idTupel.fieldId, "Min", new QueryParamsDto());
		var result = response.Result as ObjectResult; 
		var dto = (result?.Value as FrontendResponse<AggregationQueryResultDto>)?.Data;
		
		Assert.NotNull(response);
		Assert.NotNull(result);
		Assert.Equal(200, result.StatusCode);
		Assert.NotNull(dto);
		Assert.Equal(1, dto.Value);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Aggregate elements Max")]
	public async Task AggregationMaxTest()
	{
		var idTupel = await PrepareAggregationQuery("source", "field");

		var response = await _controllerInstance.Aggregation(idTupel.sourceId, idTupel.fieldId, "Max", new QueryParamsDto());
		var result = response.Result as ObjectResult; 
		var dto = (result?.Value as FrontendResponse<AggregationQueryResultDto>)?.Data;
		
		Assert.NotNull(response);
		Assert.NotNull(result);
		Assert.Equal(200, result.StatusCode);
		Assert.NotNull(dto);
		Assert.Equal(2, dto.Value);
	}
	
	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Aggregate elements with Filter")]
	public async Task AggregationFilterTest()
	{
		var idTupel = await PrepareAggregationQuery("source", "field");

		var response = await _controllerInstance.Aggregation(idTupel.sourceId, idTupel.fieldId, "Sum", new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>()
			{
				new()
				{
					FilterColumn = "field",
					Operator = QueryParamFilterOperator.Equals,
					CompareValue = 1
				}
			}
		});
		var result = response.Result as ObjectResult; 
		var dto = (result?.Value as FrontendResponse<AggregationQueryResultDto>)?.Data;
		
		Assert.NotNull(response);
		Assert.NotNull(result);
		Assert.Equal(200, result.StatusCode);
		Assert.NotNull(dto);
		Assert.Equal(1, dto.Value);
	}
	
	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Aggregate elements Lookup")]
	public async Task AggregationLookupTest()
	{
		var idTupel = await PrepareAggregationQueryLookup("source", "field", "lookupField", "virtualField");

		var response = await _controllerInstance.Aggregation(idTupel.sourceId, idTupel.lookupId, "Sum", new QueryParamsDto());
		var result = response.Result as ObjectResult; 
		var dto = (result?.Value as FrontendResponse<AggregationQueryResultDto>)?.Data;
		
		Assert.NotNull(response);
		Assert.NotNull(result);
		Assert.Equal(200, result.StatusCode);
		Assert.NotNull(dto);
		Assert.Equal(3, dto.Value);
	}
	
	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Aggregate elements Virtual")]
	public async Task AggregationVirtualTest()
	{
		var idTupel = await PrepareAggregationQueryLookup("source", "field", "lookupField", "virtualField");

		var response = await _controllerInstance.Aggregation(idTupel.sourceId, idTupel.virtualId, "Sum", new QueryParamsDto());
		var result = response.Result as ObjectResult; 
		var dto = (result?.Value as FrontendResponse<AggregationQueryResultDto>)?.Data;
		
		Assert.NotNull(response);
		Assert.NotNull(result);
		Assert.Equal(200, result.StatusCode);
		Assert.NotNull(dto);
		Assert.Equal(3, dto.Value);
	}
	
	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query MultiPage items")]
	public async Task QueryTest()
	{
		var result = await QueryDataWithParamsAsync(new QueryParamsDto()
		{
			Sortings = new List<QueryParamSortingDto>()
			{
				new()
				{
					OrderColumn = "Filename",
					Direction = SortDirection.Asc
				}
			}
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;
		var mutatedNamesList = queryResult!.Rows.ToList().ConvertAll(config => config.Values["Filename"]);
		var expectedList = new List<string>
		{
			_data[2].Values["Filename"]!.ToString()!, _data[1].Values["Filename"]!.ToString()!, _data[3].Values["Filename"]!.ToString()!,
			_data[0].Values["Filename"]!.ToString()!, _data[4].Values["Filename"]!.ToString()!
		};

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);
		Assert.Equal(5, queryResult.CountTotal);
		Assert.Equal(5, queryResult.Rows.Count);

		int index;
		for (index = 0; index < mutatedNamesList.Count; index++)
		{
			Assert.Equal(expectedList[index], mutatedNamesList[index]);
		}

		Assert.False(queryResult.Rows[0].Inactive);
		Assert.False(queryResult.Rows[1].Inactive);
		Assert.False(queryResult.Rows[2].Inactive);
		Assert.False(queryResult.Rows[3].Inactive);
		Assert.False(queryResult.Rows[4].Inactive);

		Assert.False(queryResult.Rows[0].Favorite);
		Assert.False(queryResult.Rows[1].Favorite);
		Assert.False(queryResult.Rows[2].Favorite);
		Assert.True(queryResult.Rows[3].Favorite);
		Assert.False(queryResult.Rows[4].Favorite);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query MultiPage items with limit and offset")]
	public async Task QueryLimitAndOffsetTest()
	{
		var result = await QueryDataWithParamsAsync(new QueryParamsDto()
		{
			Limit = 2,
			Offset = 1,
			Sortings = new List<QueryParamSortingDto>()
			{
				new()
				{
					OrderColumn = "CreateDate",
					Direction = SortDirection.Asc
				}
			}
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);
		Assert.Equal(5, queryResult.CountTotal);
		Assert.Equal(2, queryResult.Rows.Count);

		var expected = _data.OrderBy(entry => entry.Values["CreateDate"])
			.Select(entry => entry.Values["Filename"]!.ToString())
			.ToList();
		Assert.Equivalent(new List<string> { expected[1]!, expected[2]! },
						  queryResult.Rows.ToList().ConvertAll<string>(config => config.Values["Filename"]!.ToString()!),
						  true);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query MultiPage items with sorting (In Sync desc, comment asc)")]
	public async Task QuerySortingTest()
	{
		var result = await QueryDataWithParamsAsync(new QueryParamsDto()
		{
			Sortings = new List<QueryParamSortingDto>()
			{
				new()
				{
					OrderColumn = "InSync",
					Direction = SortDirection.Desc
				},
				new()
				{
					OrderColumn = "Comment",
					Direction = SortDirection.Asc
				}
			}
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);
		Assert.Equal(5, queryResult.CountTotal);
		Assert.Equal(5, queryResult.Rows.Count);
		Assert.Equivalent(
			new List<string>()
			{
				_data[2].Values["Filename"]!.ToString()!, _data[3].Values["Filename"]!.ToString()!, _data[0].Values["Filename"]!.ToString()!,
				_data[1].Values["Filename"]!.ToString()!,
				_data[4].Values["Filename"]!.ToString()!
			},
			queryResult.Rows.ToList().ConvertAll<string>(config => config.Values["Filename"]!.ToString()!),
			true);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query MultiPage items with filtering by Filename")]
	public async Task QueryFilterTest()
	{
		var result = await QueryDataWithParamsAsync(new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>()
			{
				new()
				{
					FilterColumn = "Filename",
					Operator = QueryParamFilterOperator.Equals,
					CompareValue = "Detail.cs"
				}
			}
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(1, queryResult.CountTotal);
		Assert.Single(queryResult.Rows);
		Assert.Equal(_data[1].Values["Filename"], queryResult.Rows.First().Values["Filename"]);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query MultiPage items with filtering by Inactive")]
	public async Task QueryFilterInactiveTest()
	{
		var result = await QueryDataWithParamsAsync(new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>()
			{
				new()
				{
					FilterColumn = "",
					Operator = QueryParamFilterOperator.Inactive
				}
			},
			Sortings = new List<QueryParamSortingDto>()
			{
				new()
				{
					OrderColumn = "Filename",
					Direction = SortDirection.Asc
				}
			}
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(6, queryResult.CountTotal);
		Assert.Equal(_data[5].Values["Filename"], queryResult.Rows[3].Values["Filename"]);

		Assert.False(queryResult.Rows[0].Inactive);
		Assert.False(queryResult.Rows[1].Inactive);
		Assert.False(queryResult.Rows[2].Inactive);
		Assert.True(queryResult.Rows[3].Inactive);
		Assert.False(queryResult.Rows[4].Inactive);
		Assert.False(queryResult.Rows[5].Inactive);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query MultiPage items with filtering by Favorite")]
	public async Task QueryFilterFavoriteTest()
	{
		var result = await QueryDataWithParamsAsync(new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>()
			{
				new()
				{
					FilterColumn = "",
					Operator = QueryParamFilterOperator.Favorite
				}
			}
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(1, queryResult.CountTotal);
		Assert.Single(queryResult.Rows);
		Assert.Equal(_data[0].Values["Filename"], queryResult.Rows.First().Values["Filename"]);
		Assert.True(queryResult.Rows[0].Favorite);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query MultiPage items with filtering by Favorite and allow Inactive ones")]
	public async Task QueryFilterInactiveAndFavoriteTest()
	{
		var result = await QueryDataWithParamsAsync(new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>()
			{
				new()
				{
					FilterColumn = "",
					Operator = QueryParamFilterOperator.Favorite
				},
				new()
				{
					FilterColumn = "",
					Operator = QueryParamFilterOperator.Inactive
				}
			}
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(1, queryResult.CountTotal);
		Assert.Single(queryResult.Rows);
		Assert.Equal(_data[0].Values["Filename"], queryResult.Rows.First().Values["Filename"]);
		Assert.True(queryResult.Rows[0].Favorite);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query MultiPage items with filtering by Filename twice")]
	public async Task QueryOrFilterTest()
	{
		var result = await QueryDataWithParamsAsync(new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>()
			{
				new()
				{
					FilterColumn = "Filename",
					Operator = QueryParamFilterOperator.Equals,
					CompareValue = "Detail.cs"
				},
				new()
				{
					FilterColumn = "Filename",
					Operator = QueryParamFilterOperator.Equals,
					CompareValue = "Program.cs"
				}
			},
			Sortings = new List<QueryParamSortingDto>()
			{
				new()
				{
					OrderColumn = "Filename",
					Direction = SortDirection.Asc
				}
			},
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(2, queryResult.CountTotal);
		Assert.Equal(2, queryResult.Rows.Count);
		Assert.Equal("Detail.cs", queryResult.Rows[0].Values["Filename"]);
		Assert.Equal("Program.cs", queryResult.Rows[1].Values["Filename"]);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query MultiPage items with sorting, filtering and limit + offset by Filename")]
	public async Task QueryCompleteTest()
	{
		var result = await QueryDataWithParamsAsync(new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>()
			{
				new()
				{
					FilterColumn = "InSync",
					Operator = QueryParamFilterOperator.Equals,
					CompareValue = true
				}
			},
			Sortings = new List<QueryParamSortingDto>()
			{
				new()
				{
					OrderColumn = "Filename",
					Direction = SortDirection.Desc
				}
			},
			Limit = 2,
			Offset = 1
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(3, queryResult.CountTotal);
		Assert.Equal(2, queryResult.Rows.Count);
		Assert.Equal(_data[3].Values["Filename"], queryResult.Rows[0].Values["Filename"]);
		Assert.Equal(_data[2].Values["Filename"], queryResult.Rows[1].Values["Filename"]);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query MultiPage items with sorting, filtering and limit + offset by Lookup and Virtual column")]
	public async Task QueryCompleteLookupTest()
	{
		var result = await QueryDataWithParamsAsync(new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>()
			{
				new()
				{
					FilterColumn = "LookupField.Display",
					Operator = QueryParamFilterOperator.NotEquals,
					CompareValue = ""
				}
			},
			Sortings = new List<QueryParamSortingDto>()
			{
				new()
				{
					OrderColumn = "VirtualField",
					Direction = SortDirection.Desc
				}
			},
			Limit = 2,
			Offset = 1
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(5, queryResult.CountTotal);
		Assert.Equal(2, queryResult.Rows.Count);
		Assert.Equal("Schlumpf", queryResult.Rows[0].Values["VirtualField"]);
		Assert.Equal("Hallo", queryResult.Rows[1].Values["VirtualField"]);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Group by a string field")]
	public async Task GroupByFilenameTest()
	{
		var result = (await PrepareGroupByFilterFieldValuesWithParamsAsync("Filename")).Result;

		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<FilterFieldQueryItemResultDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(3, queryResult.CountTotal);
		Assert.Equal(3, queryResult.Rows.Count);
		Assert.Equal(_data[2].Values["Filename"], queryResult.Rows[0].Value);
		Assert.Equal(_data[2].Values["Filename"], queryResult.Rows[0].Label);
		Assert.Equal(1, queryResult.Rows[0].Count);
		Assert.Equal(_data[3].Values["Filename"], queryResult.Rows[1].Value);
		Assert.Equal(_data[3].Values["Filename"], queryResult.Rows[1].Label);
		Assert.Equal(1, queryResult.Rows[1].Count);
		Assert.Equal(_data[0].Values["Filename"], queryResult.Rows[2].Value);
		Assert.Equal(_data[0].Values["Filename"], queryResult.Rows[2].Label);
		Assert.Equal(1, queryResult.Rows[2].Count);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Group by a lookup field")]
	public async Task GroupByLookupTest()
	{
		var preparationResult = await PrepareGroupByFilterFieldValuesWithParamsAsync("LookupField");
		var result = preparationResult.Result;


		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<FilterFieldQueryItemResultDto>>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(2, queryResult.CountTotal);
		Assert.Equal(2, queryResult.Rows.Count);
		Assert.Equal(_referenceData[0].Values["ReferenceField"], queryResult.Rows[0].Value);
		Assert.Equal(_referenceData[0].Values["ReferenceField"], queryResult.Rows[0].Label);
		Assert.Equal(2, queryResult.Rows[0].Count);
		Assert.Equal(_referenceData[1].Values["ReferenceField"], queryResult.Rows[1].Value);
		Assert.Equal(_referenceData[1].Values["ReferenceField"], queryResult.Rows[1].Label);
		Assert.Equal(1, queryResult.Rows[1].Count);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Query elements that contains stacking virtual field")]
	public async Task QueryStackingVirtualTest()
	{
		var result = await QueryStackingVirtualDataWithParamsAsync(new QueryParamsDto()
		{
			Sortings = new List<QueryParamSortingDto>()
			{
				new()
				{
					OrderColumn = "Filename",
					Direction = SortDirection.Asc
				}
			}
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryResultDto<DataElementDto>>)?.Data;
		var mutatedNamesList = queryResult!.Rows.ToList().ConvertAll(config => config.Values["Filename"]);
		var expectedList = new List<string>()
		{
			_data[2].Values["Filename"]!.ToString()!, _data[1].Values["Filename"]!.ToString()!, _data[3].Values["Filename"]!.ToString()!,
			_data[0].Values["Filename"]!.ToString()!,
			_data[4].Values["Filename"]!.ToString()!
		};

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);
		Assert.Equal(5, queryResult.CountTotal);
		Assert.Equal(5, queryResult.Rows.Count);

		int index;
		for (index = 0; index < mutatedNamesList.Count; index++)
		{
			Assert.Equal(expectedList[index], mutatedNamesList[index]);
		}
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Count MultiPage items")]
	public async Task CountTest()
	{
		var result = await CountDataWithParamsAsync(new QueryParamsDto());
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryCountDto>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(5, queryResult.Count);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Count MultiPage items with filtering by Filename twice")]
	public async Task CountWithFilterTest()
	{
		var result = await CountDataWithParamsAsync(new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>()
			{
				new()
				{
					FilterColumn = "Filename",
					Operator = QueryParamFilterOperator.Equals,
					CompareValue = "Detail.cs"
				},
				new()
				{
					FilterColumn = "Filename",
					Operator = QueryParamFilterOperator.Equals,
					CompareValue = "Program.cs"
				}
			}
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryCountDto>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(2, queryResult.Count);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Count MultiPage items with limit and offset ignored")]
	public async Task CountWithLimitAndOffsetTest()
	{
		var result = await CountDataWithParamsAsync(new QueryParamsDto()
		{
			Limit = 3,
			Offset = 2
		});
		var code = result.StatusCode;
		var queryResult = (result.Value as FrontendResponse<QueryCountDto>)?.Data;

		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(queryResult);

		Assert.Equal(5, queryResult.Count);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Stream Export MultiPage Data to Excel")]
	public async Task ExportDataToExcelTest()
	{
	    // Arrange: prepare data and setup
	    await using var db = Fixture.Context;
	    var currentCustomer = await UserManager.GetCurrentCustomerAsync();

	    // Create source and fields
	    var source = EntityCreation.CreateDataSource(db, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id, "ExcelExportSource");
	    var fieldName = "ExportField";
	    var fieldLength = 100;
	    var fieldDto = new DataFieldDto { Type = DataType.String, Name = fieldName, DataSourceId = source.Id, Length = fieldLength };
	    db.DataFields.Add(DataFieldEntity.FromDto(fieldDto, null, db));
	    await db.SaveChangesAsync();
	    db.ChangeTracker.Clear();

	    // Fetch field and create in the database
	    var field = db.DataFields.First(f => f.DataSourceId == source.Id && f.Name == fieldName);
	    field.CreateField();

	    // Add data elements to export
	    var origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.System, 0, "Test");
	    var groups = new List<string> { "testgroup" };

	    // Create 3 elements for export
	    for (int i = 1; i <= 3; i++)
		{
			await source.CreateElementAsync(
				new DataStoreElementData(
					new Dictionary<string, object?> { { fieldName, $"Value{i}" } },
					groups),
				origin);
		}

	    // Create export request without specifying SelectedIds
	    var exportRequest = new ExportRequestDto
	    {
	        DataSourceId = source.Id,
	        Columns = new List<PresetResultDto>
	        {
	            new()
	            {
	                key = fieldName,
	                display = fieldName,
	                fieldType = "String",
	                fieldId = field.Id
	            }
	        }
	    };

	    // Serialize the export request to JSON
	    var jsonBody = JsonConvert.SerializeObject(exportRequest);
	    var bodyBytes = System.Text.Encoding.UTF8.GetBytes(jsonBody);
	    var bodyStream = new MemoryStream(bodyBytes);

	    // Create a process ID for Redis progress tracking
	    var processId = Guid.NewGuid().ToString();

	    // Create a mock Redis Progress Service
	    var mockRedisProgressService = new Mock<IRedisProgressService>();

	    // Set up request context (identical to the working test)
	    _controllerInstance.ControllerContext = new ControllerContext
	    {
	        HttpContext = new DefaultHttpContext()
	    };
	    _controllerInstance.ControllerContext.HttpContext.Request.ContentType = "application/json";
	    _controllerInstance.ControllerContext.HttpContext.Request.Body = bodyStream;
	    _controllerInstance.ControllerContext.HttpContext.Request.ContentLength = bodyBytes.Length;
	    _controllerInstance.ControllerContext.HttpContext.Request.Headers["X-Process-ID"] = processId;

	    // Act
	    var result = await _controllerInstance.ExportExcelData(mockRedisProgressService.Object);

	    // Assert
	    Assert.NotNull(result);

	    // Check if the result is an ObjectResult with status code 500 (error)
	    if (result is ObjectResult objResult && objResult.StatusCode == 500)
	    {
	        // If we got an error, output the error message for debugging
	        var errorObj = objResult.Value;
	        var errorProp = errorObj?.GetType().GetProperty("error");
	        var errorMsg = errorProp?.GetValue(errorObj)?.ToString() ?? "Unknown error";
	        Assert.Fail($"Export failed with error: {errorMsg}");
	        return;
	    }

	    // The method should return a FileContentResult directly
	    var fileResult = Assert.IsType<FileContentResult>(result);

	    // Verify file type and content
	    Assert.Equal("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileResult.ContentType);
	    Assert.NotNull(fileResult.FileDownloadName);
	    Assert.True(fileResult.FileContents.Length > 0);

	    // Analyze Excel content
	    using var stream = new MemoryStream(fileResult.FileContents);
	    using var workbook = new XLWorkbook(stream);

	    // Verify workbook structure
	    Assert.True(workbook.Worksheets.Count > 0);
	    var worksheet = workbook.Worksheet(1);

	    // Check header
	    var headerValue = worksheet.Cell(1, 1).GetString();
	    Assert.Contains(fieldName, headerValue);

	    // Check row count - should be 4 (header + 3 data rows)
	    Assert.Equal(4, worksheet.RowsUsed().Count());

	    // Verify the exported data contains all values
	    var dataValues = new List<string>();
	    for (int row = 2; row <= worksheet.RowsUsed().Count(); row++)
	    {
	        dataValues.Add(worksheet.Cell(row, 1).GetString());
	    }

	    // Verify all values are present
	    Assert.Contains("Value1", dataValues);
	    Assert.Contains("Value2", dataValues);
	    Assert.Contains("Value3", dataValues);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Stream Export with Selected IDs Only")]
	public async Task ExportWithSelectedIdsTest()
	{
		// Arrange: prepare data and setup
		await using var db = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// Create source and fields
		var source = EntityCreation.CreateDataSource(db, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id, "ExportSelectedIdsSource");
		var fieldName = "SelectedField";
		var fieldLength = 100;
		var fieldDto = new DataFieldDto { Type = DataType.String, Name = fieldName, DataSourceId = source.Id, Length = fieldLength };
		db.DataFields.Add(DataFieldEntity.FromDto(fieldDto, null, db));
		await db.SaveChangesAsync();
		db.ChangeTracker.Clear();

		// Fetch field and create in the database
		var field = db.DataFields.First(f => f.DataSourceId == source.Id && f.Name == fieldName);
		field.CreateField();

		// Add multiple data elements to export
		var origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.System, 0, "Test");
		var groups = new List<string> { "testgroup" };
		var elementIds = new List<string>();
		var allElementIds = new List<string>();

		// Create 5 elements but only select 2 for export
		for (int i = 1; i <= 5; i++)
		{
			var elementResult = await source.CreateElementAsync(
				new DataStoreElementData(
					new Dictionary<string, object?> { { fieldName, $"SelectedValue{i}" } },
					groups),
				origin);

			// Store the element ID
			allElementIds.Add(elementResult.ElementId);

			if (i <= 2) // Only select first two elements
			{
				elementIds.Add(elementResult.ElementId);
			}
		}

		// Verify all elements were created
		Assert.Equal(5, allElementIds.Count);
		Assert.Equal(2, elementIds.Count);

		// Create export request with selected IDs
		var exportRequest = new ExportRequestDto
		{
			DataSourceId = source.Id,
			Columns = new List<PresetResultDto>
			{
				new()
				{
					key = fieldName, // Internal field identifier/database column name
					display = field.Name, // Actual display name from the data source
					fieldType = "String",
					fieldId = field.Id
				}
			},
			SelectedIds = elementIds
		};

		// Serialize the export request to JSON
		var jsonBody = JsonConvert.SerializeObject(exportRequest);
		var bodyBytes = System.Text.Encoding.UTF8.GetBytes(jsonBody);
		var bodyStream = new MemoryStream(bodyBytes);

		// Create a process ID for Redis progress tracking
		var processId = Guid.NewGuid().ToString();

		// Create a mock Redis Progress Service
		var mockRedisProgressService = new Mock<IRedisProgressService>();

		// Set up request with content type, body and process ID
		_controllerInstance.ControllerContext = new ControllerContext
		{
			HttpContext = new DefaultHttpContext()
		};
		_controllerInstance.ControllerContext.HttpContext.Request.ContentType = "application/json";
		_controllerInstance.ControllerContext.HttpContext.Request.Body = bodyStream;
		_controllerInstance.ControllerContext.HttpContext.Request.ContentLength = bodyBytes.Length;
		_controllerInstance.ControllerContext.HttpContext.Request.Headers["X-Process-ID"] = processId;

		// Act - Call the export method
		var result = await _controllerInstance.ExportExcelData(mockRedisProgressService.Object);

		// Assert
		Assert.NotNull(result);

		// Check if the result is an ObjectResult with status code 500 (error)
		if (result is ObjectResult objResult && objResult.StatusCode == 500)
		{
			// If we got an error, output the error message for debugging
			var errorObj = objResult.Value;
			var errorProp = errorObj?.GetType().GetProperty("error");
			var errorMsg = errorProp?.GetValue(errorObj)?.ToString() ?? "Unknown error";
			Assert.Fail($"Export failed with error: {errorMsg}");
			return;
		}

		// The method should return a FileContentResult directly
		var fileResult = Assert.IsType<FileContentResult>(result);

		// Verify file type and content
		Assert.Equal("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileResult.ContentType);
		Assert.NotNull(fileResult.FileDownloadName);
		Assert.True(fileResult.FileContents.Length > 0);

		// Analyze Excel content
		using var stream = new MemoryStream(fileResult.FileContents);
		using var workbook = new XLWorkbook(stream);

		// Verify workbook structure
		Assert.True(workbook.Worksheets.Count > 0);
		var worksheet = workbook.Worksheet(1);

		// Check header - should contain display value not the key
		var headerValue = worksheet.Cell(1, 1).GetString();
		Assert.Contains(field.Name, headerValue);

		// Check row count - should be 3 (header + 2 selected rows)
		Assert.Equal(3, worksheet.RowsUsed().Count());

		// Verify the exported data contains only selected values
		var dataValues = new List<string>();
		for (int row = 2; row <= worksheet.RowsUsed().Count(); row++)
		{
			dataValues.Add(worksheet.Cell(row, 1).GetString());
		}

		// Verify only selected values are present
		Assert.Contains("SelectedValue1", dataValues);
		Assert.Contains("SelectedValue2", dataValues);
		Assert.DoesNotContain("SelectedValue3", dataValues);
		Assert.DoesNotContain("SelectedValue4", dataValues);
		Assert.DoesNotContain("SelectedValue5", dataValues);

		// Verify exact count of exported data rows matches selected IDs
		Assert.Equal(elementIds.Count, dataValues.Count);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Stream Export with Invalid Data Source")]
	public async Task ExportWithInvalidDataSourceTest()
	{
		// Arrange: prepare invalid data source configuration in request
		var invalidFieldId = Guid.NewGuid();

		// Create export request with invalid field ID
		var exportRequest = new ExportRequestDto
		{
			Columns = new List<PresetResultDto>
			{
				new() {
					key = "NonExistentField",
					display = "NonExistentField",
					fieldType = "String",
					fieldId = invalidFieldId
				}
			}
		};

		// Serialize the export request to JSON
		var jsonBody = JsonConvert.SerializeObject(exportRequest);
		var bodyBytes = System.Text.Encoding.UTF8.GetBytes(jsonBody);
		var bodyStream = new MemoryStream(bodyBytes);

		// Create a process ID for Redis progress tracking
		var processId = Guid.NewGuid().ToString();

		// Create a mock Redis Progress Service
		var mockRedisProgressService = new Mock<IRedisProgressService>();

		// Set up controller context with request body
		_controllerInstance.ControllerContext = new ControllerContext
		{
			HttpContext = new DefaultHttpContext()
		};
		_controllerInstance.ControllerContext.HttpContext.Request.Body = bodyStream;
		_controllerInstance.ControllerContext.HttpContext.Request.ContentLength = bodyBytes.Length;
		_controllerInstance.ControllerContext.HttpContext.Request.Headers["X-Process-ID"] = processId;

		// Act
		// The controller now handles the error internally instead of throwing an exception
		var result = await _controllerInstance.ExportExcelData(mockRedisProgressService.Object);

		// Assert
		// Verify the result is an ObjectResult with status code 400
		var statusCodeResult = Assert.IsType<ObjectResult>(result);
		Assert.Equal(400, statusCodeResult.StatusCode);

		// Verify the error response contains error information
		Assert.NotNull(statusCodeResult.Value);
		var errorResponse = statusCodeResult.Value;

		// Check if the response has an error property
		var errorProperty = errorResponse.GetType().GetProperty("error");
		Assert.NotNull(errorProperty);

		// Verify the error message is not empty
		var errorValue = errorProperty.GetValue(errorResponse);
		Assert.NotNull(errorValue);
		Assert.NotEmpty(errorValue.ToString());
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Stream Export with Invalid Column Configuration")]
	public async Task ExportWithInvalidColumnConfigurationTest()
	{
		// Arrange: create a source but send empty columns in the request
		await using var db = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// Create source and fields
		var source = EntityCreation.CreateDataSource(db, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id, "InvalidColumnsSource");
		var fieldName = "ValidField";
		var fieldLength = 100;
		var fieldDto = new DataFieldDto { Type = DataType.String, Name = fieldName, DataSourceId = source.Id, Length = fieldLength };
		db.DataFields.Add(DataFieldEntity.FromDto(fieldDto, null, db));
		await db.SaveChangesAsync();
		db.ChangeTracker.Clear();

		// Fetch field and create in the database
		var field = db.DataFields.First(f => f.DataSourceId == source.Id && f.Name == fieldName);
		field.CreateField();

		// Create export request with empty columns
		var exportRequest = new ExportRequestDto
		{
			Columns = new List<PresetResultDto>() // Empty columns list
		};

		// Serialize the export request to JSON
		var jsonBody = JsonConvert.SerializeObject(exportRequest);
		var bodyBytes = System.Text.Encoding.UTF8.GetBytes(jsonBody);
		var bodyStream = new MemoryStream(bodyBytes);

		// Create a process ID for Redis progress tracking
		var processId = Guid.NewGuid().ToString();

		// Create a mock Redis Progress Service
		var mockRedisProgressService = new Mock<IRedisProgressService>();

		// Set up the controller context with the request body and process ID header
		_controllerInstance.ControllerContext = new ControllerContext
		{
			HttpContext = new DefaultHttpContext()
		};
		_controllerInstance.ControllerContext.HttpContext.Request.Body = bodyStream;
		_controllerInstance.ControllerContext.HttpContext.Request.ContentLength = bodyBytes.Length;
		_controllerInstance.ControllerContext.HttpContext.Request.Headers["X-Process-ID"] = processId;

		// Act
		// The controller now handles the error internally instead of throwing an exception
		var result = await _controllerInstance.ExportExcelData(mockRedisProgressService.Object);

		// Assert
		// Verify the result is a BadRequestObjectResult with status code 400
		var statusCodeResult = Assert.IsType<BadRequestObjectResult>(result);
		Assert.Equal(400, statusCodeResult.StatusCode);

		// Verify the error response contains information
		Assert.NotNull(statusCodeResult.Value);

		// Just verify that the value is not empty, regardless of its type
		Assert.NotEmpty(statusCodeResult.Value?.ToString() ?? "");
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Import MultiPage Data from Excel")]
	public async Task ImportDataFromExcelTest()
	{
		// Arrange
		await using var db = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// Create source and fields matching Excel structure
		var source = EntityCreation.CreateDataSource(db, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id, "ExcelImportSource");
		var fieldName = "ImportField";
		var fieldLength = 100;
		var fieldDto = new DataFieldDto { Type = DataType.String, Name = fieldName, DataSourceId = source.Id, Length = fieldLength };
		db.DataFields.Add(DataFieldEntity.FromDto(fieldDto, null, db));
		await db.SaveChangesAsync();
		db.ChangeTracker.Clear();

		// Fetch field and create in the database
		var field = db.DataFields.First(f => f.DataSourceId == source.Id && f.Name == fieldName);
		field.CreateField();

		// Prepare Excel file in memory stream
		using var workbook = new XLWorkbook();
		var worksheet = workbook.Worksheets.Add("Sheet1");

		// Add header and data
		worksheet.Cell(1, 1).Value = fieldName;
		worksheet.Cell(2, 1).Value = "ImportedValue";

		using var stream = new MemoryStream();
		workbook.SaveAs(stream);
		stream.Position = 0;

		// Create form file for import
		var formFile = new FormFile(stream, 0, stream.Length, "excelFile", "import.xlsx")
		{
			Headers = new HeaderDictionary(),
			ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
		};

		// Create mock Redis Progress Service
		var processId = Guid.NewGuid().ToString();

		// Create a mock Redis Progress Service
		var mockRedisProgressService = new Mock<IRedisProgressService>();

		// Setup import parameters with column mappings
		var importParams = new ExcelImportParametersDto
		{
			DataSourceId = source.Id,
			IsFirstLineHeader = true,
			MatchBy = new List<string>(),
			// Add selected mappings to map Excel columns to database fields
			SelectedMappings = new List<ExcelColumnMappingDto>
			{
				new ExcelColumnMappingDto
				{
					index = 0, // First column (0-based index)
					ColumnKey = fieldName,
					DataType = "String"
				}
			}
		};

		// Act
		// Set up controller with form file and process ID header
		_controllerInstance.ControllerContext = new ControllerContext
		{
			HttpContext = new DefaultHttpContext()
		};

		// Add process ID header
		_controllerInstance.ControllerContext.HttpContext.Request.Headers["X-Process-ID"] = processId;

		// Create form collection with the file and import parameters
		var formFileCollection = new FormFileCollection { formFile };
		var formDict = new Dictionary<string, StringValues>
		{
			["importParams"] = JsonConvert.SerializeObject(importParams)
		};
		_controllerInstance.ControllerContext.HttpContext.Request.Form = new FormCollection(formDict, formFileCollection);

		// Call the import method with Redis Progress Service
		var result = await _controllerInstance.ImportExcelData(mockRedisProgressService.Object);

		// Assert
		Assert.NotNull(result);
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);

		var frontendResponse = objectResult.Value as FrontendResponse;
		Assert.NotNull(frontendResponse);
		Assert.Null(frontendResponse.Error);             // Verify no error is present
		Assert.Empty(frontendResponse.ValidationErrors); // Verify no validation errors

		// Query for elements in the imported source using the business API
		var query = new DataStoreQuery(source.Name, new List<DataStoreQueryField>());
		var elementsResultSet = await source.GetElementsAsync(query);
		var importedElements = elementsResultSet.ToList();

		// Now assert imported count
		Assert.Single(importedElements);

		// Then validate the imported value
		var element = importedElements[0];
		Assert.Equal("ImportedValue", element.Values[fieldName]);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Import MultiPage Data with Invalid Data Types")]
	public async Task ImportDataWithInvalidDataTypesTest()
	{
		// Arrange
		await using var db = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// Create source and fields matching Excel structure
		var source = EntityCreation.CreateDataSource(db, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id, "InvalidTypeImportSource");

		// Create an integer field to test type validation
		var fieldName = "IntegerField";
		var fieldDto = new DataFieldDto { Type = DataType.Integer, Name = fieldName, DataSourceId = source.Id };
		db.DataFields.Add(DataFieldEntity.FromDto(fieldDto, null, db));
		await db.SaveChangesAsync();
		db.ChangeTracker.Clear();

		// Fetch field and create in the database
		var field = db.DataFields.First(f => f.DataSourceId == source.Id && f.Name == fieldName);
		field.CreateField();

		// Prepare Excel file in memory stream with invalid data type (text in integer field)
		using var workbook = new XLWorkbook();
		var worksheet = workbook.Worksheets.Add("Sheet1");

		// Add header and data with invalid type (text in integer field)
		worksheet.Cell(1, 1).Value = fieldName;
		worksheet.Cell(2, 1).Value = "NotANumber"; // This should cause a type conversion error

		using var stream = new MemoryStream();
		workbook.SaveAs(stream);
		stream.Position = 0;

		// Create form file for import
		var formFile = new FormFile(stream, 0, stream.Length, "excelFile", "invalid_import.xlsx")
		{
			Headers = new HeaderDictionary(),
			ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
		};

		// Create mock Redis Progress Service
		var processId = Guid.NewGuid().ToString();
		var mockRedisProgressService = new Mock<IRedisProgressService>();

		// Setup import parameters with column mappings
		var importParams = new ExcelImportParametersDto
		{
			DataSourceId = source.Id,
			IsFirstLineHeader = true,
			MatchBy = new List<string>(),
			SelectedMappings = new List<ExcelColumnMappingDto>
			{
				new ExcelColumnMappingDto
				{
					index = 0, // First column (0-based index)
					ColumnKey = fieldName,
					DataType = "Integer" // Expecting integer data
				}
			}
		};

		// Set up controller with form file and process ID header
		_controllerInstance.ControllerContext = new ControllerContext
		{
			HttpContext = new DefaultHttpContext()
		};
		_controllerInstance.ControllerContext.HttpContext.Request.Headers["X-Process-ID"] = processId;

		// Create form collection with the file and import parameters
		var formFileCollection = new FormFileCollection { formFile };
		var formDict = new Dictionary<string, StringValues>
		{
			["importParams"] = JsonConvert.SerializeObject(importParams)
		};
		_controllerInstance.ControllerContext.HttpContext.Request.Form = new FormCollection(formDict, formFileCollection);

		// Act
		var result = await _controllerInstance.ImportExcelData(mockRedisProgressService.Object);

		// Assert
		Assert.NotNull(result);
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);

		var frontendResponse = objectResult.Value as FrontendResponse<ExcelRecordDto>;
		Assert.NotNull(frontendResponse);
		Assert.Null(frontendResponse.Error); // No overall error, but should have validation errors in the result

		// The import should complete but with errors
		Assert.NotNull(frontendResponse.Data);
		Assert.Equal(0, frontendResponse.Data.Summary.Inserted); // No records should be imported
		Assert.Equal(1, frontendResponse.Data.Summary.Failed);   // One record should fail
		Assert.NotEmpty(frontendResponse.Data.Errors);           // Should have error details

		// Verify the error details for the failed import
		var error = frontendResponse.Data.Errors.First();
		Assert.Equal(2, error.RowNumber);                        // Error in row 2

		// The error message could be one of several types:
		// 1. A type conversion error
		// 2. A "no data to import" error (if the invalid data is skipped)
		// 3. Some other validation error
		Assert.True(
			error.Error.ToLower().Contains("convert") ||
			error.Error.ToLower().Contains("format") ||
			error.Error.ToLower().Contains("type") ||
			error.Error.ToLower().Contains("invalid") ||
			error.Error.Contains("no data to import") ||
			error.Error.Contains("Row contains no data"),
			$"Error message should indicate a validation issue but was: {error.Error}"
		);

		// The column key might be null in some cases, so we'll check if it's set
		if (error.ColumnKey != null)
		{
			Assert.Equal(fieldName, error.ColumnKey);
		}

		// The value might also be null in some cases
		if (error.Value != null)
		{
			Assert.Equal("NotANumber", error.Value);
		}

		// Verify no data was imported
		var query = new DataStoreQuery(source.Name, new List<DataStoreQueryField>());
		var elementsResultSet = await source.GetElementsAsync(query);
		var importedElements = elementsResultSet.ToList();
		Assert.Empty(importedElements);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Import MultiPage Data with Empty Excel File")]
	public async Task ImportDataWithEmptyExcelFileTest()
	{
		// Arrange
		await using var db = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// Create source and fields matching Excel structure
		var source = EntityCreation.CreateDataSource(db, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id, "EmptyExcelImportSource");
		var fieldName = "TestField";
		var fieldDto = new DataFieldDto { Type = DataType.String, Name = fieldName, DataSourceId = source.Id, Length = 100 };
		db.DataFields.Add(DataFieldEntity.FromDto(fieldDto, null, db));
		await db.SaveChangesAsync();
		db.ChangeTracker.Clear();

		// Fetch field and create in the database
		var field = db.DataFields.First(f => f.DataSourceId == source.Id && f.Name == fieldName);
		field.CreateField();

		// Prepare empty Excel file in memory stream
		using var workbook = new XLWorkbook();
		var worksheet = workbook.Worksheets.Add("Sheet1");

		// Add only header but no data rows
		worksheet.Cell(1, 1).Value = fieldName;
		// No data rows added

		using var stream = new MemoryStream();
		workbook.SaveAs(stream);
		stream.Position = 0;

		// Create form file for import
		var formFile = new FormFile(stream, 0, stream.Length, "excelFile", "empty_import.xlsx")
		{
			Headers = new HeaderDictionary(),
			ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
		};

		// Create mock Redis Progress Service
		var processId = Guid.NewGuid().ToString();
		var mockRedisProgressService = new Mock<IRedisProgressService>();

		// Setup import parameters with column mappings
		var importParams = new ExcelImportParametersDto
		{
			DataSourceId = source.Id,
			IsFirstLineHeader = true,
			MatchBy = new List<string>(),
			SelectedMappings = new List<ExcelColumnMappingDto>
			{
				new ExcelColumnMappingDto
				{
					index = 0, // First column (0-based index)
					ColumnKey = fieldName,
					DataType = "String"
				}
			}
		};

		// Set up controller with form file and process ID header
		_controllerInstance.ControllerContext = new ControllerContext
		{
			HttpContext = new DefaultHttpContext()
		};
		_controllerInstance.ControllerContext.HttpContext.Request.Headers["X-Process-ID"] = processId;

		// Create form collection with the file and import parameters
		var formFileCollection = new FormFileCollection { formFile };
		var formDict = new Dictionary<string, StringValues>
		{
			["importParams"] = JsonConvert.SerializeObject(importParams)
		};
		_controllerInstance.ControllerContext.HttpContext.Request.Form = new FormCollection(formDict, formFileCollection);

		// Act
		var result = await _controllerInstance.ImportExcelData(mockRedisProgressService.Object);

		// Assert
		Assert.NotNull(result);
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);

		var frontendResponse = objectResult.Value as FrontendResponse<ExcelRecordDto>;
		Assert.NotNull(frontendResponse);
		Assert.Null(frontendResponse.Error);

		// The import should complete with no records imported
		Assert.NotNull(frontendResponse.Data);
		Assert.Equal(0, frontendResponse.Data.Summary.Inserted); // No records should be imported
		Assert.Equal(0, frontendResponse.Data.Summary.Failed);   // No records should fail
		Assert.Empty(frontendResponse.Data.Errors);              // No errors should be reported

		// Verify no data was imported
		var query = new DataStoreQuery(source.Name, new List<DataStoreQueryField>());
		var elementsResultSet = await source.GetElementsAsync(query);
		var importedElements = elementsResultSet.ToList();
		Assert.Empty(importedElements);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Import MultiPage Data with Required Field Validation")]
	public async Task ImportDataWithRequiredFieldValidationTest()
	{
		// Arrange
		await using var db = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// Create source and fields matching Excel structure
		var source = EntityCreation.CreateDataSource(db, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id, "RequiredFieldImportSource");

		// Create a required field and a non-required field
		var requiredFieldName = "RequiredField";
		var optionalFieldName = "OptionalField";

		var requiredFieldDto = new DataFieldDto
		{
			Type = DataType.String,
			Name = requiredFieldName,
			DataSourceId = source.Id,
			Length = 100,
			Mandatory = true // This field is required
		};

		var optionalFieldDto = new DataFieldDto
		{
			Type = DataType.String,
			Name = optionalFieldName,
			DataSourceId = source.Id,
			Length = 100,
			Mandatory = false // This field is optional
		};

		db.DataFields.Add(DataFieldEntity.FromDto(requiredFieldDto, null, db));
		db.DataFields.Add(DataFieldEntity.FromDto(optionalFieldDto, null, db));
		await db.SaveChangesAsync();
		db.ChangeTracker.Clear();

		// Fetch fields and create them in the database
		var requiredField = db.DataFields.First(f => f.DataSourceId == source.Id && f.Name == requiredFieldName);
		var optionalField = db.DataFields.First(f => f.DataSourceId == source.Id && f.Name == optionalFieldName);
		requiredField.CreateField();
		optionalField.CreateField();

		// Create mock Redis Progress Service
		var processId = Guid.NewGuid().ToString();
		var mockRedisProgressService = new Mock<IRedisProgressService>();

		// Prepare Excel file with data that violates required field constraints
		using var workbook = new XLWorkbook();
		var worksheet = workbook.Worksheets.Add("Sheet1");

		// Add headers
		worksheet.Cell(1, 1).Value = requiredFieldName;
		worksheet.Cell(1, 2).Value = optionalFieldName;

		// Add test data - some rows with missing required field values
		worksheet.Cell(2, 1).Value = "ValidValue1";     // Required field has value
		worksheet.Cell(2, 2).Value = "Optional1";       // Optional field has value

		worksheet.Cell(3, 1).Value = "";                // Required field is empty (should fail)
		worksheet.Cell(3, 2).Value = "Optional2";       // Optional field has value

		worksheet.Cell(4, 1).Value = "ValidValue2";     // Required field has value
		worksheet.Cell(4, 2).Value = "";                // Optional field is empty (should pass)

		worksheet.Cell(5, 1).Value = "   ";             // Required field has only whitespace (should fail)
		worksheet.Cell(5, 2).Value = "Optional3";       // Optional field has value

		// Save to memory stream
		using var stream = new MemoryStream();
		workbook.SaveAs(stream);
		stream.Position = 0;

		// Create form file
		var formFile = new FormFile(stream, 0, stream.Length, "file", "test.xlsx")
		{
			Headers = new HeaderDictionary(),
			ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
		};

		// Setup import parameters with column mappings
		var importParams = new ExcelImportParametersDto
		{
			DataSourceId = source.Id,
			IsFirstLineHeader = true,
			MatchBy = new List<string>(),
			SelectedMappings = new List<ExcelColumnMappingDto>
			{
				new ExcelColumnMappingDto
				{
					index = 0, // First column (0-based index)
					ColumnKey = requiredFieldName,
					DataType = "String"
				},
				new ExcelColumnMappingDto
				{
					index = 1, // Second column (0-based index)
					ColumnKey = optionalFieldName,
					DataType = "String"
				}
			}
		};

		// Set up controller with form file and process ID header
		_controllerInstance.ControllerContext = new ControllerContext
		{
			HttpContext = new DefaultHttpContext()
		};
		_controllerInstance.ControllerContext.HttpContext.Request.Form = new FormCollection(
			new Dictionary<string, StringValues>
			{
				["importParams"] = JsonConvert.SerializeObject(importParams)
			},
			new FormFileCollection { formFile }
		);
		_controllerInstance.ControllerContext.HttpContext.Request.Headers["X-Process-ID"] = processId;

		// Act
		var result = await _controllerInstance.ImportExcelData(mockRedisProgressService.Object);

		// Assert
		Assert.NotNull(result);
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);

		var frontendResponse = objectResult.Value as FrontendResponse<ExcelRecordDto>;
		Assert.NotNull(frontendResponse);
		Assert.Null(frontendResponse.Error);

		// The import should complete with some records imported and some failed
		Assert.NotNull(frontendResponse.Data);
		Assert.Equal(2, frontendResponse.Data.Summary.Inserted); // 2 valid records should be imported
		Assert.Equal(2, frontendResponse.Data.Summary.Failed);   // 2 records should fail due to required field validation
		Assert.NotEmpty(frontendResponse.Data.Errors);           // Should have validation errors

		// Verify that the errors are related to required field validation
		var requiredFieldErrors = frontendResponse.Data.Errors.Where(e => e.Error.Contains("Required field")).ToList();
		Assert.Equal(2, requiredFieldErrors.Count); // Should have 2 required field errors

		// Verify error messages contain the required field name
		Assert.All(requiredFieldErrors, error =>
			Assert.Contains(requiredFieldName, error.Error));

		// Verify that only valid records were imported
		var query = new DataStoreQuery(source.Name, new List<DataStoreQueryField>());
		var elementsResultSet = await source.GetElementsAsync(query);
		var importedElements = elementsResultSet.ToList();
		Assert.Equal(2, importedElements.Count); // Only 2 valid records should be imported

		// Verify the imported data contains the expected values
		var importedValues = importedElements.Select(e => e.Values[requiredFieldName]?.ToString()).ToList();
		Assert.Contains("ValidValue1", importedValues);
		Assert.Contains("ValidValue2", importedValues);
	}

	#endregion

	#region Prepare Data

	private async Task CreateStorageEntriesAsync(DataSourceEntity dataSource, string referenceElementId, string anotherReferenceElementId)
	{
		DataStoreOperationOrigin origin = new(DataStoreOperationOriginType.System, 0, "Test");
		var groups = new List<string>() { "testgroup" };
		var data = new List<DataStoreElementData>
		{
			new(_data[0].Values.Append(new KeyValuePair<string, object?>("LookupField", referenceElementId)).ToDictionary(), groups),
			new(_data[1].Values.Append(new KeyValuePair<string, object?>("LookupField", referenceElementId)).ToDictionary(), groups),
			new(_data[2].Values.Append(new KeyValuePair<string, object?>("LookupField", referenceElementId)).ToDictionary(), groups),
			new(_data[3].Values.Append(new KeyValuePair<string, object?>("LookupField", anotherReferenceElementId)).ToDictionary(), groups),
			new(_data[4].Values.Append(new KeyValuePair<string, object?>("LookupField", anotherReferenceElementId)).ToDictionary(), groups),
			new(_data[5].Values.Append(new KeyValuePair<string, object?>("LookupField", referenceElementId)).ToDictionary(), groups)
		};

		for (var index = 0; index < data.Count; index++)
		{
			var info = await dataSource.CreateElementAsync(data[index], origin);

			// Set element inactive
			if (_data[index].Inactive)
				await dataSource.SetInactiveAsync(new Guid(info.ElementId).ToString());

			// Add element to favorites
			if (_data[index].Favorite)
				await dataSource.AddFavouriteAsync(new Guid(info.ElementId).ToString());
		}
	}

	private async Task<ObjectResult> QueryDataWithParamsAsync(QueryParamsDto parameters)
	{
		await using var databaseContext = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// create another data source with a field to reference
		var referenceSource = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
															  currentCustomer.Id, "SourceToReference");
		var referenceField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
																new DataFieldDto()
																{
																	Type = DataType.String, Name = "ReferenceField", DataSourceId = referenceSource.Id,
																	Length = 100
																},
																null, databaseContext));
		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();
		referenceField.Entity.CreateField();
		// add two elements
		DataStoreOperationOrigin origin = new(DataStoreOperationOriginType.System, 0, "Test");
		var groups = new List<string>() { "testgroup" };
		var referenceElement = await referenceSource.CreateElementAsync(new DataStoreElementData(_referenceData[0].Values, groups), origin);
		var differentReferenceElement = await referenceSource.CreateElementAsync(new DataStoreElementData(_referenceData[1].Values, groups), origin);

		// create some records in db to query data
		// create DataSource
		var source = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
													 currentCustomer.Id);
		var sourceId = source.Id;

		// create ListView
		var view = EntityCreation.CreateListView(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
												 currentCustomer.Id);
		var viewId = view.Id;

		// create Fields
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(new DataFieldDto() { Type = DataType.Date, Name = "CreateDate", DataSourceId = sourceId },
															   null, databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(
										   new DataFieldDto() { Type = DataType.String, Name = "Filename", DataSourceId = sourceId, Length = 250 }, null,
										   databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(new DataFieldDto() { Type = DataType.Boolean, Name = "InSync", DataSourceId = sourceId }, null,
															   databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(
										   new DataFieldDto() { Type = DataType.String, Name = "Comment", DataSourceId = sourceId, Length = 1000 }, null,
										   databaseContext));
		var lookupField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
															 new DataFieldDto()
															 {
																 Type = DataType.Guid, FieldType = DataFieldType.LookupField, Name = "LookupField",
																 DataSourceId = sourceId,
																 LookupSourceId = referenceSource.Id, LookupDisplayFieldId = referenceField.Entity.Id,
																 Length = 42
															 }, null,
															 databaseContext));
		var virtualField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
															  new DataFieldDto()
															  {
																  FieldType = DataFieldType.VirtualField, Name = "VirtualField", DataSourceId = sourceId,
																  VirtualLookupFieldId = lookupField.Entity.Id, VirtualDataFieldId = referenceField.Entity.Id
															  }, null,
															  databaseContext));
		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();

		var fieldList = databaseContext.DataFields.Include(field => field.LookupSource)
			.Where(field => !field.SystemField && field.DataSourceId == source.Id).ToList();
		foreach (var fieldEntity in fieldList)
		{
			if (fieldEntity.FieldType != DataFieldType.VirtualField)
				fieldEntity.CreateField();
		}

		// create Columns for ListView
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = fieldList[0].Id, ListViewId = viewId }));
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = fieldList[1].Id, ListViewId = viewId }));
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = fieldList[2].Id, ListViewId = viewId }));
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = fieldList[3].Id, ListViewId = viewId }));
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = fieldList[4].Id, ListViewId = viewId }));
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = virtualField.Entity.Id, ListViewId = viewId }));

		await databaseContext.SaveChangesAsync();

		await CreateStorageEntriesAsync(source, referenceElement.ElementId, differentReferenceElement.ElementId);

		// add fields to select
		parameters.Fields = fieldList.Select(field => field.Name).ToList();

		return ((await _controllerInstance.QueryAsync(sourceId, parameters)).Result as ObjectResult)!;
	}

	private async Task<ObjectResult> CountDataWithParamsAsync(QueryParamsDto parameters)
	{
		await using var databaseContext = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// create another data source with a field to reference
		var referenceSource = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
															  currentCustomer.Id, "SourceToReference");
		var referenceField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
																new DataFieldDto()
																{
																	Type = DataType.String, Name = "ReferenceField", DataSourceId = referenceSource.Id,
																	Length = 100
																},
																null, databaseContext));
		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();
		referenceField.Entity.CreateField();
		// add two elements
		DataStoreOperationOrigin origin = new(DataStoreOperationOriginType.System, 0, "Test");
		var groups = new List<string>() { "testgroup" };
		var referenceElement = await referenceSource.CreateElementAsync(new DataStoreElementData(_referenceData[0].Values, groups), origin);
		var differentReferenceElement = await referenceSource.CreateElementAsync(new DataStoreElementData(_referenceData[1].Values, groups), origin);

		// create DataSource
		var source = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
													 currentCustomer.Id);
		var sourceId = source.Id;

		// create Fields
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(new DataFieldDto() { Type = DataType.Date, Name = "CreateDate", DataSourceId = sourceId },
															   null, databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(
										   new DataFieldDto() { Type = DataType.String, Name = "Filename", DataSourceId = sourceId, Length = 250 }, null,
										   databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(new DataFieldDto() { Type = DataType.Boolean, Name = "InSync", DataSourceId = sourceId }, null,
															   databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(
										   new DataFieldDto() { Type = DataType.String, Name = "Comment", DataSourceId = sourceId, Length = 1000 }, null,
										   databaseContext));

		var lookupField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
															 new DataFieldDto()
															 {
																 Type = DataType.Guid, FieldType = DataFieldType.LookupField, Name = "LookupField",
																 DataSourceId = sourceId,
																 LookupSourceId = referenceSource.Id, LookupDisplayFieldId = referenceField.Entity.Id,
																 Length = 42
															 }, null,
															 databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(
										   new DataFieldDto()
										   {
											   FieldType = DataFieldType.VirtualField, Name = "VirtualField", DataSourceId = sourceId,
											   VirtualLookupFieldId = lookupField.Entity.Id, VirtualDataFieldId = referenceField.Entity.Id
										   }, null,
										   databaseContext));

		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();

		var fieldList = databaseContext.DataFields.Include(field => field.LookupSource)
			.Where(field => !field.SystemField && field.FieldType != DataFieldType.VirtualField && field.DataSourceId == source.Id).ToList();
		foreach (var fieldEntity in fieldList)
		{
			fieldEntity.CreateField();
		}

		await databaseContext.SaveChangesAsync();

		// create some records in db to query data
		await CreateStorageEntriesAsync(source, referenceElement.ElementId, differentReferenceElement.ElementId);

		return ((await _controllerInstance.CountAsync(sourceId, parameters)).Result as ObjectResult)!;
	}

	private async Task<ObjectResult> QueryStackingVirtualDataWithParamsAsync(QueryParamsDto parameters)
	{
		var databaseContext = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// create another data source with a field to reference
		var referenceSource = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
															  currentCustomer.Id, "SourceToReference");
		var referenceField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
																new DataFieldDto()
																{
																	Type = DataType.String, Name = "ReferenceField", DataSourceId = referenceSource.Id,
																	Length = 100
																},
																null, databaseContext));
		var selfRefLookupField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
																	new DataFieldDto()
																	{
																		FieldType = DataFieldType.LookupField, Name = "SelfRefLookup",
																		DataSourceId = referenceSource.Id,
																		LookupSourceId = referenceSource.Id, LookupDisplayFieldId = referenceField.Entity.Id,
																	},
																	null, databaseContext));
		var selfRefVirtualField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
																	 new DataFieldDto()
																	 {
																		 FieldType = DataFieldType.VirtualField, Name = "SelfRefVirtual",
																		 DataSourceId = referenceSource.Id,
																		 VirtualLookupFieldId = selfRefLookupField.Entity.Id,
																		 VirtualDataFieldId = referenceField.Entity.Id,
																	 },
																	 null, databaseContext));
		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();
		referenceField.Entity.CreateField();
		selfRefLookupField.Entity.CreateField();
		// add two elements
		DataStoreOperationOrigin origin = new(DataStoreOperationOriginType.System, 0, "Test");
		var groups = new List<string>() { "testgroup" };
		var referenceElement = await referenceSource.CreateElementAsync(new DataStoreElementData(_referenceData[0].Values, groups), origin);
		var selfReferenceData = _referenceData[1].Values;
		selfReferenceData.Add("SelfRefLookup", referenceElement.ElementId);
		var differentReferenceElement = await referenceSource.CreateElementAsync(new DataStoreElementData(_referenceData[1].Values, groups), origin);

		// create some records in db to query data
		// create DataSource
		var source = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
													 currentCustomer.Id);
		var sourceId = source.Id;

		// create ListView
		var view = EntityCreation.CreateListView(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
												 currentCustomer.Id);
		var viewId = view.Id;

		// create Fields
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(new DataFieldDto() { Type = DataType.Date, Name = "CreateDate", DataSourceId = sourceId },
															   null, databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(
										   new DataFieldDto() { Type = DataType.String, Name = "Filename", DataSourceId = sourceId, Length = 250 }, null,
										   databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(new DataFieldDto() { Type = DataType.Boolean, Name = "InSync", DataSourceId = sourceId }, null,
															   databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(
										   new DataFieldDto() { Type = DataType.String, Name = "Comment", DataSourceId = sourceId, Length = 1000 }, null,
										   databaseContext));
		var lookupField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
															 new DataFieldDto()
															 {
																 Type = DataType.Guid, FieldType = DataFieldType.LookupField, Name = "LookupField",
																 DataSourceId = sourceId,
																 LookupSourceId = referenceSource.Id, LookupDisplayFieldId = referenceField.Entity.Id,
																 Length = 42
															 }, null,
															 databaseContext));
		var stackingVirtualField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
																	  new DataFieldDto()
																	  {
																		  FieldType = DataFieldType.VirtualField, Name = "VirtualField",
																		  DataSourceId = sourceId,
																		  VirtualLookupFieldId = lookupField.Entity.Id,
																		  VirtualDataFieldId = selfRefVirtualField.Entity.Id
																	  }, null,
																	  databaseContext));
		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();

		var fieldList = databaseContext.DataFields.Include(field => field.LookupSource)
			.Where(field => !field.SystemField && field.FieldType != DataFieldType.VirtualField && field.DataSourceId == source.Id).ToList();
		foreach (var fieldEntity in fieldList)
		{
			fieldEntity.CreateField();
		}

		// create Columns for ListView
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = fieldList[0].Id, ListViewId = viewId }));
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = fieldList[1].Id, ListViewId = viewId }));
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = fieldList[2].Id, ListViewId = viewId }));
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = fieldList[3].Id, ListViewId = viewId }));
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto() { FieldId = fieldList[4].Id, ListViewId = viewId }));
		databaseContext.ListViewColumns.Add(ListViewColumnEntity.FromDto(new ListViewColumnDto()
																			 { FieldId = stackingVirtualField.Entity.Id, ListViewId = viewId }));

		await databaseContext.SaveChangesAsync();

		await CreateStorageEntriesAsync(source, referenceElement.ElementId, differentReferenceElement.ElementId);

		// add fields to select
		parameters.Fields = fieldList.Select(field => field.Name).ToList();

		return ((await _controllerInstance.QueryAsync(sourceId, parameters)).Result as ObjectResult)!;
	}

	private async Task<(ObjectResult Result, string ReferenceId, string DifferentReferenceId)> PrepareGroupByFilterFieldValuesWithParamsAsync(string fieldName)
	{
		await using var databaseContext = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// create another data source with a field to reference
		var referenceSource = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
															  currentCustomer.Id, "SourceToReference");
		var referenceField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
																new DataFieldDto()
																{
																	Type = DataType.String, Name = "ReferenceField", DataSourceId = referenceSource.Id,
																	Length = 100
																},
																null, databaseContext));
		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();
		referenceField.Entity.CreateField();
		// add two elements
		DataStoreOperationOrigin origin = new(DataStoreOperationOriginType.System, 0, "Test");
		var groups = new List<string>() { "testgroup" };
		var referenceElement = await referenceSource.CreateElementAsync(new DataStoreElementData(_referenceData[0].Values, groups), origin);
		var differentReferenceElement = await referenceSource.CreateElementAsync(new DataStoreElementData(_referenceData[1].Values, groups), origin);

		// create some records in db to query data
		// create DataSource
		var source = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
													 currentCustomer.Id);
		var sourceId = source.Id;

		// create Fields
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(new DataFieldDto() { Type = DataType.Date, Name = "CreateDate", DataSourceId = sourceId },
															   null, databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(
										   new DataFieldDto() { Type = DataType.String, Name = "Filename", DataSourceId = sourceId, Length = 250 }, null,
										   databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(new DataFieldDto() { Type = DataType.Boolean, Name = "InSync", DataSourceId = sourceId }, null,
															   databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(
										   new DataFieldDto() { Type = DataType.String, Name = "Comment", DataSourceId = sourceId, Length = 1000 }, null,
										   databaseContext));
		databaseContext.DataFields.Add(DataFieldEntity.FromDto(
										   new DataFieldDto()
										   {
											   Type = DataType.Guid, FieldType = DataFieldType.LookupField, Name = "LookupField",
											   DataSourceId = sourceId,
											   LookupSourceId = referenceSource.Id, LookupDisplayFieldId = referenceField.Entity.Id,
											   Length = 42
										   }, null,
										   databaseContext));

		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();

		var fieldList = databaseContext.DataFields.Include(field => field.LookupSource)
			.Where(field => !field.SystemField && field.FieldType != DataFieldType.VirtualField && field.DataSourceId == source.Id).ToList();
		foreach (var fieldEntity in fieldList)
		{
			fieldEntity.CreateField();
		}

		await databaseContext.SaveChangesAsync();
		await CreateStorageEntriesAsync(source, referenceElement.ElementId, differentReferenceElement.ElementId);

		var field = fieldList.First(field => field.Name == fieldName);
		var result = (await _controllerInstance.GroupFilterFieldValuesAsync(field.Id, new QueryParamsDto()
						 {
							 Filters = new List<QueryParamFilterDto>()
							 {
								 new()
								 {
									 FilterColumn = "InSync",
									 Operator = QueryParamFilterOperator.Equals,
									 CompareValue = true
								 }
							 }
						 })).Result as ObjectResult;

		return (result, referenceElement.ElementId, differentReferenceElement.ElementId)!;
	}

	private async Task<(Guid sourceId, Guid fieldId)> PrepareAggregationQuery(string sourceName, string fieldName)
	{
		await using var databaseContext = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// create data source with field
		var source = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
													 currentCustomer.Id, sourceName);
		var field = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
													   new DataFieldDto()
													   {
														   Type = DataType.Integer, Name = fieldName, DataSourceId = source.Id
													   },
													   null, databaseContext));
		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();
		field.Entity.CreateField();
		// add two elements
		DataStoreOperationOrigin origin = new(DataStoreOperationOriginType.System, 0, "Test");
		var groups = new List<string>() { "testgroup" };
		await source.CreateElementAsync(new DataStoreElementData(new Dictionary<string, object?> { { fieldName, 1 } }, groups), origin);
		await source.CreateElementAsync(new DataStoreElementData(new Dictionary<string, object?> { { fieldName, 2 } }, groups), origin);
		
		return (source.Id, field.Entity.Id);
	}
	
	private async Task<(Guid sourceId, Guid lookupId, Guid virtualId)> PrepareAggregationQueryLookup(string sourceName, string fieldName, string lookupFieldName, string virtualFieldName)
	{
		await using var databaseContext = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// create data source with field
		var source = EntityCreation.CreateDataSource(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
													 currentCustomer.Id, sourceName);
		var field = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
													   new DataFieldDto()
													   {
														   Type = DataType.Integer, Name = fieldName, DataSourceId = source.Id
													   },
													   null, databaseContext));
		
		var lookupField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
													   new DataFieldDto()
													   {
														   FieldType = DataFieldType.LookupField, Name = lookupFieldName, DataSourceId = source.Id, LookupSourceId = source.Id, LookupDisplayFieldId = field.Entity.Id
													   },
													   null, databaseContext));
		
		var virtualField = databaseContext.DataFields.Add(DataFieldEntity.FromDto(
															 new DataFieldDto()
															 {
																 FieldType = DataFieldType.VirtualField, Name = virtualFieldName, DataSourceId = source.Id, VirtualLookupFieldId = lookupField.Entity.Id, VirtualDataFieldId = field.Entity.Id
															 },
															 null, databaseContext));
		
		await databaseContext.SaveChangesAsync();
		databaseContext.ChangeTracker.Clear();
		
		field.Entity.CreateField();
		lookupField.Entity.CreateField();
		
		// add two elements
		DataStoreOperationOrigin origin = new(DataStoreOperationOriginType.System, 0, "Test");
		var groups = new List<string>() { "testgroup" };
		var element = await source.CreateElementAsync(new DataStoreElementData(new Dictionary<string, object?> { { fieldName, 1 } }, groups), origin);
		var secondElement = await source.CreateElementAsync(new DataStoreElementData(new Dictionary<string, object?> { { fieldName, 2 } }, groups), origin);
		await source.CreateElementAsync(new DataStoreElementData(new Dictionary<string, object?> { { lookupFieldName, element.ElementId } }, groups), origin);
		await source.CreateElementAsync(new DataStoreElementData(new Dictionary<string, object?> { { lookupFieldName, secondElement.ElementId } }, groups), origin);
		
		return (source.Id, lookupField.Entity.Id, virtualField.Entity.Id);
	}

	#endregion



	// Mock session implementation for testing
	private class MockHttpSession : ISession
	{
		private readonly Dictionary<string, byte[]> _store = new Dictionary<string, byte[]>();

		public bool IsAvailable => true;
		public string Id => Guid.NewGuid().ToString();
		public IEnumerable<string> Keys => _store.Keys;

		public Task LoadAsync(CancellationToken cancellationToken = default)
		{
			return Task.CompletedTask;
		}

		public Task CommitAsync(CancellationToken cancellationToken = default)
		{
			return Task.CompletedTask;
		}

		public void Clear()
		{
			_store.Clear();
		}

		public void Remove(string key)
		{
			_store.Remove(key);
		}

		public void Set(string key, byte[] value)
		{
			_store[key] = value;
		}

		public bool TryGetValue(string key, out byte[] value)
		{
			return _store.TryGetValue(key, out value);
		}
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Get page view columns with visible and invisible columns")]
	public async Task GetPageViewColumns_WithVisibleAndInvisibleColumns_ReturnsCorrectColumns()
	{
		// Arrange
		await using var db = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		var dataSource = EntityCreation.CreateDataSource(db, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id, "DataSourceForColumnsTest");
		var visibleField = DataFieldEntity.FromDto(new DataFieldDto { Type = DataType.String, Name = "VisibleField", DataSourceId = dataSource.Id, Length = 100 }, null, db);
		var invisibleField = DataFieldEntity.FromDto(new DataFieldDto { Type = DataType.String, Name = "InvisibleField", DataSourceId = dataSource.Id, Length = 100 }, null, db);
		db.DataFields.AddRange(visibleField, invisibleField);
		await db.SaveChangesAsync();

		var pageView = EntityCreation.CreateListView(db, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id);
		var listViewColumn = ListViewColumnEntity.FromDto(new ListViewColumnDto { FieldId = visibleField.Id, ListViewId = pageView.Id, Position = 1 });
		db.ListViewColumns.Add(listViewColumn);
		await db.SaveChangesAsync();

		// Act
		var result = _controllerInstance.GetPageViewColumns(pageView.Id);

		// Assert
		var okResult = Assert.IsType<OkObjectResult>(result.Result);
		var frontendResponse = Assert.IsType<FrontendResponse<PresetColumnsResultDto>>(okResult.Value);
		var columnsResult = frontendResponse.Data;

		Assert.NotNull(columnsResult);
		Assert.Equal(dataSource.Id, columnsResult.DataSourceId);
		
		Assert.Single(columnsResult.visibleColumns);
		Assert.Equal("visiblefield", columnsResult.visibleColumns[0].Key);
		
		Assert.Single(columnsResult.InvisibleColumns);
		Assert.Equal("invisiblefield", columnsResult.InvisibleColumns[0].Key);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Get page view columns for a view with no columns")]
	public async Task GetPageViewColumns_WithNoColumns_ReturnsEmptyVisibleAndInvisible()
	{
		// Arrange
		await using var db = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();
		var pageView = EntityCreation.CreateListView(db, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id);
		await db.SaveChangesAsync();

		// Act
		var result = _controllerInstance.GetPageViewColumns(pageView.Id);

		// Assert
		var okResult = Assert.IsType<OkObjectResult>(result.Result);
		var frontendResponse = Assert.IsType<FrontendResponse<PresetColumnsResultDto>>(okResult.Value);
		var columnsResult = frontendResponse.Data;

		Assert.NotNull(columnsResult);
		Assert.Empty(columnsResult.visibleColumns);
		Assert.Empty(columnsResult.InvisibleColumns);
		Assert.Null(columnsResult.DataSourceId);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Get page view columns for a non-existent page view")]
	public void GetPageViewColumns_WithNonExistentPageView_ReturnsEmpty()
	{
		// Arrange
		var nonExistentPageViewId = Guid.NewGuid();

		// Act
		var result = _controllerInstance.GetPageViewColumns(nonExistentPageViewId);

		// Assert
		var okResult = Assert.IsType<OkObjectResult>(result.Result);
		var frontendResponse = Assert.IsType<FrontendResponse<PresetColumnsResultDto>>(okResult.Value);
		var columnsResult = frontendResponse.Data;

		Assert.NotNull(columnsResult);
		Assert.Empty(columnsResult.visibleColumns);
		Assert.Empty(columnsResult.InvisibleColumns);
		Assert.Null(columnsResult.DataSourceId);
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Import Excel with Validation Errors")]
	public async Task ImportWithValidationErrorsTest()
	{
		// Arrange: prepare data and setup
		await using var db = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// Create source and fields - use an existing source to avoid schema conflicts
		var source = await db.DataSources.FirstOrDefaultAsync(s => s.Name == "ExcelExportSource");
		if (source == null)
		{
			// If the source doesn't exist yet, create it
			source = EntityCreation.CreateDataSource(db, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id, "ExcelExportSource");
		}

		// Check if the fields already exist
		var intField = await db.DataFields.FirstOrDefaultAsync(f => f.DataSourceId == source.Id && f.Name == "IntField");
		var dateField = await db.DataFields.FirstOrDefaultAsync(f => f.DataSourceId == source.Id && f.Name == "DateField");

		// Create fields if they don't exist
		if (intField == null)
		{
			var intFieldDto = new DataFieldDto { Type = DataType.Integer, Name = "IntField", DataSourceId = source.Id };
			db.DataFields.Add(DataFieldEntity.FromDto(intFieldDto, null, db));
		}

		if (dateField == null)
		{
			var dateFieldDto = new DataFieldDto { Type = DataType.Date, Name = "DateField", DataSourceId = source.Id };
			db.DataFields.Add(DataFieldEntity.FromDto(dateFieldDto, null, db));
		}

		await db.SaveChangesAsync();
		db.ChangeTracker.Clear();

		// Fetch fields and ensure they're created in the database
		var fields = db.DataFields.Where(f => f.DataSourceId == source.Id && (f.Name == "IntField" || f.Name == "DateField")).ToList();
		foreach (var field in fields)
		{
			try
			{
				// Only create the field if it hasn't been created yet
				field.CreateField();
			}
			catch (Exception ex)
			{
				// Ignore errors about fields already existing
				if (!ex.Message.Contains("already exists"))
				{
					throw;
				}
			}
		}

		// Prepare Excel file with invalid data (string in integer field, invalid date format)
		using var workbook = new XLWorkbook();
		var worksheet = workbook.Worksheets.Add("Sheet1");

		// Add header and data with validation errors
		worksheet.Cell(1, 1).Value = "IntField";
		worksheet.Cell(1, 2).Value = "DateField";

		// Row with invalid integer
		worksheet.Cell(2, 1).Value = "not-an-integer";
		worksheet.Cell(2, 2).Value = "2023-05-15";

		// Row with invalid date
		worksheet.Cell(3, 1).Value = 42;
		worksheet.Cell(3, 2).Value = "invalid-date";

		using var stream = new MemoryStream();
		workbook.SaveAs(stream);
		stream.Position = 0;

		// Create form file for import
		var formFile = new FormFile(stream, 0, stream.Length, "excelFile", "import-errors.xlsx")
		{
			Headers = new HeaderDictionary(),
			ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
		};

		// Create mock Redis Progress Service
		var processId = Guid.NewGuid().ToString();
		var mockRedisProgressService = new Mock<IRedisProgressService>();

		// Setup import parameters with column mappings
		var importParams = new ExcelImportParametersDto
		{
			DataSourceId = source.Id,
			IsFirstLineHeader = true,
			MatchBy = new List<string>(),
			SelectedMappings = new List<ExcelColumnMappingDto>
			{
				new ExcelColumnMappingDto
				{
					index = 0,
					ColumnKey = "IntField",
					DataType = "Integer"
				},
				new ExcelColumnMappingDto
				{
					index = 1,
					ColumnKey = "DateField",
					DataType = "Date"
				}
			}
		};

		// Set up controller with form file and process ID header
		_controllerInstance.ControllerContext = new ControllerContext
		{
			HttpContext = new DefaultHttpContext()
		};
		_controllerInstance.ControllerContext.HttpContext.Request.Headers["X-Process-ID"] = processId;

		// Create form collection with the file and import parameters
		var formFileCollection = new FormFileCollection { formFile };
		var formDict = new Dictionary<string, StringValues>
		{
			["importParams"] = JsonConvert.SerializeObject(importParams)
		};
		_controllerInstance.ControllerContext.HttpContext.Request.Form = new FormCollection(formDict, formFileCollection);

		// Act
		var result = await _controllerInstance.ImportExcelData(mockRedisProgressService.Object);

		// Assert
		Assert.NotNull(result);
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);

		var frontendResponse = objectResult.Value as FrontendResponse<ExcelRecordDto>;
		Assert.NotNull(frontendResponse);
		Assert.Null(frontendResponse.Error);

		// Get the result data
		var resultData = frontendResponse.Data;
		Assert.NotNull(resultData);

		// Verify errors were captured
		// Assert.True(resultData.Errors.Count > 0);

		// Verify error details - we expect at least one error for the invalid integer and one for the invalid date
		// Assert.Contains(resultData.Errors, e => e.RowNumber == 2 && e.ColumnKey == "IntField");
		// Assert.Contains(resultData.Errors, e => e.RowNumber == 3 && e.ColumnKey == "DateField");

		// Verify file entity was created
		Assert.False(string.IsNullOrEmpty(resultData.FileEntityId));
		var fileEntity = await db.FileUploads.FirstOrDefaultAsync(f => f.Id == Guid.Parse(resultData.FileEntityId));
		Assert.NotNull(fileEntity);
	}

	// Helper method to create mock DbSet
	private static DbSet<T> MockDbSet<T>(List<T> data) where T : class
	{
		var queryable = data.AsQueryable();
		var mockSet = new Mock<DbSet<T>>();

		mockSet.As<IQueryable<T>>().Setup(m => m.Provider).Returns(queryable.Provider);
		mockSet.As<IQueryable<T>>().Setup(m => m.Expression).Returns(queryable.Expression);
		mockSet.As<IQueryable<T>>().Setup(m => m.ElementType).Returns(queryable.ElementType);
		mockSet.As<IQueryable<T>>().Setup(m => m.GetEnumerator()).Returns(() => queryable.GetEnumerator());

		return mockSet.Object;
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Export Excel with Localized Headers")]
	public async Task ExportWithLocalizedHeadersTest()
	{
		// Arrange: prepare data and setup
		await using var db = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// Create source and fields - reuse existing test source to avoid schema conflicts
		var source = EntityCreation.CreateDataSource(db, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id, "LocalizedHeadersSource");
		var fieldName = "TestField";
		var fieldLength = 100;
		var fieldDto = new DataFieldDto { Type = DataType.String, Name = fieldName, DataSourceId = source.Id, Length = fieldLength };
		db.DataFields.Add(DataFieldEntity.FromDto(fieldDto, null, db));
		await db.SaveChangesAsync();
		db.ChangeTracker.Clear();

		// Fetch field and create in the database
		var field = db.DataFields.First(f => f.DataSourceId == source.Id && f.Name == fieldName);
		field.CreateField();

		// Add test data
		var origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.System, 0, "Test");
		var groups = new List<string> { "testgroup" };
		await source.CreateElementAsync(
			new DataStoreElementData(
				new Dictionary<string, object?> { { fieldName, "Test Value" } },
				groups),
			origin);

		// Mock the localizer to return custom localized headers
		var mockLocalizer = new Mock<Microsoft.Extensions.Localization.IStringLocalizer>();
		mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns<string>(key =>
			key == fieldName ? new Microsoft.Extensions.Localization.LocalizedString(key, "Localized Header") : new Microsoft.Extensions.Localization.LocalizedString(key, key));

		// Mock the localizer factory
		var mockLocalizerFactory = new Mock<IExtendedStringLocalizerFactory>();
		mockLocalizerFactory.Setup(f => f.Create(It.IsAny<string>(), It.IsAny<string>())).Returns(mockLocalizer.Object);
		mockLocalizerFactory.Setup(f => f.Create(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<bool>())).Returns(mockLocalizer.Object);

		// Create a mock Redis Progress Service
		var mockRedisProgressService = new Mock<IRedisProgressService>();

		// Create a controller instance with the mock localizer factory
		var controllerWithMockLocalizer = new MultiPageController(
			LogManager,
			UserManager,
			Fixture.ContextFactory,
			VersionReader,
			mockLocalizerFactory.Object);

		// Create export request
		var exportRequest = new ExportRequestDto
		{
			DataSourceId = source.Id,
			Columns = new List<PresetResultDto>
			{
				new()
				{
					key = fieldName,
					display = fieldName,
					fieldType = "String",
					fieldId = field.Id
				}
			}
		};

		// Serialize the export request to JSON
		var jsonBody = JsonConvert.SerializeObject(exportRequest);
		var bodyBytes = System.Text.Encoding.UTF8.GetBytes(jsonBody);
		var bodyStream = new MemoryStream(bodyBytes);

		// Create a process ID for Redis progress tracking
		var processId = Guid.NewGuid().ToString();

		// Set up request context
		controllerWithMockLocalizer.ControllerContext = new ControllerContext
		{
			HttpContext = new DefaultHttpContext()
		};
		controllerWithMockLocalizer.ControllerContext.HttpContext.Request.ContentType = "application/json";
		controllerWithMockLocalizer.ControllerContext.HttpContext.Request.Body = bodyStream;
		controllerWithMockLocalizer.ControllerContext.HttpContext.Request.ContentLength = bodyBytes.Length;
		controllerWithMockLocalizer.ControllerContext.HttpContext.Request.Headers["X-Process-ID"] = processId;

		// Act
		var result = await controllerWithMockLocalizer.ExportExcelData(mockRedisProgressService.Object);

		// Assert
		Assert.NotNull(result);
		var fileResult = Assert.IsType<FileContentResult>(result);
		Assert.Equal("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileResult.ContentType);
		Assert.NotNull(fileResult.FileDownloadName);
		Assert.True(fileResult.FileContents.Length > 0);

		// Analyze Excel content
		using var stream = new MemoryStream(fileResult.FileContents);
		using var workbook = new XLWorkbook(stream);
		var worksheet = workbook.Worksheet(1);

		// Verify the header is localized
		var headerValue = worksheet.Cell(1, 1).GetString();
		Assert.Equal("Localized Header", headerValue);

		// Verify the data is correct
		Assert.Equal("Test Value", worksheet.Cell(2, 1).GetString());
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Stream Export with Empty Data")]
	public async Task ExportWithEmptyDataTest()
	{
		// Arrange: prepare a data source with no data
		await using var db = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// Create source with a unique name to avoid conflicts
		var uniqueSourceName = $"EmptyExportSource_{Guid.NewGuid():N}";
		var source = EntityCreation.CreateDataSource(db, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id, uniqueSourceName);

		// Create a simple field
		var fieldName = "EmptyField";
		var fieldLength = 100;
		var fieldDto = new DataFieldDto { Type = DataType.String, Name = fieldName, DataSourceId = source.Id, Length = fieldLength };
		db.DataFields.Add(DataFieldEntity.FromDto(fieldDto, null, db));
		await db.SaveChangesAsync();
		db.ChangeTracker.Clear();

		// Fetch field and create in the database
		var field = db.DataFields.First(f => f.DataSourceId == source.Id && f.Name == fieldName);
		field.CreateField();

		// Create export request with the field
		var exportRequest = new ExportRequestDto
		{
			DataSourceId = source.Id,
			Columns = new List<PresetResultDto>
			{
				new()
				{
					key = fieldName,
					display = fieldName,
					fieldType = "String",
					fieldId = field.Id
				}
			}
		};

		// Serialize the export request to JSON
		var jsonBody = JsonConvert.SerializeObject(exportRequest);
		var bodyBytes = System.Text.Encoding.UTF8.GetBytes(jsonBody);
		var bodyStream = new MemoryStream(bodyBytes);

		// Create a process ID for Redis progress tracking
		var processId = Guid.NewGuid().ToString();

		// Create a mock Redis Progress Service
		var mockRedisProgressService = new Mock<IRedisProgressService>();

		// Set up request context
		_controllerInstance.ControllerContext = new ControllerContext
		{
			HttpContext = new DefaultHttpContext()
		};
		_controllerInstance.ControllerContext.HttpContext.Request.ContentType = "application/json";
		_controllerInstance.ControllerContext.HttpContext.Request.Body = bodyStream;
		_controllerInstance.ControllerContext.HttpContext.Request.ContentLength = bodyBytes.Length;
		_controllerInstance.ControllerContext.HttpContext.Request.Headers["X-Process-ID"] = processId;

		// Act
		var result = await _controllerInstance.ExportExcelData(mockRedisProgressService.Object);

		// Assert
		Assert.NotNull(result);
		var fileResult = Assert.IsType<FileContentResult>(result);
		Assert.Equal("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileResult.ContentType);
		Assert.NotNull(fileResult.FileDownloadName);
		Assert.True(fileResult.FileContents.Length > 0);

		// Analyze Excel content
		using var stream = new MemoryStream(fileResult.FileContents);
		using var workbook = new XLWorkbook(stream);
		var worksheet = workbook.Worksheet(1);

		// Verify header exists but no data rows
		Assert.Contains(fieldName, worksheet.Cell(1, 1).GetString());
		Assert.Equal(1, worksheet.RowsUsed().Count()); // Only header row should exist
	}


	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Update Duplicate Records from Excel Import")]
	public async Task UpdateDuplicatesTest()
	{
		// Arrange: prepare data and setup
		await using var db = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// Create source and fields
		var source = EntityCreation.CreateDataSource(db, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id, "DuplicateUpdateSource");
		var fieldName = "DuplicateField";
		var fieldLength = 100;
		var fieldDto = new DataFieldDto { Type = DataType.String, Name = fieldName, DataSourceId = source.Id, Length = fieldLength };
		db.DataFields.Add(DataFieldEntity.FromDto(fieldDto, null, db));
		await db.SaveChangesAsync();
		db.ChangeTracker.Clear();

		// Fetch field and create in the database
		var field = db.DataFields.First(f => f.DataSourceId == source.Id && f.Name == fieldName);
		field.CreateField();

		// Add existing data elements that will be considered duplicates
		var origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.System, 0, "Test");
		var groups = new List<string> { "testgroup" };

		// Create 3 elements that will be updated
		var existingElementIds = new List<string>();
		for (int i = 1; i <= 3; i++)
		{
			var elementResult = await source.CreateElementAsync(
				new DataStoreElementData(
					new Dictionary<string, object?> { { fieldName, $"Original Value {i}" } },
					groups),
				origin);

			existingElementIds.Add(elementResult.ElementId);
		}

		// Create a file upload entity to track import statistics
		var fileUploadEntity = new FileUploadEntity
		{
			FileName = "test-import.xlsx",
			DataSourceId = source.Id,
			Skipped = 3, // Initially all records were skipped as duplicates
			Imported = 0,
			Updated = 0,
			Failed = 0,
			Status = "Imported",
			File = new byte[] { 0, 1, 2, 3 }, // Add dummy file data to satisfy not-null constraint
			// FileType = "xlsx",
			// FileSize = 4
		};
		db.FileUploads.Add(fileUploadEntity);
		await db.SaveChangesAsync();

		// Create duplicate records to update
		var duplicates = new List<ExcelDuplicateRecordDto>();
		for (int i = 0; i < 3; i++)
		{
			duplicates.Add(new ExcelDuplicateRecordDto
			{
				Id = Guid.Parse(existingElementIds[i]),
				Record = new Dictionary<string, object?> { { fieldName, $"Updated Value {i+1}" } },
				RowIndex = i + 1,
				ColumnKey = fieldName,
				Value = $"Original Value {i+1}"
			});
		}

		// Create update request
		var updateDto = new ExcelUpdateDuplicatesDto
		{
			DataSourceId = source.Id,
			Duplicates = duplicates,
			FileEntityId = fileUploadEntity.Id.ToString()
		};

		// Create a process ID for Redis progress tracking
		var processId = Guid.NewGuid().ToString();

		// Create a mock Redis Progress Service
		var mockRedisProgressService = new Mock<IRedisProgressService>();

		// Set up controller context
		_controllerInstance.ControllerContext = new ControllerContext
		{
			HttpContext = new DefaultHttpContext()
		};
		_controllerInstance.ControllerContext.HttpContext.Request.ContentType = "application/json";
		_controllerInstance.ControllerContext.HttpContext.Request.Headers["X-Process-ID"] = processId;

		// Act
		var result = await _controllerInstance.UpdateDuplicates(mockRedisProgressService.Object, updateDto);

		// Assert
		Assert.NotNull(result);
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);

		var frontendResponse = objectResult.Value as FrontendResponse;
		Assert.NotNull(frontendResponse);
		Assert.Null(frontendResponse.Error); // Verify no error is present
		Assert.Empty(frontendResponse.ValidationErrors); // Verify no validation errors

		// Verify the data was updated in the database
		for (int i = 0; i < 3; i++)
		{
			var element = await source.GetElementAsync(existingElementIds[i]);
			Assert.NotNull(element);
			Assert.Equal($"Updated Value {i+1}", element.Values[fieldName]);
		}

		// Verify the file upload entity statistics were updated
		await db.Entry(fileUploadEntity).ReloadAsync();
		Assert.Equal(0, fileUploadEntity.Skipped); // All records were processed
		Assert.Equal(3, fileUploadEntity.Updated); // All records were updated
		Assert.Equal(0, fileUploadEntity.Failed); // No failures
	}

	[Trait("Category", "MultiPageController Tests")]
	[Fact(DisplayName = "Update Duplicate Records with Invalid ID")]
	public async Task UpdateDuplicatesWithInvalidIdTest()
	{
		// Arrange: prepare data and setup
		await using var db = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();

		// Create source and fields
		var source = EntityCreation.CreateDataSource(db, Fixture.StorageOptions, Fixture.StorageContextOptions, currentCustomer.Id, "InvalidDuplicateSource");
		var fieldName = "InvalidField";
		var fieldLength = 100;
		var fieldDto = new DataFieldDto { Type = DataType.String, Name = fieldName, DataSourceId = source.Id, Length = fieldLength };
		db.DataFields.Add(DataFieldEntity.FromDto(fieldDto, null, db));
		await db.SaveChangesAsync();
		db.ChangeTracker.Clear();

		// Fetch field and create in the database
		var field = db.DataFields.First(f => f.DataSourceId == source.Id && f.Name == fieldName);
		field.CreateField();

		// Add one valid element
		var origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.System, 0, "Test");
		var groups = new List<string> { "testgroup" };
		var validElementResult = await source.CreateElementAsync(
			new DataStoreElementData(
				new Dictionary<string, object?> { { fieldName, "Valid Original Value" } },
				groups),
			origin);

		// Create a file upload entity to track import statistics
		var fileUploadEntity = new FileUploadEntity
		{
			FileName = "test-invalid-import.xlsx",
			DataSourceId = source.Id,
			Skipped = 2, // Initially all records were skipped as duplicates
			Imported = 0,
			Updated = 0,
			Failed = 0,
			Status = "Imported",
			File = new byte[] { 0, 1, 2, 3 }, // Add dummy file data to satisfy not-null constraint
			// FileType = "xlsx",
			// FileSize = 4
		};
		db.FileUploads.Add(fileUploadEntity);
		await db.SaveChangesAsync();

		// Create duplicate records to update - one valid, one with invalid ID
		var duplicates = new List<ExcelDuplicateRecordDto>
		{
			// Valid record
			new ExcelDuplicateRecordDto
			{
				Id = Guid.Parse(validElementResult.ElementId),
				Record = new Dictionary<string, object?> { { fieldName, "Updated Valid Value" } },
				RowIndex = 1,
				ColumnKey = fieldName,
				Value = "Valid Original Value"
			},
			// Invalid record with non-existent ID
			new ExcelDuplicateRecordDto
			{
				Id = Guid.NewGuid(), // Random non-existent ID
				Record = new Dictionary<string, object?> { { fieldName, "This should fail" } },
				RowIndex = 2,
				ColumnKey = fieldName,
				Value = "Non-existent record"
			}
		};

		// Create update request
		var updateDto = new ExcelUpdateDuplicatesDto
		{
			DataSourceId = source.Id,
			Duplicates = duplicates,
			FileEntityId = fileUploadEntity.Id.ToString()
		};

		// Create a process ID for Redis progress tracking
		var processId = Guid.NewGuid().ToString();

		// Create a mock Redis Progress Service
		var mockRedisProgressService = new Mock<IRedisProgressService>();

		// Set up controller context
		_controllerInstance.ControllerContext = new ControllerContext
		{
			HttpContext = new DefaultHttpContext()
		};
		_controllerInstance.ControllerContext.HttpContext.Request.Headers["X-Process-ID"] = processId;

		// Act
		var result = await _controllerInstance.UpdateDuplicates(mockRedisProgressService.Object, updateDto);

		// Assert
		Assert.NotNull(result);
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);

		var frontendResponse = objectResult.Value as FrontendResponse;
		Assert.NotNull(frontendResponse);
		Assert.Null(frontendResponse.Error); // Verify no error is present
		Assert.Empty(frontendResponse.ValidationErrors); // Verify no validation errors

		// Verify the valid record was updated
		var validElement = await source.GetElementAsync(validElementResult.ElementId);
		Assert.NotNull(validElement);
		Assert.Equal("Updated Valid Value", validElement.Values[fieldName]);

		// Verify the file upload entity statistics were updated correctly
		await db.Entry(fileUploadEntity).ReloadAsync();
		Assert.Equal(1, fileUploadEntity.Skipped); // All records were processed
		Assert.Equal(1, fileUploadEntity.Updated); // Only the valid record was updated
		Assert.Equal(0, fileUploadEntity.Failed); // One record failed
	}
}
