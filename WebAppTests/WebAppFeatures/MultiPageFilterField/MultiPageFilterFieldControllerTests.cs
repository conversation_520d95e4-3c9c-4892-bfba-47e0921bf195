using Google.Api.Gax;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.Page.MultiData;
using Levelbuild.Frontend.WebApp.Features.MultiPageFilterField.Controllers;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Microsoft.AspNetCore.Mvc;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.MultiPageFilterField;

[ExcludeFromCodeCoverage]
public class PostgresMultiPageFilterFieldControllerTests(PostgresDatabaseFixture fixture) : MultiPageFilterFieldControllerTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

[ExcludeFromCodeCoverage]
public abstract class MultiPageFilterFieldControllerTests : AdminControllerTest<MultiPageFilterFieldController, MultiPageFilterFieldEntity, MultiPageFilterFieldDto>
{
	#region Test Data Properties
	
	protected override MultiPageFilterFieldDto CreateDto
	{
		get
		{
			using var databaseContext = Fixture.Context;
			var currentCustomer = UserManager.GetCurrentCustomerAsync().ResultWithUnwrappedExceptions();
			var field = EntityCreation.CreateDataField(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
													   currentCustomer.Id);
			
			var page = EntityCreation.CreatePageMulti(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
													  currentCustomer.Id);
			
			return new MultiPageFilterFieldDto()
			{
				PageId = page.Id,
				Position = 0,
				FieldId = field.Id,
				FieldName = field.Name,
				FieldType = field.Type,
				MultiValue = true,
				ValuePreview = true,
				DisplayInPanel = false
			};
		}
	}
	
	protected override MultiPageFilterFieldDto InvalidCreateDto => new()
	{
		FieldName = "company-name",
		FieldId = Guid.NewGuid(),
		PageId = Guid.NewGuid()
	};
	
	protected override MultiPageFilterFieldDto InvalidUpdateDto => new()
	{
		Id = Guid.Empty,
		PageId = Guid.Empty,
	};
	
	protected override QueryParamsDto QueryParams => new()
	{
		Sortings = new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "position",
				Direction = SortDirection.Asc
			}
		}
	};
	
	protected override QueryParamsDto QueryParamsWithLimitAndOffset => new()
	{
		Sortings = new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "position",
				Direction = SortDirection.Asc
			}
		},
		Limit = 2,
		Offset = 1
	};
	
	protected override QueryParamsDto QueryParamsWithSorting => new()
	{
		Sortings = new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "pageId",
				Direction = SortDirection.Desc
			},
			new()
			{
				OrderColumn = "position",
				Direction = SortDirection.Asc
			}
		}
	};
	
	protected override QueryParamsDto QueryParamsWithFilter => new()
	{
		Filters = new List<QueryParamFilterDto>()
		{
			new()
			{
				FilterColumn = "position",
				Operator = QueryParamFilterOperator.Equals,
				CompareValue = "1"
			}
		}
	};
	
	#endregion

	protected MultiPageFilterFieldControllerTests(DatabaseFixture fixture) : base(fixture, initStorage: true)
	{
		ControllerInstance = new MultiPageFilterFieldController(LogManager, Fixture.ContextFactory, UserManager, LocalizerFactory, VersionReader);
	}
	
	#region Data Preparation
	
	protected override MultiPageFilterFieldEntity PrepareSingleEntity()
	{
		using var databaseContext = Fixture.Context;
		databaseContext.MultiPageFilterFields.Add(MultiPageFilterFieldEntity.FromDto(CreateDto, null, databaseContext));
		databaseContext.SaveChanges();
		databaseContext.ChangeTracker.Clear();
		
		var entity = databaseContext.MultiPageFilterFields.First();
		
		return entity;
	}
	
	protected override MultiPageFilterFieldDto GetUpdateDto(MultiPageFilterFieldEntity entity)
	{
		var dto = entity.ToDto();
		dto.Position = 1;
		return dto;
	}
	
	protected override async Task PrepareQueryDataAsync()
	{
		await using var databaseContext = Fixture.Context;
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();
		var field = EntityCreation.CreateDataField(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
												   currentCustomer.Id, "testField");
		
		var secondField = EntityCreation.CreateDataField(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
														 currentCustomer.Id, "secondTestField");
		
		var thirdField = EntityCreation.CreateDataField(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
														currentCustomer.Id, "thirdTestField");
		
		var fourthField = EntityCreation.CreateDataField(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
														 currentCustomer.Id, "fourthTestField");
		
		var page = EntityCreation.CreatePageMulti(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
												  currentCustomer.Id);
		
		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000001"),
			FieldId = field.Id,
			FieldName = field.Name,
			FieldType = field.Type,
			PageId = page.Id,
			DisplayInPanel = true,
			MultiValue = false,
			ValuePreview = true,
			Position = 1,
		});
		
		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000002"),
			FieldId = secondField.Id,
			FieldName = secondField.Name,
			FieldType = field.Type,
			PageId = page.Id,
			DisplayInPanel = false,
			MultiValue = true,
			ValuePreview = false,
			Position = 2,
		});
		
		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000003"),
			FieldId = thirdField.Id,
			FieldName = thirdField.Name,
			FieldType = field.Type,
			PageId = page.Id,
			DisplayInPanel = true,
			MultiValue = false,
			ValuePreview = false,
			Position = 3,
		});
		
		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000004"),
			FieldId = fourthField.Id,
			FieldName = fourthField.Name,
			FieldType = field.Type,
			PageId = page.Id,
			DisplayInPanel = false,
			MultiValue = false,
			ValuePreview = true,
			Position = 4,
		});
		
		foreach (var dto in QueryData)
		{
			databaseContext.MultiPageFilterFields.Add(MultiPageFilterFieldEntity.FromDto(dto, null, databaseContext));
		}
		
		await databaseContext.SaveChangesAsync();
	}
	
	#endregion
	
	#region Assertions
	
	protected override void AssertIsAsExpected(MultiPageFilterFieldDto expected, MultiPageFilterFieldDto actual)
	{
		Assert.Equal(expected.MultiValue, actual.MultiValue);
		Assert.Equal(expected.ValuePreview, actual.ValuePreview);
		Assert.Equal(expected.DisplayInPanel, actual.DisplayInPanel);
		Assert.Equal(expected.FieldId, actual.FieldId);
		Assert.Equal(expected.Position, actual.Position);
		
		if(expected.FieldName != null && actual.FieldName != null)
			Assert.Equal(expected.FieldName, actual.FieldName);
		
		if(expected.FieldType != null && actual.FieldType != null)
			Assert.Equal(expected.FieldType, actual.FieldType);
		
		Assert.Equal(expected.PageId, actual.PageId);
	}
	
	protected override void AssertExists(MultiPageFilterFieldDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.MultiPageFilterFields.Any(sorting => sorting.FieldId == dto.FieldId && sorting.PageId == dto.PageId));
	}
	
	protected override void AssertDoesNotExist(MultiPageFilterFieldDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.MultiPageFilterFields.All(sorting => sorting.FieldId != dto.FieldId || sorting.PageId != dto.PageId));
	}
	
	protected override bool DeleteSuccessful(MultiPageFilterFieldEntity entity)
	{
		using var databaseContext = Fixture.Context;
		return databaseContext.MultiPageFilterFields.Find(entity.Id) == null;
	}
	
	protected override bool CheckQueryResult(ConfigQueryResultDto<MultiPageFilterFieldDto> queryResultDto, int limit = 0, int offset = 0)
	{
		var count = QueryData.Count;
		var mutatedNamesList = queryResultDto.Rows.ToList().ConvertAll(config => config.FieldName);
		var expectedList = new List<string>();
		for (var i = offset; i < count && (expectedList.Count < limit || limit == 0); i++)
		{
			expectedList.Add(QueryData[i].FieldName!);
		}
		
		if (queryResultDto.CountTotal != count)
			return false;
		if (limit > 0 && queryResultDto.Rows.Count > limit)
			return false;
		
		for (var i = 0; i < mutatedNamesList.Count; i++)
		{
			if (expectedList[i] != mutatedNamesList[i])
				return false;
		}
		
		return true;
	}
	
	protected override bool CheckSortingQueryResult(ConfigQueryResultDto<MultiPageFilterFieldDto> queryResultDto)
	{
		var count = QueryData.Count;
		var mutatedNamesList = queryResultDto.Rows.ToList().ConvertAll(config => config.FieldName);
		var expectedList = QueryData.OrderByDescending(sorting => sorting.PageId).ThenBy(sorting => sorting.Position).ToList();
		
		if (queryResultDto.CountTotal != count)
			return false;
		if (queryResultDto.Rows.Count != count)
			return false;
		
		for (var i = 0; i < mutatedNamesList.Count; i++)
		{
			if (expectedList[i].FieldName != mutatedNamesList[i])
				return false;
		}
		
		return true;
	}
	
	protected override bool CheckFilterQueryResult(ConfigQueryResultDto<MultiPageFilterFieldDto> queryResultDto)
	{
		if (queryResultDto.CountTotal != 1)
			return false;
		if (queryResultDto.Rows.Count != 1)
			return false;
		
		return QueryData[0].FieldName == queryResultDto.Rows.FirstOrDefault()?.FieldName;
	}
	
	#endregion
	
	#region Tests
	
	[Trait("Category", "MultiPageFilterFieldController Tests")]
	[Fact(DisplayName = "Update one of multiple entities and check positions")]
	public async Task UpdatePositionTest()
	{
		await using var databaseContext = Fixture.Context;
		
		await PrepareQueryDataAsync();
		var entity = databaseContext.MultiPageFilterFields.FirstOrDefault(sorting => sorting.Position == 2)!;
		var updateDto = GetUpdateDto(entity);
		var entitiesBeforeUpdate = databaseContext.MultiPageFilterFields.OrderBy(sorting => sorting.Position).ToList();
		
		var result = await ControllerInstance!.Update(entity.Id, updateDto);
		var code = (result.Result as ObjectResult)?.StatusCode;
		var config = ((result.Result as ObjectResult)?.Value as FrontendResponse<MultiPageFilterFieldDto>)?.Data;
		databaseContext.ChangeTracker.Clear();
		var entitiesAfterUpdate =  databaseContext.MultiPageFilterFields.OrderBy(sorting => sorting.Position).ToList();
		
		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.NotNull(config);
		AssertIsAsExpected(updateDto, config);
		Assert.True(entitiesAfterUpdate[0].Id == entitiesBeforeUpdate[1].Id && entitiesAfterUpdate[0].Position == 1);
		Assert.True(entitiesAfterUpdate[1].Id == entitiesBeforeUpdate[0].Id && entitiesAfterUpdate[1].Position == 2);
		Assert.True(entitiesAfterUpdate[2].Id == entitiesBeforeUpdate[2].Id && entitiesAfterUpdate[2].Position == 3);
		Assert.True(entitiesAfterUpdate[3].Id == entitiesBeforeUpdate[3].Id && entitiesAfterUpdate[3].Position == 4);
	}
	
	[Trait("Category", "MultiPageFilterFieldController Tests")]
	[Fact(DisplayName = "Delete one of multiple entities and check positions")]
	public async Task DeletePositionTest()
	{
		await using var databaseContext = Fixture.Context;
		
		await PrepareQueryDataAsync();
		var entity = databaseContext.MultiPageFilterFields.FirstOrDefault(sorting => sorting.Position == 2);
		
		var result = ControllerInstance!.Delete(entity!.Id);
		var code = (result.Result as ObjectResult)?.StatusCode;
		databaseContext.ChangeTracker.Clear();
		var entitiesAfterDelete =  databaseContext.MultiPageFilterFields.OrderBy(sorting => sorting.Position).ToList();
		
		Assert.NotNull(result);
		Assert.Equal(200, code);
		Assert.True(entitiesAfterDelete[0].Position == 1);
		Assert.True(entitiesAfterDelete[1].Position == 2);
		Assert.True(entitiesAfterDelete[2].Position == 3);
	}
	
	#endregion
}