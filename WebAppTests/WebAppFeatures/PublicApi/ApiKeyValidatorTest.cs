using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities;
using Levelbuild.Frontend.WebApp.Features.PublicApi.Services;
using Microsoft.Extensions.DependencyInjection;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.PublicApi;

[ExcludeFromCodeCoverage]
public class PostgresApiKeyValidatorTest(PostgresDatabaseFixture fixture) : ApiKeyValidatorTest(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

[ExcludeFromCodeCoverage]
public abstract class ApiKeyValidatorTest : IntegrationTest
{
	private readonly CoreDatabaseContext _apiKeyValidatorContext;
	private readonly ApiKeyValidator _apiKeyValidator;
	
	/// <summary>
	/// Constructor.
	/// </summary>
	/// <param name="fixture"></param>
	/// <param name="additionalServiceInjection"></param>
	/// <param name="initStorage"></param>
	protected ApiKeyValidatorTest(DatabaseFixture fixture, Action<IServiceCollection>? additionalServiceInjection = null, bool initStorage = false) : base(fixture, additionalServiceInjection, initStorage)
	{
		_apiKeyValidatorContext = Fixture.Context;
		_apiKeyValidator = new ApiKeyValidator(_apiKeyValidatorContext, UserManager);
	}
	
	#region Tests
	
	[Fact(DisplayName = "Validate ApiKey")]
	public async Task BasicTest()
	{
		await using var databaseContext = Fixture.Context;
		var device = await EntityCreation.CreateDeviceAsync(databaseContext, UserManager);
		
		var isValid = await _apiKeyValidator.ValidateApiKeyAsync(device.ApiKey);
		Assert.True(isValid);
	}
	
	[Fact(DisplayName = "Fail to Validate disabled Device")]
	public async Task DisabledDeviceTest()
	{
		await using var databaseContext = Fixture.Context;
		var device = await EntityCreation.CreateDeviceAsync(databaseContext, UserManager, enabled: false);
		
		var isValid = await _apiKeyValidator.ValidateApiKeyAsync(device.ApiKey);
		Assert.False(isValid);
	}
	
	[Fact(DisplayName = "Fail to Validate ApiKey without device")]
	public async Task MissingDeviceTest()
	{
		await using var databaseContext = Fixture.Context;
		var device = await EntityCreation.CreateDeviceAsync(databaseContext, UserManager, enabled: false);
		var apiKey = device.ApiKey;
		databaseContext.Devices.Remove(device);
		await databaseContext.SaveChangesAsync();
		
		var isValid = await _apiKeyValidator.ValidateApiKeyAsync(apiKey);
		Assert.False(isValid);
	}
	
	[Fact(DisplayName = "Fail to Validate ApiKey for wrong User")]
	public async Task WrongUserTest()
	{
		await using var databaseContext = Fixture.Context;
		var customer = EntityCreation.CreateCustomer(databaseContext, ZitadelApiClientFactory);
		var user = EntityCreation.CreateUser(databaseContext, ZitadelApiClientFactory, customer.DisplayName);
		var device = await EntityCreation.CreateDeviceAsync(databaseContext, UserManager, enabled: false, userId: user.Id);
		
		var isValid = await _apiKeyValidator.ValidateApiKeyAsync(device.ApiKey);
		Assert.False(isValid);
	}
	
	#endregion

	public new void Dispose()
	{
		_apiKeyValidatorContext.Dispose();
		base.Dispose();
	}
}