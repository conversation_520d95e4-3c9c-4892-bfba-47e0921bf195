using System.Globalization;
using System.Text;
using System.Text.Json.Nodes;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Services;
using Levelbuild.Frontend.WebApp.Features.Logging.Controllers;
using Levelbuild.Frontend.WebApp.Features.Logging.ViewModels;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Hosting.Internal;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.Logging;

[ExcludeFromCodeCoverage]
public class PostgresLoggingControllerTests(PostgresDatabaseFixture fixture) : LoggingControllerTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

/*[ExcludeFromCodeCoverage]
public class SqlServerLoggerConfigControllerTests : LoggerConfigControllerTests, IClassFixture<SqlServerDatabaseFixture>
{
	public SqlServerLoggerConfigControllerTests(SqlServerDatabaseFixture fixture) : base(fixture)
	{
		// nothing
	}
}*/

[ExcludeFromCodeCoverage]
public abstract class LoggingControllerTests: IntegrationTest
{
	private readonly LoggingController _controllerInstance;
	private readonly string _namespace;
	private DateTime _currentTime;
	private readonly IConfiguration _config;

	private static readonly Action<IServiceCollection> ServiceInjection = services =>
	{
		services.AddSingleton<LogManager>();
		services.AddSingleton<IHostEnvironment>(new HostingEnvironment() { EnvironmentName = Environments.Development });
		services.AddSingleton<IConfiguration>(new ConfigurationBuilder()
			.AddJsonFile("testsettings.json", optional: false)
			.AddEnvironmentVariables()
			.Build().GetSection("WebApp"));
		services.AddSingleton<IExtendedStringLocalizerFactory, StringLocalizerFactory>();
	};

	protected LoggingControllerTests(DatabaseFixture fixture) : base(fixture, ServiceInjection)
	{
		_config = fixture.ServiceProvider.GetRequiredService<IConfiguration>();
		var task = SetupLogs();
		task.ConfigureAwait(false);
		if (!task.Wait(120000))
			throw new TimeoutException("SetupLogs did not finish on time.");
		_namespace = task.Result;
		var logManager = fixture.ServiceProvider.GetRequiredService<LogManager>();
		var builder = new ConfigurationBuilder()
			.AddConfiguration(_config)
			.AddInMemoryCollection(new Dictionary<string, string?>()
			{
				["LoggingSource:NamespaceWhitelist:0"] = _namespace
			});
		_config = builder.Build();
		
		_controllerInstance = new LoggingController(logManager, _config, VersionReader);
		var waitFor = WaitForFullIngestion(_controllerInstance);
		waitFor.ConfigureAwait(false);
		if (!waitFor.Wait(30000))
			throw new TimeoutException("WaitForFullIngestion did not finish on time.");
	}
	
	#region Tests

	/// <summary>
	/// Tests the <see cref="LoggingController.Config"/> API to receive the
	/// configured labels and namespaces for the logging feature.
	/// </summary>
	[Trait("Category", "Logging controller")]
	[Fact(DisplayName = "Get namespaces")]
	public async Task GetNamespaces()
	{
		var namespaces = await _controllerInstance.Config();
		var config = ((FrontendResponse<LogsConfig>)((ObjectResult)namespaces.Result!).Value!).Data;
		Assert.NotEmpty(config.Labels);
		Assert.Contains("level", config.Labels[_namespace]);
		Assert.Contains("host", config.Labels[_namespace]);
		Assert.Contains("Application", config.Labels[_namespace]);
		Assert.NotEmpty(config.NamespaceConfigs);
		Assert.Equal(11, config.NamespaceConfigs[_namespace].Count);
		Assert.Equal("api", config.NamespaceConfigs[_namespace][0]["source"]);
	}

	[Trait("Category", "Logging controller")]
	[Fact(DisplayName = "Test basic log queries")]
	public async Task BasicLogQueries()
	{
		var res = await _controllerInstance.Query(new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>
			{
				new()
				{
					FilterColumn = "K8SNamespace",
					CompareValue = _namespace
				}
			},
			Limit = 100
		});

		var response = ((FrontendResponse<QueryResultDto<IDictionary<string, object>>>)((ObjectResult)res.Result!).Value!).Data;
		
		var firstLine = response.Rows.FirstOrDefault();
		Assert.Equal(100, response.CountTotal);
		Assert.Equal(response.CountTotal, response.Rows.Count);
		Assert.NotNull(firstLine);
		AssertDateTimeEqual(_currentTime, (DateTime)firstLine["timestamp"], "timestamp of last entry matches");
		
		var invertedRes = await _controllerInstance.Query(new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>
			{
				new()
				{
					FilterColumn = "K8SNamespace",
					CompareValue = _namespace
				}
			},
			Limit = 100,
			Sortings = new List<QueryParamSortingDto>()
			{
				new("timestamp", SortDirection.Asc)
			}
		});

		var invertedResponse = ((FrontendResponse<QueryResultDto<IDictionary<string, object>>>)((ObjectResult)invertedRes.Result!).Value!).Data;
		// log currently spans 24mins and 52s.
		AssertDateTimeEqual(_currentTime - TimeSpan.FromSeconds(24 * 60 + 52), (DateTime)invertedResponse.Rows.First()["timestamp"]);
		
		var invertedAndFilteredRes = await _controllerInstance.Query(new QueryParamsDto()
		{
			Filters = new List<QueryParamFilterDto>
			{
				new()
				{
					FilterColumn = "K8SNamespace",
					CompareValue = _namespace
				},
				new ()
				{
					FilterColumn = "timestamp",
					CompareValue = ((DateTime)response.Rows.Last()["timestamp"]).ToString("o", CultureInfo.InvariantCulture),
					Operator = QueryParamFilterOperator.GreaterThan
				}
			},
			Limit = 100,
			Sortings = new List<QueryParamSortingDto>()
			{
				new("timestamp", SortDirection.Asc)
			}
		});

		var invertedAndFilteredResponse = ((FrontendResponse<QueryResultDto<IDictionary<string, object>>>)((ObjectResult)invertedAndFilteredRes.Result!).Value!).Data;
		AssertDateTimeEqual((DateTime)response.Rows.Last()["timestamp"], (DateTime)invertedAndFilteredResponse.Rows.First()["timestamp"]);
		AssertDateTimeEqual((DateTime)response.Rows.First()["timestamp"], (DateTime)invertedAndFilteredResponse.Rows.Last()["timestamp"]);
	}

	#endregion

	private void AssertDateTimeEqual(DateTime expected, DateTime actual, string description = "", int tolerance = 2)
	{
		description = string.IsNullOrWhiteSpace(description) ? "" : description + " - ";
		
		Assert.True(((DateTimeOffset)expected).ToUnixTimeSeconds()-tolerance <= ((DateTimeOffset)actual).ToUnixTimeSeconds(), 
					$"{description}expected {expected.ToString("o", CultureInfo.InvariantCulture)} - {tolerance} <= {actual.ToString("o", CultureInfo.InvariantCulture)}");
		
		Assert.True(((DateTimeOffset)expected).ToUnixTimeSeconds()+tolerance >= ((DateTimeOffset)actual).ToUnixTimeSeconds(),
					$"{description}expected {expected.ToString("o", CultureInfo.InvariantCulture)} + {tolerance} >= {actual.ToString("o", CultureInfo.InvariantCulture)}");
	}

	#region management stuff

	/// <summary>
	/// Sets up the logs for the tests.
	/// <para>
	/// This method reads a file containing log lines, adjusts the timestamp to be relative to the current time,
	/// and sends each line to the Grafana server.
	/// </para>
	/// <para>
	/// The namespace of the logs is set to "loggingtest_" followed by a GUID.
	/// </para>
	/// </summary>
	/// <returns>The namespace of the logs.</returns>
	private async Task<string> SetupLogs()
	{
		var url = _config.GetSection("LoggingSource")["AlloyUrl"]!;
		
		var client = new HttpClient();

		var filePath = "./test.log";
		
		var nameSpace = "loggingtest_" + Guid.NewGuid();
		
		string[] lines = await File.ReadAllLinesAsync(filePath);

		// Get the timestamp of the last line
		var lastLine = lines[^1];
		var lastLineTimestamp = GetTimestampFromLine(lastLine);

		// Calculate the time difference
		_currentTime = DateTime.UtcNow;
		var timeToAdd = _currentTime - lastLineTimestamp;


		StringBuilder sb = new StringBuilder();
		// Read each line from the file and send it to Grafana
		foreach (var line in lines)
		{
			var lineTimestamp = GetTimestampFromLine(line);
			var adjustedTimestamp = lineTimestamp + timeToAdd;
			
			var jsonLine = JsonNode.Parse(line)!;
			jsonLine["timestamp"] = adjustedTimestamp.ToString("o", CultureInfo.InvariantCulture);
			jsonLine["namespace"] = nameSpace;
			
			sb.AppendLine(jsonLine.ToJsonString());
		}

		await client.PostAsync(url, new StringContent(sb.ToString(), Encoding.UTF8, "application/json"));
		return nameSpace;
	}
	
	/// <summary>
	/// Extracts the timestamp from a JSON-formatted log line. The log line is expected to have a "timestamp" property.
	/// </summary>
	/// <param name="jsonLine">The log line to parse.</param>
	/// <returns>The timestamp as a DateTime object.</returns>
	/// <exception cref="Exception">If the log line does not contain a "timestamp" property, or if the value is not a valid DateTime string.</exception>
	private static DateTime GetTimestampFromLine(string jsonLine)
	{
		var jsonObj = JsonNode.Parse(jsonLine)!;
		string timestampStr = jsonObj["timestamp"]!.ToString();
		return DateTime.Parse(timestampStr).ToUniversalTime();
	}

	private async Task WaitForFullIngestion(LoggingController controllerInstance)
	{
		DateTimeOffset dateTimeOffset = _currentTime;
		IDictionary<string, object>? lines;
		do
		{
			var res = await controllerInstance.Query(new QueryParamsDto()
			{
				Filters = new List<QueryParamFilterDto>
				{
					new()
					{
						FilterColumn = "K8SNamespace",
						CompareValue = _namespace
					}
				},
				Limit = 1
			});

			lines = ((FrontendResponse<QueryResultDto<IDictionary<string, object>>>)((ObjectResult)res.Result!).Value!).Data.Rows.FirstOrDefault();
		}
		// timestamp precision is lost through log ingestion
		while (lines == null || !lines.ContainsKey("timestamp") ||
			   ((DateTimeOffset)(DateTime)lines["timestamp"]).ToUnixTimeSeconds() !=
			   dateTimeOffset.ToUnixTimeSeconds());
	}
	#endregion
}