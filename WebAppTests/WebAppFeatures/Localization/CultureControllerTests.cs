using System.Globalization;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Localization;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.Localization;
using Levelbuild.Frontend.WebApp.Features.Localization.Controllers;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Services;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.Localization;
using Microsoft.Extensions.DependencyInjection;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.Localization;

[ExcludeFromCodeCoverage]
public class PostgresCultureControllerTests(PostgresDatabaseFixture fixture) : CultureControllerTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

/*[ExcludeFromCodeCoverage]
public class SqlServerCultureControllerTests : CultureControllerTests, IClassFixture<SqlServerDatabaseFixture>
{
	public SqlServerCultureControllerTests(SqlServerDatabaseFixture fixture) : base(fixture)
	{
		// nothing
	}
}*/

[ExcludeFromCodeCoverage]
public abstract class CultureControllerTests : AdminControllerTest<CultureController, CultureEntity, CultureDto>
{
	private static readonly Action<IServiceCollection> ServiceInjection = services =>
	{
		services.AddLocalization();
		services.ConfigureOptions<CustomRequestLocalizationOptions>();
	};

	#region Test Data Properties

	protected override CultureDto CreateDto => new()
	{
		Name = "de",
		DisplayName = "German"
	};
	
	protected override CultureDto GetExpectedCreateDto()
	{
		var expected = CreateDto;
		expected.DisplayName = new CultureInfo("de").DisplayName;
		return expected;
	}

	protected override CultureDto InvalidCreateDto => new();
	
	protected override CultureDto InvalidUpdateDto => new()
	{
		Id = Guid.Empty,
		Name = "es",
		DisplayName = "Spanish"
	};

	protected override QueryParamsDto QueryParams => new()
	{
		Sortings = new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Asc
			}
		}
	};

	protected override QueryParamsDto QueryParamsWithLimitAndOffset => new()
	{
		Sortings = new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Asc
			}
		},
		Limit = 2,
		Offset = 1
	};

	protected override QueryParamsDto QueryParamsWithSorting => new()
	{
		Sortings = new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Desc
			}
		}
	};

	protected override QueryParamsDto QueryParamsWithFilter => new()
	{
		Filters = new List<QueryParamFilterDto>()
		{
			new()
			{
				FilterColumn = "name",
				Operator = QueryParamFilterOperator.Equals,
				CompareValue = "en"
			}
		}
	};
	
	#endregion

	protected CultureControllerTests(DatabaseFixture fixture) : base(fixture, ServiceInjection)
	{
		var logManager = Fixture.ServiceProvider.GetRequiredService<LogManager>();
		var localizerFactory = Fixture.ServiceProvider.GetRequiredService<IExtendedStringLocalizerFactory>();
		ControllerInstance = new CultureController(logManager, Fixture.ContextFactory, UserManager, localizerFactory, VersionReader);
	}
	
	#region Data Preparation
	
	private CultureEntity AddCulture(CultureDto dto)
	{
		var culture = CultureEntity.FromDto(dto);
		culture.SetStringLocalizerFactory(ControllerInstance!.StringLocalizerFactory);
		
		using var databaseContext = Fixture.Context;
		databaseContext.Cultures.Add(culture);
		databaseContext.SaveChanges();

		return culture;
	}

	protected override CultureEntity PrepareSingleEntity()
	{
		return AddCulture(CreateDto);
	}

	protected override CultureDto GetUpdateDto(CultureEntity entity)
	{
		// Updates are not (yet) possible.
		return entity.ToDto();
	}

	protected override async Task PrepareQueryDataAsync()
	{
		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000001"),
			Name = "de",
			DisplayName = "German"
		});

		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000002"),
			Name = "en",
			DisplayName = "English"
		});

		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000003"),
			Name = "fr",
			DisplayName = "French"
		});

		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000004"),
			Name = "es",
			DisplayName = "Spanish"
		});

		foreach (var dto in QueryData)
		{
			AddCulture(dto);
		}

		await using var databaseContext = Fixture.Context;
		await databaseContext.SaveChangesAsync();
	}
	
	#endregion
	
	#region Assertions

	protected override void AssertIsAsExpected(CultureDto expected, CultureDto actual)
	{
		Assert.Equal(expected.Name, actual.Name);
		Assert.Equal(expected.DisplayName, actual.DisplayName);
		Assert.Equal(expected.TranslationCount, actual.TranslationCount);
		Assert.Equal(expected.UserCount, actual.UserCount);
	}

	protected override void AssertExists(CultureDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.Cultures.Any(culture => culture.Name == dto.Name));
	}

	protected override void AssertDoesNotExist(CultureDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.Cultures.All(culture => culture.Name != dto.Name));
	}

	protected override bool DeleteSuccessful(CultureEntity entity)
	{
		using var databaseContext = Fixture.Context;
		return databaseContext.Cultures.Find(entity.Id) == null;
	}

	protected override bool CheckQueryResult(ConfigQueryResultDto<CultureDto> queryResultDto, int limit = 0, int offset = 0)
	{
		var count = QueryData.Count;
		var mutatedNamesList = queryResultDto.Rows.ToList().ConvertAll(config => config.Name);
		var sortedData = QueryData.OrderBy(culture => culture.Name).ToList();
		var expectedList = new List<string>();
		for(var i = offset; i < count && (expectedList.Count < limit || limit == 0); i++)
		{
			expectedList.Add(sortedData[i].Name);
		}
		expectedList.Sort();
		
		if (queryResultDto.CountTotal != count)
			return false;
		if (limit > 0 && queryResultDto.Rows.Count > limit)
			return false;
		
		for (var i = 0; i < mutatedNamesList.Count; i++)
		{
			if (expectedList[i] != mutatedNamesList[i])
				return false;
		}

		return true;
	}

	protected override bool CheckSortingQueryResult(ConfigQueryResultDto<CultureDto> queryResultDto)
	{
		var count = QueryData.Count;
		var mutatedNamesList = queryResultDto.Rows.ToList().ConvertAll(config => config.Name);
		var expectedList = QueryData.OrderByDescending(dataSource => dataSource.Name).ThenBy(dataSource => dataSource.DisplayName).ToList();
		
		if (queryResultDto.CountTotal != count)
			return false;
		if (queryResultDto.Rows.Count != count)
			return false;
		
		for (var i = 0; i < mutatedNamesList.Count; i++)
		{
			if (expectedList[i].Name != mutatedNamesList[i])
				return false;
		}

		return true;
	}

	protected override bool CheckFilterQueryResult(ConfigQueryResultDto<CultureDto> queryResultDto)
	{
		if (queryResultDto.CountTotal != 1)
			return false;
		if (queryResultDto.Rows.Count != 1)
			return false;

		return QueryData[1].Name == queryResultDto.Rows.FirstOrDefault()?.Name;
	}
	
	#endregion
}