using Google.Api.Gax;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.PageView;
using Levelbuild.Entities.Features.PageView.GalleryView;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.PageView;

[ExcludeFromCodeCoverage]
[Collection("PostgresDatabaseCollection")]
public class PostgresGalleryViewControllerTests(PostgresDatabaseFixture fixture) : GalleryViewControllerTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

/*[ExcludeFromCodeCoverage]
[Collection("SqlServerDatabaseCollection")]
public class SqlServerGalleryViewControllerTests : GalleryViewControllerTests, IClassFixture<SqlServerDatabaseFixture>
{
	public SqlServerGalleryViewControllerTests(SqlServerDatabaseFixture fixture) : base(fixture)
	{
		// nothing
	}
}*/

[ExcludeFromCodeCoverage]
public abstract class GalleryViewControllerTests(DatabaseFixture fixture) : PageViewControllerTests(fixture)
{
	#region Test Data Properties

	protected override GalleryViewDto CreateDto
	{
		get
		{
			using var databaseContext = Fixture.Context;
			var galleryViewDto = base.CreateDto;
			var titleField = EntityCreation.CreateDataField(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
														 UserManager.GetCurrentUserAsync().ResultWithUnwrappedExceptions().Id, "Title");
			var subtitleField = EntityCreation.CreateDataField(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
														 UserManager.GetCurrentUserAsync().ResultWithUnwrappedExceptions().Id, "Subtitle");
			return new GalleryViewDto(galleryViewDto)
			{
				Type = PageViewType.Gallery,
				TypeName = "[Gallery]",
				Created = DateTime.Now.ToUniversalTime(),
				CreatedBy = UserManager.GetCurrentUserAsync().ResultWithUnwrappedExceptions().DisplayName,
				TitleFieldId = titleField.Id,
				SubtitleFieldId = subtitleField.Id,
				Icon = "grid-2"
			};
		}
	}

	protected override GalleryViewDto InvalidCreateDto
	{
		get
		{
			var galleryViewDto = base.InvalidCreateDto;
			return new GalleryViewDto(galleryViewDto)
			{
				Type = PageViewType.Gallery,
			};
		}
	}

	protected override GalleryViewDto InvalidUpdateDto
	{
		get
		{
			var galleryViewDto = base.InvalidUpdateDto;
			return new GalleryViewDto(galleryViewDto)
			{
				Type = PageViewType.Gallery,
			};
		}
	}
	
	protected override QueryParamsDto QueryParamsWithFilter => new()
	{
		Filters = new List<QueryParamFilterDto>
		{
			new()
			{
				FilterColumn = "name",
				Operator = QueryParamFilterOperator.Equals,
				CompareValue = "GalleryView2"
			}
		}
	};
	
	#endregion

	#region Data Preparation
	
	protected override GalleryViewEntity PrepareSingleEntity()
	{
		using var databaseContext = Fixture.Context;
		var entry = databaseContext.GalleryViews.Add(GalleryViewEntity.FromDto(CreateDto, null, databaseContext));
		databaseContext.SaveChanges();
		
		entry.Entity.SetStringLocalizerFactory(ControllerInstance!.StringLocalizerFactory);

		return entry.Entity;
	}
	
	protected override GalleryViewDto GetUpdateDto(PageViewEntity entity)
	{
		var dto = ((GalleryViewEntity)entity).ToDto();
		UpdateDto(dto);
		return dto;
	}

	#endregion

	#region Assertions

	protected override void AssertIsAsExpected(PageViewDto expected, PageViewDto actual)
	{
		var castActual = (GalleryViewDto)actual;
		var castExpected = (GalleryViewDto)expected;
		Assert.Equal(castActual.SubtitleFieldId, castExpected.SubtitleFieldId);
		Assert.Equal(castActual.TitleFieldId, castExpected.TitleFieldId);
		
		base.AssertIsAsExpected(expected, actual);
	}

	protected override void AssertExists(PageViewDto dto)
	{
		base.AssertExists(dto);
		
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.GalleryViews.Any(gallery => gallery.Name == dto.Name));
	}
	
	protected override void AssertDoesNotExist(PageViewDto dto)
	{
		base.AssertDoesNotExist(dto);

		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.GalleryViews.All(gallery => gallery.Name != dto.Name));
	}
	
	protected override bool DeleteSuccessful(PageViewEntity entity)
	{
		if (!base.DeleteSuccessful(entity))
			return false;
		
		using var databaseContext = Fixture.Context;
		return databaseContext.GalleryViews.Find(entity.Id) == null;
	}
	
	#endregion
}