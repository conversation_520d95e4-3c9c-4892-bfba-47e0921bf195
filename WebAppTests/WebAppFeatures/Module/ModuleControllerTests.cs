using Elastic.Clients.Elasticsearch;
using Google.Api.Gax;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.DataField;
using Levelbuild.Core.FrontendDtos.DataSource;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Module;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.Module;
using Levelbuild.Entities.Helpers.DataSource;
using Levelbuild.Frontend.WebApp.Features.Module.Controllers;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Microsoft.AspNetCore.Mvc;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.Module;

[ExcludeFromCodeCoverage]
public class PostgresModuleControllerTests(PostgresDatabaseFixture fixture) : ModuleControllerTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

[ExcludeFromCodeCoverage]
public abstract class ModuleControllerTests : AdminControllerTest<ModuleController, ModuleEntity, ModuleDto>
{
	#region Test Data Properties

	protected override ModuleDto CreateDto
	{
		get
		{
			using var databaseContext = Fixture.Context;
			var currentUser = UserManager.GetCurrentCustomerAsync().ResultWithUnwrappedExceptions();
			var store = EntityCreation.CreateDataStore(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
													   currentUser.Id);

			return new ModuleDto
			{
				DataStore = store.ToDto(),
				DataStoreId = store.Id,
				Name = "Company",
				Icon = "fa-company",
				Responsible = null,
			};
		}
	}

	protected override ModuleDto InvalidCreateDto => new()
	{
		Name = null!,
	};

	protected override ModuleDto InvalidUpdateDto => new()
	{
		Id = Guid.Empty,
		Name = null!,
	};

	protected override QueryParamsDto QueryParams => new()
	{
		Sortings = new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Asc
			}
		}
	};

	protected override QueryParamsDto QueryParamsWithLimitAndOffset => new()
	{
		Sortings = new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Asc
			}
		},
		Limit = 2,
		Offset = 1
	};

	protected override QueryParamsDto QueryParamsWithSorting => new()
	{
		Sortings = new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "description",
				Direction = SortDirection.Desc
			},
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Asc
			}
		}
	};

	protected override QueryParamsDto QueryParamsWithFilter => new()
	{
		Filters = new List<QueryParamFilterDto>()
		{
			new()
			{
				FilterColumn = "name",
				Operator = QueryParamFilterOperator.Equals,
				CompareValue = "Data2"
			}
		}
	};

	#endregion

	protected ModuleControllerTests(DatabaseFixture fixture) : base(fixture, initStorage: true)
	{
		ControllerInstance = new ModuleController(LogManager, Fixture.ContextFactory, UserManager, LocalizerFactory, VersionReader);
	}

	#region Data Preparation

	protected override ModuleEntity PrepareSingleEntity()
	{
		using var databaseContext = Fixture.Context;
		var entry = databaseContext.Modules.Add(ModuleEntity.FromDto(CreateDto, null, databaseContext));
		databaseContext.SaveChanges();

		return entry.Entity;
	}

	protected override ModuleDto GetUpdateDto(ModuleEntity entity)
	{
		var dto = entity.ToDto();
		dto.Description = "Updated description";
		dto.Icon = "fa-update";
		return dto;
	}

	protected override async Task PrepareQueryDataAsync()
	{
		using var databaseContext = Fixture.Context;
		var store = EntityCreation.CreateDataStore(databaseContext, Fixture.StorageOptions, Fixture.StorageContextOptions,
												   (await UserManager.GetCurrentCustomerAsync()).Id);
		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000001"),
			DataStore = store.ToDto(),
			DataStoreId = store.Id,
			Name = "Data1",
		});

		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000002"),
			DataStore = store.ToDto(),
			DataStoreId = store.Id,
			Name = "Data2",
		});

		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000003"),
			DataStore = store.ToDto(),
			DataStoreId = store.Id,
			Name = "Data3",
		});

		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000004"),
			DataStore = store.ToDto(),
			DataStoreId = store.Id,
			Name = "Data4",
		});

		foreach (var dto in QueryData)
		{
			databaseContext.Modules.Add(ModuleEntity.FromDto(dto, null, databaseContext));
		}

		databaseContext.SaveChanges();
	}

	#endregion

	#region Assertions

	protected override void AssertIsAsExpected (ModuleDto expected, ModuleDto actual)
	{
		Assert.Equal(expected.Name, actual.Name);
		Assert.Equal(expected.Responsible, actual.Responsible);
		Assert.Equal(expected.DataStoreId, actual.DataStoreId);
		Assert.Equal(expected.Icon, actual.Icon);
		Assert.Equal(expected.Description, actual.Description);
	}

	protected override void AssertExists(ModuleDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.Modules.Any(module => module.Name == dto.Name));
	}

	protected override void AssertDoesNotExist(ModuleDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.Modules.All(module => module.Name != dto.Name));
	}

	protected override bool DeleteSuccessful(ModuleEntity entity)
	{
		using var databaseContext = Fixture.Context;
		return databaseContext.Modules.Find(entity.Id) == null;
	}

	protected override bool CheckQueryResult(ConfigQueryResultDto<ModuleDto> queryResultDto, int limit = 0, int offset = 0)
	{
		var count = QueryData.Count;
		var mutatedNamesList = queryResultDto.Rows.ToList().ConvertAll(config => config.Name);
		var expectedList = new List<string>();
		for (var i = offset; i < count && (expectedList.Count < limit || limit == 0); i++)
		{
			expectedList.Add(QueryData[i].Name);
		}

		if (queryResultDto.CountTotal != count)
			return false;
		if (limit > 0 && queryResultDto.Rows.Count > limit)
			return false;

		return !mutatedNamesList.Where((t, i) => expectedList[i] != t).Any();
	}

	protected override bool CheckSortingQueryResult(ConfigQueryResultDto<ModuleDto> queryResultDto)
	{
		var count = QueryData.Count;
		var mutatedNamesList = queryResultDto.Rows.ToList().ConvertAll(config => config.Name);
		var expectedList = QueryData.OrderByDescending(module => module.Description).ThenBy(dataSource => dataSource.Name).ToList();

		if (queryResultDto.CountTotal != count)
			return false;
		if (queryResultDto.Rows.Count != count)
			return false;

		for (var i = 0; i < mutatedNamesList.Count; i++)
		{
			if (expectedList[i].Name != mutatedNamesList[i])
				return false;
		}

		return true;
	}

	protected override bool CheckFilterQueryResult(ConfigQueryResultDto<ModuleDto> queryResultDto)
	{
		if (queryResultDto.CountTotal != 1)
			return false;
		if (queryResultDto.Rows.Count != 1)
			return false;

		return QueryData[1].Name == queryResultDto.Rows.FirstOrDefault()?.Name;
	}

	#endregion
}