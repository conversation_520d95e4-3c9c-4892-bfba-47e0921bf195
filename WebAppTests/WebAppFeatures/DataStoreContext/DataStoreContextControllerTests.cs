using Google.Api.Gax;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.DataStoreContext;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Domain.WebAppTests.Setup;
using Levelbuild.Entities.Features.DataStoreConfig;
using Levelbuild.Entities.Features.DataStoreContext;
using Levelbuild.Frontend.WebApp.Features.DataStoreContext.Controllers;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Domain.WebAppTests.WebAppFeatures.DataStoreContext;

[ExcludeFromCodeCoverage]
public class PostgresDataStoreContextControllerTests(PostgresDatabaseFixture fixture)
	: DataStoreContextControllerTests(fixture), IClassFixture<PostgresDatabaseFixture>
{
	// nothing
}

/*[ExcludeFromCodeCoverage]
public class SqlServerDataStoreContextControllerTests : DataStoreContextControllerTests, IClassFixture<SqlServerDatabaseFixture>
{
	public SqlServerDataStoreContextControllerTests(SqlServerDatabaseFixture fixture) : base(fixture)
	{
		// nothing
	}
}*/

[ExcludeFromCodeCoverage]
public abstract class DataStoreContextControllerTests : AdminControllerTest<DataStoreContextController, DataStoreContextEntity, DataStoreContextDto>
{
	#region Test Data Properties
	
	protected override DataStoreContextDto CreateDto
	{
		get
		{
			var dataStore = CreateDataStore();
			var currentCustomer = UserManager.GetCurrentCustomerAsync().ResultWithUnwrappedExceptions();
			
			return new DataStoreContextDto()
			{
				Name = "TenantName",
				Enabled = true,
				DataStoreId = dataStore.Id,
				CustomerId = currentCustomer.Id,
				Options = Fixture.StorageContextOptions
			};
		}
	}
	
	protected override DataStoreContextDto InvalidCreateDto => new()
	{
		Name = "TenantName",
		Enabled = true
	};
	
	protected override DataStoreContextDto InvalidUpdateDto => new()
	{
		Id = Guid.Empty,
		Name = null
	};
	
	protected override QueryParamsDto QueryParams => new()
	{
		Sortings = new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Asc
			}
		}
	};
	
	protected override QueryParamsDto QueryParamsWithLimitAndOffset => new()
	{
		Sortings = new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Asc
			}
		},
		Limit = 2,
		Offset = 1
	};
	
	protected override QueryParamsDto QueryParamsWithSorting => new()
	{
		Sortings = new List<QueryParamSortingDto>()
		{
			new()
			{
				OrderColumn = "enabled",
				Direction = SortDirection.Desc
			},
			new()
			{
				OrderColumn = "name",
				Direction = SortDirection.Asc
			}
		}
	};
	
	protected override QueryParamsDto QueryParamsWithFilter => new()
	{
		Filters = new List<QueryParamFilterDto>()
		{
			new()
			{
				FilterColumn = "name",
				Operator = QueryParamFilterOperator.Equals,
				CompareValue = "Tenant2"
			}
		}
	};
	
	#endregion

	protected DataStoreContextControllerTests(DatabaseFixture fixture) : base(fixture)
	{
		Fixture.InitStorageDataBase();
		
		ControllerInstance = new DataStoreContextController(LogManager, Fixture.ContextFactory, UserManager, LocalizerFactory, VersionReader);
	}
	
	#region Data Preparation
	
	protected override DataStoreContextEntity PrepareSingleEntity()
	{
		using var databaseContext = Fixture.Context;
		databaseContext.DataStoreContexts.Add(DataStoreContextEntity.FromDto(CreateDto, null, databaseContext));
		databaseContext.SaveChanges();
		databaseContext.ChangeTracker.Clear();
		
		var entity = databaseContext.DataStoreContexts.Include(context => context.Customer).First();
		entity.CreateContext();
		
		return entity;
	}
	
	protected override DataStoreContextDto GetUpdateDto(DataStoreContextEntity entity)
	{
		var dto = entity.ToDto();
		dto.Enabled = false;
		return dto;
	}
	
	protected override async Task PrepareQueryDataAsync()
	{
		var dataStore = CreateDataStore();
		var currentCustomer = await UserManager.GetCurrentCustomerAsync();
		
		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000001"),
			Name = "Tenant1",
			Enabled = true,
			DataStoreId = dataStore.Id,
			CustomerId = currentCustomer.Id,
			Options = Fixture.StorageContextOptions
		});
		
		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000002"),
			Name = "Tenant2",
			Enabled = true,
			DataStoreId = dataStore.Id,
			CustomerId = currentCustomer.Id,
			Options = Fixture.StorageContextOptions
		});
		
		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000003"),
			Name = "Tenant3",
			Enabled = true,
			DataStoreId = dataStore.Id,
			CustomerId = currentCustomer.Id,
			Options = Fixture.StorageContextOptions
		});
		
		QueryData.Add(new()
		{
			Id = new Guid("00000000-0000-0000-0000-000000000004"),
			Name = "Tenant4",
			Enabled = true,
			DataStoreId = dataStore.Id,
			CustomerId = currentCustomer.Id,
			Options = Fixture.StorageContextOptions
		});

		await using var databaseContext = Fixture.Context;
		foreach (var dto in QueryData)
		{
			databaseContext.DataStoreContexts.Add(DataStoreContextEntity.FromDto(dto, null, databaseContext));
		}
		
		await databaseContext.SaveChangesAsync();
	}
	
	private DataStoreConfigEntity CreateDataStore()
	{
		using var databaseContext = Fixture.Context;
		if (databaseContext.DataStoreConfigs.Any())
			return databaseContext.DataStoreConfigs.First();
		
		databaseContext.DataStoreConfigs.Add(new()
		{
			Name = "Example",
			Enabled = true,
			Type = DataStoreType.Storage,
			Options = Fixture.StorageOptions
		});
		databaseContext.SaveChanges();
		
		return databaseContext.DataStoreConfigs.First();
	}
	
	#endregion
	
	#region Assertions
	
	protected override void AssertIsAsExpected(DataStoreContextDto expected, DataStoreContextDto actual)
	{
		Assert.Equal(expected.Name, actual.Name);
		Assert.Equal(expected.Enabled, actual.Enabled);
		Assert.Equal(expected.DataStoreId, actual.DataStoreId);
		Assert.Equal(expected.CustomerId, actual.CustomerId);
		Assert.Equal(expected.Options!.Count, actual.Options!.Count);
	}
	
	protected override void AssertExists(DataStoreContextDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.DataStoreContexts.Any(context => context.Name == dto.Name && context.DataStoreId == dto.DataStoreId &&
																 context.CustomerId == dto.CustomerId));
	}
	
	protected override void AssertDoesNotExist(DataStoreContextDto dto)
	{
		using var databaseContext = Fixture.Context;
		Assert.True(databaseContext.DataStoreContexts.All(dataField => dataField.Name != dto.Name ||
																   (dataField.Name == dto.Name &&
																	(dataField.DataStoreId != dto.DataStoreId || dataField.CustomerId != dto.CustomerId))));
	}
	
	protected override bool DeleteSuccessful(DataStoreContextEntity entity)
	{
		using var databaseContext = Fixture.Context;
		return databaseContext.DataStoreContexts.Find(entity.Id) == null;
	}
	
	protected override bool CheckQueryResult(ConfigQueryResultDto<DataStoreContextDto> queryResultDto, int limit = 0, int offset = 0)
	{
		var count = QueryData.Count;
		var mutatedNamesList = queryResultDto.Rows.ToList().ConvertAll(context => context.Name);
		var expectedList = new List<string>();
		for (var i = offset; i < count && (expectedList.Count < limit || limit == 0); i++)
		{
			expectedList.Add(QueryData[i].Name!);
		}
		
		if (queryResultDto.CountTotal != count)
			return false;
		if (limit > 0 && queryResultDto.Rows.Count > limit)
			return false;
		
		for (var i = 0; i < mutatedNamesList.Count; i++)
		{
			if (expectedList[i] != mutatedNamesList[i])
				return false;
		}
		
		return true;
	}
	
	protected override bool CheckSortingQueryResult(ConfigQueryResultDto<DataStoreContextDto> queryResultDto)
	{
		var count = QueryData.Count;
		var mutatedNamesList = queryResultDto.Rows.ToList().ConvertAll(config => config.Name);
		var expectedList = QueryData.OrderByDescending(context => context.Enabled).ThenBy(context => context.Name).ToList();
		
		if (queryResultDto.CountTotal != count)
			return false;
		if (queryResultDto.Rows.Count != count)
			return false;
		
		for (var i = 0; i < mutatedNamesList.Count; i++)
		{
			if (expectedList[i].Name != mutatedNamesList[i])
				return false;
		}
		
		return true;
	}
	
	protected override bool CheckFilterQueryResult(ConfigQueryResultDto<DataStoreContextDto> queryResultDto)
	{
		if (queryResultDto.CountTotal != 1)
			return false;
		if (queryResultDto.Rows.Count != 1)
			return false;
		
		return QueryData[1].Name == queryResultDto.Rows.FirstOrDefault()?.Name;
	}
	
	#endregion
	
	#region Other
	
	[Trait("Category", "DataStoreContextController Tests")]
	[Fact(DisplayName = "Get data store context info from storage")]
	public void GetDataStoreInfoStorageTest()
	{
		var contextEntity = PrepareSingleEntity();
		var result = ControllerInstance?.GetDataStoreContextInfo(contextEntity.DataStoreId);
		Assert.NotNull(result);
		
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(200, objectResult.StatusCode);

		var info = (objectResult.Value as FrontendResponse<DataStoreInfo>)?.Data;
		Assert.NotNull(info);
	}
	
	[Trait("Category", "DataStoreContextController Tests")]
	[Fact(DisplayName = "Fail to get data store context info from storage")]
	public void FailGetDataStoreInfoStorageTest()
	{
		var result = ControllerInstance?.GetDataStoreContextInfo(Guid.Empty);
		Assert.NotNull(result);
		
		var objectResult = result.Result as ObjectResult;
		Assert.NotNull(objectResult);
		Assert.Equal(400, objectResult.StatusCode);
		
		var info = (objectResult.Value as FrontendResponse<DataStoreInfo>)?.Data;
		Assert.Null(info);
	}
	
	#endregion
}