using System.Text;
using Levelbuild.Core.WebAppCoreTests.FileInterface;
using Levelbuild.Domain.FileSystemFile.Dto;
using Levelbuild.Domain.GoogleStorageFile;
using Microsoft.Extensions.Configuration;

namespace Levelbuild.Domain.WebAppTests.FileSystemGoogleStorage;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

/// this test will fail locally, as there is no proper authentication configured.
public class FileSystemGoogleStorageFailsLocallyTests : FileInterfaceTests
{
	public FileSystemGoogleStorageFailsLocallyTests() : base(CreateFileInterface())
	{
	}

	private static GoogleStorageFileStore CreateFileInterface()
	{
		var config = new GoogleStorageFileStoreConfig("", "", "");
		var c = new ConfigurationBuilder()
			.SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
			.AddJsonFile("testsettings.json")
			.Build()
			.GetSection("GoogleStorageFile");
		c.Bind(config);
		
		config.Url = Encoding.UTF8.GetString(Convert.FromBase64String(config.Url));
		config.RootPath = "/tests/" + Guid.NewGuid();

		return new GoogleStorageFileStore(config);
	}
	
	[Fact]
	public void StreamUpload2Test()
	{
		var fileSystemFileStore = new FileSystemStore(new FileSystemFileStoreConfig(Directory.GetCurrentDirectory()));
		var sourceFile = fileSystemFileStore.GetFile("Storage/Test.pdf");
		try
		{
			FileStore.GetFile("stream/getfilestreamtestpdf").DeleteFile();
		}
		catch (Exception)
		{
			// ignored
		}
		
		var newFile = FileStore.GetFile("stream/getfilestreamtestpdf");
		
		using (var readStream = sourceFile.ReadFile())
			using (var writeStream = newFile.WriteFile())
			{
				readStream.CopyTo(writeStream);
			}
		
		Assert.Equal(sourceFile.ReadAllBytes(), newFile.ReadAllBytes());
		try
		{
			FileStore.GetFile("stream/getfilestreamtestpdf").DeleteFile();
		}
		catch (Exception)
		{
			// ignored
		}
	}
}