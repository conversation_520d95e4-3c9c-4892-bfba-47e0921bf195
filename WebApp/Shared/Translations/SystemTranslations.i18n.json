{"de": {"pleaseChoose": "Bitte wählen", "abortButton": "Abbrechen", "saveButton": "Speichern", "resetButton": "Z<PERSON>ücksetzen", "deleteButton": "Löschen", "pleaseEnter": "<PERSON><PERSON> e<PERSON>ben", "created": "Erstellt am", "lastModified": "Letzte Änderung am", "module": "<PERSON><PERSON><PERSON>", "responsible": "Verantwortlicher", "dataStore": "Backend", "dataSource": "<PERSON><PERSON><PERSON><PERSON>", "createdBy": "<PERSON><PERSON><PERSON><PERSON> von", "lastModifiedBy": "Letzte Änderung", "underConstruction": "Hier wird noch gebaut...", "sectionInfo": "Informationen", "sectionConfig": "Konfiguration", "sectionSettings": "Einstellungen", "sectionAppSettings": "App Einstellungen", "mvpTooltip": "Diese Funktion steht erst zu einem späteren Zeitpunkt zur Verfügung", "selectAll": "Alle auswählen", "addEntry": "Hinzufügen", "removeSelection": "Auswahl löschen", "createSelection": "Auswahl erstellen", "noPreviewAvailable": "<PERSON><PERSON>", "dragFileToUpload": "<PERSON><PERSON><PERSON> eine Date<PERSON> hierher, um sie hinzuzufügen oder nutzen Si<PERSON> den <PERSON> unten.", "icon": "Icon", "position": "Position", "getPreviousBatchError": "Fehler beim Sammeln des vorherigen Batches", "getPreviousElementError": "Fehler beim Sammeln des vorherigen Datensatzes", "getNextBatchError": "Fehler beim Sammeln des nächstes Batches", "getNextElementError": "Fehler beim Sammeln des nächstes Datensatzes", "noPreviousRecord": "<PERSON><PERSON> weiteren Datensätze vorhanden", "goToPreviousRecord": "zum vorherigen Datensatz", "noNextRecord": "<PERSON><PERSON> weiteren Datensätze vorhanden", "goToNextRecord": "zum nächsten Datensatz", "unsavedChangesHeader": "Offene Änderungen", "unsavedChangesMessage": "Es wurden nicht gespeicherte Änderungen entdeckt. Wollen sie diese vor dem Verlassen der Seite speichern?", "saveChangesButton": "Ja, Änderungen speichern", "discardChangesButton": "Nein, Änderungen verwerfen", "name": "Name", "description": "Beschreibung"}, "en": {"pleaseChoose": "Please choose", "abortButton": "Abort", "saveButton": "Save", "resetButton": "Reset", "deleteButton": "Delete", "pleaseEnter": "Please enter", "created": "Created", "module": "<PERSON><PERSON><PERSON>", "lastModified": "Last Modified", "responsible": "Responsible", "dataStore": "Backend", "dataSource": "Datasource", "createdBy": "Created by", "lastModifiedBy": "Last change", "underConstruction": "Under construction...", "sectionInfo": "Information", "sectionConfig": "Configuration", "sectionSettings": "Settings", "sectionAppSettings": "App Settings", "mvpTooltip": "This function will be available at a later date", "selectAll": "Select all", "addEntry": "Add", "removeSelection": "Delete selection", "createSelection": "Create selection", "noPreviewAvailable": "No preview available", "dragFileToUpload": "Drag a file here to add it or use the button below.", "icon": "Icon", "position": "Position", "getPreviousBatchError": "An error occured while fetching the previous batch", "getPreviousElementError": "An error occured while fetching the previous record", "getNextBatchError": "An error occured while fetching the next batch", "getNextElementError": "An error occured while fetching the next record", "noPreviousRecord": "no further records", "goToPreviousRecord": "to previous record", "noNextRecord": "no further records", "goToNextRecord": "to next record", "unsavedChangesHeader": "Unsaved Changes", "unsavedChangesMessage": "Some changes on the current page have not been saved. Do you want to save the Changes before leaving the page?", "saveChangesButton": "Yes, save changes", "discardChangesButton": "No, discard changes", "name": "Name", "description": "Description"}}