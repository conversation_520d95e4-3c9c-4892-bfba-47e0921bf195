using Levelbuild.Core.SharedUtilities;
using Microsoft.OpenApi.Extensions;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Shared.Enums;

/// <summary>
/// Predefined Position for the borders within a dropdown
/// </summary>
public enum BorderPosition
{
	None,

	Top,

	Bottom,

	Both
}

public static class BorderPositionExtensions
{
	public static string GetPlacementAsString(this BorderPosition enumValue)
	{
		return enumValue.GetDisplayName().ToLower();
	}

	public static string GetString(this BorderPosition enumValue)
	{
		return EnumUtils<BorderPosition>.GetTranslatableString(enumValue);
	}
}