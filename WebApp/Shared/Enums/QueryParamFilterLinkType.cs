using Microsoft.OpenApi.Extensions;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Shared.Enums
{
	public enum QueryParamFilterLinkType
	{
		And,

		Or,
	}

	public static class QueryParamFilterLinkTypeExtensions
	{
		public static string JoinWithBrackets(this QueryParamFilterLinkType enumValue, IList<string> list)
		{
			var join = string.Join(") " + enumValue.GetDisplayName().ToUpper() + " (", list);
			return "(" + join + ")";
		}

		public static string Join(this QueryParamFilterLinkType enumValue, IList<string> list)
		{
			return string.Join(" " + enumValue.GetDisplayName().ToUpper() + " ", list);
		}
	}
}