using System.Text.Json.Serialization;
using Levelbuild.Core.SharedUtilities;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Shared.Enums
{
	[JsonConverter(typeof(JsonStringEnumConverter))]
	public enum QueryParamFilterOperator
	{
		[QueryParamFilterOperatorDescriptor("=", QueryParamFilterLinkType.Or)]
		Equals,

		[QueryParamFilterOperatorDescriptor("!=", QueryParamFilterLinkType.And)]
		NotEquals,

		[QueryParamFilterOperatorDescriptor("LIKE", QueryParamFilterLinkType.Or)]
		Like,

		[QueryParamFilterOperatorDescriptor("NOT LIKE", QueryParamFilterLinkType.And)]
		NotLike,

		[QueryParamFilterOperatorDescriptor(">", QueryParamFilterLinkType.And)]
		GreaterThan,

		[QueryParamFilterOperatorDescriptor(">=", QueryParamFilterLinkType.And)]
		GreaterThanEquals,

		[QueryParamFilterOperatorDescriptor("<", QueryParamFilterLinkType.And)]
		LessThan,

		[QueryParamFilterOperatorDescriptor("<=", QueryParamFilterLinkType.And)]
		LessThanEquals,

		[QueryParamFilterOperatorDescriptor("IN", QueryParamFilterLinkType.Or)]
		In,

		[QueryParamFilterOperatorDescriptor("NOT IN", QueryParamFilterLinkType.And)]
		NotIn,

		[QueryParamFilterOperatorDescriptor("IS NULL", QueryParamFilterLinkType.Or, false)]
		IsNull,

		[QueryParamFilterOperatorDescriptor("IS NOT NULL", QueryParamFilterLinkType.And, false)]
		IsNotNull,
		
		[QueryParamFilterOperatorDescriptor("CONTAINS", QueryParamFilterLinkType.Or)]
		Contains,

		[QueryParamFilterOperatorDescriptor("NOT CONTAINS", QueryParamFilterLinkType.And)]
		NotContains,
		
		[QueryParamFilterOperatorDescriptor("FULLTEXT SEARCH", QueryParamFilterLinkType.Or)]
		FulltextSearch,
		
		[QueryParamFilterOperatorDescriptor("FAVORITE", QueryParamFilterLinkType.Or)]
		Favorite,
		
		[QueryParamFilterOperatorDescriptor("INACTIVE", QueryParamFilterLinkType.Or)]
		Inactive
	}


	[AttributeUsage(AttributeTargets.Field)]
	public class QueryParamFilterOperatorDescriptorAttribute : Attribute
	{
		public bool CompareValueRequired { get; }

		public string Sign { get; }

		public QueryParamFilterLinkType LinkType { get; }

		public QueryParamFilterOperatorDescriptorAttribute(string sign, QueryParamFilterLinkType linkType, bool compareValueRequired = true)
		{
			Sign = sign;
			LinkType = linkType;
			CompareValueRequired = compareValueRequired;
		}
	}

	public static class QueryParamFilterOperatorExtensions
	{
		/// <summary>
		/// Get the sign of the operator type =, !=, ...
		/// </summary>
		/// <param name="enumValue"></param>
		/// <returns></returns>
		public static string GetSign(this QueryParamFilterOperator enumValue)
		{
			var attr = GetAttributes(enumValue);
			return attr.Sign;
		}

		/// <summary>
		/// How is the filter connected with other filters?
		/// </summary>
		/// <param name="enumValue"></param>
		/// <returns></returns>
		public static QueryParamFilterLinkType GetLinkType(this QueryParamFilterOperator enumValue)
		{
			var attr = GetAttributes(enumValue);
			return attr.LinkType;
		}

		/// <summary>
		/// Does the filter needs a compare value for this type of operator?
		/// </summary>
		/// <param name="enumValue"></param>
		/// <returns></returns>
		public static bool IsCompareValueRequired(this QueryParamFilterOperator enumValue)
		{
			var attr = GetAttributes(enumValue);
			return attr.CompareValueRequired;
		}

		/// <summary>
		/// String representation for filter operator types
		/// </summary>
		/// <param name="enumValue"></param>
		/// <returns></returns>
		public static string GetString(this QueryParamFilterOperator enumValue)
		{
			return EnumUtils<QueryParamFilterOperator>.GetTranslatableString(enumValue);
		}

		private static QueryParamFilterOperatorDescriptorAttribute GetAttributes(QueryParamFilterOperator enumValue)
		{
			return (EnumUtils<QueryParamFilterOperator>.GetAttributeFromEnum<QueryParamFilterOperatorDescriptorAttribute>(enumValue) as
						QueryParamFilterOperatorDescriptorAttribute)!;
		}
	}

	/*
	/// <summary>
	/// Converter to serialize the operator in a sign and deserialize a sign or a number in an operator
	/// </summary>
	public sealed class QueryParamFilterOperatorConverter : JsonConverter<QueryParamFilterOperator>
	{
		/// <summary>
		/// Deserialization method for string to enum
		/// </summary>
		/// <param name="reader"></param>
		/// <param name="typeToConvert"></param>
		/// <param name="options"></param>
		/// <returns></returns>
		public override QueryParamFilterOperator Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
		{
			var operators = Enum.GetValues<QueryParamFilterOperator>();
			if (reader.TokenType == JsonTokenType.Number)
			{
				var typeIndex = reader.GetInt32();
				return operators.Length > typeIndex ? operators[typeIndex] : QueryParamFilterOperator.Equal;
			}

			foreach (var filterOperator in operators)
			{
				var existingValue = reader.GetString();
				if (filterOperator.GetSign().ToLower() == existingValue?.ToLower())
					return filterOperator;
			}

			return QueryParamFilterOperator.Equal;
		}

		/// <summary>
		/// Serialization method for enum to string
		/// </summary>
		/// <param name="writer"></param>
		/// <param name="value"></param>
		/// <param name="options"></param>
		public override void Write(Utf8JsonWriter writer, QueryParamFilterOperator value, JsonSerializerOptions options)
		{
			writer.WriteStringValue(value.GetSign());
		}
	}
	*/
}