using Humanizer;
using Levelbuild.Core.SharedUtilities;
using Microsoft.OpenApi.Extensions;

namespace Levelbuild.Frontend.WebApp.Shared.Enums;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

public enum DataColumnType
{
	String,
	Integer,
	Long,
	Double,
	Date,
	DateTime,
	Time,
	Text,
	Boolean,
	Enum,
	Icon
}

public static class DataColumnTypeExtensions
{
	public static string GetTypeAsString(this DataColumnType enumValue)
	{
		return enumValue.GetDisplayName().ToLower();
	}

	public static string GetString(this DataColumnType enumValue)
	{
		return EnumUtils<DataColumnType>.GetTranslatableString(enumValue);
	}
}

