using Humanizer;
using Levelbuild.Core.SharedUtilities;
using Microsoft.OpenApi.Extensions;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Shared.Enums;

public enum PopupPlacement
{
	Top,

	TopStart,

	TopEnd,

	Right,

	RightStart,

	RightEnd,

	Bottom,

	BottomStart,

	BottomEnd,

	Left,

	LeftStart,

	LeftEnd
}

public static class PopupPlacementExtensions
{
	public static string GetPlacementAsString(this PopupPlacement enumValue)
	{
		return enumValue.GetDisplayName().Kebaberize();
	}

	public static string GetString(this PopupPlacement enumValue)
	{
		return EnumUtils<PopupPlacement>.GetTranslatableString(enumValue);
	}
}