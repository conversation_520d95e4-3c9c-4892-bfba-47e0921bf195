using System.Globalization;

namespace Levelbuild.Frontend.WebApp.Shared.Services;

/// <summary>
/// Interface for our translation service which is able to translate strings based on a given culture
/// </summary>
public interface ITranslationService
{
	/// <summary>
	/// translates the key for the given culture
	/// </summary>
	/// <param name="culture">target culture</param>
	/// <param name="key">key to translate</param>
	/// <returns></returns>
	string? Translate(CultureInfo culture, string key);

	/// <summary>
	/// gets all available translations for a given culture
	/// </summary>
	/// <param name="culture">target culture</param>
	/// <returns></returns>
	Dictionary<string, string> GetAll(CultureInfo culture);

	/// <summary>
	/// gets the current application environment (used inside the StringLocalizer)
	/// </summary>
	/// <returns></returns>
	IHostEnvironment GetEnvironment();
}