using Levelbuild.Core.FileInterface;
using Levelbuild.Core.FileInterface.Exception;
using Levelbuild.Domain.FileSystemFile.Dto;
using Levelbuild.Domain.GoogleStorageFile;
using Levelbuild.Domain.S3BucketFile;
using Levelbuild.Domain.Storage.Helper;
using FileInfo = Levelbuild.Core.FileInterface.FileInfo;

namespace Levelbuild.Frontend.WebApp.Shared.Services.Files;

/// <summary>
/// Service for caching thumbnails
/// </summary>
public class FileCachingService : IFileCachingService
{
	private readonly FileStore _storage;
	
	private readonly SemaphoreSlim _semaphoreSlim;

	/// <summary>
	/// Creates an instance of the service
	/// </summary>
	public FileCachingService(IConfiguration configuration)
	{
		var fileStoreType = configuration.GetSection("FileCachingService")["FileStoreType"];
		_storage = fileStoreType switch
		{
			"FileSystem" => new FileSystemStore(new FileSystemFileStoreConfig(
													configuration.GetSection("FileCachingService")["RootPath"] ??
													throw new Exception("FileCachingServices RootPath is not configured!"))),
			"S3Bucket" => new S3BucketFileStore(new S3BucketFileStoreConfig(
													configuration.GetSection("FileCachingService")["RootPath"] ??
													throw new Exception("FileCachingServices RootPath is not configured!"),
													configuration.GetSection("FileCachingService")["Url"] ??
													throw new Exception("FileCachingServices Url is not configured!"),
													configuration.GetSection("FileCachingService")["Container"] ??
													throw new Exception("FileCachingServices Container is not configured!"), true,
													configuration.GetSection("FileCachingService")["Username"] ??
													throw new Exception("FileCachingServices Username is not configured!"),
													configuration.GetSection("FileCachingService")["Password"] ??
													throw new Exception("FileCachingServices Password is not configured!"))),
			"GoogleStorage" => new GoogleStorageFileStore(new GoogleStorageFileStoreConfig(
															  configuration.GetSection("FileCachingService")["RootPath"]?? "Files",
															  configuration.GetSection("FileCachingService")["Url"] ??
															  throw new Exception("FileCachingServices Url is not configured!"),
															  configuration.GetSection("FileCachingService")["Container"] ??
															  throw new Exception("FileCachingServices Container is not configured!"))),
			_ => throw new Exception("FileCachingService is not configured!")
		};

		_semaphoreSlim = new SemaphoreSlim(20);
	}

	/// <summary>
	/// Saves a  file to the services storage
	/// <param name="fileStream">the file to save</param>
	/// <param name="directory">the path to the file</param>
	/// <param name="fileId">the identifier for the original file</param>
	/// <param name="fileName">the identifier of the cached file</param>
	/// <param name="encrypt">should the file be encrypted?</param>
	/// </summary>
	public async Task UploadFileAsync(Stream fileStream, string directory, string fileId, string fileName, bool encrypt = true)
	{
		await SaveFileAsync(fileStream, $"{directory}/{fileId}/{fileName}", encrypt);
	}
	
	/// <summary>
	/// Retrieves a cached file
	/// <param name="directory">the path to the file</param>
	/// <param name="fileId">the identifier for the file</param>
	/// <param name="fileName">the name of the specific sub-file of the deep zoom image directory</param>
	/// <param name="decrypt">should the file be decrypted?</param>
	/// <param name="from">start to read the file at a specified byte</param>
	/// <param name="length">read the file for a specified length</param>
	/// </summary>
	public Stream? GetFile(string directory, string fileId, string fileName, bool decrypt = true, long? from = null, long? length = null)
	{
		return LoadFile($"{directory}/{fileId}/{fileName}", decrypt, from, length);
	}
	
	/// <summary>
	/// Retrieves a cached file
	/// <param name="directory">the path to the file</param>
	/// <param name="fileId">the identifier for the file</param>
	/// <param name="fileName">the name of the specific sub-file of the deep zoom image directory</param>
	/// <param name="decrypt">should the file be decrypted?</param>
	/// <param name="from">start to read the file at a specified byte</param>
	/// <param name="length">read the file for a specified length</param>
	/// </summary>
	public async Task<Stream?> GetFileAsync(string directory, string fileId, string fileName, bool decrypt = true, long? from = null, long? length = null, CancellationToken cancellationToken = default)
	{
		return await LoadFileAsync($"{directory}/{fileId}/{fileName}", decrypt, from, length, cancellationToken);
	}
	
	/// <summary>
	/// Deletes  the specified file
	/// <param name="directory">the path to the file</param>
	/// <param name="fileId">the identifier for the file</param>
	/// <param name="fileName">the name of the specific sub-file of the deep zoom image directory</param>
	/// </summary>
	public void DeleteFile(string directory, string fileId, string fileName)
	{
		try
		{
			var file = _storage.GetFile($"{directory}/{fileId}/{fileName}");
			file.DeleteFile();
		}
		catch (FileNotFoundException)
		{
			throw;
		}
		catch (Exception e)
		{
			throw new FileDeleteException("File could not be deleted!", e);
		}
	}

	/// <summary>
	/// returns encrypted stream
	/// <param name="inputStream">the stream to encrypt</param>
	/// <param name="outputStream">the stream to write the encrypted stream to</param>
	/// <param name="filePath">the identifier for the file</param>
	/// </summary>
	public void EncryptFile(Stream inputStream, Stream outputStream, string filePath)
	{
		var key = EncryptionHelper.GetKeyFileIdOnly(filePath);
		EncryptionHelper.AesEncrypt(inputStream, outputStream, key, false);
		if(outputStream.CanSeek)
			outputStream.Seek(0, SeekOrigin.Begin);
	}

	private Stream? LoadFile(string path, bool decrypt = true, long? from = null, long? length = null)
	{
		Stream? stream = null;
		try
		{
			var fileInfo = _storage.GetFile(path);

			if (decrypt)
			{
				var key = EncryptionHelper.GetKeyFileIdOnly(path);
				//stream = new MemoryStream(length != null ? (int)length : 0);
				stream = new MemoryStream();
				using var readStream = fileInfo.ReadFile(from, length);
				EncryptionHelper.AesDecrypt(readStream, stream, key, false);
				if(stream.CanSeek)
					stream.Seek(0, SeekOrigin.Begin);
			}
			else
				stream = fileInfo.ReadFile(from, length);
		}
		catch (Exception e)
		{
			if (stream != null)
			{
				try
				{
					stream.Dispose();
				}
				catch (Exception)
				{
					// ignored
				}
			}

			if (e is FileNotFoundException)
				return null;

			throw;
		}

		return stream;
	}
	
	private async Task<Stream?> LoadFileAsync(string path, bool decrypt = true, long? from = null, long? length = null, CancellationToken cancellationToken = default)
	{
		Stream? stream = null;
		try
		{
			var fileInfo = _storage.GetFile(path);

			if (decrypt)
			{
				var key = EncryptionHelper.GetKeyFileIdOnly(path);
				stream = new MemoryStream();
				await using var readStream = fileInfo.ReadFileWithCancellation(from, length, cancellationToken);
				EncryptionHelper.AesDecrypt(readStream, stream, key, false);
				if(stream.CanSeek)
					stream.Seek(0, SeekOrigin.Begin);
			}
			else
				stream = fileInfo.ReadFileWithCancellation(from, length, cancellationToken);
		}
		catch (Exception e)
		{
			if (stream != null)
			{
				try
				{
					await stream.DisposeAsync();
				}
				catch (Exception)
				{
					// ignored
				}
			}

			if (e is FileNotFoundException)
				return null;

			throw;
		}

		return stream;
	}

	private async Task SaveFileAsync(Stream fileStream, string filePath, bool encrypt = true)
	{
		try
		{
			await _semaphoreSlim.WaitAsync();
			var file = _storage.GetFile(filePath);

			if (encrypt)
			{
				var key = EncryptionHelper.GetKeyFileIdOnly(filePath);
				using var outputStream = new MemoryStream();
				EncryptionHelper.AesEncrypt(fileStream, outputStream, key, false);
				if(outputStream.CanSeek)
					outputStream.Seek(0, SeekOrigin.Begin);
				
				await WriteFileWithRetryAsync(file, outputStream);
			}
			else
				await WriteFileWithRetryAsync(file, fileStream);
		}
		catch (Exception e)
		{
			throw new FileSaveException("File could not be saved!", e);
		}
		finally
		{
			_semaphoreSlim.Release();
		}
	}

	private async Task WriteFileWithRetryAsync(FileInfo file, Stream outputStream, int retries = 0)
	{
		try
		{
			await file.WriteFileAsync(outputStream);
		}
		catch (Exception e)
		{
			if(retries > 3)
				throw new FileSaveException("File could not be saved!", e);
			
			retries++;
			var delay = new Random().Next(500, 2000) * retries * retries;
			await Task.Delay(delay);
			
			await WriteFileWithRetryAsync(file, outputStream, retries);
		}
	}
	
	private async Task WriteFileWithRetryAsync(FileInfo file, byte[] outputStream, int retries = 0)
	{
		try
		{
			await file.WriteFileAsync(outputStream);
		}
		catch (Exception e)
		{
			if(retries > 3)
				throw new FileSaveException("File could not be saved!", e);
			
			retries++;
			var delay = new Random().Next(500, 2500) * retries * retries;
			await Task.Delay(delay);
			
			await WriteFileWithRetryAsync(file, outputStream, retries);
		}
	}
}