namespace Levelbuild.Frontend.WebApp.Shared.Services.Files;

/// <summary>
/// Service for caching thumbnails
/// </summary>
public interface IFileCachingService
{
	/// <summary>
	/// Saves a  file to the services storage
	/// <param name="fileStream">the file to save</param>
	/// <param name="directory">the path to the file</param>
	/// <param name="fileId">the identifier for the original file</param>
	/// <param name="fileName">the identifier of the cached file</param>
	/// <param name="encrypt">should the file be encrypted?</param>
	/// </summary>
	public Task UploadFileAsync(Stream fileStream, string directory, string fileId, string fileName, bool encrypt = true);

	/// <summary>
	/// Retrieves a cached file
	/// <param name="directory">the path to the file</param>
	/// <param name="fileId">the identifier for the file</param>
	/// <param name="fileName">the name of the specific sub-file of the deep zoom image directory</param>
	/// <param name="decrypt">should the file be decrypted?</param>
	/// <param name="from">start to read the file at a specified byte</param>
	/// <param name="length">read the file for a specified length</param>
	/// </summary>
	public Stream? GetFile(string directory, string fileId, string fileName, bool decrypt = true, long? from = null, long? length = null);
	
	/// <summary>
	/// Retrieves a cached file
	/// <param name="directory">the path to the file</param>
	/// <param name="fileId">the identifier for the file</param>
	/// <param name="fileName">the name of the specific sub-file of the deep zoom image directory</param>
	/// <param name="decrypt">should the file be decrypted?</param>
	/// <param name="from">start to read the file at a specified byte</param>
	/// <param name="length">read the file for a specified length</param>
	/// </summary>
	public Task<Stream?> GetFileAsync(string directory, string fileId, string fileName, bool decrypt = true, long? from = null, long? length = null, CancellationToken cancellationToken = default);

	/// <summary>
	/// Deletes  the specified file
	/// <param name="directory">the path to the file</param>
	/// <param name="fileId">the identifier for the file</param>
	/// <param name="fileName">the name of the specific sub-file of the deep zoom image directory</param>
	/// </summary>
	public void DeleteFile(string directory, string fileId, string fileName);

	/// <summary>
	/// returns encrypted stream
	/// <param name="inputStream">the stream to encrypt</param>
	/// <param name="outputStream">the stream to write the encrypted stream to</param>
	/// <param name="filePath">the identifier for the file</param>
	/// </summary>
	public void EncryptFile(Stream inputStream, Stream outputStream, string filePath);
}