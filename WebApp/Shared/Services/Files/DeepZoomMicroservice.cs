using System.Net.Http.Headers;
using System.Net.Mime;

namespace Levelbuild.Frontend.WebApp.Shared.Services.Files;

/// <summary>
/// Service to call upon the deep zoom microservice
/// </summary>
public class DeepZoomMicroservice : IDeepZoomMicroservice
{
	private readonly IConfiguration _configuration;

	private readonly string _microserviceSecret = "~#4!levelbuild-l905-#34-extrem<2*0{-geheim|9+";

	/// <summary>
	/// inject the Configuration
	/// </summary>
	/// <param name="configuration"></param>
	public DeepZoomMicroservice(IConfiguration configuration)
	{
		_configuration = configuration;
	}
	
	/// <summary>
	/// Sends a request to the microservice and returns the response
	/// </summary>
	public async Task<HttpResponseMessage> GetDeepZoomFile(Stream file, string fileName = "file", int? dpi = 300, CancellationToken cancellationToken = default)
	{
		using MultipartFormDataContent requestContent = new();
		
		requestContent.Headers.Add("LB-secret", _microserviceSecret);
			
		if (dpi.HasValue)
		{
			var dpiParam = new StringContent(dpi.ToString()!);
			requestContent.Add(dpiParam, "dpi");
		}
			
		cancellationToken.ThrowIfCancellationRequested();
		
		using var fileContent = new StreamContent(file);
		fileContent.Headers.ContentType = MediaTypeHeaderValue.Parse(MediaTypeNames.Application.Octet);
		requestContent.Add(fileContent, "file", fileName);

		var url = _configuration.GetSection("DeepZoomMicroService")["Url"];
		if(url == null)
			throw new Exception("DeepZoomMicroService Url configuration is missing");

		cancellationToken.ThrowIfCancellationRequested();
		
		using var client = new HttpClient();
		client.Timeout = TimeSpan.FromMinutes(10);
		var response = await client.PostAsync(url, requestContent, cancellationToken: cancellationToken);

		return response;
	}
}