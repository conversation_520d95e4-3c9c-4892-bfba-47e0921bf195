@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.Home.Services
@using Levelbuild.Frontend.WebApp.Features.User.Services
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@inject UserManager UserManager
@inject IExtendedStringLocalizerFactory LocalizerFactory
@inject NavigationService NavigationService

@{
	var localizer = LocalizerFactory.Create("Layout", "");
	var userLocalizer = LocalizerFactory.Create("User", "");
	var user = await UserManager.GetCurrentUserAsync();

	var menuItems = await NavigationService.GetItemsAsync();
	var customerList = await UserManager.GetAllowedCustomersAsync();
	var customerAutocompleteValues = new List<AutocompleteOptionDefinition>();
	foreach (var customer in customerList)
	{
		customerAutocompleteValues.Add(new AutocompleteOptionDefinition(customer.Id, customer.DisplayName));
	}
}

<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8"/>
	<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
	<meta name="color-scheme" content="dark light">
	<title>levelbuild</title>
	@if (Program.IsDevelopment && !Program.IsRunningInContainer)
	{
		<link rel="stylesheet" vite-href="~/css/site.css" asp-append-version="true"/>
	}
	else
	{
		<link rel="stylesheet" href="~/css/site.css" asp-append-version="true"/>
	}
	<link rel="stylesheet" href="~/libs/fontawesome/css/all.min.css"/>
	<style>
		#menu-button:not(:defined) {
			display: inline-block;
			width:2.4rem;
			text-align: center;
			
			& > * {
				display: none;
			}
			
			&:after {
				content: "\f0c9";
				font-family: "Font Awesome 6 Pro", system-ui;
				font-weight: 900;
				font-size: var(--size-text-m);
				color: var(--clr-text-primary-default);
				visibility: visible;
			}
		}

		header > img {
			user-select: none;
		}
		
		#user-profile {
			--icon-color: auto;	
		}
	</style>
	<script type="module" vite-src="~/main.ts"></script>
</head>
<body>
<header>
	<lvl-nav-button id="menu-button">
		<ul>
			<li data-keywords="workplace">
				<h3>@(localizer["myWorkplace"])</h3>
				<ul>
					<li><a href="/Public/Tasks"><i class="fal fa-clipboard-list-check"></i><span>@localizer["WorkflowEntries"]</span></a></li>
				</ul>
			</li>
			@foreach (var item in menuItems.OrderBy(item => item.Label))
			{
				<li data-keywords="@(string.Join(';', item.Keywords))">
					<h3>@item.Label</h3>
					<ul>
						@foreach (var childItem in item.Childs.OrderBy(child => child.Label))
						{
							@await Html.PartialAsync("_NavigationItem.cshtml", childItem)
						}
					</ul>
				</li>
			}
			@if (await UserManager.IsAdminAsync())
			{
				<li data-keywords="news">
					<h3>@(localizer["CorporateCommunications"])</h3>
					<ul>
						<li>
							<span><i class="fal fa-newspaper"></i><span>@localizer["CorporateNews"]</span></span>
							<ul>
								<li><a href="/Admin/News"><span>@localizer["ManageNews"]</span></a></li>
								<li><a href="/Admin/NewsCategories"><span>@localizer["ManageCategories"]</span></a></li>
							</ul>
						</li>
					</ul>
				</li>
				<li data-keywords="config">
					<h3>@(localizer["productConfiguration"])</h3>
					<ul>
						<li><a href="/Admin/Products"><i class="fal fa-hand-holding-heart"></i><span>@localizer["Products"]</span></a></li>
						<li><a href="/Admin/Modules"><i class="fal fa-cubes"></i><span>@localizer["Modules"]</span></a></li>
						<li><a href="/Admin/DataSources"><i class="fal fa-database"></i><span>@localizer["DataSources"]</span></a></li>
						<li data-keywords="Masken"><a href="/Admin/Pages"><i class="fal fa-memo-pad"></i><span>@localizer["Pages"]</span></a></li>
					</ul>
				</li>
				<li>
					<h3>@(localizer["administration"])</h3>
					<ul>
						<li><a href="/Admin/DataStores"><i class="fal fa-plug"></i><span>@localizer["DataStores"]</span></a></li>
						<li><a href="/Admin/Customers"><i class="fal fa-building"></i><span>@localizer["Customers"]</span></a></li>
						<li><a href="/Admin/Users"><i class="fal fa-users"></i><span>@localizer["Users"]</span></a></li>
						<li><a href="/Admin/LoggerConfigs"><i class="fal fa-wave-pulse"></i><span>@localizer["LoggerConfig"]</span></a></li>
						<li><a href="/Admin/Logging"><i class="fal fa-waveform"></i><span>@localizer["Logs"]</span></a></li>
						<li><a href="/Admin/Cultures"><i class="fal fa-globe"></i><span>@localizer["Languages"]</span></a></li>
						<li><a href="/Admin/Translations"><i class="fal fa-subtitles"></i><span>@localizer["Translations"]</span></a></li>
					</ul>
				</li>	
			}
		</ul>
	</lvl-nav-button>
	<img alt="levelbuild logo" src="~/images/logo-levelbuild-rgb.png" height="16"/>
	<div id="user-control">
		<button-component icon="bell" rounded size="FontSize.Medium" tooltip="@localizer["mvpTooltip"]"></button-component>
		<button-component icon="headset" rounded size="FontSize.Medium" tooltip="@localizer["mvpTooltip"]"></button-component>
		@if (user != null)
		{
			<text>
				@{
					var userIcon = "user-circle";
					if (await UserManager.CheckForImpersonationAsync())
						userIcon = "user-secret";
				}
				<button-component data-dropdown="user-menu-dropdown" icon="@userIcon" tooltip="@(await UserManager.GetCurrentUserDisplayNameAsync())" rounded icon-style="IconStyle.Solid" size="FontSize.Medium"></button-component>
				<dropdown-component name="user-menu-dropdown" keep-open popup-placement="PopupPlacement.BottomEnd" shift block-offset-y="4" border="1px solid var(--clr-border-medium)" border-radius="var(--size-radius-m)" box-shadow="Intensity.Medium">
					<div id="user-profile" class="dropdown-dialog">
						<div class="profile__header">
							<span class="heading">@userLocalizer["profile"]</span>
							<button-component id="color-scheme-switch" icon="moon" tooltip="@localizer["toggleDarkMode"]"></button-component>
						</div>
						<info-element label="@userLocalizer["displayName"]" text="@(user.DisplayName)"></info-element>
						<info-element label="@userLocalizer["email"]" text="@(user.Email)"></info-element>
						@if (customerList.Count > 1)
						{
							<autocomplete-component
								id="customer-switch"
								name="customer-switch"
								label="@localizer["switchCustomer"]"
								value="@((await UserManager.GetCurrentCustomerAsync()).Id)"
								required="true"
								key-value="id"
								display-field="displayName"
								data-error-message="@localizer["switchCustomer/error"]"
								options="@customerAutocompleteValues">
							</autocomplete-component>
						}
						else
						{
							<info-element label="@userLocalizer["mainCustomerName"]" text="@((await UserManager.GetCurrentCustomerAsync()).DisplayName)"></info-element>
						}
						<separator direction="Horizontal"></separator>
						<div id="mobile-qr-container" class="hide"></div>
						<button-component id="mobile-qr-switch" class="button" icon="qrcode" label="@localizer["showMobileQr"]" data-show-label="@localizer["showMobileQr"]" data-hide-label="@localizer["hideMobileQr"]" type="ButtonType.Secondary"></button-component>
						<separator direction="Horizontal"></separator>
						@{
							var logoutLocalizer = localizer["Logout"];
							if (await UserManager.CheckForImpersonationAsync())
								logoutLocalizer = localizer["endImpersonation"];
						}
						<button-component id="logout" class="button" icon="right-from-bracket" label="@logoutLocalizer" type="ButtonType.Primary"></button-component>
					</div>
				</dropdown-component>
			</text>
		}
		@if (await UserManager.IsAdminAsync())
		{
			<i class="inline--active fa-solid fa-shield-keyhole" style="font-size:1.4rem;width:2.4rem;text-align:center;color: var(--clr-levelbuild-blue);" data-tooltip="admin-icon-tooltip"></i>
			<tooltip-component name="admin-icon-tooltip" placement="PopupPlacement.BottomEnd">@localizer["AdminRights"]</tooltip-component>
		}
	</div>
</header>
	<div id="second-header">
		<lvl-breadcrumb id="bread-crumb-navigation"></lvl-breadcrumb>
		<div class="search-wrapper header__part">
			<searchbar-component id="global-search" disabled rounded main-bar with-sorting></searchbar-component>
		</div>
		<div id="content-buttons" class="button-set header__part">
			<button-component data-dropdown="file-options-dropdown" data-action="file-options" label="@localizer["fileOptions"]" icon="ellipsis-v" color="ColorState.Info" stacked hidden></button-component>
			<dropdown-component name="file-options-dropdown">
				<dropdown-menu-component>
					<dropdown-menu-item-component icon-left="arrows-repeat" data-action="update-file">@localizer["updateFile"]</dropdown-menu-item-component>
					<dropdown-menu-item-component icon-left="trash" data-action="cut-file" icon-left-color="var(--clr-signal-error)">@localizer["deleteFile"]</dropdown-menu-item-component>
				</dropdown-menu-component>
			</dropdown-component>

			<button-component data-action="sync" label="@localizer["Sync"]" icon="rotate" color="ColorState.Info" stacked hidden></button-component>
			<button-component data-action="sync-all" label="@localizer["syncAll"]" icon="rotate" color="ColorState.Info" stacked hidden></button-component>
			<button-component data-action="import" label="@localizer["Import"]" icon="cloud-arrow-down" color="ColorState.Info" stacked hidden></button-component>
			<button-component data-action="impersonate" label="@localizer["impersonate"]" icon="user-secret" color="ColorState.Info" stacked hidden></button-component>
			<button-component data-action="deactivate" label="@localizer["deactivate"]" icon="power-off" color="ColorState.Info" stacked hidden></button-component>
			<button-component data-action="discard" label="@localizer["discard"]" icon="circle-minus" color="ColorState.Error" stacked hidden></button-component>
			<button-component data-action="reactivate" label="@localizer["reactivate"]" icon="power-off" color="ColorState.Info" stacked hidden></button-component>
			<button-component data-action="delete" label="@localizer["Delete"]" icon="trash" stacked color="ColorState.Error" hidden></button-component>
			<button-component data-action="save" label="@localizer["Save"]" hidden="true" type="ButtonType.Primary"></button-component>
			<button-component data-action="preview" label="@localizer["Preview"]" icon="eye" color="ColorState.Info" stacked hidden></button-component>
			<button-component data-action="edit" label="@localizer["Edit"]" icon="screwdriver-wrench" color="ColorState.Info" type="ButtonType.Primary" hidden style="--background-color:var(--clr-levelbuild-blue);"></button-component>
		</div>
	</div>
	<main id="content" role="main" class="pb-3 vanishing-scrollbar">
		@RenderBody()
	</main>
	<lvl-toaster id="toaster"></lvl-toaster>
	<lvl-overlay id="overlay"></lvl-overlay>
	@await RenderSectionAsync("Scripts", required: false)
	@await Html.PartialAsync("_ComponentTranslations.cshtml")
	<script>
		/* Add Logout event listener */
		document.getElementById('logout').addEventListener('click', () => {
			window.location.href = '/Auth/@(await UserManager.CheckForImpersonationAsync() ? "EndImpersonation" : "Logout")'
		})

		@if (user != null) {
			@:window.UserInfo = { name: '@user.DisplayName' }
		}
		
		/* set initial color scheme */
		const lightSwitch = document.getElementById('color-scheme-switch')
		const isDarkModeStored = window.localStorage.getItem('color-scheme') != null
		const isDarkMode = isDarkModeStored ? window.localStorage.getItem('color-scheme') === 'dark' : false // TODO: uncomment if system is dark mode ready -> window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
		
		document.documentElement.dataset['scheme'] = isDarkMode ? 'dark' : 'light'
		lightSwitch.icon = isDarkMode ? 'moon' : 'sun-bright'
	</script>
</body>
</html>