@model Levelbuild.Core.FrontendDtos.PageView.GridViewSectionDto

<section id="@(Model.Id)" class="page-designer__section" data-type="Section" slot="@(Model.GridViewColumn)">
	<div class="section-flag">
		<span>@(Model.Title)</span><i class="fal fa-arrows-up-down-left-right"></i>
	</div>
	<label style="@(Model.ShowTitle is true ? "" : "display:none;")">@(Model.TitleTranslated)</label>
	<lvl-grid rows="@(Model.RowCount + 1)"@(Model.RowCount > 0 ? " skeleton" : "")></lvl-grid>
	@if (Model.RowCount > 0)
	{
		<script type="module" defer>
			const section = document.getElementById("@(Model.Id)")
			const pageDesigner = section.closest('lvl-page-designer')
			if (!customElements.get('lvl-page-designer'))
				await customElements.whenDefined('lvl-page-designer')
			await pageDesigner.refreshSectionElements(section)
			section.querySelector('lvl-grid').removeAttribute('skeleton')
		</script>
	}
</section>