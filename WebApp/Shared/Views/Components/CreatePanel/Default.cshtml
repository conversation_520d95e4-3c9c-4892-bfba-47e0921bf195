@using Humanizer
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Microsoft.AspNetCore.Html
@using Microsoft.IdentityModel.Tokens
@model Levelbuild.Frontend.WebApp.Shared.Views.Components.CreatePanel.CreatePanelModel
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var apiUrl = $"/Api/{Model.RouteName}";
	var adminDefaultUrl = $"/Admin/{Model.RouteName}";
	var kebabEntity = Model.EntityName.Kebaberize();
	var listId = !Model.ListId.IsNullOrEmpty() ? Model.ListId : $"{kebabEntity}-list";
	var scriptLocalizer = LocalizerFactory.Create("ScriptMessages", "");
}
<script type="module" defer>
	const list = document.getElementById('@(listId)')
	if (!list)
		console.warn('Datalist with id: @(listId) was not found.')
			
	@if (ViewData["targetAction"]?.ToString() == "Create")
	{
		@:Page.setPanelInfo(`${Page.getMainPageUrl()}@(Model.MenuEntry != null ? "/" + Model.MenuEntry : "")/Create`, {}, '@Model.Localizer["list/newItem"]')
	}
	Page.registerCreatePanel(document.getElementById('create-panel'));

	/**
	* opens a dialog to create a new configuration
	* @@param mouseEvent {MouseEvent} button click event
	*/
	document.querySelector('lvl-fab[data-action=add]').addEventListener("click", async () =>
	{
		// edit routeParams so it can be parsed as a json
		let routeParams = "{}"
		@if (!Model.RouteParams.IsNullOrEmpty())
		{
			<text>
				routeParams = "@(new HtmlString(Model.RouteParams))"
				routeParams = routeParams.replace(/\w+(?=:)/g, (match) => "\""+match+"\"")
				routeParams = routeParams.replace(/'(\w+)'/g, (match, groupOne) => "\"" + groupOne + "\"")
			</text>
		}
		
		let routeParamsJson = JSON.parse(routeParams)

		@if (Model.AdditionalParentRouteParams != null)
		{
			foreach (var parentRouteParam in Model.AdditionalParentRouteParams)
			{
				<text>
					routeParamsJson['@parentRouteParam.Key'] = Page.getFormData()["@parentRouteParam.Value"]
				</text>
			}
		}
	
		await Page.showCreatePanel('@(adminDefaultUrl)/Create', routeParamsJson, '@Model.Localizer["list/newItem"]')
		Page.setPanelInfo(`${Page.getMainPageUrl()}@(Model.MenuEntry != null ? "/" + Model.MenuEntry : "")/Create`, {}, '@Model.Localizer["list/newItem"]')

		@* @TODO: remove as soon as all forms are lvl - form *@
		const form = Page.createSlideOut.querySelector('lvl-form, .form')
		@if (Model.Skeleton)
		{
			<text>
				if (form != null)
					form.skeleton = false
			</text>
		}
		
		@if (!string.IsNullOrEmpty(Model.ParentPropertyName))
		{
			<text>
		
				const parentInput = form.querySelector('input[name="@(Model.ParentPropertyName)"]')
				if (parentInput && Page.getFormData()?.id) {
					parentInput.value = Page.getFormData().id
					parentInput.dispatchEvent(new Event('change'));
				}
				if (form.tagName === 'LVL-FORM')
					form.skeleton = false
			</text>
		}
	})
	
	const handleSaveButtonClick = async () => {
		const result = await Form.storeData(Page.createSlideOut.querySelector('form, lvl-form'), '@apiUrl', 'POST')
		if (!result)
			return
		Page.createSlideOut.open = false
		list?.reload()
	}
	
	const saveButton = Page.createSlideOut.querySelector('[data-action="save"]')
	saveButton?.addEventListener('click', handleSaveButtonClick, { signal: Page.getPageChangeSignal() })
	Page.createSlideOut.setAttribute('initDone', '')
</script>
<fab-component data-action="add" skeleton="Model.Skeleton"></fab-component>
<slide-out-component id="create-panel" class="side-panel" position="@(Alignment.Right)" modal anchor heading="@Model.Localizer["list/newItem"]" width="550" open="@(ViewData["targetAction"]?.ToString() == "Create")">
	<div class="content @(Model.IgnoreOverflow ? "" : "vanishing-scrollbar static-scrollbar")">
		@if (ViewData["targetAction"]?.ToString() == "Create")
		{
			@await Html.PartialAsync(ViewData["targetAction"] as string, ViewData["targetViewModel"])
		}
	</div>
	<button-component slot="button-left" data-action="cancel" label="@Model.Localizer["abortButton"]" type="ButtonType.Secondary" color="ColorState.Info"></button-component>
	<button-component slot="button-right" data-action="save" label="@Model.Localizer["saveButton"]" type="ButtonType.Primary"></button-component>
</slide-out-component>