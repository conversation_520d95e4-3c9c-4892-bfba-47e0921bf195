using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

namespace Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminListPage;

/// <summary>
/// Component for the configuration list template
/// </summary>
public class AdminListPageViewComponent : ViewComponent
{
	/// <summary>
	/// Default method to render a view component
	/// </summary>
	/// <param name="entity">Name of the WebAppEntity in PascalCase</param>
	/// <param name="routeName">Entity name which is used in url routes</param>
	/// <param name="localizer">Used to translate labels</param>
	/// <param name="columns">Columns for the data list</param>
	/// <param name="sorting">Sorting for the data list</param>
	/// <param name="parentPropertyName">Is the list entity a children of another Entity? Used for parent filtering</param>
	/// <param name="menuEntry">Is the access of the list view part of a menu view</param>
	/// <param name="openOnRowClick">Add Event listener to open the detail view when row item was clicked?</param>
	/// <param name="useCustomList">Implement your own data list html</param>
	/// <param name="filterByMenu">Shall the list be filtered by a menu component?</param>
	/// <param name="routeParams">Optional Get Params for the URL</param>
	/// <param name="displayPropertyName">Where can the display value be found?</param>
	/// <param name="listId">Optional custom list id (default is "{EntityName kebaberized}-list")</param>
	/// <param name="activeColumn">Optional name of the column which controls active/inactive state</param>
	/// <param name="parentPropertyValue">Should the list be filtered for a specific parent entry already?</param>
	/// <returns></returns>
	public IViewComponentResult Invoke(string entity, string routeName, IStringLocalizer localizer, IList<BaseColumnComponentTagHelper>? columns = null,
									   List<QueryParamSortingDto>? sorting = null, string? parentPropertyName = null, string? menuEntry = null,
									   bool? openOnRowClick = false, bool? useCustomList = false, bool? filterByMenu = false, string? routeParams = null,
									   string? displayPropertyName = null, string? listId = null, string? activeColumn = null, Guid? parentPropertyValue = null)
	{
		AdminListPageModel model = new()
		{
			EntityName = entity,
			RouteName = routeName,
			Localizer = localizer,
			Columns = columns ?? new List<BaseColumnComponentTagHelper>(),
			Sorting = sorting,
			ParentPropertyName = parentPropertyName,
			ParentPropertyValue = parentPropertyValue,
			DisplayPropertyName = displayPropertyName,
			MenuEntry = menuEntry,
			OpenOnRowClick = openOnRowClick == true,
			UseCustomList = useCustomList == true,
			FilterByMenu = filterByMenu == true,
			RouteParams = routeParams,
			ListId = listId,
			ActiveColumn = activeColumn
		};
		
		return View(model);
	}
}

/// <summary>
/// Model for the configuration list template
/// </summary>
public class AdminListPageModel
{
	/// <summary>
	/// Name of the WebAppEntity in PascalCase
	/// </summary>
	public required string EntityName { get; init; }
	
	/// <summary>
	/// Entity name which is used in url routes
	/// </summary>
	public required string RouteName { get; init; }
	
	/// <summary>
	/// Use to translate labels
	/// </summary>
	public required IStringLocalizer Localizer { get; init; }
	
	/// <summary>
	/// Columns for the data list
	/// </summary>
	public IList<BaseColumnComponentTagHelper> Columns { get; init; } = [];
	
	/// <summary>
	/// Sorting for the data list
	/// </summary>
	public List<QueryParamSortingDto>? Sorting { get; init; }
	
	/// <summary>
	/// Is the list entity a children of another Entity? Used for parent filtering
	/// </summary>
	public string? ParentPropertyName { get; init; }
	
	/// <summary>
	/// Should the list be filtered for a specific parent entry already?
	/// </summary>
	public Guid? ParentPropertyValue { get; init; }
	
	/// <summary>
	/// Where can the display value be found?
	/// </summary>
	public string? DisplayPropertyName { get; init; }
	
	/// <summary>
	/// Is the access of the create-panel part of a menu view
	/// </summary>
	public string? MenuEntry { get; init; }
	
	/// <summary>
	/// Name of the column which controls active/inactive in camelCase
	/// </summary>
	public string? ActiveColumn { get; init; }
	
	/// <summary>
	/// Add Event listener to open the detail view when row item was clicked?
	/// </summary>
	public bool OpenOnRowClick { get; init; }
	
	/// <summary>
	/// Implement your own data list html
	/// </summary>
	public bool UseCustomList { get; init; }
	
	/// <summary>
	/// Shall the list be filtered by a menu component?
	/// </summary>
	public bool FilterByMenu { get; init; }
	
	/// <summary>
	/// Optional Get Params for the URL
	/// </summary>
	public string? RouteParams { get; init; }
	
	/// <summary>
	/// Optional custom list id (default is "{EntityName kebaberized}-list")
	/// </summary>
	public string? ListId { get; init; }
}