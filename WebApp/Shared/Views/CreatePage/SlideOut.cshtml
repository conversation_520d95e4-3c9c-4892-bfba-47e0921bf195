@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Microsoft.IdentityModel.Tokens
@model Levelbuild.Frontend.WebApp.Shared.ViewModels.CreatePageModel
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("CreatePage", "");
}

<slide-out-component id="create-panel" class="side-panel @(Model.AllowFile ? "allow-file" : "")" position="@(Alignment.Right)" modal anchor heading="@(Model.CreatePageTitle ?? localizer["heading/dataset"])" icon="pen-field"
                     width="480" open="@(ViewData["targetAction"]?.ToString() == "Create")">
	<!--suppress CssUnresolvedCustomProperty, CssUnusedSymbol -->
	<style>
		.dropzone-preview {
			position: absolute;
			inset: 0;
			z-index: 10000;
			background-color: rgb(0 0 0 / 50%);
			pointer-events: none;
			user-select: none;
			padding:  var(--size-spacing-xxxl);
			opacity:  0;
			transition: opacity 150ms ease-in;
		}

		.dropzone-preview > div {
			border: 0.4rem dashed rgb(255 255 255 / 50%);
			border-radius: var(--size-radius-l);
			width: 100%;
			height: 100%;
			padding: 3.5rem;
			position: relative;
			display:  flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			color: var(--clr-white);
			font-size: var(--size-text-l);
			row-gap: var(--size-spacing-l);
		}

		.dropzone-preview > div > i {
			font-size:  4rem;
		}

		.dz-drag-hover .dropzone-preview {
			opacity:  1;
		}

		.fileQueue {
			position: absolute;
			top: 0;
			left: 0;
			width:  360px;
			height:  100%;
			background-color: var(--cp-clr-background-lvl-0);
			display: grid;
			grid-template-rows: auto 1fr auto;
			transform: translateX(0); /* dropdown fixed needs to be positioned inside fileQueue, not slide-out */
			z-index: -2;
		}

		.filePreview {
			position: absolute;
			top: 0;
			left: 0;
			width:  480px;
			height:  100%;
			background-color: var(--cp-clr-background-lvl-0);
			display: none;
			grid-template-rows: auto 1fr auto;
			z-index: -1;
		}

		.file-upload-button {
			display: none;
		}

		lvl-slide-out.opening,
		lvl-slide-out.closing {
			& .fileQueue,
			& .filePreview {
				transition: left ease-in var(--animation-time);
			}
		}

		.filePreview > main {
			position:  relative;
		}

		lvl-slide-out[open] {
			& .fileQueue {
				left: -360px;
			}

			& .filePreview {
				left: -480px;
			}
		}

		@@media (min-width:1350px) {
			lvl-slide-out[open].allow-file .fileQueue {
				left:  -840px;
			}

			lvl-slide-out.allow-file .filePreview {
				display: grid;
			}

			lvl-slide-out.allow-file .file-upload-button {
				display: block;
			}
		}

		@@media (min-width:1800px) {
			lvl-slide-out.allow-file .filePreview {
				width:  580px;
			}

			lvl-slide-out[open].allow-file .filePreview {
				left: -580px;
			}

			lvl-slide-out[open].allow-file .fileQueue {
				left: -940px;
			}
		}

		@@media (min-width:2000px) {
			lvl-slide-out.allow-file .filePreview {
				width:  680px;
			}

			lvl-slide-out[open].allow-file .filePreview {
				left: -680px;
			}

			lvl-slide-out[open].allow-file .fileQueue {
				left: -1040px;
			}
		}

		@@media (min-width:2200px) {
			lvl-slide-out.allow-file .filePreview {
				width:  780px;
			}

			lvl-slide-out[open].allow-file .filePreview {
				left: -780px;
			}

			lvl-slide-out[open].allow-file .fileQueue {
				left: -1140px;
			}
		}

		.fileQueue header,
		.filePreview header {
			display: flex;
			align-items: center;
			column-gap: var(--size-spacing-s);
			font-size: var(--font-size-header);
			padding: var(--size-spacing-m) var(--size-spacing-m) var(--size-spacing-m) 1.2rem;
			height: 4rem;
			min-width: 0;
			border-bottom: none;
			background-color: var(--clr-background-lvl-0);

			& i {
				display: inline-block;
				width: 2.4rem;
				height: 2.4rem;
				line-height: 2.4rem;
				text-align: center;
			}
		}

		.fileQueue main,
		.filePreview main {
			padding:  var(--size-spacing-l);
			background-color: var(--cp-clr-background-lvl-0);
			display: block;
		}

		.filePreview main {
			padding: var(--size-spacing-xxxl);
			background-color: var(--clr-background-viewer);
			overflow:  hidden;
		}

		.fileQueue footer,
		.filePreview footer {
			height: 6.4rem;
			padding:  var(--size-spacing-l);
			display: flex;
			align-items: center;
			justify-content: space-between;
			gap: var(--size-spacing-m);
		}

		.file-dropzone,
		.file-dropzone-button {
			border: 5px dashed var(--clr-border-medium);
			border-radius: var(--size-radius-l);
			width: 100%;
			height: 100%;
			padding: 3.5rem;
			position: relative;
		}

		.file-dropzone.uploading {
			border: none;
		}

		.file-dropzone-button,
		.element-add-button {
			height:  auto;
			padding: 1.8rem 0;
			text-align: center;
			display: none;
		}

		.element-add-button {
			display:  block;
		}

		lvl-slide-out.allow-file .file-dropzone-button {
			display: block;
		}

		lvl-slide-out.allow-file .element-add-button {
			display:  none;
		}

		.file-dropzone:before,
		.file-dropzone-button:before {
			content:  "";
			position: absolute;
			box-shadow: 0 0 0 4px var(--clr-background-viewer);
			border-radius: var(--size-radius-m);
			inset: 0;
			pointer-events: none;
		}

		.file-dropzone-button:before {
			box-shadow: 0 0 0 4px var(--cp-clr-background-lvl-0);
		}

		.file-dropzone__empty {
			color: var(--clr-text-tertiary);
			text-align:  center;
			flex-direction: column;
			row-gap: var(--size-spacing-m);
			height:  100%;
			justify-content: center;
			user-select: none;
			display: none;

			& i {
				font-size: 40px;
			}

			& h2 {
				font-size: var(--size-text-l);
				font-weight: 400;
				min-height: 26px;
				margin: 0;
			}

			& lvl-button {
				margin: var(--size-spacing-l) auto 0;
			}
		}

		.file-dropzone__upload {
			color: var(--clr-text-tertiary);
			text-align:  center;
			flex-direction: column;
			row-gap: var(--size-spacing-m);
			width:  50%;
			margin: 0 auto;
			height:  100%;
			justify-content: center;
			user-select: none;
			display: none;

			& i {
				font-size: 4.0rem;
				margin-bottom: var(--size-spacing-m);
			}

			& .file-upload-status {
				color:  var(--clr-state-active);
				font-weight: 600;
			}

			& .file-upload-filename {
				color:  var(--clr-text-primary-default);
				text-align:  left;
				font-weight: 600;
				line-height: var(--size-spacing-l);
				max-height: var(--size-spacing-xxl);
				overflow: hidden;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				word-break: break-all;
			}

			& .file-upload-progress {
				height: 4px;
				border-radius: var(--size-radius-m);
				background-color: var(--clr-background-lvl-2);
				position: relative;
				overflow: hidden;
			}

			& .file-upload-progress:after {
				content: "";
				position: absolute;
				left: 0;
				bottom: 0;
				height: 4px;
				width: calc(var(--progress)* 1%);
				background-color: var(--cp-clr-state-active);
				transition: width 150ms ease-in;
			}

			& legend {
				font-size: var(--size-text-m);
				color: var(--clr-text-secondary);
				display:  flex;

				& .file-size-uploaded {
					color: var(--clr-state-active);
				}

				& .file-upload-percent {
					color: var(--clr-state-active);
					font-weight: 600;
					flex-grow:  1;
					text-align:  right;
				}

				& .file-upload-percent:after {
					content: ' %';
				}
			}

			& lvl-button {
				margin: var(--size-spacing-m) auto 0;
			}
		}

		.file-dropzone__upload.success {
			& i:before {
				content:  "\f058" !important;
				color:  var(--clr-signal-success);
			}

			& .file-upload-status {
				color:  var(--clr-signal-success);
			}

			& .file-upload-progress:after {
				background-color:  var(--clr-signal-success);
			}
		}

		.file-dropzone__upload.error {
			& i:before {
				content:  "\f071" !important;
				color:  var(--clr-signal-warning);
			}

			& .file-upload-status {
				color:  var(--clr-signal-warning);
			}

			& .file-upload-progress:after {
				background-color:  var(--clr-signal-warning);
			}
		}

		.file-dropzone.empty .file-dropzone__empty,
		.file-dropzone.uploading .file-dropzone__upload {
			display: flex;
		}

		.element-queue {
			padding: 0;
			margin: var(--size-spacing-m) 0;
		}

		.element-queue > li {
			display:  flex;
			align-items: center;
		}

		.element-preview {
			border:  2px solid var(--cp-clr-border-medium);
			border-radius:  var(--size-radius-m);
			padding: 6px;
			width:  100%;
			position: relative;
			display:  flex;
			align-items: center;
			column-gap: var(--size-spacing-m);
			overflow:  hidden;

			& > div {
				flex-grow: 1;
			}

			& label {
				max-height: 3.2rem;
				overflow: hidden;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				word-break: break-all;
			}

			& legend {
				color: var(--clr-text-secondary);
				margin-top: var(--size-spacing-s);
				line-height:  16px;
			}

			& i.file-icon {
				font-size: var(--size-text-xl);
				color: var(--clr-state-active);
				line-height:  40px;
				width: 40px;
				height:  40px;
				text-align: center;
				margin: 10px 0;
				flex-shrink: 0;
			}

			& [data-action=close] {
				align-self: start;
			}

			& .button-circle {
				--button-radius: 50%;
				flex-shrink: 0;
			}
		}

		.element-preview.error {

			& i.file-icon:before {
				content: "\f071" !important;
				color: var(--clr-signal-warning);
			}

			& legend {
				color: var(--clr-signal-warning);
			}
		}

		.element-preview[progress].error {
			border-bottom-color: var(--clr-signal-warning);
		}

		.element-preview[progress].error:after {
			background-color: var(--clr-signal-warning);
		}

		.element-preview[selected] {
			border-color: var(--cp-clr-state-active);
			background-color: var(--cp-clr-signal-info-light);

			& label {
				font-weight:  bold;
			}
		}

		.element-preview[progress]:after {
			content:  "";
			position:  absolute;
			left: 0;
			bottom: 0;
			height: 3px;
			width: calc(var(--progress) * 1%);
			background-color: var(--cp-clr-state-active);
			transition: width 150ms ease-in;
		}

		.file-viewer {
			position:  absolute;
			inset:  0;
			display:  none;

			& i {
				font-size: var(--size-text-xxl);
				color: var(--clr-white);
				display: none;
			}
		}

		.file-viewer.loading {
			background-color: rgb(0 0 0 / 50%);

			& i {
				display: inline;
				position:  absolute;
				top:  50%;
				left: 50%;
				width:  3.2rem;
				height:  3.2rem;
				margin: calc(var(--size-spacing-l) * -1);
			}
		}

		.file-viewer.enabled {
			display:  block;
		}
	</style>
	<div class="fileQueue @(Model.RenderCreatePage ? "" : "hidden")" onmouseup="event.stopPropagation()">
		<header><i class="fal fa-hourglass-clock"></i>@localizer["heading/queue"] (1)</header>
		<main>
			<button-component disabled="true" label="@localizer["selectAll"]"></button-component>
			<ul class="element-queue">
				<li>
					<checkbox-component readonly="true"></checkbox-component>
					<div class="element-preview" selected>
						<i class="file-icon fal fa-memo-pad"></i>
						<div>
							<label>@localizer["newDataset"]</label>
							<legend></legend>
						</div>
						<lvl-button class="file-edit-button button-circle" icon="ellipsis-vertical" data-dropdown="fileEditDropdown" icon-style="solid" disabled hidden></lvl-button>
						<lvl-dropdown name="fileEditDropdown" placement="bottom-end">
							<lvl-menu>
								<lvl-menu-item class="file-replace" icon-left="arrows-repeat">@localizer["replaceFile"]</lvl-menu-item>
								<lvl-menu-item class="file-remove" icon-left="trash" style="color:var(--clr-signal-error);">@localizer["removeFile"]</lvl-menu-item>
							</lvl-menu>
						</lvl-dropdown>
						<lvl-button class="file-upload-button button-circle" icon="arrow-up-from-bracket" tooltip="@localizer["addFile"]" style="--text-color: var(--clr-state-active);"></lvl-button>
						<lvl-button icon="xmark" data-action="close" size="small" disabled></lvl-button>
					</div>
				</li>
			</ul>
			<div class="file-dropzone-button">
				<button-component color="ColorState.Info" icon="arrow-up-from-bracket" label="@localizer["addEntry"]" disabled="true"></button-component>
			</div>
			<div class="element-add-button">
				<button-component color="ColorState.Info" icon="plus-circle" label="@localizer["addEntry"]" disabled="true"></button-component>
			</div>
		</main>
		<footer>
			<button-component label="@localizer["removeSelection"]" icon="trash" type="ButtonType.Secondary" disabled="true" color="ColorState.Active"></button-component>
			<button-component label="@localizer["createSelection"]" type="ButtonType.Primary" disabled="true"></button-component>
		</footer>
	</div>
	<div class="filePreview" onmouseup="event.stopPropagation()">
		<header><i class="fal fa-eye"></i>@localizer["heading/preview"]</header>
		<main>
			<div class="file-dropzone empty">
				<div class="file-dropzone__empty">
					<i class="fal fa-arrow-up-from-bracket"></i>
					<h2>@localizer["noPreviewAvailable"]</h2>
					<legend>@localizer["dragFileToUpload"]</legend>
					<button-component class="file-upload-start" label="@localizer["addEntry"]" icon="plus-circle" type="ButtonType.Secondary" color="ColorState.Info"></button-component>
				</div>
				<div class="file-dropzone__upload">
					<i class="fal fa-arrow-up-from-bracket"></i>
					<span class="file-upload-status">@localizer["uploading"] ...</span>    
					<span class="file-upload-filename"></span>
					<span class="file-upload-progress"></span>
					<legend>
						<span class="file-size-uploaded">0</span>&nbsp;/&nbsp;<span class="file-size-total"></span>&nbsp;·&nbsp;<span class="file-type"></span>
						<span class="file-upload-percent">0</span>
					</legend>
					<button-component class="file-upload-abort" label="@localizer["abortButton"]" type="ButtonType.Secondary" color="ColorState.Info"></button-component>
				</div>
			</div>
			<viewer-component class="file-viewer" readonly="true"></viewer-component>
		</main>
	</div>
	<div class="content">
		@if (Model.RenderCreatePage)
		{
			<text>
				<script type="module">
					Page.registerCreatePanel(document.getElementById('create-panel'))
					Page.createSlideOut.skeleton = true
					await Page.showCreatePanel(`/Api/Pages/@Model.CreatePageId/Render`)
					Page.setPanelInfo(window.Page.getMainPageUrl() + '/Create')
					Page.createSlideOut.skeleton = false
				</script>
			</text>
		}
	</div>
	<button-component slot="button-left" data-action="cancel" label="@localizer["abortButton"]" color="ColorState.Info"></button-component>
	<button-component slot="button-right" data-action="save" label="@(localizer[!Model.SaveButtonLabel.IsNullOrEmpty() ? Model.SaveButtonLabel! : "saveButton"])" type="ButtonType.Primary"></button-component>
</slide-out-component>
<script type="module" defer>
	const createPanel = document.getElementById('create-panel')
	Page.registerCreatePanel(createPanel)
	
	const fileViewer = createPanel.querySelector('.file-viewer')
	createPanel.addEventListener('dialog-close', () => {
		fileViewer.querySelector('apryse-webviewer')?.remove()
		fileViewer.classList.remove('enabled', 'loading')
		
		const elementPreview = createPanel.querySelector('.element-preview')
		elementPreview.classList.remove('error')
	})
	
	createPanel.querySelector('lvl-menu-item.file-replace').addEventListener('click', (_) => {
		const fileDropzone = Page.createSlideOut.querySelector('.file-dropzone');
		fileDropzone.parentElement.dropzone.clickableElements[0].dispatchEvent(new CustomEvent('click'))
	})

	createPanel.querySelector('lvl-menu-item.file-remove').addEventListener('click', (e) => {
		const elementPreview = e.target.closest('.element-preview')
		FileManager.resetElementPreview(elementPreview)

		const uploadInfo = Page.createSlideOut.querySelector('.file-dropzone__upload');
		FileManager.resetUploadInfo(uploadInfo)
			
		const fileDropzone = Page.createSlideOut.querySelector('.file-dropzone');
		fileDropzone.classList.remove('uploading')
		fileDropzone.classList.add('empty')
		fileDropzone.parentElement.dropzone.files.length = 0
		
		elementPreview.querySelector('.file-edit-button')?.setAttribute('disabled','')
		elementPreview.querySelector('.file-edit-button')?.setAttribute('hidden','')
		elementPreview.querySelector('.file-upload-button')?.removeAttribute('hidden')

		fileViewer.querySelector('apryse-webviewer')?.remove()
		fileViewer.classList.remove('enabled', 'loading')
	})
	
	// init dropzone if we are refreshing the window (so "showCreatePage" is not called)
	@if (Model.RenderCreatePage)
	{
		<text>
			@if (Model.AllowFile)
			{
				<text>
					const fileDropzone = Page.createSlideOut.querySelector('.file-dropzone')
					const defaultValues = { '@Model.KeyField' : '@Model.ParentElementId' }
					FileManager.initDropzone({
						target: fileDropzone.parentElement,
						dataSourceId: '@Model.DataSourceId',
						createPageId: '@Model.CreatePageId',
						defaultValues: defaultValues
					})
				</text>	
			}
			
			const createButton = Page.createSlideOut.querySelector('[data-action=save]')
			const elementPreview = Page.createSlideOut.querySelector('div.element-preview')
			createButton.onClick = async () => {
			const createForm = Page.createSlideOut.querySelector('lvl-form')
				let url = `/Api/DataSources/@Model.DataSourceId/Elements`
				if (elementPreview?.hasAttribute('fileUploadId'))
					url+= '?fileUploadId='+elementPreview.getAttribute('fileUploadId')
				const result = await Form.storeData(createForm, url, 'POST', true, I18n.translate("elementIsSaved"))
				if (result && !result.error) {
					Page.createSlideOut.open = false
					document.querySelector('.page__content lvl-multi-data-view')?.reload()
				}
			}
		</text>
	}
</script>