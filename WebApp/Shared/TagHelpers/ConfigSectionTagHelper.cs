using System.Diagnostics.CodeAnalysis;
using Google.Api.Gax;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// Section within a page
/// </summary>
[ExcludeFromCodeCoverage]
public class ConfigSectionTagHelper : TagHelper
{
	/// <summary>
	/// Heading required to separate sections from each other  
	/// </summary>
	public string? Label { get; set; }
	
	/// <summary>
	/// Little Text under the heading to describe the section
	/// </summary>
	public string? Description { get; set; }

	/// <summary>
	/// Should collapsing the section be allowed?
	/// </summary>
	public bool AllowCollapse { get; set; } = true;

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-section";
		if (AllowCollapse)
			output.Attributes.SetAttribute("allow-collapse", "");
		
		if (!string.IsNullOrEmpty(Label))
			output.Attributes.SetAttribute("heading", Label);
		
		if (!string.IsNullOrEmpty(Description))
			output.Attributes.SetAttribute("subtitle", Description);
		
		var content = output.GetChildContentAsync().ResultWithUnwrappedExceptions();
		output.Content.SetHtmlContent($"<div class='form__section'>{content.GetContent()}</div>");
	}
}