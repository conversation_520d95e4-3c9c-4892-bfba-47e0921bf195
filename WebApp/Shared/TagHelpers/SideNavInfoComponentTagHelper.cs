using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-side-nav-info
/// </summary>
public class SideNavInfoComponentTagHelper : TagHelper
{
	/// <summary>
	/// Displayed text of a nav info
	/// </summary>
	public string? Label { get; set; }

	/// <summary>
	/// Mark item as loading
	/// </summary>
	public bool Skeleton { get; set; } = false;

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-side-nav-info";

		if (Label != null)
			output.Attributes.SetAttribute("label", Label);
		if (Skeleton)
			output.Attributes.SetAttribute("skeleton", "");

		output.Attributes.SetAttribute("slot", "info");

		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}
	}
}