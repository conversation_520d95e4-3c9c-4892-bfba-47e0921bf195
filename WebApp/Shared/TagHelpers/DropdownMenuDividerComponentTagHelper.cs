using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions;
using Microsoft.AspNetCore.Razor.TagHelpers;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-menu-divider
/// </summary>
public class DropdownMenuDividerComponentTagHelper : TagHelper
{
	/// <summary>
	/// Optional label which is used as a headline for the subsequent menu items
	/// </summary>
	public string? Label { get; set; }

	/// <summary>
	/// Should there be a specific spacing before the divider starts?
	/// </summary>
	public int? Spacing { get; set; } = 0;

	/// <summary>
	/// Should the border be displayed on top or below the label (if at all)?
	/// </summary>
	public BorderPosition? BorderPosition { get; set; }

	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-menu-divider";

		if (Label != null)
			output.Attributes.SetAttribute("label", Label);
		if (Spacing != null)
			output.Attributes.SetAttribute("spacing", Spacing);
		if (BorderPosition != null)
			output.Attributes.SetAttribute("border", BorderPosition.Value.GetPlacementAsString());

		var extraAttributes = this.GetAttributeList(context);
		foreach (var attribute in extraAttributes)
		{
			output.Attributes.SetAttribute(attribute.Key, attribute.Value);
		}
	}
}