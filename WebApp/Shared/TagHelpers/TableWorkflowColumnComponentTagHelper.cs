using Microsoft.AspNetCore.Razor.TagHelpers;
// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Levelbuild.Frontend.WebApp.Shared.TagHelpers;

/// <summary>
/// TagHelper for lvl-table-workflow-column
/// </summary>
public class TableWorkflowColumnComponentTagHelper : BaseColumnComponentTagHelper
{
	/// <summary>
	/// Standard TagHelper Process method
	/// </summary>
	/// <param name="context"></param>
	/// <param name="output"></param>
	public override void Process(TagHelperContext context, TagHelperOutput output)
	{
		output.TagName = "lvl-table-workflow-column";

		ProcessBaseOutput(output, context);
	}
}