using System.Globalization;
using Microsoft.AspNetCore.Localization;
using Serilog;
using ILogger = Serilog.ILogger;

namespace Levelbuild.Frontend.WebApp.Shared.Localization;

/// <summary>
/// Provides Culture based on current request (and later based on logged-in user)
/// </summary>
public class CustomCultureProvider : RequestCultureProvider
{
	private readonly RequestLocalizationOptions _localizationOptions;
	private readonly object _locker = new();
	private readonly ILogger _logger = Log.ForContext<CustomCultureProvider>();

	/// <summary>
	/// Inject RequestLocalizationOptions to manipulate later on
	/// </summary>
	/// <param name="localizationOptions"></param>
	public CustomCultureProvider(RequestLocalizationOptions localizationOptions)
		=> _localizationOptions = localizationOptions;

	/// <summary>
	/// Determine culture based on current request
	/// </summary>
	/// <param name="httpContext"></param>
	/// <returns></returns>
	public override Task<ProviderCultureResult?> DetermineProviderCultureResult(HttpContext httpContext)
	{
		CultureInfo culture = GetCulture(httpContext);

		lock (_locker)
		{
			// SupportedCultures/SupportedUICultures may be null
			_localizationOptions.SupportedCultures ??= new List<CultureInfo>();

			_localizationOptions.SupportedUICultures ??= new List<CultureInfo>();

			// Add culture to supported cultures if not already present
			if (!_localizationOptions.SupportedCultures.Contains(culture))
			{
				_localizationOptions.SupportedCultures.Add(culture);
				_localizationOptions.SupportedUICultures.Add(culture);
			}
		}

		return Task.FromResult(new ProviderCultureResult(culture.Name))!;
	}

	private CultureInfo GetCulture(HttpContext httpContext)
	{
		// Todo: load configured culture from User?
		var requestLanguage = httpContext.Request.GetTypedHeaders().AcceptLanguage.MaxBy(x => x.Quality ?? 1)?.Value.Value ?? "en-EN";
		
		// try to parse the string into a culture
		CultureInfo cultureInfo;
		try
		{
			cultureInfo = new CultureInfo(requestLanguage);
		}
		catch (CultureNotFoundException)
		{
			cultureInfo = new CultureInfo("en-EN");
			_logger.Information("unable to parse request language string {Language} to CultureInfo", requestLanguage);
		}

		return cultureInfo;
	}
}