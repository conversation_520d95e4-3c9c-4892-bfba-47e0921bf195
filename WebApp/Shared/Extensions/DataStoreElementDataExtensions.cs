using FluentMigrator.Infrastructure.Extensions;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Frontend.WebApp.Shared.Utils;

namespace Levelbuild.Frontend.WebApp.Shared.Extensions;

/// <summary>
/// Extension methods for <see cref="DataStoreElementData"/>.
/// </summary>
public static class DataStoreElementDataExtensions
{
	/// <summary>
	/// Parses incoming <see cref="DataStoreElementData"/> values to their supposed type and removes unknown entries.
	/// </summary>
	/// <param name="elementData"></param>
	/// <param name="fields"></param>
	/// <exception cref="ArgumentException"></exception>
	public static void PrepareValuesForDataStore(this DataStoreElementData elementData, ICollection<DataFieldEntity> fields)
	{
		var originalValues = elementData.Values.Clone();

		foreach (var entry in originalValues)
		{
			var field = fields.FirstOrDefault(dataField => string.Equals(dataField.Name, entry.Key, StringComparison.CurrentCultureIgnoreCase));

			if (field == null)
				throw new ArgumentException($"Field '{entry.Key}' is not present in DataSource");

			if (field.Multi)
				elementData.Values[entry.Key] = DataStoreUtils.ParseMultiDataValues(entry.Value, field.Type, DataStoreUtils.ParseRequestElementDataValue);
			else
				elementData.Values[entry.Key] = DataStoreUtils.ParseRequestElementDataValue(entry.Value, field.Type);
		}
	}
}