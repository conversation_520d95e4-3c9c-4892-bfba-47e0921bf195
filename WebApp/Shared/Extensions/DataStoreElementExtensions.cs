using FluentMigrator.Infrastructure.Extensions;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Frontend.WebApp.Shared.Constants;
using Levelbuild.Frontend.WebApp.Shared.Utils;

namespace Levelbuild.Frontend.WebApp.Shared.Extensions;

/// <summary>
/// Extension methods for see <see cref="DataStoreElement"/>.
/// </summary>
public static class DataStoreElementExtensions
{
	/// <summary>
	/// Combines outgoing <see cref="DataStoreElement"/> values that are split into name / name.Id and name.Display.
	/// </summary>
	/// <param name="element"></param>
	/// <param name="additionalQueryFields"></param>
	/// <exception cref="ArgumentException"></exception>
	public static void ParseCompositeValuesForResponse(this DataStoreElement element, List<DataStoreQueryField>? additionalQueryFields = null)
	{
		if (additionalQueryFields == null)
			return;
		
		var displayQueryFields = additionalQueryFields.Where(field => field.Alias?.EndsWith(".Display", StringComparison.CurrentCultureIgnoreCase) == true)
			.ToList();
		
		foreach (var queryField in displayQueryFields)
		{
			var name = queryField.Alias!.Split(".")[0];

			if (element.Values.TryGetValue(name, out var value)) { }
			else if (element.Values.TryGetValue(name + ".Id", out value))
			{
				name += ".Id";
			}
			else
				continue;
			
			if (!element.Values.TryGetValue(queryField.Alias, out var displayValue))
				continue;
			
			element.Values.Remove(queryField.Alias!);
			element.Values.Remove(name);
			element.Values.Add(queryField.Alias!.Split(".")[0], value == null && displayValue == null ? null : new Dictionary<string, object> { { "Id", value! }, { "DisplayValue", displayValue! } });
		}
	}
	
	
	/// <summary>
	/// Parses outgoing <see cref="DataStoreElement"/> values for the API response.
	/// </summary>
	/// <param name="element"></param>
	/// <param name="fields"></param>
	/// <param name="logger"></param>
	/// <param name="additionalQueryFields"></param>
	/// <exception cref="ArgumentException"></exception>
	public static void PrepareValuesForResponse(this DataStoreElement element, ICollection<DataFieldEntity> fields, List<DataStoreQueryField>? additionalQueryFields = null)
	{
		var originalValues = element.Values.Clone();
		
		foreach (var entry in originalValues)
		{
			var field = fields.FirstOrDefault(dataField => string.Equals(dataField.Name, entry.Key, StringComparison.CurrentCultureIgnoreCase));
			
			if (field == null)
				continue;
			
			if (field.Multi)
				element.Values[entry.Key] = DataStoreUtils.ParseMultiDataValues(entry.Value, field.Type, ParseResponseElementDataValue);
			else
				element.Values[entry.Key] = ParseResponseElementDataValue(entry.Value, field.Type);
		}
	}
	
	private static object? ParseResponseElementDataValue(object? value, DataType dataType)
	{
		if (value == null)
			return null;
		
		switch (dataType)
		{
			case DataType.Date:
				return ((DateTime) value).ToString(DateTimeFormat.Date);
			case DataType.DateTimeFixed:
				return ((DateTime) value).ToString(DateTimeFormat.DateTimeFixed);
			case DataType.Time:
				if(value is DateTime time)
					return time.ToString(DateTimeFormat.Time);
				if(value is TimeSpan)
					return $"{value}Z";
				
				return value;
			case DataType.TimeFixed:
				if(value is DateTime timeFixed)
					return timeFixed.ToString(DateTimeFormat.TimeFixed);
				if(value is TimeSpan)
					return $"{value}";
				
				return value;
			default:
				return value;
		}
	}
}