namespace Levelbuild.Frontend.WebApp.Shared.ControllerDtos;

/// <summary>
/// Default Query Data Response from Server 
/// </summary>
public class FilterFieldQueryItemResultDto
{
	/// <summary>
	/// Data label
	/// </summary>
	public required string? Label { get; init; }
	
	/// <summary>
	/// Data value
	/// </summary>
	public required object Value { get; set; }

	/// <summary>
	/// Count of the grouped value
	/// </summary>
	public long? Count { get; set; }
}