using Levelbuild.Core.FrontendDtos.Shared;

namespace Levelbuild.Frontend.WebApp.Shared.ControllerDtos;

/// <summary>
/// Default Query Data Response from Server 
/// </summary>
/// <typeparam name="T"></typeparam>
public class QueryResultDto<T> : IResponseObject
{
	/// <summary>
	/// List of data
	/// </summary>
	public required IList<T> Rows { get; init; }

	/// <summary>
	/// Total count of data without limit and offset
	/// </summary>
	public int CountTotal { get; init; }
}