<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <RootNamespace>Levelbuild.Frontend.WebApp</RootNamespace>
        <Company>levelbuild AG</Company>

        <GenerateDocumentationFile>true</GenerateDocumentationFile>

        <ComponentFolder>$(ProjectDir)..\WebAppComponents/ClientApp/</ComponentFolder>
        <NodeJsInstallMessage>Please install NodeJS and npm globally (https://nodejs.org/en/download/package-manager/current)</NodeJsInstallMessage>
    </PropertyGroup>

    <ItemGroup>
        <ComponentFiles Include="$(ComponentFolder)src/**/*.ts" />
    </ItemGroup>

    <!-- Install node packages which are outdated or uninstalled  -->
    <!-- GITLAB_CIis an Environment Variable, which is only set in the Gitlab Pipeline or in the local.Dockerfile  -->
    <Target Name="NpmInstall" Inputs="package.json" Outputs="node_modules/.install-stamp" Label="Install npm packages" BeforeTargets="BeforeBuild" Condition="$(GITLAB_CI) != 'true'">
        <!-- Check that Node is installed -->
        <Exec Command="node --version" ContinueOnError="true"><Output PropertyName="NodeVersionExitCode" TaskParameter="ExitCode" /></Exec>
        <Error Text="$(NodeJsInstallMessage)" Condition="$(NodeVersionExitCode) != 0" />
        <Exec Command="npm ci" ContinueOnError="false" Condition="$(NodeVersionExitCode) == 0" />

        <!-- Write the stamp file, so incremental builds work -->
        <Touch Files="node_modules/.install-stamp" AlwaysCreate="true" />
    </Target>

    <!-- Install node packages in webcomponents folder which are outdated or uninstalled  -->
    <Target Name="Components:NpmInstall" Inputs="$(ComponentFolder)package.json" Outputs="$(ComponentFolder)node_modules/.install-stamp" Label="Install npm packages from web components" BeforeTargets="BeforeBuild" Condition="$(GITLAB_CI) != 'true'">
        <!-- Check that Node is installed -->
        <Exec Command="node --version" ContinueOnError="true"><Output PropertyName="NodeVersionExitCode" TaskParameter="ExitCode" /></Exec>
        <Error Text="$(NodeJsInstallMessage)" Condition="$(NodeVersionExitCode) != 0" />
        <Exec Command="npm ci" ContinueOnError="false" Condition="$(NodeVersionExitCode) == 0" WorkingDirectory="$(ProjectDir)..\WebAppComponents/ClientApp/" />

        <!-- Write the stamp file, so incremental builds work -->
        <Touch Files="$(ComponentFolder)node_modules/.install-stamp" AlwaysCreate="true" />
    </Target>

    <!-- for development purpose -->
    <ItemGroup>
        <Reference Include="DataStoreInterface">
            <HintPath>..\..\WebAppCore\DataStoreInterface\bin\Debug\net8.0\DataStoreInterface.dll</HintPath>
        </Reference>
        <Reference Include="EntityInterface">
            <HintPath>..\..\WebAppCore\EntityInterface\bin\Debug\net8.0\EntityInterface.dll</HintPath>
        </Reference>
        <Reference Include="FileInterface">
          <HintPath>..\..\WebAppCore\FileInterface\bin\Debug\net8.0\FileInterface.dll</HintPath>
        </Reference>
        <Reference Include="FrontendDtos">
            <HintPath>..\..\WebAppCore\FrontendDtos\bin\Debug\net8.0\FrontendDtos.dll</HintPath>
        </Reference>
        <Reference Include="SharedDtos">
            <HintPath>..\..\WebAppCore\SharedDtos\bin\Debug\net8.0\SharedDtos.dll</HintPath>
        </Reference>
        <Reference Include="SharedUtilities">
            <HintPath>..\..\WebAppCore\SharedUtilities\obj\Debug\net8.0\SharedUtilities.dll</HintPath>
        </Reference>
        <Reference Include="ZitadelApiInterface">
            <HintPath>..\..\WebAppCore\ZitadelApiInterface\bin\Debug\net8.0\ZitadelApiInterface.dll</HintPath>
        </Reference>
        <Reference Include="StorageInterface">
            <HintPath>..\..\WebAppCore\StorageInterface\bin\Debug\net8.0\StorageInterface.dll</HintPath>
        </Reference>
    </ItemGroup>


    <!-- for release purpose -->
    <ItemGroup Condition="'$(Configuration)'=='Release'">
        <PackageReference Include="levelbuild.DataStoreInterface" Version="$(LVL_RELEASE_VERSION)" />
        <PackageReference Include="levelbuild.EntityInterface" Version="$(LVL_RELEASE_VERSION)" />
        <PackageReference Include="levelbuild.FrontendDtos" Version="$(LVL_RELEASE_VERSION)" />
        <PackageReference Include="levelbuild.SharedDtos" Version="$(LVL_RELEASE_VERSION)" />
        <PackageReference Include="levelbuild.SharedUtilities" Version="$(LVL_RELEASE_VERSION)" />
        <PackageReference Include="levelbuild.ZitadelApiInterface" Version="$(LVL_RELEASE_VERSION)" />
        <PackageReference Include="levelbuild.StorageInterface" Version="$(LVL_RELEASE_VERSION)" />
    </ItemGroup>


    <!-- local solution reference -->
    <ItemGroup>
        <ProjectReference Include="..\Migrations\WebApp.Postgres\WebApp.Postgres.csproj" />
        <ProjectReference Include="..\Migrations\WebApp.SqlServer\WebApp.SqlServer.csproj" />
        <ProjectReference Include="..\Storage\Storage.csproj" />
        <ProjectReference Include="..\WebAppEntities\WebAppEntities.csproj" />
        <ProjectReference Include="..\Backends\DaguWebBackend\DaguWebBackend.csproj"/>
        <ProjectReference Include="..\ZitadelApi\ZitadelApi.csproj" />
    </ItemGroup>


    <!-- for publishing -->
    <ItemGroup>
        <Content Update="appsettings.*.json">
            <CopyToPublishDirectory>Never</CopyToPublishDirectory>
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Content>

        <Content Update="**/Translations/*.i18n.json">
            <CopyToPublishDirectory>Never</CopyToPublishDirectory>
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </Content>

        <Content Update="Client\public\css\site.css">
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        </Content>

        <Content Include="..\WebAppComponents\ClientApp\src\components\news\preview\NewsPreview.i18n.json">
          <Link>src\components\news\preview\NewsPreview.i18n.json</Link>
        </Content>

        <Content Include="Client/src/.modules/**">
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>

        <Content Include="wwwroot/.vite/**">
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>

        <Content Update="Features\News\Translations\NewsCategoryController.i18n.json">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
          <CopyToPublishDirectory>Never</CopyToPublishDirectory>
        </Content>

        <Content Update="Features\News\Translations\NewsController.i18n.json">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
          <CopyToPublishDirectory>Never</CopyToPublishDirectory>
        </Content>

        <Content Update="Features\News\Translations\NewsContentController.i18n.json">
          <CopyToOutputDirectory>Never</CopyToOutputDirectory>
          <CopyToPublishDirectory>Never</CopyToPublishDirectory>
        </Content>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="AspNetCore.HealthChecks.NpgSql" Version="8.0.1" />
        <PackageReference Include="AspNetCore.HealthChecks.Redis" Version="8.0.1" />
        <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="8.0.1" />
        <PackageReference Include="IdentityModel.AspNetCore.OAuth2Introspection" Version="6.2.0" />
        <PackageReference Include="Ionic.Zip" Version="*******" />
        <PackageReference Include="JetBrains.Annotations" Version="2024.3.0" />
        <PackageReference Include="Microsoft.AspNetCore.OData" Version="8.2.6" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.11">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.11" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.11" />
        <PackageReference Include="Microsoft.OData.Core" Version="7.21.6" />
        <PackageReference Include="Microsoft.OpenApi" Version="1.6.22" />
        <PackageReference Include="Microsoft.Spatial" Version="8.2.1" />
        <PackageReference Include="MimeKit" Version="4.9.0" />
        <PackageReference Include="MongoDB.Driver.Core.Extensions.DiagnosticSources" Version="2.0.0" />
        <PackageReference Include="MsgReader" Version="5.7.0" />
        <PackageReference Include="NGuid" Version="1.0.0" />
        <PackageReference Include="Npgsql.DependencyInjection" Version="8.0.6" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.11" />
        <PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.11.1" />
        <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.11.1" />
<!--        <PackageReference Include="OpenTelemetry.Exporter.Prometheus.AspNetCore" Version="1.11.0-beta.1" />-->
        <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.11.1" />
        <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.11.0" />
        <PackageReference Include="OpenTelemetry.Instrumentation.EntityFrameworkCore" Version="1.11.0-beta.1" />
        <PackageReference Include="OpenTelemetry.Instrumentation.GrpcNetClient" Version="1.11.0-beta.1" />
        <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.11.0" />
        <PackageReference Include="OpenTelemetry.Instrumentation.StackExchangeRedis" Version="1.11.0-beta.1" />
        <PackageReference Include="QRCoder" Version="1.6.0" />
        <PackageReference Include="Serilog" Version="4.1.0" />
        <PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
        <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
        <PackageReference Include="Serilog.Expressions" Version="5.0.0" />
        <PackageReference Include="Serilog.Extensions.Logging" Version="8.0.0" />
        <PackageReference Include="SharpCompress" Version="0.38.0" />
        <PackageReference Include="StackExchange.Redis" Version="2.8.31" />
        <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="7.0.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="8.0.2" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="7.0.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="7.0.0" />
        <PackageReference Include="System.Linq.Dynamic.Core" Version="1.6.0" />
        <PackageReference Include="Vite.AspNetCore" Version="2.1.2" />
        <PackageReference Include="ClosedXML" Version="0.105.0-rc" />
        <PackageReference Include="ExcelDataReader" Version="3.8.0-develop00474" />
        <PackageReference Include="ExcelDataReader.DataSet" Version="3.8.0-develop00474" />
        <PackageReference Include="System.Text.Encoding.CodePages" Version="8.0.0" />
        <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="9.0.0">
            <PrivateAssets>none</PrivateAssets>
        </PackageReference>
    </ItemGroup>


    <ItemGroup>
        <None Include="wwwroot\favicon.ico" />
        <None Remove="Client\public\page\services\progress-socket.js" />
    </ItemGroup>


    <ItemGroup>
      <Folder Include="Client\public\page\services\" />
    </ItemGroup>


    <ItemGroup>
      <TypeScriptCompile Include="wwwroot\progress-modal.ts" />
    </ItemGroup>
</Project>
