(function(){var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(v){var y=0;return function(){return y<v.length?{done:!1,value:v[y++]}:{done:!0}}};$jscomp.arrayIterator=function(v){return{next:$jscomp.arrayIteratorImpl(v)}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;$jscomp.FORCE_POLYFILL_PROMISE=!1;$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(v,y,u){if(v==Array.prototype||v==Object.prototype)return v;v[y]=u.value;return v};$jscomp.getGlobal=function(v){v=["object"==typeof globalThis&&globalThis,v,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var y=0;y<v.length;++y){var u=v[y];if(u&&u.Math==Math)return u}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(v,y,u){if(!u||null!=v){u=$jscomp.propertyToPolyfillSymbol[y];if(null==u)return v[y];u=v[u];return void 0!==u?u:v[y]}};
$jscomp.polyfill=function(v,y,u,r){y&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(v,y,u,r):$jscomp.polyfillUnisolated(v,y,u,r))};$jscomp.polyfillUnisolated=function(v,y,u,r){u=$jscomp.global;v=v.split(".");for(r=0;r<v.length-1;r++){var t=v[r];if(!(t in u))return;u=u[t]}v=v[v.length-1];r=u[v];y=y(r);y!=r&&null!=y&&$jscomp.defineProperty(u,v,{configurable:!0,writable:!0,value:y})};
$jscomp.polyfillIsolated=function(v,y,u,r){var t=v.split(".");v=1===t.length;r=t[0];r=!v&&r in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var x=0;x<t.length-1;x++){var l=t[x];if(!(l in r))return;r=r[l]}t=t[t.length-1];u=$jscomp.IS_SYMBOL_NATIVE&&"es6"===u?r[t]:null;y=y(u);null!=y&&(v?$jscomp.defineProperty($jscomp.polyfills,t,{configurable:!0,writable:!0,value:y}):y!==u&&(void 0===$jscomp.propertyToPolyfillSymbol[t]&&(u=1E9*Math.random()>>>0,$jscomp.propertyToPolyfillSymbol[t]=$jscomp.IS_SYMBOL_NATIVE?
$jscomp.global.Symbol(t):$jscomp.POLYFILL_PREFIX+u+"$"+t),$jscomp.defineProperty(r,$jscomp.propertyToPolyfillSymbol[t],{configurable:!0,writable:!0,value:y})))};$jscomp.initSymbol=function(){};
$jscomp.polyfill("Symbol",function(v){if(v)return v;var y=function(x,l){this.$jscomp$symbol$id_=x;$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:l})};y.prototype.toString=function(){return this.$jscomp$symbol$id_};var u="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",r=0,t=function(x){if(this instanceof t)throw new TypeError("Symbol is not a constructor");return new y(u+(x||"")+"_"+r++,x)};return t},"es6","es3");
$jscomp.polyfill("Symbol.iterator",function(v){if(v)return v;v=Symbol("Symbol.iterator");for(var y="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),u=0;u<y.length;u++){var r=$jscomp.global[y[u]];"function"===typeof r&&"function"!=typeof r.prototype[v]&&$jscomp.defineProperty(r.prototype,v,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}})}return v},"es6",
"es3");$jscomp.iteratorPrototype=function(v){v={next:v};v[Symbol.iterator]=function(){return this};return v};$jscomp.checkEs6ConformanceViaProxy=function(){try{var v={},y=Object.create(new $jscomp.global.Proxy(v,{get:function(u,r,t){return u==v&&"q"==r&&t==y}}));return!0===y.q}catch(u){return!1}};$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS=!1;$jscomp.ES6_CONFORMANCE=$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS&&$jscomp.checkEs6ConformanceViaProxy();
$jscomp.makeIterator=function(v){var y="undefined"!=typeof Symbol&&Symbol.iterator&&v[Symbol.iterator];if(y)return y.call(v);if("number"==typeof v.length)return $jscomp.arrayIterator(v);throw Error(String(v)+" is not an iterable or ArrayLike");};$jscomp.owns=function(v,y){return Object.prototype.hasOwnProperty.call(v,y)};
$jscomp.polyfill("WeakMap",function(v){function y(){if(!v||!Object.seal)return!1;try{var n=Object.seal({}),f=Object.seal({}),k=new v([[n,2],[f,3]]);if(2!=k.get(n)||3!=k.get(f))return!1;k.delete(n);k.set(f,4);return!k.has(n)&&4==k.get(f)}catch(g){return!1}}function u(){}function r(n){var f=typeof n;return"object"===f&&null!==n||"function"===f}function t(n){if(!$jscomp.owns(n,l)){var f=new u;$jscomp.defineProperty(n,l,{value:f})}}function x(n){if(!$jscomp.ISOLATE_POLYFILLS){var f=Object[n];f&&(Object[n]=
function(k){if(k instanceof u)return k;Object.isExtensible(k)&&t(k);return f(k)})}}if($jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS){if(v&&$jscomp.ES6_CONFORMANCE)return v}else if(y())return v;var l="$jscomp_hidden_"+Math.random();x("freeze");x("preventExtensions");x("seal");var d=0,h=function(n){this.id_=(d+=Math.random()+1).toString();if(n){n=$jscomp.makeIterator(n);for(var f;!(f=n.next()).done;)f=f.value,this.set(f[0],f[1])}};h.prototype.set=function(n,f){if(!r(n))throw Error("Invalid WeakMap key");
t(n);if(!$jscomp.owns(n,l))throw Error("WeakMap key fail: "+n);n[l][this.id_]=f;return this};h.prototype.get=function(n){return r(n)&&$jscomp.owns(n,l)?n[l][this.id_]:void 0};h.prototype.has=function(n){return r(n)&&$jscomp.owns(n,l)&&$jscomp.owns(n[l],this.id_)};h.prototype.delete=function(n){return r(n)&&$jscomp.owns(n,l)&&$jscomp.owns(n[l],this.id_)?delete n[l][this.id_]:!1};return h},"es6","es3");$jscomp.MapEntry=function(){};
$jscomp.polyfill("Map",function(v){function y(){if($jscomp.ASSUME_NO_NATIVE_MAP||!v||"function"!=typeof v||!v.prototype.entries||"function"!=typeof Object.seal)return!1;try{var h=Object.seal({x:4}),n=new v($jscomp.makeIterator([[h,"s"]]));if("s"!=n.get(h)||1!=n.size||n.get({x:4})||n.set({x:4},"t")!=n||2!=n.size)return!1;var f=n.entries(),k=f.next();if(k.done||k.value[0]!=h||"s"!=k.value[1])return!1;k=f.next();return k.done||4!=k.value[0].x||"t"!=k.value[1]||!f.next().done?!1:!0}catch(g){return!1}}
if($jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS){if(v&&$jscomp.ES6_CONFORMANCE)return v}else if(y())return v;var u=new WeakMap,r=function(h){this.data_={};this.head_=l();this.size=0;if(h){h=$jscomp.makeIterator(h);for(var n;!(n=h.next()).done;)n=n.value,this.set(n[0],n[1])}};r.prototype.set=function(h,n){h=0===h?0:h;var f=t(this,h);f.list||(f.list=this.data_[f.id]=[]);f.entry?f.entry.value=n:(f.entry={next:this.head_,previous:this.head_.previous,head:this.head_,key:h,value:n},f.list.push(f.entry),
this.head_.previous.next=f.entry,this.head_.previous=f.entry,this.size++);return this};r.prototype.delete=function(h){h=t(this,h);return h.entry&&h.list?(h.list.splice(h.index,1),h.list.length||delete this.data_[h.id],h.entry.previous.next=h.entry.next,h.entry.next.previous=h.entry.previous,h.entry.head=null,this.size--,!0):!1};r.prototype.clear=function(){this.data_={};this.head_=this.head_.previous=l();this.size=0};r.prototype.has=function(h){return!!t(this,h).entry};r.prototype.get=function(h){return(h=
t(this,h).entry)&&h.value};r.prototype.entries=function(){return x(this,function(h){return[h.key,h.value]})};r.prototype.keys=function(){return x(this,function(h){return h.key})};r.prototype.values=function(){return x(this,function(h){return h.value})};r.prototype.forEach=function(h,n){for(var f=this.entries(),k;!(k=f.next()).done;)k=k.value,h.call(n,k[1],k[0],this)};r.prototype[Symbol.iterator]=r.prototype.entries;var t=function(h,n){var f=n&&typeof n;"object"==f||"function"==f?u.has(n)?f=u.get(n):
(f=""+ ++d,u.set(n,f)):f="p_"+n;var k=h.data_[f];if(k&&$jscomp.owns(h.data_,f))for(h=0;h<k.length;h++){var g=k[h];if(n!==n&&g.key!==g.key||n===g.key)return{id:f,list:k,index:h,entry:g}}return{id:f,list:k,index:-1,entry:void 0}},x=function(h,n){var f=h.head_;return $jscomp.iteratorPrototype(function(){if(f){for(;f.head!=h.head_;)f=f.previous;for(;f.next!=f.head;)return f=f.next,{done:!1,value:n(f)};f=null}return{done:!0,value:void 0}})},l=function(){var h={};return h.previous=h.next=h.head=h},d=0;
return r},"es6","es3");
$jscomp.polyfill("Promise",function(v){function y(){this.batch_=null}function u(l){return l instanceof t?l:new t(function(d,h){d(l)})}if(v&&(!($jscomp.FORCE_POLYFILL_PROMISE||$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION&&"undefined"===typeof $jscomp.global.PromiseRejectionEvent)||!$jscomp.global.Promise||-1===$jscomp.global.Promise.toString().indexOf("[native code]")))return v;y.prototype.asyncExecute=function(l){if(null==this.batch_){this.batch_=[];var d=this;this.asyncExecuteFunction(function(){d.executeBatch_()})}this.batch_.push(l)};
var r=$jscomp.global.setTimeout;y.prototype.asyncExecuteFunction=function(l){r(l,0)};y.prototype.executeBatch_=function(){for(;this.batch_&&this.batch_.length;){var l=this.batch_;this.batch_=[];for(var d=0;d<l.length;++d){var h=l[d];l[d]=null;try{h()}catch(n){this.asyncThrow_(n)}}}this.batch_=null};y.prototype.asyncThrow_=function(l){this.asyncExecuteFunction(function(){throw l;})};var t=function(l){this.state_=0;this.result_=void 0;this.onSettledCallbacks_=[];this.isRejectionHandled_=!1;var d=this.createResolveAndReject_();
try{l(d.resolve,d.reject)}catch(h){d.reject(h)}};t.prototype.createResolveAndReject_=function(){function l(n){return function(f){h||(h=!0,n.call(d,f))}}var d=this,h=!1;return{resolve:l(this.resolveTo_),reject:l(this.reject_)}};t.prototype.resolveTo_=function(l){if(l===this)this.reject_(new TypeError("A Promise cannot resolve to itself"));else if(l instanceof t)this.settleSameAsPromise_(l);else{a:switch(typeof l){case "object":var d=null!=l;break a;case "function":d=!0;break a;default:d=!1}d?this.resolveToNonPromiseObj_(l):
this.fulfill_(l)}};t.prototype.resolveToNonPromiseObj_=function(l){var d=void 0;try{d=l.then}catch(h){this.reject_(h);return}"function"==typeof d?this.settleSameAsThenable_(d,l):this.fulfill_(l)};t.prototype.reject_=function(l){this.settle_(2,l)};t.prototype.fulfill_=function(l){this.settle_(1,l)};t.prototype.settle_=function(l,d){if(0!=this.state_)throw Error("Cannot settle("+l+", "+d+"): Promise already settled in state"+this.state_);this.state_=l;this.result_=d;2===this.state_&&this.scheduleUnhandledRejectionCheck_();
this.executeOnSettledCallbacks_()};t.prototype.scheduleUnhandledRejectionCheck_=function(){var l=this;r(function(){if(l.notifyUnhandledRejection_()){var d=$jscomp.global.console;"undefined"!==typeof d&&d.error(l.result_)}},1)};t.prototype.notifyUnhandledRejection_=function(){if(this.isRejectionHandled_)return!1;var l=$jscomp.global.CustomEvent,d=$jscomp.global.Event,h=$jscomp.global.dispatchEvent;if("undefined"===typeof h)return!0;"function"===typeof l?l=new l("unhandledrejection",{cancelable:!0}):
"function"===typeof d?l=new d("unhandledrejection",{cancelable:!0}):(l=$jscomp.global.document.createEvent("CustomEvent"),l.initCustomEvent("unhandledrejection",!1,!0,l));l.promise=this;l.reason=this.result_;return h(l)};t.prototype.executeOnSettledCallbacks_=function(){if(null!=this.onSettledCallbacks_){for(var l=0;l<this.onSettledCallbacks_.length;++l)x.asyncExecute(this.onSettledCallbacks_[l]);this.onSettledCallbacks_=null}};var x=new y;t.prototype.settleSameAsPromise_=function(l){var d=this.createResolveAndReject_();
l.callWhenSettled_(d.resolve,d.reject)};t.prototype.settleSameAsThenable_=function(l,d){var h=this.createResolveAndReject_();try{l.call(d,h.resolve,h.reject)}catch(n){h.reject(n)}};t.prototype.then=function(l,d){function h(g,z){return"function"==typeof g?function(q){try{n(g(q))}catch(p){f(p)}}:z}var n,f,k=new t(function(g,z){n=g;f=z});this.callWhenSettled_(h(l,n),h(d,f));return k};t.prototype.catch=function(l){return this.then(void 0,l)};t.prototype.callWhenSettled_=function(l,d){function h(){switch(n.state_){case 1:l(n.result_);
break;case 2:d(n.result_);break;default:throw Error("Unexpected state: "+n.state_);}}var n=this;null==this.onSettledCallbacks_?x.asyncExecute(h):this.onSettledCallbacks_.push(h);this.isRejectionHandled_=!0};t.resolve=u;t.reject=function(l){return new t(function(d,h){h(l)})};t.race=function(l){return new t(function(d,h){for(var n=$jscomp.makeIterator(l),f=n.next();!f.done;f=n.next())u(f.value).callWhenSettled_(d,h)})};t.all=function(l){var d=$jscomp.makeIterator(l),h=d.next();return h.done?u([]):new t(function(n,
f){function k(q){return function(p){g[q]=p;z--;0==z&&n(g)}}var g=[],z=0;do g.push(void 0),z++,u(h.value).callWhenSettled_(k(g.length-1),f),h=d.next();while(!h.done)})};return t},"es6","es3");
$jscomp.polyfill("Array.from",function(v){return v?v:function(y,u,r){u=null!=u?u:function(d){return d};var t=[],x="undefined"!=typeof Symbol&&Symbol.iterator&&y[Symbol.iterator];if("function"==typeof x){y=x.call(y);for(var l=0;!(x=y.next()).done;)t.push(u.call(r,x.value,l++))}else for(x=y.length,l=0;l<x;l++)t.push(u.call(r,y[l],l));return t}},"es6","es3");
$jscomp.checkStringArgs=function(v,y,u){if(null==v)throw new TypeError("The 'this' value for String.prototype."+u+" must not be null or undefined");if(y instanceof RegExp)throw new TypeError("First argument to String.prototype."+u+" must not be a regular expression");return v+""};
$jscomp.polyfill("String.prototype.endsWith",function(v){return v?v:function(y,u){var r=$jscomp.checkStringArgs(this,y,"endsWith");y+="";void 0===u&&(u=r.length);u=Math.max(0,Math.min(u|0,r.length));for(var t=y.length;0<t&&0<u;)if(r[--u]!=y[--t])return!1;return 0>=t}},"es6","es3");$jscomp.findInternal=function(v,y,u){v instanceof String&&(v=String(v));for(var r=v.length,t=0;t<r;t++){var x=v[t];if(y.call(u,x,t,v))return{i:t,v:x}}return{i:-1,v:void 0}};
$jscomp.polyfill("Array.prototype.find",function(v){return v?v:function(y,u){return $jscomp.findInternal(this,y,u).v}},"es6","es3");$jscomp.underscoreProtoCanBeSet=function(){var v={a:!0},y={};try{return y.__proto__=v,y.a}catch(u){}return!1};$jscomp.setPrototypeOf=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.setPrototypeOf?Object.setPrototypeOf:$jscomp.underscoreProtoCanBeSet()?function(v,y){v.__proto__=y;if(v.__proto__!==y)throw new TypeError(v+" is not extensible");return v}:null;
$jscomp.assign=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.assign?Object.assign:function(v,y){for(var u=1;u<arguments.length;u++){var r=arguments[u];if(r)for(var t in r)$jscomp.owns(r,t)&&(v[t]=r[t])}return v};
$jscomp.polyfill("Set",function(v){function y(){if($jscomp.ASSUME_NO_NATIVE_SET||!v||"function"!=typeof v||!v.prototype.entries||"function"!=typeof Object.seal)return!1;try{var r=Object.seal({x:4}),t=new v($jscomp.makeIterator([r]));if(!t.has(r)||1!=t.size||t.add(r)!=t||1!=t.size||t.add({x:4})!=t||2!=t.size)return!1;var x=t.entries(),l=x.next();if(l.done||l.value[0]!=r||l.value[1]!=r)return!1;l=x.next();return l.done||l.value[0]==r||4!=l.value[0].x||l.value[1]!=l.value[0]?!1:x.next().done}catch(d){return!1}}
if($jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS){if(v&&$jscomp.ES6_CONFORMANCE)return v}else if(y())return v;var u=function(r){this.map_=new Map;if(r){r=$jscomp.makeIterator(r);for(var t;!(t=r.next()).done;)this.add(t.value)}this.size=this.map_.size};u.prototype.add=function(r){r=0===r?0:r;this.map_.set(r,r);this.size=this.map_.size;return this};u.prototype.delete=function(r){r=this.map_.delete(r);this.size=this.map_.size;return r};u.prototype.clear=function(){this.map_.clear();this.size=0};u.prototype.has=
function(r){return this.map_.has(r)};u.prototype.entries=function(){return this.map_.entries()};u.prototype.values=function(){return this.map_.values()};u.prototype.keys=u.prototype.values;u.prototype[Symbol.iterator]=u.prototype.values;u.prototype.forEach=function(r,t){var x=this;this.map_.forEach(function(l){return r.call(t,l,l,x)})};return u},"es6","es3");
$jscomp.iteratorFromArray=function(v,y){v instanceof String&&(v+="");var u=0,r=!1,t={next:function(){if(!r&&u<v.length){var x=u++;return{value:y(x,v[x]),done:!1}}r=!0;return{done:!0,value:void 0}}};t[Symbol.iterator]=function(){return t};return t};$jscomp.polyfill("Array.prototype.keys",function(v){return v?v:function(){return $jscomp.iteratorFromArray(this,function(y){return y})}},"es6","es3");
(function(v){function y(r){if(u[r])return u[r].exports;var t=u[r]={i:r,l:!1,exports:{}};v[r].call(t.exports,t,t.exports,y);t.l=!0;return t.exports}var u={};y.m=v;y.c=u;y.d=function(r,t,x){y.o(r,t)||Object.defineProperty(r,t,{enumerable:!0,get:x})};y.r=function(r){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"});Object.defineProperty(r,"__esModule",{value:!0})};y.t=function(r,t){t&1&&(r=y(r));if(t&8||t&4&&"object"===typeof r&&r&&r.__esModule)return r;
var x=Object.create(null);y.r(x);Object.defineProperty(x,"default",{enumerable:!0,value:r});if(t&2&&"string"!=typeof r)for(var l in r)y.d(x,l,function(d){return r[d]}.bind(null,l));return x};y.n=function(r){var t=r&&r.__esModule?function(){return r["default"]}:function(){return r};y.d(t,"a",t);return t};y.o=function(r,t){return Object.prototype.hasOwnProperty.call(r,t)};y.p="/core/pdf/";return y(y.s=19)})([function(v,y,u){u.d(y,"d",function(){return x});u.d(y,"e",function(){return t});u.d(y,"c",function(){return l});
u.d(y,"a",function(){return d});u.d(y,"b",function(){return h});var r=u(2),t=function(n,f){Object(r.a)("disableLogs")||(f?console.warn("".concat(n,": ").concat(f)):console.warn(n))},x=function(n,f){Object(r.a)("disableLogs")||(f?console.log("".concat(n,": ").concat(f)):console.log(n))},l=function(n){if(!Object(r.a)("disableLogs"))throw console.error(n),Error(n);},d=function(n,f){},h=function(n,f){}},function(v,y,u){u.d(y,"c",function(){return h});u.d(y,"a",function(){return n});u.d(y,"b",function(){return f});
u.d(y,"d",function(){return k});var r=u(14),t=console.log,x=console.warn,l=console.error,d=function(g){void 0===g&&(g=!0);g?(console.log=function(){},console.warn=function(){},console.error=function(){}):(console.log=t,console.warn=x,console.error=l)},h=function(){var g=Object(r.a)(location.search);d("1"===g.disableLogs)},n=function(g){g.on("disableLogs",function(z){d(z.disabled)})},f=function(g,z){return function(){}},k=function(g,z){z?console.warn("".concat(g,": ").concat(z)):console.warn(g)}},
function(v,y,u){u.d(y,"a",function(){return x});u.d(y,"b",function(){return l});var r={},t={flattenedResources:!1,CANVAS_CACHE_SIZE:void 0,maxPagesBefore:void 0,maxPagesAhead:void 0,disableLogs:!1,wvsQueryParameters:{},_trnDebugMode:!1,_logFiltersEnabled:null},x=function(d){return t[d]},l=function(d,h){var n;t[d]=h;null===(n=r[d])||void 0===n?void 0:n.forEach(function(f){f(h)})}},function(v,y,u){u.d(y,"a",function(){return E});u.d(y,"b",function(){return I});var r=u(11),t=u(0),x=u(8),l=u(4),d="undefined"===
typeof window?self:window,h=d.importScripts,n=!1,f=function(a,b){n||(h("".concat(d.basePath,"decode.min.js")),n=!0);a=Object(l.b)(a);a=self.BrotliDecode(a);return b?a:Object(l.a)(a)},k=function(a,b){return Object(r.a)(void 0,void 0,Promise,function(){var c;return Object(r.b)(this,function(m){switch(m.label){case 0:return n?[3,2]:[4,Object(x.a)("".concat(self.Core.getWorkerPath(),"external/decode.min.js"),"Failed to download decode.min.js",window)];case 1:m.sent(),n=!0,m.label=2;case 2:return c=self.BrotliDecode(Object(l.b)(a)),
[2,b?c:Object(l.a)(c)]}})})};(function(){function a(){this.remainingDataArrays=[]}a.prototype.processRaw=function(b){return b};a.prototype.processBrotli=function(b){this.remainingDataArrays.push(b);return null};a.prototype.GetNextChunk=function(b){this.decodeFunction||(this.decodeFunction=0===b[0]&&97===b[1]&&115===b[2]&&109===b[3]?this.processRaw:this.processBrotli);return this.decodeFunction(b)};a.prototype.End=function(){if(this.remainingDataArrays.length){for(var b=this.arrays,c=0,m=0;m<b.length;++m)c+=
b[m].length;c=new Uint8Array(c);var e=0;for(m=0;m<b.length;++m){var w=b[m];c.set(w,e);e+=w.length}return f(c,!0)}return null};return a})();var g=function(a,b,c){void 0===b&&(b=!0);void 0===c&&(c=!1);var m=new XMLHttpRequest;m.open("GET",a,b);a=c&&m.overrideMimeType;m.responseType=a?"text":"arraybuffer";a&&m.overrideMimeType("text/plain; charset=x-user-defined");return m},z=function(a,b,c){return new Promise(function(m,e){var w=g(a,b,c);w.send();w.onload=function(){200===this.status||0===this.status?
m(w.response):e(Error("Download Failed ".concat(a)))};w.onerror=function(){e(Error("Network error occurred ".concat(a)))}})},q=function(a,b){var c=b.decompressFunction,m=b.shouldOutputArray,e=b.compressedMaximum,w="undefined"!==typeof h?Date.now():null;try{var D=m?p(a):a.join("");Object(t.a)("worker","Result length is ".concat(D.length));if(D.length<e){var B=c(D,m);Object(t.e)("There may be some degradation of performance. Your server has not been configured to serve .gz. and .br. files with the expected Content-Encoding. See https://docs.apryse.com/documentation/web/faq/content-encoding/ for instructions on how to resolve this.");
h&&Object(t.a)("worker","Decompressed length is ".concat(B.length));D=B}else m||(D=Object(l.a)(D));if(h){var F=b.paths.join(", ");Object(t.a)("worker","".concat(F," Decompression took ").concat(Date.now()-w," ms"))}return D}catch(J){throw Error("Failed to decompress: ".concat(J));}},p=function(a){a=a.reduce(function(b,c){c=new Uint8Array(c);return b.concat(Array.from(c))},[]);return new Uint8Array(a)},A=function(a){var b=!a.shouldOutputArray,c=a.paths,m=a.isAsync;m?c=Promise.all(c.map(function(e){return z(e,
m,b)})).then(function(e){return q(e,a)}).catch(function(e){throw Error("Failed to fetch or decompress files: ".concat(e.message));}):(c=c.map(function(e){var w=g(e,m,b);w.send();if(200===w.status||0===w.status)return w.response;throw Error("Failed to load ".concat(e));}),c=q(c,a));return c},E=function(a){var b=a.lastIndexOf("/");-1===b&&(b=0);var c=a.slice(b).replace(".",".br.");h||(c.endsWith(".js.mem")?c=c.replace(".js.mem",".mem"):c.endsWith(".js")&&(c=c.concat(".mem")));return a.slice(0,b)+c},
C=function(a){return a.map(function(b){return E(b)})},G=function(a,b){b.decompressFunction=h?f:k;b.paths=C(a);return A(b)},H=function(a,b,c,m){return a.catch(function(e){Object(t.e)(e);return m(b,c)})},I=function(a,b,c,m){a=Array.isArray(a)?a:[a];a:{var e=[G];b={compressedMaximum:b,isAsync:c,shouldOutputArray:m};if(b.isAsync){var w=e[0](a,b);for(c=1;c<e.length;++c)w=H(w,a,b,e[c])}else{for(c=0;c<e.length;c++){m=e[c];try{w=m(a,b);break a}catch(D){Object(t.e)(D.message)}}throw Error("None of the worker files were able to load. ");
}}return w}},function(v,y,u){u.d(y,"b",function(){return r});u.d(y,"a",function(){return t});var r=function(x){if("string"===typeof x){for(var l=new Uint8Array(x.length),d=x.length,h=0;h<d;h++)l[h]=x.charCodeAt(h);return l}return x},t=function(x){if("string"!==typeof x){for(var l="",d=0,h=x.length,n;d<h;)n=x.subarray(d,d+1024),d+=1024,l+=String.fromCharCode.apply(null,n);return l}return x}},function(v,y,u){u.d(y,"a",function(){return q});var r,t="undefined"===typeof window?self:window;v=function(){var p=
navigator.userAgent.toLowerCase();return(p=/(msie) ([\w.]+)/.exec(p)||/(trident)(?:.*? rv:([\w.]+)|)/.exec(p))?parseInt(p[2],10):p}();var x=function(){var p=t.navigator.userAgent.match(/OPR/),A=t.navigator.userAgent.match(/Maxthon/),E=t.navigator.userAgent.match(/Edge/);return t.navigator.userAgent.match(/Chrome\/(.*?) /)&&!p&&!A&&!E}();(function(){if(!x)return null;var p=t.navigator.userAgent.match(/Chrome\/([0-9]+)\./);return p?parseInt(p[1],10):p})();var l=!!navigator.userAgent.match(/Edge/i)||
navigator.userAgent.match(/Edg\/(.*?)/)&&t.navigator.userAgent.match(/Chrome\/(.*?) /);(function(){if(!l)return null;var p=t.navigator.userAgent.match(/Edg\/([0-9]+)\./);return p?parseInt(p[1],10):p})();y=/iPad|iPhone|iPod/.test(t.navigator.platform)||"MacIntel"===navigator.platform&&1<navigator.maxTouchPoints||/iPad|iPhone|iPod/.test(t.navigator.userAgent);var d=function(){var p=t.navigator.userAgent.match(/.*\/([0-9\.]+)\s(Safari|Mobile).*/i);return p?parseFloat(p[1]):p}(),h=/^((?!chrome|android).)*safari/i.test(t.navigator.userAgent)||
/^((?!chrome|android).)*$/.test(t.navigator.userAgent)&&y;h&&/^((?!chrome|android).)*safari/i.test(navigator.userAgent)&&parseInt(null===(r=navigator.userAgent.match(/Version\/(\d+)/))||void 0===r?void 0:r[1],10);var n=t.navigator.userAgent.match(/Firefox/);(function(){if(!n)return null;var p=t.navigator.userAgent.match(/Firefox\/([0-9]+)\./);return p?parseInt(p[1],10):p})();v||/Android|webOS|Touch|IEMobile|Silk/i.test(navigator.userAgent);navigator.userAgent.match(/(iPad|iPhone|iPod)/i);t.navigator.userAgent.indexOf("Android");
var f=/Mac OS X 10_13_6.*\(KHTML, like Gecko\)$/.test(t.navigator.userAgent),k=t.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)?14<=parseInt(t.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)[3],10):!1,g=!(!self.WebAssembly||!self.WebAssembly.validate),z=-1<t.navigator.userAgent.indexOf("Edge/16")||-1<t.navigator.userAgent.indexOf("MSAppHost"),q=function(){return g&&!z&&!(!k&&(h&&14>d||f))}},function(v,y,u){function r(d){"@babel/helpers - typeof";return r="function"==
typeof Symbol&&"symbol"==typeof Symbol.iterator?function(h){return typeof h}:function(h){return h&&"function"==typeof Symbol&&h.constructor===Symbol&&h!==Symbol.prototype?"symbol":typeof h},r(d)}var t,x,l;!function(d){"object"===r(y)&&"undefined"!==typeof v?v.exports=d():!(x=[],t=d,l="function"===typeof t?t.apply(y,x):t,void 0!==l&&(v.exports=l))}(function(){return function k(h,n,f){function g(p,A){if(!n[p]){if(!h[p]){if(z)return z(p,!0);A=Error("Cannot find module '".concat(p,"'"));throw A.code=
"MODULE_NOT_FOUND",A;}A=n[p]={exports:{}};h[p][0].call(A.exports,function(E){return g(h[p][1][E]||E)},A,A.exports,k,h,n,f)}return n[p].exports}for(var z=!1,q=0;q<f.length;q++)g(f[q]);return g}({1:[function(h,n,f){var k={}.hasOwnProperty,g=function(z,q){function p(){this.constructor=z}for(var A in q)k.call(q,A)&&(z[A]=q[A]);p.prototype=q.prototype;z.prototype=new p;z.__super__=q.prototype;return z};f=h("./PriorityQueue/AbstractPriorityQueue");h=h("./PriorityQueue/ArrayStrategy");f=function(z){function q(p){p||
(p={});p.strategy||(p.strategy=BinaryHeapStrategy);p.comparator||(p.comparator=function(A,E){return(A||0)-(E||0)});q.__super__.constructor.call(this,p)}g(q,z);return q}(f);f.ArrayStrategy=h;n.exports=f},{"./PriorityQueue/AbstractPriorityQueue":2,"./PriorityQueue/ArrayStrategy":3}],2:[function(h,n,f){n.exports=function(){function k(g){if(null==(null!=g?g.strategy:void 0))throw"Must pass options.strategy, a strategy";if(null==(null!=g?g.comparator:void 0))throw"Must pass options.comparator, a comparator";
this.priv=new g.strategy(g);this.length=0}k.prototype.queue=function(g){this.length++;this.priv.queue(g)};k.prototype.dequeue=function(g){if(!this.length)throw"Empty queue";this.length--;return this.priv.dequeue()};k.prototype.peek=function(g){if(!this.length)throw"Empty queue";return this.priv.peek()};k.prototype.remove=function(g){this.priv.remove(g)&&--this.length};k.prototype.find=function(g){return 0<=this.priv.find(g)};k.prototype.removeAllMatching=function(g,z){g=this.priv.removeAllMatching(g,
z);this.length-=g};return k}()},{}],3:[function(h,n,f){var k=function(g,z,q){var p;var A=0;for(p=g.length;A<p;){var E=A+p>>>1;0<=q(g[E],z)?A=E+1:p=E}return A};n.exports=function(){function g(z){var q;this.options=z;this.comparator=this.options.comparator;this.data=(null!=(q=this.options.initialValues)?q.slice(0):void 0)||[];this.data.sort(this.comparator).reverse()}g.prototype.queue=function(z){var q=k(this.data,z,this.comparator);this.data.splice(q,0,z)};g.prototype.dequeue=function(){return this.data.pop()};
g.prototype.peek=function(){return this.data[this.data.length-1]};g.prototype.find=function(z){var q=k(this.data,z,this.comparator)-1;return 0<=q&&!this.comparator(this.data[q],z)?q:-1};g.prototype.remove=function(z){z=this.find(z);return 0<=z?(this.data.splice(z,1),!0):!1};g.prototype.removeAllMatching=function(z,q){for(var p=0,A=this.data.length-1;0<=A;--A)if(z(this.data[A])){var E=this.data.splice(A,1)[0];q&&q(E);++p}return p};return g}()},{}]},{},[1])(1)})},function(v,y,u){(function(r){function t(d,
h){this._id=d;this._clearFn=h}var x="undefined"!==typeof r&&r||"undefined"!==typeof self&&self||window,l=Function.prototype.apply;y.setTimeout=function(){return new t(l.call(setTimeout,x,arguments),clearTimeout)};y.setInterval=function(){return new t(l.call(setInterval,x,arguments),clearInterval)};y.clearTimeout=y.clearInterval=function(d){d&&d.close()};t.prototype.unref=t.prototype.ref=function(){};t.prototype.close=function(){this._clearFn.call(x,this._id)};y.enroll=function(d,h){clearTimeout(d._idleTimeoutId);
d._idleTimeout=h};y.unenroll=function(d){clearTimeout(d._idleTimeoutId);d._idleTimeout=-1};y._unrefActive=y.active=function(d){clearTimeout(d._idleTimeoutId);var h=d._idleTimeout;0<=h&&(d._idleTimeoutId=setTimeout(function(){d._onTimeout&&d._onTimeout()},h))};u(23);y.setImmediate="undefined"!==typeof self&&self.setImmediate||"undefined"!==typeof r&&r.setImmediate||this&&this.setImmediate;y.clearImmediate="undefined"!==typeof self&&self.clearImmediate||"undefined"!==typeof r&&r.clearImmediate||this&&
this.clearImmediate}).call(this,u(10))},function(v,y,u){function r(x,l,d){return new Promise(function(h){if(!x)return h();var n=d.document.createElement("script");n.type="text/javascript";n.onload=function(){h()};n.onerror=function(){l&&Object(t.e)(l);h()};n.src=x;d.document.getElementsByTagName("head")[0].appendChild(n)})}u.d(y,"a",function(){return r});var t=u(0)},function(v,y,u){function r(h,n,f,k){return t(h,n,f,k,!!WebAssembly.instantiateStreaming,void 0,void 0).then(function(g){Object(x.a)("load",
"WASM compilation took ".concat(Date.now()-NaN," ms"));return g})}function t(h,n,f,k,g,z,q){z=z||Date.now();if(g&&!k)return Object(x.a)("load","Try instantiateStreaming"),fetch(Object(l.a)(h)).then(function(p){return WebAssembly.instantiateStreaming(p,n)}).catch(function(p){Object(x.a)("load","instantiateStreaming Failed ".concat(h," message ").concat(p.message));return t(h,n,f,k,!1,z,q)});g=k?k.map(function(p,A){return"".concat(p,"PDFNetCWasm-chunk-").concat(A,".wasm")}):h;return Object(l.b)(g,f,
!0,!0).then(function(p){q=Date.now();Object(x.a)("load","Request took ".concat(q-z," ms"));return WebAssembly.instantiate(p,n)})}u.d(y,"a",function(){return r});var x=u(0),l=u(3),d=u(8);u.d(y,"b",function(){return d.a})},function(v,y){y=function(){return this}();try{y=y||(new Function("return this"))()}catch(u){"object"===typeof window&&(y=window)}v.exports=y},function(v,y,u){function r(x,l,d,h){function n(f){return f instanceof d?f:new d(function(k){k(f)})}return new (d||(d=Promise))(function(f,
k){function g(p){try{q(h.next(p))}catch(A){k(A)}}function z(p){try{q(h["throw"](p))}catch(A){k(A)}}function q(p){p.done?f(p.value):n(p.value).then(g,z)}q((h=h.apply(x,l||[])).next())})}function t(x,l){function d(q){return function(p){return h([q,p])}}function h(q){if(f)throw new TypeError("Generator is already executing.");for(;z&&(z=0,q[0]&&(n=0)),n;)try{if(f=1,k&&(g=q[0]&2?k["return"]:q[0]?k["throw"]||((g=k["return"])&&g.call(k),0):k.next)&&!(g=g.call(k,q[1])).done)return g;if(k=0,g)q=[q[0]&2,g.value];
switch(q[0]){case 0:case 1:g=q;break;case 4:return n.label++,{value:q[1],done:!1};case 5:n.label++;k=q[1];q=[0];continue;case 7:q=n.ops.pop();n.trys.pop();continue;default:if(!(g=n.trys,g=0<g.length&&g[g.length-1])&&(6===q[0]||2===q[0])){n=0;continue}if(3===q[0]&&(!g||q[1]>g[0]&&q[1]<g[3]))n.label=q[1];else if(6===q[0]&&n.label<g[1])n.label=g[1],g=q;else if(g&&n.label<g[2])n.label=g[2],n.ops.push(q);else{g[2]&&n.ops.pop();n.trys.pop();continue}}q=l.call(x,n)}catch(p){q=[6,p],k=0}finally{f=g=0}if(q[0]&
5)throw q[1];return{value:q[0]?q[1]:void 0,done:!0}}var n={label:0,sent:function(){if(g[0]&1)throw g[1];return g[1]},trys:[],ops:[]},f,k,g,z=Object.create(("function"===typeof Iterator?Iterator:Object).prototype);return z.next=d(0),z["throw"]=d(1),z["return"]=d(2),"function"===typeof Symbol&&(z[Symbol.iterator]=function(){return this}),z}u.d(y,"a",function(){return r});u.d(y,"b",function(){return t})},function(v,y){function u(){throw Error("setTimeout has not been defined");}function r(){throw Error("clearTimeout has not been defined");
}function t(A){if(f===setTimeout)return setTimeout(A,0);if((f===u||!f)&&setTimeout)return f=setTimeout,setTimeout(A,0);try{return f(A,0)}catch(E){try{return f.call(null,A,0)}catch(C){return f.call(this,A,0)}}}function x(A){if(k===clearTimeout)return clearTimeout(A);if((k===r||!k)&&clearTimeout)return k=clearTimeout,clearTimeout(A);try{return k(A)}catch(E){try{return k.call(null,A)}catch(C){return k.call(this,A)}}}function l(){z&&q&&(z=!1,q.length?g=q.concat(g):p=-1,g.length&&d())}function d(){if(!z){var A=
t(l);z=!0;for(var E=g.length;E;){q=g;for(g=[];++p<E;)q&&q[p].run();p=-1;E=g.length}q=null;z=!1;x(A)}}function h(A,E){this.fun=A;this.array=E}function n(){}v=v.exports={};try{var f="function"===typeof setTimeout?setTimeout:u}catch(A){f=u}try{var k="function"===typeof clearTimeout?clearTimeout:r}catch(A){k=r}var g=[],z=!1,q,p=-1;v.nextTick=function(A){var E=Array(arguments.length-1);if(1<arguments.length)for(var C=1;C<arguments.length;C++)E[C-1]=arguments[C];g.push(new h(A,E));1!==g.length||z||t(d)};
h.prototype.run=function(){this.fun.apply(null,this.array)};v.title="browser";v.browser=!0;v.env={};v.argv=[];v.version="";v.versions={};v.on=n;v.addListener=n;v.once=n;v.off=n;v.removeListener=n;v.removeAllListeners=n;v.emit=n;v.prependListener=n;v.prependOnceListener=n;v.listeners=function(A){return[]};v.binding=function(A){throw Error("process.binding is not supported");};v.cwd=function(){return"/"};v.chdir=function(A){throw Error("process.chdir is not supported");};v.umask=function(){return 0}},
function(v,y,u){y.a=function(){ArrayBuffer.prototype.slice||(ArrayBuffer.prototype.slice=function(r,t){void 0===r&&(r=0);void 0===t&&(t=this.byteLength);r=Math.floor(r);t=Math.floor(t);0>r&&(r+=this.byteLength);0>t&&(t+=this.byteLength);r=Math.min(Math.max(0,r),this.byteLength);t=Math.min(Math.max(0,t),this.byteLength);if(0>=t-r)return new ArrayBuffer(0);var x=new ArrayBuffer(t-r),l=new Uint8Array(x);r=new Uint8Array(this,r,t-r);l.set(r);return x})}},function(v,y,u){y.a=function(r){var t={};decodeURIComponent(r.slice(1)).split("&").forEach(function(x){x=
x.split("=",2);t[x[0]]=x[1]});return t}},function(v,y,u){(function(r){function t(G){"function"!==typeof G&&(G=new Function("".concat(G)));for(var H=Array(arguments.length-1),I=0;I<H.length;I++)H[I]=arguments[I+1];q[z]={callback:G,args:H};E(z);return z++}function x(G){delete q[G]}function l(G){if(p)setTimeout(l,0,G);else{var H=q[G];if(H){p=!0;try{var I=H.callback,a=H.args;switch(a.length){case 0:I();break;case 1:I(a[0]);break;case 2:I(a[0],a[1]);break;case 3:I(a[0],a[1],a[2]);break;default:I.apply(void 0,
a)}}finally{x(G),p=!1}}}}function d(){E=function(G){r.nextTick(function(){l(G)})}}function h(){if(g.postMessage&&!g.importScripts){var G=!0,H=g.onmessage;g.onmessage=function(){G=!1};g.postMessage("","*");g.onmessage=H;return G}}function n(){var G="setImmediate$".concat(Math.random(),"$"),H=function(I){I.source!==g&&I.source!==g.parent||"string"!==typeof I.data||0!==I.data.indexOf(G)||l(+I.data.slice(G.length))};g.addEventListener?g.addEventListener("message",H,!1):g.attachEvent("onmessage",H);E=
function(I){g.postMessage(G+I,"*")}}function f(){var G=A.documentElement;E=function(H){var I=A.createElement("script");I.onreadystatechange=function(){l(H);I.onreadystatechange=null;G.removeChild(I);I=null};G.appendChild(I)}}function k(){E=function(G){setTimeout(l,0,G)}}var g="undefined"===typeof window?self:window,z=1,q={},p=!1,A=g.document,E,C=Object.getPrototypeOf&&Object.getPrototypeOf(g);C=C&&C.setTimeout?C:g;"[object process]"==={}.toString.call(g.process)?d():h()?n():A&&"onreadystatechange"in
A.createElement("script")?f():k();C.setImmediate=t;C.clearImmediate=x;y.a={setImmediate:t,clearImmediate:x}}).call(this,u(12))},function(v,y,u){var r=u(0),t=u(2);v=function(){function x(l,d){this.name=l;this.comObj=d;this.callbackIndex=1;this.postMessageTransfers=!0;this.callbacksCapabilities={};this.actionHandler={};this.actionHandlerAsync={};this.pdfnetCommandChain=this.nextAsync=null;this.pdfnetActiveCommands=new Set;this.actionHandler.console_log=[function(h){Object(r.d)(h)}];this.actionHandler.console_error=
[function(h){Object(r.c)(h)}];this.actionHandler.workerLoaded=[function(){}];this.msgHandler=this.handleMessage.bind(this);d.addEventListener("message",this.msgHandler)}x.prototype.on=function(l,d,h){var n=this.actionHandler;n[l]&&Object(r.c)('There is already an actionName called "'.concat(l,'"'));n[l]=[d,h]};x.prototype.clearActionHandlers=function(){this.actionHandler={};this.comObj.removeEventListener("message",this.msgHandler)};x.prototype.reset=function(){this.clearActionHandlers();this.comObj.reset&&
this.comObj.reset()};x.prototype.replace=function(l,d,h){this.actionHandler[l]=[d,h]};x.prototype.onAsync=function(l,d,h){var n=this.actionHandlerAsync;n[l]&&Object(r.c)('There is already an actionName called "'.concat(l,'"'));n[l]=[d,h]};x.prototype.replaceAsync=function(l,d,h){var n=this.actionHandlerAsync,f=this.actionHandler;f[l]&&delete f[l];n[l]=[d,h]};x.prototype.onNextAsync=function(l){this.nextAsync=l};x.prototype.send=function(l,d){this.postMessage({action:l,data:d})};x.prototype.getNextId=
function(){return this.callbackIndex++};x.prototype.sendWithPromise=function(l,d,h){var n=this.getNextId();l={action:l,data:d,callbackId:n,priority:h};d=window.createPromiseCapability();this.callbacksCapabilities[n]=d;try{this.postMessage(l)}catch(f){d.reject(f)}return d.promise};x.prototype.sendWithPromiseReturnId=function(l,d,h){var n=this.getNextId();l={action:l,data:d,callbackId:n,priority:h};d=window.createPromiseCapability();this.callbacksCapabilities[n]=d;try{this.postMessage(l)}catch(f){d.reject(f)}return{promise:d.promise,
callbackId:n}};x.prototype.sendWithPromiseWithId=function(l,d,h){d>this.callbackIndex&&Object(r.c)("Can't reuse callbackId ".concat(d," lesser than callbackIndex ").concat(this.callbackIndex));d in this.callbacksCapabilities&&Object(r.c)("Can't reuse callbackId ".concat(d,". There is a capability waiting to be resolved. "));l={action:l,data:h,callbackId:d};h=window.createPromiseCapability();this.callbacksCapabilities[d]=h;try{this.postMessage(l)}catch(n){h.reject(n)}return h.promise};x.prototype.sendError=
function(l,d){if(l.message||l.errorData){l.message&&l.message.message&&(l.message=l.message.message);var h=l.errorData;l={type:l.type?l.type:"JavascriptError",message:l.message};h&&Object.keys(h).forEach(function(n){h.hasOwnProperty(n)&&(l[n]=h[n])})}this.postMessage({isReply:!0,callbackId:d,error:l})};x.prototype.getPromise=function(l){if(l in this.callbacksCapabilities)return this.callbacksCapabilities[l];Object(r.c)("Cannot get promise for callback ".concat(l))};x.prototype.cancelPromise=function(l){if(l in
this.callbacksCapabilities){var d=this.callbacksCapabilities[l];delete this.callbacksCapabilities[l];this.pdfnetActiveCommands.has(l)&&this.pdfnetActiveCommands.delete(l);d.reject({type:"Cancelled",message:"Request has been cancelled."});this.postMessage({action:"actionCancel",data:{callbackId:l}})}else Object(r.b)("Cannot cancel callback ".concat(l))};x.prototype.postMessage=function(l){"officeeditor"!==this.name&&Object(t.a)("enableWorkerLogs")&&Object(r.d)("PDFWorker","".concat(performance.now(),
" Sent ").concat(JSON.stringify(l)));if(this.postMessageTransfers){var d=this.getTransfersArray(l);this.comObj.postMessage(l,d)}else this.comObj.postMessage(l)};x.prototype.getObjectTransfers=function(l,d){var h=this;null!==l&&"object"===typeof l&&(l instanceof Uint8Array?d.push(l.buffer):l instanceof ArrayBuffer?d.push(l):Object.keys(l).forEach(function(n){l.hasOwnProperty(n)&&h.getObjectTransfers(l[n],d)}))};x.prototype.getTransfersArray=function(l){var d=[];this.getObjectTransfers(l,d);return 0===
d.length?void 0:d};x.prototype.handleMessage=function(l){var d=this,h=l.data;"officeeditor"!==this.name&&Object(t.a)("enableWorkerLogs")&&Object(r.d)("PDFWorker","".concat(performance.now()," Received ").concat(JSON.stringify(h)));var n=this.actionHandler,f=this.actionHandlerAsync;l=this.callbacksCapabilities;var k=this.pdfnetActiveCommands;if(h.isReply)n=h.callbackId,n in l?(f=l[n],delete l[n],k.has(n)&&k.delete(n),"error"in h?f.reject(h.error):f.resolve(h.data)):Object(r.a)("Cannot resolve callback ".concat(n));
else if(h.action in n){var g=n[h.action];h.callbackId?Promise.resolve().then(function(){return g[0].call(g[1],h.data)}).then(function(z){d.postMessage({isReply:!0,callbackId:h.callbackId,data:z})},function(z){d.sendError(z,h.callbackId)}):g[0].call(g[1],h.data)}else h.action in f?(g=f[h.action],h.callbackId?g[0].call(g[1],h).then(function(z){d.postMessage({isReply:!0,callbackId:h.callbackId,data:z});d.nextAsync()},function(z){d.sendError(z,h.callbackId);d.nextAsync()}):g[0].call(g[1],h).then(function(){d.nextAsync()},
function(){d.nextAsync()})):Object(r.c)("Unknown action from worker: ".concat(h.action))};return x}();y.a=v},function(v,y,u){u.d(y,"a",function(){return d});var r=u(3),t=u(9),x=u(5),l=function(){function h(n){var f=this;this.promise=n.then(function(k){f.response=k;f.status=200})}h.prototype.addEventListener=function(n,f){this.promise.then(f)};return h}(),d=function(h,n,f,k){if(Object(x.a)()&&!f){self.Module.instantiateWasm=function(z,q){return Object(t.a)("".concat(h,"Wasm.wasm"),z,n["Wasm.wasm"],
k).then(function(p){q(p.instance)})};if(n.disableObjectURLBlobs){importScripts("".concat(h,"Wasm.js"));return}f=Object(r.b)("".concat(h,"Wasm.js.mem"),n["Wasm.js.mem"],!1,!1)}else{if(n.disableObjectURLBlobs){importScripts("".concat((self.Module.asmjsPrefix?self.Module.asmjsPrefix:"")+h,".js"));return}f=Object(r.b)("".concat((self.Module.asmjsPrefix?self.Module.asmjsPrefix:"")+h,".js.mem"),n[".js.mem"],!1);var g=Object(r.b)("".concat((self.Module.memoryInitializerPrefixURL?self.Module.memoryInitializerPrefixURL:
"")+h,".mem"),n[".mem"],!0,!0);self.Module.memoryInitializerRequest=new l(g)}f=new Blob([f],{type:"application/javascript"});importScripts(URL.createObjectURL(f))}},function(v,y,u){u.d(y,"a",function(){return r});var r="optimized/"},function(v,y,u){v.exports=u(20)},function(v,y,u){u.r(y);u(5);v=u(13);u(21);u(22);u(25);u(26);u(27);u(28);u(29);Object(v.a)()},function(v,y,u){(function(r){"undefined"===typeof r.crypto&&(r.crypto={getRandomValues:function(t){for(var x=0;x<t.length;x++)t[x]=256*Math.random()}})})("undefined"===
typeof window?self:window)},function(v,y,u){(function(r,t){function x(l){"@babel/helpers - typeof";return x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(d){return typeof d}:function(d){return d&&"function"==typeof Symbol&&d.constructor===Symbol&&d!==Symbol.prototype?"symbol":typeof d},x(l)}(function(l){function d(){for(var B=0;B<w.length;B++)w[B][0](w[B][1]);w=[];D=!1}function h(B,F){w.push([B,F]);D||(D=!0,e(d,0))}function n(B,F){function J(K){g(F,K)}function L(K){q(F,K)}try{B(J,
L)}catch(K){L(K)}}function f(B){var F=B.owner,J=F.state_;F=F.data_;var L=B[J];B=B.then;if("function"===typeof L){J=b;try{F=L(F)}catch(K){q(B,K)}}k(B,F)||(J===b&&g(B,F),J===c&&q(B,F))}function k(B,F){var J;try{if(B===F)throw new TypeError("A promises callback cannot return that same promise.");if(F&&("function"===typeof F||"object"===x(F))){var L=F.then;if("function"===typeof L)return L.call(F,function(K){J||(J=!0,F!==K?g(B,K):z(B,K))},function(K){J||(J=!0,q(B,K))}),!0}}catch(K){return J||q(B,K),!0}return!1}
function g(B,F){B!==F&&k(B,F)||z(B,F)}function z(B,F){B.state_===I&&(B.state_=a,B.data_=F,h(A,B))}function q(B,F){B.state_===I&&(B.state_=a,B.data_=F,h(E,B))}function p(B){var F=B.then_;B.then_=void 0;for(B=0;B<F.length;B++)f(F[B])}function A(B){B.state_=b;p(B)}function E(B){B.state_=c;p(B)}function C(B){if("function"!==typeof B)throw new TypeError("Promise constructor takes a function argument");if(!(this instanceof C))throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.");
this.then_=[];n(B,this)}l.createPromiseCapability=function(){var B={};B.promise=new C(function(F,J){B.resolve=F;B.reject=J});return B};var G=l.Promise,H=G&&"resolve"in G&&"reject"in G&&"all"in G&&"race"in G&&function(){var B;new G(function(F){B=F});return"function"===typeof B}();"undefined"!==typeof exports&&exports?(exports.Promise=H?G:C,exports.Polyfill=C):"function"===typeof define&&u(24)?define(function(){return H?G:C}):H||(l.Promise=C);var I="pending",a="sealed",b="fulfilled",c="rejected",m=
function(){},e="undefined"!==typeof t?t:setTimeout,w=[],D;C.prototype={constructor:C,state_:I,then_:null,data_:void 0,then:function(B,F){B={owner:this,then:new this.constructor(m),fulfilled:B,rejected:F};this.state_===b||this.state_===c?h(f,B):this.then_.push(B);return B.then},"catch":function(B){return this.then(null,B)}};C.all=function(B){if("[object Array]"!==Object.prototype.toString.call(B))throw new TypeError("You must pass an array to Promise.all().");return new this(function(F,J){function L(R){N++;
return function(P){K[R]=P;--N||F(K)}}for(var K=[],N=0,M=0,Q;M<B.length;M++)(Q=B[M])&&"function"===typeof Q.then?Q.then(L(M),J):K[M]=Q;N||F(K)})};C.race=function(B){if("[object Array]"!==Object.prototype.toString.call(B))throw new TypeError("You must pass an array to Promise.race().");return new this(function(F,J){for(var L=0,K;L<B.length;L++)(K=B[L])&&"function"===typeof K.then?K.then(F,J):F(K)})};C.resolve=function(B){return B&&"object"===x(B)&&B.constructor===this?B:new this(function(F){F(B)})};
C.reject=function(B){return new this(function(F,J){J(B)})}})("undefined"!==typeof window?window:"undefined"!==typeof r?r:"undefined"!==typeof self?self:void 0)}).call(this,u(10),u(7).setImmediate)},function(v,y,u){(function(r,t){(function(x,l){function d(I){delete A[I]}function h(I){if(E)setTimeout(h,0,I);else{var a=A[I];if(a){E=!0;try{var b=a.callback,c=a.args;switch(c.length){case 0:b();break;case 1:b(c[0]);break;case 2:b(c[0],c[1]);break;case 3:b(c[0],c[1],c[2]);break;default:b.apply(l,c)}}finally{d(I),
E=!1}}}}function n(){G=function(I){t.nextTick(function(){h(I)})}}function f(){if(x.postMessage&&!x.importScripts){var I=!0,a=x.onmessage;x.onmessage=function(){I=!1};x.postMessage("","*");x.onmessage=a;return I}}function k(){var I="setImmediate$"+Math.random()+"$",a=function(b){b.source===x&&"string"===typeof b.data&&0===b.data.indexOf(I)&&h(+b.data.slice(I.length))};x.addEventListener?x.addEventListener("message",a,!1):x.attachEvent("onmessage",a);G=function(b){x.postMessage(I+b,"*")}}function g(){var I=
new MessageChannel;I.port1.onmessage=function(a){h(a.data)};G=function(a){I.port2.postMessage(a)}}function z(){var I=C.documentElement;G=function(a){var b=C.createElement("script");b.onreadystatechange=function(){h(a);b.onreadystatechange=null;I.removeChild(b);b=null};I.appendChild(b)}}function q(){G=function(I){setTimeout(h,0,I)}}if(!x.setImmediate){var p=1,A={},E=!1,C=x.document,G,H=Object.getPrototypeOf&&Object.getPrototypeOf(x);H=H&&H.setTimeout?H:x;"[object process]"==={}.toString.call(x.process)?
n():f()?k():x.MessageChannel?g():C&&"onreadystatechange"in C.createElement("script")?z():q();H.setImmediate=function(I){"function"!==typeof I&&(I=new Function(""+I));for(var a=Array(arguments.length-1),b=0;b<a.length;b++)a[b]=arguments[b+1];A[p]={callback:I,args:a};G(p);return p++};H.clearImmediate=d}})("undefined"===typeof self?"undefined"===typeof r?this:r:self)}).call(this,u(10),u(12))},function(v,y){v.exports={}},function(v,y,u){(function(r){var t=function(x,l){var d=function k(f){f=this["catch"](f);
return{cancel:l,promise:f,then:h.bind(f),"catch":k.bind(f)}},h=function z(k,g){k=this.then(k,g);return{cancel:l,promise:k,then:z.bind(k),"catch":d.bind(k)}};return{cancel:l,promise:x,then:h.bind(x),"catch":d.bind(x)}};r.CancellablePromise=function(x,l){var d=!1,h,n=new Promise(function(f,k){h=function(){d||(l(),k("cancelled"))};(new Promise(x)).then(function(g){d=!0;f(g)},function(g){d=!0;k(g)})});return t(n,h)};r.CancellablePromise.all=function(x){var l=Promise.all(x);return t(l,function(){x.forEach(function(d){d.cancel&&
d.cancel()})})}})("undefined"===typeof self?void 0:self)},function(v,y,u){(function(r,t){function x(n){"@babel/helpers - typeof";return x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(f){return typeof f}:function(f){return f&&"function"==typeof Symbol&&f.constructor===Symbol&&f!==Symbol.prototype?"symbol":typeof f},x(n)}function l(n,f){var k=Object.keys(n);if(Object.getOwnPropertySymbols){var g=Object.getOwnPropertySymbols(n);f&&(g=g.filter(function(z){return Object.getOwnPropertyDescriptor(n,
z).enumerable}));k.push.apply(k,g)}return k}function d(n){for(var f=1;f<arguments.length;f++){var k=null!=arguments[f]?arguments[f]:{};f%2?l(Object(k),!0).forEach(function(g){var z=k[g];a:if("object"===x(g)&&null!==g){var q=g[Symbol.toPrimitive];if(void 0!==q){g=q.call(g,"string");if("object"!==x(g))break a;throw new TypeError("@@toPrimitive must return a primitive value.");}g=String(g)}g="symbol"===x(g)?g:String(g);g in n?Object.defineProperty(n,g,{value:z,enumerable:!0,configurable:!0,writable:!0}):
n[g]=z}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(k)):l(Object(k)).forEach(function(g){Object.defineProperty(n,g,Object.getOwnPropertyDescriptor(k,g))})}return n}var h=u(1);(function(n){n.Module={INITIAL_MEMORY:50331648,noExitRuntime:!0,devicePixelRatio:1,cur_doc:null,cachePtrSize:0,hasBufOwnership:!0,loaded:!1,initCb:null,cachePtr:null,cleanupState:null,docs:{},postEvent:function(f,k,g){Module.workerMessageHandler.send("event",{docId:f,type:k,data:g})},
postProgressiveRenderingStartEvent:function(f,k){Module.postEvent(f,"progressiveRenderingStart",{pageNum:k})},postPagesUpdatedEvent:function(f,k,g,z){f={pageDimensions:Module.GetPageDimensions(f)};if(g)for(var q=0;q<g.length;++q)g[q]in f.pageDimensions?(f.pageDimensions[g[q]].contentChanged=!0,z&&(f.pageDimensions[g[q]].annotationsUnchanged=!0)):console.warn("Invalid Page Number ".concat(g[q]));Module.postEvent(k,"pagesUpdated",f);return f},postPagesRenamedEvent:function(f,k){var g={};f=Module.PDFDocGetPageIterator(f,
1);for(var z=1;Module.IteratorHasNext(f);++z){var q=Module.stackSave(),p=Module.IteratorCurrent(f);g[z]=Module.PageGetId(p);Module.IteratorNext(f);Module.stackRestore(q)}Module.postEvent(k,"pagesRenamed",{pageNumToId:g})},GetIndividualPageDimensions:function(f,k,g){f=Module.PageGetPageInfo(g);f.id=Module.PageGetId(g);return f},GetPageDimensionsRange:function(f,k,g){for(var z={},q=Module.PDFDocGetPageIterator(f,k);k<g&&Module.IteratorHasNext(q);++k){var p=Module.stackSave(),A=Module.IteratorCurrent(q);
z[k]=this.GetIndividualPageDimensions(f,k,A);Module.IteratorNext(q);Module.stackRestore(p)}return z},GetPageDimensionsContentChangedList:function(f,k){k.sort(function(G,H){return G-H});for(var g={},z=k[0],q=k[k.length-1],p=0,A=Module.PDFDocGetPageIterator(f,z);z<=q&&Module.IteratorHasNext(A);++z){if(k[p]==z){for(++p;k[p]==z;)++p;var E=Module.stackSave(),C=Module.IteratorCurrent(A);C=this.GetIndividualPageDimensions(f,z,C);C.contentChanged=!0;g[z]=C;Module.stackRestore(E)}Module.IteratorNext(A)}return g},
GetPageDimensions:function(f){try{var k=Module.stackSave();var g=Module.GetPageCount(f);if(0===g)throw"This document has no pages.";return Module.GetPageDimensionsRange(f,1,g+1)}finally{Module.stackRestore(k)}},loadDoc:function(f,k){"undefined"===typeof Module&&this._main();var g=null;try{var z=Module.stackSave();f.customHandlerId&&Module._TRN_PDFNetAddPDFTronCustomHandler(f.customHandlerId);k=Module.CreateDoc(f,k);var q=Module.GetDoc(k);if(Module.PDFDocInitSecurityHandler(q))return{docId:k,pageDimensions:Module.GetPageDimensions(q)};
g={type:"NeedsPassword",errorData:{docId:k},message:"This document requires a password"}}catch(p){g={type:"InvalidPDF",message:p}}finally{Module.stackRestore(z)}throw g;},loadCanvas:function(f,k,g,z,q,p,A,E){var C=Module.GetDoc(f),G=Module.docs[f].chunkStorage,H=Promise.resolve();if(G&&2===E.overprintMode){var I=Module.GetDownloadData(C);if(!I.docInfoRequested){var a=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_DownloaderPrefetchDocInfo(I.downloader,a));0==Module.getValue(a,"i8")&&(H=
I.docInfoPromiseCapability.promise);I.docInfoRequested=!0}}return new Promise(function(b,c){var m=k+1,e=function(){b(H.then(function(){return Module.RasterizePage(C,m,g,z,p,q,A,E,f)}))};if(G){var w=Module.GetDownloadData(C).downloader,D=G.getRequiredChunkOffsetArrays(w,m);G.keepChunks(D.have);w=function(){var B=G.getChunks(D.missing);Module.loadPromise=B.then(function(){var F=Module.loadPromise.cancelled;Module.loadPromise=null;F||e()})["catch"](function(F){"cancelled"!==F?c(F):Module.loadPromise=
null})};Module.loadPromise?Module.loadPromise.then(w,w):w()}else e()})},loadResources:function(f,k){Module.Initialize(k);Object(h.b)("worker","PDFNet initialized!");Module._TRN_PDFNetSetDefaultDiskCachingEnabled(!1);f=new Uint8Array(f);Module.PDFNetSetResourceData(f)},onRuntimeInitialized:function(){"undefined"===typeof Module&&(("undefined"!==typeof window?window:self).Module={});(function(f){f.PDFDocExportXFDF=function(k,g){k=Module.GetDoc(k);var z=Module.stackSave();try{var q=g?Module.PDFDocFDFExtract(k,
g):Module.PDFDocFDFExtract(k);var p=Module.FDFDocSaveAsXFDF(q);Module.stackRestore(z)}catch(A){throw Module.stackRestore(z),A;}return p};f.PageArrayToPageSet=function(k){var g=Module.stackSave();try{var z=Module.PageSetCreate();for(var q=0;q<k.length;++q)Module.PageSetAddPage(z,k[q]);Module.stackRestore(g)}catch(p){throw Module.stackRestore(g),p;}return z};f.cancelCurrent=function(){var k=Module.loadPromise;return k?(k.cancel(),k.cancelled=!0):(k=Module.cleanupState)?(r(k.timeout),k.cleanupArr.reverse().forEach(function(g){g()}),
Module.cleanupState=null,!0):!1};f.SetWorkerRestartCallback=function(k){Module.workerRestartCallback=k};f.XFDFMerge=function(k,g,z){if(g){var q=Module.GetDoc(k),p=[];try{Object(h.b)("worker","Merge XFDF of length ".concat(g.length));var A=Module.GetUStringFromJSString(g,!0);p.push(function(){Module.UStringDestroy(A)});var E=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_FDFDocCreateFromXFDF(A,E));var C=Module.getValue(E,"i8*");p.push(function(){Module.FDFDocDestroy(C)});var G=Module.PDFDocFDFUpdate(q,
C,z);G&&G.length&&Module.postEvent(k,"apRefChanged",{apRefChanges:G})}finally{p.reverse().forEach(function(H){H()})}}};f.MergeXFDF=function(k,g,z){return new Promise(function(q,p){var A=[];try{var E=Module.stackSave();A[A.length]=function(){Module.stackRestore(E)};Module.XFDFMerge(k,g,z);A.forEach(function(C){C()});q({})}catch(C){A.forEach(function(G){G()}),p(C)}})};f.CreateBufferFile=function(k,g,z){Module.MakeDev(k);var q=new ArrayBuffer(g);q=new Uint8Array(q);z=z?0:255;for(var p=0;p<g;++p)q[p]=
z;Module.docs[k]={buffer:q}};f.ReadBufferFile=function(k,g){var z=Module.docs[k].buffer;g&&(Module.docs[k].buffer=new Uint8Array(z.buffer.slice(0)));return z};f.RemoveBufferFile=function(k){Module.docs[k]=null};f.SaveHelper=function(k,g,z){z="undefined"===typeof z?2:z;Module.MakeDev(g);var q=Module._TRN_PDFDocSave(k,Module.GetUStringFromJSString(g),z);Module.docs[g].sink=null;REX(q);z&16&&Module.postPagesRenamedEvent(k,g);return Module.docs[g].buffer.buffer};f.SaveDoc=function(k,g,z,q,p,A,E,C,G){return new Promise(function(H,
I){var a=[];try{var b=Module.GetDoc(k),c=Module.stackSave();a[a.length]=function(){Module.stackRestore(c)};Module.XFDFMerge(k,g,E);var m=Module.allocate(8,"i8",Module.ALLOC_STACK),e=Module.allocate(Module.intArrayFromString('{"UseNonStandardRotation": true}'),"i8",Module.ALLOC_STACK);Module.setValue(m,e,"i8*");Module.setValue(m+4,0,"i32");Module._TRN_PDFDocRefreshAnnotAppearances(b,m);if(A){m=function(O){O=new Uint8Array(O);n.FS.writeFile("watermarkFile",O);O=Module.ImageCreateFromFile(b,Module.GetUStringFromJSString("watermarkFile"));
n.FS.unlink("watermarkFile");return O};var w=Module.ElementBuilderCreate();a.push(function(){Module.ElementBuilderDestroy(w)});var D=Module.ElementWriterCreate();a.push(function(){Module.ElementWriterDestroy(D)});try{if(!A.hasOwnProperty("default"))throw Error("Watermark dictionary has no 'default' key!");var B=m(A["default"]),F=Module.PDFDocGetPageIterator(b,1);for(e=-1;Module.IteratorHasNext(F);){var J=Module.IteratorCurrent(F);Module.IteratorNext(F);e++;var L=e.toString();try{var K=void 0;if(A.hasOwnProperty(L)){var N=
A[L];if(N)K=m(N);else continue}else K=B;var M=Module.PageGetPageInfo(J),Q=Module.ElementBuilderCreateImage(w,K,0,0,M.width,M.height);Module.ElementWriterBegin(D,J);Module.ElementWriterWritePlacedElement(D,Q);Module.ElementWriterEnd(D)}catch(O){console.warn("Watermark for page "+L+"can not be added due to error: "+O)}}}catch(O){console.warn("Watermarks can not be added due to error: "+O)}}if(C){var R=Module.SecurityHandlerCreate(G);R&&(Module.SecurityHandlerChangeUserPasswordUString(R,C),Module.PDFDocSetSecurityHandler(b,
R))}B=0;if(q){var P=Module.PDFDocGetRoot(b);(B=Module.ObjFindObj(P,"OpenAction"))&&Module.ObjPut(P,"__OpenActionBackup__",B);var S=Module.ObjPutDict(P,"OpenAction");Module.ObjPutName(S,"Type","Action");Module.ObjPutName(S,"S","JavaScript");Module.ObjPutString(S,"JS","this.print()")}var T=Module.SaveHelper(b,k,p);q&&(B?Module.ObjPut(P,"OpenAction",Module.ObjFindObj(P,"__OpenActionBackup__")):Module.ObjErase(P,"OpenAction"));a.reverse().forEach(function(O){O()});if(z)H({fileData:T});else{var U=T.slice(0);
H({fileData:U})}}catch(O){a.reverse().forEach(function(V){V()}),I(O)}})};f.SaveDocFromFixedElements=function(k,g,z,q,p,A){k=Module.PDFDocCreateFromLayoutEls(k);k=Module.CreateDoc({type:"ptr",value:k});return Module.SaveDoc(k,g,!0,!1,z,q,p,A)};f.GetCurrentCanvasData=function(k){var g=Module.currentRenderData;if(!g)return null;k&&REX(Module._TRN_PDFRasterizerUpdateBuffer(g.rast));var z=Date.now();if(g.bufPtr){k=new Uint8Array(new ArrayBuffer(g.buf_size));for(var q=0,p=0;p<g.out_height;++p)for(var A=
g.bufPtr+g.stride*p,E=0;E<g.out_width;++E)k[q++]=Module.HEAPU8[A+2],k[q++]=Module.HEAPU8[A+1],k[q++]=Module.HEAPU8[A],k[q++]=Module.HEAPU8[A+3],A+=4}else k=Module.ReadBufferFile("b",k);Object(h.b)("bufferTiming","Copy took ".concat(Date.now()-z));return{pageBuf:k.buffer,pageWidth:g.out_width,pageHeight:g.out_height}};f.RasterizePage=function(k,g,z,q,p,A,E,C,G){return new Promise(function(H,I){Module.currentRenderData={};var a=Module.currentRenderData;a.out_width=parseInt(z,10);a.out_height=parseInt(q,
10);var b=[];b.push(function(){Module.currentRenderData=null});try{var c=Module.stackSave();b[b.length]=function(){Module.stackRestore(c)};var m=Module.GetPage(k,g),e=Module.PageGetPageWidth(m),w=Module.PageGetPageHeight(m);a.stride=4*a.out_width;a.buf_size=a.out_width*a.out_height*4;Object(h.b)("Memory","Created rasterizer");a.rast=Module.PDFRasterizerCreate();b.push(function(){Object(h.b)("Memory","Destroyed rasterizer");Module._TRN_PDFRasterizerDestroy(a.rast)});if(E){var D=Module.EMSCreateUpdatedLayersContext(k,
E);0!==D&&(REX(Module._TRN_PDFRasterizerSetOCGContext(a.rast,D)),b.push(function(){Module._TRN_OCGContextDestroy(D)}))}var B=!1;C.hasOwnProperty("renderAnnots")?(C.renderAnnots&&(B=!0),REX(Module._TRN_PDFRasterizerSetDrawAnnotations(a.rast,C.renderAnnots?1:0))):REX(Module._TRN_PDFRasterizerSetDrawAnnotations(a.rast,0));C.hasOwnProperty("highlightFields")&&(C.highlightFields&&(B=!0),REX(Module._TRN_PDFRasterizerSetHighlightFields(a.rast,C.highlightFields)));C.hasOwnProperty("antiAliasing")&&REX(Module._TRN_PDFRasterizerSetAntiAliasing(a.rast,
C.antiAliasing));C.hasOwnProperty("pathHinting")&&REX(Module._TRN_PDFRasterizerSetPathHinting(a.rast,C.pathHinting));if(C.hasOwnProperty("thinLinePixelGridFit")){var F=!0;C.hasOwnProperty("thinLineStrokeAdjust")&&(F=C.thinLineStrokeAdjust);REX(Module._TRN_PDFRasterizerSetThinLineAdjustment(a.rast,C.thinLinePixelGridFit,F))}else C.hasOwnProperty("thinLineStrokeAdjust")&&REX(Module._TRN_PDFRasterizerSetThinLineAdjustment(a.rast,!1,C.thinLineStrokeAdjust));C.hasOwnProperty("thinLineScaling")&&REX(Module._TRN_PDFRasterizerSetThinLineScaling(a.rast,
C.thinLineScaling));if(C.hasOwnProperty("nightModeTuningContrast")||C.hasOwnProperty("nightModeTuningSaturation")||C.hasOwnProperty("nightModeTuningFlipness")){var J=C.hasOwnProperty("nightModeTuningContrast")?C.nightModeTuningContrast:.9,L=C.hasOwnProperty("nightModeTuningSaturation")?C.nightModeTuningSaturation:.8,K=C.hasOwnProperty("nightModeTuningFlipness")?C.nightModeTuningFlipness:1;REX(Module._TRN_PDFRasterizerSetNightModeTuning(a.rast,J,L,K))}C.hasOwnProperty("imageSmoothing")?(F=!1,C.hasOwnProperty("hqImageResampling")&&
(F=C.hqImageResampling),REX(Module._TRN_PDFRasterizerSetImageSmoothing(a.rast,C.imageSmoothing,F))):C.hasOwnProperty("hqImageResampling")&&REX(Module._TRN_PDFRasterizerSetImageSmoothing(a.rast,!0,C.hqImageResampling));C.hasOwnProperty("caching")&&REX(Module._TRN_PDFRasterizerSetCaching(a.rast,C.caching));C.hasOwnProperty("expGamma")&&REX(Module._TRN_PDFRasterizerSetGamma(a.rast,C.expGamma));C.hasOwnProperty("isPrinting")&&(C.isPrinting&&(B=!0),REX(Module._TRN_PDFRasterizerSetPrintMode(a.rast,C.isPrinting)));
C.hasOwnProperty("colorPostProcessMode")&&(C.colorPostProcessMode&&(B=!0),REX(Module._TRN_PDFRasterizerSetColorPostProcessMode(a.rast,C.colorPostProcessMode)));var N=Module.PageGetRotation(m);F=1===A||3===A;N=(1===N||3===N)!==F;var M=Module.allocate(48,"i8",Module.ALLOC_STACK);if(p){p.x1=p[0];p.y1=p[1];p.x2=p[2];p.y2=p[3];var Q=Module.PageGetDefaultMatrix(m,0),R=Module.Matrix2DInverse(Q);p=Module.Matrix2DMultBBox(R,p);if(p.x2<p.x1){var P=p.x1;p.x1=p.x2;p.x2=P}p.y2<p.y1&&(P=p.y1,p.y1=p.y2,p.y2=P);
var S=a.out_width/(N?p.y2-p.y1:p.x2-p.x1);var T=Module.GetDefaultMatrixBox(m,p,A)}else T=Module.PageGetDefaultMatrix(m,A),S=a.out_width/(F?w:e);Module.Matrix2DSet(M,S,0,0,S,0,0);Module.Matrix2DConcat(M,T);var U=Module.allocate(4,"i8",Module.ALLOC_STACK),O=Module.allocate(4,"i8",Module.ALLOC_STACK);B?(a.bufPtr=Module._malloc(a.buf_size),Module._memset(a.bufPtr,C.pageTransparent?0:255,a.buf_size),b.push(function(){Module._free(a.bufPtr)})):(Module.CreateBufferFile("b",a.buf_size,C.pageTransparent),
b.push(function(){Module.RemoveBufferFile("b")}));var V=C.overprintMode;if(10===V){REX(Module._TRN_PDFRasterizerSetOverprint(a.rast,1));var Z=Module.PDFRasterizerRasterizeSeparations(a.rast,m,a.out_width,a.out_height,M,0,0);H({pageBuf:Z,pageWidth:a.out_width,pageHeight:a.out_height})}else{REX(Module._TRN_PDFRasterizerSetOverprint(a.rast,V));B?REX(Module._TRN_PDFRasterizerGetChunkRenderer(a.rast,m,a.bufPtr,a.out_width,a.out_height,a.stride,4,!0,M,0,0,0,U)):REX(Module._TRN_PDFRasterizerGetChunkRendererPath(a.rast,
m,Module.GetUStringFromJSString("b"),a.out_width,a.out_height,!0,M,0,0,0,U));var Y=Module.getValue(U,"i8*");b.push(function(){REX(Module._TRN_ChunkRendererDestroy(Y))})}var aa=(new Date).getTime();C.useProgress&&Module.postProgressiveRenderingStartEvent(G,g);var da=t(function W(){try{if(REX(Module._TRN_ChunkRendererRenderForTimePeriod(Y,200,O)),Module.getValue(O,"i8"))Module.cleanupState.timeout=t(W);else{var ba=Module.GetCurrentCanvasData(!1);Object(h.b)("worker","Total Page Time ".concat((new Date).getTime()-
aa));b.reverse().forEach(function(X){X()});H(ba)}}catch(X){b.reverse().forEach(function(ca){ca()}),I(X)}});Module.cleanupState={cleanupArr:b,timeout:da};b.push(function(){Module.cleanupState=null})}catch(ea){b.reverse().forEach(function(W){W()}),I(ea)}})};f.UpdatePassword=function(k,g){try{var z=Module.stackSave();var q=Module.GetDoc(k);return Module.PDFDocInitStdSecurityHandler(q,g)?(q in downloadDataMap&&REX(Module._TRN_PDFDocDownloaderInitialize(q,downloadDataMap[q].downloader)),{success:!0,pageDimensions:Module.GetPageDimensions(q)}):
{success:!1}}finally{Module.stackRestore(z)}};f.UpdateCustomHeader=function(k,g){Module.customHeadersMap[k]=k in Module.customHeadersMap?d(d({},Module.customHeadersMap[k]),g):g;Module.docs[k].chunkStorage&&(Module.docs[k].chunkStorage.customHeaders=Module.customHeadersMap[k])};f.TriggerFullDownload=function(k){return new Promise(function(g,z){var q=Module.GetDoc(k);try{q in downloadDataMap&&REX(Module.PDFDocDownloaderTriggerFullDownload(q,downloadDataMap[q].downloader)),g({})}catch(p){z(p)}})};f.InsertBlankPages=
function(k,g,z,q){return new Promise(function(p,A){var E=[],C=Module.GetDoc(k);try{var G=Module.stackSave();E[E.length]=function(){Module.stackRestore(G)};for(var H=g.length-1;0<=H;--H){var I=Module.PDFDocGetPageIterator(C,g[H]),a=Module.PDFDocPageCreate(C,z,q);Module.PDFDocPageInsert(C,I,a)}var b=Module.postPagesUpdatedEvent(C,k);E.forEach(function(c){c()});p(b)}catch(c){E.forEach(function(m){m()}),A(c)}})};f.InsertPages=function(k,g,z,q,p,A){return new Promise(function(E,C){var G=[],H=Module.GetDoc(k);
try{var I=Module.stackSave();G[G.length]=function(){Module.stackRestore(I)};if(g instanceof ArrayBuffer){var a=Module.CreateDoc(g);var b=Module.GetDoc(a);G[G.length]=function(){Module.DeleteDoc(a)}}else b=Module.GetDoc(g);for(var c=z.length,m=Module.PageSetCreate(),e=0;e<c;++e)Module.PageSetAddPage(m,z[e]);A?Module.PDFDocInsertPages2(H,q,b,m,p):Module.PDFDocInsertPages(H,q,b,m,p);var w;A||(w=Module.postPagesUpdatedEvent(H,k));G.reverse().forEach(function(D){D()});E(w)}catch(D){G.reverse().forEach(function(B){B()}),
C(D)}})};f.MovePages=function(k,g,z){return new Promise(function(q,p){var A=[],E=Module.GetDoc(k);try{var C=Module.stackSave();A[A.length]=function(){Module.stackRestore(C)};for(var G=g.length,H=Module.PageSetCreate(),I=0;I<G;++I)Module.PageSetAddPage(H,g[I]);Module.PDFDocMovePages(E,z,H);var a=Module.postPagesUpdatedEvent(E,k);A.forEach(function(b){b()});q(a)}catch(b){A.forEach(function(c){c()}),p(b)}})};f.RemovePages=function(k,g,z){return new Promise(function(q,p){var A=Module.GetDoc(k),E=[];try{var C=
Module.stackSave();E[E.length]=function(){Module.stackRestore(C)};for(var G=g.length-1;0<=G;--G){var H=Module.PDFDocGetPageIterator(A,g[G]);Module.IteratorHasNext(H)&&(z?Module.PDFDocPageRemove2(A,H):Module.PDFDocPageRemove(A,H))}var I;z||(I=Module.postPagesUpdatedEvent(A,k));E.forEach(function(a){a()});q(I)}catch(a){E.forEach(function(b){b()}),p(a)}})};f.RotatePages=function(k,g,z){return new Promise(function(q,p){var A=Module.GetDoc(k),E=[];try{var C=Module.stackSave();E[E.length]=function(){Module.stackRestore(C)};
var G=g.length,H=0,I=Module.PDFDocGetPageIterator(A,g[0]),a=[];E.push(function(){Module._TRN_IteratorDestroy(I)});for(var b=g[0];Module.IteratorHasNext(I)&&H<g[G-1];++b){if(b===g[H]){var c=Module.IteratorCurrent(I),m=(Module.PageGetRotation(c)+z)%4;Module.PageSetRotation(c,m);a.push(b);H++}Module.IteratorNext(I)}var e=Module.postPagesUpdatedEvent(A,k,a,!0);E.reverse().forEach(function(w){w()});q(e)}catch(w){E.reverse().forEach(function(D){D()}),p(w)}})};f.ExtractPages=function(k,g,z,q,p,A){return new Promise(function(E,
C){var G=[];try{var H=Module.stackSave();G[G.length]=function(){Module.stackRestore(H)};var I=function(c){G.reverse().forEach(function(m){m()});C(c)};Module.XFDFMerge(k,z,p);var a=Module.CreateEmptyDoc();G[G.length]=function(){Module.DeleteDoc(a)};var b=Module.InsertPages(a,k,g,1,!0,A).then(function(){return Module.SaveDoc(a,void 0,!0,!1,void 0,q)}).then(function(c){G.reverse().forEach(function(m){m()});return c});E(b)}catch(c){I(c)}})};f.CropPages=function(k,g,z,q,p,A){return new Promise(function(E,
C){var G=Module.GetDoc(k),H=[];try{var I=Module.stackSave();H[H.length]=function(){Module.stackRestore(I)};var a=g.length,b=0,c=Module.PDFDocGetPageIterator(G,g[0]);H.push(function(){Module._TRN_IteratorDestroy(c)});for(var m=[],e=g[0];Module.IteratorHasNext(c)&&b<g[a-1];++e){if(e===g[b]){var w=Module.IteratorCurrent(c),D=Module.allocate(8,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetCropBox(w,D));var B=Module.PageGetRotation(w),F=Module.getValue(D,"double"),J=Module.getValue(D+8,"double"),L=Module.getValue(D+
16,"double"),K=Module.getValue(D+24,"double");0===B%4?(Module.setValue(D,F+p,"double"),Module.setValue(D+8,J+q,"double"),Module.setValue(D+16,L-A,"double"),Module.setValue(D+24,K-z,"double")):1===B%4?(Module.setValue(D,F+z,"double"),Module.setValue(D+8,J+p,"double"),Module.setValue(D+16,L-q,"double"),Module.setValue(D+24,K-A,"double")):2===B%4?(Module.setValue(D,F+A,"double"),Module.setValue(D+8,J+z,"double"),Module.setValue(D+16,L-p,"double"),Module.setValue(D+24,K-q,"double")):3===B%4&&(Module.setValue(D,
F+q,"double"),Module.setValue(D+8,J+A,"double"),Module.setValue(D+16,L-z,"double"),Module.setValue(D+24,K-p,"double"));Module.setValue(D+32,0,"double");REX(Module._TRN_PageSetBox(w,0,D));REX(Module._TRN_PageSetBox(w,1,D));m.push(e);b++}Module.IteratorNext(c)}var N=Module.postPagesUpdatedEvent(G,k,m,!0);H.reverse().forEach(function(M){M()});E(N)}catch(M){H.reverse().forEach(function(Q){Q()}),C(M)}})}})("undefined"===typeof self?this.Module:self.Module);this.loaded=!0;Module.initCb&&Module.initCb()}}})(self)}).call(this,
u(7).clearImmediate,u(7).setImmediate)},function(v,y,u){(function(r){function t(d){"@babel/helpers - typeof";return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(h){return typeof h}:function(h){return h&&"function"==typeof Symbol&&h.constructor===Symbol&&h!==Symbol.prototype?"symbol":typeof h},t(d)}var x=u(1),l="undefined"!==typeof window?window:self;l.global=l;(function(d){d.currentFileString="/current";var h=0,n=0,f={},k=null;Module.chunkMax=200;var g=function(a,b,c,m,e){var w=
new XMLHttpRequest;return CancellablePromise(function(D,B){w.open("GET",a,!0);w.responseType="arraybuffer";w.onerror=function(){B("Network error occurred")};w.onload=function(){if(206===this.status&&w.response.byteLength===c){var J=new Int8Array(w.response);D(J)}else B("Download Failed")};var F=["bytes=",b,"-",b+c-1].join("");w.setRequestHeader("Range",F);e&&(w.withCredentials=e);m&&Object.keys(m).forEach(function(J){w.setRequestHeader(J,m[J])});w.send()},function(){w.abort()})},z=function(a){this.maxChunkNum=
a;this.lruList=[];this.chunkMap={}};z.prototype={has:function(a){return a in this.chunkMap},insert:function(a,b){this.lruList.length>=this.maxChunkNum&&(delete this.chunkMap[this.lruList[0]],this.lruList.shift());this.lruList.push(b);this.chunkMap[b]=a},get:function(a){var b=this.lruList.lastIndexOf(a);0<=b&&this.lruList.splice(b,1);this.lruList.push(a);return this.chunkMap[a]}};var q=function(a){this.file=a;this.filePosition=0;this.fileLength=a.size;this.chunkSize=1048576;this.chunkCache=new z(8);
this.reader=new FileReaderSync};q.prototype={read:function(a,b,c){c=this.filePosition+c<=this.fileLength?c:this.fileLength-this.filePosition;a=a.subarray(b,b+c);b=c;for(var m=this.filePosition%this.chunkSize,e=this.filePosition-m,w=0;0<c;){if(this.chunkCache.has(e))var D=this.chunkCache.get(e);else D=new Int8Array(this.reader.readAsArrayBuffer(this.file.slice(e,e+this.chunkSize))),this.chunkCache.insert(D,e);var B=D.length,F=m+c;F<=B?(a.set(D.subarray(m,F),w),this.filePosition+=c,c=0):(a.set(D.subarray(m),
w),this.filePosition+=B-m,m=0,e=this.filePosition,c=F-B,w=b-c)}return b},seek:function(a){this.filePosition=a},close:function(){this.reader=this.file=null},getPos:function(){return this.filePosition},getTotalSize:function(){return this.fileLength}};var p=function(a){this.data=a;this.position=0;this.length=this.data.length};p.prototype={read:function(a,b,c){c=this.position+c<=this.length?c:this.length-this.position;a=a.subarray(b,b+c);b=this.data.subarray(this.position,this.position+c);a.set(b);this.position+=
c;return c},write:function(a,b,c){c=this.position+c<=this.length?c:this.length-this.position;a=a.subarray(b,b+c);this.data.subarray(this.position,this.position+c).set(a);this.position+=c;return c},seek:function(a){this.position=a},close:function(){this.data=null},getPos:function(){return this.position},getTotalSize:function(){return this.length}};var A=function(a,b,c,m,e){"object"===t(a)?(this.lruList=a.lruList,this.chunkMap=a.chunkMap,this.length=a.length,this.url=a.url,this.customHeaders=a.customHeaders,
this.withCredentials=a.withCredentials):(this.lruList=[],this.chunkMap={},this.chunkMap[b]=e,this.length=b,this.url=a,this.customHeaders=c,this.withCredentials=m)};A.prototype={lruUpdate:function(a){var b=this.lruList.lastIndexOf(a);0<=b&&this.lruList.splice(b,1);this.lruList.push(a)},getChunk:function(a){var b=this;if(this.chunkMap[a])this.lruUpdate(a);else{var c=Math.min(a+1048576,this.length)-1,m=new XMLHttpRequest;m.open("GET",this.url,!1);m.responseType="arraybuffer";m.setRequestHeader("Range",
["bytes=",a,"-",c].join(""));this.withCredentials&&(m.withCredentials=this.withCredentials);this.customHeaders&&Object.keys(this.customHeaders).forEach(function(e){m.setRequestHeader(e,b.customHeaders[e])});m.send();if(200===m.status||206===m.status)this.writeChunk(new Int8Array(m.response),a);else throw Error("Failed to load data from");}return this.chunkMap[a]},hadChunk:function(a){return a in this.chunkMap},hasChunk:function(a){return this.chunkMap[a]},getCacheData:function(){return this.chunkMap[this.length]},
getRequiredChunkOffsetArrays:function(a,b){var c={have:[],downloading:[],missing:[]};try{var m=Module.stackSave();var e=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_DownloaderGetRequiredChunksSize(a,b,e));var w=Module.getValue(e,"i8*");if(w){var D=Module._malloc(8*w);REX(Module._TRN_DownloaderGetRequiredChunks(a,b,D,w));for(a=0;a<w;++a){var B=Module.getValue(D+8*a,"double");this.hasChunk(B)?c.have.push(B):this.hadChunk(B)?c.missing.push(B):c.downloading.push(B)}}}finally{D&&Module._free(D),
Module.stackRestore(m)}return c},keepVisibleChunks:function(a,b){for(var c=b.length,m=Module.chunkMax/2,e=0,w=0;w<c;++w){var D=this.getRequiredChunkOffsetArrays(a,b[w]),B=D.have,F=B.length;e+=F;if(e>m){this.keepChunks(B.slice(0,F-e+m));break}this.keepChunks(D.have)}},getChunkAsync:function(a){var b=this,c=a+1048576,m=1048576;c>this.length&&(m-=c-this.length);return g(this.url,a,m,this.customHeaders,this.withCredentials).then(function(e){b.writeChunk(e,a)})},getChunks:function(a){for(var b=a.length,
c=Array(b),m=0;m<b;++m)c[m]=this.getChunkAsync(a[m]);return CancellablePromise.all(c)},keepChunks:function(a){for(var b=a.length,c=0;c<b;++c)this.lruUpdate(a[c])},writeChunk:function(a,b,c){c=c||0;var m=this.chunkMap[b],e=a.length,w=this.lruList.length>=Module.chunkMax&&!m;1048576!==e||a.buffer.byteLength!==e?(w?(m=this.lruList.shift(),w=this.chunkMap[m],1048576>w.length&&(w=new Int8Array(1048576)),this.chunkMap[m]=null):w=m?this.chunkMap[b]:new Int8Array(1048576),w.subarray(c,c+e).set(a),a=w):w&&
(m=this.lruList.shift(),this.chunkMap[m]=null);this.lruUpdate(b);this.chunkMap[b]=a}};var E=function(a){this.chunkStorage=a;this.position=0;this.length=this.chunkStorage.length};E.prototype={read:function(a,b,c){var m=this.position+c<=this.length,e=m?c:this.length-this.position;if(this.position<this.length){var w;for(w=0;w<e;){var D=this.position%1048576;var B=this.position-D;var F=e-w,J=a.subarray(b+w,b+w+F);if(this.chunkStorage.hadChunk(B))B=this.chunkStorage.getChunk(B).subarray(D,D+F),J.set(B),
J=B.length,w+=J,this.position+=J;else for(this.position+=F;w<e;++w)J[w]=0}}if(!m){b+=e;if(c-=e)m=this.chunkStorage.getCacheData(),c>m.length&&(c=m.length),w=this.position-this.length,a=a.subarray(b,b+c),B=m.subarray(w,w+c),a.set(B);this.position+=c;return e+c}return e},write:function(a,b,c){var m=this.position+c<=this.length,e=this.position+c<=this.length?c:this.length-this.position,w=a.subarray(b,b+e),D=this.position%1048576;this.chunkStorage.writeChunk(w,this.position-D,D);this.position+=e;if(!m){w=
b+e;if(c-=e)b=this.chunkStorage.getCacheData(),c>b.length&&(c=b.length),m=this.position-this.length,w=a.subarray(w,w+c),b.subarray(m,m+c).set(w);this.position+=c;return e+c}return e},seek:function(a){this.position=a},close:function(){this.chunkStorage=null},getPos:function(){return this.position},getTotalSize:function(){return this.length}};var C=function(a){this.docId=a;this.length=0;this.data=new Int8Array(8192);this.position=0};C.prototype={seek:function(a){this.position=a},close:function(){var a=
new Int8Array(this.data.buffer.slice(0,this.length));Module.ChangeDocBackend(this.docId,{ptr:Module.GetDoc(this.docId),buffer:a});this.data=null},getPos:function(){return this.position},getTotalSize:function(){return this.length},read:function(a,b,c){var m=this.data.length;c=c+b<m?c:m-b;a=a.subarray(b,b+c);b=this.data.subarray(this.position,this.position+c);a.set(b);this.position+=c;return c},write:function(a,b,c){for(var m=this.position+c,e=this.data.length;m>e;){e=Math.max(e*(16777216<e?1.5:2),
m);var w=new Int8Array(e);w.set(this.data.subarray(0,this.length),0);this.data=w}a=a.subarray(b,b+c);this.data.set(a,this.position);this.position+=c;this.position>this.length&&(this.length=this.position);return c}};var G={IsSink:function(a){return 66===(a.flags&255)},open:function(a){var b=a.path.slice(1);this.IsSink(a)?(a.provider=new C(b),Module.docs[b].sink=a.provider):a.provider=Module.docs[b].sink?new p(Module.docs[b].sink.data):Module.docs[b].chunkStorage?new E(Module.docs[b].chunkStorage):
Module.docs[b].buffer?new p(Module.docs[b].buffer):new q(Module.docs[b].file)},close:function(a){a.provider.close()},read:function(a,b,c,m,e){return a.provider.read(b,c,m)},llseek:function(a,b,c){a=a.provider;1===c?b+=a.getPos():2===c&&(b=a.getTotalSize()+b);if(0>b)throw new FS.ErrnoError(l.ERRNO_CODES.EINVAL);a.seek(b);return b},write:function(a,b,c,m,e){return m?a.provider.write(b,c,m):0}};l.THROW=function(a){throw{type:"PDFWorkerError",message:a};};var H=function(a){return"Exception: \n\t Message: ".concat(d.GetJSStringFromCString(Module._TRN_GetMessage(a)),
"\n\t Filename: ").concat(d.GetJSStringFromCString(Module._TRN_GetFileName(a)),"\n\t Function: ").concat(d.GetJSStringFromCString(Module._TRN_GetFunction(a)),"\n\t Linenumber: ").concat(d.GetJSStringFromCString(Module._TRN_GetLineNum(a)))};d.GetErrToString=H;l.REX=function(a){a&&THROW(H(a))};d.Initialize=function(a){var b=Module.stackSave();a=a?Module.allocate(Module.intArrayFromString(a),"i8",Module.ALLOC_STACK):0;REX(Module._TRN_PDFNetInitialize(a));Module.stackRestore(b)};d.GetDoc=function(a){if(a in
Module.docs)return Module.docs[a].ptr;throw{type:"InvalidDocReference",message:"Unable to access Document id=".concat(a,". The document appears to be invalid or was deleted.")};};d.clearDocBackend=function(){null!==Module.cachePtr?(Module.hasBufOwnership&&Module._free(Module.cachePtr),Module.cachePtr=null):Module.docs[d.currentFileString]&&delete Module.docs[d.currentFileString]};d.MakeDev=function(a){if(!f[a]){var b=FS.makedev(3,5);FS.registerDevice(b,G);FS.mkdev(a,511,b);f[a]=!0}};d.CreateDocFileBackend=
function(a,b){Module.MakeDev(b);var c=Module.allocate(4,"i8",Module.ALLOC_STACK);Module.docs[b]={file:a};a=Module.allocate(Module.intArrayFromString(b),"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocCreateFromFilePath(a,c));c=Module.getValue(c,"i8*");Module.docs[b].ptr=c};d.InsertImageIntoDoc=function(a,b,c){var m=[];try{var e=Module.ElementBuilderCreate();m.push(function(){Module.ElementBuilderDestroy(e)});var w=Module.ElementWriterCreate();m.push(function(){Module.ElementWriterDestroy(w)});if(c){var D=
c.width;var B=c.height}else D=Module.ImageGetImageWidth(b),B=Module.ImageGetImageHeight(b),c=D/B,c>612/792?(D=612,B=parseInt(D/c,10)):(B=792,D=parseInt(B*c,10));var F=Module.ElementBuilderCreateImage(e,b,0,0,D,B),J=Module.PDFDocPageCreate(a,D,B);Module.ElementWriterBegin(w,J);Module.ElementWriterWritePlacedElement(w,F);Module.ElementWriterEnd(w);Module.PDFDocPagePushBack(a,J)}finally{m.reverse().forEach(function(L){L()})}};var I=function(a,b,c){"object"===t(a)?(this.m_pages=a.m_pages,this.m_has_named_dests=
a.m_has_named_dests,this.m_finished_download=a.m_finished_download,this.m_has_outline=a.m_has_outline,this.m_current_page=a.m_current_page,this.m_id=a.m_id,this.size=a.size,this.timeout=a.timeout,this.eventPageArray=a.eventPageArray,this.requirePageCallbacks=a.requirePageCallbacks):(this.m_pages=[],this.m_has_outline=this.m_finished_download=this.m_has_named_dests=!1,this.m_current_page=1,this.m_id=c,this.size=a,this.timeout=null,this.eventPageArray=[],this.requirePageCallbacks={});this.downloadUserData=
Module.createDownloadUserData(b,c)};I.prototype={getJSUrl:function(){return Module.extractDownloadUserData(this.downloadUserData).url},getDocId:function(){return Module.extractDownloadUserData(this.downloadUserData).docId},destroyUserData:function(){this.m_id in Module.withCredentials&&delete Module.withCredentials[this.m_id];this.m_id in Module.customHeadersMap&&delete Module.customHeadersMap[this.m_id];Module.destroyDownloadUserData(this.downloadUserData)}};d.createDownloadUserData=function(a,b){a=
Module.allocate(Module.intArrayFromString(a),"i8",Module.ALLOC_NORMAL);var c=Module.allocate(8,"i8",Module.ALLOC_NORMAL);Module.setValue(c,a,"i8*");Module.setValue(c+4,parseInt(b,10),"i32");return this.downloadUserData=c};d.extractDownloadUserData=function(a){var b=Module.getValue(a,"i8*");b=d.GetJSStringFromCString(b);a=Module.getValue(a+4,"i32").toString();return{url:b,docId:a}};d.destroyDownloadUserData=function(a){Module._free(Module.getValue(a,"i8*"));Module._free(a)};l.downloadDataMap={};Module.customHeadersMap=
{};Module.withCredentials={};d.GetDownloadData=function(a){if(a in downloadDataMap)return downloadDataMap[a]};d.DownloaderHint=function(a,b){var c=Module.GetDoc(a),m=downloadDataMap[c];b.currentPage&&(m.m_current_page=b.currentPage);if(b.visiblePages){var e=b.visiblePages;for(b=0;b<e.length;++b)++e[b];Object.keys(m.requirePageCallbacks).forEach(function(D){m.requirePageCallbacks.hasOwnProperty(D)&&e.push(parseInt(D,10))});(b=Module.docs[a].chunkStorage)&&b.keepVisibleChunks(m.downloader,e);a=e.length;
var w=Module.allocate(4*a,"i8",Module.ALLOC_STACK);for(b=0;b<a;++b)Module.setValue(w+4*b,e[b],"i32");REX(Module._TRN_PDFDocDownloadPages(c,w,a,1,0))}};d.RequirePage=function(a,b){return new Promise(function(c,m){m=Module.GetDoc(a);var e=downloadDataMap[m];!e||e.m_finished_download||e.m_pages[b]?c():(b in e.requirePageCallbacks?e.requirePageCallbacks[b].push(c):e.requirePageCallbacks[b]=[c],c=Module.allocate(4,"i8",Module.ALLOC_STACK),Module.setValue(c,b,"i32"),Module._TRN_PDFDocDownloadPages(m,c,
1,0,0))})};d.IsLinearizationValid=function(a){a=Module.GetDoc(a);if(a=downloadDataMap[a]){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_DownloaderIsLinearizationValid(a.downloader,b));return 0!==Module.getValue(b,"i8")}return!1};d.ShouldRunRender=function(a,b){a=Module.GetDoc(a);return(a=downloadDataMap[a])?a.m_finished_download?!0:a.m_pages[b]:!0};d.postPagesDownloadedEvent=function(a,b,c){a={pageDimensions:Module.GetPageDimensionsContentChangedList(a,c),pageNumbers:c};Module.postEvent(b,
"pagesDownloaded",a);return a};d.createCallbacksStruct=function(a){if(!k){var b=function(c){return function(m){var e=arguments;m in downloadDataMap?c.apply(this,e):r(function(){m in downloadDataMap&&c.apply(this,e)},0)}};k={downloadProc:Module.addFunction(function(c,m,e,w,D){w=Module.extractDownloadUserData(w);var B=w.docId;g(w.url,m,e,Module.customHeadersMap[B],Module.withCredentials[B]).then(function(F){B in Module.docs&&Module.docs[B].chunkStorage&&Module.docs[B].chunkStorage.writeChunk(F,m);Module._TRN_DownloadComplete(0,
m,e,c)})},"vidiii"),notifyUpdatePage:Module.addFunction(b(function(c,m,e,w){var D=downloadDataMap[c];D.m_pages[m]=!0;var B=D.eventPageArray;if(m in D.requirePageCallbacks)for(e=D.requirePageCallbacks[m],w=0;w<e.length;++w)e[w]();D.timeout?B.push(m):(B=D.eventPageArray=[m],D.timeout=setTimeout(function(){Module.postPagesDownloadedEvent(c,D.m_id,B);D.timeout=null},100))}),"viiii"),notifyUpdateOutline:Module.addFunction(b(function(c,m){c=downloadDataMap[c];c.m_has_outline||(c.m_has_outline=!0,Module.postEvent(c.m_id,
"bookmarksUpdated",{}))}),"vii"),notifyUpdateNamedDests:Module.addFunction(b(function(c,m){c=downloadDataMap[c];c.m_has_named_dests||(c.m_has_named_dests=!0)}),"vii"),notifyDocInfoDownloaded:Module.addFunction(b(function(c,m){downloadDataMap[c].docInfoPromiseCapability.resolve()}),"vii"),notifyUpdateThumb:Module.addFunction(b(function(c,m){}),"viiii"),notifyFinishedDownload:Module.addFunction(b(function(c,m){c=downloadDataMap[c];c.m_finished_download||(c.m_finished_download=!0,Module.postEvent(c.m_id,
"documentComplete",{}))}),"vii"),notifyDocumentError:Module.addFunction(function(c,m){},"viii"),getCurrentPage:Module.addFunction(function(c,m){return downloadDataMap[c].m_current_page},"iii")}}b=Module.allocate(44,"i8",Module.ALLOC_STACK);Module.setValue(b,k.downloadProc,"i8*");Module.setValue(b+4,a,"i8*");Module.setValue(b+8,k.notifyUpdatePage,"i8*");Module.setValue(b+12,k.notifyUpdateOutline,"i8*");Module.setValue(b+16,k.notifyUpdateNamedDests,"i8*");Module.setValue(b+20,k.notifyDocInfoDownloaded,
"i8*");Module.setValue(b+24,k.notifyUpdateThumb,"i8*");Module.setValue(b+28,k.notifyFinishedDownload,"i8*");Module.setValue(b+32,k.notifyDocumentError,"i8*");Module.setValue(b+36,k.getCurrentPage,"i8*");Module.setValue(b+40,0,"i8*");return b};d.CreateDocDownloaderBackend=function(a,b,c){var m=a.url,e=a.size,w=a.customHeaders,D=a.withCredentials,B=a.shouldUseMinimumDownloads;w&&(Module.customHeadersMap[c]=w);D&&(Module.withCredentials[c]=D);var F=a.downloadData?new I(a.downloadData,m,c):new I(a.size,
m,c);var J=Module.createCallbacksStruct(F.downloadUserData),L=Module.allocate(4,"i8",Module.ALLOC_STACK);Module.MakeDev(c);a.chunkStorage?m=new A(a.chunkStorage):(a=new Int8Array(new ArrayBuffer(Math.ceil((a.size+1048576-1)/1048576/8))),m=new A(m,e,w,D,a));Module.docs[c]={chunkStorage:m};REX(Module._TRN_DownloaderCreate(J,e,Module.GetUStringFromJSString(c),B,L));F.downloader=Module.getValue(L,"i8*");F.docInfoPromiseCapability=createPromiseCapability();F.docInfoRequested=!1;if(e=Module._TRN_PDFDocCreateFromFilter(F.downloader,
b))Module._TRN_FilterDestroy(F.downloader),REX(e);b=Module.getValue(b,"i8*");Module.docs[c].ptr=b;Module.PDFDocInitSecurityHandler(b)&&REX(Module._TRN_PDFDocDownloaderInitialize(b,F.downloader));downloadDataMap[b]=F};d.CreateDocBackend=function(a,b){var c=a.value,m=a.extension,e=a.type,w=Module.allocate(4,"i8",Module.ALLOC_STACK),D=Module.stackSave();try{if(c)if("ptr"===e)Module.docs[b]={ptr:c};else{c.shouldUseMinimumDownloads=a.shouldUseMinimumDownloads;var B="object"===t(c)&&c.url;e=m&&"pdf"!==
m;if(B)d.CreateDocDownloaderBackend(c,w,b);else{var F=c instanceof ArrayBuffer;B=F?"buffer":"file";if(F&&(c=new Uint8Array(c),10485760>c.length+h&&!e)){h+=c.length;var J=c.length,L=Module._malloc(c.length);Module.HEAPU8.set(c,L);REX(Module._TRN_PDFDocCreateFromBuffer(L,J,w));var K=Module.getValue(w,"i8*");Module.docs[b]={ptr:K,bufPtr:L,bufPtrSize:J,ownership:!0};Module.docs[b].extension=m;return}Module.MakeDev(b);F={};F[B]=c;Module.docs[b]=F;if(e){if(a.pageSizes&&a.pageSizes.length)var N=a.pageSizes[0];
else a.defaultPageSize&&(N=a.defaultPageSize);var M=Module.GetUStringFromJSString(b);REX(Module._TRN_PDFDocCreate(w));K=Module.getValue(w,"i8*");var Q=Module.ImageCreateFromFile(K,M);Module.InsertImageIntoDoc(K,Q,N)}else{var R=Module.allocate(Module.intArrayFromString(b),"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocCreateFromFilePath(R,w));K=Module.getValue(w,"i8*")}Module.docs[b].extension=m;Module.docs[b].ptr=K}}else REX(Module._TRN_PDFDocCreate(w)),K=Module.getValue(w,"i8*"),Module.docs[b]={ptr:K},
Module.docs[b].extension=m}finally{Module.stackRestore(D)}};d.ChangeDocBackend=function(a,b){var c=Module.docs[a];c?(c.bufPtr&&c.ownership&&(Module._free(c.bufPtr),h-=c.bufPtrSize),delete Module.docs[a]):Object(x.d)("Trying to delete document ".concat(a," that does not exist."));Module.docs[a]=b};d.DeleteDoc=function(a){var b=Module.docs[a];b?(b.ptr&&(b.ptr in downloadDataMap&&(clearTimeout(downloadDataMap[b.ptr].timeout),downloadDataMap[b.ptr].destroyUserData(),delete downloadDataMap[b.ptr]),Module.PDFDocDestroy(b.ptr)),
b.bufPtr&&b.ownership&&(Module._free(b.bufPtr),h-=b.bufPtrSize),delete Module.docs[a]):Object(x.d)("Trying to delete document ".concat(a," that does not exist."))};d.CreateDoc=function(a,b){if("id"===a.type){if(Module.docPtrStringToIdMap&&a.value in Module.docPtrStringToIdMap)return Module.docPtrStringToIdMap[a.value];a.type="ptr";a.value=Number("0x".concat(a.value))}if(!b){do b=(++n).toString();while(b in Module.docs)}Module.hasBufOwnership=!0;d.CreateDocBackend(a,b);return b};d.CreateEmptyDoc=function(){var a=
(++n).toString(),b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocCreate(b));b=Module.getValue(b,"i8*");Module.docs[a]={ptr:b};return a};d.PDFDocCreateFromLayoutEls=function(a){var b=new Uint8Array(a);a=Module._malloc(b.length);var c=Module.stackSave(),m=Module.allocate(4,"i8",Module.ALLOC_STACK);Module.HEAPU8.set(b,a);b=Module._TRN_PDFDocCreateFromLayoutEls(a,b.length,m);m=Module.getValue(m,"i8*");Module._free(a);Module.stackRestore(c);REX(b);return m};d.GetPageCount=function(a){var b=
Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocGetPageCount(a,b));return Module.getValue(b,"i8*")};d.GetPage=function(a,b){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocGetPage(a,b,c));a=Module.getValue(c,"i8*");Module.PageIsValid(a)||THROW("Trying to access page that doesn't exist at index ".concat(b));return a};d.PageGetPageWidth=function(a){var b=Module.allocate(8,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetPageWidth(a,1,b));return Module.getValue(b,
"double")};d.PageGetPageHeight=function(a){var b=Module.allocate(8,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetPageHeight(a,1,b));return Module.getValue(b,"double")};d.PageGetDefaultMatrix=function(a,b){var c=Module.allocate(48,"i8",Module.ALLOC_STACK);b||(b=0);REX(Module._TRN_PageGetDefaultMatrix(a,!0,1,b,c));return c};d.GetMatrixAsArray=function(a){for(var b=[],c=0;6>c;++c)b[c]=Module.getValue(a+8*c,"double");return b};d.PageGetPageInfo=function(a){var b=Module.allocate(48,"i8",Module.ALLOC_STACK),
c=Module.allocate(8,"i8",Module.ALLOC_STACK),m=Module.allocate(8,"i8",Module.ALLOC_STACK),e=Module.allocate(4,"i8",Module.ALLOC_STACK),w=Module.allocate(4,"i8",Module.ALLOC_STACK),D=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetPageInfo(a,!0,1,0,c,m,b,e,w,D));return{rotation:Module.getValue(e,"i8*"),width:Module.getValue(c,"double"),height:Module.getValue(m,"double"),matrix:Module.GetMatrixAsArray(b),linkAnnotCount:Module.getValue(w,"i8*"),otherExceptPopupAnnotCount:Module.getValue(D,
"i8*")}};d.GetUStringFromJSString=function(a,b){var c=Module.allocate(4,"i8",Module.ALLOC_STACK),m=2*(a.length+1),e=Module.allocate(m,"i8",b?Module.ALLOC_NORMAL:Module.ALLOC_STACK);Module.stringToUTF16(a,e,m);a=Module._TRN_UStringCreateFromString(e,c);b&&Module._free(e);REX(a);return Module.getValue(c,"i8*")};d.GetJSStringFromUString=function(a){var b=Module.allocate(4,"i16*",Module.ALLOC_STACK);REX(Module._TRN_UStringCStr(a,b));return Module.UTF16ToString(Module.getValue(b,"i16*"))};d.GetJSStringFromCString=
function(a){return Module.UTF8ToString(a)};d.PDFNetSetResourceData=function(a){Module.res_ptr=Module._malloc(a.length);Module.HEAPU8.set(a,Module.res_ptr);REX(Module._TRN_PDFNetSetResourceData(Module.res_ptr,a.length));Module.res_ptr_size=a.length};d.PDFDocDestroy=function(a){REX(Module._TRN_PDFDocDestroy(a))};d.VectorGetSize=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_VectorGetSize(a,b));return Module.getValue(b,"i32")};d.VectorGetAt=function(a,b){var c=Module.allocate(1,
"i8*",Module.ALLOC_STACK);REX(Module._TRN_VectorGetAt(a,b,c));return Module.getValue(c,"i8*")};d.VectorDestroy=function(a){REX(Module._TRN_VectorDestroy(a))};d.PDFRasterizerCreate=function(){var a=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFRasterizerCreate(0,a));return Module.getValue(a,"i8*")};d.ExtractSeparationData=function(a){var b=Module.getValue(a,"i8*"),c=Module.getValue(a+4,"i32"),m=Module.getValue(a+8,"i8*"),e=Module.HEAPU8[a+12],w=Module.HEAPU8[a+13],D=Module.HEAPU8[a+
14];a=Module.HEAPU8[a+15];var B=new Uint8Array(c);B.set(Module.HEAPU8.subarray(b,b+c));b=Module.GetJSStringFromUString(m);return{color:[e,w,D,a],data:B.buffer,name:b}};d.ExtractApRefChangeData=function(a){var b=Module.getValue(a,"i32"),c=Module.getValue(a+4,"i32"),m=Module.getValue(a+8,"i32"),e=Module.getValue(a+12,"i32");a=0!==Module.getValue(a+16,"i8");return{oldObjNum:b,discardAppearance:a,newObjNum:c,genNum:m,pageNum:e}};d.PDFRasterizerRasterizeSeparations=function(a,b,c,m,e,w,D){var B=Module.allocate(8,
"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFRasterizerRasterizeSeparations(a,b,c,m,e,w,D,B));a=Module.getValue(B,"i8*");b=Module.VectorGetSize(a);c=Array(b);for(m=0;m<b;++m)e=Module.VectorGetAt(a,m),c[m]=Module.ExtractSeparationData(e);Module.VectorDestroy(a);return c};d.PageGetRotation=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetRotation(a,b));return Module.getValue(b,"i8*")};d.PageGetId=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetSDFObj(a,
b));b=Module.getValue(b,"i8*");a=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjGetObjNum(b,a));a=Module.getValue(a,"i32");var c=Module.allocate(2,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjGetGenNum(b,c));c=Module.getValue(c,"i16");return"".concat(a,"-").concat(c)};d.PageSetRotation=function(a,b){REX(Module._TRN_PageSetRotation(a,b))};d.GetDefaultMatrixBox=function(a,b,c){var m=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetRotation(a,m));a=(Module.getValue(m,"i32")+
c)%4;c=Module.allocate(48,"i8",Module.ALLOC_STACK);switch(a){case 0:return REX(Module._TRN_Matrix2DSet(c,1,0,0,-1,-b.x1,b.y2)),c;case 1:return REX(Module._TRN_Matrix2DSet(c,0,1,1,0,-b.y1,-b.x1)),c;case 2:return REX(Module._TRN_Matrix2DSet(c,-1,0,0,1,b.x2,-b.y1)),c;case 3:return REX(Module._TRN_Matrix2DSet(c,0,-1,-1,0,b.y2,b.x2)),c}throw Error("Yikes, we don't support that rotation type");};d.Matrix2DMultBBox=function(a,b){var c=Module.allocate(8,"i8",Module.ALLOC_STACK),m=Module.allocate(8,"i8",Module.ALLOC_STACK);
Module.setValue(c,b.x1,"double");Module.setValue(m,b.y1,"double");REX(Module._TRN_Matrix2DMult(a,c,m));b.x1=Module.getValue(c,"double");b.y1=Module.getValue(m,"double");Module.setValue(c,b.x2,"double");Module.setValue(m,b.y2,"double");REX(Module._TRN_Matrix2DMult(a,c,m));b.x2=Module.getValue(c,"double");b.y2=Module.getValue(m,"double");return b};d.Matrix2DMult=function(a,b){var c=Module.allocate(8,"i8",Module.ALLOC_STACK),m=Module.allocate(8,"i8",Module.ALLOC_STACK);Module.setValue(c,b.x,"double");
Module.setValue(m,b.y,"double");REX(Module._TRN_Matrix2DMult(a,c,m));b.x=Module.getValue(c,"double");b.y=Module.getValue(m,"double");return b};d.Matrix2DConcat=function(a,b){var c=Module.getValue(b,"double"),m=Module.getValue(b+8,"double"),e=Module.getValue(b+16,"double"),w=Module.getValue(b+24,"double"),D=Module.getValue(b+32,"double");b=Module.getValue(b+40,"double");REX(Module._TRN_Matrix2DConcat(a,c,m,e,w,D,b))};d.Matrix2DSet=function(a,b,c,m,e,w,D){REX(Module._TRN_Matrix2DSet(a,b,c,m,e,w,D))};
d.IteratorHasNext=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_IteratorHasNext(a,b));return 0!==Module.getValue(b,"i8")};d.IteratorCurrent=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_IteratorCurrent(a,b));return Module.getValue(Module.getValue(b,"i8*"),"i8*")};d.IteratorNext=function(a){REX(Module._TRN_IteratorNext(a))};d.PageGetNumAnnots=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetNumAnnots(a,
b));return Module.getValue(b,"i32")};d.PageGetAnnot=function(a,b){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetAnnot(a,b,c));return Module.getValue(c,"i8*")};d.PageAnnotRemove=function(a,b){REX(Module._TRN_PageAnnotRemoveByIndex(a,b))};d.AnnotGetType=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_AnnotGetType(a,b));return Module.getValue(b,"i32")};d.AnnotHasAppearance=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_AnnotGetAppearance(a,
0,0,b));return 0!==Module.getValue(b,"i8")};d.AnnotRefreshAppearance=function(a){REX(Module._TRN_AnnotRefreshAppearance(a))};d.ObjErase=function(a,b){b=Module.allocate(Module.intArrayFromString(b),"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjEraseFromKey(a,b))};d.GetJSDoubleArrFromCore=function(a,b){for(var c=Array(b),m=0;m<b;++m)c[m]=Module.getValue(a,"double"),a+=8;return c};d.GetJSIntArrayFromCore=function(a,b){for(var c=Array(b),m=0;m<b;++m)c[m]=Module.getValue(a,"i32"),a+=4;return c};d.BookmarkIsValid=
function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_BookmarkIsValid(a,b));return 0!==Module.getValue(b,"i8")};d.BookmarkGetNext=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_BookmarkGetNext(a,b));return Module.getValue(b,"i8*")};d.BookmarkGetFirstChild=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_BookmarkGetFirstChild(a,b));return Module.getValue(b,"i8*")};d.BookmarkHasChildren=function(a){var b=Module.allocate(4,
"i8",Module.ALLOC_STACK);REX(Module._TRN_BookmarkHasChildren(a,b));return 0!==Module.getValue(b,"i8")};d.BookmarkGetAction=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_BookmarkGetAction(a,b));return Module.getValue(b,"i8*")};d.BookmarkGetTitle=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_BookmarkGetTitle(a,b));a=Module.getValue(b,"i8*");return Module.GetJSStringFromUString(a)};d.ActionIsValid=function(a){var b=Module.allocate(4,"i8",
Module.ALLOC_STACK);REX(Module._TRN_ActionIsValid(a,b));return 0!==Module.getValue(b,"i8")};d.ActionGetType=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ActionGetType(a,b));return Module.getValue(b,"i32")};d.ActionGetDest=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ActionGetDest(a,b));return Module.getValue(b,"i8*")};d.DestinationIsValid=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_DestinationIsValid(a,
b));return 0!==Module.getValue(b,"i8")};d.DestinationGetPage=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_DestinationGetPage(a,b));return Module.getValue(b,"i8*")};d.PageIsValid=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageIsValid(a,b));return 0!==Module.getValue(b,"i8")};d.PageGetIndex=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageGetIndex(a,b));return Module.getValue(b,"i32")};d.ObjGetAsPDFText=
function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjGetAsPDFText(a,b));a=Module.getValue(b,"i8*");return Module.GetJSStringFromUString(a)};d.ObjFindObj=function(a,b){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);b=Module.allocate(Module.intArrayFromString(b),"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjFindObj(a,b,c));return Module.getValue(c,"i8*")};d.PDFDocGetFirstBookmark=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocGetFirstBookmark(a,
b));return Module.getValue(b,"i8*")};d.DestinationGetExplicitDestObj=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_DestinationGetExplicitDestObj(a,b));return Module.getValue(b,"i8*")};d.DestinationGetFitType=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_DestinationGetFitType(a,b));return Module.getValue(b,"i32")};d.ObjIsNumber=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjIsNumber(a,b));return 0!==Module.getValue(b,
"i8")};d.ObjGetNumber=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjGetNumber(a,b));return Module.getValue(b,"double")};d.PDFDocGetRoot=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocGetRoot(a,b));return Module.getValue(b,"i8*")};d.ObjPutName=function(a,b,c){b=Module.allocate(Module.intArrayFromString(b),"i8",Module.ALLOC_STACK);c=Module.allocate(Module.intArrayFromString(c),"i8",Module.ALLOC_STACK);var m=Module.allocate(4,"i8",
Module.ALLOC_STACK);REX(Module._TRN_ObjPutName(a,b,c,m));return Module.getValue(m,"i8*")};d.ObjPutDict=function(a,b){b=Module.allocate(Module.intArrayFromString(b),"i8",Module.ALLOC_STACK);var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjPutDict(a,b,c));return Module.getValue(c,"i8*")};d.ObjPutString=function(a,b,c){b=Module.allocate(Module.intArrayFromString(b),"i8",Module.ALLOC_STACK);c=Module.allocate(Module.intArrayFromString(c),"i8",Module.ALLOC_STACK);var m=Module.allocate(4,
"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjPutString(a,b,c,m));return Module.getValue(m,"i8*")};d.ObjPut=function(a,b,c){b=Module.allocate(Module.intArrayFromString(b),"i8",Module.ALLOC_STACK);var m=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjPut(a,b,c,m));return Module.getValue(m,"i8*")};d.ObjGetAt=function(a,b){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ObjGetAt(a,b,c));return Module.getValue(c,"i8*")};d.Matrix2DInverse=function(a){var b=Module.allocate(48,
"i8",Module.ALLOC_STACK);REX(Module._TRN_Matrix2DInverse(a,b));return b};d.PDFDocInitSecurityHandler=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocInitSecurityHandler(a,0,b));return 0!==Module.getValue(b,"i8")};d.PDFDocSetSecurityHandler=function(a,b){REX(Module._TRN_PDFDocSetSecurityHandler(a,b))};d.SecurityHandlerCreate=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_SecurityHandlerCreate(a,b));return Module.getValue(b,"i8*")};d.SecurityHandlerChangeUserPasswordUString=
function(a,b){REX(Module._TRN_SecurityHandlerChangeUserPasswordUString(a,Module.GetUStringFromJSString(b)))};d.PDFDocInitStdSecurityHandler=function(a,b){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocInitStdSecurityHandlerUString(a,Module.GetUStringFromJSString(b),c));return 0!==Module.getValue(c,"i8")};d.PDFDocDownloaderTriggerFullDownload=function(a,b){REX(Module._TRN_PDFDocDownloaderTriggerFullDownload(a,b))};d.PDFDocInsertPages=function(a,b,c,m,e){REX(Module._TRN_PDFDocInsertPageSet(a,
b,c,m,e?1:0,0))};d.PDFDocInsertPages2=function(a,b,c,m,e){REX(Module._TRN_PDFDocInsertPageSet2(a,b,c,m,e?1:0,0))};d.PDFDocMovePages=function(a,b,c){REX(Module._TRN_PDFDocMovePageSet(a,b,a,c,0,0))};d.PDFDocGetPageIterator=function(a,b){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocGetPageIterator(a,b,c));return Module.getValue(c,"i8*")};d.PDFDocPageRemove=function(a,b){REX(Module._TRN_PDFDocPageRemove(a,b))};d.PDFDocPageRemove2=function(a,b){REX(Module._TRN_PDFDocPageRemove2(a,
b))};d.PDFDocPageCreate=function(a,b,c){var m=Module.allocate(40,"i8",Module.ALLOC_STACK);Module.setValue(m,0,"double");Module.setValue(m+8,0,"double");Module.setValue(m+16,b,"double");Module.setValue(m+24,c,"double");Module.setValue(m+32,0,"double");b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocPageCreate(a,m,b));return Module.getValue(b,"i8*")};d.PDFDocPageInsert=function(a,b,c){REX(Module._TRN_PDFDocPageInsert(a,b,c))};d.PageSetCreate=function(){var a=Module.allocate(4,"i8",
Module.ALLOC_STACK);REX(Module._TRN_PageSetCreate(a));return Module.getValue(a,"i8*")};d.PageSetCreateRange=function(a,b){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PageSetCreateRange(c,a,b));return Module.getValue(c,"i8*")};d.PageSetAddPage=function(a,b){REX(Module._TRN_PageSetAddPage(a,b))};d.ElementBuilderCreate=function(){var a=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ElementBuilderCreate(a));return Module.getValue(a,"i8*")};d.ElementBuilderDestroy=function(a){REX(Module._TRN_ElementBuilderDestroy(a))};
d.ElementBuilderCreateImage=function(a,b,c,m,e,w){var D=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ElementBuilderCreateImageScaled(a,b,c,m,e,w,D));return Module.getValue(D,"i8*")};d.ElementWriterCreate=function(){var a=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ElementWriterCreate(a));return Module.getValue(a,"i8*")};d.ElementWriterDestroy=function(a){REX(Module._TRN_ElementWriterDestroy(a))};d.ElementWriterBegin=function(a,b){REX(Module._TRN_ElementWriterBeginOnPage(a,
b,1,1,1,0))};d.ElementWriterWritePlacedElement=function(a,b){REX(Module._TRN_ElementWriterWritePlacedElement(a,b))};d.ElementWriterEnd=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ElementWriterEnd(a,b))};d.ImageGetImageWidth=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ImageGetImageWidth(a,b));return Module.getValue(b,"i32")};d.ImageGetImageHeight=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ImageGetImageHeight(a,
b));return Module.getValue(b,"i32")};d.ImageCreateFromMemory2=function(a,b,c){var m=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ImageCreateFromMemory2(a,b,c,0,m));return Module.getValue(m,"i8*")};d.ImageCreateFromFile=function(a,b){var c=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_ImageCreateFromFile(a,b,0,c));return Module.getValue(c,"i8*")};d.PDFDocPagePushBack=function(a,b){REX(Module._TRN_PDFDocPagePushBack(a,b))};d.PDFDocHasOC=function(a){var b=Module.allocate(4,
"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocHasOC(a,b));return 0!==Module.getValue(b,"i8")};d.PDFDocGetOCGConfig=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocGetOCGConfig(a,b));return Module.getValue(b,"i8*")};d.OCGContextCreate=function(a){var b=Module.allocate(4,"i8",Module.ALLOC_STACK);REX(Module._TRN_OCGContextCreateFromConfig(a,b));return Module.getValue(b,"i8*")};d.UStringDestroy=function(a){REX(Module._TRN_UStringDestroy(a))};d.PDFDocFDFUpdate=function(a,
b,c){if(c){for(var m=Object.keys(c),e=m.length,w=Module._malloc(8*e),D=0;D<e;++D){var B=8*D,F=m[D],J=Module.GetDoc(c[F]);F=Module.GetUStringFromJSString(F);Module.setValue(w+B,J,"i8*");Module.setValue(w+B+4,F,"i8*")}c=Module.allocate(8,"i8",Module.ALLOC_STACK);REX(Module._TRN_PDFDocFDFUpdateAppearanceDocs(a,b,w,e,c));a=Module.getValue(c,"i8*");b=Module.VectorGetSize(a);e=Array(b);for(w=0;w<b;++w)c=Module.VectorGetAt(a,w),e[w]=Module.ExtractApRefChangeData(c);Module.VectorDestroy(a);if(b)return e}else REX(Module._TRN_PDFDocFDFUpdate(a,
b))};d.FDFDocDestroy=function(a){REX(Module._TRN_FDFDocDestroy(a))}})(l.Module)}).call(this,u(7).setImmediate)},function(v,y,u){function r(t){"@babel/helpers - typeof";return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(x){return typeof x}:function(x){return x&&"function"==typeof Symbol&&x.constructor===Symbol&&x!==Symbol.prototype?"symbol":typeof x},r(t)}(function(t){t.SetupPDFNetFunctions=function(x){Module._IB_=[];for(var l=function E(A){if("object"===r(A)&&null!==A)if("undefined"!==
typeof A.byteLength){var C=Module._IB_.length;Module._IB_[C]=new Uint8Array(A);A={handle:C,isArrayBufferRef:!0}}else Object.keys(A).forEach(function(G){A.hasOwnProperty(G)&&(A[G]=E(A[G]))});return A},d=function C(E){"object"===r(E)&&null!==E&&(E.buffer?E=E.buffer.slice(E.byteOffset,E.byteOffset+E.length):E.isArrayBufferRef?E=Module._IB_[E.handle].buffer:Object.keys(E).forEach(function(G){E.hasOwnProperty(G)&&(E[G]=C(E[G]))}));return E},h=Module._TRN_EMSCreateSharedWorkerInstance(),n,f=Module._TRN_EMSWorkerInstanceGetFunctionIterator(h),
k=function(E,C){return new Promise(function(G,H){E=l(E);var I=E.docId;I=I?Module.GetDoc(I):0;(I=Module.EMSCallSharedFunction(h,C,I))?H({type:"PDFWorkerError",message:Module.GetErrToString(I)}):(H=Module.EMSGetLastResponse(h),H=d(H),G(H))})};n=Module._TRN_EMSFunctionIteratorGetNextCommandName(f);)n=Module.GetJSStringFromCString(n),t.queue.onAsync(n,k);Module._TRN_EMSFunctionIteratorDestroy(f);if(Module._TRN_EMSCreatePDFNetWorkerInstance){var g={};f=function(p,A){x.on(p,A);g[p]=!0};Module.docPtrStringToIdMap=
{};var z=function(p){if(p in Module.docPtrStringToIdMap)return Module.docPtrStringToIdMap[p];throw Error("Couldn't find document ".concat(p));};t.queue.onAsync("PDFDoc.RequirePage",function(p){return Module.RequirePage(z(p.docId),p.pageNum)});f("pdfDocCreateFromBuffer",function(p){p=Module.CreateDoc({type:"array",value:p.buf});var A=Module.GetDoc(p).toString(16);Module.docPtrStringToIdMap[A]=p;return A});f("PDFDoc.destroy",function(p){p=z(p.auto_dealloc_obj);Module.DeleteDoc(p)});f("PDFDoc.saveMemoryBuffer",
function(p){var A=z(p.doc);return Module.SaveHelper(Module.GetDoc(A),A,p.flags).slice(0)});f("pdfDocCreate",function(){var p=Module.CreateDoc({type:"new"}),A=Module.GetDoc(p).toString(16);Module.docPtrStringToIdMap[A]=p;return A});f("GetPDFDoc",function(p){p=p.docId;var A=Module.GetDoc(p).toString(16);Module.docPtrStringToIdMap[A]=p;return A});f("ExtractPDFNetLayersContext",function(p){var A=p.layers;p=Module.GetDoc(p.docId);var E=0;A?E=Module.EMSCreateUpdatedLayersContext(p,A):Module.PDFDocHasOC(p)&&
(A=Module.PDFDocGetOCGConfig(p),E=Module.OCGContextCreate(A));return E.toString(16)});var q=Module._TRN_EMSCreatePDFNetWorkerInstance();f=Module._TRN_EMSWorkerInstanceGetFunctionIterator(q);for(k=function(p){return new Promise(function(A,E){p=l(p);var C=Module.EMSCallPDFNetFunction(q,p);C?E(Module.GetErrToString(C)):(E=Module.EMSGetLastResponse(q),E=d(E),A(E))})};n=Module._TRN_EMSFunctionIteratorGetNextCommandName(f);)if(n=Module.GetJSStringFromCString(n),!g[n])x.onAsync(n,k);Module._TRN_EMSFunctionIteratorDestroy(f)}}})(self)},
function(v,y,u){v=u(6);var r=u.n(v),t=u(15),x=u(16),l=u(5),d=u(17),h=u(1),n=u(18);(function(f){var k=null;f.basePath="../";var g=function(){var q=f.pdfWorkerPath||"";f.workerBasePath&&(f.basePath=f.workerBasePath);var p=f.isFull,A=p?"full/":"lean/";f.useOptimizedWorker&&(A+=n.a);var E=f.wasmDisabled,C=f.disableObjectURLBlobs,G=f.pdfWorkerChunkPaths;Object(h.c)();f.overriddenPdfWorkerPath&&(q=f.overriddenPdfWorkerPath,f.basePath="../",!Object(l.a)()||E)&&(q="");f.basePath=f.externalPath?f.externalPath:
f.basePath+"external/";Object(d.a)("".concat(q+A,"PDFNetC"),{"Wasm.wasm":p?1E7:4E6,"Wasm.js.mem":1E5,".js.mem":12E6,".mem":p?2E6:6E5,disableObjectURLBlobs:C},E,G)};f.EmscriptenPDFManager=function(){};f.EmscriptenPDFManager.prototype={OnInitialized:function(q){Module.loaded?q():(Module.initCb=function(){q()},Object(h.b)("worker","PDFNet is not initialized yet!"))},NewDoc:function(q,p){return new Promise(function(A,E){try{A(Module.loadDoc(q,p))}catch(C){E(C)}})},Initialize:function(q,p,A,E){q&&(Module.TOTAL_MEMORY=
q);Module.memoryInitializerPrefixURL=p;Module.asmjsPrefix=A;f.basePath=E;g()},shouldRunRender:function(q){return Module.ShouldRunRender(q.docId,q.pageIndex+1)}};var z={setup:function(q){function p(e){var w=e.data,D=e.action;var B="GetCanvas"===D||"GetCanvasPartial"===D?G.shouldRunRender(w):!0;if(B){k=e;var F=e.asyncCallCapability;Object(h.b)("Memory","Worker running command: ".concat(D));H.actionMap[D](w,e).then(function(J){"BeginOperation"!==k.action&&(k=null);F.resolve(J)},function(J){k=null;F.reject(J)})}else f.deferredQueue.queue(e),
C()}function A(e){e.asyncCallCapability=createPromiseCapability();k||H.length?H.queue(e):p(e);return e.asyncCallCapability.promise}function E(e){self.shouldResize&&G.Initialize(e.options.workerHeapSize,e.options.pdfResourcePath,e.options.pdfAsmPath,e.options.parentUrl);Module.chunkMax=e.options.chunkMax;if(e.array instanceof Uint8Array){var w=255===e.array[0];q.postMessageTransfers=w;"response"in new XMLHttpRequest?G.OnInitialized(function(){f.SetupPDFNetFunctions(q);b();q.send("test",{supportTypedArray:!0,
supportTransfers:w})}):q.send("test",!1)}else q.send("test",!1)}function C(){t.a.setImmediate(function(){if((!k||"BeginOperation"!==k.action)&&H.length&&!k){var e=H.dequeue();p(e)}})}var G=new f.EmscriptenPDFManager,H,I=!1,a=!1;Module.workerMessageHandler=q;var b=function(){I?a||(q.send("workerLoaded",{}),a=!0):I=!0};G.OnInitialized(b);(function(){f.queue=H=new r.a({strategy:r.a.ArrayStrategy,comparator:function(e,w){return e.priority===w.priority?e.callbackId-w.callbackId:w.priority-e.priority}});
f.deferredQueue=new r.a({strategy:r.a.ArrayStrategy,comparator:function(e,w){return e.priority===w.priority?e.callbackId-w.callbackId:w.priority-e.priority}});H.actionMap={};H.onAsync=function(e,w){q.onAsync(e,A);H.actionMap[e]=w}})();q.on("test",E);q.on("InitWorker",E);var c=function(e){k&&e(k)&&(Module.cancelCurrent(),k=null);H.removeAllMatching(e,function(w){w.asyncCallCapability.reject({type:"Cancelled",message:"Operation was cancelled due to a change affecting the loaded document."})})},m=function(e){c(function(w){return w.data&&
w.data.docId===e})};q.on("UpdatePassword",function(e){return Module.UpdatePassword(e.docId,e.password)});q.on("UpdateCustomHeader",function(e){return Module.UpdateCustomHeader(e.docId,e.customHeader)});q.on("LoadRes",function(e){Module.loadResources(e.array,e.l);return{}});q.on("DownloaderHint",function(e){Module.DownloaderHint(e.docId,e.hint)});q.on("IsLinearized",function(e){return Module.IsLinearizationValid(e.docId)});q.onNextAsync(C);H.onAsync("NewDoc",function(e){return G.NewDoc(e)});H.onAsync("GetCanvas",
function(e){Object(h.b)("workerdetails","Run GetCanvas PageIdx: ".concat(e.pageIndex," Width: ").concat(e.width));Object(h.b)("Memory","loadCanvas with potential memory usage ".concat(e.width*e.height*8));return Module.loadCanvas(e.docId,e.pageIndex,e.width,e.height,e.rotation,null,e.layers,e.renderOptions)});H.onAsync("GetCanvasPartial",function(e){Object(h.b)("Memory","GetCanvasPartial with potential memory usage ".concat(e.width*e.height*8));return Module.loadCanvas(e.docId,e.pageIndex,e.width,
e.height,e.rotation,e.bbox,e.layers,e.renderOptions)});H.onAsync("SaveDoc",function(e){return Module.SaveDoc(e.docId,e.xfdfString,e.finishedWithDocument,e.printDocument,e.flags,e.watermarks,e.apdocs,e.password,e.encryptionAlgorithmType)});H.onAsync("SaveDocFromFixedElements",function(e){return Module.SaveDocFromFixedElements(e.bytes,e.xfdfString,e.flags,e.watermarks,e.password,e.encryptionAlgorithmType)});H.onAsync("MergeXFDF",function(e){return Module.MergeXFDF(e.docId,e.xfdf,e.apdocs)});H.onAsync("InsertPages",
function(e){return Module.InsertPages(e.docId,e.doc,e.pageArray,e.destPos,e.insertBookmarks,e.skipUpdateEvent)});H.onAsync("MovePages",function(e){return Module.MovePages(e.docId,e.pageArray,e.destPos)});H.onAsync("RemovePages",function(e){return Module.RemovePages(e.docId,e.pageArray,e.skipUpdateEvent)});H.onAsync("RotatePages",function(e){return Module.RotatePages(e.docId,e.pageArray,e.rotation)});H.onAsync("ExtractPages",function(e){return Module.ExtractPages(e.docId,e.pageArray,e.xfdfString,e.watermarks,
e.apdocs,e.skipUpdateEvent)});H.onAsync("CropPages",function(e){return Module.CropPages(e.docId,e.pageArray,e.topMargin,e.botMargin,e.leftMargin,e.rightMargin)});H.onAsync("TriggerFullDownload",function(e){return Module.TriggerFullDownload(e.docId)});H.onAsync("InsertBlankPages",function(e){return Module.InsertBlankPages(e.docId,e.pageArray,e.width,e.height)});H.onAsync("BeginOperation",function(){return Promise.resolve()});H.onAsync("RequirePage",function(e,w){return Module.RequirePage(e.docId,e.pageNum)});
q.on("FinishOperation",function(){if(k&&"BeginOperation"===k.action)k=null,C();else throw{message:"Operation has not started."};});q.on("DeleteDocument",function(e){e=e.docId;m(e);Module.DeleteDoc(e)});q.on("GetCanvasProgressive",function(e){if(k&&k.callbackId===e.callbackId){Object(h.b)("worker","Progressive request in progress");var w=Module.GetCurrentCanvasData(!0)}else{if(H.find({priority:0,callbackId:e.callbackId}))throw Object(h.b)("worker","Progressive request Queued"),{type:"Queued",message:"Rendering has not started yet."};
if(f.deferredQueue.find({priority:0,callbackId:e.callbackId}))throw Object(h.b)("worker","Progressive request Deferred"),{type:"Queued",message:"Rendering has not started yet."};}if(!w)throw Object(h.b)("worker","Progressive request invalid (render already complete)"),{type:"Unavailable",message:"Rendering is complete or was cancelled."};return w});q.on("actionCancel",function(e){k&&k.callbackId===e.callbackId?(Object(h.b)("workerdetails","Cancelled Current Operation"),Module.cancelCurrent()&&(k=
null,C())):(Object(h.b)("workerdetails","Cancelled queued operation"),H.remove({priority:0,callbackId:e.callbackId}),f.deferredQueue.remove({priority:0,callbackId:e.callbackId}))})}};f.onmessage=function(q){if("init"===q.data.action){var p=q.data.shouldResize;f.shouldResize=p;f.isFull=q.data.isFull;f.wasmDisabled=!q.data.wasm;f.externalPath=q.data.externalPath;f.useOptimizedWorker=q.data.useOptimizedWorker;f.disableObjectURLBlobs=q.data.disableObjectURLBlobs;f.pdfWorkerChunkPaths=q.data.pdfWorkerChunkPaths;
if(q=q.data.pdfWorkerPath)f.overriddenPdfWorkerPath=q;p||g();p=new x.a("worker_processor",self);Object(h.a)(p);z.setup(p)}}})("undefined"===typeof window?self:window)}]);}).call(this || window)
