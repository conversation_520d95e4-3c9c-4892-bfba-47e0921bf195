(window.webpackJsonp=window.webpackJsonp||[]).push([[52],{1812:function(e,t,o){var n=o(32),r=o(1813);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,o=document){const n=[];return o.querySelectorAll(t).forEach(e=>n.push(e)),o.querySelectorAll("*").forEach(o=>{o.shadowRoot&&n.push(...e(t,o.shadowRoot))}),n}("apryse-webviewer"));const o=[];for(let n=0;n<t.length;n++){const r=t[n];if(0===n)r.shadowRoot.appendChild(e),e.onload=function(){o.length>0&&o.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),o.push(t)}}},singleton:!1};n(r,i);e.exports=r.locals||{}},1813:function(e,t,o){(t=e.exports=o(33)(!1)).push([e.i,':host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.header-footer-edit-ui{position:absolute;height:46px;width:100%;align-items:center;display:flex;visibility:hidden}.header-footer-edit-ui.header-edit-ui{top:143px}.header-footer-edit-ui.footer-edit-ui{bottom:143px}.header-footer-edit-ui.active{visibility:visible}.header-footer-edit-ui .box-shadow-div{width:100%;height:100%;overflow:hidden;position:absolute;display:flex;align-items:center}.header-footer-edit-ui .box-shadow-div:after{content:"";position:absolute;width:100%;height:40px;padding:3px 0;background-color:var(--gray-0);box-shadow:0 0 3px 0 var(--gray-7);z-index:-1}.header-footer-edit-ui .label{color:var(--gray-8);font-size:.8125rem;font-weight:700;margin-left:16px}.header-footer-edit-ui .options-dropdown-container{position:absolute;right:16px}.header-footer-edit-ui .options-dropdown-container .options-button{color:var(--blue-5);padding:8px 32px 8px 8px;background:transparent;width:auto;border:none;cursor:pointer}.header-footer-edit-ui .options-dropdown-container .options-button .Icon{width:16px;height:16px;position:absolute;right:9.5px;top:0;bottom:0;margin:auto;color:var(--blue-5)}.header-footer-edit-ui .options-dropdown-container .options-button.active,.header-footer-edit-ui .options-dropdown-container .options-button.active .Icon,.header-footer-edit-ui .options-dropdown-container .options-button:hover,.header-footer-edit-ui .options-dropdown-container .options-button:hover .Icon{color:var(--blue-6)}.header-footer-edit-ui .options-dropdown-container .options-button.active .Icon{transform:rotate(180deg)}.header-footer-edit-ui .options-dropdown-container .Dropdown__items{padding:4px 0;min-width:157px}.header-footer-edit-ui .options-dropdown-container .Dropdown__items .Dropdown__item{height:32px;padding:0 10px 0 40px;position:relative;width:auto}.header-footer-edit-ui .options-dropdown-container .Dropdown__items .Dropdown__item:first-child{margin-bottom:9px}.header-footer-edit-ui .options-dropdown-container .Dropdown__items .Dropdown__item .Icon{position:absolute;left:10px;fill:var(--gray-7);top:0;bottom:0;margin:auto}.header-footer-edit-ui .options-dropdown-container .Dropdown__items .Divider{flex-basis:100%;width:100%;height:1px;margin:0;position:absolute;bottom:-5px;left:0}',""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1814:function(e,t,o){var n=o(32),r=o(1815);"string"==typeof(r=r.__esModule?r.default:r)&&(r=[[e.i,r,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,o=document){const n=[];return o.querySelectorAll(t).forEach(e=>n.push(e)),o.querySelectorAll("*").forEach(o=>{o.shadowRoot&&n.push(...e(t,o.shadowRoot))}),n}("apryse-webviewer"));const o=[];for(let n=0;n<t.length;n++){const r=t[n];if(0===n)r.shadowRoot.appendChild(e),e.onload=function(){o.length>0&&o.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);r.shadowRoot.appendChild(t),o.push(t)}}},singleton:!1};n(r,i);e.exports=r.locals||{}},1815:function(e,t,o){(e.exports=o(33)(!1)).push([e.i,".HeaderFooterControlsOverlay{position:absolute;top:0;left:0;width:100%;height:100%;z-index:501}",""])},1990:function(e,t,o){"use strict";o.r(t);o(41);var n=o(0),r=o.n(n),i=o(116),a=o(21),c=(o(36),o(23),o(8),o(88),o(89),o(19),o(12),o(13),o(14),o(10),o(9),o(11),o(16),o(15),o(20),o(18),o(56),o(22),o(64),o(65),o(66),o(67),o(37),o(39),o(24),o(40),o(63),o(6)),u=o(429),s=o(43),d=o(84),l=o(4),p=o.n(l),f=o(2),h=o(5),m=o(80),v=o(17),y=o.n(v),g=o(1),w=o(1754);o(1812);function b(e){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function x(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */x=function(){return e};var e={},t=Object.prototype,o=t.hasOwnProperty,n=Object.defineProperty||function(e,t,o){e[t]=o.value},r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",c=r.toStringTag||"@@toStringTag";function u(e,t,o){return Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,o){return e[t]=o}}function s(e,t,o,r){var i=t&&t.prototype instanceof p?t:p,a=Object.create(i.prototype),c=new A(r||[]);return n(a,"_invoke",{value:_(e,o,c)}),a}function d(e,t,o){try{return{type:"normal",arg:e.call(t,o)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var l={};function p(){}function f(){}function h(){}var m={};u(m,i,(function(){return this}));var v=Object.getPrototypeOf,y=v&&v(v(N([])));y&&y!==t&&o.call(y,i)&&(m=y);var g=h.prototype=p.prototype=Object.create(m);function w(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){var r;n(this,"_invoke",{value:function(n,i){function a(){return new t((function(r,a){!function n(r,i,a,c){var u=d(e[r],e,i);if("throw"!==u.type){var s=u.arg,l=s.value;return l&&"object"==b(l)&&o.call(l,"__await")?t.resolve(l.__await).then((function(e){n("next",e,a,c)}),(function(e){n("throw",e,a,c)})):t.resolve(l).then((function(e){s.value=e,a(s)}),(function(e){return n("throw",e,a,c)}))}c(u.arg)}(n,i,r,a)}))}return r=r?r.then(a,a):a()}})}function _(e,t,o){var n="suspendedStart";return function(r,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===r)throw i;return j()}for(o.method=r,o.arg=i;;){var a=o.delegate;if(a){var c=O(a,o);if(c){if(c===l)continue;return c}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if("suspendedStart"===n)throw n="completed",o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);n="executing";var u=d(e,t,o);if("normal"===u.type){if(n=o.done?"completed":"suspendedYield",u.arg===l)continue;return{value:u.arg,done:o.done}}"throw"===u.type&&(n="completed",o.method="throw",o.arg=u.arg)}}}function O(e,t){var o=t.method,n=e.iterator[o];if(void 0===n)return t.delegate=null,"throw"===o&&e.iterator.return&&(t.method="return",t.arg=void 0,O(e,t),"throw"===t.method)||"return"!==o&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+o+"' method")),l;var r=d(n,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,l;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,l):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,l)}function L(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function k(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function A(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(L,this),this.reset(!0)}function N(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,r=function t(){for(;++n<e.length;)if(o.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:j}}function j(){return{value:void 0,done:!0}}return f.prototype=h,n(g,"constructor",{value:h,configurable:!0}),n(h,"constructor",{value:f,configurable:!0}),f.displayName=u(h,c,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===f||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,u(e,c,"GeneratorFunction")),e.prototype=Object.create(g),e},e.awrap=function(e){return{__await:e}},w(E.prototype),u(E.prototype,a,(function(){return this})),e.AsyncIterator=E,e.async=function(t,o,n,r,i){void 0===i&&(i=Promise);var a=new E(s(t,o,n,r),i);return e.isGeneratorFunction(o)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},w(g),u(g,c,"Generator"),u(g,i,(function(){return this})),u(g,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),o=[];for(var n in t)o.push(n);return o.reverse(),function e(){for(;o.length;){var n=o.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},e.values=N,A.prototype={constructor:A,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(k),!e)for(var t in this)"t"===t.charAt(0)&&o.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(o,n){return a.type="throw",a.arg=e,t.next=o,n&&(t.method="next",t.arg=void 0),!!n}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=o.call(i,"catchLoc"),u=o.call(i,"finallyLoc");if(c&&u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),l},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var o=this.tryEntries[t];if(o.finallyLoc===e)return this.complete(o.completion,o.afterLoc),k(o),l}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var o=this.tryEntries[t];if(o.tryLoc===e){var n=o.completion;if("throw"===n.type){var r=n.arg;k(o)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,o){return this.delegate={iterator:N(e),resultName:t,nextLoc:o},"next"===this.method&&(this.arg=void 0),l}},e}function E(e,t,o,n,r,i,a){try{var c=e[i](a),u=c.value}catch(e){return void o(e)}c.done?t(u):Promise.resolve(u).then(n,r)}function _(e){return function(){var t=this,o=arguments;return new Promise((function(n,r){var i=e.apply(t,o);function a(e){E(i,n,r,a,c,"next",e)}function c(e){E(i,n,r,a,c,"throw",e)}a(void 0)}))}}function O(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var o=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=o){var n,r,i,a,c=[],u=!0,s=!1;try{if(i=(o=o.call(e)).next,0===t){if(Object(o)!==o)return;u=!1}else for(;!(u=(n=i.call(o)).done)&&(c.push(n.value),c.length!==t);u=!0);}catch(e){s=!0,r=e}finally{try{if(!u&&null!=o.return&&(a=o.return(),Object(a)!==a))return}finally{if(s)throw r}}return c}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return L(e,t);var o=Object.prototype.toString.call(e).slice(8,-1);"Object"===o&&e.constructor&&(o=e.constructor.name);if("Map"===o||"Set"===o)return Array.from(e);if("Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return L(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function L(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,n=new Array(t);o<t;o++)n[o]=e[o];return n}var k={type:p.a.oneOf(["header","footer"]),pageNumber:p.a.number,isActive:p.a.bool},A=function(e){var t=e.type,o=e.pageNumber,i=e.isActive,a=O(Object(u.a)(),1)[0],l=Object(c.d)(),p=Object(n.useRef)(),v="".concat(t,"-options-dropdown-").concat(o),b="".concat(t,"-edit-ui-").concat(o),E=O(Object(n.useState)(0),2),L=E[0],k=E[1],A=O(Object(n.useState)(null),2),N=A[0],j=A[1],S=O(Object(n.useState)(0),2),T=S[0],D=S[1],P=O(Object(n.useState)(0),2),F=P[0],C=P[1],H=y()("header-footer-edit-ui","".concat(t,"-edit-ui"),{active:i&&L>0}),I=function(){var e,n=g.a.getDocument().getOfficeEditor(),r=(null===(e=p.current)||void 0===e?void 0:e.clientHeight)||0;switch(t){case"header":return n.getHeaderPosition(o);case"footer":return n.getFooterPosition(o)-r;default:return 0}},R=function(){var e=_(x().mark((function e(){var t,n;return x().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=g.a.getDocument().getOfficeEditor(),e.next=3,t.getSectionNumber(o);case 3:n=e.sent,j(n);case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();Object(n.useEffect)((function(){var e=function(e){"mousedown"===e.type&&l(f.a.closeElements([h.a.CONTEXT_MENU_POPUP])),e.stopPropagation()};["click","mousedown","mouseup","mousemove","mouseenter","mouseleave","contextmenu"].forEach((function(t){p.current.addEventListener(t,e)}));var t=function(){i&&(o(),R())},o=function(){var e=_(x().mark((function e(){var t;return x().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:(t=I())>0&&k(t);case 2:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return g.a.getDocument().addEventListener("headerFooterUpdated",t),k(I()),function(){["click","mousedown","mouseup","mousemove","mouseenter","mouseleave","contextmenu"].forEach((function(t){var o;null===(o=p.current)||void 0===o||o.removeEventListener(t,e)})),g.a.getDocument().removeEventListener("headerFooterUpdated",t)}}),[i]),Object(n.useEffect)((function(){var e=function(){setTimeout((function(){var e=g.a.getOfficeEditor().getHeaderPageType(o),t=g.a.getOfficeEditor().getFooterPageType(o);-1!=e&&D(e),-1!=t&&C(t)}),800)},t=g.a.getDocument();return t.addEventListener("headerFooterLayoutUpdated",e),function(){t.removeEventListener("headerFooterLayoutUpdated",e)}}),[o]),Object(n.useEffect)((function(){var e=g.a.getOfficeEditor().getHeaderPageType(o),t=g.a.getOfficeEditor().getFooterPageType(o);-1!=e&&D(e),-1!=t&&C(t),R()}),[i,o]);var M=function(){var e=_(x().mark((function e(){return x().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:w.a.setPageNumber(o),l(f.a.openElement(h.a.HEADER_FOOTER_OPTIONS_MODAL));case 2:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),G=[{label:a("officeEditor.pageOptions"),key:"page-options",icon:"ic-edit-page",onClick:M},{label:a("header"===t?"officeEditor.removeHeader":"officeEditor.removeFooter"),key:"remove-".concat(t),icon:"ic-delete-page",onClick:function(){return"header"===t?g.a.getOfficeEditor().removeHeaders(o):"footer"===t?g.a.getOfficeEditor().removeFooters(o):void 0}}],U=function(){var e=_(x().mark((function e(t){var o;return x().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=G.find((function(e){return e.key===t})),e.next=3,null==o?void 0:o.onClick();case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),W=N?" - ".concat(a("officeEditor.section")," ").concat(N):"",q="header"===t?T:F;return r.a.createElement("div",{className:H,id:b,style:{top:L}},r.a.createElement("div",{className:"box-shadow-div",ref:p}),r.a.createElement("div",{className:"label"},a("officeEditor.".concat(t,".").concat(q)),W),r.a.createElement(m.a,{width:"auto",id:v,renderItem:function(e){return r.a.createElement(r.a.Fragment,null,r.a.createElement(s.a,{glyph:e.icon,className:"Dropdown__item-icon"}),r.a.createElement("div",{className:"Dropdown__item-vertical"},r.a.createElement("div",{className:"Dropdown__item-label"},a(e.label))),"page-options"===e.key&&r.a.createElement("div",{className:"Divider"}))},className:"options-dropdown-container",getKey:function(e){return e.key},items:G,onClickItem:U,displayButton:function(e){return r.a.createElement(d.a,{className:"options-button",ariaLabelledby:b,ariaControls:"".concat(v,"-dropdown"),ariaExpanded:e,img:"ic_chevron_down_black_24px",label:a("officeEditor.options"),isActive:e})},stopPropagationOnMouseDown:!0}))};A.propTypes=k;var N=r.a.memo(A),j=(o(1814),{visiblePages:p.a.arrayOf(p.a.number),isHeaderControlsActive:p.a.bool,isFooterControlsActive:p.a.bool}),S=function(e){var t=e.visiblePages,o=e.isHeaderControlsActive,n=e.isFooterControlsActive,c=t.map((function(e){var t=Object(a.a)().getElementById("pageSection".concat(e));return t?Object(i.createPortal)(r.a.createElement("div",{key:e,className:"HeaderFooterControlsOverlay"},r.a.createElement(N,{type:"header",pageNumber:e,isActive:o}),r.a.createElement(N,{type:"footer",pageNumber:e,isActive:n})),t):null}));return r.a.createElement(r.a.Fragment,null,c)};S.propTypes=j;var T=S;t.default=T}}]);
//# sourceMappingURL=chunk.52.js.map