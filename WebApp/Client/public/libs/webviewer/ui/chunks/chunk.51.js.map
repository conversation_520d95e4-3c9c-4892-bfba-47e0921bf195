{"version": 3, "sources": ["webpack:///./src/ui/src/components/CreateStampModal/CustomStampForums.scss?cc8c", "webpack:///./src/ui/src/components/CreateStampModal/CustomStampForums.scss", "webpack:///./src/ui/src/components/CreateStampModal/CreateStampModal.scss?ed99", "webpack:///./src/ui/src/components/CreateStampModal/CreateStampModal.scss", "webpack:///./src/ui/src/components/CreateStampModal/CustomStampForums.js", "webpack:///./src/ui/src/components/CreateStampModal/CreateStampModal.js", "webpack:///./src/ui/src/components/CreateStampModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "FALLBACK_DATE_TIME_FORMAT", "date", "time", "<PERSON><PERSON><PERSON><PERSON>", "dateTimeFormatToString", "format", "useDate", "useTime", "propTypes", "state", "PropTypes", "object", "setState", "func", "setEmptyInput", "fonts", "array", "openColorPicker", "getCustomColorAndRemove", "openDeleteModal", "dateTimeFormats", "stampTool", "userName", "string", "CustomStampForums", "updateTimestampLabel", "usernameChk", "dateChk", "dateTime", "tmpText", "defaultDateTimeFormat", "useState", "setDateTime", "usernameCheckbox", "setUsernameCheckbox", "dateCheckbox", "setDateCheckbox", "timeCheckbox", "setTimeCheckbox", "stampTextInputValue", "setStampText", "t", "useTranslation", "txt", "timestampFormat", "setTimestampFormat", "tooltipVisible", "setTooltipVisible", "tooltipRef", "useRef", "currentUser", "core", "getCurrentUser", "currentDateTime", "Date", "toLocaleString", "handleClickOutside", "event", "current", "contains", "target", "useEffect", "addEventListener", "removeEventListener", "handleTooltipClick", "canvasRef", "canvasContainerRef", "inputRef", "updateCanvas", "title", "subtitle", "newState", "parameters", "canvas", "width", "height", "color", "textColor", "canvasParent", "font", "bold", "italic", "underline", "strikeout", "drawCustomStamp", "dataURL", "toDataURL", "handleRichTextStyleChange", "style", "stampInputLabel", "getHexColor", "givenColor", "A", "toHexString", "toLowerCase", "COMMON_COLORS", "openPicker", "addNew", "type", "isText", "getInstanceNode", "instance", "UI", "Events", "VISIBILITY_CHANGED", "handleVisiblityChanged", "e", "detail", "element", "isVisible", "colorToBeAdded", "setTextColor", "setBackgroundColor", "toolColors", "Core", "Tools", "RubberStampCreateTool", "setTextColors", "setBackgroundColors", "deleteColor", "newColors", "textColors", "filter", "textColorToBeDeleted", "backgroundColors", "backgroundColorToBeDeleted", "setTextColorToBeDeleted", "setBackgroundColorToBeDeleted", "backgroundColor", "handleTextColorChange", "newColor", "handleBackgroundColorChange", "formatsList", "dateTimeDropdownItems", "Array", "from", "Set", "map", "className", "ref", "role", "aria-label", "htmlFor", "id", "classNames", "value", "onChange", "Icon", "glyph", "aria-live", "Dropdown", "labelledById", "items", "aria<PERSON><PERSON><PERSON>", "onClickItem", "currentSelectionKey", "getCustomItemStyle", "item", "fontFamily", "maxHeight", "<PERSON><PERSON>", "dataElement", "onClick", "img", "isActive", "ariaPressed", "ColorPalettePicker", "setColorToBeDeleted", "colorToBeDeleted", "customColors", "onStyleChange", "handleColorOnClick", "handleOnClick", "ariaLabelledBy", "toolTipXOffset", "disable<PERSON><PERSON>le", "enableEdit", "colorsAreHex", "aria-<PERSON>by", "Choice", "checked", "label", "newDateTime", "tabIndex", "onKeyDown", "key", "preventDefault", "background", "border", "padding", "display", "alignItems", "cursor", "newFormat", "fillColors", "CustomStampModal", "stampToolArray", "getToolsFromAllDocumentViewers", "store", "useStore", "emptyInput", "useSelector", "selectors", "isElementOpen", "DataElements", "CUSTOM_STAMP_MODAL", "getFonts", "getDateTimeFormats", "getUserName", "isOpen", "dispatch", "useDispatch", "deselectAllAnnotations", "closeModal", "useFocusOnClose", "actions", "closeElement", "onConfirm", "message", "confirmBtnText", "warning", "showWarningMessage", "modalClass", "Modal", "open", "closed", "createCustomStamp", "setToolMode", "addCustomStamp", "createCustomStampAnnotation", "annot", "setRubberStamp", "showPreview", "standardStampCount", "getStandardStamps", "customStampCount", "getCustomStamps", "setSelectedStampIndex", "onCreateCustomStampClick", "data-element", "ModalWrapper", "<PERSON><PERSON><PERSON><PERSON>", "onCloseClick", "swipeToClose", "onMouseDown", "stopPropagation", "customColor", "getCustomColor", "getState", "setCustomColor", "openElement", "isModalOpen", "disabled", "CreateStampModal"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,ymWAA8mW,KAGvoW0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,43NAA63N,KAGt5N0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,yrFCMvB,IAAMC,EAA4B,CAChCC,KAAM,aACNC,KAAM,UACNC,WAAW,GAGPC,EAAyB,SAACC,GAA2C,IAAnCC,IAAU,UAAH,+CAASC,IAAU,UAAH,+CAC7D,OAAKD,EAMAC,EAMEF,EAAOF,UAAS,UAClBE,EAAOH,KAAI,YAAIG,EAAOJ,MAAI,UAC1BI,EAAOJ,KAAI,YAAII,EAAOH,MAPpBG,EAAOJ,KAGLI,EAAOJ,KAFL,GAPJI,EAAOH,KAGLG,EAAOH,KAFL,IAePM,EAAY,CAChBC,MAAOC,IAAUC,OACjBC,SAAUF,IAAUG,KACpBC,cAAeJ,IAAUG,KACzBE,MAAOL,IAAUM,MACjBC,gBAAiBP,IAAUG,KAC3BK,wBAAyBR,IAAUG,KACnCM,gBAAiBT,IAAUG,KAC3BO,gBAAiBV,IAAUM,MAC3BK,UAAWX,IAAUC,OACrBW,SAAUZ,IAAUa,QAGhBC,EAAoB,SAAH,GAWjB,IAVJf,EAAK,EAALA,MACAG,EAAQ,EAARA,SACAE,EAAa,EAAbA,cACAC,EAAK,EAALA,MACAE,EAAe,EAAfA,gBACAC,EAAuB,EAAvBA,wBACAC,EAAe,EAAfA,gBACAC,EAAe,EAAfA,gBACAC,EAAS,EAATA,UACAC,EAAQ,EAARA,SAEMG,EAAuB,SAACC,EAAaC,EAASC,GAClD,IAAIC,EAAU,GAOd,OANIH,IACFG,GAAW,mBAETF,IACFE,GAAWD,GAENC,GAGHC,EAAwBV,GAAmBA,EAAgB,GAAKA,EAAgB,GAAKpB,EACJ,IAAvD+B,mBAAS3B,EAAuB0B,IAAuB,GAAhFF,EAAQ,KAAEI,EAAW,KACkC,IAAdD,oBAAS,GAAK,GAAvDE,EAAgB,KAAEC,EAAmB,KACU,IAAdH,oBAAS,GAAK,GAA/CI,EAAY,KAAEC,EAAe,KACkB,IAAdL,oBAAS,GAAK,GAA/CM,EAAY,KAAEC,EAAe,KAEyB,IAAjBP,mBAAS,SAAQ,GAAtDQ,EAAmB,KAAEC,EAAY,KACjCC,EAAqB,EAAhBC,cAAgB,GAApB,GAEFC,EAAMlB,EAAqBQ,EAAkBE,EAAcP,GACN,IAAbG,mBAASY,GAAI,GAApDC,EAAe,KAAEC,EAAkB,KAEiB,IAAfd,oBAAS,GAAM,GAApDe,EAAc,KAAEC,EAAiB,KAClCC,GAAaC,iBAAO,MACpBC,GAAcC,IAAKC,iBACnBC,IAAkB,IAAIC,MAAOC,iBAE7BC,GAAqB,SAACC,GACtBT,GAAWU,UAAYV,GAAWU,QAAQC,SAASF,EAAMG,SAC3Db,GAAkB,IAItBc,qBAAU,WAOR,OANIf,EACFrE,SAASqF,iBAAiB,QAASN,IAEnC/E,SAASsF,oBAAoB,QAASP,IAGjC,WACL/E,SAASsF,oBAAoB,QAASP,OAEvC,CAACV,IAEJ,IAAMkB,GAAqB,WACzBjB,GAAmBD,IAUfmB,GAAYhB,mBACZiB,GAAqBjB,mBACrBkB,GAAWlB,mBAEXmB,GAAe,SAACC,EAAOC,GAA+B,IAArBC,EAAW,UAAH,6CAAG9D,EAC1C+D,EAAa,CACjBC,OAAQR,GAAUP,QAClBW,QACAC,WACAI,MAAO,IACPC,OAAQ,IACRC,MAAOL,EAASK,MAChBC,UAAWN,EAASM,UACpBC,aAAcZ,GAAmBR,QACjCqB,KAAMR,EAASQ,KACfC,KAAMT,EAASS,KACfC,OAAQV,EAASU,OACjBC,UAAWX,EAASW,UACpBC,UAAWZ,EAASY,WAGhBT,EAAQrD,EAAU+D,gBAAgBZ,GAClCa,EAAUpB,GAAUP,QAAQ4B,YAElC1E,EAAS,EAAD,KACH2D,GAAQ,IACXG,QACAL,QACAC,WACAK,OAAQH,EAAWG,OACnBU,cAWJxB,qBAAU,WACRO,GAAa7B,EAAqBK,EAAiBnC,KAClD,CAACa,IAEJ,IAuCMiE,GAA4B,SAACC,GACjCpB,GAAa7B,EAAqBK,EAAiB,EAAF,KAC5CnC,GAAK,QACP+E,GAAS/E,EAAM+E,OASdC,GAAkBhD,EAAE,qCAEpBiD,GAAc,SAACC,GACnB,OAAIA,GAAcA,EAAWC,EACpBD,EAAWE,cAAcC,cAE3BC,IAAqB,OAExBC,GAAa,SAACC,EAAQC,GAC1B,IAAMC,EAAkB,SAATD,EACfjF,IAoBAmF,cAAkBC,SAASC,GAAGxC,iBAAiByC,IAAOC,oBAnBvB,SAAzBC,EAA0BC,GAC9B,MAA+BA,EAAEC,OAAzBC,EAAO,EAAPA,QAASC,EAAS,EAATA,UACjB,GAAgB,qBAAZD,IAAmCC,EAAW,CAChD,IAAMjC,EAAQ1D,IACd,GAAI0D,EAAO,CACT,IAAMkC,EAAiBpB,GAAYd,GACnCuB,EAASY,GAAaD,GAAkBE,GAAmBF,GAC3D,IAAMG,EAAa1I,OAAO2I,KAAKC,MAAMC,sBAAsBjB,EAAS,cAAgB,eACpFc,EAAW3H,KAAKwH,GAChBX,EAASkB,GAAcJ,GAAcK,GAAoBL,GACzDxG,EAAQ,EAAH,KACAA,GAAK,QACP0F,EAAS,YAAc,QAAUW,IAEpC1C,GAAa7B,EAAqBK,EAAiBnC,GAErD2F,cAAkBC,SAASC,GAAGvC,oBAAoBwC,IAAOC,mBAAoBC,QAK7Ec,GAAc,SAACrB,GACnB,IAAMC,EAAkB,SAATD,EACf/E,GAAgB,WACd,IAAMqG,EAAYrB,EAChBsB,GAAWC,QAAO,SAAC9C,GAAK,OAAKA,IAAU+C,MACvCC,GAAiBF,QAAO,SAAC9C,GAAK,OAAKA,IAAUiD,MAC/C1B,EAASkB,GAAcG,GAAaF,GAAoBE,GACxDrB,EAAS2B,GAAwB,MAAQC,GAA8B,MACvExJ,OAAO2I,KAAKC,MAAMC,sBAAsBjB,EAAS,cAAgB,eAAiBqB,MAI0B,KAAhEzF,mBAASxD,OAAO2I,KAAKC,MAAMC,sBAAmC,aAAE,GAAzGQ,GAAgB,MAAEN,GAAmB,MACoF,KAAlFvF,mBAAStB,EAAMmE,OAASrG,OAAO2I,KAAKC,MAAMC,sBAAmC,YAAE,IAAG,GAAzHY,GAAe,MAAEhB,GAAkB,MACwC,KAAdjF,mBAAS,MAAK,GAA3E8F,GAA0B,MAAEE,GAA6B,MAEoC,KAAhEhG,mBAASxD,OAAO2I,KAAKC,MAAMC,sBAAmC,aAAE,GAA7FK,GAAU,MAAEJ,GAAa,MACqE,KAAnEtF,mBAASxD,OAAO2I,KAAKC,MAAMC,sBAAmC,YAAE,IAAG,GAA9FvC,GAAS,MAAEkC,GAAY,MACwC,KAAdhF,mBAAS,MAAK,GAA/D4F,GAAoB,MAAEG,GAAuB,MAG9CG,GAAwB,SAACC,GAC7BnB,GAAamB,GACbzH,EAAQ,EAAH,KACAA,GAAK,IACRoE,UAAWqD,IAEb9D,GAAa7B,EAAqBK,EAAiBnC,IAK/C0H,GAA8B,SAACD,GACnClB,GAAmBkB,GACnBzH,EAAQ,EAAH,KACAA,GAAK,IACRmE,MAAOsD,IAET9D,GAAa7B,EAAqBK,EAAiBnC,IAI/C2H,GAAchH,GAAmB,CAACpB,GAClCqI,GAAwBC,MAAMC,KAAK,IAAIC,IAC3CJ,GAAYK,KAAI,SAACpI,GAAM,OAAKD,EAAuBC,EAAQ8B,EAAcE,QACxEqF,QAAO,SAACrH,GAAM,MAAgB,KAAXA,KAGtB,OACE,yBAAKqI,UAAU,oBACb,yBAAKA,UAAU,mBAAmBC,IAAKzE,IACrC,4BACEwE,UAAU,sBACVC,IAAK1E,GACL2E,KAAM,MACNC,aAAA,UAAepG,EAAE,8CAA6C,YAAIF,EAAmB,aAAKW,GAAW,YAAIG,OAG7G,yBAAKqF,UAAU,oBACb,yBAAKA,UAAU,yBACb,2BAAOI,QAAQ,iBAAiBJ,UAAU,eAAc,IAAEjD,GAAgB,KAC1E,2BACEsD,GAAG,iBACHL,UAAWM,IAAW,yBAA0B,CAAE,OAAUzG,IAC5DoG,IAAKxE,GACL+B,KAAK,OACL2C,aAAYpD,GACZwD,MAAO1G,EACP2G,SAlKgB,SAACxC,GACzB,IAAMuC,EAAQvC,EAAE9C,OAAOqF,OAAS,GAChCzG,EAAayG,GACbnI,GAAemI,GACf7E,GAAa6E,EAAOrG,OAgKZL,GAAuB,kBAAC4G,EAAA,EAAI,CAACC,MAAM,aAAaV,UAAU,aAAaE,KAAK,iBAC9E,yBAAKF,UAAU,oBAAoBW,YAAU,cACzC9G,GAAuB,uBAAGmG,UAAU,aAAajG,EAAE,oCAGzD,yBAAKiG,UAAU,kBACb,yBAAKA,UAAU,iBAAiBK,GAAG,kCAAiC,IAAEtG,EAAE,qCAAqC,KAC7G,yBAAKiG,UAAU,wBACb,kBAACY,EAAA,EAAQ,CACPP,GAAG,oBACHQ,aAAa,iCACbC,MAAOzI,EACP0I,UAAWhH,EAAE,qCACbiH,YA7Ia,SAAC3E,GACxBX,GAAa7B,EAAqBK,EAAiB,EAAF,KAC5CnC,GAAK,IACRsE,WA2IQ4E,oBAAqBlJ,EAAMsE,MAAQhE,EAAM,GACzC6I,mBAAoB,SAACC,GAAI,MAAM,CAAEC,WAAYD,IAC7CE,UAAW,MAEb,kBAACC,EAAA,EAAM,CACLC,YAAY,sBACZC,QA/HO,WAAH,OAAS3E,GAA0B,SAgIvC4E,IAAI,iBACJ9F,MAAM,uBACN+F,SAAU3J,EAAMuE,KAChBqF,YAAa5J,EAAMuE,OAErB,kBAACgF,EAAA,EAAM,CACLC,YAAY,wBACZC,QAtIS,WAAH,OAAS3E,GAA0B,WAuIzC4E,IAAI,mBACJ9F,MAAM,yBACN+F,SAAU3J,EAAMwE,OAChBoF,YAAa5J,EAAMwE,SAErB,kBAAC+E,EAAA,EAAM,CACLC,YAAY,2BACZC,QA5IY,WAAH,OAAS3E,GAA0B,cA6I5C4E,IAAI,2BACJ9F,MAAM,4BACN+F,SAAU3J,EAAMyE,UAChBmF,YAAa5J,EAAMyE,YAErB,kBAAC8E,EAAA,EAAM,CACLC,YAAY,2BACZC,QArJY,WAAH,OAAS3E,GAA0B,cAsJ5C4E,IAAI,4CACJ9F,MAAM,4BACN+F,SAAU3J,EAAM0E,UAChBkF,YAAa5J,EAAM0E,cAIzB,yBAAKuD,UAAU,mBACb,yBAAKK,GAAG,yBAAyBL,UAAU,kBACxCjG,EAAE,sCAEL,yBAAKiG,UAAU,0BACb,kBAAC4B,EAAA,EAAkB,CACjB5E,YAAaA,GACbd,MAAOC,GACP0F,oBAAqBzC,GACrB0C,iBAAkB7C,GAClB8C,aAAchD,GACdiD,cAAe3D,GACf4D,mBAAoB1C,GACpB2C,cAAe3C,GACfhH,gBApHgB,SAACgF,GAAM,OAAKD,GAAWC,EAAQ,SAqH/C9E,gBA5GkB,WAAH,OAASoG,GAAY,SA6GpCsD,eAAgB,yBAChBC,gBAAiB,EACjBC,cAAY,EACZC,YAAU,EACVC,cAAY,MAIlB,yBAAKvC,UAAU,mBACb,yBAAKK,GAAG,+BAA+BL,UAAU,kBAC9CjG,EAAE,4CAEL,yBAAKiG,UAAU,0BACb,kBAAC4B,EAAA,EAAkB,CACjB5E,YAAaA,GACbd,MAAOoD,GACPuC,oBAAqBxC,GACrByC,iBAAkB3C,GAClB4C,aAAc7C,GACd8C,cAAe1D,GACf2D,mBAAoBxC,GACpByC,cAAezC,GACflH,gBAjIsB,SAACgF,GAAM,OAAKD,GAAWC,EAAQ,SAkIrD9E,gBAzHwB,WAAH,OAASoG,GAAY,SA0H1CsD,eAAgB,+BAChBC,gBAAiB,EACjBC,cAAY,EACZC,YAAU,EACVC,cAAY,MAIlB,yBAAKvC,UAAU,uBACb,yBAAKK,GAAG,kBAAkBL,UAAU,kBACjCjG,EAAE,0CAEL,yBAAKiG,UAAU,mBAAmBE,KAAK,QAAQsC,kBAAgB,mBAC7D,kBAACC,EAAA,EAAM,CACLpC,GAAG,mBACHqC,QAASnJ,EACTiH,SAnPmB,WAC7BhH,GAAqBD,GACrB,IAAMU,EAAMlB,GAAsBQ,EAAkBE,EAAcP,GAClEiB,EAAmBF,GACnByB,GAAa7B,EAAqBI,IAgPxB0I,MAAO5I,EAAE,sCAEX,kBAAC0I,EAAA,EAAM,CACLpC,GAAG,eACHqC,QAASjJ,EACT+G,SA3QqB,WAC/B9G,GAAiBD,GACjB,IAAMmJ,EAAclL,EAAuB0B,GAAwBK,EAAcE,GACjFL,EAAYsJ,GACZ,IAAM3I,EAAMlB,EAAqBQ,GAAoBE,GAAgBE,EAAeiJ,GACpFzI,EAAmBF,GACnByB,GAAa7B,EAAqBI,IAsQxB0I,MAAO5I,EAAE,kCAEX,kBAAC0I,EAAA,EAAM,CACLpC,GAAG,eACHqC,QAAS/I,EACT6G,SAxQqB,WAC/B5G,GAAiBD,GACjB,IAAMiJ,EAAclL,EAAuB0B,EAAuBK,GAAeE,GACjFL,EAAYsJ,GACZ,IAAM3I,EAAMlB,EAAqBQ,EAAmBE,IAAiBE,EAAeiJ,GACpFzI,EAAmBF,GACnByB,GAAa7B,EAAqBI,IAmQxB0I,MAAO5I,EAAE,qCAIbN,GAAgBE,IAAiB,yBAAKqG,UAAU,yBAChD,yBAAKA,UAAU,iBAAiBK,GAAG,kCAAkCtG,EAAE,uCACvE,4BACEiG,UAAU,aACVC,IAAK3F,GACLkH,QAASlG,GACT6E,aAAA,UAAepG,EAAE,6CACjByD,KAAK,SACLqF,SAAS,IACTC,UAnVY,SAAC/H,GACH,UAAdA,EAAMgI,KAAiC,MAAdhI,EAAMgI,MACjChI,EAAMiI,iBACN1H,OAiVMwB,MAAO,CACLmG,WAAY,OACZC,OAAQ,OACRC,QAAS,EACTC,QAAS,OACTC,WAAY,aACZC,OAAQ,YAGV,kBAAC7C,EAAA,EAAI,CAACC,MAAM,cACXtG,GACE,yBAAK4F,UAAU,2BACd,yBAAKA,UAAU,oBAAmB,OAAKjG,EAAE,kCACzC,yBAAKiG,UAAU,oBAAmB,OAAKjG,EAAE,gCACzC,yBAAKiG,UAAU,oBAAmB,OAAKjG,EAAE,iCACzC,yBAAKiG,UAAU,oBAAmB,OAAKjG,EAAE,gCAAgC,WACzE,yBAAKiG,UAAU,oBAAmB,OAAKjG,EAAE,gCAAgC,WACzE,yBAAKiG,UAAU,oBAAmB,OAAKjG,EAAE,mCACzC,yBAAKiG,UAAU,oBAAmB,OAAKjG,EAAE,mCACzC,yBAAKiG,UAAU,oBAAmB,eAKxC,kBAACY,EAAA,EAAQ,CACPP,GAAG,oCACHQ,aAAa,iCACbC,MAAOnB,GACPoB,UAAS,UAAKhH,EAAE,sCAAqC,cAAMb,GAC3D+H,oBAAqB/H,EACrB8H,YA9RiB,SAACuC,GAC1BjK,EAAYiK,GACZ,IAAMtJ,EAAMlB,EAAqBQ,EAAmBE,GAAgBE,EAAe4J,GACnFpJ,EAAmBF,GACnByB,GAAa7B,EAAqBI,IA2R1BoH,UAAW,UAQvBvI,EAAkBhB,UAAYA,EAEfgB,Q,yiCC/ef,8lGAAApD,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,+kBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,IAAAA,IAAA,ygBAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAcA,IACM8N,EAAa3N,OAAO2I,KAAKC,MAAMC,sBAAmC,YA2HzD+E,EAzHU,WACvB,IAA2F,IAAjEpK,mBAAS,CAAEgD,KAAM,YAAaC,MAAM,EAAMJ,MAAOsH,EAAW,KAAK,GAApFzL,EAAK,KAAEG,EAAQ,KAChBwL,EAAiBjJ,IAAKkJ,+BALZ,+BAMT5J,EAAqB,EAAhBC,cAAgB,GAApB,GACF4J,EAAQC,cACqC,IAAfxK,oBAAS,GAAM,GAA5CyK,EAAU,KAAE1L,EAAa,KAM9B,IALiD2L,aAAY,SAAChM,GAAK,MAAK,CACxEiM,IAAUC,cAAclM,EAAOmM,IAAaC,oBAC5CH,IAAUI,SAASrM,GACnBiM,IAAUK,mBAAmBtM,GAC7BiM,IAAUM,YAAYvM,OACtB,GALKwM,EAAM,KAAElM,EAAK,KAAEK,EAAe,KAAEE,EAAQ,KAMzC4L,EAAWC,cAEjBtJ,qBAAU,WACJoJ,GACF9J,IAAKiK,2BAEN,CAACH,IAEJ,IAAMI,EAAaC,aAAgB,WACjCJ,EAASK,IAAQC,aAAaZ,IAAaC,wBAavC1L,EAAe,6BAAG,WAAOsM,GAAS,6EAChCC,EAAUjL,EAAE,qCACZ4B,EAAQ5B,EAAE,mCACVkL,EAAiBlL,EAAE,aAEnBmL,EAAU,CACdF,UACArJ,QACAsJ,iBACAF,aAEFP,EAASK,IAAQM,mBAAmBD,IAAU,2CAC/C,gBAZoB,sCAcfE,EAAa9E,IAAW,CAC5B+E,OAAO,EACP5B,kBAAkB,EAClB6B,KAAMf,EACNgB,QAAShB,IAGLiB,EAAiB,6BAAG,8FACxB/K,IAAKgL,YA3DS,+BA2Dc,IACJ/B,GAAc,yDACJ,OADvB/K,EAAS,SACR+M,eAAe3N,GAAO,SACZY,EAAUgN,4BAA4B5N,GAAM,OAArD,OAAL6N,EAAQ,EAAH,eACLjN,EAAUkN,eAAeD,GAAM,QACrCjN,EAAUmN,cAAc,+IAE1BtB,EAASK,IAAQC,aAAaZ,IAAaC,qBACrC4B,EAAqBrC,EAAe,GAAGsC,oBAAoB5P,OAC3D6P,EAAmBvC,EAAe,GAAGwC,kBAAkB9P,OAC7DoO,EAASK,IAAQsB,sBAAsBJ,EAAqBE,EAAmB,IAAI,gEACpF,kBAZsB,mCAcjBG,EAA2BxB,aAAgB,WAC3Cd,GAGJ0B,OAGF,OACEjB,EACE,yBACEvE,UAAWoF,EACXiB,eAAcnC,IAAaC,oBAE3B,kBAACmC,EAAA,EAAY,CACX3K,MAAO5B,EAAE,qCACTwM,aAAc5B,EACd6B,aAAc7B,EACdJ,OAAQA,EACRkC,cAAY,GAEZ,yBAAKzG,UAAU,YAAY0G,YAAa,SAAC1I,GAAC,OAAKA,EAAE2I,oBAC/C,kBAAC,EAAiB,CAChBlO,gBAAiBA,EACjBD,wBAhEoB,WAC9B,IAAMoO,EAAc5C,IAAU6C,eAAejD,EAAMkD,YAEnD,OADAtC,EAASK,IAAQkC,eAAe,OACzBH,GA8DGrO,gBArEY,WACtBiM,EAASK,IAAQmC,YAAY,sBAqEnBC,YAAa1C,EACbxM,MAAOA,EACPG,SAAUA,EACVyM,WAAYA,EACZvM,cAAeA,EACfC,MAAOA,EACPK,gBAAiBA,EACjBC,UAAW+K,EAAe,GAC1B9K,SAAUA,IAEZ,yBAAKoH,UAAU,UACb,kBAACsB,EAAA,EAAM,CACLqB,MAAO5I,EAAE,iBACT4B,MAAO5B,EAAE,iBACTyH,QAAS4E,EACTc,SAAUpD,EACV9D,UAAU,qBAOpB,MCrISmH", "file": "chunks/chunk.51.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./CustomStampForums.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.text-customstamp .color-container .ColorPalette .cell-tool:hover{cursor:pointer;border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6);background-color:var(--faded-component-background)}.text-customstamp{padding:16px;margin:0;display:flex;flex-direction:column}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp{width:100%;padding:4px 8px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .scroll-container{width:100%;max-height:50%;padding-bottom:0;margin-bottom:0;overflow-y:auto;overflow-x:visible}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp{width:100%;padding:4px 8px}.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .scroll-container{width:100%;max-height:50%;padding-bottom:0;margin-bottom:0;overflow-y:auto;overflow-x:visible}}.text-customstamp-input{outline:none;border-radius:4px;border:1px solid var(--gray-6);box-shadow:none;padding:5px}.text-customstamp .txt-uppercase{text-transform:uppercase}.text-customstamp-container{position:relative;margin-top:10px;flex:1;border:1px solid var(--modal-stroke-and-border);border-radius:4px;overflow:auto}.text-customstamp-container canvas{position:absolute;visibility:hidden;z-index:-1;width:100%;height:100%}.text-customstamp-inner-container{position:absolute;min-width:100%}.text-customstamp .canvas-container{border:1px solid var(--gray-6);box-sizing:border-box;position:relative;overflow:hidden;background-color:var(--signature-draw-background);display:flex;flex-direction:row;justify-content:center;padding:48px 74px;border-radius:4px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .canvas-container{padding:12px .5px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .canvas-container{padding:12px .5px}}.text-customstamp .canvas-container .custom-stamp-canvas{box-sizing:border-box}@media(max-width:430px){.text-customstamp .canvas-container .custom-stamp-canvas{transform:scale(.8)}}.text-customstamp .color-container{margin-top:16px;width:100%}.text-customstamp .color-container .ColorPalette{display:flex;flex-wrap:wrap;width:100%;grid-gap:4px;gap:4px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .color-container .ColorPalette{padding:4px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .color-container .ColorPalette{padding:4px}}.text-customstamp .color-container .ColorPalette .cell-outer{margin-right:16px}.text-customstamp .color-container .ColorPalette .cell-outer.active{margin-left:-4px;margin-right:10px;width:28px;height:28px}.text-customstamp .color-container .ColorPalette .cell-outer .cell{width:16px;height:16px}.text-customstamp .color-container .ColorPalette .cell-container{flex:unset;width:auto;height:auto}.text-customstamp .color-container .ColorPalette .cell-tool{margin-right:8px;margin-left:-4px}.text-customstamp .color-container .ColorPalette .cell-tool .cell-outer{margin:4px}.text-customstamp .color-container .ColorPalette .cell-tool.active{box-shadow:inset 0 0 0 1px var(--blue-5);background:var(--tools-button-active)}.text-customstamp .color-container .ColorPalette .cell-tool.active .Icon{color:var(--blue-5)}.text-customstamp .color-container .ColorPalette .cell-tool:disabled{box-shadow:none;background:transparent}.text-customstamp .stamp-input-container{margin-top:16px;padding:0;position:relative}.text-customstamp .stamp-input-container .text-customstamp-input{margin-top:8px;height:32px;width:100%;padding:6px}.text-customstamp .stamp-input-container .text-customstamp-input.error{border:1px solid var(--error-text-color)}.text-customstamp .stamp-input-container .error-icon{position:absolute;right:8px;top:28px;color:var(--error-text-color)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .stamp-input-container .error-icon{top:30px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .stamp-input-container .error-icon{top:30px}}.text-customstamp .stamp-input-container .empty-stamp-input{height:16px;margin-top:4px;color:var(--error-text-color)}.text-customstamp .stamp-label,.text-customstamp .stamp-sublabel{font-weight:700;margin-bottom:8px;text-transform:capitalize}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .stamp-label,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .stamp-sublabel{font-size:13px;height:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .stamp-label,.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .stamp-sublabel{font-size:13px;height:16px}}.text-customstamp .timestamp-container{margin-top:16px;padding:0}.text-customstamp .timestamp-container .timeStamp-choice{height:16px;display:grid;width:100%;grid-template-columns:22.32% 22.32% 22.32% 33.04%;grid-gap:16px}@media(max-width:430px){.text-customstamp .timestamp-container .timeStamp-choice{grid-template-columns:30% 30% 30%;height:100%;font-size:13px}}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.text-customstamp .timestamp-container .timeStamp-choice .ui__choice{display:-ms-inline-flexbox;width:33.33%}}.text-customstamp .timestamp-container .timeStamp-choice .ui__choice{margin:0;line-height:1}.text-customstamp .timestamp-container .timeStamp-choice .ui__choice .ui__choice__label{padding-left:4px}.text-customstamp .date-format-container{margin-top:16px;display:flex;flex-wrap:wrap}.text-customstamp .date-format-container .Dropdown__wrapper{width:100%}.text-customstamp .date-format-container .Dropdown__wrapper .Dropdown{height:32px;width:100%!important;text-align:left}.text-customstamp .date-format-container .Dropdown__wrapper .arrow{flex:unset}.text-customstamp .date-format-container .Dropdown__wrapper .Dropdown__items{z-index:101;top:auto;left:0;right:auto;width:100%!important}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .date-format-container .Dropdown__wrapper .Dropdown__items{max-height:200px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .date-format-container .Dropdown__wrapper .Dropdown__items{max-height:200px}}.text-customstamp .date-format-container .hover-icon{margin-left:2px;position:relative}.text-customstamp .date-format-container .hover-icon .Icon{width:18px;height:18px}.text-customstamp .date-format-container .hover-icon .date-format-description{display:flex;flex-direction:column;text-align:left;position:absolute;background-color:#000;border-radius:5px;padding:12px;color:#fff;width:120px;z-index:101;bottom:100%;top:auto;left:0;right:auto}.text-customstamp .date-format-container .hover-icon:hover .Icon{color:var(--blue-6)}.text-customstamp .font-container{margin-top:8px}.text-customstamp .font-container .font-inner-container{display:flex;grid-gap:12px;gap:12px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .font-container .font-inner-container{grid-gap:6px;gap:6px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .font-container .font-inner-container{grid-gap:6px;gap:6px}}.text-customstamp .font-container .stamp-sublabel{font-weight:700;margin-bottom:8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .font-container .stamp-sublabel{font-size:13px;height:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .font-container .stamp-sublabel{font-size:13px;height:16px}}.text-customstamp .font-container .Dropdown__wrapper .Dropdown{margin-right:4px;height:32px;width:268px!important;text-align:left}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .font-container .Dropdown__wrapper .Dropdown{width:200px!important}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .font-container .Dropdown__wrapper .Dropdown{width:200px!important}}.text-customstamp .font-container .Dropdown__wrapper .arrow{flex:unset}.text-customstamp .font-container .Dropdown__wrapper .Dropdown__items{top:auto;left:0;right:auto;width:268px!important}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .font-container .Dropdown__wrapper .Dropdown__items{bottom:auto;height:200px;width:200px!important}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .font-container .Dropdown__wrapper .Dropdown__items{bottom:auto;height:200px;width:200px!important}}.text-customstamp .font-container .Button{background:none;width:32px;height:32px;margin:0}.text-customstamp .font-container .Button .Icon{height:24px;width:24px;color:var(--icon-color)}.text-customstamp .font-container .Button:hover{background:var(--popup-button-hover)}.text-customstamp .font-container .Button:hover .Icon .cls-1{fill:currentColor}.text-customstamp .font-container .Button.active{background:var(--popup-button-active)}.text-customstamp .font-container .Button.active .Icon{color:var(--blue-5)}.text-customstamp .font-container .Button.active .Icon .cls-1{fill:currentColor}.text-customstamp .custom-checkbox{position:relative;-webkit-user-select:none;-moz-user-select:none;user-select:none;margin-bottom:5px;margin-top:5px}.text-customstamp .custom-checkbox input{display:none}.text-customstamp .custom-checkbox input+label:before{cursor:pointer;content:\\\"\\\";margin:1px 10px 0 0;display:inline-block;vertical-align:text-top;width:16px;height:16px;border-radius:4px;border:1px solid #979797}.text-customstamp .custom-checkbox input:checked+label:before{background:#00a5e4;border:1px solid transparent}.text-customstamp .custom-checkbox input:checked+label:after{content:\\\"\\\";position:absolute;left:4px;top:9px;background:var(--gray-0);width:2px;height:2px;box-shadow:2px 0 0 var(--gray-0),4px 0 0 var(--gray-0),4px -2px 0 var(--gray-0),4px -4px 0 var(--gray-0),4px -6px 0 var(--gray-0),4px -8px 0 var(--gray-0);transform:rotate(45deg)}.text-customstamp .custom-checkbox label{align-items:center}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./CreateStampModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.CustomStampModal{visibility:visible}.closed.CustomStampModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.CustomStampModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.CustomStampModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.CustomStampModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.CustomStampModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.CustomStampModal .footer .modal-button.cancel:hover,.CustomStampModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.CustomStampModal .footer .modal-button.cancel,.CustomStampModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.CustomStampModal .footer .modal-button.cancel.disabled,.CustomStampModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.CustomStampModal .footer .modal-button.cancel.disabled span,.CustomStampModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.CustomStampModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.CustomStampModal .modal-container .wrapper .modal-content{padding:10px}.CustomStampModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.CustomStampModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.CustomStampModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.CustomStampModal .footer .modal-button.confirm{margin-left:4px}.CustomStampModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CustomStampModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CustomStampModal .footer .modal-button{padding:23px 8px}}.CustomStampModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CustomStampModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .CustomStampModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CustomStampModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CustomStampModal .swipe-indicator{width:32px}}.CustomStampModal .modal-container{display:flex;flex-direction:column;width:480px;padding:0;border-radius:4px;background:var(--component-background);overflow:visible;max-height:100%}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CustomStampModal .modal-container{width:100%;border-radius:0;left:0;bottom:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CustomStampModal .modal-container{width:100%;border-radius:0;left:0;bottom:0}}.CustomStampModal .modal-container .header{box-shadow:inset 0 -1px 0 var(--modal-stroke-and-border);margin:0;display:flex;align-items:center;width:100%;padding:16px}.CustomStampModal .modal-container .header p{font-size:16px;font-weight:700;width:89.286%;margin:0 16px 0 0}.CustomStampModal .modal-container .header .Button{position:static;height:32px;width:32px;border-radius:4px}.CustomStampModal .modal-container .header .Button:hover{background:var(--popup-button-hover)}.CustomStampModal .modal-container .header .Button.selected{background:var(--popup-button-active);cursor:default}.CustomStampModal .modal-container .footer{display:flex;align-items:center;flex-direction:row-reverse;justify-content:space-between;margin:0;top:460px;padding:16px;box-shadow:inset 0 1px 0 var(--modal-stroke-and-border)}.CustomStampModal .modal-container .footer .stamp-close{border:none;background-color:transparent;color:var(--secondary-button-text);padding:0 8px;height:32px;display:flex;align-items:center;justify-content:center;cursor:pointer}:host(:not([data-tabbing=true])) .CustomStampModal .modal-container .footer .stamp-close,html:not([data-tabbing=true]) .CustomStampModal .modal-container .footer .stamp-close{outline:none}.CustomStampModal .modal-container .footer .stamp-close:hover{color:var(--secondary-button-hover)}.CustomStampModal .modal-container .footer .stamp-create{border:none;background-color:transparent;background:var(--primary-button);border-radius:4px;height:32px;display:flex;align-items:center;padding:8px 16px;width:72px;justify-content:center;position:relative;color:var(--primary-button-text);cursor:pointer}:host(:not([data-tabbing=true])) .CustomStampModal .modal-container .footer .stamp-create,html:not([data-tabbing=true]) .CustomStampModal .modal-container .footer .stamp-create{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CustomStampModal .modal-container .footer .stamp-create{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CustomStampModal .modal-container .footer .stamp-create{font-size:13px}}.CustomStampModal .modal-container .footer .stamp-create:hover{background:var(--primary-button-hover)}.CustomStampModal .modal-container .footer .stamp-create:disabled{background:var(--primary-button);opacity:.5;cursor:default}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState, useEffect, useRef } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport Choice from 'components/Choice';\nimport Dropdown from 'components/Dropdown';\nimport Button from 'components/Button';\nimport Icon from 'components/Icon';\nimport ColorPalettePicker from 'components/ColorPalettePicker/ColorPalettePicker'; // ColorPalletPicker inner import required as we are not using the redux outer container\nimport Events from 'constants/events';\nimport PropTypes from 'prop-types';\nimport classNames from 'classnames';\nimport core from 'core';\n\nimport './CustomStampForums.scss';\nimport { getInstanceNode } from 'helpers/getRootNode';\nimport { COMMON_COLORS } from 'constants/commonColors';\n\nconst FALLBACK_DATE_TIME_FORMAT = {\n  date: 'MM/DD/YYYY',\n  time: 'hh:mm A',\n  timeFirst: false,\n};\n\nconst dateTimeFormatToString = (format, useDate = true, useTime = true) => {\n  if (!useDate) {\n    if (!format.time) {\n      return '';\n    }\n    return format.time;\n  }\n  if (!useTime) {\n    if (!format.date) {\n      return '';\n    }\n    return format.date;\n  }\n  return format.timeFirst ?\n    `${format.time} ${format.date}` :\n    `${format.date} ${format.time}`;\n};\n\nconst propTypes = {\n  state: PropTypes.object,\n  setState: PropTypes.func,\n  setEmptyInput: PropTypes.func,\n  fonts: PropTypes.array,\n  openColorPicker: PropTypes.func,\n  getCustomColorAndRemove: PropTypes.func,\n  openDeleteModal: PropTypes.func,\n  dateTimeFormats: PropTypes.array,\n  stampTool: PropTypes.object,\n  userName: PropTypes.string,\n};\n\nconst CustomStampForums = ({\n  state,\n  setState,\n  setEmptyInput,\n  fonts,\n  openColorPicker,\n  getCustomColorAndRemove,\n  openDeleteModal,\n  dateTimeFormats,\n  stampTool,\n  userName,\n}) => {\n  const updateTimestampLabel = (usernameChk, dateChk, dateTime) => {\n    let tmpText = '';\n    if (usernameChk) {\n      tmpText += '[$currentUser] ';\n    }\n    if (dateChk) {\n      tmpText += dateTime;\n    }\n    return tmpText;\n  };\n\n  const defaultDateTimeFormat = dateTimeFormats && dateTimeFormats[0] ? dateTimeFormats[0] : FALLBACK_DATE_TIME_FORMAT;\n  const [dateTime, setDateTime] = useState(dateTimeFormatToString(defaultDateTimeFormat));\n  const [usernameCheckbox, setUsernameCheckbox] = useState(true);\n  const [dateCheckbox, setDateCheckbox] = useState(true);\n  const [timeCheckbox, setTimeCheckbox] = useState(true);\n\n  const [stampTextInputValue, setStampText] = useState('Draft');\n  const [t] = useTranslation();\n\n  const txt = updateTimestampLabel(usernameCheckbox, dateCheckbox, dateTime);\n  const [timestampFormat, setTimestampFormat] = useState(txt);\n\n  const [tooltipVisible, setTooltipVisible] = useState(false);\n  const tooltipRef = useRef(null);\n  const currentUser = core.getCurrentUser();\n  const currentDateTime = new Date().toLocaleString();\n\n  const handleClickOutside = (event) => {\n    if (tooltipRef.current && !tooltipRef.current.contains(event.target)) {\n      setTooltipVisible(false);\n    }\n  };\n\n  useEffect(() => {\n    if (tooltipVisible) {\n      document.addEventListener('click', handleClickOutside);\n    } else {\n      document.removeEventListener('click', handleClickOutside);\n    }\n\n    return () => {\n      document.removeEventListener('click', handleClickOutside);\n    };\n  }, [tooltipVisible]);\n\n  const handleTooltipClick = () => {\n    setTooltipVisible(!tooltipVisible);\n  };\n\n  const handleKeyDown = (event) => {\n    if (event.key === 'Enter' || event.key === ' ') {\n      event.preventDefault(); // Prevent default action for space key\n      handleTooltipClick();\n    }\n  };\n\n  const canvasRef = useRef();\n  const canvasContainerRef = useRef();\n  const inputRef = useRef();\n\n  const updateCanvas = (title, subtitle, newState = state) => {\n    const parameters = {\n      canvas: canvasRef.current,\n      title,\n      subtitle,\n      width: 300,\n      height: 100,\n      color: newState.color,\n      textColor: newState.textColor,\n      canvasParent: canvasContainerRef.current,\n      font: newState.font,\n      bold: newState.bold,\n      italic: newState.italic,\n      underline: newState.underline,\n      strikeout: newState.strikeout,\n    };\n\n    const width = stampTool.drawCustomStamp(parameters);\n    const dataURL = canvasRef.current.toDataURL();\n\n    setState({\n      ...newState,\n      width,\n      title,\n      subtitle,\n      height: parameters.height,\n      dataURL,\n    });\n  };\n\n  const handleInputChange = (e) => {\n    const value = e.target.value || '';\n    setStampText(value);\n    setEmptyInput(!value);\n    updateCanvas(value, timestampFormat);\n  };\n\n  useEffect(() => {\n    updateCanvas(stampTextInputValue, timestampFormat, state);\n  }, [userName]);\n\n  const handleDateCheckboxChange = () => {\n    setDateCheckbox(!dateCheckbox);\n    const newDateTime = dateTimeFormatToString(defaultDateTimeFormat, !dateCheckbox, timeCheckbox);\n    setDateTime(newDateTime);\n    const txt = updateTimestampLabel(usernameCheckbox, (!dateCheckbox || timeCheckbox), newDateTime);\n    setTimestampFormat(txt);\n    updateCanvas(stampTextInputValue, txt);\n  };\n\n  const handleTimeCheckboxChange = () => {\n    setTimeCheckbox(!timeCheckbox);\n    const newDateTime = dateTimeFormatToString(defaultDateTimeFormat, dateCheckbox, !timeCheckbox);\n    setDateTime(newDateTime);\n    const txt = updateTimestampLabel(usernameCheckbox, (dateCheckbox || !timeCheckbox), newDateTime);\n    setTimestampFormat(txt);\n    updateCanvas(stampTextInputValue, txt);\n  };\n\n  const handleUsernameCheckbox = () => {\n    setUsernameCheckbox(!usernameCheckbox);\n    const txt = updateTimestampLabel(!usernameCheckbox, dateCheckbox, dateTime);\n    setTimestampFormat(txt);\n    updateCanvas(stampTextInputValue, txt);\n  };\n\n  const handleFontChange = (font) => {\n    updateCanvas(stampTextInputValue, timestampFormat, {\n      ...state,\n      font,\n    });\n  };\n\n  const onDateFormatChange = (newFormat) => {\n    setDateTime(newFormat);\n    const txt = updateTimestampLabel(usernameCheckbox, (dateCheckbox || timeCheckbox), newFormat);\n    setTimestampFormat(txt);\n    updateCanvas(stampTextInputValue, txt);\n  };\n\n  const handleRichTextStyleChange = (style) => {\n    updateCanvas(stampTextInputValue, timestampFormat, {\n      ...state,\n      [style]: !state[style],\n    });\n  };\n\n  const toggleBold = () => handleRichTextStyleChange('bold');\n  const toggleItalic = () => handleRichTextStyleChange('italic');\n  const toggleStrikeout = () => handleRichTextStyleChange('strikeout');\n  const toggleUnderline = () => handleRichTextStyleChange('underline');\n\n  const stampInputLabel = t('option.customStampModal.stampText');\n\n  const getHexColor = (givenColor) => {\n    if (givenColor && givenColor.A) {\n      return givenColor.toHexString().toLowerCase();\n    }\n    return COMMON_COLORS['black'];\n  };\n  const openPicker = (addNew, type) => {\n    const isText = type === 'text';\n    openColorPicker();\n    const handleVisiblityChanged = (e) => {\n      const { element, isVisible } = e.detail;\n      if (element === 'ColorPickerModal' && !isVisible) {\n        const color = getCustomColorAndRemove();\n        if (color) {\n          const colorToBeAdded = getHexColor(color);\n          isText ? setTextColor(colorToBeAdded) : setBackgroundColor(colorToBeAdded);\n          const toolColors = window.Core.Tools.RubberStampCreateTool[isText ? 'TEXT_COLORS' : 'FILL_COLORS'];\n          toolColors.push(colorToBeAdded);\n          isText ? setTextColors(toolColors) : setBackgroundColors(toolColors);\n          state = {\n            ...state,\n            [isText ? 'textColor' : 'color']: colorToBeAdded,\n          };\n          updateCanvas(stampTextInputValue, timestampFormat, state);\n        }\n        getInstanceNode().instance.UI.removeEventListener(Events.VISIBILITY_CHANGED, handleVisiblityChanged);\n      }\n    };\n    getInstanceNode().instance.UI.addEventListener(Events.VISIBILITY_CHANGED, handleVisiblityChanged);\n  };\n  const deleteColor = (type) => {\n    const isText = type === 'text';\n    openDeleteModal(() => {\n      const newColors = isText ?\n        textColors.filter((color) => color !== textColorToBeDeleted) :\n        backgroundColors.filter((color) => color !== backgroundColorToBeDeleted);\n      isText ? setTextColors(newColors) : setBackgroundColors(newColors);\n      isText ? setTextColorToBeDeleted(null) : setBackgroundColorToBeDeleted(null);\n      window.Core.Tools.RubberStampCreateTool[isText ? 'TEXT_COLORS' : 'FILL_COLORS'] = newColors;\n    });\n  };\n\n  const [backgroundColors, setBackgroundColors] = useState(window.Core.Tools.RubberStampCreateTool['FILL_COLORS']);\n  const [backgroundColor, setBackgroundColor] = useState(state.color || window.Core.Tools.RubberStampCreateTool['FILL_COLORS'][0]);\n  const [backgroundColorToBeDeleted, setBackgroundColorToBeDeleted] = useState(null);\n\n  const [textColors, setTextColors] = useState(window.Core.Tools.RubberStampCreateTool['TEXT_COLORS']);\n  const [textColor, setTextColor] = useState(window.Core.Tools.RubberStampCreateTool['TEXT_COLORS'][0]);\n  const [textColorToBeDeleted, setTextColorToBeDeleted] = useState(null);\n\n  const openColorPickerText = (addNew) => openPicker(addNew, 'text');\n  const handleTextColorChange = (newColor) => {\n    setTextColor(newColor);\n    state = {\n      ...state,\n      textColor: newColor,\n    };\n    updateCanvas(stampTextInputValue, timestampFormat, state);\n  };\n  const handleTextColorDelete = () => deleteColor('text');\n\n  const openColorPickerBackground = (addNew) => openPicker(addNew, 'fill');\n  const handleBackgroundColorChange = (newColor) => {\n    setBackgroundColor(newColor);\n    state = {\n      ...state,\n      color: newColor,\n    };\n    updateCanvas(stampTextInputValue, timestampFormat, state);\n  };\n  const handleBackgroundColorDelete = () => deleteColor('fill');\n\n  const formatsList = dateTimeFormats || [FALLBACK_DATE_TIME_FORMAT];\n  const dateTimeDropdownItems = Array.from(new Set(\n    formatsList.map((format) => dateTimeFormatToString(format, dateCheckbox, timeCheckbox))\n  )).filter((format) => format !== '');\n\n\n  return (\n    <div className=\"text-customstamp\">\n      <div className=\"canvas-container\" ref={canvasContainerRef}>\n        <canvas\n          className=\"custom-stamp-canvas\"\n          ref={canvasRef}\n          role={'img'}\n          aria-label={`${t('option.customStampModal.previewCustomStamp')} ${stampTextInputValue}, ${currentUser} ${currentDateTime}`}\n        />\n      </div>\n      <div className=\"scroll-container\">\n        <div className=\"stamp-input-container\">\n          <label htmlFor=\"stampTextInput\" className=\"stamp-label\"> {stampInputLabel}*</label>\n          <input\n            id=\"stampTextInput\"\n            className={classNames('text-customstamp-input', { 'error': !stampTextInputValue })}\n            ref={inputRef}\n            type=\"text\"\n            aria-label={stampInputLabel}\n            value={stampTextInputValue}\n            onChange={handleInputChange}\n          />\n          {!stampTextInputValue && <Icon glyph=\"icon-alert\" className=\"error-icon\" role=\"presentation\" />}\n          <div className=\"empty-stamp-input\" aria-live=\"assertive\" >\n            {!stampTextInputValue && <p className=\"no-margin\">{t('message.emptyCustomStampInput')}</p>}\n          </div>\n        </div>\n        <div className=\"font-container\">\n          <div className=\"stamp-sublabel\" id=\"custom-stamp-font-family-label\"> {t('option.customStampModal.fontStyle')} </div>\n          <div className=\"font-inner-container\">\n            <Dropdown\n              id='custom-stamp-font'\n              labelledById='custom-stamp-font-family-label'\n              items={fonts}\n              ariaLabel={t('option.customStampModal.fontStyle')}\n              onClickItem={handleFontChange}\n              currentSelectionKey={state.font || fonts[0]}\n              getCustomItemStyle={(item) => ({ fontFamily: item })}\n              maxHeight={200}\n            />\n            <Button\n              dataElement=\"stampTextBoldButton\"\n              onClick={toggleBold}\n              img=\"icon-menu-bold\"\n              title=\"option.richText.bold\"\n              isActive={state.bold}\n              ariaPressed={state.bold}\n            />\n            <Button\n              dataElement=\"stampTextItalicButton\"\n              onClick={toggleItalic}\n              img=\"icon-menu-italic\"\n              title=\"option.richText.italic\"\n              isActive={state.italic}\n              ariaPressed={state.italic}\n            />\n            <Button\n              dataElement=\"stampTextUnderlineButton\"\n              onClick={toggleUnderline}\n              img=\"icon-menu-text-underline\"\n              title=\"option.richText.underline\"\n              isActive={state.underline}\n              ariaPressed={state.underline}\n            />\n            <Button\n              dataElement=\"stampTextStrikeoutButton\"\n              onClick={toggleStrikeout}\n              img=\"icon-tool-text-manipulation-strikethrough\"\n              title=\"option.richText.strikeout\"\n              isActive={state.strikeout}\n              ariaPressed={state.strikeout}\n            />\n          </div>\n        </div>\n        <div className=\"color-container\">\n          <div id='stamp-text-color-label' className=\"stamp-sublabel\">\n            {t('option.customStampModal.textColor')}\n          </div>\n          <div className=\"colorpalette-container\">\n            <ColorPalettePicker\n              getHexColor={getHexColor}\n              color={textColor}\n              setColorToBeDeleted={setTextColorToBeDeleted}\n              colorToBeDeleted={textColorToBeDeleted}\n              customColors={textColors}\n              onStyleChange={setTextColor}\n              handleColorOnClick={handleTextColorChange}\n              handleOnClick={handleTextColorChange}\n              openColorPicker={openColorPickerText}\n              openDeleteModal={handleTextColorDelete}\n              ariaLabelledBy={'stamp-text-color-label'}\n              toolTipXOffset={-7}\n              disableTitle\n              enableEdit\n              colorsAreHex\n            />\n          </div>\n        </div>\n        <div className=\"color-container\">\n          <div id=\"stamp-background-color-label\" className=\"stamp-sublabel\">\n            {t('option.customStampModal.backgroundColor')}\n          </div>\n          <div className=\"colorpalette-container\">\n            <ColorPalettePicker\n              getHexColor={getHexColor}\n              color={backgroundColor}\n              setColorToBeDeleted={setBackgroundColorToBeDeleted}\n              colorToBeDeleted={backgroundColorToBeDeleted}\n              customColors={backgroundColors}\n              onStyleChange={setBackgroundColor}\n              handleColorOnClick={handleBackgroundColorChange}\n              handleOnClick={handleBackgroundColorChange}\n              openColorPicker={openColorPickerBackground}\n              openDeleteModal={handleBackgroundColorDelete}\n              ariaLabelledBy={'stamp-background-color-label'}\n              toolTipXOffset={-7}\n              disableTitle\n              enableEdit\n              colorsAreHex\n            />\n          </div>\n        </div>\n        <div className=\"timestamp-container\">\n          <div id=\"timestamp-label\" className=\"stamp-sublabel\">\n            {t('option.customStampModal.timestampText')}\n          </div>\n          <div className=\"timeStamp-choice\" role=\"group\" aria-labelledby=\"timestamp-label\">\n            <Choice\n              id=\"default-username\"\n              checked={usernameCheckbox}\n              onChange={handleUsernameCheckbox}\n              label={t('option.customStampModal.Username')}\n            />\n            <Choice\n              id=\"default-date\"\n              checked={dateCheckbox}\n              onChange={handleDateCheckboxChange}\n              label={t('option.customStampModal.Date')}\n            />\n            <Choice\n              id=\"default-time\"\n              checked={timeCheckbox}\n              onChange={handleTimeCheckboxChange}\n              label={t('option.customStampModal.Time')}\n            />\n          </div>\n        </div>\n        {(dateCheckbox || timeCheckbox) && <div className=\"date-format-container\">\n          <div className=\"stamp-sublabel\" id=\"custom-stamp-date-format-label\">{t('option.customStampModal.dateFormat')}</div>\n          <button\n            className=\"hover-icon\"\n            ref={tooltipRef}\n            onClick={handleTooltipClick}\n            aria-label={`${t('option.customStampModal.dateToolTipLabel')}`}\n            type=\"button\"\n            tabIndex=\"0\"\n            onKeyDown={handleKeyDown}\n            style={{\n              background: 'none',\n              border: 'none',\n              padding: 0,\n              display: 'flex',\n              alignItems: 'flex-start', // Aligns the content to the top\n              cursor: 'pointer'\n            }}\n          >\n            <Icon glyph=\"icon-info\" />\n            {tooltipVisible &&\n              (<div className=\"date-format-description\">\n                <div className=\"date-format-cell\">M = {t('option.customStampModal.month')}</div>\n                <div className=\"date-format-cell\">D = {t('option.customStampModal.day')}</div>\n                <div className=\"date-format-cell\">Y = {t('option.customStampModal.year')}</div>\n                <div className=\"date-format-cell\">H = {t('option.customStampModal.hour')} (24hr)</div>\n                <div className=\"date-format-cell\">h = {t('option.customStampModal.hour')} (12hr)</div>\n                <div className=\"date-format-cell\">m = {t('option.customStampModal.minute')}</div>\n                <div className=\"date-format-cell\">s = {t('option.customStampModal.second')}</div>\n                <div className=\"date-format-cell\">A = AM/PM</div>\n              </div>\n              )\n            }\n          </button>\n          <Dropdown\n            id='custom-stamp-date-format-dropdown'\n            labelledById='custom-stamp-date-format-label'\n            items={dateTimeDropdownItems}\n            ariaLabel={`${t('option.customStampModal.dateFormat')} - ${dateTime}`}\n            currentSelectionKey={dateTime}\n            onClickItem={onDateFormatChange}\n            maxHeight={200}\n          />\n        </div>}\n      </div>\n    </div>\n  );\n};\n\nCustomStampForums.propTypes = propTypes;\n\nexport default CustomStampForums;\n", "import React, { useEffect, useState } from 'react';\nimport classNames from 'classnames';\nimport { useSelector, useDispatch, useStore } from 'react-redux';\nimport core from 'core';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport { useTranslation } from 'react-i18next';\nimport CustomStampForums from './CustomStampForums';\nimport Button from 'components/Button';\nimport DataElements from 'constants/dataElement';\nimport ModalWrapper from 'components/ModalWrapper';\nimport useFocusOnClose from 'hooks/useFocusOnClose';\n\nimport './CreateStampModal.scss';\n\nconst TOOL_NAME = 'AnnotationCreateRubberStamp';\nconst fillColors = window.Core.Tools.RubberStampCreateTool['FILL_COLORS'];\n\nconst CustomStampModal = () => {\n  const [state, setState] = useState({ font: 'Helvetica', bold: true, color: fillColors[0] });\n  const stampToolArray = core.getToolsFromAllDocumentViewers(TOOL_NAME);\n  const [t] = useTranslation();\n  const store = useStore();\n  const [emptyInput, setEmptyInput] = useState(false);\n  const [isOpen, fonts, dateTimeFormats, userName] = useSelector((state) => [\n    selectors.isElementOpen(state, DataElements.CUSTOM_STAMP_MODAL),\n    selectors.getFonts(state),\n    selectors.getDateTimeFormats(state),\n    selectors.getUserName(state),\n  ]);\n  const dispatch = useDispatch();\n\n  useEffect(() => {\n    if (isOpen) {\n      core.deselectAllAnnotations();\n    }\n  }, [isOpen]);\n\n  const closeModal = useFocusOnClose(() => {\n    dispatch(actions.closeElement(DataElements.CUSTOM_STAMP_MODAL));\n  });\n\n  const openColorPicker = () => {\n    dispatch(actions.openElement('ColorPickerModal'));\n  };\n\n  const getCustomColorAndRemove = () => {\n    const customColor = selectors.getCustomColor(store.getState());\n    dispatch(actions.setCustomColor(null));\n    return customColor;\n  };\n\n  const openDeleteModal = async (onConfirm) => {\n    const message = t('warning.colorPicker.deleteMessage');\n    const title = t('warning.colorPicker.deleteTitle');\n    const confirmBtnText = t('action.ok');\n\n    const warning = {\n      message,\n      title,\n      confirmBtnText,\n      onConfirm,\n    };\n    dispatch(actions.showWarningMessage(warning));\n  };\n\n  const modalClass = classNames({\n    Modal: true,\n    CustomStampModal: true,\n    open: isOpen,\n    closed: !isOpen,\n  });\n\n  const createCustomStamp = async () => {\n    core.setToolMode(TOOL_NAME);\n    for (const stampTool of stampToolArray) {\n      stampTool.addCustomStamp(state);\n      const annot = await stampTool.createCustomStampAnnotation(state);\n      await stampTool.setRubberStamp(annot);\n      stampTool.showPreview();\n    }\n    dispatch(actions.closeElement(DataElements.CUSTOM_STAMP_MODAL));\n    const standardStampCount = stampToolArray[0].getStandardStamps().length;\n    const customStampCount = stampToolArray[0].getCustomStamps().length;\n    dispatch(actions.setSelectedStampIndex(standardStampCount + customStampCount - 1));\n  };\n\n  const onCreateCustomStampClick = useFocusOnClose(() => {\n    if (emptyInput) {\n      return;\n    }\n    createCustomStamp();\n  });\n\n  return (\n    isOpen ?\n      <div\n        className={modalClass}\n        data-element={DataElements.CUSTOM_STAMP_MODAL}\n      >\n        <ModalWrapper\n          title={t('option.customStampModal.modalName')}\n          closeHandler={closeModal}\n          onCloseClick={closeModal}\n          isOpen={isOpen}\n          swipeToClose\n        >\n          <div className=\"container\" onMouseDown={(e) => e.stopPropagation()}>\n            <CustomStampForums\n              openDeleteModal={openDeleteModal}\n              getCustomColorAndRemove={getCustomColorAndRemove}\n              openColorPicker={openColorPicker}\n              isModalOpen={isOpen}\n              state={state}\n              setState={setState}\n              closeModal={closeModal}\n              setEmptyInput={setEmptyInput}\n              fonts={fonts}\n              dateTimeFormats={dateTimeFormats}\n              stampTool={stampToolArray[0]}\n              userName={userName}\n            />\n            <div className=\"footer\">\n              <Button\n                label={t('action.create')}\n                title={t('action.create')}\n                onClick={onCreateCustomStampClick}\n                disabled={emptyInput}\n                className=\"stamp-create\"\n              />\n            </div>\n          </div>\n        </ModalWrapper>\n      </div>\n      :\n      null\n  );\n};\n\nexport default CustomStampModal;\n", "import CreateStampModal from './CreateStampModal';\n\nexport default CreateStampModal;\n"], "sourceRoot": ""}