{"version": 3, "sources": ["webpack:///./src/ui/src/components/ModularComponents/PresetButton/buttons/SheetEditor/CellAdjustmentButton.js"], "names": ["propTypes", "type", "PropTypes", "string", "isFlyoutItem", "bool", "style", "object", "className", "CellAdjustmentButton", "forwardRef", "props", "ref", "dispatch", "useDispatch", "menuItems", "dataElement", "icon", "title", "handleClick", "actions", "setFlyoutToggleElement", "toggleElement", "DataElements", "CELL_ADJUSTMENT_FLYOUT", "onClick", "additionalClass", "key", "isActive", "img", "ariaPressed", "displayName"], "mappings": "maASA,IAAMA,EAAY,CAChBC,KAAMC,IAAUC,OAChBC,aAAcF,IAAUG,KACxBC,MAAOJ,IAAUK,OACjBC,UAAWN,IAAUC,QAGjBM,EAAuBC,sBAAW,SAACC,EAAOC,GAC9C,IAAQR,EAAyCO,EAAzCP,aAAcH,EAA2BU,EAA3BV,KAAMK,EAAqBK,EAArBL,MAAOE,EAAcG,EAAdH,UAC7BK,EAAWC,cAGjB,EAAqCC,IAA0B,eAAvDC,EAAW,EAAXA,YAAaC,EAAI,EAAJA,KAAMC,EAAK,EAALA,MAErBC,EAAc,WAElBN,EAASO,IAAQC,uBAAuBL,IACxCH,EAASO,IAAQE,cAAcC,IAAaC,0BAG9C,OACEpB,EACE,kBAAC,IAAmB,KACdO,EAAK,CACTC,IAAKA,EACLa,QAASN,EACTO,gBAAuC,MAGvC,kBAAC,IAAY,CACXC,IAAK1B,EACL2B,UArBS,EAsBTH,QAASN,EACTH,YAAaA,EACbE,MAAOA,EACPW,IAAKZ,EACLa,aA1BS,EA2BTxB,MAAOA,EACPE,UAAWA,OAMrBC,EAAqBT,UAAYA,EACjCS,EAAqBsB,YAAc,uBAEpBtB", "file": "chunks/chunk.89.js", "sourcesContent": ["import React, { forwardRef } from 'react';\nimport { useDispatch } from 'react-redux';\nimport ActionButton from 'components/ActionButton';\nimport DataElements from 'constants/dataElement';\nimport PropTypes from 'prop-types';\nimport actions from 'actions';\nimport FlyoutItemContainer from '../../../FlyoutItemContainer';\nimport { menuItems } from '../../../Helpers/menuItems';\n\nconst propTypes = {\n  type: PropTypes.string,\n  isFlyoutItem: PropTypes.bool,\n  style: PropTypes.object,\n  className: PropTypes.string,\n};\n\nconst CellAdjustmentButton = forwardRef((props, ref) => {\n  const { isFlyoutItem, type, style, className } = props;\n  const dispatch = useDispatch();\n  const isActive = false;\n\n  const { dataElement, icon, title } = menuItems['cellAdjustment'];\n\n  const handleClick = () => {\n    // handle button click\n    dispatch(actions.setFlyoutToggleElement(dataElement));\n    dispatch(actions.toggleElement(DataElements.CELL_ADJUSTMENT_FLYOUT));\n  };\n\n  return (\n    isFlyoutItem ?\n      <FlyoutItemContainer\n        {...props}\n        ref={ref}\n        onClick={handleClick}\n        additionalClass={isActive ? 'active' : ''}\n      />\n      : (\n        <ActionButton\n          key={type}\n          isActive={isActive}\n          onClick={handleClick}\n          dataElement={dataElement}\n          title={title}\n          img={icon}\n          ariaPressed={isActive}\n          style={style}\n          className={className}\n        />\n      )\n  );\n});\n\nCellAdjustmentButton.propTypes = propTypes;\nCellAdjustmentButton.displayName = 'CellAdjustmentButton';\n\nexport default CellAdjustmentButton;\n"], "sourceRoot": ""}