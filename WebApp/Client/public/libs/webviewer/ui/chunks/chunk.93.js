(window.webpackJsonp=window.webpackJsonp||[]).push([[93],{1972:function(e,t,a){"use strict";a.r(t);a(97),a(36),a(16),a(60),a(44);var n=a(0),s=a.n(n),c=a(84),i=a(4),o=a.n(i),r=a(68),l=a(71),p=a(149);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e}).apply(this,arguments)}var y={actionType:o.a.oneOf(Object.values(p.a)).isRequired,isFlyoutItem:o.a.bool,style:o.a.object,className:o.a.string},d=Object(n.forwardRef)((function(e,t){var a=e.isFlyoutItem,n=e.actionType,i=e.style,o=e.className,p="cell".concat(n.charAt(0).toUpperCase()).concat(n.slice(1)),y=l.b[p],d=y.dataElement,f=y.icon,b=y.title,m=function(){};return a?s.a.createElement(r.a,u({},e,{ref:t,onClick:m,additionalClass:""})):s.a.createElement(c.a,{key:n,isActive:!1,onClick:m,dataElement:d,title:b,img:f,ariaPressed:!1,style:i,className:o})}));d.propTypes=y,d.displayName="CopyPasteCutButton",t.default=d}}]);
//# sourceMappingURL=chunk.93.js.map