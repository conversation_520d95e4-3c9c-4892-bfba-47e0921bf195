{"version": 3, "sources": ["webpack:///./src/ui/src/components/StylePicker/ColorPicker/ColorPicker.js", "webpack:///./src/ui/src/components/StylePicker/ColorPicker/index.js", "webpack:///./src/ui/src/components/StylePicker/ColorPicker/ColorPicker.scss?02e7", "webpack:///./src/ui/src/components/StylePicker/ColorPicker/ColorPicker.scss", "webpack:///./src/ui/src/components/RichTextStyleEditor/RichTextStyleEditor.scss?fe54", "webpack:///./src/ui/src/components/RichTextStyleEditor/RichTextStyleEditor.scss", "webpack:///./src/ui/src/components/RichTextStyleEditor/RichTextStyleEditor.js", "webpack:///./src/ui/src/components/RichTextStyleEditor/index.js", "webpack:///./src/ui/src/components/StylePicker/StylePicker.scss?c539", "webpack:///./src/ui/src/components/StylePicker/StylePicker.scss", "webpack:///./src/ui/src/components/StylePanel/StylePanel.scss?64d9", "webpack:///./src/ui/src/components/StylePanel/StylePanel.scss", "webpack:///./src/ui/src/components/StylePicker/SnapModeToggle/index.js", "webpack:///./src/ui/src/components/StylePicker/SnapModeToggle/SnapModeToggle.js", "webpack:///./src/ui/src/helpers/stylePanelHelper.js", "webpack:///./src/ui/src/components/StylePicker/StrokePanelSection/StrokePanelSection.js", "webpack:///./src/ui/src/components/StylePicker/OpacityPanelSection/OpacityPanelSection.js", "webpack:///./src/ui/src/components/StylePicker/OpacityPanelSection/index.js", "webpack:///./src/ui/src/components/StylePicker/StylePicker.js", "webpack:///./src/ui/src/components/StylePicker/index.js", "webpack:///./src/ui/src/components/StylePanel/StylePanel.js", "webpack:///./src/ui/src/components/StylePanel/StylePanelContainer.js", "webpack:///./src/ui/src/components/StylePanel/index.js"], "names": ["parseColor", "color", "parsedColor", "toHexString", "toLowerCase", "transparentIcon", "width", "height", "className", "classNames", "stroke", "x1", "y1", "x2", "y2", "strokeWidth", "strokeLinecap", "propTypes", "PropTypes", "any", "ariaTypeLabel", "string", "ColorPicker", "onColorChange", "hasTransparentColor", "activeTool", "type", "activeToolName", "Object", "values", "window", "Core", "Tools", "ToolNames", "includes", "EDIT", "store", "useStore", "t", "useTranslation", "dispatch", "useDispatch", "colors", "useSelector", "state", "selectors", "getColors", "useState", "selectedColor", "setSelectedColor", "isExpanded", "setIsExpanded", "forceExpandRef", "useRef", "useEffect", "current", "getCustomColorAndRemove", "customColor", "getCustomColor", "getState", "actions", "setCustomColor", "handleAddColor", "useCallback", "openElement", "getInstanceNode", "addEventListener", "Events", "VISIBILITY_CHANGED", "onVisibilityChanged", "e", "detail", "element", "isVisible", "newColors", "setColors", "removeEventListener", "length", "openColorPickerModalWithFocus", "useFocusHandler", "palette", "map", "push", "indexOf", "shouldHideShowMoreButton", "showCopyButtonDisabled", "isDeleteDisabled", "slice", "i", "<PERSON><PERSON><PERSON>", "content", "toUpperCase", "key", "onClick", "aria-label", "aria-current", "active", "cell", "border", "style", "backgroundColor", "<PERSON><PERSON>", "img", "title", "dataElement", "aria<PERSON><PERSON><PERSON>", "indexToDelete", "nextIndex", "splice", "disabled", "hidden", "api", "__esModule", "default", "module", "options", "styleTag", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "annotation", "object", "editor", "shape", "TextColor", "RichTextStyle", "isFreeTextAutoSize", "bool", "onFreeTextSizeToggle", "func", "onPropertyChange", "onRichTextStyleChange", "isRedaction", "isRichTextEditMode", "setIsRichTextEditMode", "isWidget", "RichTextStyleEditor", "fonts", "getFonts", "shallowEqual", "format", "setFormat", "editor<PERSON><PERSON>", "annotationRef", "propertiesRef", "oldSelectionRef", "richTextEditModeRef", "handleSelectionChange", "range", "oldRange", "setSelection", "index", "getFormat", "handleTextChange", "getSelection", "core", "disableElements", "DataElements", "ANNOTATION_STYLE_POPUP", "enableElements", "StrokeStyle", "err", "console", "error", "stylesTemp", "getRichTextStyle", "Font", "FontSize", "TextAlign", "TextVerticalAlign", "bold", "italic", "underline", "strikeout", "size", "font", "calculatedFontSize", "getCalculatedFontSize", "handleEditorBlur", "handleEditorFocus", "properties", "Annotations", "Color", "Array", "isArray", "lastSelectedColor", "prop", "undefined", "applyFormat", "formatKey", "value", "handlePropertyChange", "property", "blur", "adjustFreeTextBoundingBox", "setTimeout", "getAnnotationManager", "getEditBoxManager", "focusBox", "defaults", "strike", "quillFont", "quillFontSize", "originalSize", "commonProps", "stateless", "isFreeText", "nonWidgetProps", "propertyTranslation", "freeText", "isAutoSized", "resizeAnnotation", "newSelection", "currentFormat", "handleTextFormatChange", "widgetProps", "onMouseDown", "preventDefault", "TextStylePicker", "name", "handleColorChange", "React", "memo", "SnapModeToggle", "Scale", "Precision", "isSnapModeEnabled", "wasDocumentSwappedToClientSide", "getDocument", "getType", "workerTypes", "WEBVIEWER_SERVER", "isWebViewerServerDocument", "isEligibleDocumentForSnapping", "PDF", "showMeasurementSnappingOption", "isFullPDFEnabled", "Choice", "id", "label", "i18next", "checked", "onChange", "event", "enableSnapping", "target", "mode", "getDocumentViewer", "SnapMode", "e_DefaultSnapMode", "POINT_ON_LINE", "getMeasurementTools", "tool", "setSnapMode", "setEnableSnapMode", "toolName", "isEnabled", "shouldHideStylePanelOptions", "AddParagraphTool", "AddImageContentTool", "CropCreateTool", "SnippingCreateTool", "some", "getTool", "shouldHideOpacitySlider", "RedactionCreateTool", "EraserTool", "TextFormFieldCreateTool", "ListBoxFormFieldCreateTool", "ComboBoxFormFieldCreateTool", "SignatureFormFieldCreateTool", "CheckBoxFormFieldCreateTool", "RadioButtonFormFieldCreateTool", "shouldHideTransparentFillColor", "stylePanelSectionTitles", "section", "toolTitles", "shouldRenderWidgetLayout", "TEXT_FORM_FIELD", "LIST_BOX_FIELD", "COMBO_BOX_FIELD", "shouldShowNoStyles", "annotations", "filteredTypes", "types", "isInstanceOfAny", "StrokePanelSection", "showFillColorAndCollapsablePanelSections", "isStamp", "onStrokeColorChange", "onStyleChange", "strokeColor", "hideStrokeSlider", "strokethicknessComponent", "showLineStyleOptions", "renderSlider", "strokeStyle", "isInFormFieldCreationMode", "isFreeHand", "isArc", "onStartLineStyleChange", "startingLineStyle", "isStyleOptionDisabled", "onStrokeStyleChange", "strokeLineStyle", "middleLineSegmentLabel", "isEllipse", "withCloudyStyle", "onEndLineStyleChange", "endingLineStyle", "defaultStartLineStyles", "defaultStrokeStyles", "defaultEndLineStyles", "openStrokeStyleContainer", "isStrokeStyleContainerActive", "sectionContent", "Dropdown", "translationPrefix", "images", "onClickItem", "currentSelectionKey", "showLabelInList", "CollapsibleSection", "header", "headingLevel", "isInitiallyExpanded", "onToggle", "shouldShowHeading", "oneOfType", "node", "array", "OpacityPanelSection", "isOpacityContainerActive", "openOpacityContainer", "concat", "cloudyStrokeStyle", "activeType", "endLineStyle", "handleRichTextStyleChange", "isTextStylePickerHidden", "onLineStyleChange", "isRequired", "redactionLabelProperties", "sliderProperties", "arrayOf", "startLineStyle", "StylePicker", "saveEditorInstance", "StrokeColor", "setStrokeColor", "setStartingLineStyle", "setEndingLineStyle", "setStrokeLineStyle", "FillColor", "fillColor", "setFillColor", "hideStrokeStyle", "RubberStampCreateTool", "StampCreateTool", "RectangleCreateTool", "EllipseCreateTool", "PolygonCreateTool", "PolygonCloudCreateTool", "EllipseMeasurementCreateTool", "AreaMeasurementCreateTool", "FreeTextCreateTool", "CalloutCreateTool", "hasFillColorAndCollapsablePanelSections", "hideFillColorAndCollapsablePanelSections", "shouldHideFillColorAndCollapsablePanelSections", "TextUnderlineCreateTool", "TextHighlightCreateTool", "TextSquigglyCreateTool", "TextStrikeoutCreateTool", "CountMeasurementCreateTool", "FileAttachmentCreateTool", "StickyCreateTool", "MarkInsertTextCreateTool", "MarkReplaceTextCreateTool", "shouldHideStrokeSlider", "showSnapModeCheckbox", "DistanceMeasurementCreateTool", "ArcMeasurementCreateTool", "PerimeterMeasurementCreateTool", "RectangularAreaMeasurementCreateTool", "CloudyRectangularAreaMeasurementCreateTool", "hasSnapModeCheckbox", "showTextStyle", "shouldShowTextStyle", "RICH_TEXT_STYLE_CONTAINER", "STROKE_STYLE_CONTAINER", "closeElement", "ANNOTATION_POPUP", "onSliderChange", "Opacity", "StrokeThickness", "isElementDisabled", "STYLE_OPTION", "isElementOpen", "FILL_COLOR_CONTAINER", "OPACITY_CONTAINER", "isFillColorContainerActive", "isTextStyleContainerActive", "panelItems", "togglePanelItem", "shouldHideSliderTitle", "sliderProps", "displayProperty", "getDisplayValue", "Math", "round", "OPACITY_SLIDER", "withInputField", "inputFieldType", "min", "max", "step", "getLocalValue", "opacity", "parseInt", "getStrokeDisplayValue", "STROKE_THICKNESS_SLIDER", "steps", "getStrokeSliderSteps", "FONT_SIZE_SLIDER", "parseFloat", "toFixed", "getSliderProps", "Slide<PERSON>", "customCircleRadius", "customLineStrokeWidth", "renderDivider", "onOpenProps", "useOnFreeTextEdit", "openElements", "RICH_TEXT_EDITOR", "LabelTextEditor", "placeholderText", "colorMapKey", "StylePanel", "getToolButtonObjects", "isAnnotationToolStyleSyncingEnabled", "getActiveDocumentViewerKey", "isPanelOpen", "toolButtonObject", "activeDocumentViewerKey", "currentTool", "getToolMode", "currentToolName", "colorProperties", "showStyles", "setShowStyles", "noToolStyle", "setNoToolStyle", "setIsEllipse", "setIsFreeText", "REDACTION", "setIsRedaction", "defaultTool", "setIsWidget", "setIsFreeHand", "setIsArc", "setIsStamp", "setIsInFormFieldCreationMode", "setStyle", "setStartLineStyle", "setEndLineStyle", "setStrokeStyle", "panelTitle", "setPanelTitle", "annotationCreateToolNames", "getAnnotationCreateToolNames", "getDataWithKey", "mapToolNameToKey", "hasLineEndings", "setShowLineStyleOptions", "isAutoSizeFont", "setIsAutoSizeFont", "setActiveTool", "editorInstance", "setEditorInstance", "PushButtonWidgetAnnotation", "setToolMode", "updateSnapModeFromTool", "getSnapMode", "getPanelTitleOnAnnotationSelected", "annot", "isContentEditPlaceholder", "ToolName", "setPanelTitleForSelectedTool", "updateStylePanelProps", "extraStyles", "FreeTextAnnotation", "jsonData", "inputText", "uniqueFontFamilies", "Set", "uniqueFontSizes", "hasOwnProperty", "isNaN", "add", "trim", "from", "sizes", "extractUniqueFontFamilies", "getContents", "RedactionAnnotation", "OverlayText", "WidgetAnnotation", "getStartStyle", "getEndStyle", "dashes", "getStrokeStyle", "handleToolModeChange", "newTool", "ELLIPSE", "FREETEXT", "FREEHAND", "FREEHAND_HIGHLIGHT", "ARC", "STAMP", "getFormFieldCreationManager", "toolStyles", "getToolStyles", "StartLineStyle", "EndLineStyle", "onAnnotationSelected", "action", "EllipseAnnotation", "FreeHandAnnotation", "ArcAnnotation", "StampAnnotation", "handleMultipleAnnotations", "AnnotationEditTool", "handleDeselection", "onAnnotationsChanged", "getSelectedAnnotations", "selectedAnnotations", "setAnnotationType", "noToolSelected", "Icon", "glyph", "newStyle", "annotationsToUpdate", "hasFocus", "background", "colorRGB", "hexToRGBA", "r", "g", "b", "a", "setAnnotationStyles", "setToolStyles", "redrawAnnotation", "refresh", "sectionPropertyMap", "start", "middle", "end", "setStartStyle", "split", "lineStyle", "shift", "Style", "Dash<PERSON>", "setEndStyle", "trigger", "handleFreeTextAutoSizeToggle", "originalProperty", "originalValue", "activeToolRichTextStyle", "getTextDecoration", "richTextStyle", "0", "updateAnnotationRichTextStyle", "complete", "StylePanelContainer", "DataElementWrapper"], "mappings": "stDAcA,IAAMA,EAAa,SAACC,GAAU,QAC5B,IAAKA,EACH,OAAOA,EAET,IAAIC,EAAcD,EAQlB,OAPe,QAAf,EAAIC,SAAW,OAAX,EAAaC,cACfD,EAAcA,EAAYC,eAEb,QAAf,EAAID,SAAW,OAAX,EAAaE,cACfF,EAAcA,EAAYE,eAGrBF,GAMHG,EACJ,yBACEC,MAAM,OACNC,OAAO,OACPC,UAAWC,IAAW,gBAEtB,0BAAMC,OAAO,UAAUC,GAAG,IAAIC,GAAG,OAAOC,GAAG,OAAOC,GAAG,IAAIC,YAAY,IAAIC,cAAc,WAMrFC,EAAY,CAChBhB,MAAOiB,IAAUC,IACjBC,cAAeF,IAAUG,QAGrBC,EAAc,SAAH,GAOX,IANJC,EAAa,EAAbA,cAAa,IACbC,2BAAmB,IAAG,GAAK,EAC3BvB,EAAK,EAALA,MACAwB,EAAU,EAAVA,WACAC,EAAI,EAAJA,KACAN,EAAa,EAAbA,cAEMO,EAAiBC,OAAOC,OAAOC,OAAOC,KAAKC,MAAMC,WAAWC,SAAST,GAAcA,EAAaK,OAAOC,KAAKC,MAAMC,UAAUE,KAC5HC,EAAQC,cACNC,EAAMC,cAAND,EACFE,EAAWC,cACVC,EAEL,EAFeC,aAAY,SAACC,GAAK,MAAK,CACtCC,IAAUC,UAAUF,EAAOjB,EAAgBD,OAC3C,GAFW,GAGuC,IAAVqB,qBAAU,GAA7CC,EAAa,KAAEC,EAAgB,KACa,IAAfF,oBAAS,GAAM,GAA5CG,EAAU,KAAEC,EAAa,KAC1BC,EAAiBC,kBAAO,GAE9BC,qBAAU,WACRF,EAAeG,SAAU,IACxB,CAAC5B,EAAgB1B,IAEpBqD,qBAAU,WACJrD,GACFgD,EAAiBjD,EAAWC,MAE7B,CAACA,IAEJ,IAAMuD,EAA0B,WAC9B,IAAMC,EAAcZ,IAAUa,eAAetB,EAAMuB,YAEnD,OADAnB,EAASoB,IAAQC,eAAe,OACzBJ,GAGHK,EAAiBC,uBAAY,WACjCvB,EAASoB,IAAQI,YAAY,qBAmB7BC,cAAkBC,iBAAiBC,IAAOC,oBAlBd,SAAtBC,EAAuBC,GAC3B,MAA+BA,EAAEC,OAAzBC,EAAO,EAAPA,QAASC,EAAS,EAATA,UACjB,GAAgB,qBAAZD,IAAmCC,EAAW,CAChD,IAAMxE,EAAQD,EAAWwD,KACzB,GAAIvD,EACF,GAAIyC,EAAOR,SAASjC,GAClBgD,EAAiBhD,GACjBsB,EAActB,OACT,CACL,IAAMyE,EAAY,GAAH,SAAOhC,GAAM,CAAEzC,IAC9BuC,EAASoB,IAAQe,UAAUD,EAAW/C,EAAgBD,GAAM,IAC5DuB,EAAiBhD,GACjBsB,EAActB,GAGlBgE,cAAkBW,oBAAoBT,IAAOC,mBAAoBC,SAIpE,CAAC3B,aAAM,EAANA,EAAQmC,OAAQrC,EAAUS,EAAkB1B,EAAeiC,EAAyB9B,EAAMC,IAExFmD,EAAgCC,YAAgBjB,GA0BlDkB,EAAUtC,EAAOuC,KAAI,SAAChF,GAAK,OAAKA,EAAMG,iBACtCoB,GACFwD,EAAQE,KA1Gc,eA6GnBlC,GACHC,EAAiB,eAGf+B,EAAQG,QAAQnC,GAAiB,IAAME,GAAcE,EAAeG,UACtEJ,GAAc,GACdC,EAAeG,SAAU,GAG3B,IAAM6B,EAA2BJ,EAAQH,QAAU,EAC7CQ,IAA2BrC,IAAkBgC,EAAQ9C,SAASc,IAC9DsC,EAAmBN,EAAQH,QAAU,IAAMQ,EAMjD,OAJKnC,IACH8B,EAAUA,EAAQO,MAAM,EAAG,IAI3B,oCACE,yBAAK/E,UAAWC,IAAW,iBACxBuE,EAAQC,KAAI,SAAChF,GAAK,OAAKD,EAAWC,MAAQgF,KAAI,SAAChF,EAAOuF,GAAC,iBACrDvF,EAEG,kBAACwF,EAAA,EAAO,CAACC,QAAO,UAAKpD,EAAE,kCAAiC,YAAIrC,SAAkB,QAAb,EAALA,EAAO0F,mBAAW,WAAb,EAAL,OAAA1F,IAA0B2F,IAAK3F,SAAkB,QAAb,EAALA,EAAO0F,mBAAW,WAAb,EAAL,OAAA1F,IAC3F,4BACEO,UAAU,iBACVqF,QAAS,WACP5C,EAAiBhD,GACjBsB,EAActB,IAEhB6F,aAAA,UAAe1E,EAAa,YAAIkB,EAAE,kCAAiC,YAAIrC,SAAkB,QAAb,EAALA,EAAO0F,mBAAW,WAAb,EAAL,OAAA1F,IACvE8F,eAAc/F,EAAWgD,KAAmB/C,IAAWD,EAAWgD,IA5IxD,gBA4I0E/C,GAEpF,yBACEO,UAAWC,IAAW,CACpB,cAAc,EACduF,OAAQhG,EAAWgD,KAAmB/C,IAAWD,EAAWgD,IAjJtD,gBAiJwE/C,KAGhF,yBACEO,UAAWC,IAAW,CACpBwF,MAAM,EACNC,QAAQ,IAEVC,MAAO,CAAEC,gBAAiBnG,IAzJpB,gBA2JLA,GAA+BI,MAxBtC,yBAAKuF,IAAKJ,EAAGhF,UAAU,mBA+B/B,yBAAKA,UAAU,oBACb,yBAAKA,UAAU,oBACb,kBAAC6F,EAAA,EAAM,CACLC,IAAI,2BACJC,MAAOjE,EAAE,sBACTuD,QAASf,EACTtE,UAAU,iBACVgG,YAAY,iBACZC,UAAS,UAAKrF,EAAa,YAAIkB,EAAE,sBAAqB,YAAIA,EAAE,mCAE9D,kBAAC+D,EAAA,EAAM,CACLC,IAAI,mBACJC,MAAOjE,EAAE,sBACTuD,QA/FW,WACnB,IAAM5F,EAAQD,EAAWgD,GACnB0B,EAAY,EAAIhC,GAChBgE,EAAgBhC,EAAUS,QAAQlF,GACxC,GAAIyG,GAAiB,EAAG,CACtB,IAAMC,EAAYD,IAAkBhC,EAAUG,OAAS,EAAI,EAAI6B,EAAgB,EAC/EzD,EAAiBP,EAAOiE,IACxBpF,EAAcmB,EAAOiE,IACrBjC,EAAUkC,OAAOF,EAAe,GAChClE,EAASoB,IAAQe,UAAUD,EAAW/C,EAAgBD,GAAM,MAuFtDmF,SAAUvB,EACV9E,UAAU,iBACVgG,YAAY,sBACZC,UAAS,UAAKrF,EAAa,YAAIkB,EAAE,sBAAqB,YAAIU,KAE5D,kBAACqD,EAAA,EAAM,CACLC,IAAI,aACJC,MAAOjE,EAAE,4BACTuD,QA3Fc,WACtB,IAAM5F,EAAQD,EAAWgD,GACnB0B,EAAY,GAAH,SAAOhC,GAAM,CAAEzC,IAC9BuC,EAASoB,IAAQe,UAAUD,EAAW/C,EAAgBD,GAAM,KAyFpDmF,SAAUxB,EACV7E,UAAU,iBACVgG,YAAY,oBACZC,UAAS,UAAKrF,EAAa,YAAIkB,EAAE,4BAA2B,YAAIU,MAGpE,4BACExC,UAAWC,IAAW,kCAAmC,CACvDqG,OAAQ1B,IAEVS,QAhGe,WAErB1C,GADkBD,IAgGZ4C,aAAA,UAAe1E,EAAa,YAAIkB,EAAeA,EAAbY,EAAe,wBAA6B,4BAE7EZ,EAAEY,EAAa,mBAAqB,wBAO/C5B,EAAYL,UAAYA,EAETK,QC1OAA,O,qBCFf,IAAIyF,EAAM,EAAQ,IACFrB,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQsB,WAAatB,EAAQuB,QAAUvB,KAG/CA,EAAU,CAAC,CAACwB,EAAO1B,EAAIE,EAAS,MAG9C,IAAIyB,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKtF,OAAOuF,8BAEV,YADAC,SAASC,KAAKC,YAAYJ,GAI5B,IAAIK,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAc5C,SACjB4C,EAzBF,SAASE,EAAwBC,EAASC,EAAOP,UAC/C,MAAMQ,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAAS5C,KAAK+C,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGC,YACLJ,EAAS5C,QAAQyC,EAAwBC,EAASK,EAAGC,eAIlDJ,EAYSH,CAAwB,qBAG1C,MAAMQ,EAAkB,GACxB,IAAK,IAAI3C,EAAI,EAAGA,EAAIiC,EAAc5C,OAAQW,IAAK,CAC7C,MAAM4C,EAAeX,EAAcjC,GACnC,GAAU,IAANA,EACF4C,EAAaF,WAAWV,YAAYJ,GACpCA,EAASiB,OAAS,WACZF,EAAgBtD,OAAS,GAC3BsD,EAAgBH,QAASM,IAEvBA,EAAUC,UAAYnB,EAASmB,iBAIhC,CACL,MAAMD,EAAYlB,EAASoB,WAAU,GACrCJ,EAAaF,WAAWV,YAAYc,GACpCH,EAAgBjD,KAAKoD,MAIzC,WAAoB,GAEPvB,EAAIrB,EAASyB,GAI1BD,EAAOuB,QAAU/C,EAAQgD,QAAU,I,sBClEnCD,EAAUvB,EAAOuB,QAAU,EAAQ,GAAR,EAAqE,IAKxFvD,KAAK,CAACgC,EAAO1B,EAAI,yxKAA0xK,KAGnzKiD,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAI3B,EAAM,EAAQ,IACFrB,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQsB,WAAatB,EAAQuB,QAAUvB,KAG/CA,EAAU,CAAC,CAACwB,EAAO1B,EAAIE,EAAS,MAG9C,IAAIyB,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKtF,OAAOuF,8BAEV,YADAC,SAASC,KAAKC,YAAYJ,GAI5B,IAAIK,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAc5C,SACjB4C,EAzBF,SAASE,EAAwBC,EAASC,EAAOP,UAC/C,MAAMQ,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAAS5C,KAAK+C,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGC,YACLJ,EAAS5C,QAAQyC,EAAwBC,EAASK,EAAGC,eAIlDJ,EAYSH,CAAwB,qBAG1C,MAAMQ,EAAkB,GACxB,IAAK,IAAI3C,EAAI,EAAGA,EAAIiC,EAAc5C,OAAQW,IAAK,CAC7C,MAAM4C,EAAeX,EAAcjC,GACnC,GAAU,IAANA,EACF4C,EAAaF,WAAWV,YAAYJ,GACpCA,EAASiB,OAAS,WACZF,EAAgBtD,OAAS,GAC3BsD,EAAgBH,QAASM,IAEvBA,EAAUC,UAAYnB,EAASmB,iBAIhC,CACL,MAAMD,EAAYlB,EAASoB,WAAU,GACrCJ,EAAaF,WAAWV,YAAYc,GACpCH,EAAgBjD,KAAKoD,MAIzC,WAAoB,GAEPvB,EAAIrB,EAASyB,GAI1BD,EAAOuB,QAAU/C,EAAQgD,QAAU,I,sBClEnCD,EAAUvB,EAAOuB,QAAU,EAAQ,GAAR,EAAkE,IAKrFvD,KAAK,CAACgC,EAAO1B,EAAI,sxEAAuxE,KAGhzEiD,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,s0FCGvB,IAAMzH,EAAY,CAChB0H,WAAYzH,IAAU0H,OACtBC,OAAQ3H,IAAU0H,OAClBzC,MAAOjF,IAAU4H,MAAM,CACrBC,UAAW7H,IAAUG,OACrB2H,cAAe9H,IAAUC,MAE3B8H,mBAAoB/H,IAAUgI,KAC9BC,qBAAsBjI,IAAUkI,KAChCC,iBAAkBnI,IAAUkI,KAC5BE,sBAAuBpI,IAAUkI,KACjCG,YAAarI,IAAUgI,KACvBM,mBAAoBtI,IAAUgI,KAC9BO,sBAAuBvI,IAAUkI,KACjCM,SAAUxI,IAAUgI,MAGhBS,EAAsB,SAAH,GAYnB,8BAXJhB,EAAU,EAAVA,WAAYE,EAAM,EAANA,OACZ1C,EAAK,EAALA,MACA8C,EAAkB,EAAlBA,mBACAE,EAAoB,EAApBA,qBACAE,EAAgB,EAAhBA,iBACAC,EAAqB,EAArBA,sBACAE,EAAkB,EAAlBA,mBACAC,EAAqB,EAArBA,sBACAF,EAAW,EAAXA,YACAG,EAAQ,EAARA,SACAjI,EAAU,EAAVA,WAGEmI,EAMD,EALGjH,aACF,SAACC,GAAK,MAAK,CACTC,IAAUgH,SAASjH,MAErBkH,KACD,GANM,GAQiC,IAAZ/G,mBAAS,IAAG,GAAjCgH,EAAM,KAAEC,EAAS,KAClBC,EAAY5G,iBAAO,MACnB6G,EAAgB7G,iBAAO,MACvB8G,EAAgB9G,iBAAO,IACvBb,EAAWC,cACX2H,EAAkB/G,mBAClBgH,EAAsBhH,mBAC5BgH,EAAoB9G,QAAUiG,EAC9B,IAAOlH,EAAqB,EAAhBC,cAAgB,GAApB,GAERe,qBAAU,WACR,IAAMgH,EAAwB,SAACC,EAAOC,IACAD,GAASC,GAAYP,EAAU1G,SAEjE0G,EAAU1G,QAAQkH,aAAaD,EAASE,MAAOF,EAAS3F,QAEtD0F,GAASN,EAAU1G,SACrByG,EAAUW,EAAUJ,KAGlBK,EAAmB,WAAM,MAC7BZ,EAAUW,EAA2B,QAAlB,EAACV,EAAU1G,eAAO,aAAjB,EAAmBsH,kBAMzC,OAJAC,IAAK5G,iBAAiB,yBAA0BoG,GAChDQ,IAAK5G,iBAAiB,oBAAqB0G,GAE3CpI,EAASoB,IAAQmH,gBAAgB,CAACC,IAAaC,0BACxC,WACLH,IAAKlG,oBAAoB,yBAA0B0F,GACnDQ,IAAKlG,oBAAoB,oBAAqBgG,GAC9CpI,EAASoB,IAAQsH,eAAe,CAACF,IAAaC,6BAE/C,IAEH3H,qBAAU,WAAM,MAGd,GAFA2G,EAAU1G,QAAUsF,EACpBqB,EAAc3G,QAAUoF,EACpBa,GAAsBb,EAAY,iBAChCwC,EAAc,QAClB,IACEA,EAAuC,SAAxBxC,EAAkB,MAAY,UACtCA,EAAkB,MAAC,YAAIA,EAAmB,QAC7CA,EAAkB,MACtB,MAAOyC,GACPC,QAAQC,MAAMF,GAEhB,IACMG,EADiB5C,EAAW6C,mBACA,GAElCrB,EAAc5G,QAAU,CACtBkI,KAAM9C,EAAW8C,KACjBC,SAAU/C,EAAW+C,SACrBC,UAAWhD,EAAWgD,UACtBC,kBAAmBjD,EAAWiD,kBAC9BC,KAA4C,QAAxC,EAAkC,UAAhCN,aAAU,EAAVA,EAAa,uBAAyB,SAC5CO,OAA+C,QAAzC,EAAiC,YAA/BP,aAAU,EAAVA,EAAa,sBAA0B,SAC/CQ,WAAWR,SAA+B,QAArB,EAAVA,EAAa,0BAAkB,WAArB,EAAV,EAAiCrJ,SAAS,gBAChDqJ,SAA+B,QAArB,EAAVA,EAAa,0BAAkB,WAArB,EAAV,EAAiCrJ,SAAS,SAC/C8J,UAAoE,QAA3D,EAAET,SAA+B,QAArB,EAAVA,EAAa,0BAAkB,WAArB,EAAV,EAAiCrJ,SAAS,uBAAe,SACpE+J,KAAMV,aAAU,EAAVA,EAAa,aACnBW,KAAMX,aAAU,EAAVA,EAAa,eACnBJ,cACAgB,mBAAoBxD,EAAWyD,yBAInCpC,EAAUW,EAA2B,QAAlB,EAACV,EAAU1G,eAAO,aAAjB,EAAmBsH,iBAEnCT,EAAgB7G,UAClB0G,EAAU1G,QAAQkH,aAAaL,EAAgB7G,SAC/C6G,EAAgB7G,QAAU,QAE3B,CAACoF,EAAYE,EAAQW,IAExBlG,qBAAU,WACR,IAAM+I,EAAmB,WACvBpC,EAAU1G,QAAU,KACpB2G,EAAc3G,QAAU,KACxBkG,GAAsB,IAElB6C,EAAoB,WACxB7C,GAAsB,IAKxB,OAFAqB,IAAK5G,iBAAiB,aAAcmI,GACpCvB,IAAK5G,iBAAiB,cAAeoI,GAC9B,WACLxB,IAAKlG,oBAAoB,aAAcyH,GACvCvB,IAAKlG,oBAAoB,cAAe0H,MAEzC,CAAC9J,IAGJ,IAoHI+J,EApHE5B,EAAY,SAACJ,GACjB,IAAKA,EACH,MAAO,GAGT,IAAMR,EAASE,EAAU1G,QAAQoH,UAAUJ,EAAMG,MAAOH,EAAM1F,QAE9D,GAA4B,iBAAjBkF,EAAO9J,MAChB8J,EAAO9J,MAAQ,IAAI6B,OAAOC,KAAKyK,YAAYC,MAAM1C,EAAO9J,YACnD,GAAIyM,MAAMC,QAAQ5C,EAAO9J,OAAQ,CAEtC,IAAM2M,EAAoB,IAAI9K,OAAOC,KAAKyK,YAAYC,MAAM1C,EAAO9J,MAAM8J,EAAO9J,MAAM4E,OAAS,IAC/FkF,EAAO9J,MAAQ2M,OACL7C,EAAO9J,QACjB8J,EAAO9J,MAAQiK,EAAc3G,QAAQwF,WAKvC,IAFA,IAEA,MAF0B,CAAC,OAAQ,OAAQ,gBAEP,eAAE,CAAjC,IAAM8D,EAAI,KACT9C,EAAO8C,IAASH,MAAMC,QAAQ5C,EAAO8C,MACvC9C,EAAO8C,QAAQC,GAInB,OAAO/C,GAwBHgD,GAAc,SAACC,EAAWC,GACJ,MAEnB,EAFW,SAAdD,EACe,QAAjB,EAAA/C,EAAU1G,eAAO,OAAjB,EAAmBwG,OAAO,sBAAuBkD,GAEhC,QAAjB,EAAAhD,EAAU1G,eAAO,OAAjB,EAAmBwG,OAAOiD,EAAWC,GAGrB,UAAdD,IACFC,EAAQ,IAAInL,OAAOC,KAAKyK,YAAYC,MAAMQ,IAI5CjD,EAAU,EAAD,KACJD,GAAM,QACRiD,EAAYC,MAKXC,GAAuB,SAACC,EAAUF,GACtC,GAAK5C,EAAoB9G,QAAzB,CAKA,MAA0B0G,EAAU1G,QAAQsH,eAApCH,EAAK,EAALA,MAAO7F,EAAM,EAANA,OACT8D,EAAauB,EAAc3G,QACjCoF,EAAWwE,GAAYF,EACvBhD,EAAU1G,QAAQ6J,OACD,aAAbD,GAAwC,SAAbA,GAC7BE,YAA0B1E,GAE5B2E,YAAW,WACTlD,EAAgB7G,QAAU,CAAEmH,QAAO7F,UACZiG,IAAKyC,uBAAuBC,oBACpCC,SAAS9E,KACvB,QAfDU,EAAiB8D,EAAUF,IAgDvBjE,GAAkB7C,EAAlB6C,cACF0E,GAAW,CACf7B,KAAoD,QAAhD,EAA0C,UAAxC7C,UAAkB,QAAL,EAAbA,GAAgB,UAAE,WAAL,EAAb,EAAqB,uBAAyB,SACpD8C,OAAuD,QAAjD,EAAyC,YAAvC9C,UAAkB,QAAL,EAAbA,GAAgB,UAAE,WAAL,EAAb,EAAqB,sBAA0B,SACvD+C,WAAW/C,UAAkB,QAAL,EAAbA,GAAgB,UAAE,OAAqB,QAArB,EAAlB,EAAqB,0BAAkB,WAA1B,EAAb,EAAyC9G,SAAS,gBAAgB8G,UAAkB,QAAL,EAAbA,GAAgB,UAAE,OAAqB,QAArB,EAAlB,EAAqB,0BAAkB,WAA1B,EAAb,EAAyC9G,SAAS,SAC/H8J,UAA4E,QAAnE,EAAEhD,UAAkB,QAAL,EAAbA,GAAgB,UAAE,OAAqB,QAArB,EAAlB,EAAqB,0BAAkB,WAA1B,EAAb,EAAyC9G,SAAS,uBAAe,SAC5EgK,KAAMlD,UAAkB,QAAL,EAAbA,GAAgB,UAAE,WAAL,EAAb,EAAqB,eAC3BiD,KAAMjD,UAAkB,QAAL,EAAbA,GAAgB,UAAE,WAAL,EAAb,EAAqB,aAC3BmC,YAAa,SAGfoB,EAAa,EAAH,KACLpG,GACAuH,IAGDlE,GAAsBb,IACxBwB,EAAc5G,QAAQsI,KAAO9B,EAAO8B,KACpC1B,EAAc5G,QAAQuI,OAAS/B,EAAO+B,OACtC3B,EAAc5G,QAAQwI,UAAYhC,EAAOgC,UACzC5B,EAAc5G,QAAQyI,UAAYjC,EAAO4D,OACzCxD,EAAc5G,QAAQqK,UAAY7D,EAAOmC,MAAQ/B,EAAc5G,QAAQkI,KACvEtB,EAAc5G,QAAQsK,cAAgB9D,EAAO+D,cAAgB3D,EAAc5G,QAAQmI,UAGrF,IAAMqC,GAAc,CAClBnE,MAAOA,EACPP,iBAAkB6D,GAClBX,WAAYA,EACZyB,WAAW,EACXC,YAAa1E,GAGT2E,GAAiB,CACrB5E,sBA9DgC,SAAC6D,EAAUF,GAC3C,GAAK5C,EAAoB9G,QAAzB,CAKA,IAAM4K,EAAsB,CAC1B,cAAe,OACf,aAAc,SACd,UAAa,YACb,eAAgB,SAChB,cAAe,OACf,YAAa,QAEf,GAAiB,gBAAbhB,GAA2C,cAAbA,EAA0B,CAC1DJ,GAAYoB,EAAoBhB,GAAWF,GAC3C,IAAMmB,EAAWlE,EAAc3G,QAC/B,GAAI6K,EAASC,cACYvD,IAAKyC,uBAAuBC,oBACpCc,iBAAiBF,QAjFP,SAACrE,GAAM,OAAK,WACzC,MAAwBE,EAAU1G,QAAQsH,eAApCH,EAAK,EAALA,MAAO7F,EAAM,EAANA,OACb,GAAe,IAAXA,EAAc,CAChBuF,EAAgB7G,QAAU,CAAEmH,QAAO7F,UACnC,IAAM0J,EAAetE,EAAU1G,QAAQsH,eACvCH,EAAQ6D,EAAa7D,MACrB7F,EAAS0J,EAAa1J,OAExB,IAAM2J,EAAgBvE,EAAU1G,QAAQoH,UAAUD,EAAO7F,GAEzDkI,GAAYhD,GAASyE,EAAczE,KA0EjC0E,CAAuBN,EAAoBhB,GAA3CsB,QApBAnF,EAAsB6D,EAAUF,IA6DlCV,WAAY/C,EAAqBW,EAAc5G,QAAUgJ,EACzDtD,mBAAoBA,EACpBO,mBAAoBA,EACpBD,YAAaA,EACbJ,qBAAsBA,GAGlBuF,GAAc,CAClBpF,sBAAuB4D,GACvBjE,oBAAoB,EACpBO,oBAAoB,EACpBD,aAAa,EACbG,SAAUA,GAGZ,OACE,yBAAKlJ,UAAU,sBACbmO,YAAa,SAACrK,GACG,eAAXA,EAAE5C,MAAyB8H,GAC7BlF,EAAEsK,mBAIN,yBAAKpO,UAAU,cACb,kBAACqO,EAAA,EAAe,KACVd,GACCrE,EAAWgF,GAAcR,MAGlC,kBAAC5M,EAAA,EAAW,CACVC,cAAe,SAACtB,IA9II,SAAC6O,EAAM7O,GAC1BoK,EAAoB9G,QAIzBwJ,GAAY,QAAS9M,EAAME,eAHzBkJ,EAAiByF,EAAM7O,GA6InB8O,CAAkB,YAAa,IAAIjN,OAAOC,KAAKyK,YAAYC,MAAMxM,KAEnEA,MAAOuJ,EAAqBO,EAAO9J,MAAQkG,EAAiB,UAC5D1E,WAAYA,EACZC,KAAM,OACNN,cAAekB,EAAE,mCAKzBqH,EAAoB1I,UAAYA,EAEjB+N,UAAMC,KAAKtF,GCnVXA,a,qBCFf,IAAI5C,EAAM,EAAQ,IACFrB,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQsB,WAAatB,EAAQuB,QAAUvB,KAG/CA,EAAU,CAAC,CAACwB,EAAO1B,EAAIE,EAAS,MAG9C,IAAIyB,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKtF,OAAOuF,8BAEV,YADAC,SAASC,KAAKC,YAAYJ,GAI5B,IAAIK,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAc5C,SACjB4C,EAzBF,SAASE,EAAwBC,EAASC,EAAOP,UAC/C,MAAMQ,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAAS5C,KAAK+C,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGC,YACLJ,EAAS5C,QAAQyC,EAAwBC,EAASK,EAAGC,eAIlDJ,EAYSH,CAAwB,qBAG1C,MAAMQ,EAAkB,GACxB,IAAK,IAAI3C,EAAI,EAAGA,EAAIiC,EAAc5C,OAAQW,IAAK,CAC7C,MAAM4C,EAAeX,EAAcjC,GACnC,GAAU,IAANA,EACF4C,EAAaF,WAAWV,YAAYJ,GACpCA,EAASiB,OAAS,WACZF,EAAgBtD,OAAS,GAC3BsD,EAAgBH,QAASM,IAEvBA,EAAUC,UAAYnB,EAASmB,iBAIhC,CACL,MAAMD,EAAYlB,EAASoB,WAAU,GACrCJ,EAAaF,WAAWV,YAAYc,GACpCH,EAAgBjD,KAAKoD,MAIzC,WAAoB,GAEPvB,EAAIrB,EAASyB,GAI1BD,EAAOuB,QAAU/C,EAAQgD,QAAU,I,sBClEnCD,EAAUvB,EAAOuB,QAAU,EAAQ,GAAR,EAAkE,IAKrFvD,KAAK,CAACgC,EAAO1B,EAAI,upKAAwpK,KAGjrKiD,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAI3B,EAAM,EAAQ,IACFrB,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQsB,WAAatB,EAAQuB,QAAUvB,KAG/CA,EAAU,CAAC,CAACwB,EAAO1B,EAAIE,EAAS,MAG9C,IAAIyB,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKtF,OAAOuF,8BAEV,YADAC,SAASC,KAAKC,YAAYJ,GAI5B,IAAIK,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAc5C,SACjB4C,EAzBF,SAASE,EAAwBC,EAASC,EAAOP,UAC/C,MAAMQ,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAAS5C,KAAK+C,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGC,YACLJ,EAAS5C,QAAQyC,EAAwBC,EAASK,EAAGC,eAIlDJ,EAYSH,CAAwB,qBAG1C,MAAMQ,EAAkB,GACxB,IAAK,IAAI3C,EAAI,EAAGA,EAAIiC,EAAc5C,OAAQW,IAAK,CAC7C,MAAM4C,EAAeX,EAAcjC,GACnC,GAAU,IAANA,EACF4C,EAAaF,WAAWV,YAAYJ,GACpCA,EAASiB,OAAS,WACZF,EAAgBtD,OAAS,GAC3BsD,EAAgBH,QAASM,IAEvBA,EAAUC,UAAYnB,EAASmB,iBAIhC,CACL,MAAMD,EAAYlB,EAASoB,WAAU,GACrCJ,EAAaF,WAAWV,YAAYc,GACpCH,EAAgBjD,KAAKoD,MAIzC,WAAoB,GAEPvB,EAAIrB,EAASyB,GAI1BD,EAAOuB,QAAU/C,EAAQgD,QAAU,I,sBClEnCD,EAAUvB,EAAOuB,QAAU,EAAQ,GAAR,EAAkE,IAKrFvD,KAAK,CAACgC,EAAO1B,EAAI,4zEAA6zE,KAGt1EiD,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,iZCRRwG,ECOQ,SAAH,GAId,QAHJC,EAAK,EAALA,MACAC,EAAS,EAATA,UACAC,EAAiB,EAAjBA,kBAEM7M,EAAWC,cAEX6M,GACc,QAAlB,EAAAxE,IAAKyE,qBAAa,aAAlB,EAAoBC,aAAcC,IAAYC,kBAAoB5E,IAAKyE,cAAcI,4BACjFC,GAAkD,QAAlB,EAAA9E,IAAKyE,qBAAa,aAAlB,EAAoBC,UAAUpP,iBAAkBqP,IAAYI,KAAOP,EACnGQ,EAAgCX,GAASC,GAAaQ,GAAiC9E,IAAKiF,mBAoBlG,OACE,oCACGD,GACC,yBAAKtP,UAAU,mBACb,kBAACwP,EAAA,EAAM,CACLxJ,YAAY,4BACZyJ,GAAG,uBACHvO,KAAK,WACLwO,MAAOC,IAAQ7N,EAAE,gCACjB8N,QAASf,EACTgB,SA5Be,SAACC,GACxB,GAAKxF,IAAKiF,mBAAV,CAIA,IAAMQ,EAAiBD,EAAME,OAAOJ,QAC9BK,EAAOF,EACTzF,IAAK4F,oBAAoBC,SAASC,kBAAoB9F,IAAK4F,oBAAoBC,SAASE,cACxF,KACqBC,cAER9I,SAAQ,SAAC+I,GAAS,MACjB,QAAhB,EAAAA,EAAKC,mBAAW,OAAhB,OAAAD,EAAmBN,GACnBjO,EAASoB,IAAQqN,kBAAkB,CAAEC,SAAUH,EAAKjC,KAAMqC,UAAWZ,eChCrEvO,G,qBAAQF,OAAOC,KAAKC,OAEboP,EAA8B,SAACF,GAQ1C,MAPiC,CAC/BlP,EAAMqP,iBACNrP,EAAMsP,oBACNtP,EAAMuP,eACNvP,EAAMwP,oBAGwBC,MAAK,SAACV,GAAI,OAAKjG,IAAK4G,QAAQR,aAAqBH,MAwEtEY,EAA0B,SAACT,GAWtC,MAVqC,CACnClP,EAAM4P,oBACN5P,EAAM6P,WACN7P,EAAM8P,wBACN9P,EAAM+P,2BACN/P,EAAMgQ,4BACNhQ,EAAMiQ,6BACNjQ,EAAMkQ,4BACNlQ,EAAMmQ,gCAE4BV,MAAK,SAACV,GAAI,OAAKjG,IAAK4G,QAAQR,aAAqBH,MAqC1EqB,EAAiC,SAAClB,GAE7C,MADwC,CAAClP,EAAM4P,qBACRH,MAAK,SAACV,GAAI,OAAKjG,IAAK4G,QAAQR,aAAqBH,MAG7EsB,EAA0B,SAACnB,EAAUoB,GAChD,IAAMC,EAAa,CACjB,0BAA6B,CAC3B,MAAS,sBACT,YAAe,2CACf,UAAa,sCAGjB,OAAOA,EAAWrB,IAAaqB,EAAWrB,GAAUoB,IAGzCE,EAA2B,SAACtB,GACvC,IAAQjP,EAAcH,OAAOC,KAAKC,MAA1BC,UAMR,MALuC,CACrCA,EAAUwQ,gBACVxQ,EAAUyQ,eACVzQ,EAAU0Q,iBAE0BzQ,SAASgP,IAOpC0B,EAAqB,SAACC,EAAaC,GAC9C,OAA8B,IAAvBD,EAAYhO,QALU,SAAC8D,EAAYoK,GAC1C,OAAOA,EAAMtB,MAAK,SAAC/P,GAAI,OAAKiH,aAAsBjH,KAIfsR,CAAgBH,EAAY,GAAIC,I,4kCC5JrE,IAAMG,EAAqB,SAAH,GAgClB,IA/BJC,EAAwC,EAAxCA,yCACAC,EAAO,EAAPA,QACAC,EAAmB,EAAnBA,oBACAC,EAAa,EAAbA,cACAC,EAAW,EAAXA,YACA7R,EAAU,EAAVA,WACA8R,EAAgB,EAAhBA,iBACAC,EAAwB,EAAxBA,yBACAC,EAAoB,EAApBA,qBACAC,EAAY,EAAZA,aACAC,EAAW,EAAXA,YACAC,EAAyB,EAAzBA,0BACA3F,EAAU,EAAVA,WACA4F,EAAU,EAAVA,WACAC,EAAK,EAALA,MACAC,EAAsB,EAAtBA,uBACAC,EAAiB,EAAjBA,kBACAC,EAAqB,EAArBA,sBACAC,EAAmB,EAAnBA,oBACAC,EAAe,EAAfA,gBACAC,EAAsB,EAAtBA,uBACAC,EAAS,EAATA,UACAC,EAAe,EAAfA,gBACAC,EAAoB,EAApBA,qBACAC,EAAe,EAAfA,gBACAC,EAAsB,EAAtBA,uBACAC,EAAmB,EAAnBA,oBACAC,EAAoB,EAApBA,qBACAC,EAAwB,EAAxBA,yBACAC,EAA4B,EAA5BA,6BACAxC,EAAuB,EAAvBA,wBAEO/P,EAAqB,EAAhBC,cAAgB,GAApB,GAEFuS,EACJ,yBAAKtU,UAAU,0BACX2S,GACA,oCACE,yBAAK3S,UAAU,cACb,kBAACc,EAAA,EAAW,CAACC,cAAe6R,EAAqBC,cAAeA,EAAepT,MAAOqT,EACpF7R,WAAYA,EAAYC,KAAM,SAAUN,cAAekB,EAAE,0CAE3DiR,GAAoBC,GAA6BA,EAIlDC,GAAwB,yBAAKjT,UAAU,eAAekT,EAAa,cACjEC,KAAiBC,IAA8B3F,KAAgB4F,IAAeC,GAC/E,yBAAKtT,UAAU,eACb,yBAAKA,UAAU,uCACb,yBAAKA,UAAU,gBAAgB8B,EAAE,6BACjC,yBAAK9B,UAAU,yBACZiT,GACC,kBAACsB,EAAA,EAAQ,CACP9E,GAAG,yBACH+E,kBAAkB,8BAClBxU,UAAU,qCACVgG,YAAY,yBACZyO,OAAQR,EACRS,YAAanB,EACboB,oBAAqBnB,EACrBoB,iBAAe,KAGjBnB,GACA,kBAACc,EAAA,EAAQ,CACP9E,GAAG,0BACH+E,kBAAmBZ,EACnB5T,UAAS,6CAA0CmT,IAAgBF,EAAuB,gBAAkB,IAE5GjN,YAAY,0BACZyO,OAAQZ,GAAaZ,EAAuBiB,EAAsBJ,EAClEY,YAAahB,EACbiB,oBAAqBhB,EACrBiB,iBAAe,IAGlB3B,GACC,kBAACsB,EAAA,EAAQ,CACP9E,GAAG,uBACH+E,kBAAkB,4BAClBxU,UAAU,mCACVgG,YAAY,uBACZyO,OAAQN,EACRO,YAAaX,EACbY,oBAAqBX,EACrBY,iBAAe,SAYnC,OAAKlC,EAKH,kBAACmC,EAAA,EAAkB,CACjBC,OAAQhT,EAAE+P,EAAwB5Q,EAAY,gBAAkB,sCAChE8T,aAAc,EACdC,qBAAqB,EACrBC,SAAUb,EACVc,kBAAmBxC,EACnBhQ,WAAa2R,IAAiC3B,GAC5C4B,GAXGA,GAgBI7B,I,+hCAEfA,EAAmBhS,UAAY,CAC7BiS,yCAA0ChS,IAAUgI,KACpDiK,QAASjS,IAAUgI,KACnBkK,oBAAqBlS,IAAUkI,KAC/BiK,cAAenS,IAAUkI,KACzBkK,YAAapS,IAAUyU,UAAU,CAACzU,IAAUG,OAAQH,IAAU0H,SAC9DnH,WAAYP,IAAUG,OACtBkS,iBAAkBrS,IAAUgI,KAC5BsK,yBAA0BtS,IAAU0U,KACpCnC,qBAAsBvS,IAAUgI,KAChCwK,aAAcxS,IAAUkI,KACxBuK,YAAazS,IAAUG,OACvBuS,0BAA2B1S,IAAUgI,KACrC+E,WAAY/M,IAAUgI,KACtB2K,WAAY3S,IAAUgI,KACtB4K,MAAO5S,IAAUgI,KACjB6K,uBAAwB7S,IAAUkI,KAClC4K,kBAAmB9S,IAAUG,OAC7B4S,sBAAuB/S,IAAUgI,KACjCgL,oBAAqBhT,IAAUkI,KAC/B+K,gBAAiBjT,IAAUG,OAC3B+S,uBAAwBlT,IAAUG,OAClCgT,UAAWnT,IAAUgI,KACrBoL,gBAAiBpT,IAAU2U,MAC3BtB,qBAAsBrT,IAAUkI,KAChCoL,gBAAiBtT,IAAUG,OAC3BoT,uBAAwBvT,IAAU2U,MAClCnB,oBAAqBxT,IAAU2U,MAC/BlB,qBAAsBzT,IAAU2U,MAChCjB,yBAA0B1T,IAAUkI,KACpCyL,6BAA8B3T,IAAUgI,KACxCmJ,wBAAyBnR,IAAUkI,MCvJrC,ICHe0M,EDGa,SAAtBA,EAAmB,GAQnB,IAPJ5C,EAAwC,EAAxCA,yCACAvB,EAAuB,EAAvBA,wBACAlQ,EAAU,EAAVA,WACAgS,EAAoB,EAApBA,qBACAC,EAAY,EAAZA,aACAqC,EAAwB,EAAxBA,yBACAC,EAAoB,EAApBA,qBAEO1T,EAAqB,EAAhBC,cAAgB,GAApB,GAERuT,EAAoB7U,UAAY,CAC9BiS,yCAA0ChS,IAAUgI,KACpDyI,wBAAyBzQ,IAAUkI,KACnC3H,WAAYP,IAAUG,OACtBoS,qBAAsBvS,IAAUgI,KAChCwK,aAAcxS,IAAUkI,KACxB2M,yBAA0B7U,IAAUgI,KACpC8M,qBAAsB9U,IAAUkI,MAGlC,IAAM0L,EACJ,yBAAKtU,UAAU,kCAKXiT,IAAyB9B,EAAwBlQ,IACjD,yBAAKjB,UAAU,eAAekT,EAAa,UAAWR,KAK5D,OAAMA,GAA6CvB,EAAwBlQ,GAClEqT,EAIP,kBAACO,EAAA,EAAkB,CACjBC,OAAQhT,EAAE,yBACViT,aAAc,EACdC,qBAAqB,EACrBtS,WAAa6S,IAA6B7C,EAC1CuC,SAAUO,GACRlB,I,s6DEbR,IAAMR,EAAkBI,IAAoBuB,OAAOC,KAE7CjV,EAAY,CAChBkV,WAAYjV,IAAUG,OACtB+U,aAAclV,IAAUG,OACxBgV,0BAA2BnV,IAAUkI,KACrC0K,MAAO5S,IAAUgI,KACjBmL,UAAWnT,IAAUgI,KACrB2K,WAAY3S,IAAUgI,KACtB+E,WAAY/M,IAAUgI,KACtBD,mBAAoB/H,IAAUgI,KAC9B0K,0BAA2B1S,IAAUgI,KACrCK,YAAarI,IAAUgI,KACvBiK,QAASjS,IAAUgI,KACnBoN,wBAAyBpV,IAAUgI,KACnCQ,SAAUxI,IAAUgI,KACpBC,qBAAsBjI,IAAUkI,KAChCmN,kBAAmBrV,IAAUkI,KAC7BiK,cAAenS,IAAUkI,KAAKoN,WAC9BC,yBAA0BvV,IAAU0H,OACpC6K,qBAAsBvS,IAAUgI,KAChCwN,iBAAkBxV,IAAUyV,QAAQzV,IAAUG,QAC9CuV,eAAgB1V,IAAUG,OAC1BsS,YAAazS,IAAUG,OACvB8E,MAAOjF,IAAU0H,OAAO4N,WACxBtF,SAAUhQ,IAAUG,QAKhBwV,EAAc,SAAH,GAqBX,MJxBgC3F,EIIpCmC,EAAa,EAAbA,cACAlN,EAAK,EAALA,MACA8H,EAAU,EAAVA,WACAoG,EAAS,EAATA,UACA9K,EAAW,EAAXA,YACAG,EAAQ,EAARA,SACAmK,EAAU,EAAVA,WACAJ,EAAoB,EAApBA,qBACAK,EAAK,EAALA,MACAX,EAAO,EAAPA,QACAS,EAAyB,EAAzBA,0BACAgD,EAAc,EAAdA,eACAR,EAAY,EAAZA,aACAzC,EAAW,EAAXA,YACA4C,EAAiB,EAAjBA,kBACApN,EAAoB,EAApBA,qBACAF,EAAkB,EAAlBA,mBACAoN,EAAyB,EAAzBA,0BACA5U,EAAU,EAAVA,WACAqV,EAAkB,EAAlBA,mBAEOxU,EAAqB,EAAhBC,cAAgB,GAApB,GACFC,EAAWC,cACkD,IAAfM,oBAAS,GAAM,GAA5DyG,EAAkB,KAAEC,GAAqB,KACiB,KAA3B1G,mBAASoD,EAAM4Q,aAAY,GAA1DzD,GAAW,MAAE0D,GAAc,MACwC,KAAxBjU,mBAAS6T,GAAe,GAAnE5C,GAAiB,MAAEiD,GAAoB,MACsB,KAAtBlU,mBAASqT,GAAa,GAA7D5B,GAAe,MAAE0C,GAAkB,MACyB,KAArBnU,mBAAS4Q,GAAY,GAA5DQ,GAAe,MAAEgD,GAAkB,MACiB,KAAzBpU,mBAASoD,EAAMiR,WAAU,GAApDC,GAAS,MAAEC,GAAY,MAExBC,IJlC8BrG,EIkCUzP,EJjCX,CACjCO,EAAMwV,sBACNxV,EAAMyV,gBACNzV,EAAM6P,YAE0BJ,MAAK,SAACV,GAAI,OAAKjG,IAAK4G,QAAQR,aAAqBH,MI6B7EmC,GJnF+C,SAAChC,GAmBtD,MAlBsC,CACpClP,EAAM0V,oBACN1V,EAAM2V,kBACN3V,EAAM4V,kBACN5V,EAAM6V,uBACN7V,EAAM8V,6BACN9V,EAAM+V,0BACN/V,EAAMgW,mBACNhW,EAAMiW,kBACNjW,EAAM4P,oBAEN5P,EAAM8P,wBACN9P,EAAMmQ,+BACNnQ,EAAMkQ,4BACNlQ,EAAM+P,2BACN/P,EAAMgQ,6BAG6BP,MAAK,SAACV,GAAI,OAAKjG,IAAK4G,QAAQR,aAAqBH,KIgErCmH,CAAwCzW,GACnF0W,GJ9DsD,SAACjH,GAM7D,MALyC,CACvClP,EAAMwV,sBACNxV,EAAMyV,gBACNzV,EAAM6P,YAEgCJ,MAAK,SAACV,GAAI,OAAKjG,IAAK4G,QAAQR,aAAqBH,KIwDxCqH,CAA+C3W,GAC1F8R,GJtD8B,SAACrC,GAcrC,MAboC,CAClClP,EAAMqW,wBACNrW,EAAMsW,wBACNtW,EAAMuW,uBACNvW,EAAMwW,wBACNxW,EAAMyW,2BACNzW,EAAMwV,sBACNxV,EAAM0W,yBACN1W,EAAMyV,gBACNzV,EAAM2W,iBACN3W,EAAM4W,yBACN5W,EAAM6W,2BAE2BpH,MAAK,SAACV,GAAI,OAAKjG,IAAK4G,QAAQR,aAAqBH,KIwC3D+H,CAAuBrX,GAC1CsX,GJH2B,SAAC7H,GASlC,MARkC,CAChClP,EAAMgX,8BACNhX,EAAMiX,yBACNjX,EAAMkX,+BACNlX,EAAM+V,0BACN/V,EAAMmX,qCACNnX,EAAMoX,4CAEyB3H,MAAK,SAACV,GAAI,OAAKjG,IAAK4G,QAAQR,aAAqBH,KINrDsI,CAAoB5X,GAC3C6X,GJ9B2B,SAACpI,GASlC,MARoC,CAClClP,EAAMgW,mBACNhW,EAAMiW,kBACNjW,EAAM4P,oBACN5P,EAAM8P,wBACN9P,EAAM+P,2BACN/P,EAAMgQ,6BAE2BP,MAAK,SAACV,GAAI,OAAKjG,IAAK4G,QAAQR,aAAqBH,KIqB9DwI,CAAoB9X,GAE1C6B,qBAAU,WACJ4P,IAEA1Q,EADE8W,GACO1V,IAAQI,YAAYgH,IAAawO,2BAEjC5V,IAAQI,YAAYgH,IAAayO,2BAG7C,CAAChY,IAEJ6B,qBAAU,WACJkG,GACFhH,EAASoB,IAAQ8V,aAAa1O,IAAa2O,qBAE5C,CAACnQ,IAEJlG,qBAAU,WACR0T,GAAe7Q,EAAM4Q,aACrBO,GAAanR,EAAMiR,aAClB,CAAC9D,GAAa+D,GAAWlR,IAE5B7C,qBAAU,WACR0T,GAAe7Q,EAAM4Q,aACrBE,GAAqBL,GACrBO,GAAmBxD,GACnBuD,GAAmBd,KAClB,CAACQ,EAAgBR,EAAczC,IAElC,IAyBMiG,GAAiB,SAACzM,EAAUF,GAChCoG,WAAgBlG,EAAUF,IAIxB1D,IACFpD,EAAM0T,QAAU,KAChB1T,EAAM2T,gBAAkB,MAG1B,IAcE,KAPEnX,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAUwM,kBAAkBzM,GAC5BC,IAAUkX,kBAAkBnX,EAAOoI,IAAagP,cAChDnX,IAAUoX,cAAcrX,EAAOoI,IAAayO,wBAC5C5W,IAAUoX,cAAcrX,EAAOoI,IAAakP,sBAC5CrX,IAAUoX,cAAcrX,EAAOoI,IAAamP,mBAC5CtX,IAAUoX,cAAcrX,EAAOoI,IAAawO,+BAC5C,GAbAnK,GAAiB,MACjB4E,GAAqB,MACrBY,GAA4B,MAC5BuF,GAA0B,MAC1BrE,GAAwB,MACxBsE,GAA0B,MAUtBC,IAAU,OACbtP,IAAayO,uBAAyB5E,IAA4B,IAClE7J,IAAakP,qBAAuBE,IAA0B,IAC9DpP,IAAamP,kBAAoBpE,IAAwB,IACzD/K,IAAawO,0BAA4Ba,IAA0B,GAGhEE,GAAkB,SAAC/T,GAIrBhE,EAHG8X,GAAW9T,GAGL5C,IAAQ8V,aAAalT,GAFrB5C,IAAQI,YAAYwC,KAuE3BkN,GAAe,SAACvG,EAAUqN,GAC9B,IAAMC,EA3De,SAAC/Y,GACtB,IAAQmY,EAAuC1T,EAAvC0T,QAASC,EAA8B3T,EAA9B2T,gBAAiBpO,EAAavF,EAAbuF,SAElC,OAAQhK,EAAKtB,eACX,IAAK,UACH,OAAgB,OAAZyZ,EACK,KAEF,CACL1M,SAAU,UACVuN,gBAAiB,UACjBzN,MAAiB,IAAV4M,EACPc,gBAAiB,SAACd,GAAO,gBAAQe,KAAKC,MAAMhB,GAAQ,MACpDrT,YAAawE,IAAa8P,eAC1BC,gBAAgB,EAChBC,eAAgB,SAChBC,IAAK,EACLC,IAAK,IACLC,KAAM,EACNC,cAAe,SAACC,GAAO,OAAKC,SAASD,GAAW,MAEpD,IAAK,kBACH,OAAwB,OAApBvB,EACK,KAEF,CACL3M,SAAU,kBACVuN,gBAAiB,YACjBzN,MAAO6M,EACPa,gBAAiBY,IACjB/U,YAAawE,IAAawQ,wBAC1BT,gBAAgB,EAChBC,eAAgB,SAChBC,IAAK,EACLC,IA/KmB,GAgLnBC,KAAM,EACNM,MAAOC,YAAqBzN,IAEhC,IAAK,WACH,OAAiB,OAAbvC,EACK,KAEF,CACLyB,SAAU,WACVuN,gBAAiB,OACjBzN,MAAOvB,EACPiP,gBAAiB,SAACjP,GAAQ,gBAAQkP,KAAKC,MAAMS,SAAS5P,EAAU,KAAI,OACpElF,YAAawE,IAAa2Q,iBAC1BV,IAAK,EACLC,IAAK,GACLC,KAAM,EACNJ,gBAAgB,EAChBC,eAAgB,SAChBI,cAAe,SAAC1P,GAAQ,gBAAQkQ,WAAWlQ,GAAUmQ,QAAQ,GAAE,SAMjDC,CAAe3O,GACnC,OAAKsN,EAIH,kBAACsB,EAAA,EAAM,GACLnW,IAAKuH,GACDsN,EAAW,CACfpH,cAAeuG,GACfA,eAAgBA,GAChBY,sBAAuBA,EACvBwB,mBAAoB,EACpBC,sBAAuB,KAVlB,MAeLC,GAAgB,WACpB,GAAIhJ,GACF,OAAO,yBAAK1S,UAAU,aAIpB2b,GAAcC,YAAkBtF,GAChCtD,GAA2BE,GAAa,mBACxCU,GAAyBX,EAAuB,+BAAiC,yBAEvF,OACE,yBACEjT,UAAU,cACVmO,YAAa,SAACrK,GACG,eAAXA,EAAE5C,MAA4D,UAAnC4C,EAAEkM,OAAO5I,QAAQjC,eAC9CrB,EAAEsK,mBAIL0K,IACC,yBAAK9Y,UAAU,0BACb,kBAAC6U,EAAA,EAAkB,CACjBC,OAAQhT,EAAE+P,EAAwB5Q,EAAY,gBAAkB,+BAChE8T,aAAc,EACdC,qBAAqB,EACrBtS,WAAYmX,GACZ5E,SA9GqB,WAC7BjT,EAASoB,IAAQyY,aAAarR,IAAasR,mBAC3C/B,GAAgBvP,IAAawO,6BA6GrB,yBAAKhZ,UAAU,yBACZ+I,GACC,yBAAK/I,UAAU,sCACb,yBAAKA,UAAU,iBAAiB8B,EAAE,2CAClC,kBAACia,EAAA,EAAe,CAAChQ,WAAYpG,EAAOkD,iBAAkBgK,EAAemJ,gBAAiB,OAG1F,kBAAC7S,EAAA,QAAmB,GAClBxD,MAAOA,GACHgW,GAAW,CACfhP,SAAU,YACVsP,YAAa,WACbxT,mBAAoBA,EACpBE,qBAAsBA,EACtBE,iBAAkBgK,EAClB/J,sBAAuB+M,EACvB7M,mBAAoBA,EACpBC,sBAAuBA,GACvBF,YAAaA,EACb9H,WAAYA,EACZiI,SAAUA,OAIfwS,OAIH3E,IACA,yBAAK/W,UAAU,gBACb,kBAAC,EAAkB,CACjB0S,yCAA0CA,GAC1CC,QAASA,EACTC,oBAjNkB,SAACnT,GAC3BoT,WAAgB,cAAepT,GAC/B+W,GAAe/W,IAgNPoT,cAAeA,EACfC,YAAaA,GACb7R,WAAYA,EACZ8R,iBAAkBA,GAClBC,yBAA0BA,GAC1BC,qBAAsBA,EACtBC,aAAcA,GACdC,YAAaQ,GACbP,0BAA2BA,EAC3B3F,WAAYA,EACZ4F,WAAYA,EACZC,MAAOA,EACPC,uBAzNqB,SAAC5N,GAC9BoQ,WAAoB,QAASpQ,GAC7B8Q,GAAqB9Q,IAwNb6N,kBAAmBA,GACnBC,sBAAuBA,GACvBC,oBAvNkB,SAAC/N,GAC3BoQ,WAAoB,SAAUpQ,GAC9BgR,GAAmBhR,IAsNXgO,gBAAiBA,GACjBC,uBAAwBA,GACxBC,UAAWA,EACXC,gBAAiBA,EACjBC,qBAvNmB,SAACpO,GAC5BoQ,WAAoB,MAAOpQ,GAC3B+Q,GAAmB/Q,IAsNXqO,gBAAiBA,GACjBC,uBAAwBA,IACxBC,oBAAqBA,IACrBC,qBAAsBA,IACtBC,yBAtKuB,WAAH,OAAS2F,GAAgBvP,IAAayO,yBAuK1D5E,6BAA8BA,GAC9BxC,wBAAyBA,IAE1B6J,MAIJ3E,KAAoBhE,IAAoBC,IAA6BA,GACrEN,KAA6CiF,IAC5C,yBAAK3X,UAAU,gBACb,kBAAC6U,EAAA,EAAkB,CACjBC,OAAQhT,EAAE+P,EAAwB5Q,EAAY,cAAgB,oCAC9D8T,aAAc,EACdC,qBAAqB,EACrBtS,WAAYkX,GACZ3E,SArLqB,WAAH,OAAS8E,GAAgBvP,IAAakP,wBAsLxD,yBAAK1Z,UAAU,yBACb,yBAAKA,UAAU,cACb,kBAACc,EAAA,EAAW,CACVC,cA3OU,SAACtB,GACzBoT,WAAgB,YAAapT,GAC7BqX,GAAarX,IA0OCoT,cAAeA,EACfpT,MAAOoX,GACP7V,qBAAsB4Q,EAA+B3Q,GACrDA,WAAYA,EACZC,KAAM,OACNN,cAAekB,EAAE,0CAKvBqP,EAAwBlQ,IAAeya,MAI7C,yBAAK1b,UAAU,gBACb,kBAAC,EAAmB,CAClB0S,yCAA0CA,GAC1CvB,wBAAyBA,EACzBlQ,WAAYA,EACZgS,qBAAsBA,EACtBC,aAAcA,GACdqC,yBAA0BA,GAC1BC,qBA/MqB,WAAH,OAASuE,GAAgBvP,IAAamP,sBAiNzDpB,IAAwBmD,MAG1BnD,IACC,qCAEI7F,IAA4C,yBAAK1S,UAAU,WAC7D,yBAAKA,UAAU,gBACb,kBAAC,EAAc,CAAC2O,MAAOhJ,EAAMgJ,MAAOC,UAAWjJ,EAAMiJ,UAAWC,kBAAmBA,SAQ/FwH,EAAY5V,UAAYA,EAET4V,IC5aAA,ED4aAA,E,g2EErZf,IAAQ5U,GAAcH,OAAOC,KAAKC,MAA1BC,UACAuK,GAAgB1K,OAAOC,KAAvByK,YAggBOkQ,GA7fI,WACjB,IAAOpa,EAAqB,GAAhBC,cAAgB,GAApB,GAYN,KALEI,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAUoX,cAAcrX,EAAO,cAC/BC,IAAU8Z,qBAAqB/Z,GAC/BC,IAAU+Z,oCAAoCha,GAC9CC,IAAUga,2BAA2Bja,OACrC,GATAka,EAAW,KACXC,EAAgB,KAChBH,EAAmC,KACnCI,EAAuB,KASnBC,EAAcnS,IAAKoS,cACnBC,EAAkBF,aAAW,EAAXA,EAAanO,KAC/BsO,EAAkB,CAAC,cAAe,aACW,KAAfra,oBAAS,GAAM,GAA5Csa,EAAU,KAAEC,EAAa,KAC4D,KAAtDva,mBAASqO,EAA4B+L,IAAiB,GAArFI,EAAW,KAAEC,EAAc,KACe,KAAfza,oBAAS,GAAM,GAA1CsR,EAAS,KAAEoJ,EAAY,KACqB,KAAf1a,oBAAS,GAAM,GAA5CkL,EAAU,KAAEyP,EAAa,KACuD,KAAjD3a,mBAASoa,IAAoBlb,GAAU0b,WAAU,GAAhFpU,EAAW,KAAEqU,EAAc,KAC2F,KAA7F7a,mBAASoa,IAAoBU,MAAsBrL,EAAyB2K,IAAiB,GAAtHzT,EAAQ,KAAEoU,EAAW,KACuB,KAAf/a,oBAAS,GAAM,GAA5C8Q,EAAU,KAAEkK,EAAa,KACS,KAAfhb,oBAAS,GAAM,GAAlC+Q,EAAK,KAAEkK,EAAQ,KACuB,KAAfjb,oBAAS,GAAM,GAAtCoQ,EAAO,KAAE8K,EAAU,KACuD,KAAflb,oBAAS,GAAM,GAA1E6Q,EAAyB,KAAEsK,GAA4B,KACxB,MAAZnb,mBAAS,IAAG,GAA/BoD,GAAK,MAAEgY,GAAQ,MACgC,MAAVpb,qBAAU,GAA/C6T,GAAc,MAAEwH,GAAiB,MACU,MAAVrb,qBAAU,GAA3CqT,GAAY,MAAEiI,GAAe,MACY,MAAVtb,qBAAU,GAAzC4Q,GAAW,MAAE2K,GAAc,MAC2C,MAAzCvb,mBAAST,EAAE,+BAA8B,GAAtEic,GAAU,MAAEC,GAAa,MAC1BC,GAA4BC,cAC0I,MAApH3b,mBAASoa,IAAoBU,MAAsBc,aAAeC,aAAiBzB,IAAkB0B,gBAAe,GAArKpL,GAAoB,MAAEqL,GAAuB,MACsB,MAA9B/b,mBAASoD,GAAM4Y,gBAAe,GAAnEA,GAAc,MAAEC,GAAiB,MAC+B,MAAnCjc,mBAASoa,GAAmB,QAAO,GAAhE1b,GAAU,MAAEwd,GAAa,MAC0B,MAAdlc,mBAAS,MAAK,GAAnDmc,GAAc,MAAEC,GAAiB,MAClC3c,GAAWC,cAEXqQ,GAAgB,CACpBtG,GAAY4S,4BAGd9b,qBAAU,WACkB,iCAAtB2Z,aAAW,EAAXA,EAAanO,OACfhE,IAAKuU,YAAYxB,MAEnByB,GAAuBrC,KACtB,CAACA,IAEJ,IAAMqC,GAAyB,SAACrC,GAC9B,GAAKnS,IAAKiF,oBAGNkN,GAAeA,EAAYsC,YAAa,CAC1C,IAAMlQ,IAAsB4N,EAAYsC,cACxC/c,GAASoB,IAAQqN,kBAAkB,CAAEC,SAAU+L,EAAYnO,KAAMqC,UAAW9B,OAa1EmQ,GAAoC,SAACC,GAAU,MACnD,GAAIA,EAAMC,2BAGR,OAFAlB,GAAc,GAAD,OAAIlc,EAAE,mCAAkC,YAAIA,EAAE,yCAC3Dkb,GAAe,GAGjBgB,GAAc,GAAD,OAAIlc,EAAE+P,EAAwBoN,EAAME,SAAU,WAA4C,QAApC,EAAI5C,EAAiB0C,EAAME,iBAAS,aAAhC,EAAkCpZ,QAAM,YAAIjE,EAAE,qCAGjHsd,GAA+B,SAAC7O,GAAS,MACvCG,EAAWH,EAAKjC,KAChBvI,EAAkC,QAA7B,EAAGwW,EAAiB7L,UAAS,aAA1B,EAA4B3K,MAC1CiY,GAAc,GAAD,OAAIlc,EAAE+P,EAAwBnB,EAAU,UAAY3K,GAAM,YAAIjE,EAAE,+BAGzEud,GAAwB,SAACJ,GAC7B,IAAMK,EAAc,GAEpB,GAAIL,aAAiBjT,GAAYuT,mBAAoB,CACnD,IAAI5U,EAAc,QAClB,IACEA,EAAkC,SAAnBsU,EAAa,MAAY,UACjCA,EAAa,MAAC,YAAIA,EAAc,QACnCA,EAAa,MACjB,MAAOrU,GACPC,QAAQC,MAAMF,GAEhB0U,EAAuB,UAAIL,EAAM1W,UACjC+W,EAA2B,cAAIL,EAAMjU,mBACrCsU,EAAkB,KAAIL,EAAMhU,KAC5BqU,EAAsB,SAAIL,EAAM/T,SAChCoU,EAAuB,UAAIL,EAAM9T,UACjCmU,EAA+B,kBAAIL,EAAM7T,kBACzCkU,EAAgC,mBAAIL,EAAMrT,wBAC1C0T,EAAyB,YAAI3U,EAC7B2U,EAA4B,eAAIL,EAAMV,iBACtCC,GAAkBS,EAAMV,kBAExB,MN5BmC,SAACiB,EAAUC,GAClD,IAAMC,EAAqB,IAAIC,IACzBC,EAAkB,IAAID,IAE5B,IAAK,IAAMva,KAAOoa,EAChB,GAAIA,EAASK,eAAeza,GAAM,CAChC,IAAM8E,EAAQ4Q,SAAS1V,EAAK,KACvB0a,MAAM5V,IAA+B,MAArBuV,EAAUvV,IAAkBsV,EAASpa,GAAK,gBAC7Dsa,EAAmBK,IAAIP,EAASpa,GAAK,eAAe4a,SAEjDF,MAAM5V,IAA+B,MAArBuV,EAAUvV,IAAkBsV,EAASpa,GAAK,cAC7Dwa,EAAgBG,IAAIP,EAASpa,GAAK,aAAa4a,QAKrD,MAAO,CACL5W,MAAO8C,MAAM+T,KAAKP,GAClBQ,MAAOhU,MAAM+T,KAAKL,IMUSO,CAA0Bb,EAA2B,cAAGL,EAAMmB,eAA/EhX,EAAK,EAALA,MAAO8W,EAAK,EAALA,OACX9W,EAAM/E,QAAU,GAAuB,IAAjB+E,EAAM/E,QAAgB+E,EAAM,KAAOkW,EAAkB,QAC7EA,EAAkB,UAAIhT,IAEpB4T,EAAM7b,QAAU,GAAuB,IAAjB6b,EAAM7b,QAAgB6b,EAAM,KAAOZ,EAAsB,YACjFA,EAAsB,cAAIhT,GAI1B2S,aAAiBjT,GAAYqU,sBAC/Bf,EAAyB,YAAIL,EAAMqB,YACnChB,EAAkB,KAAIL,EAAMhU,KAC5BqU,EAAsB,SAAIL,EAAM/T,SAChCoU,EAAuB,UAAIL,EAAM9T,WAG/B8T,aAAiBjT,GAAYuU,uBAAuCjU,IAAnB2S,EAAM/T,WACzDoU,EAAYpU,SAAW+T,EAAM/T,UAG/ByS,GAAS,GAAD,MACHhY,IAAK,IACR4Q,YAAa0I,EAAM1I,YACnB+C,gBAAiB2F,EAAM3F,gBACvBD,QAAS4F,EAAM5F,QACfzC,UAAWqI,EAAMrI,WACd0I,IAEL1B,GAAkBqB,EAAMuB,cAAgBvB,EAAMuB,gBAAkB,QAChE3C,GAAgBoB,EAAMwB,YAAcxB,EAAMwB,cAAgB,QAC1D3C,GA7EqB,SAACmB,GACtB,IAAMtZ,EAAQsZ,EAAa,MACrByB,EAASzB,EAAc,OAC7B,MAAc,SAAVtZ,EACKA,EAEF,GAAP,OAAUA,EAAK,YAAI+a,GAuEJC,CAAe1B,KAGhCnc,qBAAU,WACR,IAAM8d,EAAuB,SAACC,GAC5B,GAAI5C,GAA0Bvc,SAASmf,aAAO,EAAPA,EAASvS,MAC9C,GAAKyP,GAEE,CACL,GAAInN,EAA4BiQ,aAAO,EAAPA,EAASvS,MAIvC,OAHA0O,GAAe,GACfF,GAAc,QACdsC,GAA6ByB,GAI/B7D,GAAe,GACfyB,GAAcoC,EAAQvS,MACtBgQ,GAAwBH,aAAeC,aAAiByC,EAAQvS,OAAO+P,gBACvEpB,EAAa4D,EAAQvS,OAAS7M,GAAUqf,SACxC5D,EAAc2D,EAAQvS,OAAS7M,GAAUsf,UACzC3D,EAAeyD,EAAQvS,OAAS7M,GAAU0b,WAE1CG,EAAYtL,EAAyB6O,EAAQvS,OAE7CiP,EACEsD,EAAQvS,OAAS7M,GAAUuf,UAC3BH,EAAQvS,OAAS7M,GAAUwf,oBAE7BzD,EAASqD,EAAQvS,OAAS7M,GAAUyf,KACpCzD,EAAWoD,EAAQvS,OAAS7M,GAAU0f,OACtCzD,GAA6BpT,IAAK8W,8BAA8BhO,6BAChE,IAAMiO,EAAaC,YAAcT,EAAQvS,OAErCuS,EAAQvS,KAAK5M,SAAS,aAAemf,EAAQvS,KAAK5M,SAAS,cAC7D2f,EAA2B,eAAIR,EAAkB,SAAkB,eACnErC,GAAkBqC,EAAkB,SAAkB,iBAGxDlD,GAAS0D,GACTzD,GAAkByD,EAAWE,gBAC7BzD,GAAeuD,EAAW1W,aAC1BkT,GAAgBwD,EAAWG,cAC3B1E,GAAc,GACdsC,GAA6ByB,QArC7B/D,GAAc,QAwChBA,GAAc,IAqCZ2E,EAAuB,SAACpP,EAAaqP,GACzC,GAAe,aAAXA,EAAuB,CACzB,GAAItP,EAAmBC,EAAaC,IAGlC,OAFA0K,GAAe,QACfF,GAAc,GAGhBA,GAAc,GACa,IAAvBzK,EAAYhO,QA1CY8D,EA2CHkK,EAAY,GA1CvC2M,GAAkC7W,GAC9ByI,EAA4BzI,EAAWgX,UACzCnC,GAAe,IAIjBA,GAAe,GACfyB,GAActW,EAAWgX,UACzBlC,EAAa9U,aAAsB6D,GAAY2V,mBAC/CzE,EAAc/U,aAAsB6D,GAAYuT,oBAChDnC,EAAejV,aAAsB6D,GAAYqU,qBACjD/C,EAAYtL,EAAyB7J,EAAWgX,WAChD5B,EAAcpV,aAAsB6D,GAAY4V,oBAChDpE,EAASrV,aAAsB6D,GAAY6V,eAC3CpE,EAAWtV,aAAsB6D,GAAY8V,iBAC7CpE,GAA6BpT,IAAK8W,8BAA8BhO,6BAChEkL,GAAwBH,aAAeC,aAAiBjW,EAAWgX,WAAWd,gBAC9EgB,GAAsBlX,KAEU,SAACkK,GACjC2L,GAAc,GAAD,OAAIlc,EAAE,mCAAkC,aAAKuQ,EAAYhO,OAAM,MAC5EgO,EAAY7K,SAAQ,SAACyX,GACnBI,GAAsBJ,MAsBpB8C,CAA0B1P,OAER,eAAXqP,GArBa,WACxB,IAAMjF,EAAcnS,IAAKoS,cACrBD,aAAuBnb,OAAOC,KAAKC,MAAMwgB,oBAC3ClF,GAAc,GAEhB8D,EAAqBnE,GACrBnS,IAAKuU,YAAYpC,EAAYnO,MAgB3B2T,GAhD2B,IAAC9Z,GAmD1B+Z,EAAuB,WAAM,IACgB,EADhB,E,ioBAAA,CACb5X,IAAK6X,0BAAwB,IAAjD,IAAK,EAAL,qBAAmD,KAAxClD,EAAK,QACdI,GAAsBJ,IACvB,gCAMH,OAHA3U,IAAK5G,iBAAiB,qBAAsB+d,GAC5CnX,IAAK5G,iBAAiB,kBAAmBkd,GACzCtW,IAAK5G,iBAAiB,oBAAqBwe,GACpC,WACL5X,IAAKlG,oBAAoB,qBAAsBqd,GAC/CnX,IAAKlG,oBAAoB,kBAAmBwc,GAC5CtW,IAAKlG,oBAAoB,oBAAqB8d,MAE/C,IAYHpf,qBAAU,WACR,GAAIwZ,EAAa,CACf,IAAM8F,EAAsB9X,IAAK6X,yBACjC,GAAI/P,EAAmBgQ,EAAqB9P,IAG1C,OAFA0K,GAAe,QACfF,GAAc,GAGhB,GAAmC,IAA/BsF,EAAoB/d,OAAc,CACpCyY,GAAc,GACd,IAAM3U,EAAaia,EAAoB,GACvC/C,GAAsBlX,GArBF,SAACA,GACzB8U,EAAa9U,aAAsB6D,GAAY2V,mBAC/CzE,EAAc/U,aAAsB6D,GAAYuT,oBAChDnC,EAAejV,aAAsB6D,GAAYqU,qBACjD9C,EAAcpV,aAAsB6D,GAAY4V,oBAChDpE,EAASrV,aAAsB6D,GAAY6V,eAC3CpE,EAAWtV,aAAsB6D,GAAY8V,iBAC7CxD,GAAwBH,aAAeC,aAAiBjW,EAAWgX,WAAWd,gBAe1EgE,CAAkBla,GAClB6W,GAAkC7W,QAC7B,GAAIia,EAAoB/d,OAAS,EACtCyY,GAAc,GACdkB,GAAc,GAAD,OAAIlc,EAAE,mCAAkC,aAAKsgB,EAAoB/d,OAAM,MACpF+d,EAAoB5a,SAAQ,SAACyX,GAC3BI,GAAsBJ,UAEnB,CACL,IAAMxC,EAAcnS,IAAKoS,cACzB,GAAID,GAAeA,EAAYnO,OAAS+O,KAAa,CACnDP,GAAc,GACd,IAAMuE,EAAaC,YAAc7E,EAAYnO,MACzC+S,IACF1D,GAAS0D,GACTzD,GAAkByD,EAAWE,gBAC7B1D,GAAgBwD,EAAWG,cAC3B1D,GAAeuD,EAAW1W,cAE5ByU,GAA6B3C,QAIlC,CAACH,EAAaiC,KAEjB,IAuHM+D,GACJ,oCACE,wBAAItiB,UAAU,sBACX8B,EAAE,+BAEL,yBAAK9B,UAAU,oBACb,6BACE,kBAACuiB,EAAA,EAAI,CAACviB,UAAU,aAAawiB,MAAM,kCAErC,yBAAKxiB,UAAU,OAAO8B,EAAE,gCAqC9B,OAAQ+a,EAGN,oCACE,wBAAI7c,UAAU,sBAAsB+d,IACnChB,EACC,yBAAK/c,UAAU,oBACb,6BACE,kBAACuiB,EAAA,EAAI,CAACviB,UAAU,aAAawiB,MAAM,kCAErC,yBAAKxiB,UAAU,OAAO8B,EAAE,4BAG1B,kBAAC,EAAW,CACVoU,iBAAkB,CAAC,UAAW,mBAC9BvQ,MAAOA,GACPkN,cArLc,SAAClG,EAAUF,GAC/B,IAAMgW,EAAW,GAAH,GAAQ9c,IACtB8c,EAAS9V,GAAYF,EACrBkR,GAAS8E,GACT,IAAMC,EAAsBpY,IAAK6X,yBAIjC,GAAmC,IAA/BO,EAAoBre,QAAgBqa,IAA+B,cAAb/R,EAA0B,CAClF,IAAMtE,EAASqW,GAAe,GAC9B,GAAIrW,WAAQsa,WAAY,CACtB,IAAM1D,EAAQP,GAAe,GAG7B,OAFArW,EAAOsV,SAAS,CAAEiF,WAAYnW,SAC9BwS,EAAiB,UAAI,IAAIjT,GAAYC,MAAMQ,KAK/C,GAAIiW,EAAoBre,OAAS,EAC/Bqe,EAAoBlb,SAAQ,SAACyX,GAC3B,GAAIrC,EAAgBlb,SAASiL,GAAW,CACtC,IAAMkW,EAAWC,YAAUrW,GACrBhN,EAAQ,IAAIuM,GAAYC,MAAM4W,EAASE,EAAGF,EAASG,EAAGH,EAASI,EAAGJ,EAASK,GACjF5Y,IAAK6Y,oBAAoBlE,EAAO,MAC7BtS,EAAWlN,GACX+c,GACCJ,GACFgH,aAAcnE,EAAME,SAAUxS,EAAUlN,QAG1C6K,IAAK6Y,oBAAoBlE,EAAO,MAC7BtS,EAAWF,GACX+P,GACCyC,aAAiBjT,GAAYuT,qBACd,aAAb5S,GAAwC,SAAbA,GAAoC,oBAAbA,GACpDE,aAA0BoS,IAG1B7C,GACFgH,aAAcnE,EAAME,SAAUxS,EAAUF,GAI5CnC,IAAKyC,uBAAuBsW,iBAAiBpE,GACzCA,aAAiBjT,GAAYuU,kBAC/BtB,EAAMqE,iBAGL,CACL,IAAM7G,EAAcnS,IAAKoS,cACzB,GAAID,EACF,GAAIG,EAAgBlb,SAASiL,GAAW,CACtC,IAAMkW,EAAWC,YAAUrW,GACrBhN,EAAQ,IAAIuM,GAAYC,MAAM4W,EAASE,EAAGF,EAASG,EAAGH,EAASI,EAAGJ,EAASK,GACjFE,aAAc3G,EAAYnO,KAAM3B,EAAUlN,OACpB,YAAbkN,EACTyW,aAAc3G,EAAYnO,KAAM,UAAW7B,GACrB,oBAAbE,EACTyW,aAAc3G,EAAYnO,KAAM,kBAAmB7B,GAEnD2W,aAAc3G,EAAYnO,KAAM3B,EAAUF,KA0H1CgB,WAAYA,EACZoG,UAAWA,EACX9K,YAAaA,EACbG,SAAUA,EACVmK,WAAYA,EACZC,MAAOA,EACPX,QAASA,EACTS,0BAA2BA,EAC3BH,qBAAsBA,GACtBmD,eAAgBA,GAChBR,aAAcA,GACdzC,YAAaA,GACb4C,kBAhIkB,SAACjE,EAASrF,GAClC,IAAM8W,EAAqB,CACzBC,MAAO,iBACPC,OAAQ,cACRC,IAAK,gBAES,UAAZ5R,EACF8L,GAAkBnR,GACG,WAAZqF,EACTgM,GAAerR,GACM,QAAZqF,GACT+L,GAAgBpR,GAElB,IAAMiW,EAAsBpY,IAAK6X,yBACjC,GAAIO,EAAoBre,OAAS,EAC/Bqe,EAAoBlb,SAAQ,SAACyX,GAC3B,GAAgB,UAAZnN,EACFmN,EAAM0E,cAAclX,QACf,GAAgB,WAAZqF,EAAsB,CAC/B,IAAM4O,EAASjU,EAAMmX,MAAM,KACrBC,EAAYnD,EAAOoD,QACzB7E,EAAM8E,MAAQF,EACd5E,EAAM+E,OAAStD,MACM,QAAZ5O,GACTmN,EAAMgF,YAAYxX,GAEpBnC,IAAKyC,qBAAqByP,GAAyB6G,iBAAiBpE,GAChE7C,GACFgH,aAAcnE,EAAME,SAAUoE,EAAmBzR,GAAUrF,MAI/DnC,IAAKyC,qBAAqByP,GAAyB0H,QAAQ,oBAAqB,CAACxB,EAAqB,SAAU,SAC3G,CACL,IAAMjG,EAAcnS,IAAKoS,cACrBD,GACF2G,aAAc3G,EAAYnO,KAAMiV,EAAmBzR,GAAUrF,KA6F3D9D,qBAzFe,WACrB,IAAM+Z,EAAsBpY,IAAK6X,yBAAyB,GAC1D,GAAIO,EACFyB,aAA6BzB,EAAqBlE,GAAmBD,QAChE,CACL,IAAM9B,EAAcnS,IAAKoS,cACrBD,IACF2G,aAAc3G,EAAYnO,KAAM,kBAAmB3I,GAAM4Y,gBACzDC,IAAmBD,OAkFjB9V,mBAAoB8V,GACpB1I,0BAhE0B,SAAClJ,EAAUF,GAAU,MAC/C2X,EAAmBzX,EACnB0X,EAAgB5X,EAChB6X,EAAgD,QAAzB,EAAG3e,GAAqB,qBAAC,aAAtB,EAAyB,GACxC,cAAbgH,GAAyC,iBAAbA,IAC9BF,EAAQ8X,aAAkB,MAAG5X,EAAWF,GAAS6X,GACjD3X,EAAW,mBAEb,IAAM6X,EAAgB,CACpBC,EAAG,GAAF,MACIH,GAAuB,SACzB3X,EAAWF,KAIViW,EAAsBpY,IAAK6X,yBACjC,GAAIO,EAAoBre,OAAS,EAC/Bqe,EAAoBlb,SAAQ,SAACW,GAC3BmC,IAAKoa,8BAA8Bvc,EAAY,MAAGic,EAAmBC,GAAiB7H,MAExFmB,GAAS,GAAD,MAAMhY,IAAK,IAAE,cAAiB6e,SACjC,CACL,IAAM/H,EAAcnS,IAAKoS,cACrBD,IACkC,mBAAzBA,EAAYkI,UACrBlI,EAAYkI,WAEdvB,aAAc3G,EAAYnO,KAAM,gBAAiBkW,MAsC/CvjB,WAAYA,GACZqV,mBAAoBqI,MAjC1B2D,I,SCreWsC,I,QARa,WAC1B,OACE,kBAACC,GAAA,EAAkB,CAAC7e,YAAY,aAAahG,UAAU,oBACrD,kBAAC,GAAU,SCNF4kB", "file": "chunks/chunk.37.js", "sourcesContent": ["import React, { useState, useCallback, useEffect, useRef } from 'react';\nimport classNames from 'classnames';\nimport './ColorPicker.scss';\nimport { useTranslation } from 'react-i18next';\nimport PropTypes from 'prop-types';\nimport actions from 'actions';\nimport { useDispatch, useStore, useSelector } from 'react-redux';\nimport Events from 'constants/events';\nimport { getInstanceNode } from 'helpers/getRootNode';\nimport selectors from 'selectors';\nimport Button from 'components/Button';\nimport useFocusHandler from 'hooks/useFocusHandler';\nimport Tooltip from 'components/Tooltip';\n\nconst parseColor = (color) => {\n  if (!color) {\n    return color;\n  }\n  let parsedColor = color;\n  if (parsedColor?.toHexString) {\n    parsedColor = parsedColor.toHexString();\n  }\n  if (parsedColor?.toLowerCase) {\n    parsedColor = parsedColor.toLowerCase();\n  }\n\n  return parsedColor;\n};\n\nconst TRANSPARENT_COLOR = 'transparent';\n\n/* eslint-disable custom/no-hex-colors */\nconst transparentIcon = (\n  <svg\n    width=\"100%\"\n    height=\"100%\"\n    className={classNames('transparent')}\n  >\n    <line stroke=\"#d82e28\" x1=\"0\" y1=\"100%\" x2=\"100%\" y2=\"0\" strokeWidth=\"2\" strokeLinecap=\"round\" />\n  </svg>\n);\n/* eslint-enable custom/no-hex-colors */\n\n\nconst propTypes = {\n  color: PropTypes.any,\n  ariaTypeLabel: PropTypes.string\n};\n\nconst ColorPicker = ({\n  onColorChange,\n  hasTransparentColor = false,\n  color,\n  activeTool,\n  type,\n  ariaTypeLabel\n}) => {\n  const activeToolName = Object.values(window.Core.Tools.ToolNames).includes(activeTool) ? activeTool : window.Core.Tools.ToolNames.EDIT;\n  const store = useStore();\n  const { t } = useTranslation();\n  const dispatch = useDispatch();\n  const [colors] = useSelector((state) => [\n    selectors.getColors(state, activeToolName, type),\n  ]);\n  const [selectedColor, setSelectedColor] = useState();\n  const [isExpanded, setIsExpanded] = useState(false);\n  const forceExpandRef = useRef(true);\n\n  useEffect(() => {\n    forceExpandRef.current = true;\n  }, [activeToolName, color]);\n\n  useEffect(() => {\n    if (color) {\n      setSelectedColor(parseColor(color));\n    }\n  }, [color]);\n\n  const getCustomColorAndRemove = () => {\n    const customColor = selectors.getCustomColor(store.getState());\n    dispatch(actions.setCustomColor(null));\n    return customColor;\n  };\n\n  const handleAddColor = useCallback(() => {\n    dispatch(actions.openElement('ColorPickerModal'));\n    const onVisibilityChanged = (e) => {\n      const { element, isVisible } = e.detail;\n      if (element === 'ColorPickerModal' && !isVisible) {\n        const color = parseColor(getCustomColorAndRemove());\n        if (color) {\n          if (colors.includes(color)) {\n            setSelectedColor(color);\n            onColorChange(color);\n          } else {\n            const newColors = [...colors, color];\n            dispatch(actions.setColors(newColors, activeToolName, type, true));\n            setSelectedColor(color);\n            onColorChange(color);\n          }\n        }\n        getInstanceNode().removeEventListener(Events.VISIBILITY_CHANGED, onVisibilityChanged);\n      }\n    };\n    getInstanceNode().addEventListener(Events.VISIBILITY_CHANGED, onVisibilityChanged);\n  }, [colors?.length, dispatch, setSelectedColor, onColorChange, getCustomColorAndRemove, type, activeToolName]);\n\n  const openColorPickerModalWithFocus = useFocusHandler(handleAddColor);\n\n  const handleDelete = () => {\n    const color = parseColor(selectedColor);\n    const newColors = [...colors];\n    const indexToDelete = newColors.indexOf(color);\n    if (indexToDelete > -1) {\n      const nextIndex = indexToDelete === newColors.length - 1 ? 0 : indexToDelete + 1;\n      setSelectedColor(colors[nextIndex]);\n      onColorChange(colors[nextIndex]);\n      newColors.splice(indexToDelete, 1);\n      dispatch(actions.setColors(newColors, activeToolName, type, true));\n    }\n  };\n\n  const handleCopyColor = () => {\n    const color = parseColor(selectedColor);\n    const newColors = [...colors, color];\n    dispatch(actions.setColors(newColors, activeToolName, type, true));\n  };\n\n  const toggleExpanded = () => {\n    const newValue = !isExpanded;\n    setIsExpanded(newValue);\n  };\n\n  let palette = colors.map((color) => color.toLowerCase());\n  if (hasTransparentColor) {\n    palette.push(TRANSPARENT_COLOR);\n  }\n\n  if (!selectedColor) {\n    setSelectedColor('transparent');\n  }\n\n  if (palette.indexOf(selectedColor) > 6 && !isExpanded && forceExpandRef.current) {\n    setIsExpanded(true);\n    forceExpandRef.current = false;\n  }\n\n  const shouldHideShowMoreButton = palette.length <= 7;\n  const showCopyButtonDisabled = !(selectedColor && !palette.includes(selectedColor));\n  const isDeleteDisabled = palette.length <= 1 || !showCopyButtonDisabled;\n\n  if (!isExpanded) {\n    palette = palette.slice(0, 7);\n  }\n\n  return (\n    <>\n      <div className={classNames('ColorPalette')}>\n        {palette.map((color) => parseColor(color)).map((color, i) => (\n          !color\n            ? <div key={i} className=\"dummy-cell\"/>\n            : <Tooltip content={`${t('option.colorPalette.colorLabel')} ${color?.toUpperCase?.()}`} key={color?.toUpperCase?.()}>\n              <button\n                className=\"cell-container\"\n                onClick={() => {\n                  setSelectedColor(color);\n                  onColorChange(color);\n                }}\n                aria-label={`${ariaTypeLabel} ${t('option.colorPalette.colorLabel')} ${color?.toUpperCase?.()}`}\n                aria-current={parseColor(selectedColor) === color || (!parseColor(selectedColor) && color === TRANSPARENT_COLOR)}\n              >\n                <div\n                  className={classNames({\n                    'cell-outer': true,\n                    active: parseColor(selectedColor) === color || (!parseColor(selectedColor) && color === TRANSPARENT_COLOR),\n                  })}\n                >\n                  <div\n                    className={classNames({\n                      cell: true,\n                      border: true,\n                    })}\n                    style={{ backgroundColor: color }}\n                  >\n                    {color === TRANSPARENT_COLOR && transparentIcon}\n                  </div>\n                </div>\n              </button>\n            </Tooltip>\n        ))}\n      </div>\n      <div className=\"palette-controls\">\n        <div className=\"button-container\">\n          <Button\n            img=\"icon-header-zoom-in-line\"\n            title={t('action.addNewColor')}\n            onClick={openColorPickerModalWithFocus}\n            className=\"control-button\"\n            dataElement=\"addCustomColor\"\n            ariaLabel={`${ariaTypeLabel} ${t('action.addNewColor')} ${t('action.fromCustomColorPicker')}`}\n          />\n          <Button\n            img=\"icon-delete-line\"\n            title={t('action.deleteColor')}\n            onClick={handleDelete}\n            disabled={isDeleteDisabled}\n            className=\"control-button\"\n            dataElement=\"deleteSelectedColor\"\n            ariaLabel={`${ariaTypeLabel} ${t('action.deleteColor')} ${selectedColor}`}\n          />\n          <Button\n            img=\"icon-copy2\"\n            title={t('action.copySelectedColor')}\n            onClick={handleCopyColor}\n            disabled={showCopyButtonDisabled}\n            className=\"control-button\"\n            dataElement=\"copySelectedColor\"\n            ariaLabel={`${ariaTypeLabel} ${t('action.copySelectedColor')} ${selectedColor}`}\n          />\n        </div>\n        <button\n          className={classNames('show-more-button control-button', {\n            hidden: shouldHideShowMoreButton,\n          })}\n          onClick={toggleExpanded}\n          aria-label={`${ariaTypeLabel} ${t(isExpanded ? t('action.showLessColors') : t('action.showMoreColors'))}`}\n        >\n          {t(isExpanded ? 'message.showLess' : 'message.showMore')}\n        </button>\n      </div>\n    </>\n  );\n};\n\nColorPicker.propTypes = propTypes;\n\nexport default ColorPicker;", "import ColorPicker from './ColorPicker';\n\nexport default ColorPicker;", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./ColorPicker.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.StylePicker .ColorPalette{display:flex;flex-wrap:wrap;display:grid;grid-template-columns:repeat(7,1fr);grid-row-gap:8px;row-gap:8px;justify-items:center}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.StylePicker .ColorPalette{width:196px}}.StylePicker .ColorPalette.padding{padding:4px 12px 8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .ColorPalette{max-width:450px;width:auto}}@media(max-width:640px)and (-ms-high-contrast:active),(max-width:640px)and (-ms-high-contrast:none){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .ColorPalette{width:308px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .ColorPalette{max-width:450px;width:auto}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .ColorPalette{width:308px}}}.StylePicker .ColorPalette .cell-container{padding:0;border:none;background-color:transparent;flex:1 0 14%;cursor:pointer;width:var(--cell-border-size);height:var(--cell-border-size);display:flex;align-items:center;justify-content:center}:host(:not([data-tabbing=true])) .StylePicker .ColorPalette .cell-container,html:not([data-tabbing=true]) .StylePicker .ColorPalette .cell-container{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .ColorPalette .cell-container{width:44px;height:44px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .ColorPalette .cell-container{width:44px;height:44px}}.StylePicker .ColorPalette .cell-container .cell-outer.active{border:1px solid var(--color-palette-border);width:var(--cell-outer-border-size);height:var(--cell-outer-border-size);border-radius:10000000px;display:flex;align-items:center;justify-content:center}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .ColorPalette .cell-container .cell-outer.active{width:36px;height:36px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .ColorPalette .cell-container .cell-outer.active{width:36px;height:36px}}.StylePicker .ColorPalette .cell-container .cell-outer .cell{width:18px;height:18px;border-radius:10000000px}.StylePicker .ColorPalette .cell-container .cell-outer .cell .transparent{border:2px solid var(--faded-text);border-radius:10000000px}.StylePicker .ColorPalette .cell-container .cell-outer .cell.border{border:1px solid var(--white-color-palette-border)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .ColorPalette .cell-container .cell-outer .cell{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .ColorPalette .cell-container .cell-outer .cell{width:24px;height:24px}}.StylePicker .palette-controls{padding-right:12px;padding-left:2px;display:flex;justify-content:space-between}.StylePicker .palette-controls .button-container{display:flex;grid-gap:8px;gap:8px}.StylePicker .palette-controls .control-button{display:flex;align-items:center;justify-content:center;text-align:center;min-width:32px;min-height:32px;padding:0;border:none;background-color:transparent;cursor:pointer;border-radius:4px}:host(:not([data-tabbing=true])) .StylePicker .palette-controls .control-button,html:not([data-tabbing=true]) .StylePicker .palette-controls .control-button{outline:none}.StylePicker .palette-controls .control-button.show-more-button{color:var(--ribbon-active-color)}.StylePicker .palette-controls .control-button.show-more-button:hover{background:none;color:var(--primary-button-hover)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .palette-controls .control-button.show-more-button{color:var(--ribbon-active-color)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .palette-controls .control-button.show-more-button{color:var(--ribbon-active-color)}}.StylePicker .palette-controls .control-button:disabled{cursor:no-drop}.StylePicker .palette-controls .control-button:disabled .Icon{color:var(--disabled-icon)}.StylePicker .palette-controls .control-button.hidden{display:none}.StylePicker .palette-controls .control-button.focus-visible,.StylePicker .palette-controls .control-button:focus-visible{outline:var(--focus-visible-outline)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./RichTextStyleEditor.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.RichTextStyleEditor{margin-bottom:16px}.RichTextStyleEditor .menu-items{margin-bottom:8px!important}.RichTextStyleEditor .menu-items .icon-grid{padding-top:12px;grid-row-gap:12px;row-gap:12px}.RichTextStyleEditor .menu-items .icon-grid .row{padding-top:0}.RichTextStyleEditor .menu-items .icon-grid .row.isRedaction{padding-bottom:8px}.RichTextStyleEditor .menu-items .icon-grid .auto-size-checkbox{padding-top:4px;padding-bottom:8px}.RichTextStyleEditor .menu-items .icon-grid .auto-size-checkbox .ui__choice__input__check--focus{outline:var(--focus-visible-outline)}.RichTextStyleEditor .Dropdown__wrapper{width:100%}.RichTextStyleEditor .Dropdown__wrapper .Dropdown{width:100%!important}.RichTextStyleEditor .Dropdown__wrapper .Dropdown__items{right:unset;width:100%!important}.RichTextStyleEditor .FontSizeDropdown{width:100%!important}.RichTextStyleEditor .ColorPalette{padding-bottom:8px}.RichTextStyleEditor .text-size-slider{margin-top:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RichTextStyleEditor .icon-grid{display:flex;flex-direction:column}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RichTextStyleEditor .icon-grid{display:flex;flex-direction:column}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState, useEffect, useRef } from 'react';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport ColorPicker from 'components/StylePicker/ColorPicker';\nimport core from 'core';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport './RichTextStyleEditor.scss';\nimport DataElements from 'constants/dataElement';\nimport TextStylePicker from 'components/TextStylePicker';\nimport adjustFreeTextBoundingBox from 'helpers/adjustFreeTextBoundingBox';\nimport { useTranslation } from 'react-i18next';\n\nconst propTypes = {\n  annotation: PropTypes.object,\n  editor: PropTypes.object,\n  style: PropTypes.shape({\n    TextColor: PropTypes.string,\n    RichTextStyle: PropTypes.any,\n  }),\n  isFreeTextAutoSize: PropTypes.bool,\n  onFreeTextSizeToggle: PropTypes.func,\n  onPropertyChange: PropTypes.func,\n  onRichTextStyleChange: PropTypes.func,\n  isRedaction: PropTypes.bool,\n  isRichTextEditMode: PropTypes.bool,\n  setIsRichTextEditMode: PropTypes.func,\n  isWidget: PropTypes.bool,\n};\n\nconst RichTextStyleEditor = ({\n  annotation, editor,\n  style,\n  isFreeTextAutoSize,\n  onFreeTextSizeToggle,\n  onPropertyChange,\n  onRichTextStyleChange,\n  isRichTextEditMode,\n  setIsRichTextEditMode,\n  isRedaction,\n  isWidget,\n  activeTool,\n}) => {\n  const [\n    fonts,\n  ] = useSelector(\n    (state) => [\n      selectors.getFonts(state),\n    ],\n    shallowEqual,\n  );\n\n  const [format, setFormat] = useState({});\n  const editorRef = useRef(null);\n  const annotationRef = useRef(null);\n  const propertiesRef = useRef({});\n  const dispatch = useDispatch();\n  const oldSelectionRef = useRef();\n  const richTextEditModeRef = useRef();\n  richTextEditModeRef.current = isRichTextEditMode;\n  const [t] = useTranslation();\n\n  useEffect(() => {\n    const handleSelectionChange = (range, oldRange) => {\n      const shouldRestoreLostSelection = !range && oldRange && editorRef.current;\n      if (shouldRestoreLostSelection) {\n        editorRef.current.setSelection(oldRange.index, oldRange.length);\n      }\n      if (range && editorRef.current) {\n        setFormat(getFormat(range));\n      }\n    };\n    const handleTextChange = () => {\n      setFormat(getFormat(editorRef.current?.getSelection()));\n    };\n    core.addEventListener('editorSelectionChanged', handleSelectionChange);\n    core.addEventListener('editorTextChanged', handleTextChange);\n    // Have to disable instead of closing because annotation popup will reopen itself\n    dispatch(actions.disableElements([DataElements.ANNOTATION_STYLE_POPUP]));\n    return () => {\n      core.removeEventListener('editorSelectionChanged', handleSelectionChange);\n      core.removeEventListener('editorTextChanged', handleTextChange);\n      dispatch(actions.enableElements([DataElements.ANNOTATION_STYLE_POPUP]));\n    };\n  }, []);\n\n  useEffect(() => {\n    editorRef.current = editor;\n    annotationRef.current = annotation;\n    if (isRichTextEditMode && annotation) {\n      let StrokeStyle = 'solid';\n      try {\n        StrokeStyle = (annotation['Style'] === 'dash')\n          ? `${annotation['Style']},${annotation['Dashes']}`\n          : annotation['Style'];\n      } catch (err) {\n        console.error(err);\n      }\n      const richTextStyles = annotation.getRichTextStyle();\n      const stylesTemp = richTextStyles[0];\n\n      propertiesRef.current = {\n        Font: annotation.Font,\n        FontSize: annotation.FontSize,\n        TextAlign: annotation.TextAlign,\n        TextVerticalAlign: annotation.TextVerticalAlign,\n        bold: stylesTemp?.['font-weight'] === 'bold' ?? false,\n        italic: stylesTemp?.['font-style'] === 'italic' ?? false,\n        underline: stylesTemp?.['text-decoration']?.includes('underline')\n          || stylesTemp?.['text-decoration']?.includes('word'),\n        strikeout: stylesTemp?.['text-decoration']?.includes('line-through') ?? false,\n        size: stylesTemp?.['font-size'],\n        font: stylesTemp?.['font-family'],\n        StrokeStyle,\n        calculatedFontSize: annotation.getCalculatedFontSize()\n      };\n    }\n\n    setFormat(getFormat(editorRef.current?.getSelection()));\n\n    if (oldSelectionRef.current) {\n      editorRef.current.setSelection(oldSelectionRef.current);\n      oldSelectionRef.current = null;\n    }\n  }, [annotation, editor, isRichTextEditMode]);\n\n  useEffect(() => {\n    const handleEditorBlur = () => {\n      editorRef.current = null;\n      annotationRef.current = null;\n      setIsRichTextEditMode(false);\n    };\n    const handleEditorFocus = () => {\n      setIsRichTextEditMode(true);\n    };\n\n    core.addEventListener('editorBlur', handleEditorBlur);\n    core.addEventListener('editorFocus', handleEditorFocus);\n    return () => {\n      core.removeEventListener('editorBlur', handleEditorBlur);\n      core.removeEventListener('editorFocus', handleEditorFocus);\n    };\n  }, [dispatch]);\n\n\n  const getFormat = (range) => {\n    if (!range) {\n      return {};\n    }\n\n    const format = editorRef.current.getFormat(range.index, range.length);\n\n    if (typeof format.color === 'string') {\n      format.color = new window.Core.Annotations.Color(format.color);\n    } else if (Array.isArray(format.color)) {\n      // the selection contains multiple color, so we set the current color to the last selected color\n      const lastSelectedColor = new window.Core.Annotations.Color(format.color[format.color.length - 1]);\n      format.color = lastSelectedColor;\n    } else if (!format.color) {\n      format.color = annotationRef.current.TextColor;\n    }\n\n    const propertiesToCheck = ['font', 'size', 'originalSize'];\n\n    for (const prop of propertiesToCheck) {\n      if (format[prop] && Array.isArray(format[prop])) {\n        format[prop] = undefined;\n      }\n    }\n\n    return format;\n  };\n\n  const handleTextFormatChange = (format) => () => {\n    let { index, length } = editorRef.current.getSelection();\n    if (length === 0) {\n      oldSelectionRef.current = { index, length };\n      const newSelection = editorRef.current.getSelection();\n      index = newSelection.index;\n      length = newSelection.length;\n    }\n    const currentFormat = editorRef.current.getFormat(index, length);\n\n    applyFormat(format, !currentFormat[format]);\n  };\n\n  const handleColorChange = (name, color) => {\n    if (!richTextEditModeRef.current) {\n      onPropertyChange(name, color);\n      return;\n    }\n    applyFormat('color', color.toHexString());\n  };\n\n  const applyFormat = (formatKey, value) => {\n    if (formatKey === 'size') {\n      editorRef.current?.format('applyCustomFontSize', value);\n    } else {\n      editorRef.current?.format(formatKey, value);\n    }\n\n    if (formatKey === 'color') {\n      value = new window.Core.Annotations.Color(value);\n    }\n\n    // format the entire editor doesn't trigger the editorTextChanged event, so we set the format state here\n    setFormat({\n      ...format,\n      [formatKey]: value\n    });\n  };\n\n  // onPropertyChange\n  const handlePropertyChange = (property, value) => {\n    if (!richTextEditModeRef.current) {\n      onPropertyChange(property, value);\n      return;\n    }\n\n    const { index, length } = editorRef.current.getSelection();\n    const annotation = annotationRef.current;\n    annotation[property] = value;\n    editorRef.current.blur();\n    if (property === 'FontSize' || property === 'Font') {\n      adjustFreeTextBoundingBox(annotation);\n    }\n    setTimeout(() => {\n      oldSelectionRef.current = { index, length };\n      const editBoxManager = core.getAnnotationManager().getEditBoxManager();\n      editBoxManager.focusBox(annotation);\n    }, 0);\n  };\n\n\n  // onRichTextStyleChange\n  const handleRichTextStyleChange = (property, value) => {\n    if (!richTextEditModeRef.current) {\n      onRichTextStyleChange(property, value);\n      return;\n    }\n\n    const propertyTranslation = {\n      'font-weight': 'bold',\n      'font-style': 'italic',\n      'underline': 'underline',\n      'line-through': 'strike',\n      'font-family': 'font',\n      'font-size': 'size',\n    };\n    if (property === 'font-family' || property === 'font-size') {\n      applyFormat(propertyTranslation[property], value);\n      const freeText = annotationRef.current;\n      if (freeText.isAutoSized()) {\n        const editBoxManager = core.getAnnotationManager().getEditBoxManager();\n        editBoxManager.resizeAnnotation(freeText);\n      }\n    } else {\n      handleTextFormatChange(propertyTranslation[property])();\n    }\n  };\n\n  let properties = {};\n\n  const { RichTextStyle } = style;\n  const defaults = {\n    bold: RichTextStyle?.[0]?.['font-weight'] === 'bold' ?? false,\n    italic: RichTextStyle?.[0]?.['font-style'] === 'italic' ?? false,\n    underline: RichTextStyle?.[0]?.['text-decoration']?.includes('underline') || RichTextStyle?.[0]?.['text-decoration']?.includes('word'),\n    strikeout: RichTextStyle?.[0]?.['text-decoration']?.includes('line-through') ?? false,\n    font: RichTextStyle?.[0]?.['font-family'],\n    size: RichTextStyle?.[0]?.['font-size'],\n    StrokeStyle: 'solid',\n  };\n\n  properties = {\n    ...style,\n    ...defaults,\n  };\n\n  if (isRichTextEditMode && annotation) {\n    propertiesRef.current.bold = format.bold;\n    propertiesRef.current.italic = format.italic;\n    propertiesRef.current.underline = format.underline;\n    propertiesRef.current.strikeout = format.strike;\n    propertiesRef.current.quillFont = format.font || propertiesRef.current.Font;\n    propertiesRef.current.quillFontSize = format.originalSize || propertiesRef.current.FontSize;\n  }\n\n  const commonProps = {\n    fonts: fonts,\n    onPropertyChange: handlePropertyChange,\n    properties: properties,\n    stateless: true,\n    isFreeText: !isRedaction,\n  };\n\n  const nonWidgetProps = {\n    onRichTextStyleChange: handleRichTextStyleChange,\n    properties: isRichTextEditMode ? propertiesRef.current : properties,\n    isFreeTextAutoSize: isFreeTextAutoSize,\n    isRichTextEditMode: isRichTextEditMode,\n    isRedaction: isRedaction,\n    onFreeTextSizeToggle: onFreeTextSizeToggle,\n  };\n\n  const widgetProps = {\n    onRichTextStyleChange: handlePropertyChange,\n    isFreeTextAutoSize: false,\n    isRichTextEditMode: false,\n    isRedaction: false,\n    isWidget: isWidget,\n  };\n\n  return (\n    <div className=\"RichTextStyleEditor\"\n      onMouseDown={(e) => {\n        if (e.type !== 'touchstart' && isRichTextEditMode) {\n          e.preventDefault();\n        }\n      }}\n    >\n      <div className=\"menu-items\">\n        <TextStylePicker\n          {...commonProps}\n          {...(isWidget ? widgetProps : nonWidgetProps)}\n        />\n      </div>\n      <ColorPicker\n        onColorChange={(color) => {\n          handleColorChange('TextColor', new window.Core.Annotations.Color(color));\n        }}\n        color={isRichTextEditMode ? format.color : style['TextColor']}\n        activeTool={activeTool}\n        type={'Text'}\n        ariaTypeLabel={t('option.stylePopup.textStyle')}\n      />\n    </div>\n  );\n};\nRichTextStyleEditor.propTypes = propTypes;\n\nexport default React.memo(RichTextStyleEditor);\n", "import RichTextStyleEditor from './RichTextStyleEditor';\n\nexport default RichTextStyleEditor;", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./StylePicker.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.StylePicker{display:flex;flex-direction:column}.StylePicker .slider-property{font-size:14px;font-weight:700;margin-bottom:8px!important}.StylePicker .StyleOption{margin-bottom:16px}.StylePicker .StyleOption .styles-container .styles-title{margin:0 0 8px;font-size:14px;font-weight:700}.StylePicker .StyleOption .styles-container [data-element=borderStylePicker]{margin-top:8px}.StylePicker .StyleOption .slider:only-child{margin-bottom:0}.StylePicker .StyleOption .slider .slider-element-container{margin-left:-3px}.StylePicker .StyleOption:last-child{margin-bottom:0}.StylePicker .PanelSection~.PanelSection .CollapsibleSection>.collapsible-page-group-header{margin-top:16px}.StylePicker .PanelSection .CollapsibleSection>.collapsible-page-group-header>button{font-size:16px;padding:0;font-weight:700;height:31.5px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .PanelSection .CollapsibleSection>.collapsible-page-group-header>button{font-size:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .PanelSection .CollapsibleSection>.collapsible-page-group-header>button{font-size:16px}}.StylePicker .PanelSection .CollapsibleSection:first-of-type{margin-bottom:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .PanelSection .CollapsibleSection{border-bottom:none}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .PanelSection .CollapsibleSection{border-bottom:none}}.StylePicker .PanelSection .panel-section-wrapper.Opacity{margin-top:16px}.StylePicker .PanelSection:first-child .panel-section-wrapper.Opacity{margin-top:0}.StylePicker .PanelSection .collapsible-page-group-header+.collapsible-content{margin-top:16px}.StylePicker .PanelSection .PanelSubsection{margin-bottom:12px}.StylePicker .PanelSection .PanelSubsection .menu-subtitle{font-size:14px;font-weight:700;margin-bottom:12px}.StylePicker .PanelSection .divider{background-color:var(--divider);width:100%;height:1px}.StylePicker .PanelSection .menu-items{margin-bottom:16px}.StylePicker .PanelSection .menu-items:only-child{margin-bottom:0}.StylePicker .PanelSection .menu-items .ColorPalette{margin-bottom:8px}.StylePicker .PanelSection .slider{margin-bottom:16px}.StylePicker .PanelSection .slider:last-child,.StylePicker .PanelSection:empty,.StylePicker .PanelSection:last-child{margin-bottom:0}.StylePicker .PanelSection .snapping-option{margin-top:16px}.StylePicker .spacer{width:100%}.StylePicker .Dropdown,.StylePicker .FontSizeDropdown,.StylePicker .overlay-text-input{height:32px}.StylePicker .overlay-text-input:focus{border-color:var(--blue-5)}.StylePicker .lineStyleContainer{margin-bottom:0!important}.StylePicker .lineStyleContainer .StylePicker-LineStyle{display:flex;flex-direction:row;grid-column-gap:8px;-moz-column-gap:8px;column-gap:8px;justify-content:space-between}.StylePicker .lineStyleContainer .StylePicker-LineStyle div.Dropdown{width:100%!important}.StylePicker .lineStyleContainer .StylePicker-LineStyle .Dropdown__items,.StylePicker .lineStyleContainer .StylePicker-LineStyle .Dropdown__wrapper{width:100%}.StylePicker .lineStyleContainer .StylePicker-LineStyle .linestyle-image svg{width:35px;margin-top:11px}.StylePicker .lineStyleContainer .StylePicker-LineStyle .linestyle-image.shift-alignment svg{margin-top:8px}.StylePicker .lineStyleContainer .StylePicker-LineStyle .Dropdown__items .linestyle-image svg{width:45px}.StylePicker .lineStyleContainer .StylePicker-LineStyle [data-element=middleLineStyleDropdown] .linestyle-image.shift-alignment{padding-top:0}.StylePicker .lineStyleContainer .StylePicker-LineStyle [data-element=middleLineStyleDropdown] .linestyle-image.shift-alignment svg{margin-top:11px}.StylePicker .lineStyleContainer .StylePicker-LineStyle [data-element=middleLineStyleDropdown] .Dropdown__items{top:-197px}.StylePicker .lineStyleContainer .StylePicker-LineStyle .StyleOptions{max-width:80px}.StylePicker .lineStyleContainer .StylePicker-LineStyle>*{flex-grow:1;flex-basis:0}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker{padding:0 16px 16px;overflow:auto}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker{padding:0 16px 16px;overflow:auto}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./StylePanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.StylePanel{display:flex;flex-direction:column;background-color:var(--panel-background);padding-bottom:75px}.StylePanel .style-panel-header{font-size:16px;font-weight:700;margin-top:0;margin-bottom:16px}.StylePanel .label{padding-top:16px;font-size:14px;font-weight:700}.StylePanel .no-tool-selected{padding-top:36px;display:flex;flex-direction:column;align-items:center;flex:1 1 auto}.StylePanel .no-tool-selected .msg{padding-top:24px;font-size:13px;text-align:center}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePanel .no-tool-selected .msg{line-height:15px;width:146px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePanel .no-tool-selected .msg{line-height:15px;width:146px}}.StylePanel .no-tool-selected .empty-icon,.StylePanel .no-tool-selected .empty-icon svg{width:55px;height:56px}.StylePanel .no-tool-selected .empty-icon *{fill:var(--gray-6);color:var(--gray-6)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePanel{width:100%;height:100%;padding-bottom:16px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePanel .style-panel-header{margin:0 16px 16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePanel{width:100%;height:100%;padding-bottom:16px}.App.is-web-component:not(.is-in-desktop-only-mode) .StylePanel .style-panel-header{margin:0 16px 16px}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import SnapModeToggle from './SnapModeToggle';\n\nexport default SnapModeToggle;", "import React from 'react';\nimport { workerTypes } from 'constants/types';\nimport core from 'core';\nimport i18next from 'i18next';\nimport Choice from 'components/Choice/Choice';\nimport getMeasurementTools from 'helpers/getMeasurementTools';\nimport actions from 'actions';\nimport { useDispatch } from 'react-redux';\n\nconst SnapModeToggle = ({\n  Scale,\n  Precision,\n  isSnapModeEnabled,\n}) => {\n  const dispatch = useDispatch();\n\n  const wasDocumentSwappedToClientSide =\n    core.getDocument()?.getType() === workerTypes.WEBVIEWER_SERVER && core.getDocument().isWebViewerServerDocument();\n  const isEligibleDocumentForSnapping = core.getDocument()?.getType().toLowerCase() === workerTypes.PDF || wasDocumentSwappedToClientSide;\n  const showMeasurementSnappingOption = Scale && Precision && isEligibleDocumentForSnapping && core.isFullPDFEnabled();\n\n  const onSnappingChange = (event) => {\n    if (!core.isFullPDFEnabled()) {\n      return;\n    }\n\n    const enableSnapping = event.target.checked;\n    const mode = enableSnapping\n      ? core.getDocumentViewer().SnapMode.e_DefaultSnapMode | core.getDocumentViewer().SnapMode.POINT_ON_LINE\n      : null;\n    const measurementTools = getMeasurementTools();\n\n    measurementTools.forEach((tool) => {\n      tool.setSnapMode?.(mode);\n      dispatch(actions.setEnableSnapMode({ toolName: tool.name, isEnabled: enableSnapping }));\n    });\n\n  };\n\n  return (\n    <>\n      {showMeasurementSnappingOption && (\n        <div className=\"snapping-option\">\n          <Choice\n            dataElement=\"measurementSnappingOption\"\n            id=\"measurement-snapping\"\n            type=\"checkbox\"\n            label={i18next.t('option.shared.enableSnapping')}\n            checked={isSnapModeEnabled}\n            onChange={onSnappingChange}\n          />\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default SnapModeToggle;", "import core from 'core';\n\nconst Tools = window.Core.Tools;\n\nexport const shouldHideStylePanelOptions = (toolName) => {\n  const toolsNoStylePanelOptions = [\n    Tools.AddParagraphTool,\n    Tools.AddImageContentTool,\n    Tools.CropCreateTool,\n    Tools.SnippingCreateTool,\n  ];\n\n  return toolsNoStylePanelOptions.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const hasFillColorAndCollapsablePanelSections = (toolName) => {\n  const toolsWithCollapsedStylePanels = [\n    Tools.RectangleCreateTool,\n    Tools.EllipseCreateTool,\n    Tools.PolygonCreateTool,\n    Tools.PolygonCloudCreateTool,\n    Tools.EllipseMeasurementCreateTool,\n    Tools.AreaMeasurementCreateTool,\n    Tools.FreeTextCreateTool,\n    Tools.CalloutCreateTool,\n    Tools.RedactionCreateTool,\n    // ... form builder\n    Tools.TextFormFieldCreateTool,\n    Tools.RadioButtonFormFieldCreateTool,\n    Tools.CheckBoxFormFieldCreateTool,\n    Tools.ListBoxFormFieldCreateTool,\n    Tools.ComboBoxFormFieldCreateTool\n  ];\n\n  return toolsWithCollapsedStylePanels.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const shouldHideFillColorAndCollapsablePanelSections = (toolName) => {\n  const toolsWithHiddenFillColorSections = [\n    Tools.RubberStampCreateTool,\n    Tools.StampCreateTool,\n    Tools.EraserTool,\n  ];\n  return toolsWithHiddenFillColorSections.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const shouldHideStrokeSlider = (toolName) => {\n  const toolsWithHiddenStrokeSlider = [\n    Tools.TextUnderlineCreateTool,\n    Tools.TextHighlightCreateTool,\n    Tools.TextSquigglyCreateTool,\n    Tools.TextStrikeoutCreateTool,\n    Tools.CountMeasurementCreateTool,\n    Tools.RubberStampCreateTool,\n    Tools.FileAttachmentCreateTool,\n    Tools.StampCreateTool,\n    Tools.StickyCreateTool,\n    Tools.MarkInsertTextCreateTool,\n    Tools.MarkReplaceTextCreateTool,\n  ];\n  return toolsWithHiddenStrokeSlider.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const shouldHideStrokeStyle = (toolName) => {\n  const toolsWithHiddenStrokeStyle = [\n    Tools.RubberStampCreateTool,\n    Tools.StampCreateTool,\n    Tools.EraserTool,\n  ];\n  return toolsWithHiddenStrokeStyle.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const shouldShowTextStyle = (toolName) => {\n  const toolsWithHiddenStrokeSlider = [\n    Tools.FreeTextCreateTool,\n    Tools.CalloutCreateTool,\n    Tools.RedactionCreateTool,\n    Tools.TextFormFieldCreateTool,\n    Tools.ListBoxFormFieldCreateTool,\n    Tools.ComboBoxFormFieldCreateTool,\n  ];\n  return toolsWithHiddenStrokeSlider.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const shouldHideOpacitySlider = (toolName) => {\n  const toolsWithHiddenOpacitySlider = [\n    Tools.RedactionCreateTool,\n    Tools.EraserTool,\n    Tools.TextFormFieldCreateTool,\n    Tools.ListBoxFormFieldCreateTool,\n    Tools.ComboBoxFormFieldCreateTool,\n    Tools.SignatureFormFieldCreateTool,\n    Tools.CheckBoxFormFieldCreateTool,\n    Tools.RadioButtonFormFieldCreateTool\n  ];\n  return toolsWithHiddenOpacitySlider.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const hasSnapModeCheckbox = (toolName) => {\n  const toolsWithSnapModeCheckbox = [\n    Tools.DistanceMeasurementCreateTool,\n    Tools.ArcMeasurementCreateTool,\n    Tools.PerimeterMeasurementCreateTool,\n    Tools.AreaMeasurementCreateTool,\n    Tools.RectangularAreaMeasurementCreateTool,\n    Tools.CloudyRectangularAreaMeasurementCreateTool,\n  ];\n  return toolsWithSnapModeCheckbox.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const extractUniqueFontFamilies = (jsonData, inputText) => {\n  const uniqueFontFamilies = new Set();\n  const uniqueFontSizes = new Set();\n\n  for (const key in jsonData) {\n    if (jsonData.hasOwnProperty(key)) {\n      const index = parseInt(key, 10);\n      if (!isNaN(index) && inputText[index] !== ' ' && jsonData[key]['font-family']) {\n        uniqueFontFamilies.add(jsonData[key]['font-family'].trim());\n      }\n      if (!isNaN(index) && inputText[index] !== ' ' && jsonData[key]['font-size']) {\n        uniqueFontSizes.add(jsonData[key]['font-size'].trim());\n      }\n    }\n  }\n\n  return {\n    fonts: Array.from(uniqueFontFamilies),\n    sizes: Array.from(uniqueFontSizes),\n  };\n};\n\nexport const shouldHideTransparentFillColor = (toolName) => {\n  const toolsWithHiddenTransparentColor = [Tools.RedactionCreateTool];\n  return toolsWithHiddenTransparentColor.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const stylePanelSectionTitles = (toolName, section) => {\n  const toolTitles = {\n    'AnnotationCreateRedaction': {\n      'Title': 'component.redaction',\n      'StrokeColor': 'stylePanel.headings.redactionMarkOutline',\n      'FillColor': 'stylePanel.headings.redactionFill',\n    },\n  };\n  return toolTitles[toolName] && toolTitles[toolName][section];\n};\n\nexport const shouldRenderWidgetLayout = (toolName) => {\n  const { ToolNames } = window.Core.Tools;\n  const toolsWithHiddenTextStylePicker = [\n    ToolNames.TEXT_FORM_FIELD,\n    ToolNames.LIST_BOX_FIELD,\n    ToolNames.COMBO_BOX_FIELD,\n  ];\n  return toolsWithHiddenTextStylePicker.includes(toolName);\n};\n\nexport const isInstanceOfAny = (annotation, types) => {\n  return types.some((type) => annotation instanceof type);\n};\n\nexport const shouldShowNoStyles = (annotations, filteredTypes) => {\n  return annotations.length === 1 && isInstanceOfAny(annotations[0], filteredTypes);\n};\n", "import { useTranslation } from 'react-i18next';\nimport React from 'react';\nimport ColorPicker from '../ColorPicker';\nimport Dropdown from '../../Dropdown';\nimport CollapsibleSection from 'src/components/CollapsibleSection';\nimport PropTypes from 'prop-types';\n\nconst StrokePanelSection = ({\n  showFillColorAndCollapsablePanelSections,\n  isStamp,\n  onStrokeColorChange,\n  onStyleChange,\n  strokeColor,\n  activeTool,\n  hideStrokeSlider,\n  strokethicknessComponent,\n  showLineStyleOptions,\n  renderSlider,\n  strokeStyle,\n  isInFormFieldCreationMode,\n  isFreeText,\n  isFreeHand,\n  isArc,\n  onStartLineStyleChange,\n  startingLineStyle,\n  isStyleOptionDisabled,\n  onStrokeStyleChange,\n  strokeLineStyle,\n  middleLineSegmentLabel,\n  isEllipse,\n  withCloudyStyle,\n  onEndLineStyleChange,\n  endingLineStyle,\n  defaultStartLineStyles,\n  defaultStrokeStyles,\n  defaultEndLineStyles,\n  openStrokeStyleContainer,\n  isStrokeStyleContainerActive,\n  stylePanelSectionTitles,\n}) => {\n  const [t] = useTranslation();\n\n  const sectionContent = (\n    <div className=\"panel-section-wrapper\">\n      {!isStamp && (\n        <>\n          <div className=\"menu-items\">\n            <ColorPicker onColorChange={onStrokeColorChange} onStyleChange={onStyleChange} color={strokeColor}\n              activeTool={activeTool} type={'Stroke'} ariaTypeLabel={t('option.annotationColor.StrokeColor')}/>\n          </div>\n          {!hideStrokeSlider && strokethicknessComponent && (strokethicknessComponent)}\n          {/*\n            When showLineStyleOptions is true, we want to show the opacity slider together with the stroke slider\n          */}\n          {showLineStyleOptions && <div className=\"StyleOption\">{renderSlider('opacity')}</div>}\n          {!!strokeStyle && !(isInFormFieldCreationMode && !isFreeText) && !isFreeHand && !isArc && (\n            <div className=\"StyleOption\">\n              <div className=\"styles-container lineStyleContainer\">\n                <div className=\"styles-title\">{t('option.styleOption.style')}</div>\n                <div className=\"StylePicker-LineStyle\">\n                  {showLineStyleOptions && (\n                    <Dropdown\n                      id=\"startLineStyleDropdown\"\n                      translationPrefix=\"stylePanel.lineEnding.start\"\n                      className=\"StylePicker-StartLineStyleDropdown\"\n                      dataElement=\"startLineStyleDropdown\"\n                      images={defaultStartLineStyles}\n                      onClickItem={onStartLineStyleChange}\n                      currentSelectionKey={startingLineStyle}\n                      showLabelInList\n                    />\n                  )}\n                  {!isStyleOptionDisabled && (\n                    <Dropdown\n                      id=\"middleLineStyleDropdown\"\n                      translationPrefix={middleLineSegmentLabel}\n                      className={`StylePicker-StrokeLineStyleDropdown${!!strokeStyle && !showLineStyleOptions ? ' StyleOptions' : ''\n                      }`}\n                      dataElement=\"middleLineStyleDropdown\"\n                      images={isEllipse || showLineStyleOptions ? defaultStrokeStyles : withCloudyStyle}\n                      onClickItem={onStrokeStyleChange}\n                      currentSelectionKey={strokeLineStyle}\n                      showLabelInList\n                    />\n                  )}\n                  {showLineStyleOptions && (\n                    <Dropdown\n                      id=\"endLineStyleDropdown\"\n                      translationPrefix=\"stylePanel.lineEnding.end\"\n                      className=\"StylePicker-EndLineStyleDropdown\"\n                      dataElement=\"endLineStyleDropdown\"\n                      images={defaultEndLineStyles}\n                      onClickItem={onEndLineStyleChange}\n                      currentSelectionKey={endingLineStyle}\n                      showLabelInList\n                    />\n                  )}\n                </div>\n              </div>\n            </div>\n          )}\n        </>\n      )}\n    </div>\n  );\n\n  if (!showFillColorAndCollapsablePanelSections) {\n    return sectionContent;\n  }\n\n  return (\n    <CollapsibleSection\n      header={t(stylePanelSectionTitles(activeTool, 'StrokeColor') || 'option.annotationColor.StrokeColor')}\n      headingLevel={2}\n      isInitiallyExpanded={false}\n      onToggle={openStrokeStyleContainer}\n      shouldShowHeading={showFillColorAndCollapsablePanelSections}\n      isExpanded={(isStrokeStyleContainerActive || !showFillColorAndCollapsablePanelSections)}>\n      { sectionContent }\n    </CollapsibleSection>\n  );\n};\n\nexport default StrokePanelSection;\n\nStrokePanelSection.propTypes = {\n  showFillColorAndCollapsablePanelSections: PropTypes.bool,\n  isStamp: PropTypes.bool,\n  onStrokeColorChange: PropTypes.func,\n  onStyleChange: PropTypes.func,\n  strokeColor: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),\n  activeTool: PropTypes.string,\n  hideStrokeSlider: PropTypes.bool,\n  strokethicknessComponent: PropTypes.node,\n  showLineStyleOptions: PropTypes.bool,\n  renderSlider: PropTypes.func,\n  strokeStyle: PropTypes.string,\n  isInFormFieldCreationMode: PropTypes.bool,\n  isFreeText: PropTypes.bool,\n  isFreeHand: PropTypes.bool,\n  isArc: PropTypes.bool,\n  onStartLineStyleChange: PropTypes.func,\n  startingLineStyle: PropTypes.string,\n  isStyleOptionDisabled: PropTypes.bool,\n  onStrokeStyleChange: PropTypes.func,\n  strokeLineStyle: PropTypes.string,\n  middleLineSegmentLabel: PropTypes.string,\n  isEllipse: PropTypes.bool,\n  withCloudyStyle: PropTypes.array,\n  onEndLineStyleChange: PropTypes.func,\n  endingLineStyle: PropTypes.string,\n  defaultStartLineStyles: PropTypes.array,\n  defaultStrokeStyles: PropTypes.array,\n  defaultEndLineStyles: PropTypes.array,\n  openStrokeStyleContainer: PropTypes.func,\n  isStrokeStyleContainerActive: PropTypes.bool,\n  stylePanelSectionTitles: PropTypes.func,\n};", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport CollapsibleSection from 'src/components/CollapsibleSection';\nimport PropTypes from 'prop-types';\n\nconst OpacityPanelSection = ({\n  showFillColorAndCollapsablePanelSections,\n  shouldHideOpacitySlider,\n  activeTool,\n  showLineStyleOptions,\n  renderSlider,\n  isOpacityContainerActive,\n  openOpacityContainer,\n}) => {\n  const [t] = useTranslation();\n\n  OpacityPanelSection.propTypes = {\n    showFillColorAndCollapsablePanelSections: PropTypes.bool,\n    shouldHideOpacitySlider: PropTypes.func,\n    activeTool: PropTypes.string,\n    showLineStyleOptions: PropTypes.bool,\n    renderSlider: PropTypes.func,\n    isOpacityContainerActive: PropTypes.bool,\n    openOpacityContainer: PropTypes.func,\n  };\n\n  const sectionContent = (\n    <div className=\"panel-section-wrapper Opacity\">\n      {/*\n        If showLineStyleOptions is true, then we don't want to show the opacity slider\n        in the bottom because it is already shown before together with the stroke slider\n      */}\n      {!showLineStyleOptions && !shouldHideOpacitySlider(activeTool) && (\n        <div className=\"StyleOption\">{renderSlider('opacity', showFillColorAndCollapsablePanelSections)}</div>\n      )}\n    </div>\n  );\n\n  if (!(showFillColorAndCollapsablePanelSections && !shouldHideOpacitySlider(activeTool))) {\n    return sectionContent;\n  }\n\n  return (\n    <CollapsibleSection\n      header={t('option.slider.opacity')}\n      headingLevel={2}\n      isInitiallyExpanded={false}\n      isExpanded={(isOpacityContainerActive || !showFillColorAndCollapsablePanelSections)}\n      onToggle={openOpacityContainer}>\n      { sectionContent }\n    </CollapsibleSection>\n  );\n};\n\nexport default OpacityPanelSection;", "import OpacityPanelSection from './OpacityPanelSection';\n\nexport default OpacityPanelSection;", "import React, { useEffect, useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useDispatch, useSelector } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport './StylePicker.scss';\nimport ColorPicker from './ColorPicker';\nimport Slider from 'components/Slider';\nimport DataElements from 'constants/dataElement';\nimport { getStrokeSliderSteps, getStrokeDisplayValue } from 'constants/slider';\nimport {\n  defaultStartLineStyles,\n  defaultStrokeStyles,\n  defaultEndLineStyles,\n  cloudyStrokeStyle\n} from 'constants/strokeStyleIcons';\nimport SnapModeToggle from './SnapModeToggle';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport {\n  hasFillColorAndCollapsablePanelSections,\n  stylePanelSectionTitles,\n  shouldHideStrokeSlider,\n  shouldHideOpacitySlider,\n  hasSnapModeCheckbox,\n  shouldShowTextStyle,\n  shouldHideTransparentFillColor,\n  shouldHideStrokeStyle,\n  shouldHideFillColorAndCollapsablePanelSections,\n} from 'helpers/stylePanelHelper';\nimport useOnFreeTextEdit from 'hooks/useOnFreeTextEdit';\nimport RichTextStyleEditor from '../RichTextStyleEditor';\nimport LabelTextEditor from 'components/LabelTextEditor';\nimport CollapsibleSection from '../CollapsibleSection';\nimport StrokePanelSection from './StrokePanelSection/StrokePanelSection';\nimport OpacityPanelSection from './OpacityPanelSection';\n\nconst withCloudyStyle = defaultStrokeStyles.concat(cloudyStrokeStyle);\n\nconst propTypes = {\n  activeType: PropTypes.string,\n  endLineStyle: PropTypes.string,\n  handleRichTextStyleChange: PropTypes.func,\n  isArc: PropTypes.bool,\n  isEllipse: PropTypes.bool,\n  isFreeHand: PropTypes.bool,\n  isFreeText: PropTypes.bool,\n  isFreeTextAutoSize: PropTypes.bool,\n  isInFormFieldCreationMode: PropTypes.bool,\n  isRedaction: PropTypes.bool,\n  isStamp: PropTypes.bool,\n  isTextStylePickerHidden: PropTypes.bool,\n  isWidget: PropTypes.bool,\n  onFreeTextSizeToggle: PropTypes.func,\n  onLineStyleChange: PropTypes.func,\n  onStyleChange: PropTypes.func.isRequired,\n  redactionLabelProperties: PropTypes.object,\n  showLineStyleOptions: PropTypes.bool,\n  sliderProperties: PropTypes.arrayOf(PropTypes.string),\n  startLineStyle: PropTypes.string,\n  strokeStyle: PropTypes.string,\n  style: PropTypes.object.isRequired,\n  toolName: PropTypes.string,\n};\n\nconst MAX_STROKE_THICKNESS = 23;\n\nconst StylePicker = ({\n  onStyleChange,\n  style,\n  isFreeText,\n  isEllipse,\n  isRedaction,\n  isWidget,\n  isFreeHand,\n  showLineStyleOptions,\n  isArc,\n  isStamp,\n  isInFormFieldCreationMode,\n  startLineStyle,\n  endLineStyle,\n  strokeStyle,\n  onLineStyleChange,\n  onFreeTextSizeToggle,\n  isFreeTextAutoSize,\n  handleRichTextStyleChange,\n  activeTool,\n  saveEditorInstance,\n}) => {\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n  const [isRichTextEditMode, setIsRichTextEditMode] = useState(false);\n  const [strokeColor, setStrokeColor] = useState(style.StrokeColor);\n  const [startingLineStyle, setStartingLineStyle] = useState(startLineStyle);\n  const [endingLineStyle, setEndingLineStyle] = useState(endLineStyle);\n  const [strokeLineStyle, setStrokeLineStyle] = useState(strokeStyle);\n  const [fillColor, setFillColor] = useState(style.FillColor);\n\n  const hideStrokeStyle = shouldHideStrokeStyle(activeTool);\n  const showFillColorAndCollapsablePanelSections = hasFillColorAndCollapsablePanelSections(activeTool);\n  const hideFillColorAndCollapsablePanelSections = shouldHideFillColorAndCollapsablePanelSections(activeTool);\n  const hideStrokeSlider = shouldHideStrokeSlider(activeTool);\n  const showSnapModeCheckbox = hasSnapModeCheckbox(activeTool);\n  const showTextStyle = shouldShowTextStyle(activeTool);\n\n  useEffect(() => {\n    if (showFillColorAndCollapsablePanelSections) {\n      if (showTextStyle) {\n        dispatch(actions.openElement(DataElements.RICH_TEXT_STYLE_CONTAINER));\n      } else {\n        dispatch(actions.openElement(DataElements.STROKE_STYLE_CONTAINER));\n      }\n    }\n  }, [activeTool]);\n\n  useEffect(() => {\n    if (isRichTextEditMode) {\n      dispatch(actions.closeElement(DataElements.ANNOTATION_POPUP));\n    }\n  }, [isRichTextEditMode]);\n\n  useEffect(() => {\n    setStrokeColor(style.StrokeColor);\n    setFillColor(style.FillColor);\n  }, [strokeColor, fillColor, style]);\n\n  useEffect(() => {\n    setStrokeColor(style.StrokeColor);\n    setStartingLineStyle(startLineStyle);\n    setStrokeLineStyle(strokeStyle);\n    setEndingLineStyle(endLineStyle);\n  }, [startLineStyle, endLineStyle, strokeStyle]);\n\n  const onStrokeColorChange = (color) => {\n    onStyleChange?.('StrokeColor', color);\n    setStrokeColor(color);\n  };\n\n  const onStartLineStyleChange = (style) => {\n    onLineStyleChange?.('start', style);\n    setStartingLineStyle(style);\n  };\n\n  const onStrokeStyleChange = (style) => {\n    onLineStyleChange?.('middle', style);\n    setStrokeLineStyle(style);\n  };\n\n  const onEndLineStyleChange = (style) => {\n    onLineStyleChange?.('end', style);\n    setEndingLineStyle(style);\n  };\n\n  const onFillColorChange = (color) => {\n    onStyleChange?.('FillColor', color);\n    setFillColor(color);\n  };\n\n  const onSliderChange = (property, value) => {\n    onStyleChange?.(property, value);\n  };\n\n  // We do not have sliders to show up for redaction annots\n  if (isRedaction) {\n    style.Opacity = null;\n    style.StrokeThickness = null;\n  }\n\n  const [\n    isSnapModeEnabled,\n    isStyleOptionDisabled,\n    isStrokeStyleContainerActive,\n    isFillColorContainerActive,\n    isOpacityContainerActive,\n    isTextStyleContainerActive,\n  ] = useSelector((state) => [\n    selectors.isSnapModeEnabled(state),\n    selectors.isElementDisabled(state, DataElements.STYLE_OPTION),\n    selectors.isElementOpen(state, DataElements.STROKE_STYLE_CONTAINER),\n    selectors.isElementOpen(state, DataElements.FILL_COLOR_CONTAINER),\n    selectors.isElementOpen(state, DataElements.OPACITY_CONTAINER),\n    selectors.isElementOpen(state, DataElements.RICH_TEXT_STYLE_CONTAINER),\n  ]);\n\n  const panelItems = {\n    [DataElements.STROKE_STYLE_CONTAINER]: isStrokeStyleContainerActive,\n    [DataElements.FILL_COLOR_CONTAINER]: isFillColorContainerActive,\n    [DataElements.OPACITY_CONTAINER]: isOpacityContainerActive,\n    [DataElements.RICH_TEXT_STYLE_CONTAINER]: isTextStyleContainerActive,\n  };\n\n  const togglePanelItem = (dataElement) => {\n    if (!panelItems[dataElement]) {\n      dispatch(actions.openElement(dataElement));\n    } else {\n      dispatch(actions.closeElement(dataElement));\n    }\n  };\n  const openTextStyleContainer = () => {\n    dispatch(actions.openElements(DataElements.RICH_TEXT_EDITOR));\n    togglePanelItem(DataElements.RICH_TEXT_STYLE_CONTAINER);\n  };\n  const openStrokeStyleContainer = () => togglePanelItem(DataElements.STROKE_STYLE_CONTAINER);\n  const openFillColorContainer = () => togglePanelItem(DataElements.FILL_COLOR_CONTAINER);\n  const openOpacityContainer = () => togglePanelItem(DataElements.OPACITY_CONTAINER);\n\n  const getSliderProps = (type) => {\n    const { Opacity, StrokeThickness, FontSize } = style;\n\n    switch (type.toLowerCase()) {\n      case 'opacity':\n        if (Opacity === null) {\n          return null;\n        }\n        return {\n          property: 'Opacity',\n          displayProperty: 'opacity',\n          value: Opacity * 100,\n          getDisplayValue: (Opacity) => `${Math.round(Opacity)}%`,\n          dataElement: DataElements.OPACITY_SLIDER,\n          withInputField: true,\n          inputFieldType: 'number',\n          min: 0,\n          max: 100,\n          step: 1,\n          getLocalValue: (opacity) => parseInt(opacity) / 100,\n        };\n      case 'strokethickness':\n        if (StrokeThickness === null) {\n          return null;\n        }\n        return {\n          property: 'StrokeThickness',\n          displayProperty: 'thickness',\n          value: StrokeThickness,\n          getDisplayValue: getStrokeDisplayValue,\n          dataElement: DataElements.STROKE_THICKNESS_SLIDER,\n          withInputField: true,\n          inputFieldType: 'number',\n          min: 0,\n          max: MAX_STROKE_THICKNESS,\n          step: 1,\n          steps: getStrokeSliderSteps(isFreeText),\n        };\n      case 'fontsize':\n        if (FontSize === null) {\n          return null;\n        }\n        return {\n          property: 'FontSize',\n          displayProperty: 'text',\n          value: FontSize,\n          getDisplayValue: (FontSize) => `${Math.round(parseInt(FontSize, 10))}pt`,\n          dataElement: DataElements.FONT_SIZE_SLIDER,\n          min: 5,\n          max: 45,\n          step: 1,\n          withInputField: true,\n          inputFieldType: 'number',\n          getLocalValue: (FontSize) => `${parseFloat(FontSize).toFixed(2)}pt`,\n        };\n    }\n  };\n\n  const renderSlider = (property, shouldHideSliderTitle) => {\n    const sliderProps = getSliderProps(property);\n    if (!sliderProps) {\n      return null;\n    }\n    return (\n      <Slider\n        key={property}\n        {...sliderProps}\n        onStyleChange={onSliderChange}\n        onSliderChange={onSliderChange}\n        shouldHideSliderTitle={shouldHideSliderTitle}\n        customCircleRadius={8}\n        customLineStrokeWidth={5}\n      />\n    );\n  };\n\n  const renderDivider = () => {\n    if (showFillColorAndCollapsablePanelSections) {\n      return <div className=\"divider\" />;\n    }\n  };\n\n  const onOpenProps = useOnFreeTextEdit(saveEditorInstance);\n  const strokethicknessComponent = renderSlider('strokethickness');\n  const middleLineSegmentLabel = showLineStyleOptions ? 'stylePanel.lineEnding.middle' : 'stylePanel.borderStyle';\n\n  return (\n    <div\n      className=\"StylePicker\"\n      onMouseDown={(e) => {\n        if (e.type !== 'touchstart' && e.target.tagName.toUpperCase() !== 'INPUT') {\n          e.preventDefault();\n        }\n      }}\n    >\n      {showTextStyle && (\n        <div className=\"PanelSection TextStyle\">\n          <CollapsibleSection\n            header={t(stylePanelSectionTitles(activeTool, 'OverlayText') || 'option.stylePopup.textStyle')}\n            headingLevel={2}\n            isInitiallyExpanded={false}\n            isExpanded={isTextStyleContainerActive}\n            onToggle={openTextStyleContainer}>\n            <div className=\"panel-section-wrapper\">\n              {isRedaction && (\n                <div className=\"PanelSubsection RedactionTextLabel\">\n                  <div className=\"menu-subtitle\">{t('stylePanel.headings.redactionTextLabel')}</div>\n                  <LabelTextEditor properties={style} onPropertyChange={onStyleChange} placeholderText={' '} />\n                </div>\n              )}\n              <RichTextStyleEditor\n                style={style}\n                {...onOpenProps}\n                property={'TextColor'}\n                colorMapKey={'freeText'}\n                isFreeTextAutoSize={isFreeTextAutoSize}\n                onFreeTextSizeToggle={onFreeTextSizeToggle}\n                onPropertyChange={onStyleChange}\n                onRichTextStyleChange={handleRichTextStyleChange}\n                isRichTextEditMode={isRichTextEditMode}\n                setIsRichTextEditMode={setIsRichTextEditMode}\n                isRedaction={isRedaction}\n                activeTool={activeTool}\n                isWidget={isWidget}\n              />\n            </div>\n          </CollapsibleSection>\n          {renderDivider()}\n        </div>\n      )}\n\n      {!hideStrokeStyle && (\n        <div className=\"PanelSection\">\n          <StrokePanelSection\n            showFillColorAndCollapsablePanelSections={showFillColorAndCollapsablePanelSections}\n            isStamp={isStamp}\n            onStrokeColorChange={onStrokeColorChange}\n            onStyleChange={onStyleChange}\n            strokeColor={strokeColor}\n            activeTool={activeTool}\n            hideStrokeSlider={hideStrokeSlider}\n            strokethicknessComponent={strokethicknessComponent}\n            showLineStyleOptions={showLineStyleOptions}\n            renderSlider={renderSlider}\n            strokeStyle={strokeLineStyle}\n            isInFormFieldCreationMode={isInFormFieldCreationMode}\n            isFreeText={isFreeText}\n            isFreeHand={isFreeHand}\n            isArc={isArc}\n            onStartLineStyleChange={onStartLineStyleChange}\n            startingLineStyle={startingLineStyle}\n            isStyleOptionDisabled={isStyleOptionDisabled}\n            onStrokeStyleChange={onStrokeStyleChange}\n            strokeLineStyle={strokeLineStyle}\n            middleLineSegmentLabel={middleLineSegmentLabel}\n            isEllipse={isEllipse}\n            withCloudyStyle={withCloudyStyle}\n            onEndLineStyleChange={onEndLineStyleChange}\n            endingLineStyle={endingLineStyle}\n            defaultStartLineStyles={defaultStartLineStyles}\n            defaultStrokeStyles={defaultStrokeStyles}\n            defaultEndLineStyles={defaultEndLineStyles}\n            openStrokeStyleContainer={openStrokeStyleContainer}\n            isStrokeStyleContainerActive={isStrokeStyleContainerActive}\n            stylePanelSectionTitles={stylePanelSectionTitles}\n          />\n          {renderDivider()}\n        </div>\n      )}\n\n      {hideStrokeStyle && !hideStrokeSlider && strokethicknessComponent && (strokethicknessComponent)}\n      {showFillColorAndCollapsablePanelSections && !hideFillColorAndCollapsablePanelSections && (\n        <div className=\"PanelSection\">\n          <CollapsibleSection\n            header={t(stylePanelSectionTitles(activeTool, 'FillColor') || 'option.annotationColor.FillColor')}\n            headingLevel={2}\n            isInitiallyExpanded={false}\n            isExpanded={isFillColorContainerActive}\n            onToggle={openFillColorContainer}>\n            <div className=\"panel-section-wrapper\">\n              <div className=\"menu-items\">\n                <ColorPicker\n                  onColorChange={onFillColorChange}\n                  onStyleChange={onStyleChange}\n                  color={fillColor}\n                  hasTransparentColor={!shouldHideTransparentFillColor(activeTool)}\n                  activeTool={activeTool}\n                  type={'Fill'}\n                  ariaTypeLabel={t('option.annotationColor.FillColor')}\n                />\n              </div>\n            </div>\n          </CollapsibleSection>\n          {!shouldHideOpacitySlider(activeTool) && renderDivider()}\n        </div>\n      )}\n\n      <div className=\"PanelSection\">\n        <OpacityPanelSection\n          showFillColorAndCollapsablePanelSections={showFillColorAndCollapsablePanelSections}\n          shouldHideOpacitySlider={shouldHideOpacitySlider}\n          activeTool={activeTool}\n          showLineStyleOptions={showLineStyleOptions}\n          renderSlider={renderSlider}\n          isOpacityContainerActive={isOpacityContainerActive}\n          openOpacityContainer={openOpacityContainer}\n        />\n        {showSnapModeCheckbox && renderDivider()}\n      </div>\n\n      {showSnapModeCheckbox && (\n        <>\n          {/* to avoid inline styling when there's no divider */}\n          {!showFillColorAndCollapsablePanelSections && <div className=\"spacer\" />}\n          <div className=\"PanelSection\">\n            <SnapModeToggle Scale={style.Scale} Precision={style.Precision} isSnapModeEnabled={isSnapModeEnabled} />\n          </div>\n        </>\n      )}\n    </div>\n  );\n};\n\nStylePicker.propTypes = propTypes;\n\nexport default StylePicker;", "import StylePicker from './StylePicker';\n\nexport default StylePicker;", "import React, { useEffect, useState } from 'react';\nimport { useSelector , useDispatch } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport selectors from 'selectors';\nimport Icon from 'components/Icon';\nimport StylePicker from 'components/StylePicker';\nimport getAnnotationCreateToolNames from 'helpers/getAnnotationCreateToolNames';\nimport { hexToRGBA } from 'helpers/color';\nimport getToolStyles from 'helpers/getToolStyles';\nimport setToolStyles from 'helpers/setToolStyles';\nimport adjustFreeTextBoundingBox from 'helpers/adjustFreeTextBoundingBox';\nimport core from 'core';\nimport { getDataWithKey, mapToolNameToKey } from 'constants/map';\nimport handleFreeTextAutoSizeToggle from 'helpers/handleFreeTextAutoSizeToggle';\nimport getTextDecoration from 'helpers/getTextDecoration';\nimport {\n  shouldHideStylePanelOptions,\n  extractUniqueFontFamilies,\n  stylePanelSectionTitles,\n  shouldRenderWidgetLayout,\n  shouldShowNoStyles\n} from 'helpers/stylePanelHelper';\nimport defaultTool from 'constants/defaultTool';\nimport actions from 'actions';\n\nconst { ToolNames } = window.Core.Tools;\nconst { Annotations } = window.Core;\n\n\nconst StylePanel = () => {\n  const [t] = useTranslation();\n\n  const [\n    isPanelOpen,\n    toolButtonObject,\n    isAnnotationToolStyleSyncingEnabled,\n    activeDocumentViewerKey,\n  ] = useSelector((state) => [\n    selectors.isElementOpen(state, 'stylePanel'),\n    selectors.getToolButtonObjects(state),\n    selectors.isAnnotationToolStyleSyncingEnabled(state),\n    selectors.getActiveDocumentViewerKey(state),\n  ]);\n\n\n  const currentTool = core.getToolMode();\n  const currentToolName = currentTool?.name;\n  const colorProperties = ['StrokeColor', 'FillColor'];\n  const [showStyles, setShowStyles] = useState(false);\n  const [noToolStyle, setNoToolStyle] = useState(shouldHideStylePanelOptions(currentToolName));\n  const [isEllipse, setIsEllipse] = useState(false);\n  const [isFreeText, setIsFreeText] = useState(false);\n  const [isRedaction, setIsRedaction] = useState(currentToolName === ToolNames.REDACTION);\n  const [isWidget, setIsWidget] = useState(currentToolName === defaultTool ? false : shouldRenderWidgetLayout(currentToolName));\n  const [isFreeHand, setIsFreeHand] = useState(false);\n  const [isArc, setIsArc] = useState(false);\n  const [isStamp, setIsStamp] = useState(false);\n  const [isInFormFieldCreationMode, setIsInFormFieldCreationMode] = useState(false);\n  const [style, setStyle] = useState({});\n  const [startLineStyle, setStartLineStyle] = useState();\n  const [endLineStyle, setEndLineStyle] = useState();\n  const [strokeStyle, setStrokeStyle] = useState();\n  const [panelTitle, setPanelTitle] = useState(t('stylePanel.headings.styles'));\n  const annotationCreateToolNames = getAnnotationCreateToolNames();\n  const [showLineStyleOptions, setShowLineStyleOptions] = useState(currentToolName === defaultTool ? false : getDataWithKey(mapToolNameToKey(currentToolName)).hasLineEndings);\n  const [isAutoSizeFont, setIsAutoSizeFont] = useState(style.isAutoSizeFont);\n  const [activeTool, setActiveTool] = useState(currentToolName || 'Edit');\n  const [editorInstance, setEditorInstance] = useState(null);\n  const dispatch = useDispatch();\n\n  const filteredTypes = [\n    Annotations.PushButtonWidgetAnnotation,\n  ];\n\n  useEffect(() => {\n    if (currentTool?.name === 'AnnotationCreateRubberStamp') {\n      core.setToolMode(defaultTool);\n    }\n    updateSnapModeFromTool(currentTool);\n  }, [currentTool]);\n\n  const updateSnapModeFromTool = (currentTool) => {\n    if (!core.isFullPDFEnabled()) {\n      return;\n    }\n    if (currentTool && currentTool.getSnapMode) {\n      const isSnapModeEnabled = !!currentTool.getSnapMode();\n      dispatch(actions.setEnableSnapMode({ toolName: currentTool.name, isEnabled: isSnapModeEnabled }));\n    }\n  };\n\n  const getStrokeStyle = (annot) => {\n    const style = annot['Style'];\n    const dashes = annot['Dashes'];\n    if (style !== 'dash') {\n      return style;\n    }\n    return `${style},${dashes}`;\n  };\n\n  const getPanelTitleOnAnnotationSelected = (annot) => {\n    if (annot.isContentEditPlaceholder()) {\n      setPanelTitle(`${t('stylePanel.headings.contentEdit')} ${t('stylePanel.headings.annotation')}`);\n      setNoToolStyle(true);\n      return;\n    }\n    setPanelTitle(`${t(stylePanelSectionTitles(annot.ToolName, 'Title') || toolButtonObject[annot.ToolName]?.title)} ${t('stylePanel.headings.annotation')}`);\n  };\n\n  const setPanelTitleForSelectedTool = (tool) => {\n    const toolName = tool.name;\n    const title = toolButtonObject[toolName]?.title;\n    setPanelTitle(`${t(stylePanelSectionTitles(toolName, 'Title') || title)} ${t('stylePanel.headings.tool')}`);\n  };\n\n  const updateStylePanelProps = (annot) => {\n    const extraStyles = {};\n\n    if (annot instanceof Annotations.FreeTextAnnotation) {\n      let StrokeStyle = 'solid';\n      try {\n        StrokeStyle = (annot['Style'] === 'dash')\n          ? `${annot['Style']},${annot['Dashes']}`\n          : annot['Style'];\n      } catch (err) {\n        console.error(err);\n      }\n      extraStyles['TextColor'] = annot.TextColor;\n      extraStyles['RichTextStyle'] = annot.getRichTextStyle();\n      extraStyles['Font'] = annot.Font;\n      extraStyles['FontSize'] = annot.FontSize;\n      extraStyles['TextAlign'] = annot.TextAlign;\n      extraStyles['TextVerticalAlign'] = annot.TextVerticalAlign;\n      extraStyles['calculatedFontSize'] = annot.getCalculatedFontSize();\n      extraStyles['StrokeStyle'] = StrokeStyle;\n      extraStyles['isAutoSizeFont'] = annot.isAutoSizeFont();\n      setIsAutoSizeFont(annot.isAutoSizeFont());\n\n      const { fonts, sizes } = extractUniqueFontFamilies(extraStyles['RichTextStyle'], annot.getContents());\n      if (fonts.length >= 2 || (fonts.length === 1 && fonts[0] !== extraStyles['Font'])) {\n        extraStyles['Font'] = undefined;\n      }\n      if (sizes.length >= 2 || (sizes.length === 1 && sizes[0] !== extraStyles['FontSize'])) {\n        extraStyles['FontSize'] = undefined;\n      }\n    }\n\n    if (annot instanceof Annotations.RedactionAnnotation) {\n      extraStyles['OverlayText'] = annot.OverlayText;\n      extraStyles['Font'] = annot.Font;\n      extraStyles['FontSize'] = annot.FontSize;\n      extraStyles['TextAlign'] = annot.TextAlign;\n    }\n\n    if (annot instanceof Annotations.WidgetAnnotation && annot.FontSize !== undefined) {\n      extraStyles.FontSize = annot.FontSize;\n    }\n\n    setStyle({\n      ...style,\n      StrokeColor: annot.StrokeColor,\n      StrokeThickness: annot.StrokeThickness,\n      Opacity: annot.Opacity,\n      FillColor: annot.FillColor,\n      ...extraStyles,\n    });\n    setStartLineStyle(annot.getStartStyle ? annot.getStartStyle() : 'None');\n    setEndLineStyle(annot.getEndStyle ? annot.getEndStyle() : 'None');\n    setStrokeStyle(getStrokeStyle(annot));\n  };\n\n  useEffect(() => {\n    const handleToolModeChange = (newTool) => {\n      if (annotationCreateToolNames.includes(newTool?.name)) {\n        if (!panelTitle) {\n          setShowStyles(false);\n        } else {\n          if (shouldHideStylePanelOptions(newTool?.name)) {\n            setNoToolStyle(true);\n            setShowStyles(true);\n            setPanelTitleForSelectedTool(newTool);\n            return;\n          }\n\n          setNoToolStyle(false);\n          setActiveTool(newTool.name);\n          setShowLineStyleOptions(getDataWithKey(mapToolNameToKey(newTool.name)).hasLineEndings);\n          setIsEllipse(newTool.name === ToolNames.ELLIPSE);\n          setIsFreeText(newTool.name === ToolNames.FREETEXT);\n          setIsRedaction(newTool.name === ToolNames.REDACTION);\n\n          setIsWidget(shouldRenderWidgetLayout(newTool.name));\n\n          setIsFreeHand(\n            newTool.name === ToolNames.FREEHAND ||\n            newTool.name === ToolNames.FREEHAND_HIGHLIGHT,\n          );\n          setIsArc(newTool.name === ToolNames.ARC);\n          setIsStamp(newTool.name === ToolNames.STAMP);\n          setIsInFormFieldCreationMode(core.getFormFieldCreationManager().isInFormFieldCreationMode());\n          const toolStyles = getToolStyles(newTool.name);\n\n          if (newTool.name.includes('FreeText') || newTool.name.includes('Callout')) {\n            toolStyles['isAutoSizeFont'] = newTool['defaults']['isAutoSizeFont'];\n            setIsAutoSizeFont(newTool['defaults']['isAutoSizeFont']);\n          }\n\n          setStyle(toolStyles);\n          setStartLineStyle(toolStyles.StartLineStyle);\n          setStrokeStyle(toolStyles.StrokeStyle);\n          setEndLineStyle(toolStyles.EndLineStyle);\n          setShowStyles(true);\n          setPanelTitleForSelectedTool(newTool);\n        }\n      } else {\n        setShowStyles(false);\n      }\n    };\n    const handleSingleAnnotation = (annotation) => {\n      getPanelTitleOnAnnotationSelected(annotation);\n      if (shouldHideStylePanelOptions(annotation.ToolName)) {\n        setNoToolStyle(true);\n        return;\n      }\n\n      setNoToolStyle(false);\n      setActiveTool(annotation.ToolName);\n      setIsEllipse(annotation instanceof Annotations.EllipseAnnotation);\n      setIsFreeText(annotation instanceof Annotations.FreeTextAnnotation);\n      setIsRedaction(annotation instanceof Annotations.RedactionAnnotation);\n      setIsWidget(shouldRenderWidgetLayout(annotation.ToolName));\n      setIsFreeHand(annotation instanceof Annotations.FreeHandAnnotation);\n      setIsArc(annotation instanceof Annotations.ArcAnnotation);\n      setIsStamp(annotation instanceof Annotations.StampAnnotation);\n      setIsInFormFieldCreationMode(core.getFormFieldCreationManager().isInFormFieldCreationMode());\n      setShowLineStyleOptions(getDataWithKey(mapToolNameToKey(annotation.ToolName)).hasLineEndings);\n      updateStylePanelProps(annotation);\n    };\n    const handleMultipleAnnotations = (annotations) => {\n      setPanelTitle(`${t('stylePanel.headings.annotations')} (${annotations.length})`);\n      annotations.forEach((annot) => {\n        updateStylePanelProps(annot);\n      });\n    };\n    const handleDeselection = () => {\n      const currentTool = core.getToolMode();\n      if (currentTool instanceof window.Core.Tools.AnnotationEditTool) {\n        setShowStyles(false);\n      }\n      handleToolModeChange(currentTool);\n      core.setToolMode(currentTool.name);\n    };\n    const onAnnotationSelected = (annotations, action) => {\n      if (action === 'selected') {\n        if (shouldShowNoStyles(annotations, filteredTypes)) {\n          setNoToolStyle(false);\n          setShowStyles(false);\n          return;\n        }\n        setShowStyles(true);\n        if (annotations.length === 1) {\n          handleSingleAnnotation(annotations[0]);\n        } else {\n          handleMultipleAnnotations(annotations);\n        }\n      } else if (action === 'deselected') {\n        handleDeselection();\n      }\n    };\n    const onAnnotationsChanged = () => {\n      for (const annot of core.getSelectedAnnotations()) {\n        updateStylePanelProps(annot);\n      }\n    };\n\n    core.addEventListener('annotationSelected', onAnnotationSelected);\n    core.addEventListener('toolModeUpdated', handleToolModeChange);\n    core.addEventListener('annotationChanged', onAnnotationsChanged);\n    return () => {\n      core.removeEventListener('annotationSelected', onAnnotationSelected);\n      core.removeEventListener('toolModeUpdated', handleToolModeChange);\n      core.removeEventListener('annotationChanged', onAnnotationsChanged);\n    };\n  }, []);\n\n  const setAnnotationType = (annotation) => {\n    setIsEllipse(annotation instanceof Annotations.EllipseAnnotation);\n    setIsFreeText(annotation instanceof Annotations.FreeTextAnnotation);\n    setIsRedaction(annotation instanceof Annotations.RedactionAnnotation);\n    setIsFreeHand(annotation instanceof Annotations.FreeHandAnnotation);\n    setIsArc(annotation instanceof Annotations.ArcAnnotation);\n    setIsStamp(annotation instanceof Annotations.StampAnnotation);\n    setShowLineStyleOptions(getDataWithKey(mapToolNameToKey(annotation.ToolName)).hasLineEndings);\n  };\n\n  useEffect(() => {\n    if (isPanelOpen) {\n      const selectedAnnotations = core.getSelectedAnnotations();\n      if (shouldShowNoStyles(selectedAnnotations, filteredTypes)) {\n        setNoToolStyle(false);\n        setShowStyles(false);\n        return;\n      }\n      if (selectedAnnotations.length === 1) {\n        setShowStyles(true);\n        const annotation = selectedAnnotations[0];\n        updateStylePanelProps(annotation);\n        setAnnotationType(annotation);\n        getPanelTitleOnAnnotationSelected(annotation);\n      } else if (selectedAnnotations.length > 1) {\n        setShowStyles(true);\n        setPanelTitle(`${t('stylePanel.headings.annotations')} (${selectedAnnotations.length})`);\n        selectedAnnotations.forEach((annot) => {\n          updateStylePanelProps(annot);\n        });\n      } else {\n        const currentTool = core.getToolMode();\n        if (currentTool && currentTool.name !== defaultTool) {\n          setShowStyles(true);\n          const toolStyles = getToolStyles(currentTool.name);\n          if (toolStyles) {\n            setStyle(toolStyles);\n            setStartLineStyle(toolStyles.StartLineStyle);\n            setEndLineStyle(toolStyles.EndLineStyle);\n            setStrokeStyle(toolStyles.StrokeStyle);\n          }\n          setPanelTitleForSelectedTool(currentTool);\n        }\n      }\n    }\n  }, [isPanelOpen, isAutoSizeFont]);\n\n  const onStyleChange = (property, value) => {\n    const newStyle = { ...style };\n    newStyle[property] = value;\n    setStyle(newStyle);\n    const annotationsToUpdate = core.getSelectedAnnotations();\n\n    // Newly created freetext is not saved in annotationManager yet, so getSelectedAnnotations\n    // will return empty array, but editor has focus and an annotation\n    if (annotationsToUpdate.length === 0 && editorInstance && property === 'FillColor') {\n      const editor = editorInstance[0];\n      if (editor?.hasFocus()) {\n        const annot = editorInstance[1];\n        editor.setStyle({ background: value });\n        annot['FillColor'] = new Annotations.Color(value);\n        return;\n      }\n    }\n\n    if (annotationsToUpdate.length > 0) {\n      annotationsToUpdate.forEach((annot) => {\n        if (colorProperties.includes(property)) {\n          const colorRGB = hexToRGBA(value);\n          const color = new Annotations.Color(colorRGB.r, colorRGB.g, colorRGB.b, colorRGB.a);\n          core.setAnnotationStyles(annot, {\n            [property]: color,\n          }, activeDocumentViewerKey);\n          if (isAnnotationToolStyleSyncingEnabled) {\n            setToolStyles(annot.ToolName, property, color);\n          }\n        } else {\n          core.setAnnotationStyles(annot, {\n            [property]: value,\n          }, activeDocumentViewerKey);\n          if (annot instanceof Annotations.FreeTextAnnotation) {\n            if (property === 'FontSize' || property === 'Font' || property === 'StrokeThickness') {\n              adjustFreeTextBoundingBox(annot);\n            }\n          }\n          if (isAnnotationToolStyleSyncingEnabled) {\n            setToolStyles(annot.ToolName, property, value);\n          }\n        }\n\n        core.getAnnotationManager().redrawAnnotation(annot);\n        if (annot instanceof Annotations.WidgetAnnotation) {\n          annot.refresh();\n        }\n      });\n    } else {\n      const currentTool = core.getToolMode();\n      if (currentTool) {\n        if (colorProperties.includes(property)) {\n          const colorRGB = hexToRGBA(value);\n          const color = new Annotations.Color(colorRGB.r, colorRGB.g, colorRGB.b, colorRGB.a);\n          setToolStyles(currentTool.name, property, color);\n        } else if (property === 'Opacity') {\n          setToolStyles(currentTool.name, 'Opacity', value);\n        } else if (property === 'StrokeThickness') {\n          setToolStyles(currentTool.name, 'StrokeThickness', value);\n        } else {\n          setToolStyles(currentTool.name, property, value);\n        }\n      }\n    }\n  };\n\n  const onLineStyleChange = (section, value) => {\n    const sectionPropertyMap = {\n      start: 'StartLineStyle',\n      middle: 'StrokeStyle',\n      end: 'EndLineStyle',\n    };\n    if (section === 'start') {\n      setStartLineStyle(value);\n    } else if (section === 'middle') {\n      setStrokeStyle(value);\n    } else if (section === 'end') {\n      setEndLineStyle(value);\n    }\n    const annotationsToUpdate = core.getSelectedAnnotations();\n    if (annotationsToUpdate.length > 0) {\n      annotationsToUpdate.forEach((annot) => {\n        if (section === 'start') {\n          annot.setStartStyle(value);\n        } else if (section === 'middle') {\n          const dashes = value.split(',');\n          const lineStyle = dashes.shift();\n          annot.Style = lineStyle;\n          annot.Dashes = dashes;\n        } else if (section === 'end') {\n          annot.setEndStyle(value);\n        }\n        core.getAnnotationManager(activeDocumentViewerKey).redrawAnnotation(annot);\n        if (isAnnotationToolStyleSyncingEnabled) {\n          setToolStyles(annot.ToolName, sectionPropertyMap[section], value);\n        }\n      });\n\n      core.getAnnotationManager(activeDocumentViewerKey).trigger('annotationChanged', [annotationsToUpdate, 'modify', {}]);\n    } else {\n      const currentTool = core.getToolMode();\n      if (currentTool) {\n        setToolStyles(currentTool.name, sectionPropertyMap[section], value);\n      }\n    }\n  };\n  const handleAutoSize = () => {\n    const annotationsToUpdate = core.getSelectedAnnotations()[0];\n    if (annotationsToUpdate) {\n      handleFreeTextAutoSizeToggle(annotationsToUpdate, setIsAutoSizeFont, isAutoSizeFont);\n    } else {\n      const currentTool = core.getToolMode();\n      if (currentTool) {\n        setToolStyles(currentTool.name, 'isAutoSizeFont', !style.isAutoSizeFont);\n        setIsAutoSizeFont(!isAutoSizeFont);\n      }\n    }\n  };\n\n  const noToolSelected = (\n    <>\n      <h2 className='style-panel-header'>\n        {t('stylePanel.headings.styles')}\n      </h2>\n      <div className=\"no-tool-selected\">\n        <div>\n          <Icon className=\"empty-icon\" glyph=\"style-panel-no-tool-selected\" />\n        </div>\n        <div className=\"msg\">{t('stylePanel.noToolSelected')}</div>\n      </div>\n    </>\n  );\n\n  const handleRichTextStyleChange = (property, value) => {\n    const originalProperty = property;\n    const originalValue = value;\n    const activeToolRichTextStyle = style['RichTextStyle']?.[0];\n    if (property === 'underline' || property === 'line-through') {\n      value = getTextDecoration({ [property]: value }, activeToolRichTextStyle);\n      property = 'text-decoration';\n    }\n    const richTextStyle = {\n      0: {\n        ...activeToolRichTextStyle,\n        [property]: value,\n      }\n    };\n\n    const annotationsToUpdate = core.getSelectedAnnotations();\n    if (annotationsToUpdate.length > 0) {\n      annotationsToUpdate.forEach((annotation) => {\n        core.updateAnnotationRichTextStyle(annotation, { [originalProperty]: originalValue }, activeDocumentViewerKey);\n      });\n      setStyle({ ...style, 'RichTextStyle': richTextStyle });\n    } else {\n      const currentTool = core.getToolMode();\n      if (currentTool) {\n        if (typeof currentTool.complete === 'function') {\n          currentTool.complete();\n        }\n        setToolStyles(currentTool.name, 'RichTextStyle', richTextStyle);\n      }\n    }\n  };\n\n  return !showStyles ? (\n    noToolSelected\n  ) : (\n    <>\n      <h2 className=\"style-panel-header\">{panelTitle}</h2>\n      {noToolStyle ? (\n        <div className=\"no-tool-selected\">\n          <div>\n            <Icon className=\"empty-icon\" glyph=\"style-panel-no-tool-selected\" />\n          </div>\n          <div className=\"msg\">{t('stylePanel.noToolStyle')}</div>\n        </div>\n      ) : (\n        <StylePicker\n          sliderProperties={['Opacity', 'StrokeThickness']}\n          style={style}\n          onStyleChange={onStyleChange}\n          isFreeText={isFreeText}\n          isEllipse={isEllipse}\n          isRedaction={isRedaction}\n          isWidget={isWidget}\n          isFreeHand={isFreeHand}\n          isArc={isArc}\n          isStamp={isStamp}\n          isInFormFieldCreationMode={isInFormFieldCreationMode}\n          showLineStyleOptions={showLineStyleOptions}\n          startLineStyle={startLineStyle}\n          endLineStyle={endLineStyle}\n          strokeStyle={strokeStyle}\n          onLineStyleChange={onLineStyleChange}\n          onFreeTextSizeToggle={handleAutoSize}\n          isFreeTextAutoSize={isAutoSizeFont}\n          handleRichTextStyleChange={handleRichTextStyleChange}\n          activeTool={activeTool}\n          saveEditorInstance={setEditorInstance}\n        />\n      )}\n    </>\n  );\n};\n\nexport default StylePanel;", "import React from 'react';\nimport StylePanel from './StylePanel';\nimport DataElementWrapper from '../DataElementWrapper';\nimport './StylePanel.scss';\n\nconst StylePanelContainer = () => {\n  return (\n    <DataElementWrapper dataElement=\"stylePanel\" className=\"Panel StylePanel\">\n      <StylePanel />\n    </DataElementWrapper>\n  );\n};\n\nexport default StylePanelContainer;", "import StylePanelContainer from './StylePanelContainer';\n\nexport default StylePanelContainer;"], "sourceRoot": ""}