(window.webpackJsonp=window.webpackJsonp||[]).push([[56],{1758:function(n,t,e){var i=e(32),o=e(1837);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[n.i,o,""]]);var a={insert:function(n){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(n);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function n(t,e=document){const i=[];return e.querySelectorAll(t).forEach(n=>i.push(n)),e.querySelectorAll("*").forEach(e=>{e.shadowRoot&&i.push(...n(t,e.shadowRoot))}),i}("apryse-webviewer"));const e=[];for(let i=0;i<t.length;i++){const o=t[i];if(0===i)o.shadowRoot.appendChild(n),n.onload=function(){e.length>0&&e.forEach(t=>{t.innerHTML=n.innerHTML})};else{const t=n.cloneNode(!0);o.shadowRoot.appendChild(t),e.push(t)}}},singleton:!1};i(o,a);n.exports=o.locals||{}},1837:function(n,t,e){(t=n.exports=e(33)(!1)).push([n.i,".open.AlignAnnotationPopupContainer{visibility:visible}.closed.AlignAnnotationPopupContainer{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.AlignAnnotationPopupContainer{position:absolute;z-index:70;display:flex;justify-content:center;align-items:center}.AlignAnnotationPopupContainer:empty{padding:0}.AlignAnnotationPopupContainer .buttons{display:flex}.AlignAnnotationPopupContainer .Button{margin:4px;width:32px;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .AlignAnnotationPopupContainer .Button{width:42px;height:42px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .AlignAnnotationPopupContainer .Button{width:42px;height:42px}}.AlignAnnotationPopupContainer .Button:hover{background:var(--popup-button-hover)}.AlignAnnotationPopupContainer .Button:hover:disabled{background:none}.AlignAnnotationPopupContainer .Button .Icon{width:18px;height:18px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .AlignAnnotationPopupContainer .Button .Icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .AlignAnnotationPopupContainer .Button .Icon{width:24px;height:24px}}.is-vertical.AlignAnnotationPopupContainer .Button.main-menu-button{width:100%;border-radius:0;justify-content:flex-start;padding-left:var(--padding-small);padding-right:var(--padding-small);margin:0 0 var(--padding-tiny) 0}.is-vertical.AlignAnnotationPopupContainer .Button.main-menu-button:first-child{margin-top:var(--padding-tiny)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .is-vertical.AlignAnnotationPopupContainer .Button.main-menu-button{width:100%;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .is-vertical.AlignAnnotationPopupContainer .Button.main-menu-button{width:100%;height:32px}}.is-vertical.AlignAnnotationPopupContainer .Button.main-menu-button .Icon{margin-right:10px}.is-vertical.AlignAnnotationPopupContainer .Button.main-menu-button span{white-space:nowrap}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.AlignAnnotationPopupContainer{border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background)}.AlignAnnotationPopup.is-horizontal .contents{display:flex;grid-gap:16px;gap:16px;flex-direction:column;padding:16px}.AlignAnnotationPopup.is-horizontal .contents .back-to-menu-button{width:24px;height:24px}.AlignAnnotationPopup.is-horizontal .contents .back-to-menu-button .Icon{width:16px;height:16px}.AlignAnnotationPopup.is-horizontal .contents .divider{height:1px;width:100%;background:var(--divider)}.AlignAnnotationPopup.is-horizontal button{padding:0;margin:0;height:32px;width:32px}.AlignAnnotationPopup.is-horizontal button .Icon{width:24px;height:24px}.AlignAnnotationPopup.is-horizontal .button-row-container{display:flex;grid-gap:8px;gap:8px;flex-direction:column}.AlignAnnotationPopup.is-horizontal .button-row{display:flex;align-items:center;grid-gap:8px;gap:8px}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1991:function(n,t,e){"use strict";e.r(t);e(19),e(12),e(13),e(8),e(14),e(10),e(9),e(11),e(16),e(15),e(20),e(18),e(26),e(27),e(25),e(22),e(30),e(28),e(45),e(23),e(24),e(47),e(46);var i=e(2),o=e(17),a=e.n(o),r=e(1),l=e(0),p=e.n(l),c=e(6),u=e(286),s=e(4),d=e.n(s),m=e(3),g=(e(41),e(429)),b=e(84);e(1758);function f(n,t){return function(n){if(Array.isArray(n))return n}(n)||function(n,t){var e=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=e){var i,o,a,r,l=[],p=!0,c=!1;try{if(a=(e=e.call(n)).next,0===t){if(Object(e)!==e)return;p=!1}else for(;!(p=(i=a.call(e)).done)&&(l.push(i.value),l.length!==t);p=!0);}catch(n){c=!0,o=n}finally{try{if(!p&&null!=e.return&&(r=e.return(),Object(r)!==r))return}finally{if(c)throw o}}return l}}(n,t)||function(n,t){if(!n)return;if("string"==typeof n)return h(n,t);var e=Object.prototype.toString.call(n).slice(8,-1);"Object"===e&&n.constructor&&(e=n.constructor.name);if("Map"===e||"Set"===e)return Array.from(n);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return h(n,t)}(n,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(n,t){(null==t||t>n.length)&&(t=n.length);for(var e=0,i=new Array(t);e<t;e++)i[e]=n[e];return i}var A={alignmentConfig:d.a.array,alignmentOnClick:d.a.func,backToMenuOnClick:d.a.func,distributeConfig:d.a.array,distributeOnClick:d.a.func,isAnnotation:d.a.bool},y=function(n){var t=n.alignmentConfig,e=n.alignmentOnClick,i=n.backToMenuOnClick,o=n.distributeConfig,r=n.distributeOnClick,l=n.isAnnotation,c=f(Object(g.a)(),1)[0],u=function(n,t,e){return p.a.createElement("div",{className:"button-row-container"},p.a.createElement("div",null,c(n)),p.a.createElement("div",{className:"button-row"},t.map((function(n){return p.a.createElement(b.a,{key:n.title,className:"main-menu-button",title:c(n.title),img:n.icon,onClick:function(){e(n.alignment)}})}))))};return l?p.a.createElement("div",{"data-testid":"alignment-annotation-element",className:a()({Popup:!0,AlignAnnotationPopup:!0,"is-horizontal":!0})},p.a.createElement("div",{className:"contents"},p.a.createElement("div",{className:"top-section"},p.a.createElement("div",{className:"button-row"},p.a.createElement(b.a,{className:"back-to-menu-button",dataElement:"backToMenuButton",title:c("action.backToMenu"),img:"ic_chevron_left_black_24px",onClick:i}),p.a.createElement("div",{role:"button",type:"button",tabIndex:"0",onClick:i,onKeyDown:i},c("action.backToMenu")))),p.a.createElement("div",{className:"divider"}),u("alignmentPopup.alignment",t,e),u("alignmentPopup.distribute",o,r))):null};y.propTypes=A;var w=y,x=e(76),v=e(5),P=r.a.getAnnotationManager(),O=P.Alignment.StandardAlignmentTypes,C=O.LEFT,T=O.RIGHT,k=O.TOP,E=O.BOTTOM,j=P.Alignment.CenterAlignmentTypes,N=j.CENTER_VERTICAL,I=j.CENTER_HORIZONTAL,S=P.Alignment.DistributeAlignmentTypes,B=S.DISTRIBUTE_VERTICAL,_=S.DISTRIBUTE_HORIZONTAL,M=[{alignment:C,icon:"ic-alignment-left",title:"alignmentPopup.alignLeft"},{alignment:I,icon:"ic-alignment-center-horizontal",title:"alignmentPopup.alignHorizontalCenter"},{alignment:T,icon:"ic-alignment-right",title:"alignmentPopup.alignRight"},{alignment:k,icon:"ic-alignment-top",title:"alignmentPopup.alignTop"},{alignment:N,icon:"ic-alignment-center-vertical",title:"alignmentPopup.alignVerticalCenter"},{alignment:E,icon:"ic-alignment-bottom",title:"alignmentPopup.alignBottom"}],R=[{alignment:B,icon:"ic-distribute-vertical",title:"alignmentPopup.distributeVertical"},{alignment:_,icon:"ic-distribute-horizontal",title:"alignmentPopup.distributeHorizontal"}];function z(n){return(z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(n)}function D(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(n);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable}))),e.push.apply(e,i)}return e}function H(n){for(var t=1;t<arguments.length;t++){var e=null!=arguments[t]?arguments[t]:{};t%2?D(Object(e),!0).forEach((function(t){L(n,t,e[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):D(Object(e)).forEach((function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))}))}return n}function L(n,t,e){return(t=function(n){var t=function(n,t){if("object"!==z(n)||null===n)return n;var e=n[Symbol.toPrimitive];if(void 0!==e){var i=e.call(n,t||"default");if("object"!==z(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(n)}(n,"string");return"symbol"===z(t)?t:String(t)}(t))in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function U(n,t){return function(n){if(Array.isArray(n))return n}(n)||function(n,t){var e=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=e){var i,o,a,r,l=[],p=!0,c=!1;try{if(a=(e=e.call(n)).next,0===t){if(Object(e)!==e)return;p=!1}else for(;!(p=(i=a.call(e)).done)&&(l.push(i.value),l.length!==t);p=!0);}catch(n){c=!0,o=n}finally{try{if(!p&&null!=e.return&&(r=e.return(),Object(r)!==r))return}finally{if(c)throw o}}return l}}(n,t)||function(n,t){if(!n)return;if("string"==typeof n)return V(n,t);var e=Object.prototype.toString.call(n).slice(8,-1);"Object"===e&&n.constructor&&(e=n.constructor.name);if("Map"===e||"Set"===e)return Array.from(n);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return V(n,t)}(n,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function V(n,t){(null==t||t>n.length)&&(t=n.length);for(var e=0,i=new Array(t);e<t;e++)i[e]=n[e];return i}var G={annotation:d.a.object},W=function(n){var t=n.annotation,e=U(Object(c.e)((function(n){return[m.a.isElementOpen(n,v.a.ANNOTATION_ALIGNMENT_POPUP),m.a.getActiveDocumentViewerKey(n)]}),c.c),2),o=e[0],s=e[1],d=U(Object(l.useState)({left:0,top:0}),2),g=d[0],b=d[1],f=Object(l.useRef)(),h=Object(c.d)();Object(l.useLayoutEffect)((function(){(t||o)&&A()}),[t,o,s]);var A=function(){t&&f.current&&b(Object(u.c)(t,f,s))},y=a()({Popup:!0,AlignAnnotationPopupContainer:!0,open:o,closed:!o}),P=void 0!==t;return p.a.createElement(x.a,{dataElement:v.a.ANNOTATION_ALIGNMENT_POPUP,className:y,style:H({},g),ref:f},p.a.createElement(w,{alignmentConfig:M,alignmentOnClick:function(n){var t=r.a.getAnnotationManager(),e=t.getSelectedAnnotations();"centerHorizontal"===n||"centerVertical"===n?t.Alignment.centerAnnotations(e,n):t.Alignment.alignAnnotations(e,n),A()},backToMenuOnClick:function(){h(i.a.closeElement(v.a.ANNOTATION_ALIGNMENT_POPUP));var n=r.a.getAnnotationManager(),t=n.getSelectedAnnotations();n.selectAnnotations(t)},distributeConfig:R,distributeOnClick:function(n){var t=r.a.getAnnotationManager(),e=t.getSelectedAnnotations();t.Alignment.distributeAnnotations(e,n),A()},isAnnotation:P}))};W.propTypes=G;var Y=W;t.default=Y}}]);
//# sourceMappingURL=chunk.56.js.map