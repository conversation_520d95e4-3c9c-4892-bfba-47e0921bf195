(window.webpackJsonp=window.webpackJsonp||[]).push([[94],{1970:function(t,e,r){"use strict";r.r(e);r(19),r(12),r(13),r(8),r(14),r(10),r(9),r(11),r(16),r(15),r(20),r(18),r(60),r(44);var n=r(0),a=r.n(n),l=r(84),o=r(4),i=r.n(o),c=r(68),u=r(71);function s(){return(s=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function f(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,l,o,i=[],c=!0,u=!1;try{if(l=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=l.call(r)).done)&&(i.push(n.value),i.length!==e);c=!0);}catch(t){u=!0,a=t}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw a}}return i}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return y(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return y(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var p={type:i.a.string,isFlyoutItem:i.a.bool,style:i.a.object,className:i.a.string},b=Object(n.forwardRef)((function(t,e){var r=t.isFlyoutItem,o=t.type,i=t.style,y=t.className,p=f(Object(n.useState)(!1),2),b=p[0],m=p[1],g=u.b[b?"cellUnmergeToggle":"cellMergeToggle"],d=g.dataElement,v=g.icon,h=g.title,w=function(){m(!b)};return r?a.a.createElement(c.a,s({},t,{ref:e,onClick:w,additionalClass:""})):a.a.createElement(l.a,{key:o,isActive:!1,onClick:w,dataElement:d,title:h,img:v,ariaPressed:!1,style:i,className:y})}));b.propTypes=p,b.displayName="MergeToggleButton",e.default=b}}]);
//# sourceMappingURL=chunk.94.js.map