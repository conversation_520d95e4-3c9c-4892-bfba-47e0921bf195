(window.webpackJsonp=window.webpackJsonp||[]).push([[72],{1923:function(e,t,n){var r=n(32),o=n(1924);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var a={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach(e=>r.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))}),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const o=t[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);o.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};r(o,a);e.exports=o.locals||{}},1924:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.OpenFileModal .container .footer .modal-btn.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.OpenFileModal .container .footer .modal-btn.disabled span{color:var(--primary-button-text)}.OpenFileModal .container{overflow-y:unset;width:600px}.OpenFileModal .container .footer .modal-btn{color:var(--tab-footer-button-color);border:none}.OpenFileModal .container .footer .modal-btn.disabled span{color:var(--tab-footer-button-color)}.OpenFileModal .container .tab-list .tab-options-divider+.tab-options-button{border-left:none!important}.OpenFileModal .container .tab-list .tab-options-button{border-top:1px solid var(--tab-border-color);border-bottom:1px solid var(--tab-border-color)}.OpenFileModal .container .tab-list .tab-options-button:first-child{border-left:1px solid var(--tab-border-color)}.OpenFileModal .container .tab-list .tab-options-button:last-child{border-right:1px solid var(--tab-border-color)}.OpenFileModal .container .tab-list .tab-options-button:hover{background:var(--tab-background-color-hover);border-top:1px solid var(--tab-border-color-hover);border-bottom:1px solid var(--tab-border-color-hover);border-right:1px solid var(--tab-border-color-hover)}.OpenFileModal .container .tab-list .tab-options-button:hover+button,.OpenFileModal .container .tab-list .tab-options-button:hover+div{border-left:none}.OpenFileModal .container .tab-list .tab-options-button.selected{background:var(--tab-color-selected);border:1px solid var(--tab-color-selected);color:var(--tab-text-color-selected)}.OpenFileModal .container .tab-list .tab-options-button.selected+button,.OpenFileModal .container .tab-list .tab-options-button.selected+div{border-left:none!important}.OpenFileModal .container .tab-list .tab-options-button:not(.selected){border-right:1px solid var(--tab-border-color)}.OpenFileModal .image-signature{height:240px!important;width:100%}.OpenFileModal .error{color:red;margin:5px}.OpenFileModal .extension-dropdown{padding:0 16px;display:flex;flex-direction:column}.OpenFileModal .extension-dropdown label,.OpenFileModal .extension-dropdown p{width:100%;font-size:13px;line-height:16px;font-weight:700;order:1;margin-bottom:8px}.OpenFileModal .extension-dropdown label{margin-top:13px}.OpenFileModal .extension-dropdown .Dropdown__wrapper{order:2;width:100%}.OpenFileModal .extension-dropdown .Dropdown__wrapper .Dropdown{width:100%!important}.OpenFileModal .extension-dropdown .Dropdown__wrapper .Dropdown:disabled{border:1px solid var(--gray-4)}.OpenFileModal .extension-dropdown .Dropdown__wrapper .Dropdown:disabled .arrow,.OpenFileModal .extension-dropdown .Dropdown__wrapper .Dropdown:disabled .picked-option{color:var(--gray-5)}.OpenFileModal .extension-dropdown .Dropdown__wrapper .Dropdown__items{width:100%}.OpenFileModal .extension-dropdown .Dropdown__wrapper .arrow{flex:unset;width:12px;height:16px;margin-top:2px}.OpenFileModal .extension-dropdown .Dropdown__wrapper .Dropdown .picked-option .picked-option-text{flex:none;width:unset}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},2018:function(e,t,n){"use strict";n.r(t);n(78),n(18),n(15),n(10),n(8),n(9),n(11),n(346),n(347),n(153),n(253),n(117),n(19),n(12),n(13),n(14),n(16),n(20),n(56),n(22),n(64),n(65),n(66),n(67),n(37),n(39),n(23),n(24),n(40),n(63);var r=n(0),o=n.n(r),a=n(6),i=n(130),l=n(17),c=n.n(l),s=n(34),d=n(48),u=n(429),p=n(132),f=n(1774),h=n(448),b=n(3),m=n(2),v=n(5),w=n(341),y=n(172);n(1685),n(1923);function x(e){return(x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function g(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */g=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",i=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function s(e,t,n,o){var a=t&&t.prototype instanceof p?t:p,i=Object.create(a.prototype),l=new L(o||[]);return r(i,"_invoke",{value:O(e,n,l)}),i}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var u={};function p(){}function f(){}function h(){}var b={};c(b,a,(function(){return this}));var m=Object.getPrototypeOf,v=m&&m(m(D([])));v&&v!==t&&n.call(v,a)&&(b=v);var w=h.prototype=p.prototype=Object.create(b);function y(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function E(e,t){var o;r(this,"_invoke",{value:function(r,a){function i(){return new t((function(o,i){!function r(o,a,i,l){var c=d(e[o],e,a);if("throw"!==c.type){var s=c.arg,u=s.value;return u&&"object"==x(u)&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,i,l)}),(function(e){r("throw",e,i,l)})):t.resolve(u).then((function(e){s.value=e,i(s)}),(function(e){return r("throw",e,i,l)}))}l(c.arg)}(r,a,o,i)}))}return o=o?o.then(i,i):i()}})}function O(e,t,n){var r="suspendedStart";return function(o,a){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw a;return k()}for(n.method=o,n.arg=a;;){var i=n.delegate;if(i){var l=F(i,n);if(l){if(l===u)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=d(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===u)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}function F(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,F(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),u;var o=d(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,u;var a=o.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,u):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,u)}function M(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function _(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function L(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(M,this),this.reset(!0)}function D(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:k}}function k(){return{value:void 0,done:!0}}return f.prototype=h,r(w,"constructor",{value:h,configurable:!0}),r(h,"constructor",{value:f,configurable:!0}),f.displayName=c(h,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===f||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,c(e,l,"GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},y(E.prototype),c(E.prototype,i,(function(){return this})),e.AsyncIterator=E,e.async=function(t,n,r,o,a){void 0===a&&(a=Promise);var i=new E(s(t,n,r,o),a);return e.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},y(w),c(w,l,"Generator"),c(w,a,(function(){return this})),c(w,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=D,L.prototype={constructor:L,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(_),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return i.type="throw",i.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],i=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var l=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,u):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),u},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),_(n),u}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;_(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:D(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),u}},e}function E(e,t,n,r,o,a,i){try{var l=e[a](i),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(r,o)}function O(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){E(a,r,o,i,l,"next",e)}function l(e){E(a,r,o,i,l,"throw",e)}i(void 0)}))}}function F(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,l=[],c=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=a.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,o=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw o}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return M(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return M(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function M(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var _={closeElements:m.a.closeElements},L=Object(a.b)((function(e){return{isDisabled:b.a.isElementDisabled(e,v.a.OPEN_FILE_MODAL),isOpen:b.a.isElementOpen(e,v.a.OPEN_FILE_MODAL),tabManager:b.a.getTabManager(e)}}),_)((function(e){var t=e.isDisabled,n=e.isOpen,l=e.tabManager,m=e.closeElements,x=Object(u.a)().t,E=F(Object(a.e)((function(e){return[b.a.getSelectedTab(e,"openFileModal")]})),1)[0],M=F(Object(r.useState)(""),2),_=M[0],L=M[1],D=F(Object(r.useState)("pdf"),2),k=D[0],S=D[1],N=F(Object(r.useState)(),2),A=N[0],j=N[1],P=F(Object(r.useState)(),2),T=P[0],I=P[1],R=F(Object(r.useState)({fileError:"",urlError:"",extensionError:""}),2),C=R[0],G=R[1],H=function(){m([v.a.OPEN_FILE_MODAL]),L(""),G({fileError:"",urlError:""}),j(null),S("pdf"),I(null)};Object(r.useEffect)((function(){n?m([v.a.PRINT_MODAL,v.a.LOADING_MODAL,v.a.PROGRESS_MODAL,v.a.ERROR_MODAL,v.a.MODEL3D_MODAL]):(L(""),G({fileError:"",urlError:""}),j(null),S(null),I(null))}),[n]);var B=function(){var e=O(g().mark((function e(t,n,r,o){var a;return g().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}return e.abrupt("return",G({urlError:"URL or File must be provided"}));case 2:if(n&&-1!==q.indexOf(n)){e.next=4;break}return e.abrupt("return",G({extensionError:"Extension must be provided"}));case 4:return a=!o||i.a.MAX_FILE_SIZE>o,e.next=7,l.addTab(t,{extension:n,filename:r,setActive:!0,saveCurrentActiveTabState:!0,useDB:a});case 7:H();case 8:case"end":return e.stop()}}),e)})));return function(t,n,r,o){return e.apply(this,arguments)}}(),U=c()({Modal:!0,OpenFileModal:!0,open:n,closed:!n}),W=/(?:\.([^.?]+))?$/,z=function(){var e=O(g().mark((function e(t){var n,r,o,a;return g().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(G(null),t){e.next=3;break}return e.abrupt("return");case 3:if(!(t instanceof window.Core.Document)){e.next=8;break}return e.next=6,B(t,t.type,t.filename);case 6:e.next=18;break;case 8:return n=window.Core.mimeTypeToExtension[t.type]||W.exec(t.name)[1]||null,r=t.name,o=t.size,a=URL.createObjectURL(t),L(a),j(r),S(n),I(o),e.next=18,B(t,n,r,o);case 18:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),V=function(){var e=O(g().mark((function e(t){var n;return g().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:G(null),L(t.trim()),n=t.substring(t.lastIndexOf("/")+1).split("?")[0],j(n),S(W.exec(n)[1]),I(null);case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),q=!!Object(s.a)("webviewerServerURL",null)?window.Core.SupportedFileFormats.SERVER:window.Core.SupportedFileFormats.CLIENT;return q=q.reduce((function(e,t){return-1===e.indexOf(t)&&e.push(t),e}),[]),!t&&o.a.createElement("div",{className:U,"data-element":v.a.OPEN_FILE_MODAL,onMouseDown:H},o.a.createElement("div",{className:"container",onMouseDown:function(e){return e.stopPropagation()}},o.a.createElement(w.a,{title:x("OpenFile.newTab"),isOpen:n,closeButtonDataElement:"openFileModalClose",onCloseClick:H,swipeToClose:!0,closeHandler:H},o.a.createElement("div",{className:"swipe-indicator"}),o.a.createElement(p.d,{className:"open-file-modal-tabs",id:"openFileModal"},o.a.createElement("div",{className:"tabs-header-container"},o.a.createElement("div",{className:"tab-list"},o.a.createElement(p.a,{dataElement:"urlInputPanelButton"},o.a.createElement("button",{className:"tab-options-button"},x("link.url"))),o.a.createElement("div",{className:"tab-options-divider"}),o.a.createElement(p.a,{dataElement:"filePickerPanelButton"},o.a.createElement("button",{className:"tab-options-button"},x("option.pageReplacementModal.localFile"))))),o.a.createElement(p.c,{dataElement:"urlInputPanel"},o.a.createElement("div",{className:"panel-body"},o.a.createElement(f.a,{onFileSelect:function(e){V(e)},acceptFormats:q,extension:_.length&&null!=k&&k.length?k:"",setExtension:S,defaultValue:_}))),o.a.createElement(p.c,{dataElement:"filePickerPanel"},o.a.createElement("div",{className:"panel-body"},o.a.createElement(h.a,{onFileProcessed:function(e){return z(e)}})))),o.a.createElement("div",{className:"page-replacement-divider"}),o.a.createElement("div",{className:"footer"},(null==C?void 0:C.urlError)&&o.a.createElement("p",{className:"error"},"* ",C.urlError),(null==C?void 0:C.fileError)&&o.a.createElement("p",{className:"error"},"* ",C.fileError),(null==C?void 0:C.extensionError)&&o.a.createElement("p",{className:"error"},"* ",C.extensionError),o.a.createElement(d.a,{className:"modal-btn",dataElement:"linkSubmitButton",label:x("OpenFile.addTab"),style:{width:90},onClick:Object(y.a)((function(){return B(_,k,A,T)})),disabled:"urlInputPanelButton"!==E||!_.length||!(null!=k&&k.length)})))))}));t.default=L}}]);
//# sourceMappingURL=chunk.72.js.map