(window.webpackJsonp=window.webpackJsonp||[]).push([[27,32],{1475:function(e,t,n){"use strict";n.d(t,"a",(function(){return d})),n.d(t,"b",(function(){return v})),n.d(t,"c",(function(){return f})),n.d(t,"d",(function(){return c})),n.d(t,"e",(function(){return h})),n.d(t,"f",(function(){return m})),n.d(t,"g",(function(){return u})),n.d(t,"h",(function(){return p}));var r=n(0),o=n(1491),i=(n(142),n(1492),n(1512),n(1509)),a=n(1519),l=n(1510),c=!1,u="undefined"!=typeof document,s=r.createContext("undefined"!=typeof HTMLElement?Object(o.a)({key:"css"}):null),d=s.Provider,p=function(e){return Object(r.forwardRef)((function(t,n){var o=Object(r.useContext)(s);return e(t,o,n)}))};u||(p=function(e){return function(t){var n=Object(r.useContext)(s);return null===n?(n=Object(o.a)({key:"css"}),r.createElement(s.Provider,{value:n},e(t,n))):e(t,n)}});var f=r.createContext({});var m={}.hasOwnProperty,b="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",h=function(e,t){var n={};for(var r in t)m.call(t,r)&&(n[r]=t[r]);return n[b]=e,n},g=function(e){var t=e.cache,n=e.serialized,o=e.isStringTag;Object(i.c)(t,n,o);var a=Object(l.a)((function(){return Object(i.b)(t,n,o)}));if(!u&&void 0!==a){for(var c,s=n.name,d=n.next;void 0!==d;)s+=" "+d.name,d=d.next;return r.createElement("style",((c={})["data-emotion"]=t.key+" "+s,c.dangerouslySetInnerHTML={__html:a},c.nonce=t.sheet.nonce,c))}return null},v=p((function(e,t,n){var o=e.css;"string"==typeof o&&void 0!==t.registered[o]&&(o=t.registered[o]);var l=e[b],u=[o],s="";"string"==typeof e.className?s=Object(i.a)(t.registered,u,e.className):null!=e.className&&(s=e.className+" ");var d=Object(a.a)(u,void 0,r.useContext(f));s+=t.key+"-"+d.name;var p={};for(var h in e)m.call(e,h)&&"css"!==h&&h!==b&&!c&&(p[h]=e[h]);return p.className=s,n&&(p.ref=n),r.createElement(r.Fragment,null,r.createElement(g,{cache:t,serialized:d,isStringTag:"string"==typeof l}),r.createElement(l,p))}))},1483:function(e,t,n){"use strict";n.d(t,"a",(function(){return l})),n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return c}));var r=n(1475),o=n(0),i=(n(1509),n(1510),n(1519)),a=(n(1491),n(1476),n(1492),n(345),function(e,t){var n=arguments;if(null==t||!r.f.call(t,"css"))return o.createElement.apply(void 0,n);var i=n.length,a=new Array(i);a[0]=r.b,a[1]=Object(r.e)(e,t);for(var l=2;l<i;l++)a[l]=n[l];return o.createElement.apply(null,a)});function l(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Object(i.a)(t)}var c=function(){var e=l.apply(void 0,arguments),t="animation-"+e.name;return{name:t,styles:"@keyframes "+t+"{"+e.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}},1484:function(e,t,n){"use strict";n.d(t,"a",(function(){return be})),n.d(t,"b",(function(){return K})),n.d(t,"c",(function(){return J}));var r=n(142),o=n(342),i=n(161),a=n(162),l=n(270),c=n(269);function u(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(u=function(){return!!e})()}var s=n(234);var d=n(1518),p=n(0),f=n(1486),m=n(1483),b=n(1503),h=n(1485);for(var g={name:"7pg0cj-a11yText",styles:"label:a11yText;z-index:9999;border:0;clip:rect(1px, 1px, 1px, 1px);height:1px;width:1px;position:absolute;overflow:hidden;padding:0;white-space:nowrap"},v=function(e){return Object(m.b)("span",Object(r.a)({css:g},e))},y={guidance:function(e){var t=e.isSearchable,n=e.isMulti,r=e.tabSelectsValue,o=e.context,i=e.isInitialFocus;switch(o){case"menu":return"Use Up and Down to choose options, press Enter to select the currently focused option, press Escape to exit the menu".concat(r?", press Tab to select the option and exit the menu":"",".");case"input":return i?"".concat(e["aria-label"]||"Select"," is focused ").concat(t?",type to refine list":"",", press Down to open the menu, ").concat(n?" press left to focus selected values":""):"";case"value":return"Use left and right to toggle between focused values, press Backspace to remove the currently focused value";default:return""}},onChange:function(e){var t=e.action,n=e.label,r=void 0===n?"":n,o=e.labels,i=e.isDisabled;switch(t){case"deselect-option":case"pop-value":case"remove-value":return"option ".concat(r,", deselected.");case"clear":return"All selected options have been cleared.";case"initial-input-focus":return"option".concat(o.length>1?"s":""," ").concat(o.join(","),", selected.");case"select-option":return"option ".concat(r,i?" is disabled. Select another option.":", selected.");default:return""}},onFocus:function(e){var t=e.context,n=e.focused,r=e.options,o=e.label,i=void 0===o?"":o,a=e.selectValue,l=e.isDisabled,c=e.isSelected,u=e.isAppleDevice,s=function(e,t){return e&&e.length?"".concat(e.indexOf(t)+1," of ").concat(e.length):""};if("value"===t&&a)return"value ".concat(i," focused, ").concat(s(a,n),".");if("menu"===t&&u){var d=l?" disabled":"",p="".concat(c?" selected":"").concat(d);return"".concat(i).concat(p,", ").concat(s(r,n),".")}return""},onFilter:function(e){var t=e.inputValue,n=e.resultsMessage;return"".concat(n).concat(t?" for search term "+t:"",".")}},O=function(e){var t=e.ariaSelection,n=e.focusedOption,r=e.focusedValue,i=e.focusableOptions,a=e.isFocused,l=e.selectValue,c=e.selectProps,u=e.id,s=e.isAppleDevice,d=c.ariaLiveMessages,f=c.getOptionLabel,b=c.inputValue,h=c.isMulti,g=c.isOptionDisabled,O=c.isSearchable,w=c.menuIsOpen,j=c.options,x=c.screenReaderStatus,F=c.tabSelectsValue,C=c.isLoading,E=c["aria-label"],S=c["aria-live"],P=Object(p.useMemo)((function(){return Object(o.a)(Object(o.a)({},y),d||{})}),[d]),I=Object(p.useMemo)((function(){var e,n="";if(t&&P.onChange){var r=t.option,i=t.options,a=t.removedValue,c=t.removedValues,u=t.value,s=a||r||(e=u,Array.isArray(e)?null:e),d=s?f(s):"",p=i||c||void 0,m=p?p.map(f):[],b=Object(o.a)({isDisabled:s&&g(s,l),label:d,labels:m},t);n=P.onChange(b)}return n}),[t,P,g,l,f]),k=Object(p.useMemo)((function(){var e="",t=n||r,o=!!(n&&l&&l.includes(n));if(t&&P.onFocus){var a={focused:t,label:f(t),isDisabled:g(t,l),isSelected:o,options:i,context:t===n?"menu":"value",selectValue:l,isAppleDevice:s};e=P.onFocus(a)}return e}),[n,r,f,g,P,i,l,s]),M=Object(p.useMemo)((function(){var e="";if(w&&j.length&&!C&&P.onFilter){var t=x({count:i.length});e=P.onFilter({inputValue:b,resultsMessage:t})}return e}),[i,b,w,P,j,x,C]),A="initial-input-focus"===(null==t?void 0:t.action),T=Object(p.useMemo)((function(){var e="";if(P.guidance){var t=r?"value":w?"menu":"input";e=P.guidance({"aria-label":E,context:t,isDisabled:n&&g(n,l),isMulti:h,isSearchable:O,tabSelectsValue:F,isInitialFocus:A})}return e}),[E,n,r,h,g,O,w,P,l,F,A]),R=Object(m.b)(p.Fragment,null,Object(m.b)("span",{id:"aria-selection"},I),Object(m.b)("span",{id:"aria-focused"},k),Object(m.b)("span",{id:"aria-results"},M),Object(m.b)("span",{id:"aria-guidance"},T));return Object(m.b)(p.Fragment,null,Object(m.b)(v,{id:u},A&&R),Object(m.b)(v,{"aria-live":S,"aria-atomic":"false","aria-relevant":"additions text",role:"log"},a&&!A&&R))},w=[{base:"A",letters:"AⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ"},{base:"AA",letters:"Ꜳ"},{base:"AE",letters:"ÆǼǢ"},{base:"AO",letters:"Ꜵ"},{base:"AU",letters:"Ꜷ"},{base:"AV",letters:"ꜸꜺ"},{base:"AY",letters:"Ꜽ"},{base:"B",letters:"BⒷＢḂḄḆɃƂƁ"},{base:"C",letters:"CⒸＣĆĈĊČÇḈƇȻꜾ"},{base:"D",letters:"DⒹＤḊĎḌḐḒḎĐƋƊƉꝹ"},{base:"DZ",letters:"ǱǄ"},{base:"Dz",letters:"ǲǅ"},{base:"E",letters:"EⒺＥÈÉÊỀẾỄỂẼĒḔḖĔĖËẺĚȄȆẸỆȨḜĘḘḚƐƎ"},{base:"F",letters:"FⒻＦḞƑꝻ"},{base:"G",letters:"GⒼＧǴĜḠĞĠǦĢǤƓꞠꝽꝾ"},{base:"H",letters:"HⒽＨĤḢḦȞḤḨḪĦⱧⱵꞍ"},{base:"I",letters:"IⒾＩÌÍÎĨĪĬİÏḮỈǏȈȊỊĮḬƗ"},{base:"J",letters:"JⒿＪĴɈ"},{base:"K",letters:"KⓀＫḰǨḲĶḴƘⱩꝀꝂꝄꞢ"},{base:"L",letters:"LⓁＬĿĹĽḶḸĻḼḺŁȽⱢⱠꝈꝆꞀ"},{base:"LJ",letters:"Ǉ"},{base:"Lj",letters:"ǈ"},{base:"M",letters:"MⓂＭḾṀṂⱮƜ"},{base:"N",letters:"NⓃＮǸŃÑṄŇṆŅṊṈȠƝꞐꞤ"},{base:"NJ",letters:"Ǌ"},{base:"Nj",letters:"ǋ"},{base:"O",letters:"OⓄＯÒÓÔỒỐỖỔÕṌȬṎŌṐṒŎȮȰÖȪỎŐǑȌȎƠỜỚỠỞỢỌỘǪǬØǾƆƟꝊꝌ"},{base:"OI",letters:"Ƣ"},{base:"OO",letters:"Ꝏ"},{base:"OU",letters:"Ȣ"},{base:"P",letters:"PⓅＰṔṖƤⱣꝐꝒꝔ"},{base:"Q",letters:"QⓆＱꝖꝘɊ"},{base:"R",letters:"RⓇＲŔṘŘȐȒṚṜŖṞɌⱤꝚꞦꞂ"},{base:"S",letters:"SⓈＳẞŚṤŜṠŠṦṢṨȘŞⱾꞨꞄ"},{base:"T",letters:"TⓉＴṪŤṬȚŢṰṮŦƬƮȾꞆ"},{base:"TZ",letters:"Ꜩ"},{base:"U",letters:"UⓊＵÙÚÛŨṸŪṺŬÜǛǗǕǙỦŮŰǓȔȖƯỪỨỮỬỰỤṲŲṶṴɄ"},{base:"V",letters:"VⓋＶṼṾƲꝞɅ"},{base:"VY",letters:"Ꝡ"},{base:"W",letters:"WⓌＷẀẂŴẆẄẈⱲ"},{base:"X",letters:"XⓍＸẊẌ"},{base:"Y",letters:"YⓎＹỲÝŶỸȲẎŸỶỴƳɎỾ"},{base:"Z",letters:"ZⓏＺŹẐŻŽẒẔƵȤⱿⱫꝢ"},{base:"a",letters:"aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐ"},{base:"aa",letters:"ꜳ"},{base:"ae",letters:"æǽǣ"},{base:"ao",letters:"ꜵ"},{base:"au",letters:"ꜷ"},{base:"av",letters:"ꜹꜻ"},{base:"ay",letters:"ꜽ"},{base:"b",letters:"bⓑｂḃḅḇƀƃɓ"},{base:"c",letters:"cⓒｃćĉċčçḉƈȼꜿↄ"},{base:"d",letters:"dⓓｄḋďḍḑḓḏđƌɖɗꝺ"},{base:"dz",letters:"ǳǆ"},{base:"e",letters:"eⓔｅèéêềếễểẽēḕḗĕėëẻěȅȇẹệȩḝęḙḛɇɛǝ"},{base:"f",letters:"fⓕｆḟƒꝼ"},{base:"g",letters:"gⓖｇǵĝḡğġǧģǥɠꞡᵹꝿ"},{base:"h",letters:"hⓗｈĥḣḧȟḥḩḫẖħⱨⱶɥ"},{base:"hv",letters:"ƕ"},{base:"i",letters:"iⓘｉìíîĩīĭïḯỉǐȉȋịįḭɨı"},{base:"j",letters:"jⓙｊĵǰɉ"},{base:"k",letters:"kⓚｋḱǩḳķḵƙⱪꝁꝃꝅꞣ"},{base:"l",letters:"lⓛｌŀĺľḷḹļḽḻſłƚɫⱡꝉꞁꝇ"},{base:"lj",letters:"ǉ"},{base:"m",letters:"mⓜｍḿṁṃɱɯ"},{base:"n",letters:"nⓝｎǹńñṅňṇņṋṉƞɲŉꞑꞥ"},{base:"nj",letters:"ǌ"},{base:"o",letters:"oⓞｏòóôồốỗổõṍȭṏōṑṓŏȯȱöȫỏőǒȍȏơờớỡởợọộǫǭøǿɔꝋꝍɵ"},{base:"oi",letters:"ƣ"},{base:"ou",letters:"ȣ"},{base:"oo",letters:"ꝏ"},{base:"p",letters:"pⓟｐṕṗƥᵽꝑꝓꝕ"},{base:"q",letters:"qⓠｑɋꝗꝙ"},{base:"r",letters:"rⓡｒŕṙřȑȓṛṝŗṟɍɽꝛꞧꞃ"},{base:"s",letters:"sⓢｓßśṥŝṡšṧṣṩșşȿꞩꞅẛ"},{base:"t",letters:"tⓣｔṫẗťṭțţṱṯŧƭʈⱦꞇ"},{base:"tz",letters:"ꜩ"},{base:"u",letters:"uⓤｕùúûũṹūṻŭüǜǘǖǚủůűǔȕȗưừứữửựụṳųṷṵʉ"},{base:"v",letters:"vⓥｖṽṿʋꝟʌ"},{base:"vy",letters:"ꝡ"},{base:"w",letters:"wⓦｗẁẃŵẇẅẘẉⱳ"},{base:"x",letters:"xⓧｘẋẍ"},{base:"y",letters:"yⓨｙỳýŷỹȳẏÿỷẙỵƴɏỿ"},{base:"z",letters:"zⓩｚźẑżžẓẕƶȥɀⱬꝣ"}],j=new RegExp("["+w.map((function(e){return e.letters})).join("")+"]","g"),x={},F=0;F<w.length;F++)for(var C=w[F],E=0;E<C.letters.length;E++)x[C.letters[E]]=C.base;var S=function(e){return e.replace(j,(function(e){return x[e]}))},P=Object(b.a)(S),I=function(e){return e.replace(/^\s+|\s+$/g,"")},k=function(e){return"".concat(e.label," ").concat(e.value)},M=["innerRef"];function A(e){var t=e.innerRef,n=Object(h.a)(e,M),o=Object(f.D)(n,"onExited","in","enter","exit","appear");return Object(m.b)("input",Object(r.a)({ref:t},o,{css:Object(m.a)({label:"dummyInput",background:0,border:0,caretColor:"transparent",fontSize:"inherit",gridArea:"1 / 1 / 2 / 3",outline:0,padding:0,width:1,color:"transparent",left:-100,opacity:0,position:"relative",transform:"scale(.01)"},"","")}))}var T=["boxSizing","height","overflow","paddingRight","position"],R={boxSizing:"border-box",overflow:"hidden",position:"relative",height:"100%"};function V(e){e.preventDefault()}function L(e){e.stopPropagation()}function D(){var e=this.scrollTop,t=this.scrollHeight,n=e+this.offsetHeight;0===e?this.scrollTop=1:n===t&&(this.scrollTop=e-1)}function N(){return"ontouchstart"in window||navigator.maxTouchPoints}var H=!("undefined"==typeof window||!window.document||!window.document.createElement),_=0,U={capture:!1,passive:!1};var B=function(e){var t=e.target;return t.ownerDocument.activeElement&&t.ownerDocument.activeElement.blur()},z={name:"1kfdb0e",styles:"position:fixed;left:0;bottom:0;right:0;top:0"};function W(e){var t=e.children,n=e.lockEnabled,r=e.captureEnabled,o=function(e){var t=e.isEnabled,n=e.onBottomArrive,r=e.onBottomLeave,o=e.onTopArrive,i=e.onTopLeave,a=Object(p.useRef)(!1),l=Object(p.useRef)(!1),c=Object(p.useRef)(0),u=Object(p.useRef)(null),s=Object(p.useCallback)((function(e,t){if(null!==u.current){var c=u.current,s=c.scrollTop,d=c.scrollHeight,p=c.clientHeight,f=u.current,m=t>0,b=d-p-s,h=!1;b>t&&a.current&&(r&&r(e),a.current=!1),m&&l.current&&(i&&i(e),l.current=!1),m&&t>b?(n&&!a.current&&n(e),f.scrollTop=d,h=!0,a.current=!0):!m&&-t>s&&(o&&!l.current&&o(e),f.scrollTop=0,h=!0,l.current=!0),h&&function(e){e.cancelable&&e.preventDefault(),e.stopPropagation()}(e)}}),[n,r,o,i]),d=Object(p.useCallback)((function(e){s(e,e.deltaY)}),[s]),m=Object(p.useCallback)((function(e){c.current=e.changedTouches[0].clientY}),[]),b=Object(p.useCallback)((function(e){var t=c.current-e.changedTouches[0].clientY;s(e,t)}),[s]),h=Object(p.useCallback)((function(e){if(e){var t=!!f.E&&{passive:!1};e.addEventListener("wheel",d,t),e.addEventListener("touchstart",m,t),e.addEventListener("touchmove",b,t)}}),[b,m,d]),g=Object(p.useCallback)((function(e){e&&(e.removeEventListener("wheel",d,!1),e.removeEventListener("touchstart",m,!1),e.removeEventListener("touchmove",b,!1))}),[b,m,d]);return Object(p.useEffect)((function(){if(t){var e=u.current;return h(e),function(){g(e)}}}),[t,h,g]),function(e){u.current=e}}({isEnabled:void 0===r||r,onBottomArrive:e.onBottomArrive,onBottomLeave:e.onBottomLeave,onTopArrive:e.onTopArrive,onTopLeave:e.onTopLeave}),i=function(e){var t=e.isEnabled,n=e.accountForScrollbars,r=void 0===n||n,o=Object(p.useRef)({}),i=Object(p.useRef)(null),a=Object(p.useCallback)((function(e){if(H){var t=document.body,n=t&&t.style;if(r&&T.forEach((function(e){var t=n&&n[e];o.current[e]=t})),r&&_<1){var i=parseInt(o.current.paddingRight,10)||0,a=document.body?document.body.clientWidth:0,l=window.innerWidth-a+i||0;Object.keys(R).forEach((function(e){var t=R[e];n&&(n[e]=t)})),n&&(n.paddingRight="".concat(l,"px"))}t&&N()&&(t.addEventListener("touchmove",V,U),e&&(e.addEventListener("touchstart",D,U),e.addEventListener("touchmove",L,U))),_+=1}}),[r]),l=Object(p.useCallback)((function(e){if(H){var t=document.body,n=t&&t.style;_=Math.max(_-1,0),r&&_<1&&T.forEach((function(e){var t=o.current[e];n&&(n[e]=t)})),t&&N()&&(t.removeEventListener("touchmove",V,U),e&&(e.removeEventListener("touchstart",D,U),e.removeEventListener("touchmove",L,U)))}}),[r]);return Object(p.useEffect)((function(){if(t){var e=i.current;return a(e),function(){l(e)}}}),[t,a,l]),function(e){i.current=e}}({isEnabled:n});return Object(m.b)(p.Fragment,null,n&&Object(m.b)("div",{onClick:B,css:z}),t((function(e){o(e),i(e)})))}var $={name:"1a0ro4n-requiredInput",styles:"label:requiredInput;opacity:0;pointer-events:none;position:absolute;bottom:0;left:0;right:0;width:100%"},q=function(e){var t=e.name,n=e.onFocus;return Object(m.b)("input",{required:!0,name:t,tabIndex:-1,"aria-hidden":"true",onFocus:n,css:$,value:"",onChange:function(){}})};function G(e){var t;return"undefined"!=typeof window&&null!=window.navigator&&e.test((null===(t=window.navigator.userAgentData)||void 0===t?void 0:t.platform)||window.navigator.platform)}function Y(){return G(/^Mac/i)}function X(){return G(/^iPhone/i)||G(/^iPad/i)||Y()&&navigator.maxTouchPoints>1}var K=function(e){return e.label},J=function(e){return e.value},Q={clearIndicator:f.m,container:f.n,control:f.p,dropdownIndicator:f.q,group:f.s,groupHeading:f.r,indicatorsContainer:f.u,indicatorSeparator:f.t,input:f.v,loadingIndicator:f.x,loadingMessage:f.w,menu:f.y,menuList:f.z,menuPortal:f.A,multiValue:f.B,multiValueLabel:f.C,multiValueRemove:f.F,noOptionsMessage:f.G,option:f.H,placeholder:f.I,singleValue:f.J,valueContainer:f.K};var Z,ee={borderRadius:4,colors:{primary:"#2684FF",primary75:"#4C9AFF",primary50:"#B2D4FF",primary25:"#DEEBFF",danger:"#DE350B",dangerLight:"#FFBDAD",neutral0:"hsl(0, 0%, 100%)",neutral5:"hsl(0, 0%, 95%)",neutral10:"hsl(0, 0%, 90%)",neutral20:"hsl(0, 0%, 80%)",neutral30:"hsl(0, 0%, 70%)",neutral40:"hsl(0, 0%, 60%)",neutral50:"hsl(0, 0%, 50%)",neutral60:"hsl(0, 0%, 40%)",neutral70:"hsl(0, 0%, 30%)",neutral80:"hsl(0, 0%, 20%)",neutral90:"hsl(0, 0%, 10%)"},spacing:{baseUnit:4,controlHeight:38,menuGutter:8}},te={"aria-live":"polite",backspaceRemovesValue:!0,blurInputOnSelect:Object(f.L)(),captureMenuScroll:!Object(f.L)(),classNames:{},closeMenuOnSelect:!0,closeMenuOnScroll:!1,components:{},controlShouldRenderValue:!0,escapeClearsValue:!1,filterOption:function(e,t){if(e.data.__isNew__)return!0;var n=Object(o.a)({ignoreCase:!0,ignoreAccents:!0,stringify:k,trim:!0,matchFrom:"any"},Z),r=n.ignoreCase,i=n.ignoreAccents,a=n.stringify,l=n.trim,c=n.matchFrom,u=l?I(t):t,s=l?I(a(e)):a(e);return r&&(u=u.toLowerCase(),s=s.toLowerCase()),i&&(u=P(u),s=S(s)),"start"===c?s.substr(0,u.length)===u:s.indexOf(u)>-1},formatGroupLabel:function(e){return e.label},getOptionLabel:K,getOptionValue:J,isDisabled:!1,isLoading:!1,isMulti:!1,isRtl:!1,isSearchable:!0,isOptionDisabled:function(e){return!!e.isDisabled},loadingMessage:function(){return"Loading..."},maxMenuHeight:300,minMenuHeight:140,menuIsOpen:!1,menuPlacement:"bottom",menuPosition:"absolute",menuShouldBlockScroll:!1,menuShouldScrollIntoView:!Object(f.a)(),noOptionsMessage:function(){return"No options"},openMenuOnFocus:!1,openMenuOnClick:!0,options:[],pageSize:5,placeholder:"Select...",screenReaderStatus:function(e){var t=e.count;return"".concat(t," result").concat(1!==t?"s":""," available")},styles:{},tabIndex:0,tabSelectsValue:!0,unstyled:!1};function ne(e,t,n,r){return{type:"option",data:t,isDisabled:se(e,t,n),isSelected:de(e,t,n),label:ce(e,t),value:ue(e,t),index:r}}function re(e,t){return e.options.map((function(n,r){if("options"in n){var o=n.options.map((function(n,r){return ne(e,n,t,r)})).filter((function(t){return ae(e,t)}));return o.length>0?{type:"group",data:n,options:o,index:r}:void 0}var i=ne(e,n,t,r);return ae(e,i)?i:void 0})).filter(f.k)}function oe(e){return e.reduce((function(e,t){return"group"===t.type?e.push.apply(e,Object(d.a)(t.options.map((function(e){return e.data})))):e.push(t.data),e}),[])}function ie(e,t){return e.reduce((function(e,n){return"group"===n.type?e.push.apply(e,Object(d.a)(n.options.map((function(e){return{data:e.data,id:"".concat(t,"-").concat(n.index,"-").concat(e.index)}})))):e.push({data:n.data,id:"".concat(t,"-").concat(n.index)}),e}),[])}function ae(e,t){var n=e.inputValue,r=void 0===n?"":n,o=t.data,i=t.isSelected,a=t.label,l=t.value;return(!fe(e)||!i)&&pe(e,{label:a,value:l,data:o},r)}var le=function(e,t){var n;return(null===(n=e.find((function(e){return e.data===t})))||void 0===n?void 0:n.id)||null},ce=function(e,t){return e.getOptionLabel(t)},ue=function(e,t){return e.getOptionValue(t)};function se(e,t,n){return"function"==typeof e.isOptionDisabled&&e.isOptionDisabled(t,n)}function de(e,t,n){if(n.indexOf(t)>-1)return!0;if("function"==typeof e.isOptionSelected)return e.isOptionSelected(t,n);var r=ue(e,t);return n.some((function(t){return ue(e,t)===r}))}function pe(e,t,n){return!e.filterOption||e.filterOption(t,n)}var fe=function(e){var t=e.hideSelectedOptions,n=e.isMulti;return void 0===t?n:t},me=1,be=function(e){Object(l.a)(b,e);var t,n,m=(t=b,n=u(),function(){var e,r=Object(c.a)(t);if(n){var o=Object(c.a)(this).constructor;e=Reflect.construct(r,arguments,o)}else e=r.apply(this,arguments);return Object(s.a)(this,e)});function b(e){var t;if(Object(i.a)(this,b),(t=m.call(this,e)).state={ariaSelection:null,focusedOption:null,focusedOptionId:null,focusableOptionsWithIds:[],focusedValue:null,inputIsHidden:!1,isFocused:!1,selectValue:[],clearFocusValueOnUpdate:!1,prevWasFocused:!1,inputIsHiddenAfterUpdate:void 0,prevProps:void 0,instancePrefix:""},t.blockOptionHover=!1,t.isComposing=!1,t.commonProps=void 0,t.initialTouchX=0,t.initialTouchY=0,t.openAfterFocus=!1,t.scrollToFocusedOptionOnUpdate=!1,t.userIsDragging=void 0,t.isAppleDevice=Y()||X(),t.controlRef=null,t.getControlRef=function(e){t.controlRef=e},t.focusedOptionRef=null,t.getFocusedOptionRef=function(e){t.focusedOptionRef=e},t.menuListRef=null,t.getMenuListRef=function(e){t.menuListRef=e},t.inputRef=null,t.getInputRef=function(e){t.inputRef=e},t.focus=t.focusInput,t.blur=t.blurInput,t.onChange=function(e,n){var r=t.props,o=r.onChange,i=r.name;n.name=i,t.ariaOnChange(e,n),o(e,n)},t.setValue=function(e,n,r){var o=t.props,i=o.closeMenuOnSelect,a=o.isMulti,l=o.inputValue;t.onInputChange("",{action:"set-value",prevInputValue:l}),i&&(t.setState({inputIsHiddenAfterUpdate:!a}),t.onMenuClose()),t.setState({clearFocusValueOnUpdate:!0}),t.onChange(e,{action:n,option:r})},t.selectOption=function(e){var n=t.props,r=n.blurInputOnSelect,o=n.isMulti,i=n.name,a=t.state.selectValue,l=o&&t.isOptionSelected(e,a),c=t.isOptionDisabled(e,a);if(l){var u=t.getOptionValue(e);t.setValue(Object(f.b)(a.filter((function(e){return t.getOptionValue(e)!==u}))),"deselect-option",e)}else{if(c)return void t.ariaOnChange(Object(f.c)(e),{action:"select-option",option:e,name:i});o?t.setValue(Object(f.b)([].concat(Object(d.a)(a),[e])),"select-option",e):t.setValue(Object(f.c)(e),"select-option")}r&&t.blurInput()},t.removeValue=function(e){var n=t.props.isMulti,r=t.state.selectValue,o=t.getOptionValue(e),i=r.filter((function(e){return t.getOptionValue(e)!==o})),a=Object(f.d)(n,i,i[0]||null);t.onChange(a,{action:"remove-value",removedValue:e}),t.focusInput()},t.clearValue=function(){var e=t.state.selectValue;t.onChange(Object(f.d)(t.props.isMulti,[],null),{action:"clear",removedValues:e})},t.popValue=function(){var e=t.props.isMulti,n=t.state.selectValue,r=n[n.length-1],o=n.slice(0,n.length-1),i=Object(f.d)(e,o,o[0]||null);r&&t.onChange(i,{action:"pop-value",removedValue:r})},t.getFocusedOptionId=function(e){return le(t.state.focusableOptionsWithIds,e)},t.getFocusableOptionsWithIds=function(){return ie(re(t.props,t.state.selectValue),t.getElementId("option"))},t.getValue=function(){return t.state.selectValue},t.cx=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return f.e.apply(void 0,[t.props.classNamePrefix].concat(n))},t.getOptionLabel=function(e){return ce(t.props,e)},t.getOptionValue=function(e){return ue(t.props,e)},t.getStyles=function(e,n){var r=t.props.unstyled,o=Q[e](n,r);o.boxSizing="border-box";var i=t.props.styles[e];return i?i(o,n):o},t.getClassNames=function(e,n){var r,o;return null===(r=(o=t.props.classNames)[e])||void 0===r?void 0:r.call(o,n)},t.getElementId=function(e){return"".concat(t.state.instancePrefix,"-").concat(e)},t.getComponents=function(){return Object(f.f)(t.props)},t.buildCategorizedOptions=function(){return re(t.props,t.state.selectValue)},t.getCategorizedOptions=function(){return t.props.menuIsOpen?t.buildCategorizedOptions():[]},t.buildFocusableOptions=function(){return oe(t.buildCategorizedOptions())},t.getFocusableOptions=function(){return t.props.menuIsOpen?t.buildFocusableOptions():[]},t.ariaOnChange=function(e,n){t.setState({ariaSelection:Object(o.a)({value:e},n)})},t.onMenuMouseDown=function(e){0===e.button&&(e.stopPropagation(),e.preventDefault(),t.focusInput())},t.onMenuMouseMove=function(e){t.blockOptionHover=!1},t.onControlMouseDown=function(e){if(!e.defaultPrevented){var n=t.props.openMenuOnClick;t.state.isFocused?t.props.menuIsOpen?"INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&t.onMenuClose():n&&t.openMenu("first"):(n&&(t.openAfterFocus=!0),t.focusInput()),"INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&e.preventDefault()}},t.onDropdownIndicatorMouseDown=function(e){if(!(e&&"mousedown"===e.type&&0!==e.button||t.props.isDisabled)){var n=t.props,r=n.isMulti,o=n.menuIsOpen;t.focusInput(),o?(t.setState({inputIsHiddenAfterUpdate:!r}),t.onMenuClose()):t.openMenu("first"),e.preventDefault()}},t.onClearIndicatorMouseDown=function(e){e&&"mousedown"===e.type&&0!==e.button||(t.clearValue(),e.preventDefault(),t.openAfterFocus=!1,"touchend"===e.type?t.focusInput():setTimeout((function(){return t.focusInput()})))},t.onScroll=function(e){"boolean"==typeof t.props.closeMenuOnScroll?e.target instanceof HTMLElement&&Object(f.g)(e.target)&&t.props.onMenuClose():"function"==typeof t.props.closeMenuOnScroll&&t.props.closeMenuOnScroll(e)&&t.props.onMenuClose()},t.onCompositionStart=function(){t.isComposing=!0},t.onCompositionEnd=function(){t.isComposing=!1},t.onTouchStart=function(e){var n=e.touches,r=n&&n.item(0);r&&(t.initialTouchX=r.clientX,t.initialTouchY=r.clientY,t.userIsDragging=!1)},t.onTouchMove=function(e){var n=e.touches,r=n&&n.item(0);if(r){var o=Math.abs(r.clientX-t.initialTouchX),i=Math.abs(r.clientY-t.initialTouchY);t.userIsDragging=o>5||i>5}},t.onTouchEnd=function(e){t.userIsDragging||(t.controlRef&&!t.controlRef.contains(e.target)&&t.menuListRef&&!t.menuListRef.contains(e.target)&&t.blurInput(),t.initialTouchX=0,t.initialTouchY=0)},t.onControlTouchEnd=function(e){t.userIsDragging||t.onControlMouseDown(e)},t.onClearIndicatorTouchEnd=function(e){t.userIsDragging||t.onClearIndicatorMouseDown(e)},t.onDropdownIndicatorTouchEnd=function(e){t.userIsDragging||t.onDropdownIndicatorMouseDown(e)},t.handleInputChange=function(e){var n=t.props.inputValue,r=e.currentTarget.value;t.setState({inputIsHiddenAfterUpdate:!1}),t.onInputChange(r,{action:"input-change",prevInputValue:n}),t.props.menuIsOpen||t.onMenuOpen()},t.onInputFocus=function(e){t.props.onFocus&&t.props.onFocus(e),t.setState({inputIsHiddenAfterUpdate:!1,isFocused:!0}),(t.openAfterFocus||t.props.openMenuOnFocus)&&t.openMenu("first"),t.openAfterFocus=!1},t.onInputBlur=function(e){var n=t.props.inputValue;t.menuListRef&&t.menuListRef.contains(document.activeElement)?t.inputRef.focus():(t.props.onBlur&&t.props.onBlur(e),t.onInputChange("",{action:"input-blur",prevInputValue:n}),t.onMenuClose(),t.setState({focusedValue:null,isFocused:!1}))},t.onOptionHover=function(e){if(!t.blockOptionHover&&t.state.focusedOption!==e){var n=t.getFocusableOptions().indexOf(e);t.setState({focusedOption:e,focusedOptionId:n>-1?t.getFocusedOptionId(e):null})}},t.shouldHideSelectedOptions=function(){return fe(t.props)},t.onValueInputFocus=function(e){e.preventDefault(),e.stopPropagation(),t.focus()},t.onKeyDown=function(e){var n=t.props,r=n.isMulti,o=n.backspaceRemovesValue,i=n.escapeClearsValue,a=n.inputValue,l=n.isClearable,c=n.isDisabled,u=n.menuIsOpen,s=n.onKeyDown,d=n.tabSelectsValue,p=n.openMenuOnFocus,f=t.state,m=f.focusedOption,b=f.focusedValue,h=f.selectValue;if(!(c||"function"==typeof s&&(s(e),e.defaultPrevented))){switch(t.blockOptionHover=!0,e.key){case"ArrowLeft":if(!r||a)return;t.focusValue("previous");break;case"ArrowRight":if(!r||a)return;t.focusValue("next");break;case"Delete":case"Backspace":if(a)return;if(b)t.removeValue(b);else{if(!o)return;r?t.popValue():l&&t.clearValue()}break;case"Tab":if(t.isComposing)return;if(e.shiftKey||!u||!d||!m||p&&t.isOptionSelected(m,h))return;t.selectOption(m);break;case"Enter":if(229===e.keyCode)break;if(u){if(!m)return;if(t.isComposing)return;t.selectOption(m);break}return;case"Escape":u?(t.setState({inputIsHiddenAfterUpdate:!1}),t.onInputChange("",{action:"menu-close",prevInputValue:a}),t.onMenuClose()):l&&i&&t.clearValue();break;case" ":if(a)return;if(!u){t.openMenu("first");break}if(!m)return;t.selectOption(m);break;case"ArrowUp":u?t.focusOption("up"):t.openMenu("last");break;case"ArrowDown":u?t.focusOption("down"):t.openMenu("first");break;case"PageUp":if(!u)return;t.focusOption("pageup");break;case"PageDown":if(!u)return;t.focusOption("pagedown");break;case"Home":if(!u)return;t.focusOption("first");break;case"End":if(!u)return;t.focusOption("last");break;default:return}e.preventDefault()}},t.state.instancePrefix="react-select-"+(t.props.instanceId||++me),t.state.selectValue=Object(f.h)(e.value),e.menuIsOpen&&t.state.selectValue.length){var n=t.getFocusableOptionsWithIds(),r=t.buildFocusableOptions(),a=r.indexOf(t.state.selectValue[0]);t.state.focusableOptionsWithIds=n,t.state.focusedOption=r[a],t.state.focusedOptionId=le(n,r[a])}return t}return Object(a.a)(b,[{key:"componentDidMount",value:function(){this.startListeningComposition(),this.startListeningToTouch(),this.props.closeMenuOnScroll&&document&&document.addEventListener&&document.addEventListener("scroll",this.onScroll,!0),this.props.autoFocus&&this.focusInput(),this.props.menuIsOpen&&this.state.focusedOption&&this.menuListRef&&this.focusedOptionRef&&Object(f.i)(this.menuListRef,this.focusedOptionRef)}},{key:"componentDidUpdate",value:function(e){var t=this.props,n=t.isDisabled,r=t.menuIsOpen,o=this.state.isFocused;(o&&!n&&e.isDisabled||o&&r&&!e.menuIsOpen)&&this.focusInput(),o&&n&&!e.isDisabled?this.setState({isFocused:!1},this.onMenuClose):o||n||!e.isDisabled||this.inputRef!==document.activeElement||this.setState({isFocused:!0}),this.menuListRef&&this.focusedOptionRef&&this.scrollToFocusedOptionOnUpdate&&(Object(f.i)(this.menuListRef,this.focusedOptionRef),this.scrollToFocusedOptionOnUpdate=!1)}},{key:"componentWillUnmount",value:function(){this.stopListeningComposition(),this.stopListeningToTouch(),document.removeEventListener("scroll",this.onScroll,!0)}},{key:"onMenuOpen",value:function(){this.props.onMenuOpen()}},{key:"onMenuClose",value:function(){this.onInputChange("",{action:"menu-close",prevInputValue:this.props.inputValue}),this.props.onMenuClose()}},{key:"onInputChange",value:function(e,t){this.props.onInputChange(e,t)}},{key:"focusInput",value:function(){this.inputRef&&this.inputRef.focus()}},{key:"blurInput",value:function(){this.inputRef&&this.inputRef.blur()}},{key:"openMenu",value:function(e){var t=this,n=this.state,r=n.selectValue,o=n.isFocused,i=this.buildFocusableOptions(),a="first"===e?0:i.length-1;if(!this.props.isMulti){var l=i.indexOf(r[0]);l>-1&&(a=l)}this.scrollToFocusedOptionOnUpdate=!(o&&this.menuListRef),this.setState({inputIsHiddenAfterUpdate:!1,focusedValue:null,focusedOption:i[a],focusedOptionId:this.getFocusedOptionId(i[a])},(function(){return t.onMenuOpen()}))}},{key:"focusValue",value:function(e){var t=this.state,n=t.selectValue,r=t.focusedValue;if(this.props.isMulti){this.setState({focusedOption:null});var o=n.indexOf(r);r||(o=-1);var i=n.length-1,a=-1;if(n.length){switch(e){case"previous":a=0===o?0:-1===o?i:o-1;break;case"next":o>-1&&o<i&&(a=o+1)}this.setState({inputIsHidden:-1!==a,focusedValue:n[a]})}}}},{key:"focusOption",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"first",t=this.props.pageSize,n=this.state.focusedOption,r=this.getFocusableOptions();if(r.length){var o=0,i=r.indexOf(n);n||(i=-1),"up"===e?o=i>0?i-1:r.length-1:"down"===e?o=(i+1)%r.length:"pageup"===e?(o=i-t)<0&&(o=0):"pagedown"===e?(o=i+t)>r.length-1&&(o=r.length-1):"last"===e&&(o=r.length-1),this.scrollToFocusedOptionOnUpdate=!0,this.setState({focusedOption:r[o],focusedValue:null,focusedOptionId:this.getFocusedOptionId(r[o])})}}},{key:"getTheme",value:function(){return this.props.theme?"function"==typeof this.props.theme?this.props.theme(ee):Object(o.a)(Object(o.a)({},ee),this.props.theme):ee}},{key:"getCommonProps",value:function(){var e=this.clearValue,t=this.cx,n=this.getStyles,r=this.getClassNames,o=this.getValue,i=this.selectOption,a=this.setValue,l=this.props,c=l.isMulti,u=l.isRtl,s=l.options;return{clearValue:e,cx:t,getStyles:n,getClassNames:r,getValue:o,hasValue:this.hasValue(),isMulti:c,isRtl:u,options:s,selectOption:i,selectProps:l,setValue:a,theme:this.getTheme()}}},{key:"hasValue",value:function(){return this.state.selectValue.length>0}},{key:"hasOptions",value:function(){return!!this.getFocusableOptions().length}},{key:"isClearable",value:function(){var e=this.props,t=e.isClearable,n=e.isMulti;return void 0===t?n:t}},{key:"isOptionDisabled",value:function(e,t){return se(this.props,e,t)}},{key:"isOptionSelected",value:function(e,t){return de(this.props,e,t)}},{key:"filterOption",value:function(e,t){return pe(this.props,e,t)}},{key:"formatOptionLabel",value:function(e,t){if("function"==typeof this.props.formatOptionLabel){var n=this.props.inputValue,r=this.state.selectValue;return this.props.formatOptionLabel(e,{context:t,inputValue:n,selectValue:r})}return this.getOptionLabel(e)}},{key:"formatGroupLabel",value:function(e){return this.props.formatGroupLabel(e)}},{key:"startListeningComposition",value:function(){document&&document.addEventListener&&(document.addEventListener("compositionstart",this.onCompositionStart,!1),document.addEventListener("compositionend",this.onCompositionEnd,!1))}},{key:"stopListeningComposition",value:function(){document&&document.removeEventListener&&(document.removeEventListener("compositionstart",this.onCompositionStart),document.removeEventListener("compositionend",this.onCompositionEnd))}},{key:"startListeningToTouch",value:function(){document&&document.addEventListener&&(document.addEventListener("touchstart",this.onTouchStart,!1),document.addEventListener("touchmove",this.onTouchMove,!1),document.addEventListener("touchend",this.onTouchEnd,!1))}},{key:"stopListeningToTouch",value:function(){document&&document.removeEventListener&&(document.removeEventListener("touchstart",this.onTouchStart),document.removeEventListener("touchmove",this.onTouchMove),document.removeEventListener("touchend",this.onTouchEnd))}},{key:"renderInput",value:function(){var e=this.props,t=e.isDisabled,n=e.isSearchable,i=e.inputId,a=e.inputValue,l=e.tabIndex,c=e.form,u=e.menuIsOpen,s=e.required,d=this.getComponents().Input,m=this.state,b=m.inputIsHidden,h=m.ariaSelection,g=this.commonProps,v=i||this.getElementId("input"),y=Object(o.a)(Object(o.a)(Object(o.a)({"aria-autocomplete":"list","aria-expanded":u,"aria-haspopup":!0,"aria-errormessage":this.props["aria-errormessage"],"aria-invalid":this.props["aria-invalid"],"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-required":s,role:"combobox","aria-activedescendant":this.isAppleDevice?void 0:this.state.focusedOptionId||""},u&&{"aria-controls":this.getElementId("listbox")}),!n&&{"aria-readonly":!0}),this.hasValue()?"initial-input-focus"===(null==h?void 0:h.action)&&{"aria-describedby":this.getElementId("live-region")}:{"aria-describedby":this.getElementId("placeholder")});return n?p.createElement(d,Object(r.a)({},g,{autoCapitalize:"none",autoComplete:"off",autoCorrect:"off",id:v,innerRef:this.getInputRef,isDisabled:t,isHidden:b,onBlur:this.onInputBlur,onChange:this.handleInputChange,onFocus:this.onInputFocus,spellCheck:"false",tabIndex:l,form:c,type:"text",value:a},y)):p.createElement(A,Object(r.a)({id:v,innerRef:this.getInputRef,onBlur:this.onInputBlur,onChange:f.j,onFocus:this.onInputFocus,disabled:t,tabIndex:l,inputMode:"none",form:c,value:""},y))}},{key:"renderPlaceholderOrValue",value:function(){var e=this,t=this.getComponents(),n=t.MultiValue,o=t.MultiValueContainer,i=t.MultiValueLabel,a=t.MultiValueRemove,l=t.SingleValue,c=t.Placeholder,u=this.commonProps,s=this.props,d=s.controlShouldRenderValue,f=s.isDisabled,m=s.isMulti,b=s.inputValue,h=s.placeholder,g=this.state,v=g.selectValue,y=g.focusedValue,O=g.isFocused;if(!this.hasValue()||!d)return b?null:p.createElement(c,Object(r.a)({},u,{key:"placeholder",isDisabled:f,isFocused:O,innerProps:{id:this.getElementId("placeholder")}}),h);if(m)return v.map((function(t,l){var c=t===y,s="".concat(e.getOptionLabel(t),"-").concat(e.getOptionValue(t));return p.createElement(n,Object(r.a)({},u,{components:{Container:o,Label:i,Remove:a},isFocused:c,isDisabled:f,key:s,index:l,removeProps:{onClick:function(){return e.removeValue(t)},onTouchEnd:function(){return e.removeValue(t)},onMouseDown:function(e){e.preventDefault()}},data:t}),e.formatOptionLabel(t,"value"))}));if(b)return null;var w=v[0];return p.createElement(l,Object(r.a)({},u,{data:w,isDisabled:f}),this.formatOptionLabel(w,"value"))}},{key:"renderClearIndicator",value:function(){var e=this.getComponents().ClearIndicator,t=this.commonProps,n=this.props,o=n.isDisabled,i=n.isLoading,a=this.state.isFocused;if(!this.isClearable()||!e||o||!this.hasValue()||i)return null;var l={onMouseDown:this.onClearIndicatorMouseDown,onTouchEnd:this.onClearIndicatorTouchEnd,"aria-hidden":"true"};return p.createElement(e,Object(r.a)({},t,{innerProps:l,isFocused:a}))}},{key:"renderLoadingIndicator",value:function(){var e=this.getComponents().LoadingIndicator,t=this.commonProps,n=this.props,o=n.isDisabled,i=n.isLoading,a=this.state.isFocused;if(!e||!i)return null;return p.createElement(e,Object(r.a)({},t,{innerProps:{"aria-hidden":"true"},isDisabled:o,isFocused:a}))}},{key:"renderIndicatorSeparator",value:function(){var e=this.getComponents(),t=e.DropdownIndicator,n=e.IndicatorSeparator;if(!t||!n)return null;var o=this.commonProps,i=this.props.isDisabled,a=this.state.isFocused;return p.createElement(n,Object(r.a)({},o,{isDisabled:i,isFocused:a}))}},{key:"renderDropdownIndicator",value:function(){var e=this.getComponents().DropdownIndicator;if(!e)return null;var t=this.commonProps,n=this.props.isDisabled,o=this.state.isFocused,i={onMouseDown:this.onDropdownIndicatorMouseDown,onTouchEnd:this.onDropdownIndicatorTouchEnd,"aria-hidden":"true"};return p.createElement(e,Object(r.a)({},t,{innerProps:i,isDisabled:n,isFocused:o}))}},{key:"renderMenu",value:function(){var e=this,t=this.getComponents(),n=t.Group,o=t.GroupHeading,i=t.Menu,a=t.MenuList,l=t.MenuPortal,c=t.LoadingMessage,u=t.NoOptionsMessage,s=t.Option,d=this.commonProps,m=this.state.focusedOption,b=this.props,h=b.captureMenuScroll,g=b.inputValue,v=b.isLoading,y=b.loadingMessage,O=b.minMenuHeight,w=b.maxMenuHeight,j=b.menuIsOpen,x=b.menuPlacement,F=b.menuPosition,C=b.menuPortalTarget,E=b.menuShouldBlockScroll,S=b.menuShouldScrollIntoView,P=b.noOptionsMessage,I=b.onMenuScrollToTop,k=b.onMenuScrollToBottom;if(!j)return null;var M,A=function(t,n){var o=t.type,i=t.data,a=t.isDisabled,l=t.isSelected,c=t.label,u=t.value,f=m===i,b=a?void 0:function(){return e.onOptionHover(i)},h=a?void 0:function(){return e.selectOption(i)},g="".concat(e.getElementId("option"),"-").concat(n),v={id:g,onClick:h,onMouseMove:b,onMouseOver:b,tabIndex:-1,role:"option","aria-selected":e.isAppleDevice?void 0:l};return p.createElement(s,Object(r.a)({},d,{innerProps:v,data:i,isDisabled:a,isSelected:l,key:g,label:c,type:o,value:u,isFocused:f,innerRef:f?e.getFocusedOptionRef:void 0}),e.formatOptionLabel(t.data,"menu"))};if(this.hasOptions())M=this.getCategorizedOptions().map((function(t){if("group"===t.type){var i=t.data,a=t.options,l=t.index,c="".concat(e.getElementId("group"),"-").concat(l),u="".concat(c,"-heading");return p.createElement(n,Object(r.a)({},d,{key:c,data:i,options:a,Heading:o,headingProps:{id:u,data:t.data},label:e.formatGroupLabel(t.data)}),t.options.map((function(e){return A(e,"".concat(l,"-").concat(e.index))})))}if("option"===t.type)return A(t,"".concat(t.index))}));else if(v){var T=y({inputValue:g});if(null===T)return null;M=p.createElement(c,d,T)}else{var R=P({inputValue:g});if(null===R)return null;M=p.createElement(u,d,R)}var V={minMenuHeight:O,maxMenuHeight:w,menuPlacement:x,menuPosition:F,menuShouldScrollIntoView:S},L=p.createElement(f.l,Object(r.a)({},d,V),(function(t){var n=t.ref,o=t.placerProps,l=o.placement,c=o.maxHeight;return p.createElement(i,Object(r.a)({},d,V,{innerRef:n,innerProps:{onMouseDown:e.onMenuMouseDown,onMouseMove:e.onMenuMouseMove},isLoading:v,placement:l}),p.createElement(W,{captureEnabled:h,onTopArrive:I,onBottomArrive:k,lockEnabled:E},(function(t){return p.createElement(a,Object(r.a)({},d,{innerRef:function(n){e.getMenuListRef(n),t(n)},innerProps:{role:"listbox","aria-multiselectable":d.isMulti,id:e.getElementId("listbox")},isLoading:v,maxHeight:c,focusedOption:m}),M)})))}));return C||"fixed"===F?p.createElement(l,Object(r.a)({},d,{appendTo:C,controlElement:this.controlRef,menuPlacement:x,menuPosition:F}),L):L}},{key:"renderFormField",value:function(){var e=this,t=this.props,n=t.delimiter,r=t.isDisabled,o=t.isMulti,i=t.name,a=t.required,l=this.state.selectValue;if(a&&!this.hasValue()&&!r)return p.createElement(q,{name:i,onFocus:this.onValueInputFocus});if(i&&!r){if(o){if(n){var c=l.map((function(t){return e.getOptionValue(t)})).join(n);return p.createElement("input",{name:i,type:"hidden",value:c})}var u=l.length>0?l.map((function(t,n){return p.createElement("input",{key:"i-".concat(n),name:i,type:"hidden",value:e.getOptionValue(t)})})):p.createElement("input",{name:i,type:"hidden",value:""});return p.createElement("div",null,u)}var s=l[0]?this.getOptionValue(l[0]):"";return p.createElement("input",{name:i,type:"hidden",value:s})}}},{key:"renderLiveRegion",value:function(){var e=this.commonProps,t=this.state,n=t.ariaSelection,o=t.focusedOption,i=t.focusedValue,a=t.isFocused,l=t.selectValue,c=this.getFocusableOptions();return p.createElement(O,Object(r.a)({},e,{id:this.getElementId("live-region"),ariaSelection:n,focusedOption:o,focusedValue:i,isFocused:a,selectValue:l,focusableOptions:c,isAppleDevice:this.isAppleDevice}))}},{key:"render",value:function(){var e=this.getComponents(),t=e.Control,n=e.IndicatorsContainer,o=e.SelectContainer,i=e.ValueContainer,a=this.props,l=a.className,c=a.id,u=a.isDisabled,s=a.menuIsOpen,d=this.state.isFocused,f=this.commonProps=this.getCommonProps();return p.createElement(o,Object(r.a)({},f,{className:l,innerProps:{id:c,onKeyDown:this.onKeyDown},isDisabled:u,isFocused:d}),this.renderLiveRegion(),p.createElement(t,Object(r.a)({},f,{innerRef:this.getControlRef,innerProps:{onMouseDown:this.onControlMouseDown,onTouchEnd:this.onControlTouchEnd},isDisabled:u,isFocused:d,menuIsOpen:s}),p.createElement(i,Object(r.a)({},f,{isDisabled:u}),this.renderPlaceholderOrValue(),this.renderInput()),p.createElement(n,Object(r.a)({},f,{isDisabled:u}),this.renderClearIndicator(),this.renderLoadingIndicator(),this.renderIndicatorSeparator(),this.renderDropdownIndicator())),this.renderMenu(),this.renderFormField())}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n=t.prevProps,r=t.clearFocusValueOnUpdate,i=t.inputIsHiddenAfterUpdate,a=t.ariaSelection,l=t.isFocused,c=t.prevWasFocused,u=t.instancePrefix,s=e.options,d=e.value,p=e.menuIsOpen,m=e.inputValue,b=e.isMulti,h=Object(f.h)(d),g={};if(n&&(d!==n.value||s!==n.options||p!==n.menuIsOpen||m!==n.inputValue)){var v=p?function(e,t){return oe(re(e,t))}(e,h):[],y=p?ie(re(e,h),"".concat(u,"-option")):[],O=r?function(e,t){var n=e.focusedValue,r=e.selectValue.indexOf(n);if(r>-1){if(t.indexOf(n)>-1)return n;if(r<t.length)return t[r]}return null}(t,h):null,w=function(e,t){var n=e.focusedOption;return n&&t.indexOf(n)>-1?n:t[0]}(t,v);g={selectValue:h,focusedOption:w,focusedOptionId:le(y,w),focusableOptionsWithIds:y,focusedValue:O,clearFocusValueOnUpdate:!1}}var j=null!=i&&e!==n?{inputIsHidden:i,inputIsHiddenAfterUpdate:void 0}:{},x=a,F=l&&c;return l&&!F&&(x={value:Object(f.d)(b,h,h[0]||null),options:h,action:"initial-input-focus"},F=!c),"initial-input-focus"===(null==a?void 0:a.action)&&(x=null),Object(o.a)(Object(o.a)(Object(o.a)({},g),j),{},{prevProps:e,ariaSelection:x,prevWasFocused:F})}}]),b}(p.Component);be.defaultProps=te},1485:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(167);function o(e,t){if(null==e)return{};var n,o,i=Object(r.a)(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],t.includes(n)||{}.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}},1486:function(e,t,n){"use strict";n.d(t,"a",(function(){return P})),n.d(t,"b",(function(){return L})),n.d(t,"c",(function(){return V})),n.d(t,"d",(function(){return R})),n.d(t,"e",(function(){return g})),n.d(t,"f",(function(){return Ve})),n.d(t,"g",(function(){return w})),n.d(t,"h",(function(){return v})),n.d(t,"i",(function(){return E})),n.d(t,"j",(function(){return b})),n.d(t,"k",(function(){return T})),n.d(t,"l",(function(){return $})),n.d(t,"m",(function(){return de})),n.d(t,"n",(function(){return Q})),n.d(t,"o",(function(){return Re})),n.d(t,"p",(function(){return he})),n.d(t,"q",(function(){return se})),n.d(t,"r",(function(){return Oe})),n.d(t,"s",(function(){return ye})),n.d(t,"t",(function(){return pe})),n.d(t,"u",(function(){return ee})),n.d(t,"v",(function(){return xe})),n.d(t,"w",(function(){return K})),n.d(t,"x",(function(){return me})),n.d(t,"y",(function(){return z})),n.d(t,"z",(function(){return G})),n.d(t,"A",(function(){return J})),n.d(t,"B",(function(){return Se})),n.d(t,"C",(function(){return Pe})),n.d(t,"D",(function(){return D})),n.d(t,"E",(function(){return A})),n.d(t,"F",(function(){return Ie})),n.d(t,"G",(function(){return X})),n.d(t,"H",(function(){return Me})),n.d(t,"I",(function(){return Ae})),n.d(t,"J",(function(){return Te})),n.d(t,"K",(function(){return Z})),n.d(t,"L",(function(){return S}));var r=n(342),o=n(142),i=n(1483),a=n(1517),l=n(1485),c=n(131);var u=n(291),s=n(0),d=n(116),p=n(1504),f=n(1501),m=["className","clearValue","cx","getStyles","getClassNames","getValue","hasValue","isMulti","isRtl","options","selectOption","selectProps","setValue","theme"],b=function(){};function h(e,t){return t?"-"===t[0]?e+t:e+"__"+t:e}function g(e,t){for(var n=arguments.length,r=new Array(n>2?n-2:0),o=2;o<n;o++)r[o-2]=arguments[o];var i=[].concat(r);if(t&&e)for(var a in t)t.hasOwnProperty(a)&&t[a]&&i.push("".concat(h(e,a)));return i.filter((function(e){return e})).map((function(e){return String(e).trim()})).join(" ")}var v=function(e){return t=e,Array.isArray(t)?e.filter(Boolean):"object"===Object(c.a)(e)&&null!==e?[e]:[];var t},y=function(e){e.className,e.clearValue,e.cx,e.getStyles,e.getClassNames,e.getValue,e.hasValue,e.isMulti,e.isRtl,e.options,e.selectOption,e.selectProps,e.setValue,e.theme;var t=Object(l.a)(e,m);return Object(r.a)({},t)},O=function(e,t,n){var r=e.cx,o=e.getStyles,i=e.getClassNames,a=e.className;return{css:o(t,e),className:r(null!=n?n:{},i(t,e),a)}};function w(e){return[document.documentElement,document.body,window].indexOf(e)>-1}function j(e){return w(e)?window.pageYOffset:e.scrollTop}function x(e,t){w(e)?window.scrollTo(0,t):e.scrollTop=t}function F(e,t,n,r){return n*((e=e/r-1)*e*e+1)+t}function C(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:200,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:b,o=j(e),i=t-o,a=10,l=0;function c(){var t=F(l+=a,o,i,n);x(e,t),l<n?window.requestAnimationFrame(c):r(e)}c()}function E(e,t){var n=e.getBoundingClientRect(),r=t.getBoundingClientRect(),o=t.offsetHeight/3;r.bottom+o>n.bottom?x(e,Math.min(t.offsetTop+t.clientHeight-e.offsetHeight+o,e.scrollHeight)):r.top-o<n.top&&x(e,Math.max(t.offsetTop-o,0))}function S(){try{return document.createEvent("TouchEvent"),!0}catch(e){return!1}}function P(){try{return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)}catch(e){return!1}}var I=!1,k={get passive(){return I=!0}},M="undefined"!=typeof window?window:{};M.addEventListener&&M.removeEventListener&&(M.addEventListener("p",b,k),M.removeEventListener("p",b,!1));var A=I;function T(e){return null!=e}function R(e,t,n){return e?t:n}function V(e){return e}function L(e){return e}var D=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var o=Object.entries(e).filter((function(e){var t=Object(a.a)(e,1)[0];return!n.includes(t)}));return o.reduce((function(e,t){var n=Object(a.a)(t,2),r=n[0],o=n[1];return e[r]=o,e}),{})},N=["children","innerProps"],H=["children","innerProps"];function _(e){var t=e.maxHeight,n=e.menuEl,r=e.minHeight,o=e.placement,i=e.shouldScroll,a=e.isFixedPosition,l=e.controlHeight,c=function(e){var t=getComputedStyle(e),n="absolute"===t.position,r=/(auto|scroll)/;if("fixed"===t.position)return document.documentElement;for(var o=e;o=o.parentElement;)if(t=getComputedStyle(o),(!n||"static"!==t.position)&&r.test(t.overflow+t.overflowY+t.overflowX))return o;return document.documentElement}(n),u={placement:"bottom",maxHeight:t};if(!n||!n.offsetParent)return u;var s,d=c.getBoundingClientRect().height,p=n.getBoundingClientRect(),f=p.bottom,m=p.height,b=p.top,h=n.offsetParent.getBoundingClientRect().top,g=a?window.innerHeight:w(s=c)?window.innerHeight:s.clientHeight,v=j(c),y=parseInt(getComputedStyle(n).marginBottom,10),O=parseInt(getComputedStyle(n).marginTop,10),F=h-O,E=g-b,S=F+v,P=d-v-b,I=f-g+v+y,k=v+b-O;switch(o){case"auto":case"bottom":if(E>=m)return{placement:"bottom",maxHeight:t};if(P>=m&&!a)return i&&C(c,I,160),{placement:"bottom",maxHeight:t};if(!a&&P>=r||a&&E>=r)return i&&C(c,I,160),{placement:"bottom",maxHeight:a?E-y:P-y};if("auto"===o||a){var M=t,A=a?F:S;return A>=r&&(M=Math.min(A-y-l,t)),{placement:"top",maxHeight:M}}if("bottom"===o)return i&&x(c,I),{placement:"bottom",maxHeight:t};break;case"top":if(F>=m)return{placement:"top",maxHeight:t};if(S>=m&&!a)return i&&C(c,k,160),{placement:"top",maxHeight:t};if(!a&&S>=r||a&&F>=r){var T=t;return(!a&&S>=r||a&&F>=r)&&(T=a?F-O:S-O),i&&C(c,k,160),{placement:"top",maxHeight:T}}return{placement:"bottom",maxHeight:t};default:throw new Error('Invalid placement provided "'.concat(o,'".'))}return u}var U,B=function(e){return"auto"===e?"bottom":e},z=function(e,t){var n,o=e.placement,i=e.theme,a=i.borderRadius,l=i.spacing,c=i.colors;return Object(r.a)((n={label:"menu"},Object(u.a)(n,function(e){return e?{bottom:"top",top:"bottom"}[e]:"bottom"}(o),"100%"),Object(u.a)(n,"position","absolute"),Object(u.a)(n,"width","100%"),Object(u.a)(n,"zIndex",1),n),t?{}:{backgroundColor:c.neutral0,borderRadius:a,boxShadow:"0 0 0 1px hsla(0, 0%, 0%, 0.1), 0 4px 11px hsla(0, 0%, 0%, 0.1)",marginBottom:l.menuGutter,marginTop:l.menuGutter})},W=Object(s.createContext)(null),$=function(e){var t=e.children,n=e.minMenuHeight,o=e.maxMenuHeight,i=e.menuPlacement,l=e.menuPosition,c=e.menuShouldScrollIntoView,u=e.theme,d=(Object(s.useContext)(W)||{}).setPortalPlacement,p=Object(s.useRef)(null),m=Object(s.useState)(o),b=Object(a.a)(m,2),h=b[0],g=b[1],v=Object(s.useState)(null),y=Object(a.a)(v,2),O=y[0],w=y[1],j=u.spacing.controlHeight;return Object(f.a)((function(){var e=p.current;if(e){var t="fixed"===l,r=_({maxHeight:o,menuEl:e,minHeight:n,placement:i,shouldScroll:c&&!t,isFixedPosition:t,controlHeight:j});g(r.maxHeight),w(r.placement),null==d||d(r.placement)}}),[o,i,l,c,n,d,j]),t({ref:p,placerProps:Object(r.a)(Object(r.a)({},e),{},{placement:O||B(i),maxHeight:h})})},q=function(e){var t=e.children,n=e.innerRef,r=e.innerProps;return Object(i.b)("div",Object(o.a)({},O(e,"menu",{menu:!0}),{ref:n},r),t)},G=function(e,t){var n=e.maxHeight,o=e.theme.spacing.baseUnit;return Object(r.a)({maxHeight:n,overflowY:"auto",position:"relative",WebkitOverflowScrolling:"touch"},t?{}:{paddingBottom:o,paddingTop:o})},Y=function(e,t){var n=e.theme,o=n.spacing.baseUnit,i=n.colors;return Object(r.a)({textAlign:"center"},t?{}:{color:i.neutral40,padding:"".concat(2*o,"px ").concat(3*o,"px")})},X=Y,K=Y,J=function(e){var t=e.rect,n=e.offset,r=e.position;return{left:t.left,position:r,top:n,width:t.width,zIndex:1}},Q=function(e){var t=e.isDisabled;return{label:"container",direction:e.isRtl?"rtl":void 0,pointerEvents:t?"none":void 0,position:"relative"}},Z=function(e,t){var n=e.theme.spacing,o=e.isMulti,i=e.hasValue,a=e.selectProps.controlShouldRenderValue;return Object(r.a)({alignItems:"center",display:o&&i&&a?"flex":"grid",flex:1,flexWrap:"wrap",WebkitOverflowScrolling:"touch",position:"relative",overflow:"hidden"},t?{}:{padding:"".concat(n.baseUnit/2,"px ").concat(2*n.baseUnit,"px")})},ee=function(){return{alignItems:"center",alignSelf:"stretch",display:"flex",flexShrink:0}},te=["size"],ne=["innerProps","isRtl","size"];var re,oe,ie={name:"8mmkcg",styles:"display:inline-block;fill:currentColor;line-height:1;stroke:currentColor;stroke-width:0"},ae=function(e){var t=e.size,n=Object(l.a)(e,te);return Object(i.b)("svg",Object(o.a)({height:t,width:t,viewBox:"0 0 20 20","aria-hidden":"true",focusable:"false",css:ie},n))},le=function(e){return Object(i.b)(ae,Object(o.a)({size:20},e),Object(i.b)("path",{d:"M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"}))},ce=function(e){return Object(i.b)(ae,Object(o.a)({size:20},e),Object(i.b)("path",{d:"M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"}))},ue=function(e,t){var n=e.isFocused,o=e.theme,i=o.spacing.baseUnit,a=o.colors;return Object(r.a)({label:"indicatorContainer",display:"flex",transition:"color 150ms"},t?{}:{color:n?a.neutral60:a.neutral20,padding:2*i,":hover":{color:n?a.neutral80:a.neutral40}})},se=ue,de=ue,pe=function(e,t){var n=e.isDisabled,o=e.theme,i=o.spacing.baseUnit,a=o.colors;return Object(r.a)({label:"indicatorSeparator",alignSelf:"stretch",width:1},t?{}:{backgroundColor:n?a.neutral10:a.neutral20,marginBottom:2*i,marginTop:2*i})},fe=Object(i.c)(U||(re=["\n  0%, 80%, 100% { opacity: 0; }\n  40% { opacity: 1; }\n"],oe||(oe=re.slice(0)),U=Object.freeze(Object.defineProperties(re,{raw:{value:Object.freeze(oe)}})))),me=function(e,t){var n=e.isFocused,o=e.size,i=e.theme,a=i.colors,l=i.spacing.baseUnit;return Object(r.a)({label:"loadingIndicator",display:"flex",transition:"color 150ms",alignSelf:"center",fontSize:o,lineHeight:1,marginRight:o,textAlign:"center",verticalAlign:"middle"},t?{}:{color:n?a.neutral60:a.neutral20,padding:2*l})},be=function(e){var t=e.delay,n=e.offset;return Object(i.b)("span",{css:Object(i.a)({animation:"".concat(fe," 1s ease-in-out ").concat(t,"ms infinite;"),backgroundColor:"currentColor",borderRadius:"1em",display:"inline-block",marginLeft:n?"1em":void 0,height:"1em",verticalAlign:"top",width:"1em"},"","")})},he=function(e,t){var n=e.isDisabled,o=e.isFocused,i=e.theme,a=i.colors,l=i.borderRadius,c=i.spacing;return Object(r.a)({label:"control",alignItems:"center",cursor:"default",display:"flex",flexWrap:"wrap",justifyContent:"space-between",minHeight:c.controlHeight,outline:"0 !important",position:"relative",transition:"all 100ms"},t?{}:{backgroundColor:n?a.neutral5:a.neutral0,borderColor:n?a.neutral10:o?a.primary:a.neutral20,borderRadius:l,borderStyle:"solid",borderWidth:1,boxShadow:o?"0 0 0 1px ".concat(a.primary):void 0,"&:hover":{borderColor:o?a.primary:a.neutral30}})},ge=function(e){var t=e.children,n=e.isDisabled,r=e.isFocused,a=e.innerRef,l=e.innerProps,c=e.menuIsOpen;return Object(i.b)("div",Object(o.a)({ref:a},O(e,"control",{control:!0,"control--is-disabled":n,"control--is-focused":r,"control--menu-is-open":c}),l,{"aria-disabled":n||void 0}),t)},ve=["data"],ye=function(e,t){var n=e.theme.spacing;return t?{}:{paddingBottom:2*n.baseUnit,paddingTop:2*n.baseUnit}},Oe=function(e,t){var n=e.theme,o=n.colors,i=n.spacing;return Object(r.a)({label:"group",cursor:"default",display:"block"},t?{}:{color:o.neutral40,fontSize:"75%",fontWeight:500,marginBottom:"0.25em",paddingLeft:3*i.baseUnit,paddingRight:3*i.baseUnit,textTransform:"uppercase"})},we=function(e){var t=e.children,n=e.cx,r=e.getStyles,a=e.getClassNames,l=e.Heading,c=e.headingProps,u=e.innerProps,s=e.label,d=e.theme,p=e.selectProps;return Object(i.b)("div",Object(o.a)({},O(e,"group",{group:!0}),u),Object(i.b)(l,Object(o.a)({},c,{selectProps:p,theme:d,getStyles:r,getClassNames:a,cx:n}),s),Object(i.b)("div",null,t))},je=["innerRef","isDisabled","isHidden","inputClassName"],xe=function(e,t){var n=e.isDisabled,o=e.value,i=e.theme,a=i.spacing,l=i.colors;return Object(r.a)(Object(r.a)({visibility:n?"hidden":"visible",transform:o?"translateZ(0)":""},Ce),t?{}:{margin:a.baseUnit/2,paddingBottom:a.baseUnit/2,paddingTop:a.baseUnit/2,color:l.neutral80})},Fe={gridArea:"1 / 2",font:"inherit",minWidth:"2px",border:0,margin:0,outline:0,padding:0},Ce={flex:"1 1 auto",display:"inline-grid",gridArea:"1 / 1 / 2 / 3",gridTemplateColumns:"0 min-content","&:after":Object(r.a)({content:'attr(data-value) " "',visibility:"hidden",whiteSpace:"pre"},Fe)},Ee=function(e){return Object(r.a)({label:"input",color:"inherit",background:0,opacity:e?0:1,width:"100%"},Fe)},Se=function(e,t){var n=e.theme,o=n.spacing,i=n.borderRadius,a=n.colors;return Object(r.a)({label:"multiValue",display:"flex",minWidth:0},t?{}:{backgroundColor:a.neutral10,borderRadius:i/2,margin:o.baseUnit/2})},Pe=function(e,t){var n=e.theme,o=n.borderRadius,i=n.colors,a=e.cropWithEllipsis;return Object(r.a)({overflow:"hidden",textOverflow:a||void 0===a?"ellipsis":void 0,whiteSpace:"nowrap"},t?{}:{borderRadius:o/2,color:i.neutral80,fontSize:"85%",padding:3,paddingLeft:6})},Ie=function(e,t){var n=e.theme,o=n.spacing,i=n.borderRadius,a=n.colors,l=e.isFocused;return Object(r.a)({alignItems:"center",display:"flex"},t?{}:{borderRadius:i/2,backgroundColor:l?a.dangerLight:void 0,paddingLeft:o.baseUnit,paddingRight:o.baseUnit,":hover":{backgroundColor:a.dangerLight,color:a.danger}})},ke=function(e){var t=e.children,n=e.innerProps;return Object(i.b)("div",n,t)};var Me=function(e,t){var n=e.isDisabled,o=e.isFocused,i=e.isSelected,a=e.theme,l=a.spacing,c=a.colors;return Object(r.a)({label:"option",cursor:"default",display:"block",fontSize:"inherit",width:"100%",userSelect:"none",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)"},t?{}:{backgroundColor:i?c.primary:o?c.primary25:"transparent",color:n?c.neutral20:i?c.neutral0:"inherit",padding:"".concat(2*l.baseUnit,"px ").concat(3*l.baseUnit,"px"),":active":{backgroundColor:n?void 0:i?c.primary:c.primary50}})},Ae=function(e,t){var n=e.theme,o=n.spacing,i=n.colors;return Object(r.a)({label:"placeholder",gridArea:"1 / 1 / 2 / 3"},t?{}:{color:i.neutral50,marginLeft:o.baseUnit/2,marginRight:o.baseUnit/2})},Te=function(e,t){var n=e.isDisabled,o=e.theme,i=o.spacing,a=o.colors;return Object(r.a)({label:"singleValue",gridArea:"1 / 1 / 2 / 3",maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},t?{}:{color:n?a.neutral40:a.neutral80,marginLeft:i.baseUnit/2,marginRight:i.baseUnit/2})},Re={ClearIndicator:function(e){var t=e.children,n=e.innerProps;return Object(i.b)("div",Object(o.a)({},O(e,"clearIndicator",{indicator:!0,"clear-indicator":!0}),n),t||Object(i.b)(le,null))},Control:ge,DropdownIndicator:function(e){var t=e.children,n=e.innerProps;return Object(i.b)("div",Object(o.a)({},O(e,"dropdownIndicator",{indicator:!0,"dropdown-indicator":!0}),n),t||Object(i.b)(ce,null))},DownChevron:ce,CrossIcon:le,Group:we,GroupHeading:function(e){var t=y(e);t.data;var n=Object(l.a)(t,ve);return Object(i.b)("div",Object(o.a)({},O(e,"groupHeading",{"group-heading":!0}),n))},IndicatorsContainer:function(e){var t=e.children,n=e.innerProps;return Object(i.b)("div",Object(o.a)({},O(e,"indicatorsContainer",{indicators:!0}),n),t)},IndicatorSeparator:function(e){var t=e.innerProps;return Object(i.b)("span",Object(o.a)({},t,O(e,"indicatorSeparator",{"indicator-separator":!0})))},Input:function(e){var t=e.cx,n=e.value,r=y(e),a=r.innerRef,c=r.isDisabled,u=r.isHidden,s=r.inputClassName,d=Object(l.a)(r,je);return Object(i.b)("div",Object(o.a)({},O(e,"input",{"input-container":!0}),{"data-value":n||""}),Object(i.b)("input",Object(o.a)({className:t({input:!0},s),ref:a,style:Ee(u),disabled:c},d)))},LoadingIndicator:function(e){var t=e.innerProps,n=e.isRtl,a=e.size,c=void 0===a?4:a,u=Object(l.a)(e,ne);return Object(i.b)("div",Object(o.a)({},O(Object(r.a)(Object(r.a)({},u),{},{innerProps:t,isRtl:n,size:c}),"loadingIndicator",{indicator:!0,"loading-indicator":!0}),t),Object(i.b)(be,{delay:0,offset:n}),Object(i.b)(be,{delay:160,offset:!0}),Object(i.b)(be,{delay:320,offset:!n}))},Menu:q,MenuList:function(e){var t=e.children,n=e.innerProps,r=e.innerRef,a=e.isMulti;return Object(i.b)("div",Object(o.a)({},O(e,"menuList",{"menu-list":!0,"menu-list--is-multi":a}),{ref:r},n),t)},MenuPortal:function(e){var t=e.appendTo,n=e.children,l=e.controlElement,c=e.innerProps,u=e.menuPlacement,m=e.menuPosition,b=Object(s.useRef)(null),h=Object(s.useRef)(null),g=Object(s.useState)(B(u)),v=Object(a.a)(g,2),y=v[0],w=v[1],j=Object(s.useMemo)((function(){return{setPortalPlacement:w}}),[]),x=Object(s.useState)(null),F=Object(a.a)(x,2),C=F[0],E=F[1],S=Object(s.useCallback)((function(){if(l){var e=function(e){var t=e.getBoundingClientRect();return{bottom:t.bottom,height:t.height,left:t.left,right:t.right,top:t.top,width:t.width}}(l),t="fixed"===m?0:window.pageYOffset,n=e[y]+t;n===(null==C?void 0:C.offset)&&e.left===(null==C?void 0:C.rect.left)&&e.width===(null==C?void 0:C.rect.width)||E({offset:n,rect:e})}}),[l,m,y,null==C?void 0:C.offset,null==C?void 0:C.rect.left,null==C?void 0:C.rect.width]);Object(f.a)((function(){S()}),[S]);var P=Object(s.useCallback)((function(){"function"==typeof h.current&&(h.current(),h.current=null),l&&b.current&&(h.current=Object(p.a)(l,b.current,S,{elementResize:"ResizeObserver"in window}))}),[l,S]);Object(f.a)((function(){P()}),[P]);var I=Object(s.useCallback)((function(e){b.current=e,P()}),[P]);if(!t&&"fixed"!==m||!C)return null;var k=Object(i.b)("div",Object(o.a)({ref:I},O(Object(r.a)(Object(r.a)({},e),{},{offset:C.offset,position:m,rect:C.rect}),"menuPortal",{"menu-portal":!0}),c),n);return Object(i.b)(W.Provider,{value:j},t?Object(d.createPortal)(k,t):k)},LoadingMessage:function(e){var t=e.children,n=void 0===t?"Loading...":t,a=e.innerProps,c=Object(l.a)(e,H);return Object(i.b)("div",Object(o.a)({},O(Object(r.a)(Object(r.a)({},c),{},{children:n,innerProps:a}),"loadingMessage",{"menu-notice":!0,"menu-notice--loading":!0}),a),n)},NoOptionsMessage:function(e){var t=e.children,n=void 0===t?"No options":t,a=e.innerProps,c=Object(l.a)(e,N);return Object(i.b)("div",Object(o.a)({},O(Object(r.a)(Object(r.a)({},c),{},{children:n,innerProps:a}),"noOptionsMessage",{"menu-notice":!0,"menu-notice--no-options":!0}),a),n)},MultiValue:function(e){var t=e.children,n=e.components,o=e.data,a=e.innerProps,l=e.isDisabled,c=e.removeProps,u=e.selectProps,s=n.Container,d=n.Label,p=n.Remove;return Object(i.b)(s,{data:o,innerProps:Object(r.a)(Object(r.a)({},O(e,"multiValue",{"multi-value":!0,"multi-value--is-disabled":l})),a),selectProps:u},Object(i.b)(d,{data:o,innerProps:Object(r.a)({},O(e,"multiValueLabel",{"multi-value__label":!0})),selectProps:u},t),Object(i.b)(p,{data:o,innerProps:Object(r.a)(Object(r.a)({},O(e,"multiValueRemove",{"multi-value__remove":!0})),{},{"aria-label":"Remove ".concat(t||"option")},c),selectProps:u}))},MultiValueContainer:ke,MultiValueLabel:ke,MultiValueRemove:function(e){var t=e.children,n=e.innerProps;return Object(i.b)("div",Object(o.a)({role:"button"},n),t||Object(i.b)(le,{size:14}))},Option:function(e){var t=e.children,n=e.isDisabled,r=e.isFocused,a=e.isSelected,l=e.innerRef,c=e.innerProps;return Object(i.b)("div",Object(o.a)({},O(e,"option",{option:!0,"option--is-disabled":n,"option--is-focused":r,"option--is-selected":a}),{ref:l,"aria-disabled":n},c),t)},Placeholder:function(e){var t=e.children,n=e.innerProps;return Object(i.b)("div",Object(o.a)({},O(e,"placeholder",{placeholder:!0}),n),t)},SelectContainer:function(e){var t=e.children,n=e.innerProps,r=e.isDisabled,a=e.isRtl;return Object(i.b)("div",Object(o.a)({},O(e,"container",{"--is-disabled":r,"--is-rtl":a}),n),t)},SingleValue:function(e){var t=e.children,n=e.isDisabled,r=e.innerProps;return Object(i.b)("div",Object(o.a)({},O(e,"singleValue",{"single-value":!0,"single-value--is-disabled":n}),r),t)},ValueContainer:function(e){var t=e.children,n=e.innerProps,r=e.isMulti,a=e.hasValue;return Object(i.b)("div",Object(o.a)({},O(e,"valueContainer",{"value-container":!0,"value-container--is-multi":r,"value-container--has-value":a}),n),t)}},Ve=function(e){return Object(r.a)(Object(r.a)({},Re),e.components)}},1491:function(e,t,n){"use strict";n.d(t,"a",(function(){return ue}));var r=function(){function e(e){var t=this;this._insertTag=function(e){var n;n=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,n),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)==0&&this._insertTag(function(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),void 0!==e.nonce&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}(this));var t=this.tags[this.tags.length-1];if(this.isSpeedy){var n=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(t);try{n.insertRule(e,n.cssRules.length)}catch(e){}}else t.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach((function(e){var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)})),this.tags=[],this.ctr=0},e}(),o="-ms-",i="-moz-",a="-webkit-",l="comm",c="rule",u="decl",s="@keyframes",d=Math.abs,p=String.fromCharCode,f=Object.assign;function m(e,t){return 45^y(e,0)?(((t<<2^y(e,0))<<2^y(e,1))<<2^y(e,2))<<2^y(e,3):0}function b(e){return e.trim()}function h(e,t){return(e=t.exec(e))?e[0]:e}function g(e,t,n){return e.replace(t,n)}function v(e,t){return e.indexOf(t)}function y(e,t){return 0|e.charCodeAt(t)}function O(e,t,n){return e.slice(t,n)}function w(e){return e.length}function j(e){return e.length}function x(e,t){return t.push(e),e}function F(e,t){return e.map(t).join("")}var C=1,E=1,S=0,P=0,I=0,k="";function M(e,t,n,r,o,i,a){return{value:e,root:t,parent:n,type:r,props:o,children:i,line:C,column:E,length:a,return:""}}function A(e,t){return f(M("",null,null,"",null,null,0),e,{length:-e.length},t)}function T(){return I=P<S?y(k,P++):0,E++,10===I&&(E=1,C++),I}function R(){return y(k,P)}function V(){return P}function L(e,t){return O(k,e,t)}function D(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function N(e){return C=E=1,S=w(k=e),P=0,[]}function H(e){return k="",e}function _(e){return b(L(P-1,function e(t){for(;T();)switch(I){case t:return P;case 34:case 39:34!==t&&39!==t&&e(I);break;case 40:41===t&&e(t);break;case 92:T()}return P}(91===e?e+2:40===e?e+1:e)))}function U(e){for(;(I=R())&&I<33;)T();return D(e)>2||D(I)>3?"":" "}function B(e,t){for(;--t&&T()&&!(I<48||I>102||I>57&&I<65||I>70&&I<97););return L(e,V()+(t<6&&32==R()&&32==T()))}function z(e,t){for(;T()&&e+I!==57&&(e+I!==84||47!==R()););return"/*"+L(t,P-1)+"*"+p(47===e?e:T())}function W(e){for(;!D(R());)T();return L(e,P)}function $(e){return H(function e(t,n,r,o,i,a,l,c,u){var s=0,d=0,f=l,m=0,b=0,h=0,O=1,j=1,F=1,S=0,M="",A=i,L=a,D=o,N=M;for(;j;)switch(h=S,S=T()){case 40:if(108!=h&&58==y(N,f-1)){-1!=v(N+=g(_(S),"&","&\f"),"&\f")&&(F=-1);break}case 34:case 39:case 91:N+=_(S);break;case 9:case 10:case 13:case 32:N+=U(h);break;case 92:N+=B(V()-1,7);continue;case 47:switch(R()){case 42:case 47:x(G(z(T(),V()),n,r),u);break;default:N+="/"}break;case 123*O:c[s++]=w(N)*F;case 125*O:case 59:case 0:switch(S){case 0:case 125:j=0;case 59+d:-1==F&&(N=g(N,/\f/g,"")),b>0&&w(N)-f&&x(b>32?Y(N+";",o,r,f-1):Y(g(N," ","")+";",o,r,f-2),u);break;case 59:N+=";";default:if(x(D=q(N,n,r,s,d,i,c,M,A=[],L=[],f),a),123===S)if(0===d)e(N,n,D,D,A,a,f,c,L);else switch(99===m&&110===y(N,3)?100:m){case 100:case 108:case 109:case 115:e(t,D,D,o&&x(q(t,D,D,0,0,i,c,M,i,A=[],f),L),i,L,f,c,o?A:L);break;default:e(N,D,D,D,[""],L,0,c,L)}}s=d=b=0,O=F=1,M=N="",f=l;break;case 58:f=1+w(N),b=h;default:if(O<1)if(123==S)--O;else if(125==S&&0==O++&&125==(I=P>0?y(k,--P):0,E--,10===I&&(E=1,C--),I))continue;switch(N+=p(S),S*O){case 38:F=d>0?1:(N+="\f",-1);break;case 44:c[s++]=(w(N)-1)*F,F=1;break;case 64:45===R()&&(N+=_(T())),m=R(),d=f=w(M=N+=W(V())),S++;break;case 45:45===h&&2==w(N)&&(O=0)}}return a}("",null,null,null,[""],e=N(e),0,[0],e))}function q(e,t,n,r,o,i,a,l,u,s,p){for(var f=o-1,m=0===o?i:[""],h=j(m),v=0,y=0,w=0;v<r;++v)for(var x=0,F=O(e,f+1,f=d(y=a[v])),C=e;x<h;++x)(C=b(y>0?m[x]+" "+F:g(F,/&\f/g,m[x])))&&(u[w++]=C);return M(e,t,n,0===o?c:l,u,s,p)}function G(e,t,n){return M(e,t,n,l,p(I),O(e,2,-2),0)}function Y(e,t,n,r){return M(e,t,n,u,O(e,0,r),O(e,r+1,-1),r)}function X(e,t){for(var n="",r=j(e),o=0;o<r;o++)n+=t(e[o],o,e,t)||"";return n}function K(e,t,n,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case u:return e.return=e.return||e.value;case l:return"";case s:return e.return=e.value+"{"+X(e.children,r)+"}";case c:e.value=e.props.join(",")}return w(n=X(e.children,r))?e.return=e.value+"{"+n+"}":""}function J(e){var t=j(e);return function(n,r,o,i){for(var a="",l=0;l<t;l++)a+=e[l](n,r,o,i)||"";return a}}function Q(e){return function(t){t.root||(t=t.return)&&e(t)}}var Z=n(1492),ee=n(1511),te="undefined"!=typeof document,ne=function(e,t,n){for(var r=0,o=0;r=o,o=R(),38===r&&12===o&&(t[n]=1),!D(o);)T();return L(e,P)},re=function(e,t){return H(function(e,t){var n=-1,r=44;do{switch(D(r)){case 0:38===r&&12===R()&&(t[n]=1),e[n]+=ne(P-1,t,n);break;case 2:e[n]+=_(r);break;case 4:if(44===r){e[++n]=58===R()?"&\f":"",t[n]=e[n].length;break}default:e[n]+=p(r)}}while(r=T());return e}(N(e),t))},oe=new WeakMap,ie=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,n=e.parent,r=e.column===n.column&&e.line===n.line;"rule"!==n.type;)if(!(n=n.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||oe.get(n))&&!r){oe.set(e,!0);for(var o=[],i=re(t,o),a=n.props,l=0,c=0;l<i.length;l++)for(var u=0;u<a.length;u++,c++)e.props[c]=o[l]?i[l].replace(/&\f/g,a[u]):a[u]+" "+i[l]}}},ae=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}};var le=te?void 0:Object(Z.a)((function(){return Object(ee.a)((function(){var e={};return function(t){return e[t]}}))})),ce=[function(e,t,n,r){if(e.length>-1&&!e.return)switch(e.type){case u:e.return=function e(t,n){switch(m(t,n)){case 5103:return a+"print-"+t+t;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return a+t+t;case 5349:case 4246:case 4810:case 6968:case 2756:return a+t+i+t+o+t+t;case 6828:case 4268:return a+t+o+t+t;case 6165:return a+t+o+"flex-"+t+t;case 5187:return a+t+g(t,/(\w+).+(:[^]+)/,a+"box-$1$2"+o+"flex-$1$2")+t;case 5443:return a+t+o+"flex-item-"+g(t,/flex-|-self/,"")+t;case 4675:return a+t+o+"flex-line-pack"+g(t,/align-content|flex-|-self/,"")+t;case 5548:return a+t+o+g(t,"shrink","negative")+t;case 5292:return a+t+o+g(t,"basis","preferred-size")+t;case 6060:return a+"box-"+g(t,"-grow","")+a+t+o+g(t,"grow","positive")+t;case 4554:return a+g(t,/([^-])(transform)/g,"$1"+a+"$2")+t;case 6187:return g(g(g(t,/(zoom-|grab)/,a+"$1"),/(image-set)/,a+"$1"),t,"")+t;case 5495:case 3959:return g(t,/(image-set\([^]*)/,a+"$1$`$1");case 4968:return g(g(t,/(.+:)(flex-)?(.*)/,a+"box-pack:$3"+o+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+a+t+t;case 4095:case 3583:case 4068:case 2532:return g(t,/(.+)-inline(.+)/,a+"$1$2")+t;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(w(t)-1-n>6)switch(y(t,n+1)){case 109:if(45!==y(t,n+4))break;case 102:return g(t,/(.+:)(.+)-([^]+)/,"$1"+a+"$2-$3$1"+i+(108==y(t,n+3)?"$3":"$2-$3"))+t;case 115:return~v(t,"stretch")?e(g(t,"stretch","fill-available"),n)+t:t}break;case 4949:if(115!==y(t,n+1))break;case 6444:switch(y(t,w(t)-3-(~v(t,"!important")&&10))){case 107:return g(t,":",":"+a)+t;case 101:return g(t,/(.+:)([^;!]+)(;|!.+)?/,"$1"+a+(45===y(t,14)?"inline-":"")+"box$3$1"+a+"$2$3$1"+o+"$2box$3")+t}break;case 5936:switch(y(t,n+11)){case 114:return a+t+o+g(t,/[svh]\w+-[tblr]{2}/,"tb")+t;case 108:return a+t+o+g(t,/[svh]\w+-[tblr]{2}/,"tb-rl")+t;case 45:return a+t+o+g(t,/[svh]\w+-[tblr]{2}/,"lr")+t}return a+t+o+t+t}return t}(e.value,e.length);break;case s:return X([A(e,{value:g(e.value,"@","@"+a)})],r);case c:if(e.length)return F(e.props,(function(t){switch(h(t,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return X([A(e,{props:[g(t,/:(read-\w+)/,":-moz-$1")]})],r);case"::placeholder":return X([A(e,{props:[g(t,/:(plac\w+)/,":"+a+"input-$1")]}),A(e,{props:[g(t,/:(plac\w+)/,":-moz-$1")]}),A(e,{props:[g(t,/:(plac\w+)/,o+"input-$1")]})],r)}return""}))}}],ue=function(e){var t=e.key;if(te&&"css"===t){var n=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(n,(function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))}))}var o,i,a=e.stylisPlugins||ce,l={},c=[];te&&(o=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+t+' "]'),(function(e){for(var t=e.getAttribute("data-emotion").split(" "),n=1;n<t.length;n++)l[t[n]]=!0;c.push(e)})));var u=[ie,ae];if(te){var s,d=[K,Q((function(e){s.insert(e)}))],p=J(u.concat(a,d));i=function(e,t,n,r){s=n,X($(e?e+"{"+t.styles+"}":t.styles),p),r&&(g.inserted[t.name]=!0)}}else{var f=[K],m=J(u.concat(a,f)),b=le(a)(t),h=function(e,t){var n=t.name;return void 0===b[n]&&(b[n]=X($(e?e+"{"+t.styles+"}":t.styles),m)),b[n]};i=function(e,t,n,r){var o=t.name,i=h(e,t);return void 0===g.compat?(r&&(g.inserted[o]=!0),i):r?void(g.inserted[o]=i):i}}var g={key:t,sheet:new r({key:t,container:o,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:l,registered:{},insert:i};return g.sheet.hydrate(c),g}},1492:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var r=function(e){var t=new WeakMap;return function(n){if(t.has(n))return t.get(n);var r=e(n);return t.set(n,r),r}}},1501:function(e,t,n){"use strict";var r=n(0),o=r.useLayoutEffect;t.a=o},1502:function(e,t,n){"use strict";n.d(t,"a",(function(){return c}));var r=n(342),o=n(1517),i=n(1485),a=n(0),l=["defaultInputValue","defaultMenuIsOpen","defaultValue","inputValue","menuIsOpen","onChange","onInputChange","onMenuClose","onMenuOpen","value"];function c(e){var t=e.defaultInputValue,n=void 0===t?"":t,c=e.defaultMenuIsOpen,u=void 0!==c&&c,s=e.defaultValue,d=void 0===s?null:s,p=e.inputValue,f=e.menuIsOpen,m=e.onChange,b=e.onInputChange,h=e.onMenuClose,g=e.onMenuOpen,v=e.value,y=Object(i.a)(e,l),O=Object(a.useState)(void 0!==p?p:n),w=Object(o.a)(O,2),j=w[0],x=w[1],F=Object(a.useState)(void 0!==f?f:u),C=Object(o.a)(F,2),E=C[0],S=C[1],P=Object(a.useState)(void 0!==v?v:d),I=Object(o.a)(P,2),k=I[0],M=I[1],A=Object(a.useCallback)((function(e,t){"function"==typeof m&&m(e,t),M(e)}),[m]),T=Object(a.useCallback)((function(e,t){var n;"function"==typeof b&&(n=b(e,t)),x(void 0!==n?n:e)}),[b]),R=Object(a.useCallback)((function(){"function"==typeof g&&g(),S(!0)}),[g]),V=Object(a.useCallback)((function(){"function"==typeof h&&h(),S(!1)}),[h]),L=void 0!==p?p:j,D=void 0!==f?f:E,N=void 0!==v?v:k;return Object(r.a)(Object(r.a)({},y),{},{inputValue:L,menuIsOpen:D,onChange:A,onInputChange:T,onMenuClose:V,onMenuOpen:R,value:N})}},1503:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function o(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(o=e[n],i=t[n],!(o===i||r(o)&&r(i)))return!1;var o,i;return!0}function i(e,t){void 0===t&&(t=o);var n=null;function r(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];if(n&&n.lastThis===this&&t(r,n.lastArgs))return n.lastResult;var i=e.apply(this,r);return n={lastResult:i,lastArgs:r,lastThis:this},i}return r.clear=function(){n=null},r}},1504:function(e,t,n){"use strict";n.d(t,"a",(function(){return k}));const r=Math.min,o=Math.max,i=Math.round,a=Math.floor,l=e=>({x:e,y:e});function c(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function u(){return"undefined"!=typeof window}function s(e){return f(e)?(e.nodeName||"").toLowerCase():"#document"}function d(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function p(e){var t;return null==(t=(f(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function f(e){return!!u()&&(e instanceof Node||e instanceof d(e).Node)}function m(e){return!!u()&&(e instanceof Element||e instanceof d(e).Element)}function b(e){return!!u()&&(e instanceof HTMLElement||e instanceof d(e).HTMLElement)}function h(e){return!(!u()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof d(e).ShadowRoot)}function g(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=O(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function v(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function y(e){return["html","body","#document"].includes(s(e))}function O(e){return d(e).getComputedStyle(e)}function w(e){if("html"===s(e))return e;const t=e.assignedSlot||e.parentNode||h(e)&&e.host||p(e);return h(t)?t.host:t}function j(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=function e(t){const n=w(t);return y(n)?t.ownerDocument?t.ownerDocument.body:t.body:b(n)&&g(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=d(o);if(i){const e=x(a);return t.concat(a,a.visualViewport||[],g(o)?o:[],e&&n?j(e):[])}return t.concat(o,j(o,[],n))}function x(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function F(e){const t=O(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=b(e),a=o?e.offsetWidth:n,l=o?e.offsetHeight:r,c=i(n)!==a||i(r)!==l;return c&&(n=a,r=l),{width:n,height:r,$:c}}function C(e){return m(e)?e:e.contextElement}function E(e){const t=C(e);if(!b(t))return l(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:a}=F(t);let c=(a?i(n.width):n.width)/r,u=(a?i(n.height):n.height)/o;return c&&Number.isFinite(c)||(c=1),u&&Number.isFinite(u)||(u=1),{x:c,y:u}}const S=l(0);function P(e){const t=d(e);return v()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:S}function I(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const o=e.getBoundingClientRect(),i=C(e);let a=l(1);t&&(r?m(r)&&(a=E(r)):a=E(e));const u=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==d(e))&&t}(i,n,r)?P(i):l(0);let s=(o.left+u.x)/a.x,p=(o.top+u.y)/a.y,f=o.width/a.x,b=o.height/a.y;if(i){const e=d(i),t=r&&m(r)?d(r):r;let n=e,o=x(n);for(;o&&r&&t!==n;){const e=E(o),t=o.getBoundingClientRect(),r=O(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,p*=e.y,f*=e.x,b*=e.y,s+=i,p+=a,n=d(o),o=x(n)}}return c({width:f,height:b,x:s,y:p})}function k(e,t,n,i){void 0===i&&(i={});const{ancestorScroll:l=!0,ancestorResize:c=!0,elementResize:u="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:d=!1}=i,f=C(e),m=l||c?[...f?j(f):[],...j(t)]:[];m.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});const b=f&&s?function(e,t){let n,i=null;const l=p(e);function c(){var e;clearTimeout(n),null==(e=i)||e.disconnect(),i=null}return function u(s,d){void 0===s&&(s=!1),void 0===d&&(d=1),c();const{left:p,top:f,width:m,height:b}=e.getBoundingClientRect();if(s||t(),!m||!b)return;const h={rootMargin:-a(f)+"px "+-a(l.clientWidth-(p+m))+"px "+-a(l.clientHeight-(f+b))+"px "+-a(p)+"px",threshold:o(0,r(1,d))||1};let g=!0;function v(e){const t=e[0].intersectionRatio;if(t!==d){if(!g)return u();t?u(!1,t):n=setTimeout(()=>{u(!1,1e-7)},1e3)}g=!1}try{i=new IntersectionObserver(v,{...h,root:l.ownerDocument})}catch(e){i=new IntersectionObserver(v,h)}i.observe(e)}(!0),c}(f,n):null;let h,g=-1,v=null;u&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===f&&v&&(v.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),f&&!d&&v.observe(f),v.observe(t));let y=d?I(e):null;return d&&function t(){const r=I(e);!y||r.x===y.x&&r.y===y.y&&r.width===y.width&&r.height===y.height||n();y=r,h=requestAnimationFrame(t)}(),n(),()=>{var e;m.forEach(e=>{l&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==b||b(),null==(e=v)||e.disconnect(),v=null,d&&cancelAnimationFrame(h)}}},1507:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(1508);function o(e,t){if(e){if("string"==typeof e)return Object(r.a)(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Object(r.a)(e,t):void 0}}},1508:function(e,t,n){"use strict";function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}n.d(t,"a",(function(){return r}))},1509:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i}));var r="undefined"!=typeof document;function o(e,t,n){var r="";return n.split(" ").forEach((function(n){void 0!==e[n]?t.push(e[n]+";"):n&&(r+=n+" ")})),r}var i=function(e,t,n){var o=e.key+"-"+t.name;(!1===n||!1===r&&void 0!==e.compat)&&void 0===e.registered[o]&&(e.registered[o]=t.styles)},a=function(e,t,n){i(e,t,n);var o=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var a="",l=t;do{var c=e.insert(t===l?"."+o:"",l,e.sheet,!0);r||void 0===c||(a+=c),l=l.next}while(void 0!==l);if(!r&&0!==a.length)return a}}},1510:function(e,t,n){"use strict";n.d(t,"a",(function(){return l})),n.d(t,"b",(function(){return c}));var r=n(0),o="undefined"!=typeof document,i=function(e){return e()},a=!!r.useInsertionEffect&&r.useInsertionEffect,l=o&&a||i,c=a||r.useLayoutEffect},1511:function(e,t,n){"use strict";function r(e){var t=Object.create(null);return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}n.d(t,"a",(function(){return r}))},1512:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(345),o=n.n(r),i=function(e,t){return o()(e,t)}},1517:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var r=n(1507);function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(e,t)||Object(r.a)(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},1518:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(1508);var o=n(1507);function i(e){return function(e){if(Array.isArray(e))return Object(r.a)(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Object(o.a)(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},1519:function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var r={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},o=n(1511),i=/[A-Z]|^ms/g,a=/_EMO_([^_]+?)_([^]*?)_EMO_/g,l=function(e){return 45===e.charCodeAt(1)},c=function(e){return null!=e&&"boolean"!=typeof e},u=Object(o.a)((function(e){return l(e)?e:e.replace(i,"-$&").toLowerCase()})),s=function(e,t){switch(e){case"animation":case"animationName":if("string"==typeof t)return t.replace(a,(function(e,t,n){return p={name:t,styles:n,next:p},t}))}return 1===r[e]||l(e)||"number"!=typeof t||0===t?t:t+"px"};function d(e,t,n){if(null==n)return"";var r=n;if(void 0!==r.__emotion_styles)return r;switch(typeof n){case"boolean":return"";case"object":var o=n;if(1===o.anim)return p={name:o.name,styles:o.styles,next:p},o.name;var i=n;if(void 0!==i.styles){var a=i.next;if(void 0!==a)for(;void 0!==a;)p={name:a.name,styles:a.styles,next:p},a=a.next;return i.styles+";"}return function(e,t,n){var r="";if(Array.isArray(n))for(var o=0;o<n.length;o++)r+=d(e,t,n[o])+";";else for(var i in n){var a=n[i];if("object"!=typeof a){var l=a;null!=t&&void 0!==t[l]?r+=i+"{"+t[l]+"}":c(l)&&(r+=u(i)+":"+s(i,l)+";")}else if(!Array.isArray(a)||"string"!=typeof a[0]||null!=t&&void 0!==t[a[0]]){var p=d(e,t,a);switch(i){case"animation":case"animationName":r+=u(i)+":"+p+";";break;default:r+=i+"{"+p+"}"}}else for(var f=0;f<a.length;f++)c(a[f])&&(r+=u(i)+":"+s(i,a[f])+";")}return r}(e,t,n);case"function":if(void 0!==e){var l=p,f=n(e);return p=l,d(e,t,f)}}var m=n;if(null==t)return m;var b=t[m];return void 0!==b?b:m}var p,f=/label:\s*([^\s;{]+)\s*(;|$)/g;function m(e,t,n){if(1===e.length&&"object"==typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var r=!0,o="";p=void 0;var i=e[0];null==i||void 0===i.raw?(r=!1,o+=d(n,t,i)):o+=i[0];for(var a=1;a<e.length;a++){if(o+=d(n,t,e[a]),r)o+=i[a]}f.lastIndex=0;for(var l,c="";null!==(l=f.exec(o));)c+="-"+l[1];return{name:function(e){for(var t,n=0,r=0,o=e.length;o>=4;++r,o-=4)t=1540483477*(65535&(t=255&e.charCodeAt(r)|(255&e.charCodeAt(++r))<<8|(255&e.charCodeAt(++r))<<16|(255&e.charCodeAt(++r))<<24))+(59797*(t>>>16)<<16),n=1540483477*(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^1540483477*(65535&n)+(59797*(n>>>16)<<16);switch(o){case 3:n^=(255&e.charCodeAt(r+2))<<16;case 2:n^=(255&e.charCodeAt(r+1))<<8;case 1:n=1540483477*(65535&(n^=255&e.charCodeAt(r)))+(59797*(n>>>16)<<16)}return(((n=1540483477*(65535&(n^=n>>>13))+(59797*(n>>>16)<<16))^n>>>15)>>>0).toString(36)}(o)+c,styles:o,next:p}}},1529:function(e,t,n){var r=n(214);function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}e.exports=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e},e.exports.__esModule=!0,e.exports.default=e.exports},1530:function(e,t,n){var r=n(1471),o=n(1555),i=n(1472);e.exports=function(e){var t=o();return function(){var n,o=r(e);if(t){var a=r(this).constructor;n=Reflect.construct(o,arguments,a)}else n=o.apply(this,arguments);return i(this,n)}},e.exports.__esModule=!0,e.exports.default=e.exports},1531:function(e,t){e.exports=function(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))},e.exports.__esModule=!0,e.exports.default=e.exports},1544:function(e,t,n){"use strict";var r=n(0),o=n.n(r),i=n(1491),a=n(1475),l=n(21),c=function(e){var t=e.children,n=Object(r.useMemo)((function(){var e=Object(l.a)();return Object(i.a)({key:"wv-react-select-emotion",container:e})}),[]);return window.isApryseWebViewerWebComponent?o.a.createElement(a.a,{value:n},t):t};t.a=c},1555:function(e,t){function n(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(e.exports=n=function(){return!!t},e.exports.__esModule=!0,e.exports.default=e.exports)()}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},1556:function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));var r=n(1502),o=n(142),i=n(0),a=n(1484),l=(n(1475),n(1491),n(1529),n(287),n(434),n(432),n(433),n(1470),n(1530),n(1523),n(437),n(1531),n(214),n(116),n(1501),Object(i.forwardRef)((function(e,t){var n=Object(r.a)(e);return i.createElement(a.a,Object(o.a)({ref:t},n))})))},1587:function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var r=n(142),o=n(0),i=n(1484),a=n(1502),l=n(342),c=n(1518),u=n(1485),s=n(1486),d=["allowCreateWhileLoading","createOptionPosition","formatCreateLabel","isValidNewOption","getNewOptionData","onCreateOption","options","onChange"],p=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0,r=String(e).toLowerCase(),o=String(n.getOptionValue(t)).toLowerCase(),i=String(n.getOptionLabel(t)).toLowerCase();return o===r||i===r},f={formatCreateLabel:function(e){return'Create "'.concat(e,'"')},isValidNewOption:function(e,t,n,r){return!(!e||t.some((function(t){return p(e,t,r)}))||n.some((function(t){return p(e,t,r)})))},getNewOptionData:function(e,t){return{label:t,value:e,__isNew__:!0}}};n(1529),n(432),n(433),n(1470),n(1530),n(1523),n(287),n(434),n(437),n(1531),n(214),n(116),n(1501);var m=Object(o.forwardRef)((function(e,t){var n,p,m,b,h,g,v,y,O,w,j,x,F,C,E,S,P,I,k,M,A,T,R,V,L,D,N,H,_=Object(a.a)(e),U=(p=(n=_).allowCreateWhileLoading,m=void 0!==p&&p,b=n.createOptionPosition,h=void 0===b?"last":b,g=n.formatCreateLabel,v=void 0===g?f.formatCreateLabel:g,y=n.isValidNewOption,O=void 0===y?f.isValidNewOption:y,w=n.getNewOptionData,j=void 0===w?f.getNewOptionData:w,x=n.onCreateOption,F=n.options,C=void 0===F?[]:F,E=n.onChange,S=Object(u.a)(n,d),P=S.getOptionValue,I=void 0===P?i.c:P,k=S.getOptionLabel,M=void 0===k?i.b:k,A=S.inputValue,T=S.isLoading,R=S.isMulti,V=S.value,L=S.name,D=Object(o.useMemo)((function(){return O(A,Object(s.h)(V),C,{getOptionValue:I,getOptionLabel:M})?j(A,v(A)):void 0}),[v,j,M,I,A,O,C,V]),N=Object(o.useMemo)((function(){return!m&&T||!D?C:"first"===h?[D].concat(Object(c.a)(C)):[].concat(Object(c.a)(C),[D])}),[m,h,T,D,C]),H=Object(o.useCallback)((function(e,t){if("select-option"!==t.action)return E(e,t);var n=Array.isArray(e)?e:[e];if(n[n.length-1]!==D)E(e,t);else if(x)x(A);else{var r=j(A,A),o={action:"create-option",name:L,option:r};E(Object(s.d)(R,[].concat(Object(c.a)(Object(s.h)(V)),[r]),r),o)}}),[j,A,R,L,D,x,E,V]),Object(l.a)(Object(l.a)({},S),{},{options:N,onChange:H}));return o.createElement(i.a,Object(r.a)({ref:t},U))}))},1601:function(e,t,n){var r=n(32),o=n(1682);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach(e=>r.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))}),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const o=t[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);o.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};r(o,i);e.exports=o.locals||{}},1656:function(e,t,n){"use strict";var r=n(0),o=n.n(r),i=n(1486),a=n(43),l=function(e){var t=e.selectProps.menuIsOpen;return o.a.createElement(i.o.IndicatorsContainer,e,o.a.createElement(a.a,{className:"arrow",glyph:"icon-chevron-".concat(t?"up":"down")}))};t.a=l},1670:function(e,t,n){"use strict";var r=n(0),o=n.n(r),i=n(4),a=n.n(i),l=n(429),c=n(43),u=n(17),s=n.n(u),d=(n(1679),{label:a.a.string,value:a.a.string,onChange:a.a.func,validationMessage:a.a.string,hasError:a.a.bool,ariaDescribedBy:a.a.string,ariaLabelledBy:a.a.string}),p=function(e){var t=e.label,n=e.value,r=e.onChange,i=e.validationMessage,a=e.hasError,u=e.ariaDescribedBy,d=e.ariaLabelledBy,p=Object(l.a)().t;return o.a.createElement("div",{className:"input-container"},o.a.createElement("div",{className:"input-wrapper"},o.a.createElement("input",{className:s()({"text-input":!0,"text-input--error":a}),id:t,type:"text",onChange:function(e){return r(e.target.value)},"aria-label":t,value:n,"aria-describedby":a?{ariaDescribedBy:u}:void 0,"aria-labelledby":d}),a&&o.a.createElement(c.a,{glyph:"icon-alert"})),a&&o.a.createElement("div",{id:"TextInputError",className:"text-input-error"},o.a.createElement("p",{"aria-live":"assertive",className:"no-margin",role:"alert"},p(i))))};p.propTypes=d;var f=p;t.a=f},1677:function(e,t,n){var r=n(32),o=n(1678);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach(e=>r.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))}),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const o=t[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);o.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};r(o,i);e.exports=o.locals||{}},1678:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,".messageText{width:100%;font-size:11px;margin-top:4px}",""])},1679:function(e,t,n){var r=n(32),o=n(1680);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach(e=>r.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))}),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const o=t[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);o.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};r(o,i);e.exports=o.locals||{}},1680:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,".input-container .input-wrapper{width:100%;position:relative;display:flex;align-items:center}.input-container .input-wrapper .Icon{position:absolute;right:8px;color:var(--error-border-color)}.input-container .input-wrapper .text-input{padding:6px!important;width:100%;height:32px}.input-container .input-wrapper .text-input.text-input--error{padding-right:30px;border-color:var(--error-border-color)!important}.input-container .text-input-error{color:var(--error-text-color);width:100%;margin-top:4px}",""])},1682:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,".creatable-list{display:flex;flex-direction:column}.creatable-list-item{display:flex;flex-direction:row;align-items:center}.add-item-button{display:flex;align-items:center;width:78px;cursor:pointer}.icon-handle{cursor:grab}",""])},1683:function(e,t,n){var r=n(32),o=n(1684);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach(e=>r.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))}),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const o=t[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);o.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};r(o,i);e.exports=o.locals||{}},1684:function(e,t,n){(e.exports=n(33)(!1)).push([e.i,'.FormFieldEditPopup .signature-options-container{padding:5px 0;display:grid;grid-template-columns:.5fr 1fr;grid-template-areas:"label dropdown";align-items:center}.FormFieldEditPopup .signature-options-container label{grid-area:label}.FormFieldEditPopup .arrow{width:12px;height:16px;margin-top:2px}',""])},1736:function(e,t,n){"use strict";t.a=function(e){switch(e.errorType){case"empty":return"formField.formFieldPopup.invalidField.empty";case"duplicate":return"formField.formFieldPopup.invalidField.duplicate"}}},1737:function(e,t,n){"use strict";n(41),n(36),n(28),n(8),n(16),n(126),n(19),n(12),n(13),n(14),n(10),n(9),n(11),n(15),n(20),n(18),n(26),n(27),n(25),n(22),n(30),n(45),n(23),n(24),n(47),n(46);var r=n(0),o=n.n(r),i=n(48),a=n(429),l=n(4),c=n.n(l),u=(n(83),n(96),n(110),n(1963)),s=n(1493),d=n(1513),p=n(74);n(56);function f(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function m(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var b=function(){function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),m(this,"spec",void 0),m(this,"monitor",void 0),this.spec=t,this.monitor=n}var t,n,r;return t=e,(n=[{key:"canDrop",value:function(){var e=this.spec,t=this.monitor;return!e.canDrop||e.canDrop(t.getItem(),t)}},{key:"hover",value:function(){var e=this.spec,t=this.monitor;e.hover&&e.hover(t.getItem(),t)}},{key:"drop",value:function(){var e=this.spec,t=this.monitor;if(e.drop)return e.drop(t.getItem(),t)}}])&&f(t.prototype,n),r&&f(t,r),e}();function h(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,i=[],a=!0,l=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){l=!0,o=e}finally{try{a||null==n.return||n.return()}finally{if(l)throw o}}return i}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return g(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return g(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function v(e,t,n){var o=Object(s.a)(),i=function(e,t){var n=Object(r.useMemo)((function(){return new b(e,t)}),[t]);return Object(r.useEffect)((function(){n.spec=e}),[e]),n}(e,t),a=function(e){var t=e.accept;return Object(r.useMemo)((function(){return Object(p.a)(null!=e.accept,"accept must be defined"),Array.isArray(t)?t:[t]}),[t])}(e);Object(d.a)((function(){var e=h(Object(u.b)(a,i,o),2),r=e[0],l=e[1];return t.receiveHandlerId(r),n.receiveHandlerId(r),l}),[o,t,i,n,a.map((function(e){return e.toString()})).join("|")])}function y(e){return function(e){if(Array.isArray(e))return O(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return O(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return O(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function O(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function w(e,t){var n=y(t||[]);return null==t&&"function"!=typeof e&&n.push(e),Object(r.useMemo)((function(){return"function"==typeof e?e():e}),n)}var j=n(1961);var x=n(1962);var F=n(1603);function C(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,i=[],a=!0,l=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){l=!0,o=e}finally{try{a||null==n.return||n.return()}finally{if(l)throw o}}return i}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return E(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return E(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function E(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function S(e,t,n){return function(e,t,n){var r=C(Object(F.a)(e,t,n),2),o=r[0],i=r[1];return Object(d.a)((function(){var t=e.getHandlerId();if(null!=t)return e.subscribeToStateChange(i,{handlerIds:[t]})}),[e,i]),o}(t,e||function(){return{}},(function(){return n.reconnect()}))}function P(e){return Object(r.useMemo)((function(){return e.hooks.dropTarget()}),[e])}function I(e,t){var n,o=w(e,t),i=(n=Object(s.a)(),Object(r.useMemo)((function(){return new j.a(n)}),[n])),a=function(e){var t=Object(s.a)(),n=Object(r.useMemo)((function(){return new x.a(t.getBackend())}),[t]);return Object(d.a)((function(){return n.dropTargetOptions=e||null,n.reconnect(),function(){return n.disconnectDropTarget()}}),[e]),n}(o.options);return v(o,i,a),[S(o.collect,i,a),P(a)]}function k(e){return(k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function M(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function A(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var T=function(){function e(t,n,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),A(this,"spec",void 0),A(this,"monitor",void 0),A(this,"connector",void 0),this.spec=t,this.monitor=n,this.connector=r}var t,n,r;return t=e,(n=[{key:"beginDrag",value:function(){var e,t=this.spec,n=this.monitor;return null!==(e="object"===k(t.item)?t.item:"function"==typeof t.item?t.item(n):{})&&void 0!==e?e:null}},{key:"canDrag",value:function(){var e=this.spec,t=this.monitor;return"boolean"==typeof e.canDrag?e.canDrag:"function"!=typeof e.canDrag||e.canDrag(t)}},{key:"isDragging",value:function(e,t){var n=this.spec,r=this.monitor,o=n.isDragging;return o?o(r):t===e.getSourceId()}},{key:"endDrag",value:function(){var e=this.spec,t=this.monitor,n=this.connector,r=e.end;r&&r(t.getItem(),t),n.reconnect()}}])&&M(t.prototype,n),r&&M(t,r),e}();function R(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,o,i=[],a=!0,l=!1;try{for(n=n.call(e);!(a=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);a=!0);}catch(e){l=!0,o=e}finally{try{a||null==n.return||n.return()}finally{if(l)throw o}}return i}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return V(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return V(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function V(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function L(e,t,n){var o=Object(s.a)(),i=function(e,t,n){var o=Object(r.useMemo)((function(){return new T(e,t,n)}),[t,n]);return Object(r.useEffect)((function(){o.spec=e}),[e]),o}(e,t,n),a=function(e){return Object(r.useMemo)((function(){var t=e.type;return Object(p.a)(null!=t,"spec.type must be defined"),t}),[e])}(e);Object(d.a)((function(){if(null!=a){var e=R(Object(u.a)(a,i,o),2),r=e[0],l=e[1];return t.receiveHandlerId(r),n.receiveHandlerId(r),l}}),[o,t,n,i,a])}var D=n(1964);var N=n(1965);function H(e){return Object(r.useMemo)((function(){return e.hooks.dragSource()}),[e])}function _(e){return Object(r.useMemo)((function(){return e.hooks.dragPreview()}),[e])}function U(e,t){var n=w(e,t);Object(p.a)(!n.begin,"useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)");var o,i=(o=Object(s.a)(),Object(r.useMemo)((function(){return new D.a(o)}),[o])),a=function(e,t){var n=Object(s.a)(),o=Object(r.useMemo)((function(){return new N.a(n.getBackend())}),[n]);return Object(d.a)((function(){return o.dragSourceOptions=e||null,o.reconnect(),function(){return o.disconnectDragSource()}}),[o,e]),Object(d.a)((function(){return o.dragPreviewOptions=t||null,o.reconnect(),function(){return o.disconnectDragPreview()}}),[o,t]),o}(n.options,n.previewOptions);return L(n,i,a),[S(n.collect,i,a),H(a),_(a)]}var B=n(43);n(1601);function z(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return W(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return W(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function W(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var $=function(e){var t=e.option,n=e.index,a=e.onChange,l=e.onDeleteItem,c=e.moveListItem,u=e.id,s=e.addItem,d="item",p=Object(r.useRef)(null),f=z(I({accept:d,hover:function(e,t){var r;if(p.current){var o=e.index,i=n;if(o!==i){var a=null===(r=p.current)||void 0===r?void 0:r.getBoundingClientRect(),l=(a.bottom-a.top)/2,u=t.getClientOffset().y-a.top;o<i&&u<l||o>i&&u>l||(c(o,i),e.index=i)}}}}),2)[1],m=z(U({type:d,item:{type:d,id:u,index:n},collect:function(e){return{isDragging:e.isDragging()}}}),2),b=m[0].isDragging,h=m[1],g=Object(r.useCallback)((function(e){a(e.target.value)}),[a]),v=Object(r.useCallback)((function(e){"Enter"===e.key&&s()}),[s]);h(f(p));var y=b?0:1;return o.a.createElement("div",{ref:p,style:{opacity:y},className:"creatable-list-item"},o.a.createElement("div",{className:"icon-handle"},o.a.createElement(B.a,{glyph:"icon-drag-handle"})),o.a.createElement("input",{type:"text",onChange:g,value:t.displayValue,onKeyPress:v,autoFocus:!0}),o.a.createElement(i.a,{title:"action.delete",img:"icon-delete-line",onClick:l}))};function q(e){return(q="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function G(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?G(Object(n),!0).forEach((function(t){X(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):G(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function X(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==q(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==q(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===q(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function K(e){return function(e){if(Array.isArray(e))return Z(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Q(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function J(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(e,t)||Q(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Q(e,t){if(e){if("string"==typeof e)return Z(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Z(e,t):void 0}}function Z(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var ee={options:c.a.object,onOptionsUpdated:c.a.func,popupRef:c.a.object},te=function(e){var t=e.options,n=e.onOptionsUpdated,l=e.popupRef,c=Object(r.useRef)(!1);Object(r.useEffect)((function(){c.current=!1}),[n]),Object(r.useEffect)((function(){f(s)}),[t]);var u=Object(a.a)().t,s=t.map((function(e,t){return{id:t,displayValue:e.displayValue,value:e.value}})),d=J(Object(r.useState)(s),2),p=d[0],f=d[1],m=J(Object(r.useState)(s.length),2),b=m[0],h=m[1],g=Object(r.useRef)();Object(r.useEffect)((function(){if(c.current){var e=p.map((function(e){return{value:e.value,displayValue:e.displayValue}}));n(e)}else c.current=!0}),[p,n]);var v=Object(r.useCallback)((function(){var e=b;h(b+1),f([].concat(K(p),[{id:e,value:"",displayValue:""}])),l&&w()}),[b,p]),y=function(e){return function(){var t=p.filter((function(t){return e!==t.id}));f(t)}};var O=Object(r.useCallback)((function(e,t){var n,r,o,i=p[e],a=p.filter((function(t,n){return n!==e})),l=(n=t,r=i,(o=a.slice(0)).splice(n,0,r),o);f(l)}),[p]),w=function(){var e=l.current,t=g.current,n=e.getBoundingClientRect().bottom,r=window.innerHeight-n,o=t.scrollHeight>t.clientHeight;if(r<=40&&!o){var i=40*t.childElementCount;t.style.maxHeight="".concat(i,"px")}else r>40&&(t.style.maxHeight="200px")};return o.a.createElement("div",null,o.a.createElement("div",{className:"creatable-list",ref:g},p.map((function(e,t){return o.a.createElement($,{key:e.id,index:t,id:e.id,option:e,onChange:(n=e.id,function(e){var t=p.map((function(t){return t.id!==n?t:Y(Y({},t),{},{value:e,displayValue:e})}));f(t)}),onDeleteItem:y(e.id),moveListItem:O,addItem:v});var n}))),o.a.createElement(i.a,{title:u("action.addOption"),className:"add-item-button",label:u("action.addOption"),img:"icon-plus-sign",onClick:v}))};te.propTypes=ee;var ne=te;t.a=ne},1740:function(e,t,n){"use strict";n(36),n(26),n(27),n(12),n(13),n(8),n(25),n(22),n(30),n(28),n(45),n(23),n(24),n(47),n(46),n(14),n(10),n(9),n(11);var r=n(0),o=n.n(r),i=n(429),a=n(1587),l=n(1656),c=n(1544);n(1677);function u(e){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==u(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===u(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var f=function(e){var t=e.onChange,n=e.onInputChange,r=e.options,u=e.onCreateOption,s=e.textPlaceholder,p=e.value,f=e.isClearable,m=e.isValid,b=e.messageText,h=Object(i.a)().t,g={control:function(e,t){return d(d({},e),{},{minHeight:"28px",height:"36px",backgroundColor:"var(--component-background)",borderColor:t.selectProps.isValid?"hsl(0, 0%, 80%)":"hsl(28, 80%, 52%)",boxShadow:null,"&:hover":null})},singleValue:function(e){return d(d({},e),{},{color:"var(--text-color)"})},menu:function(e){return d(d({},e),{},{backgroundColor:"var(--component-background)",color:"var(--text-color)"})},option:function(e){return d(d({},e),{},{backgroundColor:"var(--component-background)",color:"var(--text-color)","&:hover":{backgroundColor:"var(--popup-button-hover)"}})},indicatorsContainer:function(e){return d(d({},e),{},{paddingRight:"6px",height:"26px"})}};return o.a.createElement(c.a,null,o.a.createElement(a.a,{isClearable:f,onChange:t,onInputChange:n,options:r,onCreateOption:u,placeholder:s,formatCreateLabel:function(e){return"".concat(h("action.create")," ").concat(e)},value:p,styles:g,isValid:m,components:{IndicatorsContainer:l.a}}),b?o.a.createElement("div",{className:"messageText"},b):void 0)};t.a=f},1741:function(e,t,n){"use strict";n(89),n(8),n(26),n(27),n(12),n(13),n(25),n(22),n(30),n(28),n(45),n(23),n(24),n(47),n(46),n(19),n(14),n(10),n(9),n(11),n(16),n(15),n(20),n(18);var r=n(0),o=n.n(r),i=n(4),a=n.n(i),l=n(1556),c=n(429),u=n(76),s=n(115),d=n(1656),p=n(1544);n(1683);function f(e){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function m(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return b(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return b(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){v(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function v(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==f(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==f(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===f(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var y={onChangeHandler:a.a.func.isRequired,initialOption:a.a.string},O=function(e){var t=e.onChangeHandler,n=e.initialOption,i=Object(c.a)().t,a={control:function(e,t){return g(g({},e),{},{minHeight:"28px",backgroundColor:"var(--component-background)",borderColor:t.isFocused?"var(--gray-10)":"hsl(0, 0%, 80%)",borderRadius:t.isFocused?"4px":e.borderRadius,borderWidth:t.isFocused?"2px":e.borderWidth,boxShadow:t.isFocused?"0 0 0 1px var(--gray-10)":null,"&:hover":{borderColor:t.isFocused?"var(--gray-10)":"hsl(0, 0%, 70%)"}})},valueContainer:function(e){return g(g({},e),{},{padding:"2px"})},singleValue:function(e){return g(g({},e),{},{color:"var(--text-color)"})},menu:function(e){return g(g({},e),{},{backgroundColor:"var(--component-background)"})},option:function(e,t){return g(g({},e),{},{backgroundColor:t.isSelected?"var(--blue-5)":"var(--component-background)","&:hover":{backgroundColor:"var(--blue-6)",color:"var(--gray-0)"},"&:active":{backgroundColor:t.isSelected?"var(--blue-5)":"var(--blue-6)"},border:t.isFocused?"var(--focus-visible-outline) !important":"null"})},indicatorsContainer:function(e){return g(g({},e),{},{paddingRight:"6px",height:"26px"})}},f=[{value:s.a.FULL_SIGNATURE,label:i("formField.types.signature")},{value:s.a.INITIALS,label:i("option.type.initials")}],b=f.find((function(e){return e.value===n})),h=m(Object(r.useState)(b),2),v=h[0],y=h[1],O=m(Object(r.useState)(!1),2),w=O[0],j=O[1];return o.a.createElement(u.a,{className:"signature-options-container",dataElement:"signatureOptionsDropdown"},o.a.createElement("label",{id:"form-field-type-label"},i("formField.type"),":"),o.a.createElement(p.a,null,o.a.createElement(l.a,{value:v,onChange:function(e){y(e),t(e),j(!1)},styles:a,options:f,isSearchable:!1,isClearable:!1,components:{IndicatorsContainer:d.a},"aria-labelledby":"form-field-type-label",onKeyDown:function(e){"Enter"===e.key&&(e.preventDefault(),j((function(e){return!e})))},menuIsOpen:w,onMenuOpen:function(){j(!0)},onMenuClose:function(){j(!1)}})))};O.propTypes=y;var w=O;t.a=w},1763:function(e,t,n){var r=n(32),o=n(1944);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var i={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const r=[];return n.querySelectorAll(t).forEach(e=>r.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&r.push(...e(t,n.shadowRoot))}),r}("apryse-webviewer"));const n=[];for(let r=0;r<t.length;r++){const o=t[r];if(0===r)o.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);o.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};r(o,i);e.exports=o.locals||{}},1944:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,':host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.FormFieldPanel{display:flex;flex-direction:column;position:relative;flex:1;overflow:auto;background-color:var(--panel-background);grid-gap:16px;gap:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .FormFieldPanel{padding:var(--padding);padding-top:0}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .FormFieldPanel .close-container{display:flex;align-items:center;justify-content:flex-end;height:28px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .FormFieldPanel .close-container .close-icon-container{padding:0;border:none;background-color:transparent;z-index:3;cursor:pointer}:host(:not([data-tabbing=true])) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .FormFieldPanel .close-container .close-icon-container,html:not([data-tabbing=true]) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .FormFieldPanel .close-container .close-icon-container{outline:none}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .FormFieldPanel .close-container .close-icon-container .close-icon{width:24px;height:24px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .FormFieldPanel .form-field-content-container{padding:var(--padding-medium);padding-top:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .FormFieldPanel{padding:var(--padding);padding-top:0}.App.is-web-component:not(.is-in-desktop-only-mode) .FormFieldPanel .close-container{display:flex;align-items:center;justify-content:flex-end;height:28px}.App.is-web-component:not(.is-in-desktop-only-mode) .FormFieldPanel .close-container .close-icon-container{padding:0;border:none;background-color:transparent;z-index:3;cursor:pointer}:host(:not([data-tabbing=true])) .App.is-web-component:not(.is-in-desktop-only-mode) .FormFieldPanel .close-container .close-icon-container,html:not([data-tabbing=true]) .App.is-web-component:not(.is-in-desktop-only-mode) .FormFieldPanel .close-container .close-icon-container{outline:none}.App.is-web-component:not(.is-in-desktop-only-mode) .FormFieldPanel .close-container .close-icon-container .close-icon{width:24px;height:24px}.App.is-web-component:not(.is-in-desktop-only-mode) .FormFieldPanel .form-field-content-container{padding:var(--padding-medium);padding-top:0}}.FormFieldPanel input{height:32px}.FormFieldPanel .ui__input,.FormFieldPanel input{border-color:var(--gray-6)}.FormFieldPanel .ui__input.ui__input--focused{box-shadow:none;border-color:var(--blue-5)}.FormFieldPanel .ui__input__input{height:32px}.FormFieldPanel .form-field-content-container{display:flex;flex-direction:column;grid-gap:16px;gap:16px;flex-grow:1;padding:var(--padding-medium)}.FormFieldPanel .form-field-content-container .field-title{display:flex;height:32px;align-items:center;grid-gap:16px;gap:16px;align-self:stretch;color:#343a40;font-size:16px;font-style:normal;font-weight:700;line-height:normal}.FormFieldPanel .form-field-content-container .signature-options-container{display:grid;grid-template-rows:auto auto;grid-template-areas:"label" "dropdown";grid-row-gap:8px;row-gap:8px}.FormFieldPanel .form-field-content-container .signature-options-container label{grid-area:label;font-size:14px;font-weight:700;line-height:20px}.FormFieldPanel .form-field-content-container .signature-options-container [class*=css][class*=-container]{height:32px}.FormFieldPanel .form-field-content-container .signature-options-container [class*=css][class*=-container] [class*=-control]{border-color:var(--gray-6)}.FormFieldPanel .form-field-content-container .signature-options-container [class*=css][class*=-container] [class*=-control] [class*=-ValueContainer]{padding-top:0;padding-bottom:0}.FormFieldPanel .form-field-content-container .signature-options-container [class*=css][class*=-container] [class*=-control] [class*=-IndicatorsContainer]{height:32px}.FormFieldPanel .form-field-content-container .signature-options-container [class*=css][class*=-menu] [class*=-option]:hover{background-color:var(--blue-5);color:var(--gray-0)}.FormFieldPanel .form-field-content-container .signature-options-container [class*=css][class*=-menu] [class*=-option]:active{background-color:var(--blue-6);color:var(--gray-0)}.FormFieldPanel .form-field-content-container .arrow{width:12px;height:16px;margin-top:2px}.FormFieldPanel .form-field-content-container .form-field-panel-header{font-size:16px;font-weight:700}.FormFieldPanel .form-field-content-container .fields-container{display:flex;flex-direction:column;grid-gap:16px;gap:16px}.FormFieldPanel .form-field-content-container .radio-group-label{grid-area:group-message;font-size:10px;width:240px;line-height:12px;color:var(--gray-7)}.FormFieldPanel .form-field-content-container .field-input{padding:0;display:flex;flex-direction:column;grid-gap:8px;gap:8px}.FormFieldPanel .form-field-content-container .field-input .field-text-input{cursor:default;width:100%;display:grid;grid-template-rows:auto auto;grid-template-areas:"label" "input";grid-row-gap:8px;row-gap:8px}.FormFieldPanel .form-field-content-container .field-input [class*=css][class*=-container] [class*=-control]{border-color:var(--gray-6)}.FormFieldPanel .form-field-content-container .field-input [class*=css][class*=-container] [class*=-control] [class*=-ValueContainer]{padding-top:0;padding-bottom:0}.FormFieldPanel .form-field-content-container .field-input [class*=css][class*=-container] [class*=-control] [class*=-IndicatorsContainer]{height:32px}.FormFieldPanel .form-field-content-container .field-input [class*=css][class*=-menu] [class*=-option]:hover{background-color:var(--blue-5);color:var(--gray-0)}.FormFieldPanel .form-field-content-container .field-input [class*=css][class*=-menu] [class*=-option]:active{background-color:var(--blue-6);color:var(--gray-0)}.FormFieldPanel .form-field-content-container .field-input .field-select-input{display:grid;grid-template-rows:auto auto;grid-template-areas:"label" "dropdown" "group-message";grid-row-gap:8px;row-gap:8px}.FormFieldPanel .form-field-content-container .field-input .field-select-input input{height:24px}.FormFieldPanel .form-field-content-container .field-input .field-select-input [class*=css][class*=-container]{height:32px}.FormFieldPanel .form-field-content-container .field-input .field-select-input [class*=css][class*=-container] [class*=-control]{box-sizing:content-box;border-color:var(--gray-6);height:100%}.FormFieldPanel .form-field-content-container .field-input .field-select-input [class*=css][class*=-container] [class*=-control] [class*=-ValueContainer]{padding-top:0;padding-bottom:0;height:100%;overflow:visible}.FormFieldPanel .form-field-content-container .field-input .field-select-input [class*=css][class*=-container] [class*=-control] [class*=-IndicatorsContainer]{height:32px}.FormFieldPanel .form-field-content-container .field-input label{padding-right:10px;grid-area:label;font-size:14px;font-weight:700;line-height:20px}.FormFieldPanel .form-field-content-container .divider-container{margin-left:-4px;margin-right:-4px;padding:0}.FormFieldPanel .form-field-content-container .ui__choice__input__check{width:16px;height:16px}.FormFieldPanel .properties-container{display:flex;flex-direction:column}.FormFieldPanel .properties-container .form-field-checkbox{height:32px}.FormFieldPanel .property-title{margin-top:0;margin-bottom:8px;font-size:14px;font-weight:700;line-height:20px}.FormFieldPanel .form-buttons-container{border-top:1.5px solid var(--gray-4);padding-top:var(--padding-medium);padding-bottom:var(--padding-medium);padding-right:var(--padding);display:flex;flex-direction:row;justify-content:flex-end}.FormFieldPanel .ok-form-field-button{display:flex;justify-content:center;align-items:center;background-color:var(--primary-button);border:1px solid var(--primary-button);color:var(--primary-button-text);padding:8px 16px;width:-moz-fit-content;width:fit-content;border-radius:4px;height:32px;cursor:pointer}.FormFieldPanel .ok-form-field-button:hover{background:var(--primary-button-hover);border:1px solid var(--primary-button-hover)}.FormFieldPanel .form-field-indicator-container .field-indicator{padding-left:calc(var(--padding-small) + 16px);margin-bottom:5px}.FormFieldPanel .form-dimension{flex-grow:1;display:flex;flex-direction:column;align-items:flex-start}.FormFieldPanel .form-dimension .form-dimension-title{margin-bottom:12px;font-size:14px;font-weight:700;line-height:20px}.FormFieldPanel .form-dimension .form-dimension-input-container{width:100%;display:flex;flex-direction:row;justify-content:space-between;grid-gap:10px;gap:10px}.FormFieldPanel .form-dimension .form-dimension-input-container .form-dimension-input{display:flex;flex-direction:column;align-items:flex-start;flex:1;grid-gap:8px;gap:8px}.FormFieldPanel .form-dimension .form-dimension-input-container .form-dimension-input span{font-size:13px;font-weight:400;line-height:15.6px}.FormFieldPanel .form-dimension .form-dimension-input-container .form-dimension-input input{width:100%;box-sizing:border-box;border-color:var(--gray-6)}.FormFieldPanel .form-dimension .form-dimension-input-container .form-dimension-input input::-webkit-inner-spin-button,.FormFieldPanel .form-dimension .form-dimension-input-container .form-dimension-input input::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.FormFieldPanel .form-dimension .form-dimension-input-container .form-dimension-input input [type=number]{-moz-appearance:textfield}.FormFieldPanel .form-dimension .form-dimension-input-container .form-dimension-input input:focus{border:1px solid var(--blue-5)}.FormFieldPanel .field-options-container{width:100%}.FormFieldPanel .field-options-container .option-title{font-size:14px;font-weight:700;line-height:20px}.FormFieldPanel .field-options-container .creatable-list{margin-top:8px;grid-gap:8px;gap:8px}.FormFieldPanel .field-options-container input{flex:1;margin-right:8px}.FormFieldPanel .field-options-container input:focus{border:1px solid var(--blue-5)}.FormFieldPanel .field-options-container .Button.add-item-button{width:90px;margin-left:0;justify-content:flex-start}.FormFieldPanel .field-options-container .Button.add-item-button.custom-ui{color:var(--blue-5)}.FormFieldPanel .field-options-container .Button.add-item-button.custom-ui:hover{color:var(--blue-6)}.FormFieldPanel .field-options-container .Button.add-item-button.custom-ui:hover .Icon svg line{stroke:var(--blue-6)}.FormFieldPanel .field-options-container .Button.add-item-button.custom-ui .Icon svg line{stroke:var(--blue-5)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .FormFieldPanel .field-options-container .Button.add-item-button{width:110px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .FormFieldPanel .field-options-container .Button.add-item-button{width:110px}}',""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1986:function(e,t,n){"use strict";n.r(t);n(36),n(15),n(576),n(88),n(28),n(8),n(41),n(10),n(141),n(9),n(11),n(114),n(78),n(19),n(12),n(13),n(14),n(16),n(20),n(18),n(26),n(27),n(25),n(22),n(30),n(45),n(23),n(24),n(47),n(46);var r=n(0),o=n.n(r),i=n(6),a=n(2),l=n(429),c=n(1),u=n(4),s=n.n(u),d=n(3),p=n(17),f=n.n(p),m=n(43),b=n(76),h=n(5),g=n(58),v=n(158),y=n(194),O=n(61),w=n(1736),j=(n(83),n(1313)),x=n(1740),F=n(1737),C=n(182),E=n(48),S=n(1670),P=n(1741),I=n(1966),k={indicator:s.a.object.isRequired,indicatorPlaceholder:s.a.string.isRequired},M=function(e){var t=e.indicator,n=e.indicatorPlaceholder,r=Object(l.a)().t;return o.a.createElement("div",{className:"form-field-indicator-container"},o.a.createElement(j.a,{id:"field-indicator",className:"form-field-checkbox",checked:t.isChecked,onChange:function(e){return r=e.target.checked,t.value.length<1&&r&&t.onChange(n),void t.toggleIndicator(r);var r},label:r(t.label),"aria-label":r(t.label),"aria-checked":t.isChecked}),t.isChecked&&o.a.createElement("div",{className:"field-indicator"},o.a.createElement(I.a,{id:"field-indicator-input",type:"text",onChange:function(e){return t.onChange(e.target.value)},value:t.value,fillWidth:"false",placeholder:n,disabled:!t.isChecked,"aria-disabled":!t.isChecked,"aria-label":r("formField.formFieldPopup.fieldIndicator")})))};M.propTypes=k;var A=M;n(1763);function T(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return R(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return R(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function R(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var V=function(e){var t=e.width,n=e.height,i=e.panelTitle,a=e.fields,u=e.flags,s=e.closeFormFieldEditPanel,d=e.isValid,p=e.validationMessage,f=e.toolOptions,m=e.onToolOptionsChange,b=e.fieldOptions,h=e.onFieldOptionsChange,g=e.annotation,y=e.onWidthChange,O=e.onHeightChange,w=e.indicator,I=e.shouldShowOptions,k=void 0!==I&&I,M=e.fieldProperties,R=e.onRadioFieldNameChange,V=Object(l.a)().t,L=Object(r.useCallback)((function(e){if("SignatureFormField"===e.getField().getFieldType()){var t=c.a.getFormFieldCreationManager();return V("formField.formFieldPopup.indicatorPlaceHolders.SignatureFormField.".concat(t.getSignatureOption(e)))}return V("formField.formFieldPopup.indicatorPlaceHolders.".concat(e.getField().getFieldType()))})),D=Object(r.useCallback)((function(e){var t=Object(v.a)(e.name);if("SignatureFormFieldCreateTool"===e.name){var n=null==t?void 0:t.signatureType;return V("formField.formFieldPopup.indicatorPlaceHolders.SignatureFormField.".concat(n))}var r=null==t?void 0:t.FormFieldTypes;return V("formField.formFieldPopup.indicatorPlaceHolders.".concat(r))})),N=Object(r.useCallback)((function(){if(g)return L(g);var e=c.a.getToolMode();return D(e)}),[g,V]),H=T(Object(r.useState)(N()),2),_=H[0],U=H[1];Object(r.useEffect)((function(){U(N())}),[i,N]);var B,z=Object(r.useCallback)((function(e,t){e.onChange((null==t?void 0:t.value)||""),R((null==t?void 0:t.value)||"")}),[]),W=function(e){switch(e.type){case"text":return function(e){var t=e.required,n=e.label,r=e.onChange,i=e.value,a=t&&!d;return o.a.createElement("div",{className:"field-text-input"},o.a.createElement("span",{id:n},V(n),t?"*":"",":"),o.a.createElement(S.a,{label:"".concat(n,"-input"),value:i,onChange:r,validationMessage:p,hasError:a,ariaDescribedBy:a?"FormFieldInputError":void 0,ariaLabelledBy:n}))}(e);case"select":return function(e){var t={value:M.name,label:M.name};return o.a.createElement("div",{className:"field-select-input"},o.a.createElement("label",null,V(e.label),e.required?"*":"",":"),o.a.createElement(x.a,{textPlaceholder:V("formField.formFieldPopup.fieldName"),options:M.radioButtonGroups.map((function(e){return{value:e,label:e}})),onChange:function(t){return z(e,t)},value:t,isValid:d,messageText:V(p)}),o.a.createElement("div",{className:"radio-group-label"},V("formField.formFieldPopup.radioGroups")))}(e);case"signatureOption":return function(e){return o.a.createElement(P.a,{onChangeHandler:function(t){e.onChange(t);var n=t.value;U(V("formField.formFieldPopup.indicatorPlaceHolders.SignatureFormField.".concat(n)))},initialOption:e.value})}(e);default:return null}};return o.a.createElement(o.a.Fragment,null,o.a.createElement("div",{className:"form-field-content-container"},o.a.createElement("div",{className:"field-title"},i),!!a.length&&o.a.createElement(o.a.Fragment,null,o.a.createElement("div",{className:"fields-container"},a.map((function(e){return o.a.createElement("div",{className:"field-input",key:e.label},W(e))}))),o.a.createElement(C.a,null)),k&&(B=(g?b:f).map((function(e){return e.value||""})).join(""),o.a.createElement(o.a.Fragment,null,o.a.createElement("div",{className:"field-options-container"},o.a.createElement("span",{className:"option-title"},V("formField.formFieldPopup.options")),o.a.createElement(F.a,{options:g?b:f,onOptionsUpdated:g?h:m,key:B})),o.a.createElement(C.a,null))),function(e){return o.a.createElement("div",{className:"properties-container"},o.a.createElement("h2",{className:"property-title",id:"property-group"},V("formField.formFieldPopup.properties")),o.a.createElement("div",{role:"group","aria-labelledby":"property-group"},e.map((function(e){return o.a.createElement(j.a,{id:V(e.label),className:"form-field-checkbox",key:V(e.label),checked:e.isChecked,onChange:function(t){return e.onChange(t.target.checked,e.name)},label:V(e.label),"aria-checked":e.isChecked})})),o.a.createElement(A,{indicator:w,indicatorPlaceholder:_})))}(u),o.a.createElement(C.a,null),o.a.createElement("div",{className:"form-dimension"},o.a.createElement("span",{className:"form-dimension-title"},V("formField.formFieldPopup.fieldSize")),o.a.createElement("div",{className:"form-dimension-input-container"},o.a.createElement("div",{className:"form-dimension-input"},o.a.createElement("span",{id:"form-field-width"},V("formField.formFieldPopup.width")),o.a.createElement("input",{id:"form-field-width-input",type:"number",min:0,value:t,onChange:function(e){return y(e.target.value)},"aria-label":"form-field-width-input","aria-labelledby":"form-field-width"})),o.a.createElement("div",{className:"form-dimension-input"},o.a.createElement("span",{id:"form-field-height"},V("formField.formFieldPopup.height")),o.a.createElement("input",{id:"form-field-height",type:"number",min:0,value:n,onChange:function(e){return O(e.target.value)},"aria-label":"form-field-height-input","aria-labelledby":"form-field-height"}))))),o.a.createElement("div",{className:"form-buttons-container"},o.a.createElement(E.a,{className:"ok-form-field-button",onClick:s,dataElement:"formFieldOK",label:V("action.close"),disabled:!d})))};V.propTypes={width:s.a.oneOfType([s.a.string,s.a.number]),height:s.a.oneOfType([s.a.string,s.a.number]),panelTitle:s.a.string,fields:s.a.array.isRequired,flags:s.a.array.isRequired,closeFormFieldEditPanel:s.a.func.isRequired,isValid:s.a.bool.isRequired,validationMessage:s.a.string,toolOptions:s.a.array,onToolOptionsChange:s.a.func,fieldOptions:s.a.array,onFieldOptionsChange:s.a.func,annotation:s.a.object,indicator:s.a.object.isRequired,onWidthChange:s.a.func.isRequired,onHeightChange:s.a.func.isRequired,shouldShowOptions:s.a.bool,fieldProperties:s.a.object,onRadioFieldNameChange:s.a.func};var L=V,D=window.Core,N=D.Annotations,H=D.Tools,_={name:"",defaultValue:"",radioButtonGroups:[]},U={width:0,height:0},B={ReadOnly:!1,Multiline:!1,Required:!1,MultiSelect:!1},z=function(e){var t=c.a.getFormFieldCreationManager();if(e)return t.getSignatureOption(e);var n=c.a.getToolMode(),r=Object(v.a)(n.name);return(null==r?void 0:r.signatureType)||""},W=function(e){c.a.getAnnotationManager().drawAnnotationsFromList([e])},$=function(e,t,n){var r=t-n;return Math.min(e,r)};function q(e){return(q="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function G(e){return function(e){if(Array.isArray(e))return Z(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Q(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function X(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Y(Object(n),!0).forEach((function(t){K(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function K(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==q(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==q(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===q(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function J(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(e,t)||Q(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Q(e,t){if(e){if("string"==typeof e)return Z(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Z(e,t):void 0}}function Z(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var ee=window.Core,te=ee.Annotations,ne=ee.Tools,re={annotation:s.a.object},oe=o.a.memo((function(e){var t,n,u=e.annotation,s=J(Object(i.e)((function(e){return[d.a.isElementOpen(e,h.a.FORM_FIELD_PANEL),d.a.getToolButtonObjects(e),d.a.isElementDisabled(e,"signatureOptionsDropdown"),d.a.isInDesktopOnlyMode(e),d.a.getMobilePanelSize(e),d.a.getFeatureFlags(e)]}),i.c),6),p=s[0],j=s[1],x=s[2],F=s[3],C=s[4],E=s[5],S=Object(l.a)().t,P=Object(i.d)(),I=Object(O.b)(),k=E.customizableUI,M=J(Object(r.useState)(_),2),A=M[0],T=M[1],R=J(Object(r.useState)(U),2),V=R[0],D=R[1],q=J(Object(r.useState)(B),2),Y=q[0],Q=q[1],Z=J(Object(r.useState)({showIndicator:!1,indicatorText:""}),2),ee=Z[0],re=Z[1],oe=J(Object(r.useState)(!0),2),ie=oe[0],ae=oe[1],le=J(Object(r.useState)(""),2),ce=le[0],ue=le[1],se=c.a.getToolMode(),de=se instanceof ne.ListBoxFormFieldCreateTool||se instanceof ne.ComboBoxFormFieldCreateTool,pe=J(Object(r.useState)(de?se.defaults.options:[]),2),fe=pe[0],me=pe[1],be=J(Object(r.useState)(null!==(t=null==u?void 0:u.getFieldOptions())&&void 0!==t?t:[]),2),he=be[0],ge=be[1],ve=J(Object(r.useState)(),2),ye=ve[0],Oe=ve[1];function we(){P(a.a.enableElement(h.a.ANNOTATION_POPUP,g.b)),P(a.a.closeElement(h.a.FORM_FIELD_PANEL)),T(_),D(U),Q(B),re({showIndicator:!1,indicatorText:""}),Oe(""),ae(!0)}var je=c.a.getFormFieldCreationManager(),xe=Object(r.useCallback)((function(e){c.a.deleteAnnotations([e])}),[]),Fe=Object(r.useCallback)((function(e){if(e instanceof ne.FormFieldCreateTool){var t=Object(v.a)(e.name)||{},n=t.options,r=t.flags,o=t.Width,i=void 0===o?0:o,a=t.Height,l=void 0===a?0:a,c=t.defaultValue,u=void 0===c?"":c,s=t.indicatorText,d=void 0===s?"":s,p=t.showIndicator,f=void 0!==p&&p;T((function(e){return X(X({},e),{},{defaultValue:u})})),D({width:i,height:l}),Q({ReadOnly:(null==r?void 0:r.READ_ONLY)||!1,Required:(null==r?void 0:r.REQUIRED)||!1,Multiline:(null==r?void 0:r.MULTILINE)||!1,MultiSelect:(null==r?void 0:r.MULTI_SELECT)||!1}),me(n),re({showIndicator:f,indicatorText:d}),m=e.name,b=j[m].title,Oe("".concat(S(b)," ").concat(S("stylePanel.headings.tool")))}else we();var m,b}),[j,S]);Object(r.useEffect)((function(){Ce();var e=function(){T((function(e){return X(X({},e),{},{radioButtonGroups:je.getRadioButtonGroups()})}))},t=function(e,t){if("deselected"===t){var n=c.a.getToolMode();n instanceof ne.FormFieldCreateTool?Fe(n):setTimeout((function(){u||we()}),500)}};return c.a.addEventListener("formFieldCreationModeStarted",e),c.a.addEventListener("toolModeUpdated",Fe),c.a.addEventListener("annotationSelected",t),function(){c.a.removeEventListener("formFieldCreationModeStarted",e),c.a.removeEventListener("annotationSelected",t),c.a.removeEventListener("toolModeUpdated",Fe)}}),[]);var Ce=function(){var e=c.a.getAnnotationsList().filter((function(e){return e instanceof te.RadioButtonWidgetAnnotation})).map((function(e){return e.getField().name})),t=G(new Set(G(e)));T((function(e){return X(X({},e),{},{radioButtonGroups:t})}))},Ee=function(e){Ce(),T((function(t){return X(X({},t),{},{name:e})}))};Object(r.useEffect)((function(){if(p)if(u){!function(e){var t=e.getFieldOptions();1===t.length&&""===t[0].value&&""===t[0].displayValue?ge([]):ge(t)}(u);var e=u.getFieldFlags(),t=te.WidgetFlags,n=t.READ_ONLY,r=t.MULTILINE,o=t.REQUIRED,i=t.MULTI_SELECT,a=u.getField(),l=a.name,s=A.radioButtonGroups,d=Boolean(l),f=d?"":"formField.formFieldPopup.invalidField.empty";ae(d),D({width:parseInt(u.Width),height:parseInt(u.Height)}),T((function(e){return X(X({},e),{},{name:l,defaultValue:a.defaultValue,radioButtonGroups:G(new Set([].concat(G(s),G(je.getRadioButtonGroups()))))})})),Q({ReadOnly:e[n]||!1,Multiline:e[r]||!1,Required:e[o]||!1,MultiSelect:e[i]||!1}),re({showIndicator:je.getShowIndicator(u),indicatorText:je.getIndicatorText(u)}),Oe(S("formField.formFieldPanel.".concat(a.getFieldType()))),ue(f)}else Fe(c.a.getToolMode())}),[p,u]);var Se,Pe,Ie,ke=Object(r.useCallback)((function(e){var t=je.setFieldName(u,e);ae(t.isValid);var n=Object(w.a)(t);ue(n),T((function(t){return X(X({},t),{},{name:e})})),Me()}),[u]),Me=Object(r.useCallback)((function(){if(u&&u instanceof te.RadioButtonWidgetAnnotation){var e=u.getField().flags,t=te.WidgetFlags,n=t.READ_ONLY,r=t.REQUIRED;Q((function(t){return X(X({},t),{},{ReadOnly:e.get(n)||!1,Required:e.get(r)||!1})}))}}),[u]),Ae=Object(r.useCallback)((function(e,t){if(Q((function(n){return X(X({},n),{},K({},te.WidgetFlags[t],e))})),u)u.setFieldFlag(te.WidgetFlags[t],e);else{var n=c.a.getToolMode(),r=Object(v.a)(n.name),o=X({},null==r?void 0:r.flags);o[t]=e,Object(y.a)(n.name,"flags",o)}}),[u]),Te=Object(r.useCallback)((function(e){if(T((function(t){return X(X({},t),{},{defaultValue:e})})),u)u.getField().defaultValue=e;else{var t=c.a.getToolMode();Object(y.a)(t.name,"defaultValue",e)}}),[u]),Re=Object(r.useCallback)((function(e){u.setFieldOptions(e)}),[he,u]),Ve=Object(r.useCallback)((function(e){c.a.getToolMode().defaults.options=e})),Le=Object(r.useCallback)((function(e){if(re((function(t){return X(X({},t),{},{showIndicator:e})})),u)je.setShowIndicator(u,e);else{var t=c.a.getToolMode();Object(y.a)(t.name,"showIndicator",e)}}),[u]),De=Object(r.useCallback)((function(e){if(re((function(t){return X(X({},t),{},{indicatorText:e})})),u)je.setIndicatorText(u,e);else{var t=c.a.getToolMode();Object(y.a)(t.name,"indicatorText",e)}}),[u]),Ne=Object(r.useCallback)((function(){we()}),[]),He=function(e){if(u){var t=$(e,c.a.getPageWidth(c.a.getCurrentPage()),u.X);u.setWidth(t),D((function(e){return X(X({},e),{},{width:t})})),W(u)}else{D((function(t){return X(X({},t),{},{width:e})}));var n=c.a.getToolMode();Object(y.a)(n.name,"Width",e)}},_e=function(e){if(u){var t=$(e,c.a.getPageHeight(c.a.getCurrentPage()),u.Y);u.setHeight(t),D((function(e){return X(X({},e),{},{height:t})})),W(u)}else{D((function(t){return X(X({},t),{},{height:e})}));var n=c.a.getToolMode();Object(y.a)(n.name,"Height",e)}},Ue=Object(r.useCallback)((function(e){e?ke(e.getField().name):xe(e),Ne()}),[]),Be=Object(r.useCallback)((function(){ie&&-1===A.radioButtonGroups.indexOf(A.name)&&""!==A.name&&T((function(e){return X(X({},e),{},{radioButtonGroups:[A.name].concat(G(e.radioButtonGroups))})})),we()}),[A]),ze=Object(r.useCallback)((function(e){var t=e.value;if(u)je.setSignatureOption(u,t);else{var n=c.a.getToolMode();Object(y.a)(n.name,"signatureType",t)}}),[u]),We=function(){P(a.a.closeElement(h.a.FORM_FIELD_PANEL))},$e=function(e){var t=e.onFieldNameChange,n=e.onFieldValueChange,r=e.fieldProperties,o=e.onSignatureOptionChange,i=e.getSignatureOption,a=e.annotation;return{NAME:{label:"formField.formFieldPopup.fieldName",onChange:t,value:r.name,required:!0,type:"text",focus:!0},DEFAULT_VALUE:{label:"formField.formFieldPopup.fieldValue",onChange:n,value:r.defaultValue,type:"text"},RADIO_GROUP:{label:"formField.formFieldPopup.fieldName",onChange:t,value:r.name,required:!0,type:"select"},SIGNATURE_OPTION:{label:"formField.formFieldPopup.signatureOption",onChange:o,value:i(a),required:!1,type:"signatureOption"}}}({onFieldNameChange:ke,onFieldValueChange:Te,fieldProperties:A,onSignatureOptionChange:ze,getSignatureOption:z,annotation:u}),qe=function(e,t){return{READ_ONLY:{label:"formField.formFieldPopup.readOnly",name:"READ_ONLY",onChange:e,isChecked:t.ReadOnly},MULTI_LINE:{label:"formField.formFieldPopup.multiLine",name:"MULTILINE",onChange:e,isChecked:t.Multiline},REQUIRED:{label:"formField.formFieldPopup.required",name:"REQUIRED",onChange:e,isChecked:t.Required},MULTI_SELECT:{name:"MULTI_SELECT",label:"formField.formFieldPopup.multiSelect",onChange:e,isChecked:t.MultiSelect}}}(Ae,Y),Ge={label:"formField.formFieldPopup.includeFieldIndicator",toggleIndicator:Le,isChecked:ee.showIndicator,onChange:De,value:ee.indicatorText};return o.a.createElement(b.a,{dataElement:h.a.FORM_FIELD_PANEL,className:f()((n={Panel:!0,FormFieldPanel:!0},K(n,C,I),K(n,"modular-ui-panel",k),n))},!F&&I&&o.a.createElement("div",{className:"close-container"},o.a.createElement("button",{className:"close-icon-container",onClick:We},o.a.createElement(m.a,{glyph:"ic_close_black_24px",className:"close-icon"}))),(Se=function(e,t,n){var r=c.a.getToolMode(),o=[];switch(!0){case e instanceof N.RadioButtonWidgetAnnotation:o.push(t.RADIO_GROUP);break;case!!e:o.push(t.NAME)}switch(!0){case e instanceof N.SignatureWidgetAnnotation&&!n:case r instanceof H.SignatureFormFieldCreateTool&&!n:o.push(t.SIGNATURE_OPTION)}var i=!e,a=e instanceof N.TextWidgetAnnotation,l=r instanceof H.TextFormFieldCreateTool;return(a||l&&(a||i))&&o.push(t.DEFAULT_VALUE),o}(u,$e,x),Pe=function(e,t){var n=[t.READ_ONLY,t.REQUIRED],r=c.a.getToolMode();switch(!0){case e instanceof N.TextWidgetAnnotation:case r instanceof H.TextFormFieldCreateTool:n.push(t.MULTI_LINE);break;case e instanceof N.ListWidgetAnnotation:case r instanceof H.ListBoxFormFieldCreateTool:n.push(t.MULTI_SELECT)}return n}(u,qe),Ie=function(e){var t=c.a.getToolMode(),n=e instanceof N.ListWidgetAnnotation||e instanceof N.ChoiceWidgetAnnotation,r=t instanceof H.ComboBoxFormFieldCreateTool||t instanceof H.ListBoxFormFieldCreateTool;return(!e||n)&&r||n}(u),o.a.createElement(L,{width:V.width,height:V.height,isValid:ie,panelTitle:ye,fields:Se,flags:Pe,validationMessage:ce,fieldProperties:A,onRadioFieldNameChange:Ee,onFieldOptionsChange:Re,fieldOptions:he,onToolOptionsChange:Ve,toolOptions:fe,annotation:u,redrawAnnotation:W,onWidthChange:He,onHeightChange:_e,indicator:Ge,onCancelEmptyFieldName:Ue,closeFormFieldEditPanel:Be,shouldShowOptions:Ie})))}));oe.displayName="FormFieldPanelContainer",oe.propTypes=re;var ie=oe;t.default=ie}}]);
//# sourceMappingURL=chunk.27.js.map