{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/lv.js"], "names": ["module", "exports", "e", "t", "default", "s", "d", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "m", "mm", "h", "hh", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,0EAA0EC,MAAM,KAAKC,OAAO,uGAAuGD,MAAM,KAAKE,UAAU,EAAEC,cAAc,kBAAkBH,MAAM,KAAKI,YAAY,kDAAkDJ,MAAM,KAAKK,YAAY,kBAAkBL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,GAAGc,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,cAAcC,GAAG,uBAAuBC,IAAI,8BAA8BC,KAAK,qCAAqCC,aAAa,CAACC,OAAO,SAASC,KAAK,WAAWpB,EAAE,iBAAiBqB,EAAE,UAAUC,GAAG,aAAaC,EAAE,UAAUC,GAAG,aAAavB,EAAE,SAASwB,GAAG,YAAYC,EAAE,SAASC,GAAG,cAAcC,EAAE,OAAOC,GAAG,cAAc,OAAO/B,EAAEC,QAAQ+B,OAAO7B,EAAE,MAAK,GAAIA,EAAlkCD,CAAE,EAAQ", "file": "chunks/chunk.184.js", "sourcesContent": ["!function(e,s){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=s(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],s):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_lv=s(e.dayjs)}(this,(function(e){\"use strict\";function s(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=s(e),d={name:\"lv\",weekdays:\"svētdiena_pirmdiena_otrdiena_trešdiena_ceturtdiena_piektdiena_sestdiena\".split(\"_\"),months:\"janvāris_februāris_marts_aprīlis_maijs_jūnijs_jūlijs_augusts_septembris_oktobris_novembris_decembris\".split(\"_\"),weekStart:1,weekdaysShort:\"Sv_P_O_T_C_Pk_S\".split(\"_\"),monthsShort:\"jan_feb_mar_apr_mai_jūn_jūl_aug_sep_okt_nov_dec\".split(\"_\"),weekdaysMin:\"Sv_P_O_T_C_Pk_S\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD.MM.YYYY.\",LL:\"YYYY. [gada] D. MMMM\",LLL:\"YYYY. [gada] D. MMMM, HH:mm\",LLLL:\"YYYY. [gada] D. MMMM, dddd, HH:mm\"},relativeTime:{future:\"pēc %s\",past:\"pirms %s\",s:\"dažām sekundēm\",m:\"minūtes\",mm:\"%d minūtēm\",h:\"stundas\",hh:\"%d stundām\",d:\"dienas\",dd:\"%d dienām\",M:\"mēneša\",MM:\"%d mēnešiem\",y:\"gada\",yy:\"%d gadiem\"}};return t.default.locale(d,null,!0),d}));"], "sourceRoot": ""}