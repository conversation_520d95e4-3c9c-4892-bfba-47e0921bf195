{"repository": {"vcs": {"type": "Git", "url": "http://***************:8006/levelbuild/WebAppCore", "revision": "559c514077f407a073aedeb8ee6d0a6d1056c287", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebAppCore", "revision": "559c514077f407a073aedeb8ee6d0a6d1056c287", "path": ""}, "config": {"analyzer": {"skip_excluded": true}, "excludes": {"paths": [{"pattern": "*Tests/**", "reason": "TEST_OF", "comment": "This directory contains test data which are not distributed."}], "scopes": [{"pattern": "devDependencies", "reason": "TEST_DEPENDENCY_OF", "comment": "Packages for development and testing only."}]}}}, "analyzer": {"start_time": "2023-09-26T14:27:28.233248222Z", "end_time": "2023-09-26T14:27:51.812440962Z", "environment": {"ort_version": "DOCKER-SNAPSHOT", "java_version": "********", "os": "Linux", "processors": 8, "max_memory": **********, "variables": {"JAVA_HOME": "/opt/java/openjdk", "ANDROID_HOME": "/opt/android-sdk"}, "tool_versions": {}}, "config": {"allow_dynamic_versions": false, "skip_excluded": true}, "result": {"projects": [{"id": "NuGet::DataOperations/DataOperations.csproj:", "definition_file_path": "DataOperations/DataOperations.csproj", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebAppCore", "revision": "559c514077f407a073aedeb8ee6d0a6d1056c287", "path": "DataOperations"}, "homepage_url": "", "scope_names": ["net8.0"]}, {"id": "NuGet::DataStoreInterface/DataStoreInterface.csproj:", "definition_file_path": "DataStoreInterface/DataStoreInterface.csproj", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebAppCore", "revision": "559c514077f407a073aedeb8ee6d0a6d1056c287", "path": "DataStoreInterface"}, "homepage_url": "", "scope_names": ["net8.0"]}, {"id": "NuGet::EntityInterfaces/EntityInterfaces.csproj:", "definition_file_path": "EntityInterfaces/EntityInterfaces.csproj", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebAppCore", "revision": "559c514077f407a073aedeb8ee6d0a6d1056c287", "path": "EntityInterfaces"}, "homepage_url": "", "scope_names": ["net8.0"]}, {"id": "NuGet::FileInterface/FileInterface.csproj:", "definition_file_path": "FileInterface/FileInterface.csproj", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebAppCore", "revision": "559c514077f407a073aedeb8ee6d0a6d1056c287", "path": "FileInterface"}, "homepage_url": "", "scope_names": ["net8.0"]}, {"id": "NuGet::FrontendDtos/FrontendDtos.csproj:", "definition_file_path": "FrontendDtos/FrontendDtos.csproj", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebAppCore", "revision": "559c514077f407a073aedeb8ee6d0a6d1056c287", "path": "FrontendDtos"}, "homepage_url": "", "scope_names": ["net8.0"]}, {"id": "NuGet::GeoDataInterface/GeoDataInterface.csproj:", "definition_file_path": "GeoDataInterface/GeoDataInterface.csproj", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebAppCore", "revision": "559c514077f407a073aedeb8ee6d0a6d1056c287", "path": "GeoDataInterface"}, "homepage_url": "", "scope_names": ["net8.0"]}, {"id": "NuGet::Logging/Logging.csproj:", "definition_file_path": "Logging/Logging.csproj", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebAppCore", "revision": "559c514077f407a073aedeb8ee6d0a6d1056c287", "path": "Logging"}, "homepage_url": "", "scope_names": ["net8.0"]}, {"id": "NuGet::MessageBus/MessageBus.csproj:", "definition_file_path": "MessageBus/MessageBus.csproj", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebAppCore", "revision": "559c514077f407a073aedeb8ee6d0a6d1056c287", "path": "MessageBus"}, "homepage_url": "", "scope_names": ["net8.0"]}, {"id": "NuGet::SharedDtos/SharedDtos.csproj:", "definition_file_path": "SharedDtos/SharedDtos.csproj", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebAppCore", "revision": "559c514077f407a073aedeb8ee6d0a6d1056c287", "path": "SharedDtos"}, "homepage_url": "", "scope_names": ["net8.0"]}, {"id": "NuGet::StorageInterface/StorageInterface.csproj:", "definition_file_path": "StorageInterface/StorageInterface.csproj", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebAppCore", "revision": "559c514077f407a073aedeb8ee6d0a6d1056c287", "path": "StorageInterface"}, "homepage_url": "", "scope_names": ["net8.0"]}, {"id": "NuGet::ZitadelApiInterface/ZitadelApiInterface.csproj:", "definition_file_path": "ZitadelApiInterface/ZitadelApiInterface.csproj", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebAppCore", "revision": "559c514077f407a073aedeb8ee6d0a6d1056c287", "path": "ZitadelApiInterface"}, "homepage_url": "", "scope_names": ["net8.0"]}, {"id": "Unmanaged::WebAppCore:559c514077f407a073aedeb8ee6d0a6d1056c287", "definition_file_path": "", "declared_licenses": [], "declared_licenses_processed": {}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "http://***************:8006/levelbuild/WebAppCore", "revision": "559c514077f407a073aedeb8ee6d0a6d1056c287", "path": ""}, "homepage_url": "", "scope_names": []}], "packages": [{"id": "NuGet::Grpc:2.46.6", "purl": "pkg:nuget/Grpc@2.46.6", "authors": ["The gRPC Authors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "Metapackage for gRPC C#", "homepage_url": "https://github.com/grpc/grpc", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/grpc/2.46.6/grpc.2.46.6.nupkg", "hash": {"value": "07a744a2c4a0c9266f15805439f83d2e014e697574774d63df78e848df4d18dc70aaa81804951e5a577e2571fceffedf0e1deaf4f6845dbb223778067619a566", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/grpc/grpc.git", "revision": "", "path": ""}}, {"id": "NuGet::IdentityModel:6.0.0", "purl": "pkg:nuget/IdentityModel@6.0.0", "authors": ["<PERSON><PERSON>,<PERSON>"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "OpenID Connect & OAuth 2.0 client library", "homepage_url": "https://github.com/IdentityModel/IdentityModel", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/identitymodel/6.0.0/identitymodel.6.0.0.nupkg", "hash": {"value": "d12653771f18c56621909879a10493da4109f9b6d474a5eb023d5d47d21c9fda7252b473d715788d24f200469fd5f93c3b1156f25e1be1f6eb0cc1240026959b", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/IdentityModel/IdentityModel", "revision": "2c10f4b0f75a2b1cc3e69d9bae8298394cc093a1", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/IdentityModel/IdentityModel.git", "revision": "2c10f4b0f75a2b1cc3e69d9bae8298394cc093a1", "path": ""}}, {"id": "NuGet::Serilog:3.0.2-dev-02044", "purl": "pkg:nuget/Serilog@3.0.2-dev-02044", "authors": ["Serilog Contributors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "Simple .NET logging with fully-structured events", "homepage_url": "https://serilog.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/serilog/3.0.2-dev-02044/serilog.3.0.2-dev-02044.nupkg", "hash": {"value": "f6a3a1178a79b0fb3bef5ed085ed04c034bc71c0af0ed95ee2f396625f9ab5daed088ca9494722d81a7902e2aaa1d28d97a9c44eb0652521688b6ea1c10e5baf", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/serilog/serilog.git", "revision": "a28a392e96fd35521f7998fdf2d77994d39a7db0", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/serilog/serilog.git", "revision": "a28a392e96fd35521f7998fdf2d77994d39a7db0", "path": ""}}, {"id": "NuGet::Zitadel:5.2.25", "purl": "pkg:nuget/Zitadel@5.2.25", "authors": ["<PERSON>, smartive AG"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "This package contains the library for authentication and authorization", "homepage_url": "https://github.com/smartive/zitadel-net", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/zitadel/5.2.25/zitadel.5.2.25.nupkg", "hash": {"value": "1cfe852513922df632f7cc8fab1347d406d39912d1a8e23b3a2fe4036022b37094abdcb414c6248bae4283e71939e15fb875296c9eb951d7d0d8e162a8c7fd10", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/smartive/zitadel-net.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/smartive/zitadel-net.git", "revision": "", "path": ""}}, {"id": "NuGet::jose-jwt:4.1.0", "purl": "pkg:nuget/jose-jwt@4.1.0", "authors": ["<PERSON>"], "declared_licenses": ["https://raw.github.com/dvse<PERSON><PERSON><PERSON>/jose-jwt/master/LICENSE"], "declared_licenses_processed": {"unmapped": ["https://raw.github.com/dvse<PERSON><PERSON><PERSON>/jose-jwt/master/LICENSE"]}, "description": "Javascript Object Signing and Encryption (JOSE), JSON Web Token (JWT), JSON Web Encryption (JWE) and JSON Web Key (JWK) Implementation for .NET ", "homepage_url": "https://github.com/dvse<PERSON><PERSON><PERSON>/jose-jwt", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/jose-jwt/4.1.0/jose-jwt.4.1.0.nupkg", "hash": {"value": "777a131853accf1188cb6eb49eaf6dac4f20d99c6bce28750edc90cb7934e315d788ab95207559eee7f243335611a0454e830faa2ac4fbdd803b4386fe225766", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dvse<PERSON><PERSON><PERSON>/jose-jwt.git", "revision": "", "path": ""}}, {"id": "NuGet:BouncyCastle:Cryptography:2.2.1", "purl": "pkg:nuget/BouncyCastle.Cryptography@2.2.1", "authors": ["Legion of the Bouncy Castle Inc."], "declared_licenses": ["https://www.nuget.org/packages/BouncyCastle.Cryptography/2.2.1/license"], "declared_licenses_processed": {"unmapped": ["https://www.nuget.org/packages/BouncyCastle.Cryptography/2.2.1/license"]}, "description": "BouncyCastle.NET is a popular cryptography library for .NET", "homepage_url": "https://www.bouncycastle.org/csharp/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/bouncycastle.cryptography/2.2.1/bouncycastle.cryptography.2.2.1.nupkg", "hash": {"value": "99d01493b88538a17dd516acfcf3091acd08ebca169d03d93128135534afeec54cdad1417befefbdffcca308b2bc4330f47bf9443db4c84cb8c9770b4df9193d", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/bcgit/bc-csharp", "revision": "b9c0074fb1b1b210182bba31d236664ea9ca37a8", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/bcgit/bc-csharp.git", "revision": "b9c0074fb1b1b210182bba31d236664ea9ca37a8", "path": ""}}, {"id": "NuGet:Google:Protobuf:3.24.3", "purl": "pkg:nuget/Google.Protobuf@3.24.3", "authors": ["Google Inc."], "declared_licenses": ["BSD-3-<PERSON><PERSON>"], "declared_licenses_processed": {"spdx_expression": "BSD-3-<PERSON><PERSON>"}, "description": "C# runtime library for Protocol Buffers - Google's data interchange format.", "homepage_url": "https://github.com/protocolbuffers/protobuf", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/google.protobuf/3.24.3/google.protobuf.3.24.3.nupkg", "hash": {"value": "9d781986be58d826dd4386014a87fa1dfee7f39a7be65a6460540aa4c822e4c817502bda150658d1b540c1c174d80d78563581c3e4b4b659ccf320a2c64ce516", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/protocolbuffers/protobuf.git", "revision": "e3e94a372fd50fd084f87ea6491cf18969c1fa79", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/protocolbuffers/protobuf.git", "revision": "e3e94a372fd50fd084f87ea6491cf18969c1fa79", "path": ""}}, {"id": "NuGet:Google.Api:CommonProtos:2.10.0", "purl": "pkg:nuget/Google.Api.CommonProtos@2.10.0", "authors": ["Google LLC"], "declared_licenses": ["BSD-3-<PERSON><PERSON>"], "declared_licenses_processed": {"spdx_expression": "BSD-3-<PERSON><PERSON>"}, "description": "Common Protocol Buffer messages for Google APIs", "homepage_url": "https://github.com/googleapis/gax-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/google.api.commonprotos/2.10.0/google.api.commonprotos.2.10.0.nupkg", "hash": {"value": "e12f75856912642619e3f5f76d5aaee516328bbb1fc9ffe316a423820c569151ce499130a890827cc2fa24b198c185b338051aff393c9fe727e2fa75a39b2fe4", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/googleapis/gax-dotnet", "revision": "f75385343c9437d34cee002d793ddf9eddfbdc67", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/googleapis/gax-dotnet.git", "revision": "f75385343c9437d34cee002d793ddf9eddfbdc67", "path": ""}}, {"id": "NuGet:Grpc:Core:2.46.6", "purl": "pkg:nuget/Grpc.Core@2.46.6", "authors": ["The gRPC Authors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "C# implementation of gRPC based on native gRPC C-core library.", "homepage_url": "https://github.com/grpc/grpc", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/grpc.core/2.46.6/grpc.core.2.46.6.nupkg", "hash": {"value": "112b86358d372b03d5c954f4356dcc3ba3c66f0334b91ffddc0d8abdf21cf1b7fee35e83c435fe8b84d4d6a9c2e5cd4715da5e8d398cd05da5142e05adbc20ae", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/grpc/grpc.git", "revision": "cdc97245c7977e85bb0234941f5c6cd9cb7accdf", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/grpc/grpc.git", "revision": "cdc97245c7977e85bb0234941f5c6cd9cb7accdf", "path": ""}}, {"id": "NuGet:Grpc.Core:Api:2.57.0", "purl": "pkg:nuget/Grpc.Core.Api@2.57.0", "authors": ["The gRPC Authors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "gRPC C# Surface API", "homepage_url": "https://github.com/grpc/grpc-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/grpc.core.api/2.57.0/grpc.core.api.2.57.0.nupkg", "hash": {"value": "3180d5611f002ed23b5abd210751a562cac15f219ad538eedfd85f151d596d0d2a3fef2a148bcef34e3796e7649b7657bad69faf17a48c7bc7c16a5b34fa610d", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/grpc/grpc-dotnet.git", "revision": "7733c07c69c98506e9f89ec68cd0fb33ecd1bcb4", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/grpc/grpc-dotnet.git", "revision": "7733c07c69c98506e9f89ec68cd0fb33ecd1bcb4", "path": ""}}, {"id": "NuGet:Grpc.Net:Client:2.57.0", "purl": "pkg:nuget/Grpc.Net.Client@2.57.0", "authors": ["The gRPC Authors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": ".NET client for gRPC", "homepage_url": "https://github.com/grpc/grpc-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/grpc.net.client/2.57.0/grpc.net.client.2.57.0.nupkg", "hash": {"value": "e311c35552d1407167621c28bb95dc2de12d184793e1fb19dafdd5d2bcf0c027f0452b0ad628357576eb65b892733a71c77885c26750214c22a0ffc0f9c41e3c", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/grpc/grpc-dotnet.git", "revision": "7733c07c69c98506e9f89ec68cd0fb33ecd1bcb4", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/grpc/grpc-dotnet.git", "revision": "7733c07c69c98506e9f89ec68cd0fb33ecd1bcb4", "path": ""}}, {"id": "NuGet:Grpc.Net:ClientFactory:2.57.0", "purl": "pkg:nuget/Grpc.Net.ClientFactory@2.57.0", "authors": ["The gRPC Authors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "HttpClientFactory integration the for gRPC .NET client", "homepage_url": "https://github.com/grpc/grpc-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/grpc.net.clientfactory/2.57.0/grpc.net.clientfactory.2.57.0.nupkg", "hash": {"value": "c8fbde8122449557414650aaf5377c976fc06414183a276dc6233a7c166a6276155fc1c05448745bcd25fed2b449a23ced4d29d6d2893234e85e37e3f639c8d1", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/grpc/grpc-dotnet.git", "revision": "7733c07c69c98506e9f89ec68cd0fb33ecd1bcb4", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/grpc/grpc-dotnet.git", "revision": "7733c07c69c98506e9f89ec68cd0fb33ecd1bcb4", "path": ""}}, {"id": "NuGet:Grpc.Net:Common:2.57.0", "purl": "pkg:nuget/Grpc.Net.Common@2.57.0", "authors": ["The gRPC Authors"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "Infrastructure for common functionality in gRPC", "homepage_url": "https://github.com/grpc/grpc-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/grpc.net.common/2.57.0/grpc.net.common.2.57.0.nupkg", "hash": {"value": "30ae93fb69ec008ce333d61eb565f14271b1fb52d4fe12b0b407f1927db858d862e9bc7a147aec24f5b708d338caf450253291dba553f38b4ca968d2de8d62a1", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/grpc/grpc-dotnet.git", "revision": "7733c07c69c98506e9f89ec68cd0fb33ecd1bcb4", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/grpc/grpc-dotnet.git", "revision": "7733c07c69c98506e9f89ec68cd0fb33ecd1bcb4", "path": ""}}, {"id": "NuGet:IdentityModel.AspNetCore:OAuth2Introspection:6.2.0", "purl": "pkg:nuget/IdentityModel.AspNetCore.OAuth2Introspection@6.2.0", "authors": ["<PERSON><PERSON>,<PERSON>"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "ASP.NET Core authentication handler for validating tokens using OAuth 2.0 introspection", "homepage_url": "https://github.com/IdentityModel/IdentityModel.AspNetCore.OAuth2Introspection", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/identitymodel.aspnetcore.oauth2introspection/6.2.0/identitymodel.aspnetcore.oauth2introspection.6.2.0.nupkg", "hash": {"value": "604156c2b0ddc895ee023ba9e5d194de38700e83b6593f3521dafde90bb9397dc743ee3fc47504975dbbb0102e3c1dafefb92093f8ab7f5f4d73765bf32ba520", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/IdentityModel/IdentityModel.AspNetCore.OAuth2Introspection", "revision": "b71fa438bc566e3b527547969dd29302084d116d", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/IdentityModel/IdentityModel.AspNetCore.OAuth2Introspection.git", "revision": "b71fa438bc566e3b527547969dd29302084d116d", "path": ""}}, {"id": "NuGet:Microsoft:CSharp:4.5.0", "purl": "pkg:nuget/Microsoft.CSharp@4.5.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides support for compilation and code generation, including dynamic, using the C# language.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.csharp/4.5.0/microsoft.csharp.4.5.0.nupkg", "hash": {"value": "c9659e4db182cc13a544f583088c624d95b579c66231b6a8d194fdeca28459d061acbbd4a94f11773921cee091433be8c73c6547bbf2b4ee3738e805764c6fea", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:Microsoft:EntityFrameworkCore:8.0.0-preview.6.23329.4", "purl": "pkg:nuget/Microsoft.EntityFrameworkCore@8.0.0-preview.6.23329.4", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Entity Framework Core is a modern object-database mapper for .NET. It supports LINQ queries, change tracking, updates, and schema migrations. EF Core works with SQL Server, Azure SQL Database, SQLite, Azure Cosmos DB, MySQL, PostgreSQL, and other databases through a provider plugin API.", "homepage_url": "https://docs.microsoft.com/ef/core/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.entityframeworkcore/8.0.0-preview.6.23329.4/microsoft.entityframeworkcore.8.0.0-preview.6.23329.4.nupkg", "hash": {"value": "4695ea0b0c318d647a574f366b8a057e73c1a3321d5ad93d29aabf5be3c34071f46647c13b01aa983ffcb854ee9dda163b928ad41cf576a86514dda0e9918abf", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/efcore", "revision": "2a752d937e5c80bd36653b04dcdacec887603020", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/efcore.git", "revision": "2a752d937e5c80bd36653b04dcdacec887603020", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Authentication:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Authentication@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core common types used by the various authentication middleware components.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.authentication/2.2.0/microsoft.aspnetcore.authentication.2.2.0.nupkg", "hash": {"value": "2ae7b5eec16e748fad92a13ccec88f5ba288ff26272084a1f1157af5bf92a6b57c9f1fb222ba2d4f6227bf8afa5b0e5f64dd030038f6cca4b4b463595d771968", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/Security", "revision": "93926543f8469614c2feb23de8a8c0561b8b2463", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/Security.git", "revision": "93926543f8469614c2feb23de8a8c0561b8b2463", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Authentication.Abstractions:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Authentication.Abstractions@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core common types used by the various authentication components.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.authentication.abstractions/2.2.0/microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg", "hash": {"value": "67ad256e07bd55ae00e8450c137617f11b861269b3919035984c715237eba9bff71a599ca59c8c0d07b6978fd498400274a4907a40942323d5e3aad413bd2b58", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HttpAbstractions", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HttpAbstractions.git", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Authentication.Core:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Authentication.Core@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core common types used by the various authentication middleware components.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.authentication.core/2.2.0/microsoft.aspnetcore.authentication.core.2.2.0.nupkg", "hash": {"value": "5313aa9320cc68cd97d4f25a0796b47cef3a4aa05887166e52c7abeaa67e1fbf202ea35ae056aa925efc21c116575f6856aac263410534e8feda05d63e4d856b", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HttpAbstractions", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HttpAbstractions.git", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Authentication.JwtBearer:7.0.11", "purl": "pkg:nuget/Microsoft.AspNetCore.Authentication.JwtBearer@7.0.11", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "ASP.NET Core middleware that enables an application to receive an OpenID Connect bearer token.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.authentication.jwtbearer/7.0.11/microsoft.aspnetcore.authentication.jwtbearer.7.0.11.nupkg", "hash": {"value": "e63cb7f2c3603ea0f355b942560d1835db65df5da35818389f205502d1a4d79857992c06f84cc85a5ecfc216aea402890387bcf63f798b98042e00d7b2866a8f", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/aspnetcore", "revision": "70048beee894074e9718206a0017d9b080ee66ff", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/aspnetcore.git", "revision": "70048beee894074e9718206a0017d9b080ee66ff", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Authentication.OpenIdConnect:7.0.11", "purl": "pkg:nuget/Microsoft.AspNetCore.Authentication.OpenIdConnect@7.0.11", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "ASP.NET Core middleware that enables an application to support the OpenID Connect authentication workflow.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.authentication.openidconnect/7.0.11/microsoft.aspnetcore.authentication.openidconnect.7.0.11.nupkg", "hash": {"value": "2af1c45cc43ced732ad1fec3c6bb4399cb00526d099382c5dae592dcd2d226923dbfb16b5f3e88075ae207d3fe514328a36b42f70aee570aceb9ff4c9efe0c5d", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/aspnetcore", "revision": "70048beee894074e9718206a0017d9b080ee66ff", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/aspnetcore.git", "revision": "70048beee894074e9718206a0017d9b080ee66ff", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Cryptography.Internal:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Cryptography.Internal@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "Infrastructure for ASP.NET Core cryptographic packages. Applications and libraries should not reference this package directly.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.cryptography.internal/2.2.0/microsoft.aspnetcore.cryptography.internal.2.2.0.nupkg", "hash": {"value": "861ff64140528a4899be92549763a48511be752e29e9ab5afbf63f794ad711577fbe65d24ebab80db5b57dbe925415e1061c10cc7c2b741cdc374b14a8ef38e3", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/AspNetCore", "revision": "ce8cf65589734f82b0536c543aba5bd60d0a5a98", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/AspNetCore.git", "revision": "ce8cf65589734f82b0536c543aba5bd60d0a5a98", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:DataProtection:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.DataProtection@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core logic to protect and unprotect data, similar to DPAPI.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.dataprotection/2.2.0/microsoft.aspnetcore.dataprotection.2.2.0.nupkg", "hash": {"value": "948093ee5134878fb19a97e69c4bdab6d64753cb41ac1576db9a2da1b406a487e07f493b578a8357b350d57f187e979463f8bc12820da72b9887f7835d6e009c", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/AspNetCore", "revision": "ce8cf65589734f82b0536c543aba5bd60d0a5a98", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/AspNetCore.git", "revision": "ce8cf65589734f82b0536c543aba5bd60d0a5a98", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:DataProtection.Abstractions:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.DataProtection.Abstractions@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core data protection abstractions.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.dataprotection.abstractions/2.2.0/microsoft.aspnetcore.dataprotection.abstractions.2.2.0.nupkg", "hash": {"value": "674106d80f31ac355d3647e1633531cd5705290535e6a075867e4f62d741789e2c5ad1013a43526bf72e69751bfea6dee9a9958a82ad3125dcbddb826aadd0b3", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/AspNetCore", "revision": "ce8cf65589734f82b0536c543aba5bd60d0a5a98", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/AspNetCore.git", "revision": "ce8cf65589734f82b0536c543aba5bd60d0a5a98", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Hosting.Abstractions:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Hosting.Abstractions@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core hosting and startup abstractions for web applications.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.hosting.abstractions/2.2.0/microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg", "hash": {"value": "a900d6a07c05f80066c536abcbfa44a545fb7f5842a10864d110e726e325399f9d3c5a7fbaf93ef6cc5953ac3f73c10bf633e57ee60e3902463c44d4bff34d5c", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/Hosting", "revision": "0724e6cde1149ee1a19bfec9c13a2c9327b71213", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/Hosting.git", "revision": "0724e6cde1149ee1a19bfec9c13a2c9327b71213", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Hosting.Server.Abstractions:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Hosting.Server.Abstractions@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core hosting server abstractions for web applications.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.hosting.server.abstractions/2.2.0/microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg", "hash": {"value": "a7875388fd4efc7469c0cc5801345279e9331d3bf9a70877af13d0c2d6cedb5ff17f49db78246cc05b57436289c1db7ba617c36782bd1e6702be2045d498192e", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/Hosting", "revision": "0724e6cde1149ee1a19bfec9c13a2c9327b71213", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/Hosting.git", "revision": "0724e6cde1149ee1a19bfec9c13a2c9327b71213", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Http:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Http@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core default HTTP feature implementations.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.http/2.2.0/microsoft.aspnetcore.http.2.2.0.nupkg", "hash": {"value": "0a7ec82d94908a42a725be258447d5c7193e0ac437e99c55cceaf30d49aa18ea7d0493f72ea558613a3fdaf1a04254a8dc60f847c7c37c07e55fcb3dbd7973fe", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HttpAbstractions", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HttpAbstractions.git", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Http.Abstractions:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Http.Abstractions@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core HTTP object model for HTTP requests and responses and also common extension methods for registering middleware in an IApplicationBuilder.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.http.abstractions/2.2.0/microsoft.aspnetcore.http.abstractions.2.2.0.nupkg", "hash": {"value": "d0b334c186ecbd89d6d7593caa1d030d6104c7fa7a41a0eaa5c2e53328433a6356f4a829ad187d8214c009f78e0a8d6146a764b54329eaa82ecce2a2c482f5df", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HttpAbstractions", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HttpAbstractions.git", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Http.Extensions:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Http.Extensions@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core common extension methods for HTTP abstractions, HTTP headers, HTTP request/response, and session state.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.http.extensions/2.2.0/microsoft.aspnetcore.http.extensions.2.2.0.nupkg", "hash": {"value": "c177c1ebcb29dd7fbafd32d41d51797a2b1c9fd886a6d184c23272938c816db380549102139f7f294ba7eac4fe26535c342584584d12ec4c3ec8c0fa6e6debad", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HttpAbstractions", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HttpAbstractions.git", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:Http.Features:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.Http.Features@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core HTTP feature interface definitions.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.http.features/2.2.0/microsoft.aspnetcore.http.features.2.2.0.nupkg", "hash": {"value": "8efbbbe3f90e8080c29c01fd5eedbfc084eb49a803a752f8389fa94ebc56cce76935cee50ed7cfaf2c017bb87d094900c7ff3de4bf62debef4bdfe6582806b73", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HttpAbstractions", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HttpAbstractions.git", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}}, {"id": "NuGet:Microsoft.AspNetCore:WebUtilities:2.2.0", "purl": "pkg:nuget/Microsoft.AspNetCore.WebUtilities@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "ASP.NET Core utilities, such as for working with forms, multipart messages, and query strings.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.aspnetcore.webutilities/2.2.0/microsoft.aspnetcore.webutilities.2.2.0.nupkg", "hash": {"value": "f3f97e3bfde44c1563354731bd872063f7e72d07f4d46cc6f159a8012761618ba798a21c0a29a4f9a63a4af57fdedb90055919255fa9707090d7e5f28c4a9fc9", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HttpAbstractions", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HttpAbstractions.git", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}}, {"id": "NuGet:Microsoft.EntityFrameworkCore:Abstractions:8.0.0-preview.6.23329.4", "purl": "pkg:nuget/Microsoft.EntityFrameworkCore.Abstractions@8.0.0-preview.6.23329.4", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides abstractions and attributes that are used to configure Entity Framework Core", "homepage_url": "https://docs.microsoft.com/ef/core/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.entityframeworkcore.abstractions/8.0.0-preview.6.23329.4/microsoft.entityframeworkcore.abstractions.8.0.0-preview.6.23329.4.nupkg", "hash": {"value": "edcf3cc04a94d2fc21f084dd32d6ec3be5c0b444bd9d11e9de83c0795431bc1ce93f52355ac1b3f9a000144736be6a562b43421445690c2ad106d9a0d51c17ef", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/efcore", "revision": "2a752d937e5c80bd36653b04dcdacec887603020", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/efcore.git", "revision": "2a752d937e5c80bd36653b04dcdacec887603020", "path": ""}}, {"id": "NuGet:Microsoft.EntityFrameworkCore:Analyzers:8.0.0-preview.6.23329.4", "purl": "pkg:nuget/Microsoft.EntityFrameworkCore.Analyzers@8.0.0-preview.6.23329.4", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "CSharp Analyzers for Entity Framework Core.", "homepage_url": "https://docs.microsoft.com/ef/core/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.entityframeworkcore.analyzers/8.0.0-preview.6.23329.4/microsoft.entityframeworkcore.analyzers.8.0.0-preview.6.23329.4.nupkg", "hash": {"value": "796f8a7c66d806330d415658ca1ee2340bd94b8fe52a1fb97e047dcb5193c255a5426416b29381383a1a851f718c49f971c83b64813d473f42b9255a8cde1d3c", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/efcore", "revision": "2a752d937e5c80bd36653b04dcdacec887603020", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/efcore.git", "revision": "2a752d937e5c80bd36653b04dcdacec887603020", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Caching.Abstractions:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.Caching.Abstractions@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Caching abstractions for in-memory cache and distributed cache.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.caching.abstractions/8.0.0-preview.6.23329.7/microsoft.extensions.caching.abstractions.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "0d56a5151fbcb9884132922ada41c170bf7c0219c361a5c3cc39619dea06ce3599ba2dc72c096d916508c3516966b9a88a2227e5135654a3384f8f098c62beeb", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Caching.Memory:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.Caching.Memory@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "In-memory cache implementation of Microsoft.Extensions.Caching.Memory.IMemoryCache.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.caching.memory/8.0.0-preview.6.23329.7/microsoft.extensions.caching.memory.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "2d1aa42de3f4335239736b40684af62efb01ce4be1920990ce7f541aa560ed4368599b2bf90fab5139eeb37d35231881ba133981d31e8c33738069d91a215959", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Configuration.Abstractions:2.2.0", "purl": "pkg:nuget/Microsoft.Extensions.Configuration.Abstractions@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "Abstractions of key-value pair based configuration.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.configuration.abstractions/2.2.0/microsoft.extensions.configuration.abstractions.2.2.0.nupkg", "hash": {"value": "33abb870c597c4fb6ac89274de87b3caa4ce4664a04cfa5ada06512a41060975c78721a73357078cf4f3ab9b1ebd84b554910c6f64d98fad2601cfa10e83cf71", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/Extensions", "revision": "9bc79b2f25a3724376d7af19617c33749a30ea3a", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/Extensions.git", "revision": "9bc79b2f25a3724376d7af19617c33749a30ea3a", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:DependencyInjection:6.0.0", "purl": "pkg:nuget/Microsoft.Extensions.DependencyInjection@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Default implementation of dependency injection for Microsoft.Extensions.DependencyInjection.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.dependencyinjection/6.0.0/microsoft.extensions.dependencyinjection.6.0.0.nupkg", "hash": {"value": "c25eefa020a785d8608f8c5bc1b7d479aa3f9bb3295aac41a1b6b81440ee8c2d686f28ab5f7c9bd8c762152ad8957cc71b7abfa7d4b6c85592bfc27e5155928a", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:DependencyInjection:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.DependencyInjection@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Default implementation of dependency injection for Microsoft.Extensions.DependencyInjection.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.dependencyinjection/8.0.0-preview.6.23329.7/microsoft.extensions.dependencyinjection.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "b56a3ec7329639b5e55b758b19840883975ec400de1aa9a1570b01037b511cdacf785e31a410309ed3bb9c33d4f9614cebff8fa4b341139d902fea26330046a5", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:DependencyInjection.Abstractions:6.0.0", "purl": "pkg:nuget/Microsoft.Extensions.DependencyInjection.Abstractions@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Abstractions for dependency injection.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.dependencyinjection.abstractions/6.0.0/microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg", "hash": {"value": "015a392a362c81b35143b19c5da4eac0928cc2f2e13329ab2e945a15a5c9dea077cd66b0c467c75f2dfe6f90c3e0bf2ecdc059d75096bac39b4156a53f997bd2", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:DependencyInjection.Abstractions:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.DependencyInjection.Abstractions@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Abstractions for dependency injection.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.dependencyinjection.abstractions/8.0.0-preview.6.23329.7/microsoft.extensions.dependencyinjection.abstractions.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "31bd3c13b2c3d0ffca44b0eba91df50f76a9c29ec488f2dae80af8d8cad52159c3eef96dea84ba353c7927fed6db80f4a30d695e0aa648766d0965c1d3461c7c", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:FileProviders.Abstractions:2.2.0", "purl": "pkg:nuget/Microsoft.Extensions.FileProviders.Abstractions@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "Abstractions of files and directories.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.fileproviders.abstractions/2.2.0/microsoft.extensions.fileproviders.abstractions.2.2.0.nupkg", "hash": {"value": "df7bd3ca28f301511f6ee345b6cebc47b6d6d36709322c36d4c16030193e5cbfad85c6efdf7e4f543d7e0dd312bcde9ee437804783a63d246c288afce98938aa", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/Extensions", "revision": "9bc79b2f25a3724376d7af19617c33749a30ea3a", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/Extensions.git", "revision": "9bc79b2f25a3724376d7af19617c33749a30ea3a", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Hosting.Abstractions:2.2.0", "purl": "pkg:nuget/Microsoft.Extensions.Hosting.Abstractions@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": ".NET Core hosting and startup abstractions for applications.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.hosting.abstractions/2.2.0/microsoft.extensions.hosting.abstractions.2.2.0.nupkg", "hash": {"value": "52ea6fd1f020315060c7ac4a9e59b84d1ac3f9a4c14015c2af54310c54442bea10e30ac6da50849887aa9f46214b5ec7f13f9b44a5d3c850420792eb3997a55b", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/Hosting", "revision": "0724e6cde1149ee1a19bfec9c13a2c9327b71213", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/Hosting.git", "revision": "0724e6cde1149ee1a19bfec9c13a2c9327b71213", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Http:6.0.0", "purl": "pkg:nuget/Microsoft.Extensions.Http@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "The HttpClient factory is a pattern for configuring and retrieving named HttpClients in a composable way. The HttpClient factory provides extensibility to plug in DelegatingHandlers that address cross-cutting concerns such as service location, load balancing, and reliability. The default HttpClient factory provides built-in diagnostics and logging and manages the lifetimes of connections in a performant way.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.http/6.0.0/microsoft.extensions.http.6.0.0.nupkg", "hash": {"value": "c2fe14af71f407e6260219e9686d21b1a9a80421946850c57afb818c0cfd48f312ab9b4ff780402c7bdc753acd25260095953c780a5cdfa85debf14605f1e1f4", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Logging:6.0.0", "purl": "pkg:nuget/Microsoft.Extensions.Logging@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Logging infrastructure default implementation for Microsoft.Extensions.Logging.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.logging/6.0.0/microsoft.extensions.logging.6.0.0.nupkg", "hash": {"value": "f5dec590a353b7fbb35fda799c083dc07857c70fa0d46daec0e00c4950a8d65f42c6f399a86706e2d97ee51fb5042be27f35637673a2479dc4f623590d597311", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Logging:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.Logging@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Logging infrastructure default implementation for Microsoft.Extensions.Logging.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.logging/8.0.0-preview.6.23329.7/microsoft.extensions.logging.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "89df8f8f710340151f8aa85e8176275feb1f40f3531e382206123e43e3dcd71eb5b2a5d03228e7a4c18d144b115febfdc22d34db2874e1d81ad05de325b7455c", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Logging.Abstractions:6.0.0", "purl": "pkg:nuget/Microsoft.Extensions.Logging.Abstractions@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Logging abstractions for Microsoft.Extensions.Logging.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.logging.abstractions/6.0.0/microsoft.extensions.logging.abstractions.6.0.0.nupkg", "hash": {"value": "bfb1b4b98242104803d1a65a1a051d0b8e481fbc987fa2f4b58a610ab459b4d24e8753c515c32a376dd2c6804d1ce2d39624b972a81c68e92481958e1a8a31df", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Logging.Abstractions:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.Logging.Abstractions@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Logging abstractions for Microsoft.Extensions.Logging.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.logging.abstractions/8.0.0-preview.6.23329.7/microsoft.extensions.logging.abstractions.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "0f979f724eb68978eb0c01b36e293ba295c6a5dec3ae1e545d717ff9a4667ed8aea75057d5dbc4e9a46f35d809df2652fa375c0a588289cbd3c7ca7dece7a449", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:ObjectPool:2.2.0", "purl": "pkg:nuget/Microsoft.Extensions.ObjectPool@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "A simple object pool implementation.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.objectpool/2.2.0/microsoft.extensions.objectpool.2.2.0.nupkg", "hash": {"value": "c954c18d32b3d4b2be3ae89e9b4dc498c35f78e1d9db3028a6d29e634418dcd00877c96e5938ff589a8692aaa27c3a54a420ea7f79bd44917e65fed40e72cd92", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/Extensions", "revision": "9bc79b2f25a3724376d7af19617c33749a30ea3a", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/Extensions.git", "revision": "9bc79b2f25a3724376d7af19617c33749a30ea3a", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Options:6.0.0", "purl": "pkg:nuget/Microsoft.Extensions.Options@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides a strongly typed way of specifying and accessing settings using dependency injection.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.options/6.0.0/microsoft.extensions.options.6.0.0.nupkg", "hash": {"value": "3c34452bac02193264b0563654ba5282a74b03ede2b675ad25fa85ebd915bdcca857158153eb4a4a024065eb4e9d7bc1ebbd23485493cd895c4d049aba4a5dd2", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Options:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.Options@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides a strongly typed way of specifying and accessing settings using dependency injection.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.options/8.0.0-preview.6.23329.7/microsoft.extensions.options.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "c0285feae1f248edcba506a01007c72240abcf03b4b445d3709310749a45dd0d0f5a93d0884dfa046819a2e406df014eafc4d4799c49e45daee609ffdc3761cd", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Primitives:6.0.0", "purl": "pkg:nuget/Microsoft.Extensions.Primitives@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Primitives shared by framework extensions. Commonly used types include:", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.primitives/6.0.0/microsoft.extensions.primitives.6.0.0.nupkg", "hash": {"value": "0b2697f35557aeff0784b10ae6a4eafd7601bf706121ed6584a61879ec6e494514ec7a3e0da0aa6baa99f3f716f69030ec7c4c82f657c8dfdbacb637bac4547f", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:Primitives:8.0.0-preview.6.23329.7", "purl": "pkg:nuget/Microsoft.Extensions.Primitives@8.0.0-preview.6.23329.7", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Primitives shared by framework extensions. Commonly used types include:", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.primitives/8.0.0-preview.6.23329.7/microsoft.extensions.primitives.8.0.0-preview.6.23329.7.nupkg", "hash": {"value": "00e45aa2fdb0391ab6e3d199dc4625a39a51918bb1aaff7c303046e42dddb943b71c2419400f64ae4f350425095b17de73620267e0ff3732e5859df88e0db657", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "5340be2ccc67101dc890c9f1b0ec57470b685f3e", "path": ""}}, {"id": "NuGet:Microsoft.Extensions:WebEncoders:2.2.0", "purl": "pkg:nuget/Microsoft.Extensions.WebEncoders@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "Contains registration and configuration APIs to add the core framework encoders to a dependency injection container.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.extensions.webencoders/2.2.0/microsoft.extensions.webencoders.2.2.0.nupkg", "hash": {"value": "f481d3d75bea1116b97bc72d8d59394d6b7ad2708d85f30a1a9ba2c3f870fc567b509a7eb22e31db068533c34faf1f50a0d4b8bd7131e056a324ba656b6109b1", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HtmlAbstractions", "revision": "6c5ca90d81f9013a8652da4c1752bd0b4d18e908", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HtmlAbstractions.git", "revision": "6c5ca90d81f9013a8652da4c1752bd0b4d18e908", "path": ""}}, {"id": "NuGet:Microsoft.IdentityModel:JsonWebTokens:6.15.1", "purl": "pkg:nuget/Microsoft.IdentityModel.JsonWebTokens@6.15.1", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Includes types that provide support for creating, serializing and validating JSON Web Tokens.", "homepage_url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.identitymodel.jsonwebtokens/6.15.1/microsoft.identitymodel.jsonwebtokens.6.15.1.nupkg", "hash": {"value": "0568d6ba981e8daddf7a0c72421bd9c6905d9783f4a8305c9c1cd48e431bc85bfda0d7cafe30af3452bbc4850f4d0b736a69b42c4605ce476701178c8e40fda7", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet.git", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}}, {"id": "NuGet:Microsoft.IdentityModel:Logging:6.15.1", "purl": "pkg:nuget/Microsoft.IdentityModel.Logging@6.15.1", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Includes Event Source based logging support.", "homepage_url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.identitymodel.logging/6.15.1/microsoft.identitymodel.logging.6.15.1.nupkg", "hash": {"value": "4c7d956407e8a8ca02134bced7d42e6ab98c783c091c04f13ddef691378678eddcb251a3d55bcc3de9ee9bd28de2396afd87d889b03ea952355c072ff69e8598", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet.git", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}}, {"id": "NuGet:Microsoft.IdentityModel:Protocols:6.15.1", "purl": "pkg:nuget/Microsoft.IdentityModel.Protocols@6.15.1", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides base protocol support for OpenIdConnect and WsFederation.", "homepage_url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.identitymodel.protocols/6.15.1/microsoft.identitymodel.protocols.6.15.1.nupkg", "hash": {"value": "ff9c3a15efb8c5d0c982d5f3e4afa65e5e55b38947a50d283cfcda28717525f3ec7ff194d25882fda6ed6e998d46f4f3753d33c5d9d69fa6cbe51b8bb9bcf319", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet.git", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}}, {"id": "NuGet:Microsoft.IdentityModel:Protocols.OpenIdConnect:6.15.1", "purl": "pkg:nuget/Microsoft.IdentityModel.Protocols.OpenIdConnect@6.15.1", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Includes types that provide support for OpenIdConnect protocol.", "homepage_url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.identitymodel.protocols.openidconnect/6.15.1/microsoft.identitymodel.protocols.openidconnect.6.15.1.nupkg", "hash": {"value": "5b664d6989b4f6a419bea87f932b67c5904bb3df8edaa5facedff0cf8b6e3b4e0d5b13c97779d573a17f37c7b59a63462d476de7fafdbad7987225f865ec7830", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet.git", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}}, {"id": "NuGet:Microsoft.IdentityModel:Tokens:6.15.1", "purl": "pkg:nuget/Microsoft.IdentityModel.Tokens@6.15.1", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Includes types that provide support for SecurityTokens, Cryptographic operations: Signing, Verifying Signatures, Encryption.", "homepage_url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.identitymodel.tokens/6.15.1/microsoft.identitymodel.tokens.6.15.1.nupkg", "hash": {"value": "6ced20e8df3a9a1beede10af5b89cb8f00d9f1fd8cae4330ed89cc0d4fa668db69d0f5fa4169a59bccfd848c4043ac6d963866ccd133b14b109e851693706573", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet.git", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}}, {"id": "NuGet:Microsoft.NETCore:Platforms:2.0.0", "purl": "pkg:nuget/Microsoft.NETCore.Platforms@2.0.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides runtime information required to resolve target framework, platform, and runtime specific implementations of .NETCore packages. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.netcore.platforms/2.0.0/microsoft.netcore.platforms.2.0.0.nupkg", "hash": {"value": "0827f83639833a88ac7bb1408a3d953ee1c880a2acbbaf7abe44f084e90f5507cbb13981d962c57d0e3278ee5476d93c143eb7e9404cc7a63d7a8bf324a4fbe8", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:Microsoft.NETCore:Targets:1.1.0", "purl": "pkg:nuget/Microsoft.NETCore.Targets@1.1.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides supporting infrastructure for portable projects: support identifiers that define framework and runtime for support targets and packages that reference the minimum supported package versions when targeting these. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.netcore.targets/1.1.0/microsoft.netcore.targets.1.1.0.nupkg", "hash": {"value": "1ef033a68688aab9997ec1c0378acb1638b4afb618e533fcaf749d93389737ba94f4a0a94481becdf701c7e988ae2fe390136a8eae225887ee60db45063490fe", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:Microsoft.Net:Http.Headers:2.2.0", "purl": "pkg:nuget/Microsoft.Net.Http.Headers@2.2.0", "authors": ["Microsoft"], "declared_licenses": ["https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0", "mapped": {"https://raw.githubusercontent.com/aspnet/AspNetCore/2.0.0/LICENSE.txt": "Apache-2.0"}}, "description": "HTTP header parser implementations.", "homepage_url": "https://asp.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.net.http.headers/2.2.0/microsoft.net.http.headers.2.2.0.nupkg", "hash": {"value": "7a28a6cce28280cc8751347aeb4e190d90e97dcfb930ad1524010fb01a3cc85f7a6415c7452d8445eed29327dfa0437cb33ff434b6b43059e06f90f03b04ea65", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/aspnet/HttpAbstractions", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/aspnet/HttpAbstractions.git", "revision": "91db78cf926939821bc96e8e60616cf5dde0b489", "path": ""}}, {"id": "NuGet:Microsoft.Win32:Registry:4.5.0", "purl": "pkg:nuget/Microsoft.Win32.Registry@4.5.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides support for accessing and modifying the Windows Registry.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/microsoft.win32.registry/4.5.0/microsoft.win32.registry.4.5.0.nupkg", "hash": {"value": "2ca99fd058a083064184da33d12f72a5c43412151d426c309e2284a5df4b722b2f26dc72616ab6452a24ed81693839b756861d47eea27d60d7db7ff6749ab664", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System:Buffers:4.5.0", "purl": "pkg:nuget/System.Buffers@4.5.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides resource pooling of any type for performance-critical applications that allocate and deallocate objects frequently.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.buffers/4.5.0/system.buffers.4.5.0.nupkg", "hash": {"value": "d3dc8bfd088f103648f492e0d11d0e7067bfd327059baf50375e830af5e4aa4228ac20b563610ac24e4abd295f3261ac7be2dc2a40f71fe0ab6bb7c59311d712", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System:Collections:4.3.0", "purl": "pkg:nuget/System.Collections@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides classes that define generic collections, which allow developers to create strongly typed collections that provide better type safety and performance than non-generic strongly typed collections.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.collections/4.3.0/system.collections.4.3.0.nupkg", "hash": {"value": "ca7b952d30da1487ca4e43aa522817b5ee26e7e10537062810112fc67a7512766c39d402f394bb0426d1108bbcf9bbb64e9ce1f5af736ef215a51a35e55f051b", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System:Globalization:4.3.0", "purl": "pkg:nuget/System.Globalization@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides classes that define culture-related information, including language, country/region, calendars in use, format patterns for dates, currency, and numbers, and sort order for strings.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.globalization/4.3.0/system.globalization.4.3.0.nupkg", "hash": {"value": "823d2ba308cb073b40a3146ecccd0d9fd7b1615ac3fbefb16f73d873e411fd81c3bdc87df206d3dc7e2f14c9cd53aafca684a3570c25471280aada8de805ece2", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System:IO:4.3.0", "purl": "pkg:nuget/System.IO@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides base input and output (I/O) types, including System.IO.Stream, System.IO.StreamReader and System.IO.StreamWriter, that allow reading and writing to data streams", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.io/4.3.0/system.io.4.3.0.nupkg", "hash": {"value": "bfca5a21e3e1986b9765b13dc6fbcd6f8b89e4c1383855d1d7ef256bf1bf2f51889769db5365859dd7606fbf6454add4daeb3bab56994ffb98fd1d03fe8bc1e6", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System:Linq:4.3.0", "purl": "pkg:nuget/System.Linq@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides classes and interfaces that supports queries that use Language-Integrated Query (LINQ).", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.linq/4.3.0/system.linq.4.3.0.nupkg", "hash": {"value": "eacc7fe1ec526f405f5ba0e671f616d0e5be9c1828d543a9e2f8c65df4099d6b2ea4a9fa2cdae4f34b170dc37142f60e267e137ca39f350281ed70d2dc620458", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System:Memory:4.5.3", "purl": "pkg:nuget/System.Memory@4.5.3", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides types for efficient representation and pooling of managed, stack, and native memory segments and sequences of such segments, along with primitives to parse and format UTF-8 encoded text stored in those memory segments.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.memory/4.5.3/system.memory.4.5.3.nupkg", "hash": {"value": "70fce15a52cc76aacbae05c8e89e2e398d1d32903f63f640a7dd4a3e5747f2c7a887d4bfd22f2a2e40274906cf91648dfd169734fb7c74eb9b4f72614084e1db", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System:Reflection:4.3.0", "purl": "pkg:nuget/System.Reflection@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides types that retrieve information about assemblies, modules, members, parameters, and other entities in managed code by examining their metadata. These types also can be used to manipulate instances of loaded types, for example to hook up events or to invoke methods.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.reflection/4.3.0/system.reflection.4.3.0.nupkg", "hash": {"value": "2325b67ed60dce0302807064f25422cbe1b7fb275b539b44fba3c4a8ce4926f21d78529a5c34b31c03d80d110f7bace9af9589d457266beac014220057af8333", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System:Runtime:4.3.0", "purl": "pkg:nuget/System.Runtime@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides the fundamental primitives, classes and base classes that define commonly-used value and reference data types, events and event handlers, interfaces, attributes, and exceptions. This packages represents the core package, and provides the minimal set of types required to build a managed application.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.runtime/4.3.0/system.runtime.4.3.0.nupkg", "hash": {"value": "92ab2249f08073cfafdc4cfbd7db36d651ad871b8d8ba961006982187de374bf4a30af93f15f73b05af343f7a70cbd484b04d646570587636ae72171eb0714fb", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System:Threading:4.3.0", "purl": "pkg:nuget/System.Threading@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides the fundamental synchronization primitives, including System.Threading.Monitor and System.Threading.Mutex, that are required when writing asynchronous code.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.threading/4.3.0/system.threading.4.3.0.nupkg", "hash": {"value": "97a2751bdce69faaf9c54f834a9fd5c60c7a786faa52f420769828dbc9b5804c1f3721ba1ea945ea1d844835d909810f9e782c9a44d0faaecccb230c4cd95a88", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Collections:Concurrent:4.3.0", "purl": "pkg:nuget/System.Collections.Concurrent@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides several thread-safe collection classes that should be used in place of the corresponding types in the System.Collections.NonGeneric and System.Collections packages whenever multiple threads are accessing the collection concurrently.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.collections.concurrent/4.3.0/system.collections.concurrent.4.3.0.nupkg", "hash": {"value": "35c1aa3e636216fe5dc2ebeb504293e69ad6355d26e22453af060af94d8279faa93bdcfe127aecb0b316c7e7d9185bcac72e994984efdb7f2d8515f1f55cf682", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Diagnostics:Debug:4.3.0", "purl": "pkg:nuget/System.Diagnostics.Debug@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides classes and attributes that allows basic interaction with a debugger.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.diagnostics.debug/4.3.0/system.diagnostics.debug.4.3.0.nupkg", "hash": {"value": "6c58fe1e3618e7f87684c1cea7efc7d3b19bd7df8d2535f9e27b62c52f441f11b67b21225d6bcd62f409e02c2a16231c4db19be33b8fab5b9b0a5c8660ddab24", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Diagnostics:DiagnosticSource:6.0.0", "purl": "pkg:nuget/System.Diagnostics.DiagnosticSource@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides Classes that allow you to decouple code logging rich (unserializable) diagnostics/telemetry (e.g. framework) from code that consumes it (e.g. tools)", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.diagnostics.diagnosticsource/6.0.0/system.diagnostics.diagnosticsource.6.0.0.nupkg", "hash": {"value": "7589e6a3ae9b8ad7c7c4b8cd054d8b3e9e839fdf27ee147293b64a195cda00fc36307cbee3474bc5fc3bb2eb3132459f2f70bffda245fbf50300f807d9885466", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:System.Diagnostics:Tracing:4.3.0", "purl": "pkg:nuget/System.Diagnostics.Tracing@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides class that enable you to create high performance tracing events to be captured by event tracing for Windows (ETW).", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.diagnostics.tracing/4.3.0/system.diagnostics.tracing.4.3.0.nupkg", "hash": {"value": "d0a5d30e261cd45b7dfab02b7ffbd76b64e0c9b892ed826ea61481c983c0208b05b69981cd79e91cd4e5811e1cd4c3cea06a1afce05811ece58be5e4c20169ea", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.IdentityModel:Tokens.Jwt:6.15.1", "purl": "pkg:nuget/System.IdentityModel.Tokens.Jwt@6.15.1", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Includes types that provide support for creating, serializing and validating JSON Web Tokens.", "homepage_url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.identitymodel.tokens.jwt/6.15.1/system.identitymodel.tokens.jwt.6.15.1.nupkg", "hash": {"value": "f28e616fc965aae6fe142f08450e8eafc6414f1c9d305bc008543ebef5fe25484482883df1b7e28b72acbcb7186c63026b97571099feffe5ba59c28b2c6b3232", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet.git", "revision": "9e6f90a9a8032107af6f5c851e9d257f4c6c8fdc", "path": ""}}, {"id": "NuGet:System.Reflection:Primitives:4.3.0", "purl": "pkg:nuget/System.Reflection.Primitives@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides common enumerations for reflection-based libraries.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.reflection.primitives/4.3.0/system.reflection.primitives.4.3.0.nupkg", "hash": {"value": "d4b9cc905f5a5cab900206338e889068bf66c18ee863a29d68eff3cde2ccca734112a2a851f2e2e5388a21ec28005fa19317c64d9b23923b05d6344be2e49eaa", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Resources:ResourceManager:4.3.0", "purl": "pkg:nuget/System.Resources.ResourceManager@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides classes and attributes that allow developers to create, store, and manage various culture-specific resources used in an application.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.resources.resourcemanager/4.3.0/system.resources.resourcemanager.4.3.0.nupkg", "hash": {"value": "9067db28f1c48d08fc52ad40a608f88c14ad9112646741ddaf426fdfe68bed61ab01954b179461e61d187371600c1e6e5c36c788993f5a105a64f5702a6b81d4", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Runtime:CompilerServices.Unsafe:6.0.0", "purl": "pkg:nuget/System.Runtime.CompilerServices.Unsafe@6.0.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides the System.Runtime.CompilerServices.Unsafe class, which provides generic, low-level functionality for manipulating pointers.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.runtime.compilerservices.unsafe/6.0.0/system.runtime.compilerservices.unsafe.6.0.0.nupkg", "hash": {"value": "d4057301be4ec4936f24b9ce003b5ec4d99681ab6d9b65d5393dd38d04cdec37784aaa12c1a8b50ac3767ed878dae425749490773fec01e734f93cf1045822b3", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/dotnet/runtime", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "4822e3c3aa77eb82b2fb33c9321f923cf11ddde6", "path": ""}}, {"id": "NuGet:System.Runtime:Extensions:4.3.0", "purl": "pkg:nuget/System.Runtime.Extensions@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides commonly-used classes for performing mathematical functions, conversions, string comparisons and querying environment information.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.runtime.extensions/4.3.0/system.runtime.extensions.4.3.0.nupkg", "hash": {"value": "680a32b19c2bd5026f8687aa5382aea4f432b4f032f8bde299facb618c56d57369adef7f7cc8e60ad82ae3c12e5dd50772491363bf8044c778778628a6605bbc", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Runtime:Handles:4.3.0", "purl": "pkg:nuget/System.Runtime.Handles@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides base classes, including System.Runtime.InteropServices.CriticalHandle and System.Runtime.InteropServices.SafeHandle, for types that represent operating system handles.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.runtime.handles/4.3.0/system.runtime.handles.4.3.0.nupkg", "hash": {"value": "0a5baf1dd554bf9e01bcb4ce082cb26ee82b783364feb47cba730faeecd70edc528efad0394dcce11f37d7f9507f8608f15629ebaf051906bfd3513e46af0f11", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Runtime:InteropServices:4.3.0", "purl": "pkg:nuget/System.Runtime.InteropServices@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides types that support COM interop and platform invoke services.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.runtime.interopservices/4.3.0/system.runtime.interopservices.4.3.0.nupkg", "hash": {"value": "650799c3e654efbb9ad67157c9c60ce46f288a81597be37ce2a0bf5d4835044065ef3f65b997328cbbbbfb81f4c89b8d7e7d61380880019deee6eb3f963f70d9", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Runtime:Numerics:4.3.0", "purl": "pkg:nuget/System.Runtime.Numerics@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides the numeric types System.Numerics.BigInteger and System.Numerics.Complex, which complement the numeric primitives, such as System.Byte, System.Double and System.Int32.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.runtime.numerics/4.3.0/system.runtime.numerics.4.3.0.nupkg", "hash": {"value": "3e347faa8e7ec484d481e53b1c219fe1ce346ae8278a214b4508cf0e233c1627bd9c6c6c7c654e8c1f4143271838ddd9593f63a1043577ad87c40e392af7fd34", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Security:AccessControl:4.5.0", "purl": "pkg:nuget/System.Security.AccessControl@4.5.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides base classes that enable managing access and audit control lists on securable objects.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.accesscontrol/4.5.0/system.security.accesscontrol.4.5.0.nupkg", "hash": {"value": "e9142d713f93c8380b505b009e699d7d144674b60ac526469123ce774e76b6f605c4e4cc6906fa00d970846a99b4d3b9d8fa2c682a17bbbb9ab459deba303198", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Security:Cryptography.Algorithms:4.3.1", "purl": "pkg:nuget/System.Security.Cryptography.Algorithms@4.3.1", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides base types for cryptographic algorithms, including hashing, encryption, and signing operations.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.cryptography.algorithms/4.3.1/system.security.cryptography.algorithms.4.3.1.nupkg", "hash": {"value": "34b02b60b69a54e1310b511512e3210a49bfb0584c50f80b59c586d7180ad973e3b5419f7a55783837884395460db3e0db13c8fb27de6d170b8d2dab1c90adcf", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Security:Cryptography.Cng:4.7.0", "purl": "pkg:nuget/System.Security.Cryptography.Cng@4.7.0", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides cryptographic algorithm implementations and key management with Windows Cryptographic Next Generation API (CNG).", "homepage_url": "https://github.com/dotnet/corefx", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.cryptography.cng/4.7.0/system.security.cryptography.cng.4.7.0.nupkg", "hash": {"value": "b0ee54be292ea15b02b82d9925399065deb6dae5aa1bb71771bb9467e8f53882b26a0ddc6ff43121b4d2999c5858399e61a779e04d14a4f4e8e0dfcf8baebbba", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/corefx.git", "revision": "", "path": ""}}, {"id": "NuGet:System.Security:Cryptography.Encoding:4.3.0", "purl": "pkg:nuget/System.Security.Cryptography.Encoding@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides types for representing Abstract Syntax Notation One (ASN.1)-encoded data.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.cryptography.encoding/4.3.0/system.security.cryptography.encoding.4.3.0.nupkg", "hash": {"value": "5c26add23e63542f37506f5fa1f72e8980f03743d529cd8e583d1054b8d8a579fb773fa035a00d9073db84db6be4f47cac340d1ebc6d23dd761dbdbd600075e0", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Security:Cryptography.Pkcs:4.5.0", "purl": "pkg:nuget/System.Security.Cryptography.Pkcs@4.5.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides support for PKCS and CMS algorithms.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.cryptography.pkcs/4.5.0/system.security.cryptography.pkcs.4.5.0.nupkg", "hash": {"value": "a4fb3d1528ef5f2ae99b9e393277815021a68c556fae06bec09d7348f5757d8dff478c39341197a5eaf1d9d63ce58be80efa2db3e99b2f00c0a8d6ff511f037a", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Security:Cryptography.Primitives:4.3.0", "purl": "pkg:nuget/System.Security.Cryptography.Primitives@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides common types for the cryptographic libraries.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.cryptography.primitives/4.3.0/system.security.cryptography.primitives.4.3.0.nupkg", "hash": {"value": "5ad8273f998ebb9cca2f7bd03143d3f6d57b5d560657b26d6f4e78d038010fb30c379a23a27c08730f15c9b66f4ba565a06984ec246dfc79acf1a741b0dd4347", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Security:Cryptography.Xml:4.5.0", "purl": "pkg:nuget/System.Security.Cryptography.Xml@4.5.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides classes to support the creation and validation of XML digital signatures. The classes in this namespace implement the World Wide Web Consortium Recommendation, \"XML-Signature Syntax and Processing\", described at http://www.w3.org/TR/xmldsig-core/.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.cryptography.xml/4.5.0/system.security.cryptography.xml.4.5.0.nupkg", "hash": {"value": "5fefed68b5d3298ce22af584e19132869634410d7b7e6f5e5bbd1cbcc084e8b02c598a9dbfbd3c8b41c9792572effe51e79e3b191fc21063a352462ae9b25f3e", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Security:Permissions:4.5.0", "purl": "pkg:nuget/System.Security.Permissions@4.5.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides types supporting Code Access Security (CAS). ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.permissions/4.5.0/system.security.permissions.4.5.0.nupkg", "hash": {"value": "3be7bda9a9924c9e183890a99cb82c7fd15319fb3126fd8e9f539d62486677f5c0e40611bd46ad933ed1a77752f5747bae156e9259e3493d5d46830aecde1c1b", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Security:Principal.Windows:4.5.0", "purl": "pkg:nuget/System.Security.Principal.Windows@4.5.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides classes for retrieving the current Windows user and for interacting with Windows users and groups.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.security.principal.windows/4.5.0/system.security.principal.windows.4.5.0.nupkg", "hash": {"value": "86cdb3178b4e437578890b6d5672eb9d1fe2f003abac082ed869a9e3f8cd684ffee618995838f6d052bf9bf396dc8b5d8bd5c3bea7f9e56cc7922598b4e49436", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Text:Encoding:4.3.0", "purl": "pkg:nuget/System.Text.Encoding@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides base abstract encoding classes for converting blocks of characters to and from blocks of bytes.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.text.encoding/4.3.0/system.text.encoding.4.3.0.nupkg", "hash": {"value": "6ff7feec7313a7121f795ec7d376e4b8728c17294219fafdfd4ea078f9df1455b4685f0b3962c3810098e95d68594a8392c0b799d36ec8284cd6fcbd4cfe2c67", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Text:Encodings.Web:4.5.0", "purl": "pkg:nuget/System.Text.Encodings.Web@4.5.0", "authors": ["Microsoft"], "declared_licenses": ["https://github.com/dotnet/corefx/blob/master/LICENSE.TXT"], "declared_licenses_processed": {"spdx_expression": "MIT", "mapped": {"https://github.com/dotnet/corefx/blob/master/LICENSE.TXT": "MIT"}}, "description": "Provides types for encoding and escaping strings for use in JavaScript, HyperText Markup Language (HTML), and uniform resource locators (URL).", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.text.encodings.web/4.5.0/system.text.encodings.web.4.5.0.nupkg", "hash": {"value": "f802fbbcfe00a5f552092c6987033f7cd794a7b8a3ed6fc6b9b7378c12bdc081b94a7ced869447a4a79322eb47457973ba497daa07c6a94ca64388cf9282a279", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:System.Text:<PERSON>son:5.0.2", "purl": "pkg:nuget/System.Text.Json@5.0.2", "authors": ["Microsoft"], "declared_licenses": ["MIT"], "declared_licenses_processed": {"spdx_expression": "MIT"}, "description": "Provides high-performance and low-allocating types that serialize objects to JavaScript Object Notation (JSON) text and deserialize JSON text to objects, with UTF-8 support built-in. Also provides types to read and write JSON text encoded as UTF-8, and to create an in-memory document object model (DOM), that is read-only, for random access of the JSON elements within a structured view of the data.", "homepage_url": "https://github.com/dotnet/runtime", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.text.json/5.0.2/system.text.json.5.0.2.nupkg", "hash": {"value": "3d32f887630b6ca12a67ff5313a484989a75b71221d068f360f42b5866df279b206cfae95f36fdb0ea177b7722779d730c113ed8a08b77de5eff4e0988daf44d", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+git://github.com/dotnet/runtime", "revision": "2f740adc1457e8a28c1c072993b66f515977eb51", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/dotnet/runtime.git", "revision": "2f740adc1457e8a28c1c072993b66f515977eb51", "path": ""}}, {"id": "NuGet:System.Threading:Tasks:4.3.0", "purl": "pkg:nuget/System.Threading.Tasks@4.3.0", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Provides types that simplify the work of writing concurrent and asynchronous code.", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/system.threading.tasks/4.3.0/system.threading.tasks.4.3.0.nupkg", "hash": {"value": "7d488ff82cb20a3b3cef6380f2dae5ea9f7baa66bf75ad711aade1e3301b25993ccf2694e33c847ea5b9bdb90ff34c46fcd8a6ba7d6f95605ba0c124ed7c5d13", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:Zitadel:gRPC:5.2.25", "purl": "pkg:nuget/Zitadel.gRPC@5.2.25", "authors": ["<PERSON>, smartive AG"], "declared_licenses": ["Apache-2.0"], "declared_licenses_processed": {"spdx_expression": "Apache-2.0"}, "description": "This package contains the compiled *.proto definitions of the zitadel repository.", "homepage_url": "https://github.com/smartive/zitadel-net", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/zitadel.grpc/5.2.25/zitadel.grpc.5.2.25.nupkg", "hash": {"value": "d1d4b8b58fceb25d25e010c854b972478032a49735c4785f9f2280f0bfa8596278162a6b47ff0a3eb381153f60ba9d9b1d4a91a22003f2f79d759f56b18feb6b", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "git+https://github.com/smartive/zitadel-net.git", "revision": "", "path": ""}, "vcs_processed": {"type": "Git", "url": "https://github.com/smartive/zitadel-net.git", "revision": "", "path": ""}}, {"id": "NuGet:runtime.debian:8-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "8f071552ee042f0cba39b1ba0a1205cf73de447d662995bae68f857a5946f7d154c029a79e37469081675687873c8bf2b9efe57f5cbd660c366b1ca51823f7f2", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.fedora:23-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "a135ca0f4f5a49319b5a52b7f4338f8a5fc4387edf26f29e6cbf63a3c3a37b2b5c51c9caf562ec41e470fba281060362465bc56915be782d6c75778aa6195e46", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.fedora:24-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "2f24e2cba88a96bb23848e1404878e4478a65642387b7b76aa4007587fe7c4d8208cbde53d3ed65f8d0d71cd688bfc16be66dc5f7bcf84c7b2ccf1b3c505b0b4", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.native:System.Security.Cryptography.Apple:4.3.1", "purl": "pkg:nuget/runtime.native.System.Security.Cryptography.Apple@4.3.1", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.native.system.security.cryptography.apple/4.3.1/runtime.native.system.security.cryptography.apple.4.3.1.nupkg", "hash": {"value": "0be0195264011605d1fcdc45f34ab44b2ed2818896f82dc2708b80ef27747f515758dd7ac612114a65cdf76214486c7f79b18ddd799a7ae55466977e36e63f8d", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.native:System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.native.system.security.cryptography.openssl/4.3.2/runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "a34ad2dbe67efcae97fcbea57af386b30660a98ab8229a56c0dca241316e673cf7a26e19c6efb6b7117cc271fdf208741ba6f8447ae254c91acba3ddb7d2923a", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.opensuse:13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "ce0873e07000df98e640bd265e969d4a9236535cee2699c5363f3ab297557b0d756260557995f2ee163cff05fc2ba922d66ba0e4cb28521f841e7d546ab3b63e", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.opensuse:42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "bf754c1a66cd70dc1bd38d54fe675e9dd470417ebba62e2f79e278be8f06cc3496ff58ed90d30b5dd4d3efea9accbd09eb17cd87be882951c0fdfb833c371f70", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.osx:10.10-x64.runtime.native.System.Security.Cryptography.Apple:4.3.1", "purl": "pkg:nuget/runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple@4.3.1", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.1/runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.1.nupkg", "hash": {"value": "61578fe9771c6c97a7d64dec8df9ed8ea592faf01c56ddfb556dde06e66f8c7ed9953990cf7b7a9fc80b361df8d8878348133ed3a48f62d06a3f5ae80252290c", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.osx:10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "0a38f25e8773b58155b5d3f94f849b93353d0809da56228b8ebab5c976e6458ca50eb5a38acca4c8940678e6e9521fb57ae487337f7cbf2ea7893ae9e3f43935", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.rhel:7-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "2ae9db4b719b31fa7e40c60f52c70038fc8668e029cf4e1d120fde8c295631d6b08207d7018a22937b79546016c560c894e27dd6ebc01d5e0f677567e6b2c4f2", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.ubuntu:14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "cd4b7ba744de80086521ab67cad2db3085d488388d3d9cb83d9946389f0f4c784539bf3a4ffb8d4f3347c5c7813aadef95b355fd2563e30c948a883c27b95287", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.ubuntu:16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "d7fc28a9f600e471edce0989c01c485d4e2a7e99551f531413afa75039a4004d4e2c27e88976d65432635a321d86316a3c6cdaebc7b2fefa42141b64f4f10d66", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}, {"id": "NuGet:runtime.ubuntu:16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "purl": "pkg:nuget/runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl@4.3.2", "authors": ["Microsoft"], "declared_licenses": ["http://go.microsoft.com/fwlink/?LinkId=329770"], "declared_licenses_processed": {"spdx_expression": "LicenseRef-scancode-ms-net-library-2019-06", "mapped": {"http://go.microsoft.com/fwlink/?LinkId=329770": "LicenseRef-scancode-ms-net-library-2019-06"}}, "description": "Internal implementation package not meant for direct consumption.  Please do not reference directly. ", "homepage_url": "https://dot.net/", "binary_artifact": {"url": "https://api.nuget.org/v3-flatcontainer/runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2/runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg", "hash": {"value": "5fe0e6a878eff59cfe24a8d57af51140576d8e0fec4988e4892c233c47b3a3eed27dec072a6c0d55dd615777cd9ce3fe545c5353b4a95289376ad0b9408ed4be", "algorithm": "SHA-512"}}, "source_artifact": {"url": "", "hash": {"value": "", "algorithm": ""}}, "vcs": {"type": "", "url": "", "revision": "", "path": ""}, "vcs_processed": {"type": "", "url": "", "revision": "", "path": ""}}], "dependency_graphs": {"NuGet": {"packages": ["NuGet::Grpc:2.46.6", "NuGet::IdentityModel:6.0.0", "NuGet::Serilog:3.0.2-dev-02044", "NuGet::Zitadel:5.2.25", "NuGet::jose-jwt:4.1.0", "NuGet:BouncyCastle:Cryptography:2.2.1", "NuGet:Google.Api:CommonProtos:2.10.0", "NuGet:Google:Protobuf:3.24.3", "NuGet:Grpc.Core:Api:2.57.0", "NuGet:Grpc.Net:Client:2.57.0", "NuGet:Grpc.Net:ClientFactory:2.57.0", "NuGet:Grpc.Net:Common:2.57.0", "NuGet:Grpc:Core:2.46.6", "NuGet:IdentityModel.AspNetCore:OAuth2Introspection:6.2.0", "NuGet:Microsoft.AspNetCore:Authentication.Abstractions:2.2.0", "NuGet:Microsoft.AspNetCore:Authentication.Core:2.2.0", "NuGet:Microsoft.AspNetCore:Authentication.JwtBearer:7.0.11", "NuGet:Microsoft.AspNetCore:Authentication.OpenIdConnect:7.0.11", "NuGet:Microsoft.AspNetCore:Authentication:2.2.0", "NuGet:Microsoft.AspNetCore:Cryptography.Internal:2.2.0", "NuGet:Microsoft.AspNetCore:DataProtection.Abstractions:2.2.0", "NuGet:Microsoft.AspNetCore:DataProtection:2.2.0", "NuGet:Microsoft.AspNetCore:Hosting.Abstractions:2.2.0", "NuGet:Microsoft.AspNetCore:Hosting.Server.Abstractions:2.2.0", "NuGet:Microsoft.AspNetCore:Http.Abstractions:2.2.0", "NuGet:Microsoft.AspNetCore:Http.Extensions:2.2.0", "NuGet:Microsoft.AspNetCore:Http.Features:2.2.0", "NuGet:Microsoft.AspNetCore:Http:2.2.0", "NuGet:Microsoft.AspNetCore:WebUtilities:2.2.0", "NuGet:Microsoft.EntityFrameworkCore:Abstractions:8.0.0-preview.6.23329.4", "NuGet:Microsoft.EntityFrameworkCore:Analyzers:8.0.0-preview.6.23329.4", "NuGet:Microsoft.Extensions:Caching.Abstractions:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:Caching.Memory:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:Configuration.Abstractions:2.2.0", "NuGet:Microsoft.Extensions:DependencyInjection.Abstractions:6.0.0", "NuGet:Microsoft.Extensions:DependencyInjection.Abstractions:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:DependencyInjection:6.0.0", "NuGet:Microsoft.Extensions:DependencyInjection:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:FileProviders.Abstractions:2.2.0", "NuGet:Microsoft.Extensions:Hosting.Abstractions:2.2.0", "NuGet:Microsoft.Extensions:Http:6.0.0", "NuGet:Microsoft.Extensions:Logging.Abstractions:6.0.0", "NuGet:Microsoft.Extensions:Logging.Abstractions:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:Logging:6.0.0", "NuGet:Microsoft.Extensions:Logging:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:ObjectPool:2.2.0", "NuGet:Microsoft.Extensions:Options:6.0.0", "NuGet:Microsoft.Extensions:Options:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:Primitives:6.0.0", "NuGet:Microsoft.Extensions:Primitives:8.0.0-preview.6.23329.7", "NuGet:Microsoft.Extensions:WebEncoders:2.2.0", "NuGet:Microsoft.IdentityModel:JsonWebTokens:6.15.1", "NuGet:Microsoft.IdentityModel:Logging:6.15.1", "NuGet:Microsoft.IdentityModel:Protocols.OpenIdConnect:6.15.1", "NuGet:Microsoft.IdentityModel:Protocols:6.15.1", "NuGet:Microsoft.IdentityModel:Tokens:6.15.1", "NuGet:Microsoft.NETCore:Platforms:2.0.0", "NuGet:Microsoft.NETCore:Targets:1.1.0", "NuGet:Microsoft.Net:Http.Headers:2.2.0", "NuGet:Microsoft.Win32:Registry:4.5.0", "NuGet:Microsoft:CSharp:4.5.0", "NuGet:Microsoft:EntityFrameworkCore:8.0.0-preview.6.23329.4", "NuGet:System.Collections:Concurrent:4.3.0", "NuGet:System.Diagnostics:Debug:4.3.0", "NuGet:System.Diagnostics:DiagnosticSource:6.0.0", "NuGet:System.Diagnostics:Tracing:4.3.0", "NuGet:System.IdentityModel:Tokens.Jwt:6.15.1", "NuGet:System.Reflection:Primitives:4.3.0", "NuGet:System.Resources:ResourceManager:4.3.0", "NuGet:System.Runtime:CompilerServices.Unsafe:6.0.0", "NuGet:System.Runtime:Extensions:4.3.0", "NuGet:System.Runtime:Handles:4.3.0", "NuGet:System.Runtime:InteropServices:4.3.0", "NuGet:System.Runtime:Numerics:4.3.0", "NuGet:System.Security:AccessControl:4.5.0", "NuGet:System.Security:Cryptography.Algorithms:4.3.1", "NuGet:System.Security:Cryptography.Cng:4.7.0", "NuGet:System.Security:Cryptography.Encoding:4.3.0", "NuGet:System.Security:Cryptography.Pkcs:4.5.0", "NuGet:System.Security:Cryptography.Primitives:4.3.0", "NuGet:System.Security:Cryptography.Xml:4.5.0", "NuGet:System.Security:Permissions:4.5.0", "NuGet:System.Security:Principal.Windows:4.5.0", "NuGet:System.Text:Encoding:4.3.0", "NuGet:System.Text:Encodings.Web:4.5.0", "NuGet:System.Text:<PERSON>son:5.0.2", "NuGet:System.Threading:Tasks:4.3.0", "NuGet:System:Buffers:4.5.0", "NuGet:System:Collections:4.3.0", "NuGet:System:Globalization:4.3.0", "NuGet:System:IO:4.3.0", "NuGet:System:Linq:4.3.0", "NuGet:System:Memory:4.5.3", "NuGet:System:Reflection:4.3.0", "NuGet:System:Runtime:4.3.0", "NuGet:System:Threading:4.3.0", "NuGet:Zitadel:gRPC:5.2.25", "NuGet:runtime.debian:8-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "NuGet:runtime.fedora:23-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "NuGet:runtime.fedora:24-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "NuGet:runtime.native:System.Security.Cryptography.Apple:4.3.1", "NuGet:runtime.native:System.Security.Cryptography.OpenSsl:4.3.2", "NuGet:runtime.opensuse:13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "NuGet:runtime.opensuse:42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "NuGet:runtime.osx:10.10-x64.runtime.native.System.Security.Cryptography.Apple:4.3.1", "NuGet:runtime.osx:10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "NuGet:runtime.rhel:7-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "NuGet:runtime.ubuntu:14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "NuGet:runtime.ubuntu:16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2", "NuGet:runtime.ubuntu:16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl:4.3.2"], "scopes": {":EntityInterfaces/EntityInterfaces.csproj::net8.0": [{"root": 2}, {"root": 29}, {"root": 30}, {"root": 31}, {"root": 32}, {"root": 35}, {"root": 37}, {"root": 42}, {"root": 44}, {"root": 47}, {"root": 49}, {"root": 61}], ":FrontendDtos/FrontendDtos.csproj::net8.0": [{"root": 2}], ":ZitadelApiInterface/ZitadelApiInterface.csproj::net8.0": [{"root": 0}, {"root": 1}, {"root": 3}, {"root": 4}, {"root": 5}, {"root": 6}, {"root": 7}, {"root": 8}, {"root": 9}, {"root": 10}, {"root": 11}, {"root": 12}, {"root": 13}, {"root": 14}, {"root": 15}, {"root": 16}, {"root": 17}, {"root": 18}, {"root": 19}, {"root": 20}, {"root": 21}, {"root": 22}, {"root": 23}, {"root": 24}, {"root": 25}, {"root": 26}, {"root": 27}, {"root": 28}, {"root": 33}, {"root": 34}, {"root": 36}, {"root": 38}, {"root": 39}, {"root": 40}, {"root": 41}, {"root": 43}, {"root": 45}, {"root": 46}, {"root": 48}, {"root": 50}, {"root": 51}, {"root": 52}, {"root": 53}, {"root": 54}, {"root": 55}, {"root": 56}, {"root": 57}, {"root": 58}, {"root": 59}, {"root": 60}, {"root": 62}, {"root": 63}, {"root": 64}, {"root": 65}, {"root": 66}, {"root": 67}, {"root": 68}, {"root": 69}, {"root": 70}, {"root": 71}, {"root": 72}, {"root": 73}, {"root": 74}, {"root": 75}, {"root": 76}, {"root": 77}, {"root": 78}, {"root": 79}, {"root": 80}, {"root": 81}, {"root": 82}, {"root": 83}, {"root": 84}, {"root": 85}, {"root": 86}, {"root": 87}, {"root": 88}, {"root": 89}, {"root": 90}, {"root": 91}, {"root": 92}, {"root": 93}, {"root": 94}, {"root": 95}, {"root": 96}, {"root": 97}, {"root": 98}, {"root": 99}, {"root": 100}, {"root": 101}, {"root": 102}, {"root": 103}, {"root": 104}, {"root": 105}, {"root": 106}, {"root": 107}, {"root": 108}, {"root": 109}]}, "nodes": [{}, {"pkg": 1}, {"pkg": 3}, {"pkg": 4}, {"pkg": 5}, {"pkg": 6}, {"pkg": 7}, {"pkg": 8}, {"pkg": 9}, {"pkg": 10}, {"pkg": 11}, {"pkg": 12}, {"pkg": 13}, {"pkg": 14}, {"pkg": 15}, {"pkg": 16}, {"pkg": 17}, {"pkg": 18}, {"pkg": 19}, {"pkg": 20}, {"pkg": 21}, {"pkg": 22}, {"pkg": 23}, {"pkg": 24}, {"pkg": 25}, {"pkg": 26}, {"pkg": 27}, {"pkg": 28}, {"pkg": 33}, {"pkg": 34}, {"pkg": 36}, {"pkg": 38}, {"pkg": 39}, {"pkg": 40}, {"pkg": 41}, {"pkg": 43}, {"pkg": 45}, {"pkg": 46}, {"pkg": 48}, {"pkg": 50}, {"pkg": 51}, {"pkg": 52}, {"pkg": 53}, {"pkg": 54}, {"pkg": 55}, {"pkg": 56}, {"pkg": 57}, {"pkg": 58}, {"pkg": 59}, {"pkg": 60}, {"pkg": 62}, {"pkg": 63}, {"pkg": 64}, {"pkg": 65}, {"pkg": 66}, {"pkg": 67}, {"pkg": 68}, {"pkg": 69}, {"pkg": 70}, {"pkg": 71}, {"pkg": 72}, {"pkg": 73}, {"pkg": 74}, {"pkg": 75}, {"pkg": 76}, {"pkg": 77}, {"pkg": 78}, {"pkg": 79}, {"pkg": 80}, {"pkg": 81}, {"pkg": 82}, {"pkg": 83}, {"pkg": 84}, {"pkg": 85}, {"pkg": 86}, {"pkg": 87}, {"pkg": 88}, {"pkg": 89}, {"pkg": 90}, {"pkg": 91}, {"pkg": 92}, {"pkg": 93}, {"pkg": 94}, {"pkg": 95}, {"pkg": 96}, {"pkg": 97}, {"pkg": 98}, {"pkg": 99}, {"pkg": 100}, {"pkg": 101}, {"pkg": 102}, {"pkg": 103}, {"pkg": 104}, {"pkg": 105}, {"pkg": 106}, {"pkg": 107}, {"pkg": 108}, {"pkg": 109}, {"pkg": 2}, {"pkg": 29}, {"pkg": 30}, {"pkg": 31}, {"pkg": 32}, {"pkg": 35}, {"pkg": 37}, {"pkg": 42}, {"pkg": 44}, {"pkg": 47}, {"pkg": 49}, {"pkg": 61}], "edges": []}, "Unmanaged": {"nodes": [], "edges": []}}}}, "scanner": null, "advisor": null, "evaluator": null, "resolved_configuration": {"package_curations": [{"provider": {"id": "DefaultDir"}, "curations": []}, {"provider": {"id": "DefaultFile"}, "curations": []}]}}