/* form styling within a detail page */
.page-content > :not(.side-panel) {
	& .form__section {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: var(--size-spacing-l) var(--size-spacing-m);
		align-items: center;
		break-inside: avoid;
		margin: 0 var(--size-spacing-m);

		& label {
			&:has(~lvl-textarea, lvl-richtext) {
				align-self: start;
			}
		}
		
		& .section__heading {
			grid-column: span 2;
			color: var(--clr-text-primary-default);
			font-size: var(--size-text-l);
			border-bottom: 1px solid var(--clr-border-medium);
			padding: var(--size-spacing-l) 0 var(--size-spacing-m) 0;
		}
		
		& .section__description {
			color: var(--clr-text-secondary);
			font-size: var(--size-text-s);
		}
	}

	& .form__section:not(:last-child) {
		margin-bottom: var(--size-spacing-l);
	}

	& .form__item, .form__item-group, .form__options {
		display: contents;
	}

	& .form__item > lvl-toggle {
		justify-self: right;
	}
	
	& .form__item > .form__colspan_2 {
		grid-column: span 2;
		
		& lvl-rich-text {
			margin-top: var(--size-spacing-s);
		}
	}

	& .group__label {
		grid-column: span 2;
		margin: 0.5em 0;
	}
}

.page-content :not(:is(lvl-form, .panel__content, .grid--with-2, .form__options)) > lvl-section,
.page-content lvl-form {
	width: clamp(33%, 800px, 90%);
	display: grid;
	gap: var(--size-spacing-l);
}

.page-content .form__section:has(.form__section) {
	align-items: start;
	
	& .form__section {
		width: 100%;
	}
}

/* form styling within a slide out*/
.side-panel > .content {
	position: relative; /* make the content div the offset parent for things like tooltip calculation */
	min-height: 100%;
	max-height: 100%;
	
	& > lvl-tab-bar {
		position: absolute;
		width: 100%;
		height: 100%;
	}
	
	& > .grid--centered {
		min-height: 100%;
		overflow: visible;
	}
	
	& .form {
		display: grid;
		grid-template-columns: auto auto;
		width: 100%;
		gap: var(--size-spacing-l);
	}
	
	& .form__options, .form__section {
		display: contents;
	}
	
	& .form__section .section__heading {
		display: block;
		grid-column: span 2;
		color: var(--clr-text-secondary);
		font-size: var(--size-text-l);
		border-bottom: 1px solid var(--clr-border-medium);
		padding: var(--size-spacing-l) 0 var(--size-spacing-m) 0;
	}

	& .form__section:nth-of-type(1) .section__heading {
		display: none;
	}
	
	& lvl-section {
		grid-column: span 2;
	}
	
	& .form__item {
		margin: 0 var(--size-spacing-m);
	}

	& .form__item > lvl-input,
	& .form__item > lvl-autocomplete,
	& .form__item > lvl-button-group,
	& .form__item > lvl-image-upload {
		margin-top: var(--size-spacing-s);
		width: 100%;
		display: inline-block;
	}
	
	& .form__item > legend {
		margin: var(--size-spacing-s) 0 0;
		font-size: var(--size-text-s);
		color: var(--clr-text-secondary);
		width: 100%;
	}

	& .form__item > .form__colspan_2 {
		grid-column: span 2;
		display: grid;
		grid-template-areas:
        'A'
        'C'
        'B';

		& lvl-rich-text {
			margin-top: var(--size-spacing-s);
		}
	}

	& .form__item:has(& > input[type=hidden]):not(:has(label)) {
		display: none;
	}
	
	& .form__item:has(lvl-toggle, lvl-checkbox) {
		display: flex;
		flex-wrap: wrap;
		column-gap: var(--size-spacing-m);
		align-items: center;
		justify-content: space-between;
		
		& .item__description {
			flex-grow: 1;
			margin-bottom: 0;
		}
		
		&:has(lvl-toggle) {
			flex-wrap: nowrap;
		}
	}
	
	& .form__item:has(.item__description):not(:has(lvl-toggle, lvl-checkbox)) {
		display: grid;
		grid-template-areas: 
			'A'
			'C'
			'B'
		;
		
		& .item__description {
			display: contents;
		}
		
		& label {
			grid-area: A;
		}
		
		& .option__description {
			grid-area: B;
			margin-top: var(--size-spacing-s);
		}
		
		& .item__value {
			grid-area: C;
		}
	}
}

.section-span-all, .section__description, .section__heading {
	grid-column: 1 / -1;
}

.form__item:has(> [type=hidden]) {
	display: none;
}

.item__description {
	display: flex;
	flex-direction: column;
}

.item__description > label {
	padding: 0 !important;
}

.item__description > :last-child {
	font-size: var(--size-text-s);
	color: var(--clr-text-secondary);
}

.block__item {
	display: block !important;
}

lvl-form[skeleton] label.section__heading,
lvl-form[skeleton] .form__item > label {
	display: block;
	position: relative;
	color: transparent !important;
	border-color: transparent !important;
	user-select: none;
}

lvl-form[skeleton] .item__description {
	position: relative;
}

lvl-form[skeleton] .item__description * {
	opacity: 0;
}

lvl-form[skeleton] label.section__heading:before,
lvl-form[skeleton] .form__item > label:before,
lvl-form[skeleton] .item__description:before {
	content: '';
	position: absolute;
	inset: 0;
	background-color: var(--cp-clr-background-lvl-1);
	border-radius: var(--size-radius-s);
	animation: skeleton-fade 3s linear;
	animation-iteration-count: infinite;
	cursor: default;
	z-index: 9999;
}

#edit-panel lvl-button[data-action=delete] {
	display: block;
	grid-column: span 2;
	margin: var(--size-spacing-l) 0;
	color: var(--clr-signal-error);
}

lvl-dialog lvl-form {
	display: block;
	padding: var(--size-spacing-l) var(--size-spacing-xs) var(--size-spacing-l) var(--size-spacing-l);
	background-color: var(--clr-background-lvl-1);
}