// execute global stuff when opening the page

// init toaster to show notifications
import Component from '../services/component-service'
import type { Breadcrumb, Toaster, ToastType } from 'level-components'
import { v4 as uuidv4 } from 'uuid'
import { BrowserNavigationType } from '../shared/enums.ts'

// print our help service
const message = '%cWelcome to level%cstudio%c!\n\n' + 
	'%cDoes this page need fixes or improvements? You have several options.\n\n' + 
	'🎫 %c>%c Open an issue to make level%cstudio%c great again: https://gitlab.com/dev6283740/levelbuild/WebApp/-/issues\n' + 
	'☎️ %c>%c Contact our development team. (Don\'t do this!)\n' + 
	'🙈 %c>%c Ignore the error as if nothing had ever happened.\n\n' +
	'Best regards\n' +
	'Your level%cbuild%c'
console.log(message, 'font-size:2rem', 'font-size:2rem;font-weight:bold', 'font-size:2rem', '', 'color:#0097FD;', '', 'font-weight: bold;', '', 'color:#00BFCA;', '', 'color:#B04FE1;', '', 'font-weight: bold;', '')

Component.initFormAppendObserver().then(() => {
	// Add event listener for components which are want to write notifications
	const toaster = document.getElementById('toaster') as Toaster
	document.addEventListener('toaster:notify', (event: Event) => {
		const customEvent = event as CustomEvent
		if (customEvent.detail) {
			const toastParameter = customEvent.detail.toast as ToastType
			if (!toastParameter)
				return

			if (customEvent.detail.type === 'base')
				toaster.notifySimple(toastParameter)
			else
				toaster.notify(toastParameter)
		}
	})
})

const breadcrumbNavigation = document.getElementById('bread-crumb-navigation') as Breadcrumb
let tabId = sessionStorage.getItem('tabId')

// Is there an existing tabId within the session?
let navigationType = BrowserNavigationType.Open
if (tabId) {
	// duplicated tab or reload?
	const savedLocation = sessionStorage.getItem('location')
	let browserNavigationType = (performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming).type
	
	// if we navigate and the url keeps the same then we are going to do a browser reload (hit enter in browser url input)
	if (browserNavigationType === 'navigate' && savedLocation === location.href)
		browserNavigationType = 'reload'
	
	switch(browserNavigationType) {
		case 'reload':
			navigationType = BrowserNavigationType.Reload
			if (history.state == null) {
				const breadcrumbs = JSON.parse(sessionStorage.getItem('breadcrumbs:backup') || '[]')
				history.replaceState({ breadcrumbs: breadcrumbs}, '')
			}
			sessionStorage.removeItem('breadcrumbs:backup')
			break
		case 'back_forward':
			navigationType = BrowserNavigationType.Duplicate
			break
		case 'navigate':
			// set breadcrumbs if the parent tab has saved it in the session
			const breadcrumbsFromSession = sessionStorage.getItem('breadcrumbs:backup')
			if (breadcrumbsFromSession) {
				breadcrumbNavigation.items = JSON.parse(breadcrumbsFromSession)
				sessionStorage.removeItem('breadcrumbs:backup')
			}
			break
	}
}

sessionStorage.setItem('navigationType', navigationType)
// override the existing tabId and create a new unique one for this window
if (navigationType !== BrowserNavigationType.Reload)
	sessionStorage.setItem('tabId', uuidv4())

// update location in sessionStorage
sessionStorage.setItem('location', location.href)