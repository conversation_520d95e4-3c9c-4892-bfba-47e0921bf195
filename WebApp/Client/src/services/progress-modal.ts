import { progressModalService } from 'level-components/services'
import { ProgressModal as LitProgressModal } from 'level-components'

/**
 * Legacy ProgressModal class that now uses the new Lit component internally
 * This maintains backward compatibility while using the new component system
 */
export class ProgressModal {
	private litModal: LitProgressModal | null = null;

	constructor() {
		// Initialize using the new service
		this.litModal = progressModalService.show()
	}


	public show(): void {
		if (this.litModal) {
			this.litModal.show()
		}
	}

	public hide(): void {
		progressModalService.hide()
		this.litModal = null
	}

	public updateProgress(percentage: number, status?: string): void {
		if (this.litModal) {
			this.litModal.updateProgress(percentage, status)
		}
	}

	public updateStatus(status: string = 'starting...'): void {
		if (this.litModal) {
			this.litModal.updateStatus(status)
		}
	}

	public setErrorState(errorMessage: string = 'An error occurred'): void {
		if (this.litModal) {
			this.litModal.setErrorState(errorMessage)
		}
	}

	public complete(): void {
		if (this.litModal) {
			this.litModal.complete()
		}
		this.litModal = null
	}

	public reset(): void {
		if (this.litModal) {
			this.litModal.reset()
		}
	}

	public changeTitle(title: string): void {
		if (this.litModal) {
			this.litModal.changeTitle(title)
		}
	}

	public changeDescription(description: string): void {
		if (this.litModal) {
			this.litModal.changeDescription(description)
		}
	}

	public changeIcon(iconName: string): void {
		if (this.litModal) {
			this.litModal.changeIcon(iconName)
		}
	}

	public onCancel(callback: () => void): void {
		if (this.litModal) {
			this.litModal.onCancel(callback);
		}
	}

	public destroy(): void {
		progressModalService.hide();
		this.litModal = null;
	}
}
