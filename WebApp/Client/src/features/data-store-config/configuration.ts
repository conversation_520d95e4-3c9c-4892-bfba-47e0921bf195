import type { Form, Input, Section } from 'level-components'
import type { LevelResponse } from 'level-components/types'
import PageNavigator, { ConditionalVisibilityShowIf } from '../../services/page-navigator'

/**
 * create input fields for options and append them to the form
 * @param page PageNavigator reference
 * @param dataStoreInfo response data from dataStoreConfig Controller which contains a configuration data needed to build a form
 * @param optionValues values to fill out the form
 */
export function appendConfigurationOptionsToForm(page: PageNavigator, dataStoreInfo: any, optionValues: any) {
	const optionShowIfsMap = new Map()
	const groupTemplate = document.getElementById('data-store-group-template') as HTMLTemplateElement
	const form = document.getElementById('data-store-config-form') as Form
	if (!form)
		return

	dataStoreInfo.configurationGroups.forEach((groupConfig: any) => {

		// create group html and fill it out
		const groupElement = groupTemplate.content.cloneNode(true) as HTMLElement
		groupElement.querySelector<Section>('lvl-section')!.heading = groupConfig.name

		// create option html for each config option
		groupConfig.options.forEach((optionConfig: any) => {
			const optionElement = createOptionElement(optionShowIfsMap, optionConfig, optionValues)
			groupElement.querySelector('.form__section')?.appendChild(optionElement)
		})

		// finally attach new group to the dom
		form.querySelector('.form__options')?.appendChild(groupElement)
	})

	page.handleConditionalVisibility(form, optionShowIfsMap)
}

/**
 * uses a json config object to create a dom node
 * @param optionShowIfsMap map containing conditional hiding information
 * @param optionConfig config to build a form
 * @param optionValues values to fill out the form
 */
export function createOptionElement(optionShowIfsMap: any, optionConfig: any, optionValues: any): HTMLElement {
	const optionInputTemplate = document.getElementById('data-store-option-input-template') as HTMLTemplateElement
	const optionTextAreaTemplate = document.getElementById('data-store-option-textarea-template') as HTMLTemplateElement
	const optionAutocompleteTemplate = document.getElementById('data-store-option-autocomplete-template') as HTMLTemplateElement
	const optionToggleTemplate = document.getElementById('data-store-option-toggle-template') as HTMLTemplateElement

	const optionId = `data-store-${optionConfig.name.toLowerCase()}`
	const containsDescription = optionConfig.tooltip != null
	const optionType = optionConfig.type
	const hasOptions = optionConfig.options != null
	let optionElement: HTMLElement
	if (hasOptions)
		optionElement = optionAutocompleteTemplate.content.cloneNode(true) as HTMLElement
	else switch (optionType.toLowerCase()) {
		case 'boolean':
			optionElement = optionToggleTemplate.content.cloneNode(true) as HTMLElement
			break
		case 'text':
			optionElement = optionTextAreaTemplate.content.cloneNode(true) as HTMLElement
			break
		case 'password':
		// TODO: Extend, when Password is supported. For now fallback to default	
		default:
			optionElement = optionInputTemplate.content.cloneNode(true) as HTMLElement
			break
	}
	if (containsDescription) {
		(optionElement.querySelector('.option__description')! as HTMLElement).innerText = optionConfig.tooltip
	}

	const label = optionElement.querySelector('.option__label') as HTMLLabelElement
	label.innerText = optionConfig.label
	label.htmlFor = optionId

	const input = optionElement.querySelector('.option__input') as unknown as Input & HTMLInputElement
	input.setAttribute('id', optionId)
	input.setAttribute('name', optionConfig.name)
	if (hasOptions) {
		let optionBody = document.createElement('lvl-body')
		optionConfig.options.forEach((option: any) => {
			let optionNode = document.createElement('lvl-option')
			optionNode.setAttribute('value', option)
			optionNode.innerText = option
			optionBody.appendChild(optionNode)
		})
		input.appendChild(optionBody)
	}

	//TODO: uncomment when enums from server are delivered as string not number
	//input.setAttribute('type', optionConfig.type)
	if (optionConfig.required && optionConfig.type.toString().toLowerCase() !== 'boolean')
		input.setAttribute('required', 'true')
	if (!optionValues && optionConfig.defaultValue)
		input.setAttribute('value', optionConfig.defaultValue)

	if (optionValues && optionValues[optionConfig.name])
		input.setAttribute('value', optionValues[optionConfig.name])
	else if (optionValues && optionValues[optionConfig.name] === undefined && optionConfig.defaultValue)
		input.setAttribute('value', optionConfig.defaultValue)

	let showIfGroups: ConditionalVisibilityShowIf[][] = []
	optionConfig.showIf?.forEach((optionShowIfs: any[]) => {
		let showIfGroup: ConditionalVisibilityShowIf[] = []
		optionShowIfs?.forEach((optionShowIf: any) => {
			// add to showIfs Map
			showIfGroup.push({ comparator: optionShowIf.type.toLowerCase(), option: optionShowIf.configOption, compareValue: optionShowIf.compareValue })
		})
		if (showIfGroup?.length)
			showIfGroups.push(showIfGroup)
	})
	if (showIfGroups?.length)
		optionShowIfsMap.set(optionConfig.name, showIfGroups)

	return optionElement
}

/**
 * gets configuration for building a data form from server
 * @param type
 */
export async function queryDataStoreInfo(type: string) {
	const response = await fetch(`/Api/DataStores/info/${type}`)
	if (!response.ok) {
		console.error(`${response.status} - unexpected server error received: ${response.statusText}`)
		return null
	}

	const json: LevelResponse = await response.json()
	if (json.error) {
		console.error(`Server response contains an error: ${json.error}`)
		return null
	}

	return json.data
}