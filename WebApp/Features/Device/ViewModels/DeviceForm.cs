using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.FrontendDtos.Device;
using Levelbuild.Core.FrontendDtos.Enums;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Features.Device.ViewModels;

[ExcludeFromCodeCoverage]
public class DeviceForm
{
	public DeviceDto? Device { get; init; }
	
	public List<DeviceType> DeviceTypes { get; } = Enum.GetValues(typeof(DeviceType))
		.Cast<DeviceType>()
		.ToList();
	
	public List<DeviceFormat> DeviceFormats { get; } = Enum.GetValues(typeof(DeviceFormat))
		.Cast<DeviceFormat>()
		.ToList();
	
	public DeviceForm(DeviceDto device)
	{
		Device = device;
	}
}