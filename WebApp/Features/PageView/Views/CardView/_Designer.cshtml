@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Core.FrontendDtos.PageView
@using Levelbuild.Core.SharedDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@model Levelbuild.Frontend.WebApp.Features.PageView.ViewModels.CardViewForm
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("PageView", "designer");
	var fieldLocalizer = LocalizerFactory.Create("DataField", "");
	var cardViewDto = Model.PageView as CardViewDto;
}
<script type="module" defer>
	const form = document.getElementById('page-view-form')
	
	const titleAutocomplete = form.querySelector('[name="titleField"]')
	titleAutocomplete.setAttribute('url', `/Api/PageViews/${Page.getFormData().id}/DataFields`)
	
	const textOneAutocomplete = form.querySelector('[name="textOneField"]')
	textOneAutocomplete.setAttribute('url', `/Api/PageViews/${Page.getFormData().id}/DataFields`)
	
	const textTwoAutocomplete = form.querySelector('[name="textTwoField"]')
	textTwoAutocomplete.setAttribute('url', `/Api/PageViews/${Page.getFormData().id}/DataFields`)
</script>
<div class="grid--centered vanishing-scrollbar static-scrollbar">
	<form-component id="page-view-form">
		<input type="hidden" name="dtoType" value="@Model.PageViewType"/>
		<config-section label="@localizer["sectionInfo"]">
			<div class="form__item">
				<input type="hidden" class="item__value" name="id" value="@Model.PageView?.Id"/>
			</div>
			<div class="form__item">
				@{
					List<AutocompleteColumnDefinition> autocompleteColumns = [
						new(name: "name", label: @fieldLocalizer["Name"], DataType.String),
						new(name: "type", label: @fieldLocalizer["Type"], DataType.String)
					]; 
				}
				<config-label label="@localizer["titleField"]" description="@localizer["titleFieldDescription"]"></config-label>
				<autocomplete-component type="InputDataType.Guid" columns="autocompleteColumns" name="titleField" output-name="titleFieldId"
				                        value="@(cardViewDto?.TitleFieldId)" display-value="@(cardViewDto?.TitleField?.Name)" class="item__value"
				                        placeholder="@localizer["pleaseChoose"]" required="true"
				                        edit-link-url="/Admin/DataStores/{dataSource.DataStore.Slug}/DataSources/{dataSource.Slug}/Fields/{slug}">
				</autocomplete-component>
			</div>
			<div class="form__item">
				<config-label label="@localizer["textOneField"]" description="@localizer["textOneFieldDescription"]"></config-label>
				<autocomplete-component type="InputDataType.Guid" columns="autocompleteColumns" name="textOneField" output-name="textOneFieldId"
				                        value="@(cardViewDto?.TextOneFieldId)" display-value="@(cardViewDto?.TextOneField?.Name)" class="item__value"
				                        placeholder="@localizer["pleaseChoose"]"
				                        edit-link-url="/Admin/DataStores/{dataSource.DataStore.Slug}/DataSources/{dataSource.Slug}/Fields/{slug}">
				</autocomplete-component>
			</div>
			<div class="form__item">
				<config-label label="@localizer["textTwoField"]" description="@localizer["textTwoFieldDescription"]"></config-label>
				<autocomplete-component type="InputDataType.Guid" columns="autocompleteColumns" name="textTwoField" output-name="textTwoFieldId"
				                        value="@(cardViewDto?.TextTwoFieldId)" display-value="@(cardViewDto?.TextTwoField?.Name)" class="item__value"
				                        placeholder="@localizer["pleaseChoose"]"
				                        edit-link-url="/Admin/DataStores/{dataSource.DataStore.Slug}/DataSources/{dataSource.Slug}/Fields/{slug}">
				</autocomplete-component>
			</div>
		</config-section>
	</form-component>
</div>