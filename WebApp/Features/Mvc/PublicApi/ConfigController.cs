using System.Net.Mime;
using System.Text.Json;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Entities;
using Levelbuild.Entities.Interfaces;
using Levelbuild.Frontend.WebApp.Shared.Extensions;
using Levelbuild.Frontend.WebApp.Shared.Interfaces;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Swashbuckle.AspNetCore.Annotations;
using ILogger = Serilog.ILogger;

namespace Levelbuild.Frontend.WebApp.Features.Mvc.PublicApi;

/// <summary>
/// Base class for all public API controllers belonging to configuration objects (i.e. pages and data sources).
/// </summary>
[Produces(MediaTypeNames.Application.Json)]
[Consumes(MediaTypeNames.Application.Json)]
public abstract class ConfigController : PublicApiController, IConfigController
{
	/// <inheritdoc />
	protected ConfigController(ILogger logger, IDbContextFactory<CoreDatabaseContext> contextFactory, IVersionReader versionReader) : base(logger, contextFactory, versionReader)
	{
		// nothing...
	}
	
	#region Request Handling

	/// <summary>
	/// Provides a simple method for getting a single persistent entity
	/// </summary>
	/// <param name="queryable">prepared queryable (which may have already been enhanced with things like .Include())</param>
	/// <param name="excludedProperties">An array of properties of the entity that are excluded</param>
	/// <typeparam name="TEntity">A specific PersistentEntity</typeparam>
	/// <typeparam name="TEntityDto">The corresponding EntityDto for the PersistentEntity</typeparam>
	/// <returns>PublicApiResponse as JSON</returns>
	protected async Task<ActionResult<PublicApiResponse>> HandleListRequestAsync<TEntity, TEntityDto>(IQueryable<TEntity> queryable, params string[] excludedProperties)
		where TEntity : class, IPersistentEntity, IConvertibleEntity<TEntityDto>
		where TEntityDto : EntityDto
	{
		try
		{
			var dataList = await queryable.ToDtoListAsync(excludedProperties);
			
			var json = JsonSerializer.SerializeToElement(dataList, SerializerOptions);
			return GetOkResponse(json);
		}
		catch (Exception e)
		{
			Logger.Error(e, "{EntityName} list could not be loaded", typeof(TEntity).Name);
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Provides a simple method for getting a single persistent entity
	/// </summary>
	/// <param name="queryable">prepared queryable (which may have already been enhanced with things like .Include())</param>
	/// <param name="id">Id of the element which should be selected</param>
	/// <typeparam name="TEntity">A specific PersistentEntity</typeparam>
	/// <typeparam name="TEntityDto">The corresponding EntityDto for the PersistentEntity</typeparam>
	/// /// <param name="additionalAction">An optional action that has to be invoked successfully <b>before</b> the entity is delivered.</param>
	/// <returns>PublicApiResponse as JSON</returns>
	protected async Task<ActionResult<PublicApiResponse>> HandleGetRequestAsync<TEntity, TEntityDto>(IQueryable<TEntity> queryable, Guid id, Action<TEntity>? additionalAction = null)
		where TEntity : class, IPersistentEntity, IConvertibleEntity<TEntityDto>
		where TEntityDto : EntityDto
	{
		try
		{
			var entry = await queryable.FirstOrDefaultAsync(entity => entity.Id == id);
			if (entry == null)
				return GetNotFoundResponse($"{typeof(TEntity).Name} with id: {id} could not be found");
			
			// the additional action shouldn't kill the whole endpoint
			try
			{
				additionalAction?.Invoke(entry);
			}
			catch (Exception actionException)
			{
				Logger.Error(actionException, "failed to invoke additional get request action");
			}

			var entryDto = entry.ToDto();
			
			var json = JsonSerializer.SerializeToElement(entryDto, SerializerOptions);
			return GetOkResponse(json);
		}
		catch (Exception e)
		{
			Logger.Error(e, "{EntityName} could not be loaded", typeof(TEntity).Name);
			return GetBadRequestResponse(e.Message);
		}
	}

	#endregion
	
	#region Endpoints
	
	/// <inheritdoc />
	[HttpGet]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status401Unauthorized)]
	public abstract Task<ActionResult<PublicApiResponse>> List();
	
	/// <inheritdoc />
	[HttpGet("{id:guid}")]
	[SwaggerResponse(StatusCodes.Status400BadRequest, Type = typeof(PublicApiResponse))]
	[SwaggerResponse(StatusCodes.Status401Unauthorized)]
	[SwaggerResponse(StatusCodes.Status404NotFound, Type = typeof(PublicApiResponse))]
	public abstract Task<ActionResult<PublicApiResponse>> Get(Guid id);
	
	#endregion
}