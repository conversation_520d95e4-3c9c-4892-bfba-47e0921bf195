using System.Text.Json;
using System.Text.Json.Serialization.Metadata;
using FluentMigrator.Infrastructure.Extensions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.ZitadelApiInterface.Constants;
using Levelbuild.Entities;
using Levelbuild.Entities.Attributes;
using Levelbuild.Entities.Interfaces;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Converters;
using Levelbuild.Frontend.WebApp.Shared.Extensions;
using Levelbuild.Frontend.WebApp.Shared.Interfaces;
using Levelbuild.Frontend.WebApp.Shared.Reflection;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using ILogger = Serilog.ILogger;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;

/// <summary>
/// Base class for all controllers that must only be available to admin users.
/// </summary>
[Authorize(Policy = AuthRole.Admin)]
public abstract class AdminController<TDto> : FrontendController, IAdminController<TDto>
{
	/// <summary>
	/// The injected DatabaseContext.
	/// </summary>
	protected CoreDatabaseContext DatabaseContext;
	
	/// <summary>
	/// The injected UserManager.
	/// </summary>
	protected UserManager UserManager;
	
	/// <summary>
	/// The injected StringLocalizerFactory.
	/// </summary>
	public IExtendedStringLocalizerFactory StringLocalizerFactory { get; }
	
	// ReSharper disable once StaticMemberInGenericType
	/// <summary>
	/// JSON serializer options for list headers.
	/// </summary>
	protected static readonly JsonSerializerOptions HeaderSerializer = new()
	{
		PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
		TypeInfoResolver = new DefaultJsonTypeInfoResolver()
		{
			Modifiers = {
				typeInfo =>
				{
					typeInfo.PolymorphismOptions = null;
				} 
			}
		},
		Converters = { new EntityHeaderValueConverterFactory() }
	};

	/// <summary>
	/// Constructor.
	/// </summary>
	/// <param name="logManager"></param>
	/// <param name="logger"></param>
	/// <param name="contextFactory"></param>
	/// <param name="userManager"></param>
	/// <param name="stringLocalizerFactory"></param>
	/// <param name="versionReader"></param>
	protected AdminController(ILogManager logManager, ILogger logger, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager, IExtendedStringLocalizerFactory stringLocalizerFactory, IVersionReader versionReader) : base(logManager, logger, versionReader)
	{
		DatabaseContext = contextFactory.CreateDbContext();
		UserManager = userManager;
		StringLocalizerFactory = stringLocalizerFactory;
	}
	
	#region Endpoints
	
	/// <inheritdoc />
	public abstract ActionResult<FrontendResponse> Query(QueryParamsDto parameters);
	
	/// <inheritdoc />
	public abstract ActionResult<FrontendResponse> Get(Guid id);
	
	/// <inheritdoc />
	public abstract Task<ActionResult<FrontendResponse>> Create([FromBody] TDto dto);
	
	/// <inheritdoc />
	public abstract Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] TDto dto);
	
	/// <inheritdoc />
	public abstract ActionResult<FrontendResponse> Delete(Guid id);
	
	#endregion

	#region Request Handling
	
	/// <summary>
	/// Extends the AdminController to provide a simple method for getting a list of persistent entities
	/// </summary>
	/// <param name="queryable">prepared queryable (which may have already been enhanced with things like .Include())</param>
	/// <param name="parameters">filter conditions to apply</param>
	/// <param name="additionalColumns">columns which are not part of TEntity like parent entity columns</param>
	/// <typeparam name="TEntity">A specific PersistentEntity</typeparam>
	/// <typeparam name="TEntityDto">The corresponding EntityDto for the PersistentEntity</typeparam>
	/// <returns>FrontendResponse as JSON</returns>
	protected ActionResult<FrontendResponse> HandleQueryRequest<TEntity, TEntityDto>(IQueryable<TEntity> queryable, QueryParamsDto parameters, PropertyPathList<TEntity>? additionalColumns = null)
		where TEntity : PersistentEntity<TEntity>, IConvertibleEntity<TEntityDto>
		where TEntityDto : EntityDto
	{
		try
		{
			// Group by a property name?
			if (!string.IsNullOrEmpty(parameters.GroupBy))
			{
				return HandleGroupingRequest<TEntity, TEntityDto>(queryable, parameters.GroupBy, parameters, additionalColumns);
			}
			
			var queryResult = GetQueryResult<TEntity, TEntityDto>(queryable, parameters, additionalColumns);
			
			var json = JsonSerializer.SerializeToElement(queryResult, HeaderSerializer);
			return GetOkResponse(ServerResponsePayload.FromJson(json));
		}
		catch (Exception e)
		{
			var message = $"{typeof(TEntity).Name} list could not be loaded.";
			Logger.Error(e, "{Message}", message);
			
			return GetBadRequestResponse(message);
		}
	}
	
	/// <summary>
	/// Extends the AdminController to provide a simple method for getting a list of persistent entities
	/// </summary>
	/// <param name="queryable">prepared queryable (which may have already been enhanced with things like .Include())</param>
	/// <param name="parameters">filter conditions to apply</param>
	/// <param name="additionalColumns">columns which are not part of TEntity like parent entity columns</param>
	/// <typeparam name="TEntity">A specific PersistentEntity</typeparam>
	/// <typeparam name="TEntityDto">The corresponding EntityDto for the PersistentEntity</typeparam>
	/// <returns>The query result</returns>
	protected ConfigQueryResultDto<TEntityDto> GetQueryResult<TEntity, TEntityDto>(IQueryable<TEntity> queryable, QueryParamsDto parameters, PropertyPathList<TEntity>? additionalColumns = null)
		where TEntity : PersistentEntity<TEntity>, IConvertibleEntity<TEntityDto>
		where TEntityDto : EntityDto
	{
		// Empty set? -> Stop here
		if (!queryable.Any())
		{
			return new ConfigQueryResultDto<TEntityDto>
			{
				Rows = new List<TEntityDto>(),
				CountTotal = 0
			};
		}
		
		var headerColumns = GetFilterableHeaderColumns<TEntity, TEntityDto>();
		if (additionalColumns != null)
			headerColumns.AddRange(additionalColumns);
		
		var configurationCount = queryable.WithFiltering(parameters.Filters, headerColumns).Count();
		var configurationList = queryable.WithQueryParams(parameters, headerColumns).ToDtoList();
		var queryResult = new ConfigQueryResultDto<TEntityDto>
		{
			Rows = configurationList,
			CountTotal = configurationCount
		};

		return queryResult;
	}
	
	/// <summary>
	/// Extends the AdminController to provide a simple method for getting a grouping list of persistent entities
	/// </summary>
	/// <param name="queryable">prepared queryable (which may have already been enhanced with things like .Include())</param>
	/// <param name="propertyName">Name of the field to be grouped by</param>
	/// <param name="parameters">filter conditions to apply</param>
	/// <param name="additionalColumns">columns which are not part of TEntity like parent entity columns</param>
	/// <typeparam name="TEntity">A specific PersistentEntity</typeparam>
	/// <typeparam name="TEntityDto">The corresponding EntityDto for the PersistentEntity</typeparam>
	/// <returns>FrontendResponse as JSON</returns>
	protected ActionResult<FrontendResponse> HandleGroupingRequest<TEntity, TEntityDto>(IQueryable<TEntity> queryable, string propertyName, QueryParamsDto parameters, PropertyPathList<TEntity>? additionalColumns = null)
		where TEntity : PersistentEntity<TEntity>, IConvertibleEntity<TEntityDto>
		where TEntityDto : EntityDto
	{
		try
		{
			var queryResult = GetGroupingResult<TEntity, TEntityDto>(queryable, propertyName, parameters, additionalColumns);
			return GetOkResponse(queryResult);
		}
		catch (Exception e)
		{
			var message = $"{typeof(TEntity).Name} grouping list could not be loaded.";
			Logger.Error(e, "{Message}", message);
			
			return GetBadRequestResponse(message);
		}
	}

	/// <summary>
	/// Extends the AdminController to provide a simple method for grouping a list of persistent entities by a property
	/// </summary>
	/// <param name="queryable">prepared queryable (which may have already been enhanced with things like .Include())</param>
	/// <param name="fieldName">Name of the field to be grouped by</param>
	/// <param name="parameters">filter conditions to apply</param>
	/// <param name="additionalColumns">columns which are not part of TEntity like parent entity columns</param>
	/// <typeparam name="TEntity">A specific PersistentEntity</typeparam>
	/// <typeparam name="TEntityDto">The corresponding EntityDto for the PersistentEntity</typeparam>
	/// <returns>The query result</returns>
	private QueryResultDto<FilterFieldQueryItemResultDto> GetGroupingResult<TEntity, TEntityDto>(IQueryable<TEntity> queryable, string fieldName, QueryParamsDto parameters, PropertyPathList<TEntity>? additionalColumns = null)
		where TEntity : PersistentEntity<TEntity>, IConvertibleEntity<TEntityDto>
		where TEntityDto : EntityDto
	{
		// Empty set? -> Stop here
		if (!queryable.Any())
		{
			return new QueryResultDto<FilterFieldQueryItemResultDto>
			{
				Rows = new List<FilterFieldQueryItemResultDto>(),
				CountTotal = 0
			};
		}
		
		var headerColumns = GetFilterableHeaderColumns<TEntity, TEntityDto>();
		if (additionalColumns != null)
			headerColumns.AddRange(additionalColumns);
		var filterFieldList = queryable
			.WithFiltering(parameters.Filters, headerColumns)
			.GroupByProperty<string, TEntity>(fieldName)
			.Select(grouping => new FilterFieldQueryItemResultDto
			{
				Label = string.IsNullOrEmpty(grouping.Key) ? "-" : grouping.Key,
				Value = string.IsNullOrEmpty(grouping.Key) ? "" : grouping.Key,
				Count = grouping.Count()
			})
			.OrderByDescending(item => item.Count)
			.ThenBy(item => item.Label)
			.ToList();
		
		var queryResult = new QueryResultDto<FilterFieldQueryItemResultDto>
		{
			Rows = filterFieldList,
			CountTotal = filterFieldList.Count
		};
		return queryResult;
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="queryable">prepared queryable (which may have already been enhanced with things like .Include())</param>
	/// <param name="parameters">filter conditions to apply</param>
	/// <param name="valueColumn">column containing the key value</param>
	/// <param name="labelColumn">optional column containing a display value (of not set, the key value column is used)</param>
	/// <param name="additionalColumns">optional list of additional columns which need to be returned and filtered</param>
	/// <typeparam name="TEntity">A specific PersistentEntity we are working with</typeparam>
	/// <returns>FrontendResponse as JSON</returns>
	/// <exception cref="Exception"></exception>
	protected ActionResult<FrontendResponse> HandleAutocompleteRequest<TEntity>(IQueryable<TEntity> queryable,
																					QueryParamsDto parameters,
																					string valueColumn,
																					string? labelColumn = null,
																					PropertyPathList<TEntity>? additionalColumns = null)
		where TEntity : PersistentEntity<TEntity>
	{
		try
		{
			// prepare fields
			var entityType = typeof(TEntity);
			var valueProperty = entityType.GetProperty(valueColumn);
			var termColumns = new PropertyPathList<TEntity>();
			
			if (additionalColumns != null)
			{
				// prepare search queries
				termColumns = additionalColumns
					.Where(entry => entry.Last().PropertyType == typeof(string))
					.ToPropertyNameList<TEntity>();
			}
			if (valueProperty == null)
				throw new Exception("keyColumn was not part of the object");
			
			var labelProperty = labelColumn != null ? entityType.GetProperty(labelColumn) : null;
			termColumns.Add(labelProperty != null ? labelProperty.Name : valueProperty.Name);

			// add default sorting if no sorting is given
			if (parameters.Sortings == null || parameters.Sortings.Count == 0)
			{
				parameters.Sortings = new List<QueryParamSortingDto>();
				parameters.Sortings.Add(new QueryParamSortingDto { OrderColumn = labelProperty?.Name ?? "Id", Direction = SortDirection.Asc });
			}
			
			var countQuery = queryable.WithFiltering(parameters.Filters, null, parameters.Term, termColumns);
			var listQuery = queryable.WithQueryParams(parameters, null, termColumns);

			// fire queries and select AutocompleteDto's
			var elementCount = countQuery.Count();
			var elementList = listQuery.ToList().Select(entity => new AutocompleteDto()
			{
				Value = valueProperty.GetValue(entity) ?? "",
				Label = labelProperty != null ? labelProperty.GetValue(entity)?.ToString() : null,
				ColumnValues = additionalColumns?.ToDictionary(entry => entry.ToString().ToLowerFirstChar(),
															   entry => entry.GetPropertyValueFromPath(entity)) ?? new Dictionary<string, object?>()
			}).Where(entry => !entry.Value.Equals("")).ToList();

			// create result
			var queryResult = new AutocompleteQueryResultDto()
			{
				Rows = elementList,
				Count = elementCount,
				CountTotal = elementCount
			};
			return GetOkResponse(queryResult);
		}
		catch (Exception e)
		{
			var message = $"{typeof(TEntity).Name} list could not be loaded.";
			Logger.Error(e, "{Message}", message);
			return GetBadRequestResponse(message);
		}
	}

	/// <summary>
	/// Extends the AdminController to provide a simple method for getting a single persistent entity
	/// </summary>
	/// <param name="dbSet">Entity set inside which the element can be found</param>
	/// <param name="id">Id of the element which should be selected</param>
	/// <typeparam name="TEntity">A specific PersistentEntity</typeparam>
	/// <typeparam name="TEntityDto">The corresponding EntityDto for the PersistentEntity</typeparam>
	/// /// <param name="additionalAction">An optional action that has to be invoked successfully <b>before</b> the entity is delivered.</param>
	/// <returns>FrontendResponse as JSON</returns>
	protected ActionResult<FrontendResponse> HandleGetRequest<TEntity, TEntityDto>(DbSet<TEntity> dbSet, Guid id, Action<TEntity>? additionalAction = null)
		where TEntity : class, IPersistentEntity, IConvertibleEntity<TEntityDto>
		where TEntityDto : EntityDto
	{
		return HandleGetRequest<TEntity, TEntityDto>(dbSet.AsQueryable(), id, additionalAction);
	}

	/// <summary>
	/// Extends the AdminController to provide a simple method for getting a single persistent entity
	/// </summary>
	/// <param name="queryable">prepared queryable (which may have already been enhanced with things like .Include())</param>
	/// <param name="id">Id of the element which should be selected</param>
	/// <typeparam name="TEntity">A specific PersistentEntity</typeparam>
	/// <typeparam name="TEntityDto">The corresponding EntityDto for the PersistentEntity</typeparam>
	/// <param name="additionalAction">An optional action that has to be invoked successfully <b>before</b> the entity is delivered.</param>
	/// <returns>FrontendResponse as JSON</returns>
	protected ActionResult<FrontendResponse> HandleGetRequest<TEntity, TEntityDto>(IQueryable<TEntity> queryable, Guid id, Action<TEntity>? additionalAction = null)
		where TEntity : class, IPersistentEntity, IConvertibleEntity<TEntityDto>
		where TEntityDto : EntityDto
	{
		try
		{
			var element = queryable.FirstOrDefault(config => config.Id == id);
			if (element == null)
				return GetNotFoundResponse($"{typeof(TEntity).Name} with id: {id} could not be found");
			
			// the additional action shouldn't kill the whole endpoint
			try
			{
				additionalAction?.Invoke(element);
			}
			catch (Exception actionException)
			{
				Logger.Error(actionException, "failed to invoke additional get request action");
			}
			
			return GetOkResponse(element.ToDto()!);
		}
		catch (Exception e)
		{
			var message = $"{typeof(TEntity).Name} with Id '{id}' could not be loaded.";
			Logger.Error(e, "{Message}", message);
			return GetBadRequestResponse(message);
		}
	}

	/// <summary>
	/// Extends the AdminController to provide a simple method for entity creation including proper error handling
	/// </summary>
	/// <param name="dbSet">Entity set inside which the element can be found</param>
	/// <param name="entityDto">the dto filled with data which has to be written into the new entity</param>
	/// <param name="beforeSaveAction">An optional action that has to be invoked successfully <b>before</b> the entity is persisted.</param>
	/// <param name="afterSaveAction">An optional action that has to be invoked successfully <b>after</b> the entity is persisted.</param>
	/// <typeparam name="TEntity">A specific PersistentEntity</typeparam>
	/// <typeparam name="TEntityDto">The corresponding EntityDto for the PersistentEntity</typeparam>
	/// <returns>FrontendResponse as JSON</returns>
	protected async Task<ActionResult<FrontendResponse>> HandleCreateRequestAsync<TEntity, TEntityDto>(DbSet<TEntity> dbSet,
																						  TEntityDto entityDto,
																						  Action<TEntity>? beforeSaveAction = null,
																						  Action<TEntity>? afterSaveAction = null)
		where TEntity : class, IPersistentEntity, IAdministrationEntity<TEntity, TEntityDto>
		where TEntityDto : EntityDto
	{
		try
		{
			// Validate based on DTO
			var validationErrors = TEntity.Validate(DatabaseContext, entityDto);
			if (validationErrors.Count > 0)
				return GetValidationFailedResponse(validationErrors);
			
			// TODO: return custom http code?
			// check if name (slug) is unique
			if (typeof(TEntity).HasAttribute<SlugAttribute>())
			{
				var propertyNames = typeof(TEntity).GetOneAttribute<SlugAttribute>().PropertyName;
				if (!IsUniquePropertyValues(dbSet, propertyNames, entityDto))
					return GetBadRequestResponse($"{string.Join(", ", propertyNames)} property values for entity {typeof(TEntity).Name} are not unique.");
			}

			// create entity
			var entity = typeof(TEntity).GetMethod("FromDto")?.Invoke(null, [entityDto, await UserManager.GetCurrentUserAsync(), DatabaseContext]) as TEntity;
			if (entity == null)
				throw new Exception($"unable to create {typeof(TEntity).Name} entity from {typeof(TEntityDto).Name}");
			
			// inject the StringLocalizerFactory
			entity.SetStringLocalizerFactory(StringLocalizerFactory);
			
			// check required fields
			// TODO: return custom http code?
			var missingProperty = CheckRequiredFields(entity);
			if (missingProperty != null)
				return GetBadRequestResponse($"Property {missingProperty.Name} may not be empty");

			// additional action necessary?
			// TODO: return custom http code?
			beforeSaveAction?.Invoke(entity);

			// add entity to dbSet
			await dbSet.AddAsync(entity);

			// commit changes
			var dbContext = dbSet.GetService<ICurrentDbContext>().Context;
			await dbContext.SaveChangesAsync();
			
			afterSaveAction?.Invoke(entity);

			return GetOkResponse(entity.ToDto()!);
		}
		catch (Exception e)
		{
			var message = $"{typeof(TEntity).Name} could not be created.";
			Logger.Error(e, "{Message}", message);
			return GetBadRequestResponse(message);
		}
	}

	/// <summary>
	/// Extends the AdminController to provide a simple method for entity updates including proper error handling
	/// </summary>
	/// <param name="dbSet">Entity set inside which the element can be found</param>
	/// <param name="id">Id of the element which should be updated</param>
	/// <param name="entityDto">the dto filled with data which has to be written into the entity</param>
	/// <param name="additionalAction">An optional action that has to be invoked successfully <b>before</b> the changes are persisted.</param>
	/// <typeparam name="TEntity">A specific PersistentEntity</typeparam>
	/// <typeparam name="TEntityDto">The corresponding EntityDto for the PersistentEntity</typeparam>
	/// <returns>FrontendResponse as JSON</returns>
	protected async Task<ActionResult<FrontendResponse>> HandleUpdateRequestAsync<TEntity, TEntityDto>(DbSet<TEntity> dbSet, Guid id,
																									   TEntityDto entityDto,
																									   Action<TEntity>? additionalAction = null)
		where TEntity : class, IPersistentEntity, IAdministrationEntity<TEntity, TEntityDto>
		where TEntityDto : EntityDto
	{
		return await HandleUpdateRequestAsync(dbSet.AsQueryable(), id, entityDto, additionalAction);
	}
	
	/// <summary>
	/// Extends the AdminController to provide a simple method for entity updates including proper error handling
	/// </summary>
	/// <param name="queryable">Entity queryable</param>
	/// <param name="id">Id of the element which should be updated</param>
	/// <param name="entityDto">the dto filled with data which has to be written into the entity</param>
	/// <param name="additionalAction">An optional action that has to be invoked successfully <b>before</b> the changes are persisted.</param>
	/// <typeparam name="TEntity">A specific PersistentEntity</typeparam>
	/// <typeparam name="TEntityDto">The corresponding EntityDto for the PersistentEntity</typeparam>
	/// <returns>FrontendResponse as JSON</returns>
	protected async Task<ActionResult<FrontendResponse>> HandleUpdateRequestAsync<TEntity, TEntityDto>(IQueryable<TEntity> queryable, Guid id,
																						  TEntityDto entityDto,
																						  Action<TEntity>? additionalAction = null)
		where TEntity : class, IPersistentEntity, IAdministrationEntity<TEntity, TEntityDto>
		where TEntityDto : EntityDto
	{
		// check if element exists
		var entity = await queryable.FirstOrDefaultAsync(element => element.Id == id);
		if (entity == null)
			return GetNotFoundResponse($"{typeof(TEntity).Name}#{id} does not exist");

		return await HandleUpdateRequestAsync(queryable, entity, entityDto, additionalAction);
	}

	/// <summary>
	/// Extends the AdminController to provide a simple method for entity updates including proper error handling
	/// </summary>
	/// <param name="dbSet">Entity set inside which the element can be found</param>
	/// <param name="entity">Entity which should be updated</param>
	/// <param name="entityDto">the dto filled with data which has to be written into the entity</param>
	/// <param name="additionalAction">An optional action that has to be invoked successfully <b>before</b> the changes are persisted.</param>
	/// <typeparam name="TEntity">A specific PersistentEntity</typeparam>
	/// <typeparam name="TEntityDto">The corresponding EntityDto for the PersistentEntity</typeparam>
	/// <returns>FrontendResponse as JSON</returns>
	protected async Task<ActionResult<FrontendResponse>> HandleUpdateRequestAsync<TEntity, TEntityDto>(DbSet<TEntity> dbSet, TEntity entity,
																									   TEntityDto entityDto,
																									   Action<TEntity>? additionalAction = null)
		where TEntity : class, IPersistentEntity, IAdministrationEntity<TEntity, TEntityDto>
		where TEntityDto : EntityDto
	{
		return await HandleUpdateRequestAsync(dbSet.AsQueryable(), entity, entityDto, additionalAction);
	}

	/// <summary>
	/// Extends the AdminController to provide a simple method for entity updates including proper error handling
	/// </summary>
	/// <param name="dbSet">Entity set inside which the element can be found</param>
	/// <param name="entity">Entity which should be updated</param>
	/// <param name="entityDto">the dto filled with data which has to be written into the entity</param>
	/// <param name="additionalAction">An optional action that has to be invoked successfully <b>before</b> the changes are persisted.</param>
	/// <typeparam name="TEntity">A specific PersistentEntity</typeparam>
	/// <typeparam name="TEntityDto">The corresponding EntityDto for the PersistentEntity</typeparam>
	/// <returns>FrontendResponse as JSON</returns>
	protected async Task<ActionResult<FrontendResponse>> HandleUpdateRequestAsync<TEntity, TEntityDto>(IQueryable<TEntity> queryable, TEntity entity,
																						  TEntityDto entityDto,
																						  Action<TEntity>? additionalAction = null)
		where TEntity : class, IPersistentEntity, IAdministrationEntity<TEntity, TEntityDto>
		where TEntityDto : EntityDto
	{
		try
		{
			// Validate based on DTO
			var validationErrors = TEntity.Validate(DatabaseContext, entityDto, entity.Id);
			if (validationErrors.Count > 0)
				return GetValidationFailedResponse(validationErrors);

			// TODO: return custom http code?
			// check if name (slug) is unique
			if (typeof(TEntity).HasAttribute<SlugAttribute>())
			{
				var propertyNames = typeof(TEntity).GetOneAttribute<SlugAttribute>().PropertyName;
				if (!IsUniquePropertyValues(queryable, propertyNames, entityDto, entity.Id))
					return GetBadRequestResponse($"{string.Join(", ", propertyNames)} property values for entity {typeof(TEntity).Name} are not unique.");
			}

			// update entity instance
			entity.UpdatePartial(entityDto, await UserManager.GetCurrentUserAsync());

			// check required fields
			// TODO: return custom http code?
			var missingProperty = CheckRequiredFields(entity);
			if (missingProperty != null)
				return GetBadRequestResponse($"Property {missingProperty.Name} may not be empty");

			// additional action necessary?
			// TODO: return custom http code?
			additionalAction?.Invoke(entity);

			// commit changes
			await DatabaseContext.SaveChangesAsync();

			return GetOkResponse(entity.ToDto()!);
		}
		catch (Exception e)
		{
			var message = $"{typeof(TEntity).Name} with id '{entity.Id}' could not be updated.";
			Logger.Error(e, "{Message}", message);
			return GetBadRequestResponse(message);
		}
	}

	/// <summary>
	/// Extends the AdminController to provide a simple method for entity deletion including proper error handling
	/// </summary>
	/// <param name="dbSet">Entity set inside which the element can be found</param>
	/// <param name="id">Id of the element which should be deleted</param>
	/// <param name="beforeDeleteAction">An optional action that has to be invoked successfully <b>before</b> the entity gets deleted.</param>
	/// <param name="afterDeleteAction">An optional action that has to be invoked successfully <b>after</b> the entity gets deleted.</param>
	/// <typeparam name="TEntity">A specific PersistentEntity</typeparam>
	/// <returns>FrontendResponse as JSON</returns>
	protected ActionResult<FrontendResponse> HandleDeleteRequest<TEntity>(DbSet<TEntity> dbSet, Guid id, 
																		  Action<TEntity>? beforeDeleteAction = null,
																		  Action<TEntity>? afterDeleteAction = null)
		where TEntity : class, IPersistentEntity
	{
		try
		{
			// check if element exists
			var element = dbSet.Find(id);
			if (element == null)
				return GetNotFoundResponse($"{typeof(TEntity).Name}#{id} does not exist");

			// additional action necessary?
			// TODO: return custom http code?
			beforeDeleteAction?.Invoke(element);

			// remove element from Set
			dbSet.Remove(element);
			
			// commit changes
			var dbContext = dbSet.GetService<ICurrentDbContext>().Context;
			dbContext.Entry(element).State = EntityState.Deleted;
			dbContext.SaveChanges();

			// additional action necessary?
			// TODO: return custom http code?
			afterDeleteAction?.Invoke(element);
			
			return GetOkResponse();
		}
		catch (Exception e)
		{
			var message = $"{typeof(TEntity).Name} with id '{id}' could not be deleted.";
			Logger.Error(e, "{Message}", message);
			return GetBadRequestResponse(message);
		}
	}
	
	#endregion
}