using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Frontend.WebApp.Features.Error.ViewModels;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;

namespace Levelbuild.Frontend.WebApp.Features.Error.Controllers;

/// <summary>
/// Controller for error handling
/// Show error pages, partial views and error as json (for fetch requests) 
/// </summary>
public class ErrorController : AnonymousController
{
	private readonly UserManager _userManager;

	/// <summary>
	/// inject some helpful things into the controller
	/// </summary>
	/// <param name="logManager">log manager</param>
	/// <param name="userManager">injected UserManager</param>
	/// <param name="versionReader">injected VersionReader</param>
	public ErrorController(ILogManager logManager, UserManager userManager, IVersionReader versionReader) : base(logManager, logManager.GetLoggerForClass<ErrorController>(), versionReader)
	{
		_userManager = userManager;
	}

	/// <summary>
	/// Handles all requests which are not well-formed and leads to errors 
	/// </summary>
	/// <returns>rendered detail view or a json if it's a rest request</returns>
	[Route("/Error/400")]
	public ActionResult<FrontendResponse> BadRequestAction()
	{
		var statusCodeReExecuteFeature =
			HttpContext.Features.Get<IStatusCodeReExecuteFeature>();

		if (statusCodeReExecuteFeature != null &&
			(statusCodeReExecuteFeature.OriginalPath.Contains("/Api/") || statusCodeReExecuteFeature.OriginalPath.Contains("/PublicApi/")))
		{
			return GetBadRequestResponse("Bad Request");
		}

		Response.Clear();
		Response.StatusCode = StatusCodes.Status404NotFound;
		return RenderErrorView(new ErrorModel()
		{
			ErrorTitle = "Oooops!",
			ErrorSubTitle = "Something went wrong",
			ErrorMessage =
				"We're sorry, an unexpected error occured<br/>Please go back to the homepage and contact your administrator for more information"
		});
	}

	/// <summary>
	/// Handles all unauthorized requests
	/// </summary>
	/// <returns>rendered detail view or a json if it's a rest request</returns>
	[Route("/Error/401")]
	public ActionResult<FrontendResponse> UnauthorizedAction()
	{
		var statusCodeReExecuteFeature =
			HttpContext.Features.Get<IStatusCodeReExecuteFeature>();

		if (statusCodeReExecuteFeature != null &&
			(statusCodeReExecuteFeature.OriginalPath.Contains("/Api/") || statusCodeReExecuteFeature.OriginalPath.Contains("/PublicApi/")))
		{
			return GetUnauthorizedResponse("Authorization required");
		}

		Response.Clear();
		Response.StatusCode = StatusCodes.Status404NotFound;
		
		return RenderErrorView(new ErrorModel()
		{
			ErrorTitle = "401",
			ErrorSubTitle = "Authorization required",
			ErrorMessage = "You don't have permission to access the resource"
		});
	}

	/// <summary>
	/// Handles all "Page not found" (404) responses
	/// </summary>
	/// <returns>rendered detail view or a json if it's a rest request</returns>
	[Route("/Error/404")]
	public ActionResult<FrontendResponse> PageNotFoundAction()
	{
		var statusCodeReExecuteFeature =
			HttpContext.Features.Get<IStatusCodeReExecuteFeature>();

		if (statusCodeReExecuteFeature != null &&
			(statusCodeReExecuteFeature.OriginalPath.Contains("/Api/") || statusCodeReExecuteFeature.OriginalPath.Contains("/PublicApi/")))
		{
			return GetNotFoundResponse("Resource not found");
		}

		Response.Clear();
		Response.StatusCode = StatusCodes.Status404NotFound;
		return RenderErrorView(new ErrorModel()
		{
			ErrorTitle = "404",
			ErrorSubTitle = "Page Not Found",
			ErrorMessage = "We're sorry, the page you requested could not be found<br/>Please go back to the homepage"
		});
	}

	/// <summary>
	/// Handles all unhandled exceptions responses
	/// </summary>
	/// <returns>rendered detail view or a json if it's a rest request</returns>
	[Route("/Error/500")]
	public async Task<ActionResult<FrontendResponse>> HandleException()
	{
		// log error
		var feature = HttpContext.Features.Get<IExceptionHandlerPathFeature>();
		
		if (feature == null) 
			return StatusCode(StatusCodes.Status500InternalServerError);
		
		if (feature.Path.Contains("/Api/") || feature.Path.Contains("/PublicApi/"))
		{
			var message = await _userManager.IsAdminAsync() ? feature.Error.Message : "Something went wrong";
			return new ObjectResult(new FrontendResponse(Program.HostName, VersionReader.GetVersion())
			{
				Error = new(500, message, TraceId)
			})
			{
				StatusCode = 500
			};
		}

		if (await _userManager.IsAdminAsync())
		{
			return RenderErrorView(new ErrorModel()
			{
				ErrorTitle = "500",
				ErrorSubTitle = "Something went wrong",
				ErrorMessage = feature.Error.Message,
				ErrorStacktrace = feature.Error.StackTrace ?? ""
			}, Request.QueryString.Value);
		}

		return StatusCode(StatusCodes.Status500InternalServerError);
	}

	/// <summary>
	/// Handles all other errors that are not treated separately
	/// </summary>
	/// <param name="code">Error Code</param>
	/// <returns>rendered detail view or a json if it's a rest request</returns>
	[Route("/Error/{code:int}")]
	public ActionResult<FrontendResponse> Index(int code)
	{
		var statusCodeReExecuteFeature =
			HttpContext.Features.Get<IStatusCodeReExecuteFeature>();

		if (statusCodeReExecuteFeature != null &&
			(statusCodeReExecuteFeature.OriginalPath.ToLower().Contains("/api/") || statusCodeReExecuteFeature.OriginalPath.ToLower().Contains("/publicapi/")))
		{
			return StatusCode(code);
		}

		Response.Clear();
		Response.StatusCode = code;

		return RenderErrorView(new ErrorModel()
		{
			ErrorTitle = "Oooops!",
			ErrorSubTitle = "Something went wrong",
			ErrorMessage =
				"We're sorry, an unexpected error occured<br/>Please go back to the homepage and contact your administrator for more information"
		});
	}

	#region Test Actions
	
	/// <summary>
	/// Test url for a 500 error with exception
	/// </summary>
	/// <returns></returns>
	/// <exception cref="NotImplementedException"></exception>
	[ExcludeFromCodeCoverage]
	[Route("/Error/unhandled-exception")]
	[Route("/Api/Error/unhandled-exception")]
	public IActionResult TestNotImplementedException() => throw new NotImplementedException();
	
	/// <summary>
	/// Test url for a 400 error 
	/// </summary>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[Route("/Error/bad-request")]
	[Route("/Api/Error/bad-request")]
	public IActionResult TestBadRequest() => BadRequest();
	
	/// <summary>
	/// Test url for a 401 error 
	/// </summary>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[Route("/Error/unauthorized")]
	[Route("/Api/Error/unauthorized")]
	public IActionResult TestUnauthorized() => Unauthorized();
	
	/// <summary>
	/// Test url for a 404 error
	/// </summary>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[Route("/Error/not-found")]
	[Route("/Api/Error/not-found")]
	public IActionResult TestNotFound() => NotFound();
	
	#endregion

	#region Private methods

	private ActionResult<FrontendResponse> RenderErrorView(ErrorModel model, string? queryString = null)
	{
		bool inline;
		if (string.IsNullOrEmpty(queryString))
		{
			var statusCodeReExecuteFeature =
				HttpContext.Features.Get<IStatusCodeReExecuteFeature>();
			inline = statusCodeReExecuteFeature is { OriginalQueryString: not null } &&
						 statusCodeReExecuteFeature.OriginalQueryString.Contains("inline");
		}
		else
		{
			inline = queryString.Contains("inline");
		}
		
		return inline ? PartialView("GenericErrorInline", model) : View("GenericError", model);
	}

	#endregion

}