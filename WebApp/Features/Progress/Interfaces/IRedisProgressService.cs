using Levelbuild.Frontend.WebApp.Features.Progress.Dtos;

namespace Levelbuild.Frontend.WebApp.Features.Progress.Interfaces;

/// <summary>
/// Service for managing progress updates via Redis pub/sub
/// </summary>
public interface IRedisProgressService
{
	/// <summary>
	/// Publishes a progress update message to Redis
	/// </summary>
	/// <param name="userId">User ID to send the progress update to</param>
	/// <param name="processId">Process ID associated with the progress</param>
	/// <param name="current">Current progress value</param>
	/// <param name="total">Total progress value</param>
	/// <param name="status">Optional status message</param>
	/// <param name="data">Optional additional data</param>
	/// <returns>Task representing the asynchronous operation</returns>
	Task PublishProgressUpdateAsync(Guid userId, string processId, int current, int total, string? status = null, object? data = null);

	/// <summary>
	/// Publishes a completion message to Redis
	/// </summary>
	/// <param name="userId">User ID to send the completion message to</param>
	/// <param name="processId">Process ID that completed</param>
	/// <param name="success">Whether the process completed successfully</param>
	/// <param name="message">Optional completion message</param>
	/// <param name="data">Optional additional data</param>
	/// <returns>Task representing the asynchronous operation</returns>
	Task PublishCompletionMessageAsync(Guid userId, string processId, bool success, string? message = null, object? data = null);

	/// <summary>
	/// Publishes an error message to Redis
	/// </summary>
	/// <param name="userId">User ID to send the error message to</param>
	/// <param name="processId">Process ID that encountered an error</param>
	/// <param name="errorMessage">Error message</param>
	/// <param name="data">Optional additional data</param>
	/// <returns>Task representing the asynchronous operation</returns>
	Task PublishErrorMessageAsync(Guid userId, string processId, string errorMessage, object? data = null);

	/// <summary>
	/// Subscribes to progress updates for a specific user
	/// </summary>
	/// <param name="userId">User ID to subscribe to</param>
	/// <param name="cancellationToken">Cancellation token</param>
	/// <returns>Async enumerable of progress messages</returns>
	IAsyncEnumerable<ProgressMessageDto> SubscribeToUserProgressAsync(Guid userId, CancellationToken cancellationToken = default);

	/// <summary>
	/// Subscribes to progress updates for a specific user and process
	/// </summary>
	/// <param name="userId">User ID to subscribe to</param>
	/// <param name="processId">Process ID to subscribe to</param>
	/// <param name="cancellationToken">Cancellation token</param>
	/// <returns>Async enumerable of progress messages</returns>
	IAsyncEnumerable<ProgressMessageDto> SubscribeToProcessProgressAsync(Guid userId, string processId, CancellationToken cancellationToken = default);
}
