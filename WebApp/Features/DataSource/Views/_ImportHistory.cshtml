@inject IExtendedStringLocalizerFactory LocalizerFactory
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.CreatePanel
@model Levelbuild.Frontend.WebApp.Features.DataSource.ViewModels.DataSourceForm
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("DataSource", "ImportHistory");

	List<BaseColumnComponentTagHelper> columns =
	[
		new ListDataColumnComponentTagHelper { Name = "fileName", Label = localizer["FileName"] },
		new ListDataColumnComponentTagHelper { Name = "imported", Label = localizer["Imported"] },
		new ListDataColumnComponentTagHelper { Name = "skipped", Label = localizer["Skipped"] },
		new ListDataColumnComponentTagHelper { Name = "updated", Label = localizer["Updated"] },
		new ListDataColumnComponentTagHelper { Name = "failed", Label = localizer["Failed"] },
		new ListDataColumnComponentTagHelper { Name = "status", Label = localizer["Status"] },
		new ListDataColumnComponentTagHelper { Name = "created", Label = localizer["Created"], Type = DataColumnType.DateTime },
		new ListDataColumnComponentTagHelper { Name = "uploadedBy", Label = localizer["UploadedBy"], MaxWidth = 200, MaxWidthUnit = DimensionUnit.Pixel, TextAlign = Alignment.Right}
	];

	// Get the datasource ID from ViewData
	var dataSourceId = ViewData["dataSourceId"]?.ToString();

	var filter = new List<QueryParamFilterDto>()
	{
		new()
		{
			FilterColumn = "isPublic",
			Operator = QueryParamFilterOperator.Equals,
			CompareValue = false,
		}
	};

	// Add datasource filter if dataSourceId is available
	if (!string.IsNullOrEmpty(dataSourceId) && Guid.TryParse(dataSourceId, out var parsedDataSourceId))
	{
		filter.Add(new QueryParamFilterDto
		{
			FilterColumn = "dataSourceId",
			Operator = QueryParamFilterOperator.Equals,
			CompareValue = parsedDataSourceId,
		});
	}

}

<style>
	lvl-data-list {
		width: clamp(300px, 80%, 1400px);
		background-color: var(--clr-background-lvl-0);
		padding: 0 var(--size-spacing-m);
		border-radius: var(--size-radius-m);
		box-shadow: 0 0 0.2rem 0 var(--clr-shadow-weak), 0 0.2rem 0.4rem 0 var(--clr-shadow-weak);
	}
</style>

<div class="list-view">
	<enumeration-component id="file-upload-list" filters="@filter" with-sorting="true">
		<list-component identity-column="id">
			<list-data-column-component name="fileName" label="@localizer["FileName"]"></list-data-column-component>
			<list-data-column-component name="imported" label="@localizer["Imported"]"></list-data-column-component>
			<list-data-column-component name="skipped" label="@localizer["Skipped"]"></list-data-column-component>
			<list-data-column-component name="updated" label="@localizer["Updated"]"></list-data-column-component>
			<list-data-column-component name="failed" label="@localizer["Failed"]"></list-data-column-component>
			<list-data-column-component name="status" label="@localizer["Status"]"></list-data-column-component>
			<list-data-column-component name="created" label="@localizer["Created"]" type="@DataColumnType.DateTime"></list-data-column-component>
			<list-data-column-component name="uploadedBy" label="@localizer["UploadedBy"]"></list-data-column-component>
		</list-component>
	</enumeration-component>
</div>

<vc:admin-list-page entity="FileUpload" route-name="FileUploads" localizer="@localizer" columns="@columns" menu-entry="ImportHistory" active-column="enabled" use-custom-list="true">
</vc:admin-list-page>

<script type="module" defer>
    const enumeration = document.getElementById('file-upload-list');
    const list = enumeration?.querySelector('& > *');

    if (list) {
        list.onRowClick = (rowContent) => {
            if (rowContent && rowContent.data) {
				window.open(`/Api/FileUploads/${rowContent.data.id}/Download`, '_blank');
            }
        };
    }
</script>

