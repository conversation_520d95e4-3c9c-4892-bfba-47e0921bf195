using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using System.Net.Mime;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Entities;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.Viewer.Email.Services;
using Levelbuild.Frontend.WebApp.Shared.Constants;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Levelbuild.Frontend.WebApp.Shared.Services.Files;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.DataSource.Controllers;

/// <summary>
/// Public API controller for data access
/// </summary>
[ExcludeFromCodeCoverage]
[Route("Api/DataSources/{dataSourceId:guid}/Files")]
public class FileController : FrontendController
{
	private static readonly int s_DeepZoomRenderTimeout = 30;

	private readonly CoreDatabaseContext _databaseContext;

	private readonly IDbContextFactory<CoreDatabaseContext> _contextFactory;

	private readonly UserManager _userManager;

	private readonly IThumbnailHelperService _thumbnailHelperService;

	private readonly IDeepZoomHelperService _deepZoomHelperService;


	private readonly IEmailReader _emailReader;

	private readonly IRedisAccessService _redisAccessService;

	private readonly IServiceScopeFactory _scopes;

	/// <inheritdoc />
	public FileController(ILogManager logManager, IVersionReader versionReader, IDbContextFactory<CoreDatabaseContext> contextFactory,
						  IThumbnailHelperService thumbnailHelperService, IDeepZoomHelperService deepZoomHelperService,
						  IEmailReader emailReader,
						  IRedisAccessService redisAccessService, UserManager userManager, IServiceScopeFactory scopes) : base(
		logManager, logManager.GetLoggerForClass<FileController>(), versionReader)
	{
		_databaseContext = contextFactory.CreateDbContext();
		_contextFactory = contextFactory;
		_userManager = userManager;
		_thumbnailHelperService = thumbnailHelperService;
		_deepZoomHelperService = deepZoomHelperService;
		_emailReader = emailReader;
		_scopes = scopes;
		_redisAccessService = redisAccessService;
	}

	/// <summary>
	/// Uploads a single file to get a temporary ID used to append the file to an element.
	/// </summary>
	/// <param name="dataSourceId">id of the datasource</param>
	/// <param name="file">file to upload</param>
	[HttpPost]
	[RequestSizeLimit(10_000_000_000)]
	public async Task<ActionResult<FrontendResponse>> CreateAsync(Guid dataSourceId, [Required] IFormFile file)
	{
		try
		{
			var dataSource = await _databaseContext.DataSources.FindAsync(dataSourceId);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");

			var mailData = _emailReader.ParseMail(file);

			using var fileStream = new DataStoreFileStream(file.FileName, DateTime.Now.ToUniversalTime(), file.OpenReadStream(), file.Length);
			var fileId = await dataSource.UploadFileAsync(fileStream);
			
			return GetOkResponse(new FileMetaDataDto { FileId = fileId, MailData = mailData });
		}
		catch (Exception e)
		{
			Logger.Error(e, "error uploading file to datasource with id {DataSourceId}", dataSourceId);
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Returns a specific file, converted to json
	/// </summary>
	/// <param name="dataSourceId"></param>
	/// <param name="fileId"></param>
	/// <returns></returns>
	[HttpGet("{fileId}/Json")]
	public async Task<ActionResult<object>> GetAsJsonAsync(Guid dataSourceId, string fileId)
	{
		DataStoreFileStream? fileStream = null;
		try
		{
			var dataSource = await _databaseContext.DataSources.FindAsync(dataSourceId);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");

			fileStream = await dataSource.GetFileAsync(fileId);

			if (fileStream == null)
				return GetNotFoundResponse($"File with id: {fileId} could not be found.");
			
			var fileExtension = fileStream.Name.Split('.').Last().ToLower();
			switch (fileExtension)
			{
				case "eml":
				case "msg":
					var mailData = _emailReader.ParseMail(fileStream);
					_ = fileStream.DisposeAsync();
					return mailData != null ? mailData : GetNotFoundResponse($"File with id: {fileId} could not be parsed.");
				default:
					return GetBadRequestResponse("Json conversion for this file is not supported.");
			}
		}
		catch (Exception e)
		{
			_ = fileStream?.DisposeAsync();

			Logger.Error(e, "File could not be loaded");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Returns a specific attachment of an email file.
	/// </summary>
	/// <param name="dataSourceId">id of the datasource</param>
	/// <param name="fileId">id of the storage file</param>
	/// <param name="fileName">name of the attachment</param>
	[HttpGet("{fileId}/Attachments/{fileName}")]
	public async Task<ActionResult<FrontendResponse>> GetAttachmentAsync(Guid dataSourceId, string fileId, string fileName)
	{
		var dataSource = await _databaseContext.DataSources.FindAsync(dataSourceId);
		
		if (dataSource == null)
			return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");

		var fileStream = await dataSource.GetFileAsync(fileId);
		
		if (fileStream == null)
			return GetNotFoundResponse($"File with id: {fileId} could not be found.");
		
		var fileExtension = fileStream.Name.Split('.').Last().ToLower();
		if( fileExtension is not ("eml" or "msg"))
			return GetNotFoundResponse($"File with id: {fileId} is not an email.");
		
		var attachmentBytes = _emailReader.GetAttachmentFromMail(fileStream, fileName);
		if (attachmentBytes == null)
			return GetNotFoundResponse($"Attachment with name: {fileName} could not be found.");
		
		return File(attachmentBytes, MediaTypeNames.Application.Octet, fileName);
	}
	
	/// <summary>
	/// Returns a specific attachment of an email file.
	/// </summary>
	/// <param name="dataSourceId">id of the datasource</param>
	/// <param name="fileId">id of the storage file</param>
	[HttpGet("{fileId}/Attachments")]
	public async Task<ActionResult<FrontendResponse>> GetAttachmentsAsZipAsync(Guid dataSourceId, string fileId)
	{
		var dataSource = await _databaseContext.DataSources.FindAsync(dataSourceId);
		
		if (dataSource == null)
			return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");

		var fileStream = await dataSource.GetFileAsync(fileId);
		
		if (fileStream == null)
			return GetNotFoundResponse($"File with id: {fileId} could not be found.");
		
		var fileExtension = fileStream.Name.Split('.').Last().ToLower();
		if( fileExtension is not ("eml" or "msg"))
			return GetNotFoundResponse($"File with id: {fileId} is not an email.");
		
		var attachmentBytes = _emailReader.GetAttachmentsAsZip(fileStream);
		if (attachmentBytes.Length == 0)
			return GetNotFoundResponse($"No Attachments for name: {fileStream.Name} could not be found.");
		
		return File(attachmentBytes, MediaTypeNames.Application.Zip, $"{fileStream.Name}_Attachments.zip");
	}


	/// <summary>
	/// Returns a thumbnail of a specific file.
	/// </summary>
	/// <param name="dataSourceId">id of the datasource</param>
	/// <param name="fileId">id of the storage file</param>
	/// <param name="width">desired width of the thumbnail</param>
	/// <param name="height">desired height of the thumbnail</param>
	/// <param name="crop">should the thumbnail be cropped out of the file?(optional)</param>
	[HttpGet("{fileId}/Thumbnail")]
	[ResponseCache(Duration = 31536000, Location = ResponseCacheLocation.Client)]
	public async Task<ActionResult<FrontendResponse>> GetThumbnail(Guid dataSourceId, string fileId, [FromQuery] int width = 100,
																   [FromQuery] int height = 100, [FromQuery] bool crop = true)
	{
		Stream? thumbnailStream = null;
		try
		{
			var dataSource = await _databaseContext.DataSources.FindAsync(dataSourceId);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");

			//check redis if the user has accessed this file before
			var user = await _userManager.GetCurrentUserAsync();
			if (await _redisAccessService.StringGetAsync(RedisConstants.UserThumbnailCachingKey + user?.Id) != fileId)
			{
				try
				{
					if (!await dataSource.IsGetFileAllowedAsync(fileId))
						return GetNotFoundResponse($"File with id: {fileId} could not be found.");
				}
				catch (Exception)
				{
					return GetNotFoundResponse($"File with id: {fileId} could not be found.");
				}

				//save access to redis
				if (user != null)
					await _redisAccessService.StringSetAsync(RedisConstants.UserThumbnailCachingKey + user.Id, fileId, TimeSpan.FromDays(30));
			}

			thumbnailStream = await _thumbnailHelperService.GetThumbnailAsync(_databaseContext, fileId, dataSource, width, height, crop);

			if (thumbnailStream == null)
				return GetBadRequestResponse("Thumbnail could not be fetched.");

			var thumbnailStreamCopy = new MemoryStream();
			await thumbnailStream.CopyToAsync(thumbnailStreamCopy);
			thumbnailStream.Seek(0, SeekOrigin.Begin);

			_ = Task.Run(async () =>
			{
				try
				{
					using var scope = _scopes.CreateScope();
					await using var parallelContext = scope.ServiceProvider.GetRequiredService<CoreDatabaseContext>();
					thumbnailStreamCopy.Seek(0, SeekOrigin.Begin);
					await _thumbnailHelperService.CacheThumbnailAsync(parallelContext, thumbnailStreamCopy, fileId, width, height, crop);
				}
				catch (Exception e)
				{
					Logger.Error(e, "Could not cache thumbnail");
				}
				finally
				{
					await thumbnailStreamCopy.DisposeAsync();
				}
			});

			var thumbnail = File(thumbnailStream, MediaTypeNames.Application.Octet, "thumbnail");

			return thumbnail;
		}
		catch (Exception e)
		{
			if (thumbnailStream != null)
				await thumbnailStream.DisposeAsync();

			Logger.Error(e, "Thumbnail could not be loaded");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Fetches and saves deep zoom image version of a file
	/// </summary>
	/// <param name="dataSourceId">id of the datasource</param>
	/// <param name="fileId">id of the storage file</param>
	/// <param name="dpi">dpi if file is pdf</param>
	[HttpPost("{fileId}/DeepZoom")]
	public async Task<ActionResult<FrontendResponse>> SaveDeepZoom(Guid dataSourceId, string fileId, [FromQuery] int dpi = 300)
	{
		try
		{
			var dataSource = await _databaseContext.DataSources.FindAsync(dataSourceId);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");

			try
			{
				if (!await dataSource.IsGetFileAllowedAsync(fileId))
					return GetNotFoundResponse($"File with id: {fileId} could not be found.");
			}
			catch (Exception)
			{
				return GetNotFoundResponse($"File with id: {fileId} could not be found.");
			}

			var cachedDeepZoomEntity = await _deepZoomHelperService.GetAndCacheDeepZoomImageAsync(_databaseContext, fileId, dataSource, dpi);

			if (cachedDeepZoomEntity == null)
				return BadRequest("Deep Zoom Image could not be generated");

			if (cachedDeepZoomEntity.State != CachedDeepZoomState.Ready)
				return BadRequest($"Deep Zoom Image could not be generated! {cachedDeepZoomEntity.ErrorMessage}");

			return GetOkResponse(cachedDeepZoomEntity.ToDto());
		}
		catch (Exception e)
		{
			Logger.Error(e, "Deep Zoom Image could not be generated");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Returns DeepZoomInfo as soon as Rendering is finished within 30sec (otherwise 503)
	/// </summary>
	/// <param name="dataSourceId">id of the datasource</param>
	/// <param name="fileId">if of the storage file</param>
	/// <returns></returns>
	/// TODO: make endpoint cancelable (request may get aborted if user leaves dataset before request is finished)
	[HttpGet("{fileId}/DeepZoomInfo")]
	public async Task<ActionResult<FrontendResponse>> GetDeepZoomInfo(Guid dataSourceId, string fileId)
	{
		var dataSource = await _databaseContext.DataSources.FindAsync(dataSourceId);

		if (dataSource == null)
			return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");

		try
		{
			if (!await dataSource.IsGetFileAllowedAsync(fileId))
				return GetNotFoundResponse($"File with id: {fileId} could not be found.");
		}
		catch (Exception)
		{
			return GetNotFoundResponse($"File with id: {fileId} could not be found.");
		}

		var start = DateTime.Now;
		var deepZoomInfo = _databaseContext.DeepZoomImages.AsNoTracking().FirstOrDefault(image => image.FileId == fileId);
		
		using PeriodicTimer timer = new(TimeSpan.FromSeconds(0.5));
		while (await timer.WaitForNextTickAsync() && deepZoomInfo?.State == CachedDeepZoomState.Caching && DateTime.Now.Subtract(start).TotalSeconds < s_DeepZoomRenderTimeout)
		{
			deepZoomInfo = _databaseContext.DeepZoomImages.AsNoTracking().FirstOrDefault(image => image.FileId == fileId);
		}

		if (DateTime.Now.Subtract(start).TotalSeconds > s_DeepZoomRenderTimeout)
			return StatusCode(503); // 408 causes unintended automatic retries from the browser

		if (deepZoomInfo == null)
			return GetNotFoundResponse("failed to load deep zoom image info");

		return GetOkResponse(deepZoomInfo.ToDto());
	}

	/// <summary>
	/// Fetches a specific .webp file of a cached deep zoom image
	/// </summary>
	/// <param name="dataSourceId">id of the datasource</param>
	/// <param name="fileId">if of the storage file</param>
	/// <param name="subFileFolder">level of the tile file</param>
	/// <param name="subFileName">tile name</param>
	/// <param name="dpi">dpi if the file is pdf</param>
	/// <param name="cancellationToken">cancellation token</param>
	[HttpGet("{fileId}/DeepZoom/SubFiles/{subFileFolder}/{subFileName}")]
	[ResponseCache(Duration = 31536000, Location = ResponseCacheLocation.Client)]
	public async Task<ActionResult<FrontendResponse>> GetDeepZoom(Guid dataSourceId, string fileId, string subFileFolder, string subFileName,
																  [FromQuery] int dpi = 300, CancellationToken cancellationToken = default)
	{
		Stream? cachedDeepZoomFile = null;
		try
		{
			cancellationToken.ThrowIfCancellationRequested();

			var dataSource = await _databaseContext.DataSources.FindAsync(dataSourceId, cancellationToken);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");

			//check redis if the user has accessed this file before
			var user = await _userManager.GetCurrentUserAsync();
			if (await _redisAccessService.StringGetAsync(RedisConstants.UserDeepZoomCachingKey + user?.Id) != fileId)
			{
				try
				{
					if (!await dataSource.IsGetFileAllowedAsync(fileId))
						return GetNotFoundResponse($"File with id: {fileId} could not be found.");
				}
				catch (Exception)
				{
					return GetNotFoundResponse($"File with id: {fileId} could not be found.");
				}

				//save access to redis
				if (user != null)
					await _redisAccessService.StringSetAsync(RedisConstants.UserDeepZoomCachingKey + user.Id, fileId, TimeSpan.FromDays(30));
			}

			cachedDeepZoomFile =
				await _deepZoomHelperService.GetDeepZoomImageFileAsync(_databaseContext, fileId, subFileFolder, subFileName, dpi, cancellationToken);

			if (cachedDeepZoomFile != null)
				return File(cachedDeepZoomFile, MediaTypeNames.Image.Webp, "cachedDeepZoomImage");

			return GetNotFoundResponse($"Deep zoom image for fileId {fileId} could not be found.");
		}
		catch (OperationCanceledException e)
		{
			if (cachedDeepZoomFile != null)
				await cachedDeepZoomFile.DisposeAsync();

			return GetBadRequestResponse(e.Message);
		}
		catch (Exception e)
		{
			if (cachedDeepZoomFile != null)
				await cachedDeepZoomFile.DisposeAsync();

			Logger.Error(e, "Thumbnail could not be loaded");
			return GetBadRequestResponse(e.Message);
		}
	}
}