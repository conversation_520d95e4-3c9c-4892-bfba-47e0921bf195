using System;
using System.Collections.Generic;
using Levelbuild.Core.FrontendDtos.ExcelPresets;
using Levelbuild.Core.FrontendDtos.DataSource;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Core.FrontendDtos.DataField;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or me

namespace Levelbuild.Frontend.WebApp.Features.ExcelPresets.ViewModels;

/// <summary>
/// View model for the create/edit form of preset columns
/// </summary>
public class ExcelPresetsForm
{

	public DataSourceDto? DataSource { get; set; }
    /// <summary>
    /// Type of the view
    /// </summary>
    public ViewType ViewType { get; set; }

    /// <summary>
    /// ID of the parent preset
    /// </summary>
    public Guid PresetId { get; set; }

    /// <summary>
    /// Name of the parent preset
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Data source of the parent preset
    /// </summary>
    public ExcelPresetsDto? Preset { get; set; }

    /// <summary>
    /// Column data when editing an existing column
    /// </summary>
    public ExcelPresetColumnDto? Column { get; set; }

    /// <summary>
    /// Available data fields for selection
    /// </summary>
    public IList<DataFieldDto> AvailableDataFields { get; set; } = new List<DataFieldDto>();

    /// <summary>
    /// Available data sources for selection
    /// </summary>
    public List<DataSourceDto> AvailableDataSources { get; set; } = new();

    public ExcelPresetsForm(ViewType viewType = ViewType.Create)
    {
        ViewType = viewType;
        Preset = new ExcelPresetsDto
        {
            Id = Guid.Empty,
            Name = string.Empty,
            Description = string.Empty,
            IsActive = true,
            WorkspaceId = 1, // Set appropriate default workspace ID
            DataSourceId = Guid.Empty,
            Columns = new List<ExcelPresetColumnDto>()
        };
    }
}