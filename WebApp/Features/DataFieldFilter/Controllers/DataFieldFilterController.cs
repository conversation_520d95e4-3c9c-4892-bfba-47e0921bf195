using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.DataField;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.DataFieldFilter.Controllers;

/// <summary>
/// Controller for the configuration view of list view columns
/// </summary>
public class DataFieldFilterController : AdminController<DataFieldFilterDto>
{
	/// <summary>
	/// inject some helpful things into the controller
	/// </summary>
	/// <param name="logManager">logging</param>
	/// <param name="contextFactory">database context</param>
	/// <param name="userManager">injected UserManager</param>
	/// <param name="localizerFactory">injected StringLocalizerFactory</param>
	/// <param name="versionReader">injected VersionReader</param>
	public DataFieldFilterController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager, IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader) : 
		base(logManager, logManager.GetLoggerForClass<DataFieldFilterController>(), contextFactory, userManager, localizerFactory, versionReader)
	{
		
	}
	
	#region Actions

	/// <summary>
	/// fetch all filters for the given DataField
	/// </summary>
	/// <param name="parameters">query params containing things like filters and sortings</param>
	/// <returns></returns>
	[HttpGet("/Api/DataFieldFilters")]
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		var filterQuery = DatabaseContext.DataFieldFilters
			.Include(filter => filter.FilterField);
		return HandleQueryRequest<DataFieldFilterEntity, DataFieldFilterDto>(filterQuery, parameters);
	}
	
	/// <summary>
	/// get a data field filter
	/// </summary>
	/// <param name="id">id of the filter to get</param>
	/// <returns></returns>
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		return HandleGetRequest<DataFieldFilterEntity, DataFieldFilterDto>(DatabaseContext.DataFieldFilters.Include(filter => filter.FilterField), id);
	}
	
	/// <summary>
	/// create new filter condition
	/// </summary>
	/// <param name="configurationDto">dto containing entity property values</param>
	/// <returns></returns>
	[HttpPost("/Api/DataFieldFilters")]
	public override async Task<ActionResult<FrontendResponse>> Create([FromBody] DataFieldFilterDto configurationDto)
	{
		if (DatabaseContext.DataFieldFilters.Any(entity => entity.DataFieldId == configurationDto.DataFieldId &&
														   entity.FilterFieldId == configurationDto.FilterFieldId &&
														   entity.Operator == configurationDto.Operator &&
														   entity.CompareValue == configurationDto.CompareValue ))
			return GetBadRequestResponse(
				$"Filter {configurationDto.FilterFieldId} {configurationDto.Operator} {configurationDto.CompareValue} for field {configurationDto.DataFieldId} already exists");
		
		var result = await HandleCreateRequestAsync(DatabaseContext.DataFieldFilters, configurationDto);
		return PrepareResponseDto(result);
	}
	
	/// <summary>
	/// update filter condition
	/// </summary>
	/// <param name="id">id of the filter to patch</param>
	/// <param name="configurationDto">dto containing entity property values</param>
	/// <returns></returns>
	[HttpPatch("/Api/DataFieldFilters/{id:guid}")]
	public override async Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] DataFieldFilterDto configurationDto)
	{
		var result = await HandleUpdateRequestAsync(DatabaseContext.DataFieldFilters, id, configurationDto);
		return PrepareResponseDto(result);
	}
	
	/// <summary>
	/// delete a filter condition
	/// </summary>
	/// <param name="id">id of the filter condition to delete</param>
	/// <returns></returns>
	[HttpDelete("/Api/DataFieldFilters/{id:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		var entity = DatabaseContext.DataFieldFilters
			.Include(filter => filter.DataField)
			.FirstOrDefault(filter => filter.Id == id);
		var result = HandleDeleteRequest(DatabaseContext.DataFieldFilters, id);
		if (entity != null && result.Result is OkObjectResult)
		{
			// Touch DataField
			entity.DataField?.Touch();
		}
		return result;
	}
	
	#endregion

	private ActionResult<FrontendResponse> PrepareResponseDto(ActionResult<FrontendResponse> result)
	{
		if (result.Result is not OkObjectResult objectResult)
			return result;

		var frontendResponse = objectResult.Value as FrontendResponse<DataFieldFilterDto>;
		
		// load entity again to include field info
		var entity = DatabaseContext.DataFieldFilters
			.Include(filter => filter.FilterField)
			.Include(filter => filter.DataField)
			.FirstOrDefault(filter => filter.Id == frontendResponse!.Data.Id);
		
		// touch field (which touches the DataSource)
		entity?.DataField?.Touch();
		
		return entity != null ? GetOkResponse(entity.ToDto()) : result;
	}
}