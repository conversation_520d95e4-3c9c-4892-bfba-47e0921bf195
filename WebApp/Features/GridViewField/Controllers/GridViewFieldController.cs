using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Entities.Features.PageView.GridView;
using Levelbuild.Frontend.WebApp.Features.GridViewField.ViewModels;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Reflection;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;

namespace Levelbuild.Frontend.WebApp.Features.GridViewField.Controllers;

/// <summary>
/// Controller for the configuration view of list view columns
/// </summary>
public class GridViewFieldController : AdminController<GridViewFieldDto>
{
	/// <summary>
	/// inject some helpful things into the controller
	/// </summary>
	/// <param name="logManager">logging</param>
	/// <param name="contextFactory">database context</param>
	/// <param name="userManager">injected UserManager</param>
	/// <param name="localizerFactory">injected StringLocalizerFactory</param>
	/// <param name="versionReader">injected VersionReader</param>
	public GridViewFieldController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager, IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader) :
		base(logManager, logManager.GetLoggerForClass<GridViewFieldController>(), contextFactory, userManager, localizerFactory, versionReader)
	{
		
	}
	
	#region Views
	
	/// <summary>
	/// Renders the detail (edit) view
	/// </summary>
	/// <returns>rendered detail view</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/GridViewFields/Edit")]
	public IActionResult Detail([FromQuery(Name = "type")] InputDataType? dataType, [FromQuery(Name = "textView")] bool? textView)
	{
		return CachedPartial() ?? RenderPartial(new GridViewFieldForm(ViewType.Edit, dataType, textView));
	}
	
	#endregion
	
	#region Actions

	/// <inheritdoc />
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		throw new NotImplementedException();
	}
	
	/// <summary>
	/// Returns a specific grid view field configuration as JSON
	/// </summary>
	/// <param name="id">identifier of a specific list view column</param>
	[HttpGet("/Api/GridViewFields/{id:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		// include DataField in order to get DataField.Name
		var query = DatabaseContext.GridViewFields
			.Include(field => field.DataField);
		
		return HandleGetRequest<GridViewFieldEntity, GridViewFieldDto>(query, id);
	}
	
	/// <summary>
	/// Creates a new grid view field in the database
	/// </summary>
	/// <param name="configurationDto">DTO for the new list view column</param>
	/// <returns>OK Response for successful creating and BAD Response when an errors occurred</returns>
	[HttpPost("/Api/GridViewFields/")]
	public override async Task<ActionResult<FrontendResponse>> Create([FromBody] GridViewFieldDto configurationDto)
	{
		configurationDto.Type = GridViewSectionElementType.Field;
		var result = await HandleCreateRequestAsync(DatabaseContext.GridViewFields, configurationDto);
		if (result.Result is not OkObjectResult objectResult)
			return result;
		var frontendResponse = objectResult.Value as FrontendResponse<GridViewFieldDto>;
		GridViewSectionEntity.UpdateRowCount(DatabaseContext, frontendResponse?.Data.SectionId);

		return result;
	}
	
	/// <summary>
	/// Updates a list view column in the database.
	/// </summary>
	/// <param name="id">identifier for a specific list view column</param>
	/// <param name="configurationDto">DTO for the updated version of a list view column</param>
	/// <returns>OK Response for successful updating and BAD Response when an errors occurred</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[HttpPatch("/Api/GridViewFields/{id:guid}")]
	public override async Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] GridViewFieldDto configurationDto)
	{
		// load current entity (remember section for rowcount update)
		var oldSectionId = (await DatabaseContext.GridViewFields.FindAsync(id))?.SectionId;
		
		// we need to load the DataField on order to fill all ui elements properly
		var result = await HandleUpdateRequestAsync(DatabaseContext.GridViewFields, id, configurationDto);
		if (result.Result is not OkObjectResult objectResult)
			return result;
		var frontendResponse = objectResult.Value as FrontendResponse<GridViewFieldDto>;
		GridViewSectionEntity.UpdateRowCount(DatabaseContext, frontendResponse?.Data.SectionId);
		if (oldSectionId != frontendResponse?.Data.SectionId)
			GridViewSectionEntity.UpdateRowCount(DatabaseContext, oldSectionId);
			
		if (!frontendResponse!.Data.DataFieldId.IsNullOrEmpty())
			return Get(id);
		return result;
	}
	
	/// <summary>
	/// Deletes a list view column in the database.
	/// </summary>
	/// <param name="id">identifier for a specific list view column</param>
	/// <returns>OK Response for successful deleting and BAD Response when an errors occurred</returns>
	[HttpDelete("/Api/GridViewFields/{id:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		var fieldEntity = DatabaseContext.GridViewFields.Find(id);
		var result = HandleDeleteRequest(DatabaseContext.GridViewFields, id);
		if (fieldEntity?.SectionId != null && result.Result is OkObjectResult)
			GridViewSectionEntity.UpdateRowCount(DatabaseContext, fieldEntity.SectionId);
		return result;
	}

	/// <summary>
	///  Returns all data fields which are based on the given list view
	/// </summary>
	/// <param name="gridViewId">identifier of the grid view</param>
	/// <param name="fieldId">identifier for the grid view field</param>
	/// <param name="parameters">an object to query a Subset of data fields</param>
	/// <returns></returns>
	[HttpGet("/Api/GridViews/{gridViewId}/Fields/{fieldId}/DataFields")]
	public ActionResult<FrontendResponse> QueryDataFields(Guid gridViewId, Guid fieldId, QueryParamsDto parameters)
	{
		var gridViewEntity = DatabaseContext.GridViews.Include(gridView => gridView.Page).FirstOrDefault(gridView => gridView.Id == gridViewId);
		if (gridViewEntity == null)
			return GetBadRequestResponse($"Grid View with id {gridViewId} not found");
		var fieldEntity = DatabaseContext.GridViewFields.FirstOrDefault(field => field.Id == fieldId);
		if (fieldEntity == null)
			return GetBadRequestResponse($"GridViewField with id: {fieldId} could not be found");
		
		var dataFieldIds = DatabaseContext.GridViewSections.Where(section => section.GridViewId == gridViewId).Include(section => section.Fields)
			.SelectMany(section => section.Fields.Where(field => field.DataType != InputDataType.Annotation).Select(field => field.DataFieldId)).ToList();
		
		// exclude current field id (because the currently selected field has to be part of the result set)
		if (fieldEntity.DataFieldId != null)
			dataFieldIds.Remove(fieldEntity.DataFieldId);
		
		// get all valid data field types which can be connected with the current grid view field  
		var allowedTypes = new List<DataType>();
		if (fieldEntity.DataType.IsNumeric())
			allowedTypes = Enum.GetValues<DataType>().Where(dataType => dataType.IsNumeric()).ToList();
		else if (fieldEntity.DataType.IsDate() || fieldEntity.DataType.IsTime())
		{
			if (fieldEntity.DataType.IsDate())
				allowedTypes.AddRange(Enum.GetValues<DataType>().Where(dataType => dataType.IsDate()).ToList());
			if (fieldEntity.DataType.IsTime())
				allowedTypes.AddRange(Enum.GetValues<DataType>().Where(dataType => dataType.IsTime()).ToList());
		} 
		else 
			allowedTypes.Add(fieldEntity.DataType.DataType());
			
		var query = DatabaseContext.DataFields.Include(field => field.DataSource).ThenInclude(source => source!.DataStore)
			.Where(dataField => dataField.DataSourceId == gridViewEntity.Page.DataSourceId &&
								allowedTypes.Contains(dataField.Type));
			
		// for annotation fields (aka pin locations) we only allow references to blueprint lookup sources
		if (fieldEntity.DataType == InputDataType.Annotation)
			query = query.Where(dataField => dataField.LookupSource != null && dataField.LookupSource.Type == DataSourceType.Blueprint);
		else
			query = query.Where(dataField => !dataFieldIds.Contains(dataField.Id));
		
		return HandleAutocompleteRequest(
			query, 
			parameters, 
			nameof(DataFieldEntity.Id), 
			nameof(DataFieldEntity.Name), 
			new PropertyPathList<DataFieldEntity>()
			{
				nameof(DataFieldEntity.Name), 
				nameof(DataFieldEntity.RichText),	
				nameof(DataFieldEntity.Slug),
				(DataFieldEntity field) => field.DataSource!.Slug,
				(DataFieldEntity field) => field.DataSource!.DataStore.Slug,
			});
	}
	
	#endregion
}