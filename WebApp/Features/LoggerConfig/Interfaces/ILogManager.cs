using Levelbuild.Core.DataStoreInterface;
using Levelbuild.Entities.Features.LoggerConfig;
using Serilog.Events;
using ILogger = Serilog.ILogger;

namespace Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;

/// <summary>
/// Interface for the creation of class specific loggers
/// </summary>
public interface ILogManager
{
	/// <summary>
	/// Checks if a config for a classes logger already exists (via configuration from UI/Database).
	/// </summary>
	/// <param name="defaultLevel">default level for the logger, if no config exists</param>
	/// <param name="groupName">name to group multiple Loggers by</param>
	/// <typeparam name="T"></typeparam>
	/// <returns></returns>
	public ILogger GetLoggerForClass<T>(LogEventLevel? defaultLevel = null, string? groupName = null);
	
	/// <summary>
	/// Checks if a config for a static classes logger already exists (via configuration from UI/Database).
	/// </summary>
	/// <param name="Type">Static Class Name</param>
	/// <param name="defaultLevel">default level for the logger, if no config exists</param>
	/// <param name="groupName">name to group multiple Loggers by</param>
	/// <typeparam name="T"></typeparam>
	/// <returns></returns>
	public ILogger GetLoggerForClass(Type type, LogEventLevel? defaultLevel = null, string? groupName = null);
	
	/// <summary>
	/// Checks if a config for a DataStore's logger already exists (via configuration from UI/Database).
	/// </summary>
	/// <param name="defaultLevel">default level for the logger, if no config exists</param>
	/// <param name="groupName">name to group multiple Loggers by</param>
	/// <typeparam name="T">A <see cref="IDataStore"/> type.</typeparam>
	/// <returns></returns>
	public ILogger GetLoggerForDataStore<T>(LogEventLevel? defaultLevel = null, string? groupName = null) where T : IDataStore;
	
	/// <summary>
	/// Checks if a config for a DataStore's logger already exists (via configuration from UI/Database).
	/// </summary>
	/// <param name="type">A <see cref="IDataStore"/> type.</param>
	/// <param name="defaultLevel">default level for the logger, if no config exists</param>
	/// <param name="groupName">name to group multiple Loggers by</param>
	/// <returns></returns>
	public ILogger GetLoggerForDataStore(Type type, LogEventLevel? defaultLevel = null, string? groupName = null);

	/// <summary>
	/// TESTS ONLY: Gets the LogLevel for a class
	/// </summary>
	/// <param name="className">name of the class</param>
	/// <returns></returns>
	public LogEventLevel? GetLogLevel(string className);

	/// <summary>
	/// Updates Loggers on config change/creation
	/// </summary>
	/// <param name="loggerConfigEntity"></param>
	public void UpdateLogger(LoggerConfigEntity loggerConfigEntity);
}