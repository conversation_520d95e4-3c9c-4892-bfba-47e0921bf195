using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Entities;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Extensions;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Levelbuild.Frontend.WebApp.Features.FileUpload.ViewModels;
using Levelbuild.Entities.Features.FileUpload;
using Levelbuild.Core.FrontendDtos.FileUpload;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.FileUpload.Controllers
{
    /// <summary>
    /// Controller for managing File Uploads
    /// </summary>
    public class AdminFileUploadController : AdminController<FileUploadDto>
    {
        /// <summary>
        /// Constructor with dependency injection
        /// </summary>
        /// <param name="logManager">The log manager.</param>
        /// <param name="contextFactory">The database context factory.</param>
        /// <param name="userManager">The user manager for authentication/authorization.</param>
        /// <param name="localizerFactory">The localizer factory for localization.</param>
        /// <param name="versionReader">The version reader service.</param>
        public AdminFileUploadController(
            ILogManager logManager,
            IDbContextFactory<CoreDatabaseContext> contextFactory,
            UserManager userManager,
            IExtendedStringLocalizerFactory localizerFactory,
            IVersionReader versionReader
        ) : base(
            logManager,
            logManager.GetLoggerForClass<AdminFileUploadController>(),
            contextFactory,
            userManager,
            localizerFactory,
            versionReader)
        {
            // The constructor sets up the base controller with injected dependencies ma'am
        }

		#region Views
        /// <summary>
        /// Renders the list view for presets or preset columns.
        /// </summary>
        /// <returns>The partial view with the list of File Uploads.</returns>
        [ExcludeFromCodeCoverage]
        [Route("/Admin/FileUploads/")]
        [HttpGet]
        public IActionResult List(Guid id)
        {
            // Retrieve and show a list of File Uploads, ordered by their Name
            return CachedPartial() ?? RenderPartial(new FileUploadList()
            {
                FileUploads = DatabaseContext.FileUploads
                    .AsQueryable()
                    .OrderBy(fileUploadEntity => fileUploadEntity.FileName)
                    .ToDtoList().ToList()
            });
        }

        /// <summary>
        /// Render empty create view or use an existing preset's data (if the id is provided).
        /// </summary>
        /// <param name="id">Optional id to identify an existing preset.</param>
        /// <returns>Partial view for creating a new File Upload.</returns>
        [ExcludeFromCodeCoverage]
        [HttpGet("/Admin/FileUploads/Create")]
        [HttpGet("/Admin/FileUploads/{id?}/Create")]
        public IActionResult Create(Guid? id)
        {
            // Load available data sources from the database
            var availableDataSources = DatabaseContext.DataSources
                            .AsQueryable()
                            .ToDtoList()
                            .ToList();

            if (id == Guid.Empty)
            {
                return CachedPartial() ?? RenderPartial(
                    new FileUploadForm
                    {
                        FileUpload = new FileUploadDto
                        {
                            FileName = string.Empty,
                        },
                        AvailableDataSources = availableDataSources
                    },
                    "List",
                    new FileUploadList());
            }

            var existingFileUpload = DatabaseContext.FileUploads
                .FirstOrDefault(fileUploadEntity => fileUploadEntity.Id == id);

            // Throw an exception if the existing preset is not found
            if (existingFileUpload == null)
                throw new ElementNotFoundException($"File upload with Id: {id} could not be found");

            return CachedPartial() ?? RenderPartial(
                new FileUploadForm
                {
                    FileUpload = new FileUploadDto
                    {
                        FileName = string.Empty,
                    }
                },
                "List",
                new FileUploadList
                {
                    FileUploads = DatabaseContext.FileUploads
                        .AsQueryable()
                        .ToDtoList()
                        .ToList()
                });
        }

        /// <summary>
        /// Renders the details view for a preset (either creating a new preset or editing an existing one).
        /// </summary>
        /// <param name="id">The unique id that identifies the preset.</param>
        /// <param name="menu">Optional parameter to load a specific sub-menu.</param>
        /// <returns>The partial view containing details of the preset.</returns>
        [ExcludeFromCodeCoverage]
        [HttpGet("/Admin/FileUploads/Edit/{menu?}")]
        [HttpGet("/Admin/FileUploads/{id}/{menu?}")]
        [HttpGet("/Admin/FileUploads/Edit/{id}/{menu?}")]
        public IActionResult Detail(Guid id, string? menu)
        {
            // Load data sources for the preset
            var availableDataSources = DatabaseContext.DataSources
                            .AsQueryable()
                            .ToDtoList()
                            .ToList();

            var fileUpload = DatabaseContext.FileUploads
                .FirstOrDefault(fileUploadEntity => fileUploadEntity.Id == id);

            // If none found, render a create view
            if (fileUpload == null)
            {
                return this.CachedPartial() ?? this.RenderPartial(new FileUploadForm(ViewType.Create)
                {
                    FileUpload = new FileUploadDto()
                    {
                        FileName = string.Empty,
                    },
                    AvailableDataSources = availableDataSources
                });
            }

            // If found, render the edit view
            return this.CachedPartial() ?? this.RenderPartial(new FileUploadForm(ViewType.Edit)
            {
                FileUpload = fileUpload.ToDto(),
                AvailableDataSources = availableDataSources
            });
        }
		#endregion
		#region Actions
        /// <inheritdoc />
        /// <summary>
        /// Query endpoint for retrieving uploaded files in a filtered/sorted manner.
        /// </summary>
        /// <param name="queryParams">The parameters for filtering, sorting, etc.</param>
        [HttpGet("/Api/FileUploads")]
        public override ActionResult<FrontendResponse> Query(QueryParamsDto queryParams)
        {
            // Handles queries for FileUploads using the common HandleQueryRequest method
            return HandleQueryRequest<FileUploadEntity, FileUploadDto>(DatabaseContext.FileUploads, queryParams);
        }

        /// <inheritdoc />
        /// <summary>
        /// Gets a single uploaded file by GUID.
        /// </summary>
        /// <param name="id">The unique identifier of the preset.</param>
        [HttpGet("/Api/FileUploads/{id:guid}")]
        public override ActionResult<FrontendResponse> Get(Guid id)
        {
            // Fetch the preset by ID
            var fileUpload = DatabaseContext.FileUploads.Find(id);
            if (fileUpload == null)
                return GetNotFoundResponse($"File upload with id: {id} could not be found");

            // Returns the single preset data
            return HandleGetRequest<FileUploadEntity, FileUploadDto>(DatabaseContext.FileUploads, id);
        }

        /// <inheritdoc />

		[HttpPost("/Api/FileUploads")]
		public override async Task<ActionResult<FrontendResponse>> Create([FromBody] FileUploadDto dto)
		{

			return await HandleCreateRequestAsync(DatabaseContext.FileUploads, dto);
		}

        /// <inheritdoc />

        [HttpPut("/Api/FileUploads/{id:guid}")]
		public override async Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] FileUploadDto dto)
		{
			var currentUser = await UserManager.GetCurrentUserAsync();
			return await HandleUpdateRequestAsync(DatabaseContext.FileUploads, id, dto, fileUploadEntity => fileUploadEntity.UpdatePartial(dto, currentUser));
		}

		/// <inheritdoc />
        /// <summary>
        /// Deletes an uploaded file by GUID.
        /// </summary>
        [HttpDelete("/Api/FileUploads/{id:guid}")]
        public override ActionResult<FrontendResponse> Delete(Guid id)
        {
            // Attempt to find the preset
            var fileUpload = DatabaseContext.FileUploads.Find(id);
            if (fileUpload == null)
                return GetNotFoundResponse($"File upload with id: {id} could not be found");

            // Remove the found preset and save changes
            DatabaseContext.FileUploads.Remove(fileUpload);
            DatabaseContext.SaveChanges();

            // Return success response
            return HandleDeleteRequest(DatabaseContext.FileUploads, id);
        }
		#endregion

		/// <summary>
		/// Downloads a file directly from the FileUploadEntity
		/// </summary>
		/// <param name="id">The unique identifier of the file upload</param>
		/// <returns>The file content as a FileStreamResult</returns>
		[HttpGet("/Api/FileUploads/{id:guid}/Download")]
		[AllowAnonymous] // Allow anonymous access to this endpoint
		[ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)] // Prevent caching
		[ProducesResponseType(typeof(FileStreamResult), StatusCodes.Status200OK)]
		[ProducesResponseType(StatusCodes.Status404NotFound)]
		[ProducesResponseType(StatusCodes.Status500InternalServerError)]
		public ActionResult DownloadFile(Guid id)
		{
			try
			{
				// Fetch the file upload by ID
				var fileUpload = DatabaseContext.FileUploads.Find(id);
				if (fileUpload == null)
				{
					Logger.Warning($"File upload with id: {id} could not be found");
					return new NotFoundObjectResult($"File upload with id: {id} could not be found");
				}

				// Check if the file content exists
				if (fileUpload.File == null || fileUpload.File.Length == 0)
				{
					Logger.Warning($"File content for upload with id: {id} could not be found");
					return new NotFoundObjectResult($"File content for upload with id: {id} could not be found");
				}

				// Create a memory stream from the file content
				var stream = new MemoryStream(fileUpload.File);

				// Return the file with the original filename
				return new FileStreamResult(stream, "application/octet-stream")
				{
					FileDownloadName = fileUpload.FileName
				};
			}
			catch (Exception ex)
			{
				Logger.Error(ex, "Error downloading file");
				return new ObjectResult("An error occurred while downloading the file")
				{
					StatusCode = 500
				};
			}
		}



    }
}
