using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Entities;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.Services;
using Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.Utils;
using Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.ViewModels;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;

namespace Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.Controllers;

/// <inheritdoc />
[Route("MicrosoftOffice/{dataSourceId:guid}/File/{sysFileId}")]
public class MicrosoftOfficeController : FrontendController
{
	private readonly UserManager _userManager;
	private readonly CoreDatabaseContext _databaseContext;
	private readonly RedisFileTrackingService _redisFileTrackingService;

	/// <summary>
	/// Initializes a new instance of the MicrosoftOfficeController class
	/// </summary>
	/// <param name="logManager">The log manager service</param>
	/// <param name="versionReader">The version reader service</param>
	/// <param name="userManager"></param>
	/// <param name="databaseContext"></param>
	/// <param name="redisFileTrackingService"></param>
	[ExcludeFromCodeCoverage]
	public MicrosoftOfficeController(ILogManager logManager, IVersionReader versionReader, UserManager userManager, CoreDatabaseContext databaseContext,
									 RedisFileTrackingService redisFileTrackingService) : base(logManager, logManager.GetLoggerForClass<MicrosoftOfficeController>(),versionReader)
	{
		_userManager = userManager;
		_databaseContext = databaseContext;
		_redisFileTrackingService = redisFileTrackingService;
	}
	
	/// <summary>
	/// Handles the rendering of Microsoft Office files by constructing WOPI URLs and initializing required file information.
	/// </summary>
	/// <param name="dataSourceId">The unique identifier of the data source containing the file.</param>
	/// <param name="sysFileId">The system file identifier of the file to be processed.</param>
	/// <returns>A task that represents the asynchronous operation, containing a result of type <see cref="IActionResult"/>.</returns>
	[HttpGet("")]
	public async Task<ActionResult<FrontendResponse>> Index(Guid? dataSourceId, string sysFileId)
	{
		try
		{
			var dataSource = _databaseContext.DataSources.FirstOrDefault(x => x.Id == dataSourceId);
			if (dataSource == null)
			{
				return GetNotFoundResponse("Data source not found");
			}

			var fileIdParts = sysFileId.Split('_', StringSplitOptions.RemoveEmptyEntries);
			var elementId = fileIdParts.Length > 0 ? fileIdParts[0] : null;

			if (elementId == null)
			{
				throw new InvalidOperationException("Invalid file ID");
			}

			var wopiCode = await _userManager.GetOrCreateWopiTokenAsync();
			var baseUrl = $"https://{Request.Host}";

			var element = await dataSource.GetElementAsync(elementId);
			if (element == null)
			{
				return GetNotFoundResponse("Element not found");
			}

			// Get or create the file record in Redis
			var (fileRecord, _) = await _redisFileTrackingService.GetOrCreateFileRecordAsync(dataSource.Id, elementId);

			if (element.FileInfo == null)
			{
				return GetNotFoundResponse("File not found");
			}

			var file = await dataSource.GetFileAsync(element.FileInfo.Id);
			if (file == null)
			{
				return GetNotFoundResponse("File not found");
			}

			var ext = Path.GetExtension(file.Name).ToLowerInvariant().TrimStart('.');
			if (!OfficeFileExtensions.IsSupportedOfficeExtension(ext))
			{
				return GetBadRequestResponse($"File type '{ext}' is not supported by Microsoft Office Online");
			}

			var wopiSrc = $"{baseUrl}/PublicApi/wopi/files/{elementId}";

			var discoveryService = HttpContext.RequestServices.GetRequiredService<WopiDiscoveryService>();
			var discoveryInfo = await discoveryService.GetDiscoveryInfoAsync();
			var action = discoveryInfo?.GetActionByExtension(ext, "edit");

			if (action == null)
			{
				return BadRequest($"No WOPI action found for extension '{ext}' and action 'edit'");
			}

			var urlBuilder = action.Name.Equals("edit", StringComparison.OrdinalIgnoreCase)
								 ? WopiUrlBuilder.CreateEditBuilder(action, wopiSrc, wopiCode)
								 : WopiUrlBuilder.CreateViewBuilder(action, wopiSrc, wopiCode);

			urlBuilder.SetParameter("timezone", TimeZoneInfo.Local.Id);

			// Check if a file is currently locked
			var isCurrentlyLocked = !string.IsNullOrEmpty(fileRecord.LockValue) && 
								(fileRecord.LockExpires == null || fileRecord.LockExpires > DateTime.UtcNow);

			var model = new MicrosoftOfficeModel
			{
				FileId = elementId,
				DataSourceId = dataSource.Id,
				WopiCode = wopiCode,
				FileName = file.Name,
				ActionUrl = urlBuilder.Build(),
				AccessTokenTtl = DateTimeOffset.UtcNow.AddDays(1).ToUnixTimeMilliseconds(),
				IsLocked = isCurrentlyLocked,
				LockValue = fileRecord.LockValue,
				LockExpires = fileRecord.LockExpires,
				FileRevision = fileRecord.Revision.ToString()
			};

			return PartialView(model);
		}
		catch (Exception ex) when (ex is not InvalidOperationException)
		{
			Logger.Error(ex, "Error opening file {0} from data source {1}", sysFileId, dataSourceId);

			var baseUrl = $"https://{Request.Host}";
			var wopiCode = await _userManager.GetOrCreateWopiTokenAsync();
			var url = $"https://view.officeapps.live.com/op/embed.aspx?src={baseUrl}/PublicApi/datasource/{dataSourceId}/wopi/files/{sysFileId.Split('_')[0]}/contents?access_token={wopiCode}";
			var model = new MicrosoftOfficeModel { ActionUrl = url };
			return PartialView(model);
		}
	}
	
	/// <summary>
	/// Retrieves the action URL for opening a specified file in the provided view mode, along with coauthoring information.
	/// </summary>
	/// <param name="dataSourceId">The identifier of the data source where the file is located. Nullable.</param>
	/// <param name="sysFileId">The system file identifier of the target file. Nullable.</param>
	/// <param name="viewMode">The mode in which the file will be opened, such as view or edit.</param>
	/// <returns>An <see cref="OkObjectResult"/> containing the action URL and coauthoring information if successful, otherwise an error message.</returns>
	[HttpGet("/Api/MicrosoftOffice/{dataSourceId:guid}/File/{sysFileId}/ViewMode/{viewMode}")]
	public async Task<ActionResult<FrontendResponse>> IndexApi(Guid? dataSourceId, string? sysFileId, string viewMode)
	{
		var result = await MicrosoftOfficeOperations.GetOfficeActionUrlAndLockInfoAsync(
			dataSourceId, sysFileId, viewMode, _databaseContext, _userManager,
			_redisFileTrackingService, HttpContext, Logger);

		return !result.Success ? GetBadRequestResponse(result.ErrorMessage ?? "An error occurred while processing the file") : Ok(new { actionUrl = result.ActionUrl, lockInfo = result.LockInfo });
	}
	
}
