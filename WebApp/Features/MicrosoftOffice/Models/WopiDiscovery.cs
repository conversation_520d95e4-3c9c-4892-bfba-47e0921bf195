namespace Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.Models;

    /// <summary>
    /// Represents WOPI discovery information
    /// </summary>
    public class WopiDiscovery
    {
        /// <summary>
        /// The list of WOPI actions from the discovery XML
        /// </summary>
        public List<WopiAction> Actions { get; init; } = [];

        /// <summary>
        /// Gets a WOPI action by extension and name
        /// </summary>
        /// <param name="extension">The file extension (with or without the dot)</param>
        /// <param name="actionName">The action name (e.g., "view", "edit")</param>
        /// <returns>The WOPI action, or null if not found</returns>
        public WopiAction? GetActionByExtension(string extension, string actionName)
        {
            // Normalize the extension (ensure it has a dot)
            if (!string.IsNullOrEmpty(extension) && !extension.StartsWith($"."))
            {
                extension = "." + extension;
            }

            // Remove the dot for comparison with the ext property
            var extWithoutDot = extension.TrimStart('.');

            // Find the action
            var result = Actions.FirstOrDefault(a =>
                string.Equals(a.Ext, extWithoutDot, StringComparison.OrdinalIgnoreCase) &&
                string.Equals(a.Name, actionName, StringComparison.OrdinalIgnoreCase));

            return result;
        }
	}
