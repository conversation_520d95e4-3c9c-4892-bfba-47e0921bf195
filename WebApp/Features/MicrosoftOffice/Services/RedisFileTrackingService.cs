using System.Text.Json;
using Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.Models;
using Levelbuild.Frontend.WebApp.Shared.Services;

namespace Levelbuild.Frontend.WebApp.Features.MicrosoftOffice.Services;

/// <summary>
/// Service for tracking files in Redis for WOPI integration
/// </summary>
public class RedisFileTrackingService
{
    private readonly IRedisAccessService _redisAccessService;

	private const string FileHashKey = "wopi_files";

	/// <summary>
	/// Creates a new instance of the RedisFileTrackingService
	/// </summary>
	/// <param name="redisAccessService">The Redis access service</param>
	public RedisFileTrackingService(
        IRedisAccessService redisAccessService)
    {
        _redisAccessService = redisAccessService;
	}
    
    /// <summary>
    /// Gets or creates a file record in Redis
    /// </summary>
    /// <param name="dataSourceId">The data source ID</param>
    /// <param name="elementId">The element ID</param>
    /// <returns>A tuple containing the file record and whether it was created</returns>
    public async Task<(RedisFileRecord FileRecord, bool Created)> GetOrCreateFileRecordAsync(Guid dataSourceId, string elementId)
    {
        // Check if the file record already exists
        var exists = await _redisAccessService.HashExistsAsync(FileHashKey, elementId);

		RedisFileRecord? fileRecord;
		if (exists)
		{
			// Get the existing record
			fileRecord = await GetFileRecordAsync(elementId);
			if (fileRecord == null)
			{
				throw new Exception("File record not found");
			}
			return (fileRecord, false);
		}
        
        // Create a new record
        fileRecord = new RedisFileRecord
		{
			ElementId = elementId,
			DataSourceId = dataSourceId,
			IsPublic = true,
			IsReadOnly = false,
			Revision = Guid.NewGuid(),
			Created = DateTime.UtcNow,
			LastModified = DateTime.UtcNow
		};
        
        // Save the record
        await SaveFileRecordAsync(fileRecord);
        
        return (fileRecord, true);
    }
    
    /// <summary>
    /// Gets a file record by element ID
    /// </summary>
    /// <param name="elementId">The element ID</param>
    /// <returns>The file record, or null if not found</returns>
    public async Task<RedisFileRecord?> GetFileRecordAsync(string elementId)
    {
        var jsonString = await _redisAccessService.HashGetAsync(FileHashKey, elementId);

		if (jsonString.IsNullOrEmpty)
			return null;

        // Convert RedisValue to string safely before deserializing
        var jsonStringValue = jsonString.ToString();
        return string.IsNullOrEmpty(jsonStringValue) ? null : JsonSerializer.Deserialize<RedisFileRecord>(jsonStringValue);
	}

	/// <summary>
	/// Updates the lock information for a file
	/// </summary>
	/// <param name="fileRecord">The file record</param>
	/// <param name="lockValue">The lock value</param>
	/// <param name="lockExpires">The lock expiration time</param>
	/// <returns>True if the update was successful</returns>
	public async Task UpdateFileLockAsync(RedisFileRecord fileRecord, string? lockValue, DateTime lockExpires)
    {
        fileRecord.LockValue = lockValue;
        fileRecord.LockExpires = lockExpires;
        fileRecord.LastModified = DateTime.UtcNow;

		await SaveFileRecordAsync(fileRecord);
	}

	/// <summary>
	/// Updates the revision for a file
	/// </summary>
	/// <param name="fileRecord">The file record</param>
	/// <param name="revision">The new revision</param>
	/// <param name="modifiedBy">The user who modified the file</param>
	/// <returns>True if the update was successful</returns>
	public async Task UpdateFileRevisionAsync(RedisFileRecord fileRecord, Guid revision, string? modifiedBy = null)
    {
        fileRecord.Revision = revision;
        fileRecord.LastModified = DateTime.UtcNow;
        
        if (!string.IsNullOrEmpty(modifiedBy))
        {
            fileRecord.LastModifiedBy = modifiedBy;
        }

		await SaveFileRecordAsync(fileRecord);
	}

	/// <summary>
	/// Updates the user information for a file
	/// </summary>
	/// <param name="fileRecord">The file record</param>
	/// <param name="userInfo">The user information</param>
	/// <returns>True if the update was successful</returns>
	public async Task UpdateUserInfoAsync(RedisFileRecord fileRecord, string userInfo)
    {
        fileRecord.UserInfo = userInfo;
        fileRecord.LastModified = DateTime.UtcNow;

		await SaveFileRecordAsync(fileRecord);
	}

    // Private helper methods
    
    private async Task SaveFileRecordAsync(RedisFileRecord fileRecord)
    {
        var jsonString = JsonSerializer.Serialize(fileRecord);
		await _redisAccessService.HashSetAsync(FileHashKey, fileRecord.ElementId, jsonString);
	}
}
