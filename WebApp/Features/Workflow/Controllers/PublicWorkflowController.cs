using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Entities;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Levelbuild.Frontend.WebApp.Shared.Services.Files;
using Levelbuild.Frontend.WebApp.Shared.Utils;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.Workflow.Controllers;

/// <summary>
/// Controller for public Workflow actions
/// </summary>
public class PublicWorkflowController : FrontendController
{
	private readonly UserManager _userManager;
	private readonly CoreDatabaseContext _databaseContext;
	private readonly IDeepZoomHelperService _deepZoomHelperService;
	private IExtendedStringLocalizerFactory _stringLocalizerFactory;

	/// <inheritdoc />
	public PublicWorkflowController(ILogManager logManager, UserManager userManager, IVersionReader versionReader, IDeepZoomHelperService deepZoomHelperService,
									IDbContextFactory<CoreDatabaseContext> contextFactory, IExtendedStringLocalizerFactory stringLocalizerFactory) : base(logManager, logManager.GetLoggerForClass<PublicWorkflowController>(), versionReader)
	{
		_userManager = userManager;
		_databaseContext = contextFactory.CreateDbContext();
		_deepZoomHelperService = deepZoomHelperService;
		_stringLocalizerFactory = stringLocalizerFactory;
	}

	#region Endpoints

	/// <summary>
	/// Returns the current state of a given workflow.
	/// </summary>
	/// <param name="id"></param>
	/// <param name="elementId"></param>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Api/Workflows/{id:guid}/Elements/{elementId}")]
	public async Task<ActionResult<FrontendResponse>> GetCurrentWorkflowStateAsync(Guid id, string elementId)
	{
		try
		{
			var workflow = _databaseContext.Workflows
				.Include(workflow => workflow.DataSource)
				.Include(workflow => workflow.StatusField)
				.Include(workflow => workflow.Nodes)
				.FirstOrDefault(workflow => workflow.Id == id);
			if (workflow == null)
				return GetNotFoundResponse($"Workflow with id {id} not found.");

			var element = await workflow.DataSource.GetElementAsync(elementId);
			if (element == null)
				return GetNotFoundResponse($"Element with id: {elementId} could not be found.");
			
			Guid statusValue;
			try
			{
				statusValue = Guid.Parse((element.Values[workflow.StatusField!.Name] as string)!);
			}
			catch (Exception e)
			{
				if (e is FormatException or ArgumentNullException)
					statusValue = workflow.Nodes.First(node => node.State == WorkflowNodeState.Start).Id;
				else
					throw;
			}
			
			var node = workflow.Nodes.FirstOrDefault(node => node.Id == statusValue);
			if(node == null)
				return GetNotFoundResponse($"Node with id {statusValue} not found.");
			
			return GetOkResponse(node.ToDto());
		}
		catch (Exception e)
		{
			if(e is DataSourceNotFoundException or ElementNotFoundException)
				return GetNotFoundResponse($"Element with id {elementId} not found.");
			
			return GetBadRequestResponse($"Workflow status for element {elementId} could not be retrieved: {e.Message}");
		}
	}
	
	/// <summary>
	/// Changes the state of a given workflow.
	/// </summary>
	/// <param name="id"></param>
	/// <param name="elementId"></param>
	/// <param name="nodeId"></param>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[HttpPost("/Api/Workflows/{id:guid}/Elements/{elementId}/ChangeStatus/{nodeId:guid}")]
	public async Task<ActionResult<FrontendResponse>> ChangeWorkflowStatusAsync(Guid id, string elementId, Guid nodeId)
	{
		try
		{
			var workflow = _databaseContext.Workflows
				.Include(workflow => workflow.DataSource)
				.Include(workflow => workflow.StatusField)
				.Include(workflow => workflow.Nodes)
				.FirstOrDefault(workflow => workflow.Id == id);
			if (workflow == null)
				return GetNotFoundResponse($"Workflow with id {id} not found.");
			
			var node = workflow.Nodes.FirstOrDefault(node => node.Id == nodeId);
			if(node == null)
				return GetNotFoundResponse($"Workflow node with id {nodeId} not found.");

			var updateValues = new Dictionary<string, object?>
			{
				{ workflow.StatusField!.Name, node.Id }
			};
			var elementData = new DataStoreElementData(elementId, updateValues);
			
			// TODO - Use real origin
			var successInfo = await workflow.DataSource.UpdateElementAsync(elementData, new DataStoreOperationOrigin(DataStoreOperationOriginType.UserWorkflow, -1, ""));
			if (successInfo.ElementId == null)
				throw new DataStoreOperationException($"Something went wrong during update of element with id: {elementId}.");
			
			// load element fresh (because we need the virtual fields and those can't be accessed through the update method currently)
			var element = await ElementUtils.GetElementWithVirtualFieldsAsync(_stringLocalizerFactory, _databaseContext, _deepZoomHelperService, workflow.DataSourceId, elementId);
			if (element == null)
				return GetNotFoundResponse($"Element with id: {elementId} could not be found in DataSource with id {workflow.DataSourceId}");
			
			var user = await _userManager.GetCurrentUserAsync();
			_ = ElementUtils.GenerateWorkflowEntries(_databaseContext, element, workflow.DataSource, user);
			
			return GetOkResponse(element);
		}
		catch (Exception e)
		{
			if(e is DataSourceNotFoundException or ElementNotFoundException)
				return GetNotFoundResponse($"Element with id {elementId} not found.");
			
			return GetBadRequestResponse($"Status for element {elementId} could not be updated: {e.Message}");
		}
	}

	#endregion
	
}