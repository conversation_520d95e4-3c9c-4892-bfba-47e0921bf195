@inject IExtendedStringLocalizerFactory LocalizerFactory
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.WorkflowNode.ViewModels
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.CreatePanel
@model Levelbuild.Frontend.WebApp.Features.Workflow.ViewModels.WorkflowForm
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("WorkflowNode", "");
	var sortings = new List<QueryParamSortingDto>()
	{
		new()
		{
			OrderColumn = "Sorting",
			Direction = SortDirection.Asc
		},
		new()
		{
			OrderColumn = "Name",
			Direction = SortDirection.Asc
		}
	};
	IList<BaseColumnComponentTagHelper> columns = [
		new ListDataColumnComponentTagHelper() { Name = "name", Label = localizer["list/name"] },
		new ListDataColumnComponentTagHelper() { Name = "state", Label = localizer["list/state"] }
	];
	var nodeName = "";
	if (ViewData["targetViewModel"] != null && ViewData["targetAction"]?.ToString() == "Detail")
	{
		nodeName = (ViewData["targetViewModel"] as WorkflowNodeForm)?.WorkflowNode?.Name;
	}
}
<script type="module" defer>
	// disable save button
	Page.buttonConfig.saveButton.disabled = true

	// reactivate save button when content changes
	Page.getContentChangeSignal().addEventListener("abort", () => { 
		Page.buttonConfig.saveButton.disabled = false
	})
</script>
<vc:admin-list-page entity="WorkflowNode" route-name="WorkflowNodes" localizer="@localizer" columns="@columns" sorting="@sortings" menu-entry="WorkflowNodes" parent-property-name="workflowId"></vc:admin-list-page>
<vc:create-panel entity="WorkflowNode" route-name="WorkflowNodes" localizer="@localizer" menu-entry="WorkflowNodes" parent-property-name="workflowId"></vc:create-panel>
<vc:edit-panel entity="WorkflowNode" route-name="WorkflowNodes" width="450" localizer="@localizer" menu-entry="WorkflowNodes" heading="@nodeName" skeleton="@(Model.Workflow == null)" ignore-overflow="true"></vc:edit-panel>
