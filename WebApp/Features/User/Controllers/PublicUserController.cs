using System.Diagnostics.CodeAnalysis;
using System.Drawing;
using System.Security.Authentication;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using QRCoder;

namespace Levelbuild.Frontend.WebApp.Features.User.Controllers;

/// <summary>
/// Controller for user actions that are not restricted to admins.
/// </summary>
public class PublicUserController : FrontendController
{
	private readonly UserManager _userManager;
	private readonly IConfiguration _configuration;
	
	/// <inheritdoc />
	public PublicUserController(ILogManager logManager, UserManager userManager, IConfiguration configuration, IVersionReader versionReader) :
		base(logManager, logManager.GetLoggerForClass<AdminUserController>(), versionReader)
	{
		_userManager = userManager;
		_configuration = configuration;
	}
	
	#region Actions
	
	/// <summary>
	/// Sets the user's current customer.
	/// </summary>
	/// <param name="id">id of a specific customer</param>
	/// <returns></returns>
	[HttpPost("/Api/Users/<USER>/{id:guid}")]
	public async Task<ActionResult<FrontendResponse>> SwitchCustomer(Guid id)
	{
		try
		{
			var switchResult = await _userManager.SwitchCustomerAsync(id);
			if (!switchResult.Success)
				return GetBadRequestResponse(switchResult.Message);
			
			return GetOkResponse(switchResult.CustomerInstance!.ToDto());
		}
		catch (Exception e)
		{
			Logger.Error(e, "Customer could not be switched");
			return GetBadRequestResponse(e.Message);
		}
	}
	
	/// <summary>
	/// Generates a QR Code containing all necessary information for the Mobile App to connect to the WebApp.
	/// </summary>
	/// <param name="width">Optional.</param>
	/// <param name="height">Optional.</param>
	/// <returns>A frontend response containing the generated QR Code as SVG.</returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Api/Users/<USER>")]
	public async Task<ActionResult<FrontendResponse>> GetMobileConfigQrCode(int width = 300, int height = 300)
	{
		try
		{
			var appUrl = $"{HttpContext.Request.Scheme}://{HttpContext.Request.Host}/PublicApi";
			var authConfig = _configuration.GetSection("Zitadel");
			var user = await _userManager.GetCurrentUserAsync();
			var payload = new Dictionary<string, object>
			{
				{ "oauthUrl", authConfig["Url"]! },
				{ "webAppUrl", appUrl },
				{ "authCode", await _userManager.GetOrCreateOneTimeAuthCodeAsync() },
				{ "userRemoteId", user.RemoteId },
				{ "userName", user.DisplayName },
				{ "clientId", authConfig["MobileClientId"]! },
				{
					"scope", new[]
					{
						"openid",
						"profile",
						"email",
						"offline_access",
						"urn:zitadel:iam:user:resourceowner",
						"urn:zitadel:iam:org:project:id:zitadel:aud"
					}
				}
			};
			
			var qrGenerator = new QRCodeGenerator();
			var qrData = qrGenerator.CreateQrCode(JsonConvert.SerializeObject(payload), QRCodeGenerator.ECCLevel.Q, true);
			
			var qrCode = new SvgQRCode(qrData);
			var image = qrCode.GetGraphic(new Size(width, height), sizingMode: SvgQRCode.SizingMode.ViewBoxAttribute);
			
			return GetOkResponse(ServerResponsePayload.FromString(image.Replace("\"", "'")));
		}
		catch (Exception e)
		{
			Logger.Error(e, "QR Code could not be generated");
			return GetBadRequestResponse(e.Message);
		}
	}
	
	#endregion
}