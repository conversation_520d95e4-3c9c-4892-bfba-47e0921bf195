using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.FrontendDtos.Customer;
using Levelbuild.Core.FrontendDtos.User;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member

namespace Levelbuild.Frontend.WebApp.Features.User.ViewModels;

[ExcludeFromCodeCoverage]
public class UserForm
{
	public ViewType ViewType { get; init; }
	public UserDto? User { get; init; }

	// TODO: Get them via JS
	public ICollection<CustomerDto> Customers { get; init; } = new List<CustomerDto>();

	public UserForm(ViewType viewType = ViewType.Create)
	{
		ViewType = viewType;
	}
}