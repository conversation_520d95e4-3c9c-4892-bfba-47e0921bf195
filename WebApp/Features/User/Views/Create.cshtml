@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Core.SharedDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@model Levelbuild.Frontend.WebApp.Features.User.ViewModels.UserForm
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("User", "");
}
<script type="module" defer>
	const showIfMapping = new Map()
	showIfMapping.set("firstName", [[{option: "isMachineUser", compareValue: false}]]);
	showIfMapping.set("lastName", [[{option: "isMachineUser", compareValue: false}]]);
	showIfMapping.set("email", [[{option: "isMachineUser", compareValue: false}]]);
	showIfMapping.set("password", [[{option: "isMachineUser", compareValue: false}]]);
	
	const form = document.getElementById('user-form')
	await Component.waitForComponentInitialization(form)
	Page.handleConditionalVisibility(form, showIfMapping)
</script>

<div class="grid--centered">
	<form-component id="user-form">
		<config-section label="@localizer["sectionInfo"]">
			<div class="form__item">
				<config-label target="user-is-machine-user" label="@localizer["isMachineUser"]"></config-label>
				<toggle-component id="user-is-machine-user" name="isMachineUser" class="item__value" value=""></toggle-component>
			</div>
			<div class="form__item">
				<config-label target="user-display-name" label="@localizer["displayName"]"></config-label>
				<input-component id="user-display-name" name="username" class="item__value" required value=""></input-component>
			</div>
			<div class="form__item">
				@{
					var customerColumns = new List<AutocompleteColumnDefinition>
					{
						new("displayName", localizer["name"], DataType.String)
					};
				}
				<config-label target="user-customer" label="@localizer["mainCustomerName"]"></config-label>
				<autocomplete-component type="InputDataType.Guid"
					id="user-customer"
					Url="/Api/Customers/autocomplete"
					columns="@customerColumns"
					value="@(Model.User?.MainCustomerId ?? string.Empty)"
					name="mainCustomerId"
					class="item__value"
					required
					placeholder="@localizer["pleaseChoose"]"
					edit-link-url="/Admin/Customers/{slug}">
				</autocomplete-component>
			</div>
			<div class="form__item">
				<config-label target="is-admin" label="@localizer["isAdmin"]"></config-label>
				<toggle-component id="is-admin" name="isAdmin" class="item__value" value=""></toggle-component>
			</div>
			<div class="form__item">
				<config-label target="user-first-name" label="@localizer["firstName"]"></config-label>
				<input-component id="user-first-name" name="firstName" class="item__value" required value=""></input-component>
			</div>
			<div class="form__item">
				<config-label target="user-last-name" label="@localizer["lastName"]"></config-label>
				<input-component id="user-last-name" name="lastName" class="item__value" required value=""></input-component>
			</div>
			<div class="form__item">
				<config-label target="user-email" label="@localizer["email"]"></config-label>
				<input-component id="user-email" name="email" class="item__value" required value=""></input-component>
			</div>
			<div class="form__item">
				<config-label target="password" label="@localizer["initialPassword"]"></config-label>
				<input-component id="password" name="password" class="item__value" type="InputDataType.Password" validator="Validator.ZitadelPassword" tooltip="@localizer["zitadelPasswordValidation"]" required value=""></input-component>
			</div>
		</config-section>
	</form-component>
</div>