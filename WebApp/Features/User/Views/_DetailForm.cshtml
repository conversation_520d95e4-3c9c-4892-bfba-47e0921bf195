@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.User.Services
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminListPage
@model Levelbuild.Frontend.WebApp.Features.User.ViewModels.UserForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@inject UserManager UserManager
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("User", "");
	var deviceLocalizer = LocalizerFactory.Create("Device", "");
	var mappingLocalizer = LocalizerFactory.Create("UserCustomerMapping", "");
}

<style>
	.custom-input-icon {
		position: absolute;
		right: 0;
		top: 0;
		height: 100%;
	}
	
	.access_token_wrapper {
		position: relative; 
		height: 3.2rem;
		
		& lvl-input {
			padding-right: 4rem;
		}
	}
</style>

<script type="module" defer>
	const showIfMapping = new Map()
	showIfMapping.set("firstName", [[{option: "isMachineUser", compareValue: false}]]);
	showIfMapping.set("lastName", [[{option: "isMachineUser", compareValue: false}]]);
	showIfMapping.set("email", [[{option: "isMachineUser", compareValue: false}]]);

	showIfMapping.set("personalAccessToken", [[{option: "isMachineUser", compareValue: true}]]);
	showIfMapping.set("personalAccessTokenExpirationDate", [[{option: "isMachineUser", compareValue: true}]]);
	
	showIfMapping.set("isMachineUser", [[{option: "isMachineUser", compareValue: true}]]);
	
	showIfMapping.set("devices", [[{option: "isMachineUser", compareValue: false}]])
	showIfMapping.set("customerContexts", [[{option: "isMachineUser", compareValue: false}]])

	Page.buttonConfig.impersonate = true
	Page.buttonConfig.impersonateButton.disabled = true
	Page.buttonConfig.impersonateButton.skeleton = false

	const form = document.getElementById('user-form')
	await Component.waitForComponentInitialization(form)
	Page.handleConditionalVisibility(form, showIfMapping)
</script>

<script type="module" defer>
	Page.registerCreatePanel(document.getElementById('create-panel'));
	@if (Model.User != null)
	{
		<text>
			Page.setMainPage('/Admin/Users/<USER>')
		</text>
		if (Model.User.IsMachineUser != true)
		{
			<text>
				document.querySelector('#customer-mapping-list lvl-list-column[name="delete"]').converter = () =>
					`<i class="fa-light fa-trash clickable" style="color: var(--clr-signal-error)" onclick="removeMapping(event)"></i>`
			</text>
		}
		else
		{
			<text>
				document.querySelector('#customer-mapping-list lvl-list-column[name="delete"]').converter = () => ''			
			</text>
		}
		
		var userId = (Guid) Model.User?.Id!.Value!;
		if (await UserManager.CanUserBeImpersonatedAsync(userId))
		{
			<text>
				await activateImpersonateButton('@userId.ToString()')
			</text>
		}
	}

	/**
	 * opens a dialog to create a new mapping
	 * @@param mouseEvent {MouseEvent} button click event
	 */
	document.querySelector('lvl-fab[data-action=add]').addEventListener('click', async () => {
		await Page.showCreatePanel(`${Page.getMainPageUrl()}/UserCustomerMappings/Create`,  {type: Page.getFormData().type}, '@localizer["list/newItem"]')
		Page.setPanelInfo(`${Page.getMainPageUrl()}/UserCustomerMappings/Create`, {type: Page.getFormData().type}, '@localizer["list/newItem"]');
	})
	
	document.querySelector('#device-list lvl-list-column[name="flag"]').converter = (rowData) => {
		const icon = (rowData.icon === "android" || rowData.icon === "apple") ? 
			"fa-brands fa-" + rowData.icon : 
			"fa-light fa-" + rowData.icon
		return `<i class="${icon}"></i>`
	}

	const handleSaveButtonClick = async () => {
		const result = await Form.storeData(Page.createSlideOut.querySelector('form, lvl-form'), '/Api/UserCustomerMappings', 'POST')
		if (!result)
			return
		Page.createSlideOut.open = false
		document.getElementById('customer-mapping-list')?.reload()
	}

	const saveButton = Page.createSlideOut.querySelector('[data-action="save"]')
	saveButton?.addEventListener('click', handleSaveButtonClick, { signal: Page.getPageChangeSignal() })
	Page.createSlideOut.setAttribute('initDone', '')
	
	const patRefreshButton = document.getElementById('user-personal-access-token-refresh');
	patRefreshButton.addEventListener('click', async () => await refreshPersonalAccessToken())

	@if (Model.User == null)
	{
		<text>
			document.querySelector('input[name="slug"]').addEventListener('change', async (event) => {
				Page.setMainPage(`/Admin/Users/<USER>
			})
			
			document.querySelector('lvl-toggle[name="isMachineUser"]').addEventListener('change', async (event) => {
				
				const isMachineUser = event.target.value
				document.querySelector('lvl-fab[data-action=add]').hidden = isMachineUser
				
				if (isMachineUser !== true) {
					document.querySelector('#customer-mapping-list lvl-list-column[name="delete"]').converter = () => 
						`<i class="fa-light fa-trash clickable" style="color: var(--clr-signal-error)" onclick="removeMapping(event)"></i>`
				} else {
					document.querySelector('#customer-mapping-list lvl-list-column[name="delete"]').converter = () => ''
				}
			})

			const id = Page.getFormData().id;
			const response = await fetch(`/Auth/CheckImpersonationRights/${id}`)
			if (response.ok) {
				await activateImpersonateButton(id)
			}
		</text>
	}
		
	async function removeMapping(event) {
		event.stopPropagation()

		const listLine = event.target.parentElement.parentElement
		const columnEnumeration = document.getElementById('customer-mapping-list')
		const columnList = columnEnumeration.querySelector('lvl-list')
		if (!listLine || listLine.tagName !== 'LVL-LIST-LINE'){
			console.error('List line not found. Column cannot be removed')
			return
		}

		const position = Number(listLine.dataset['position'])
		const currentRow = columnList.rows[position]
		const callback = async (elementId) => {
			Overlay.showWait("@localizer["deleting"]")
			const response = await fetch(`/Api/UserCustomerMappings/${elementId}`, { method: 'DELETE' })
			if (response.ok)
				columnEnumeration.reload()
			Overlay.hideWait()
		}
		
		Page.showDeletionWarningToast('@mappingLocalizer["deletionWarningHeading"]', '@mappingLocalizer["deletionWarning"]', async () => await callback(currentRow.id), @localizer["ok"], @localizer["abort"])
	}

	async function refreshPersonalAccessToken() {
		const toaster = document.getElementById('toaster')
		const id = document.querySelector('input[name="id"]').value
		const response = await fetch(`/Api/Users/<USER>/RefreshPersonalAccessToken`, { method: 'GET' })
		if (!response.ok) {
			toaster.notifySimple({ heading: '@localizer["patRefresh/error"]', type: 'error' })
			return
		}
		
		const json = await response.json()
		document.getElementById('user-personal-access-token').value = json.data.token
		document.getElementById('user-personal-access-token-expiration-date').value = json.data.expirationDate
		toaster.notifySimple({ heading: '@localizer["patRefresh/success"]', type: 'success' })
	}

	async function activateImpersonateButton(userId) {
		Page.buttonConfig.impersonateButton.addEventListener('click', async () => {
			await fetch(`/Auth/Impersonate/${userId}`, {
				method: 'POST',
				redirect: 'follow'
			}).then(response => {
				if (response.redirected) {
					window.location.href = response.url;
				} else {
					const toaster = document.getElementById('toaster')
					toaster.notifySimple({ heading: '@localizer["impersonation/error"]', type: 'error' })
				}
			})
		})
		Page.buttonConfig.impersonateButton.disabled = false
	}
</script>

<vc:admin-list-page entity="Device" route-name="Devices" localizer="@deviceLocalizer"
                    parent-property-name="userId" parent-property-value="@(Model.User?.Id)" use-custom-list="true">
</vc:admin-list-page>
<vc:admin-list-page list-id="customer-mapping-list" entity="UserCustomerMapping" route-name="UserCustomerMappings" localizer="@localizer"
                    parent-property-name="userId" parent-property-value="@(Model.User?.Id)" use-custom-list="true">
</vc:admin-list-page>
<fab-component data-action="add" data-tooltip="add-mapping-tooltip" hidden="@(Model.User is { IsMachineUser: true })"></fab-component>
<tooltip-component name="add-mapping-tooltip" placement="PopupPlacement.Top" block-offset-x="8">@mappingLocalizer["list/newItem"]</tooltip-component>
<slide-out-component id="create-panel" class="side-panel" position="Alignment.Right" modal anchor heading="@mappingLocalizer["list/newItem"]" width="370" open="@(ViewData["targetAction"]?.ToString() == "Create")">
	<div class="content vanishing-scrollbar static-scrollbar">
		@if (ViewData["targetAction"]?.ToString() == "Create")
		{
			@await Html.PartialAsync(ViewData["targetAction"] as string, ViewData["targetViewModel"])
		}
	</div>
	<button-component slot="button-left" data-action="cancel" label="@localizer["abortButton"]" type="ButtonType.Secondary" color="ColorState.Info"></button-component>
	<button-component slot="button-right" data-action="save" label="@localizer["saveButton"]" type="ButtonType.Primary"></button-component>
</slide-out-component>

<div class="grid--centered @(Model.ViewType == ViewType.Edit ? "vanishing-scrollbar static-scrollbar" : "")">
	<form-component id="user-form" skeleton="@(Model.User == null)">
		<div class="form__item">
			<input type="hidden" class="item__value" name="id" value="@Model.User?.Id"/>
		</div>
		<div class="form__item">
			<input type="hidden" class="item__value" name="slug" value="@Model.User?.Slug"/>
		</div>
		<config-section label="@localizer["sectionInfo"]">
			<div class="form__item">
				<config-label target="user-display-name" label="@localizer["displayName"]"></config-label>
				<input-component id="user-display-name" name="username" class="item__value" required value="@Model.User?.Username"></input-component>
			</div>
			<div class="form__item">
				<config-label target="user-first-name" label="@localizer["firstName"]"></config-label>
				<input-component id="user-first-name" name="firstName" class="item__value" required value="@Model.User?.FirstName"></input-component>
			</div>
			<div class="form__item">
				<config-label target="user-last-name" label="@localizer["lastName"]"></config-label>
				<input-component id="user-last-name" name="lastName" class="item__value" required value="@Model.User?.LastName"></input-component>
			</div>
			<div class="form__item">
				<config-label target="user-email" label="@localizer["email"]"></config-label>
				<input-component id="user-email" name="email" class="item__value" required value="@Model.User?.Email"></input-component>
			</div>
			<div class="form__item">
				@{
					var customerOptions = Model.Customers.Select(customer => new AutocompleteOptionDefinition(customer.Id!, customer.DisplayName)).ToList();
				}
				<config-label target="user-customer" label="@localizer["mainCustomerName"]"></config-label>
				<autocomplete-component
					id="user-customer"
					name="mainCustomerId"
					class="item__value"
					required
					options="customerOptions"
					value="@Model.User?.MainCustomer?.Id"
					placeholder="@localizer["pleaseChoose"]"
					readonly="true">
				</autocomplete-component>
			</div>
			<div class="form__item">
				<config-label target="user-is-machine-user" label="@localizer["isMachineUser"]"></config-label>
				<toggle-component id="user-is-machine-user" name="isMachineUser" class="item__value" value="@Model.User?.IsMachineUser" readonly="@(Model.ViewType == ViewType.Edit)"></toggle-component>
			</div>
			@* TODO: Make this input type Password *@
			<div class="form__item">
				<config-label target="user-personal-access-token" label="@localizer["personalAccessToken"]"></config-label>
				@* TODO: Change to native component, once our input has the option to add custom buttons *@
				<span class="access_token_wrapper">
					<input-component id="user-personal-access-token" name="personalAccessToken" class="item__value" required readonly="true" value="@Model.User?.PersonalAccessToken"></input-component>
					@* TODO: Add tooltip once button is handled by input component *@
					<lvl-input-button id="user-personal-access-token-refresh" class="custom-input-icon" icon-css="rotate"></lvl-input-button>
				</span>
			</div>
			<div class="form__item">
				<config-label target="user-personal-access-token-expiration-date" label="@localizer["personalAccessTokenExpirationDate"]"></config-label>
				<input-component id="user-personal-access-token-expiration-date" name="personalAccessTokenExpirationDate" class="item__value" value="@Model.User?.PersonalAccessTokenExpirationDate?.ToString("dd.MM.yyyy HH:mm")" type="InputDataType.DateTime" required readonly="true"></input-component>
			</div>
		</config-section>
		@{
			var deviceSortings = new List<QueryParamSortingDto>()
			{
				new() { OrderColumn = "displayName", Direction = SortDirection.Asc },
				new() { OrderColumn = "Created", Direction = SortDirection.Asc }
			};
		}	
		@* TODO: Hide "Add new entry" text message *@
		<config-section label="@(deviceLocalizer["Devices"])" name="devices">
			<enumeration-component id="device-list" class="section-span-all" sorting="@deviceSortings">
				<list-component skeleton="@(Model.User == null)">
					<list-column-component name="flag" type="ListColumnType.Flag" with-converter></list-column-component>
					<list-data-column-component name="displayName" label="@deviceLocalizer["displayName"]" hide-label></list-data-column-component>
					<list-data-column-component name="typeName" label="@deviceLocalizer["typeName"]" max-width="120"></list-data-column-component>
					<list-data-column-component name="formatName" label="@deviceLocalizer["formatName"]" max-width="120"></list-data-column-component>
					<list-data-column-component name="enabled" label="@deviceLocalizer["enabled"]" type="DataColumnType.Boolean" live-editable="true" max-width="120"></list-data-column-component>
					<list-data-column-component name="created" label="@deviceLocalizer["created"]" type="DataColumnType.DateTime" max-width="120"></list-data-column-component>
					<list-data-column-component name="lastModified" label="@deviceLocalizer["lastModified"]" type="DataColumnType.DateTime" max-width="120"></list-data-column-component>
				</list-component>
			</enumeration-component>
		</config-section>
			
		@{
			var customerMappingSortings = new List<QueryParamSortingDto>()
			{
				new() { OrderColumn = "customer.displayName", Direction = SortDirection.Asc }
			};
		}
		@* @TODO: Add jumping point to customer *@
		<config-section label="@(mappingLocalizer["label"])" name="customerContexts">
			<enumeration-component id="customer-mapping-list" class="section-span-all" sorting="@customerMappingSortings">
				<list-component skeleton="@(Model.User == null)">
					<list-data-column-component name="customerName" reference-field="customer.displayName" label="@mappingLocalizer["customerName"]" hide-label></list-data-column-component>
					<list-data-column-component name="isAdmin" type="DataColumnType.Icon" live-editable="true" icon="hammer" width="36" icon-tooltip-yes="@mappingLocalizer["isAdminTrueTooltip"]" icon-tooltip-no="@mappingLocalizer["isAdminFalseTooltip"]" ></list-data-column-component>
					<list-column-component name="delete" with-converter width="36"></list-column-component>
				</list-component>
			</enumeration-component>
		</config-section>
	</form-component>

	<slide-out-component id="edit-panel" class="side-panel" position="Alignment.Right" modal anchor icon="pen-to-square" navigation width="370" open="@(ViewData["targetAction"]?.ToString() == "Detail")">
		<div class="content vanishing-scrollbar static-scrollbar">
			@if (ViewData["targetAction"]?.ToString() == "Detail")
			{
				@await Html.PartialAsync(ViewData["targetAction"] as string, ViewData["targetViewModel"])
			}
		</div>
		<button-component slot="button-left" class="dialog-button" data-action="reset" label="@localizer["resetButton"]"></button-component>
		@* TODO: Remove this with the correct button from UI Team which currently not exists *@
		<button-component slot="button-left" class="dialog-button" data-action="delete" style="display:none;"></button-component>
		<button-component slot="button-right" class="dialog-button dialog-button--secondary" data-action="cancel" label="@localizer["abortButton"]"></button-component>
		<button-component slot="button-right" class="dialog-button dialog-button--primary" data-action="save" label="@localizer["saveButton"]"></button-component>
	</slide-out-component>
</div>