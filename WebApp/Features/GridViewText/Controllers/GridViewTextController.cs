using System.Diagnostics.CodeAnalysis;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.PageView.GridView;
using Levelbuild.Frontend.WebApp.Features.GridViewText.ViewModels;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.GridViewText.Controllers;

/// <summary>
/// Controller for the configuration view of list view columns
/// </summary>
public class GridViewTextController : AdminController<GridViewTextDto>
{
	/// <summary>
	/// inject some helpful things into the controller
	/// </summary>
	/// <param name="logManager">logging</param>
	/// <param name="contextFactory">database context</param>
	/// <param name="userManager">injected UserManager</param>
	/// <param name="localizerFactory">injected StringLocalizerFactory</param>
	/// <param name="versionReader">injected VersionReader</param>
	public GridViewTextController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager, IExtendedStringLocalizerFactory localizerFactory, IVersionReader versionReader) :
		base(logManager, logManager.GetLoggerForClass<GridViewTextController>(), contextFactory, userManager, localizerFactory, versionReader)
	{
		
	}
	
	#region Views
	
	/// <summary>
	/// Renders the detail (edit) view
	/// </summary>
	/// <returns>rendered detail view</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Admin/GridViewTexts/Edit")]
	public IActionResult Detail([FromQuery(Name = "type")] GridViewTextType? textType)
	{
		return CachedPartial() ?? RenderPartial(new GridViewTextForm(ViewType.Edit)
		{
			TextType = textType
		});
	}
	
	#endregion
	
	#region Actions

	/// <inheritdoc />
	public override ActionResult<FrontendResponse> Query(QueryParamsDto parameters)
	{
		throw new NotImplementedException();
	}
	
	/// <summary>
	/// Returns a specific grid view text configuration as JSON
	/// </summary>
	/// <param name="id">identifier of a specific list view column</param>
	[HttpGet("/Api/GridViewTexts/{id:guid}")]
	public override ActionResult<FrontendResponse> Get(Guid id)
	{
		return HandleGetRequest<GridViewTextEntity, GridViewTextDto>(DatabaseContext.GridViewTexts, id);
	}
	
	/// <summary>
	/// Creates a new grid view text configuration in the database
	/// </summary>
	/// <param name="configurationDto">DTO for the new list view column</param>
	/// <returns>OK Response for successful creating and BAD Response when an errors occurred</returns>
	[HttpPost("/Api/GridViewTexts/")]
	public override async Task<ActionResult<FrontendResponse>> Create([FromBody] GridViewTextDto configurationDto)
	{
		configurationDto.Type = GridViewSectionElementType.Text;
		var result = await HandleCreateRequestAsync(DatabaseContext.GridViewTexts, configurationDto);
		if (result.Result is OkObjectResult objectResult)
		{
			var frontendResponse = objectResult.Value as FrontendResponse<GridViewTextDto>;
			GridViewSectionEntity.UpdateRowCount(DatabaseContext, frontendResponse?.Data.SectionId);
		}

		return result;
	}
	
	/// <summary>
	/// Updates a grid view text configuration in the database
	/// </summary>
	/// <param name="id">identifier for a specific list view column</param>
	/// <param name="configurationDto">DTO for the updated version of a list view column</param>
	/// <returns>OK Response for successful updating and BAD Response when an errors occurred</returns>
	/// <exception cref="ElementNotFoundException">slug is not existing</exception>
	[HttpPatch("/Api/GridViewTexts/{id:guid}")]
	public override async Task<ActionResult<FrontendResponse>> Update(Guid id, [FromBody] GridViewTextDto configurationDto)
	{
		// load current entity (remember section for rowcount update)
		var oldSectionId = (await DatabaseContext.GridViewTexts.FindAsync(id))?.SectionId;
		
		var result = await HandleUpdateRequestAsync(DatabaseContext.GridViewTexts, id, configurationDto);
		if (result.Result is OkObjectResult objectResult)
		{
			var frontendResponse = objectResult.Value as FrontendResponse<GridViewTextDto>;
			GridViewSectionEntity.UpdateRowCount(DatabaseContext, frontendResponse?.Data.SectionId);
			if (oldSectionId != frontendResponse?.Data.SectionId)
				GridViewSectionEntity.UpdateRowCount(DatabaseContext, oldSectionId);
		}
		return result;
	}
	
	/// <summary>
	/// Deletes a grid view text configuration
	/// </summary>
	/// <param name="id">identifier for a specific list view column</param>
	/// <returns>OK Response for successful deleting and BAD Response when an errors occurred</returns>
	[HttpDelete("/Api/GridViewTexts/{id:guid}")]
	public override ActionResult<FrontendResponse> Delete(Guid id)
	{
		var textEntity = DatabaseContext.GridViewTexts.Find(id);
		var result = HandleDeleteRequest(DatabaseContext.GridViewTexts, id);
		if (textEntity?.SectionId != null && result.Result is OkObjectResult)
			GridViewSectionEntity.UpdateRowCount(DatabaseContext, textEntity.SectionId);
		return result;
	}
	
	#endregion
}