@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@model Levelbuild.Frontend.WebApp.Features.GridViewText.ViewModels.GridViewTextForm
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("GridViewText", "detail");
	var typeLocalizer = LocalizerFactory.Create("GridViewTextType", "");
}
<form-component id="grid-view-headline-form" skeleton="@(Model is { ViewType: ViewType.Edit, GridViewText: null })">
	<config-section label="@localizer["sectionConfig"]">
		<div class="form__item">
			<input type="hidden" class="item__value" name="id" value="@Model.GridViewText?.Id"/>
		</div>
		<div class="form__item block__item">
			<label>@localizer["text"]</label>
			<input-component type="InputDataType.Translation" required="true" id="text-text" class="item__value" name="text"
			                 value="@(Model.GridViewText?.Text)" translation-prefix="/GridViewText">
			</input-component>
		</div>
		<div class="form__item block__item">
			<label>@localizer["textType"]</label>
			<lvl-button-group id="text-type" name="textType" value="@(Model.GridViewText?.TextType ?? GridViewTextType.Headline1)">
				<button-component label="@typeLocalizer[GridViewTextType.Headline1.ToString()]" value="@GridViewTextType.Headline1"></button-component>
				<button-component label="@typeLocalizer[GridViewTextType.Headline2.ToString()]" value="@GridViewTextType.Headline2"></button-component>
				<button-component label="@typeLocalizer[GridViewTextType.Headline3.ToString()]" value="@GridViewTextType.Headline3"></button-component>
			</lvl-button-group>
		</div>
		<div class="form__item block__item">
			<label>@localizer["color"]</label>
			<lvl-input id="text-color" name="color" value="@(Model.GridViewText?.Color)" type="color" class="item__value" placeholder="auto"></lvl-input>
			<legend>@(localizer["colorLegend"])</legend>
		</div>
	</config-section>
</form-component>
<button-component data-action="delete" label="@localizer["deleteButton"]" icon="trash" type="ButtonType.Secondary" color="ColorState.Error"></button-component>