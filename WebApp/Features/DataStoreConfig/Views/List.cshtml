@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminListPage
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.CreatePanel
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp
@{
	var localizer = LocalizerFactory.Create("DataStoreConfig", "");
	var localizerStoreType = LocalizerFactory.Create("DataStoreType", "");
	List<DataColumnValueDefinition> typeValues = Enum.GetValues(typeof(DataStoreType))
		.Cast<DataStoreType>()
		.Select(type => new DataColumnValueDefinition((int)type, localizerStoreType[type.GetString()]))
		.ToList();
	List<BaseColumnComponentTagHelper> columns =
	[
		new ListDataColumnComponentTagHelper()
		{
			Name = "name"
		},

		new ListDataColumnComponentTagHelper()
		{
			Name = "typeName",
			ReferenceField = "type",
			Label = localizer["type"],
			Type = DataColumnType.Enum,
			Values = typeValues
		},

		new ListDataColumnComponentTagHelper()
		{
			Name = "enabled",
			Type = DataColumnType.Boolean,
			LiveEditable = true,
			HideLabel = true
		}
	];
}
<script type="module" defer>
	Page.setMainPage('/Admin/DataStores')
	Page.setBreadcrumbs([{ label: '@localizer["list/pageTitle"]', url: Page.getMainPageUrl() }])
</script>
<vc:admin-list-page entity="DataStoreConfig" route-name="DataStores" localizer="@localizer" columns="@columns" open-on-row-click="true"></vc:admin-list-page>
<vc:create-panel entity="DataStoreConfig" route-name="DataStores" localizer="@localizer"></vc:create-panel>