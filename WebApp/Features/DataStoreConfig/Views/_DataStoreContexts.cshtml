@using Humanizer
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.DataStoreContext.ViewModels
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminListPage
@model Levelbuild.Frontend.WebApp.Features.DataStoreConfig.ViewModels.DataStoreConfigForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("DataStoreContext", "");
	
	var sortings = new List<QueryParamSortingDto>()
	{
		new()
		{
			OrderColumn = "Name",
			Direction = SortDirection.Asc
		}
	};
	var contextName = "";
	if (ViewData["targetViewModel"] != null && ViewData["targetAction"]?.ToString() == "Detail")
	{
		contextName = (ViewData["targetViewModel"] as DataStoreContextForm)?.DataStoreContext?.Name;
	}
}
<script type="module" defer>
	// disable save button
	Page.buttonConfig.saveButton.disabled = true

	// reactivate save button when content changes
	Page.getContentChangeSignal().addEventListener("abort", () => {
		Page.buttonConfig.saveButton.disabled = false
	})
</script>
<div class="list-view">
	
	<enumeration-component id="data-store-context-list" sorting="@sortings">
		<list-component identity-column="id">
			<list-data-column-component name="name" min-width="300"></list-data-column-component>
			<list-data-column-component name="customerName" label="@localizer["customerName"]"></list-data-column-component>
			<list-data-column-component name="enabled" label="@localizer["enabled"]" type="DataColumnType.Boolean" live-editable="false"></list-data-column-component>
		</list-component>
	</enumeration-component>
</div>

<vc:admin-list-page entity="DataStoreContext" route-name="DataStoreContexts" localizer="@localizer" menu-entry="DataStoreContexts"
                    parent-property-name="dataStoreId" use-custom-list="true">
</vc:admin-list-page>
<fab-component data-action="add"></fab-component>
<slide-out-component id="create-panel" class="side-panel" position="Alignment.Right" modal anchor heading="@localizer["list/newItem"]" width="370" open="@(ViewData["targetAction"]?.ToString() == "Create")">
	<div class="content vanishing-scrollbar static-scrollbar">
		@if (ViewData["targetAction"]?.ToString() == "Create")
		{
			@await Html.PartialAsync(ViewData["targetAction"] as string, ViewData["targetViewModel"])
		}
	</div>
	<button-component slot="button-left" data-action="cancel" label="@localizer["abortButton"]" type="ButtonType.Secondary" color="ColorState.Info"></button-component>
	<button-component slot="button-right" data-action="save" label="@localizer["saveButton"]" type="ButtonType.Primary"></button-component>
</slide-out-component>
<slide-out-component id="edit-panel" class="side-panel" position="Alignment.Right" heading="@contextName" modal anchor icon="pen-to-square" navigation width="370" open="@(ViewData["targetAction"]?.ToString() == "Detail")">
	<div class="content vanishing-scrollbar static-scrollbar">
		@if (ViewData["targetAction"]?.ToString() == "Detail")
		{
			@await Html.PartialAsync(ViewData["targetAction"] as string, ViewData["targetViewModel"])
		}
	</div>
	<button-component slot="button-left" data-action="reset" label="@localizer["resetButton"]" type="ButtonType.Tertiary" color="ColorState.Info"></button-component>
	@* //TODO: Remove this with the correct button from UI Team which currently not exists *@
	<button-component slot="button-left" class="dialog-button" data-action="delete" style="display:none;"></button-component>
	<button-component slot="button-right" data-action="cancel" label="@localizer["abortButton"]" type="ButtonType.Secondary" color="ColorState.Info"></button-component>
	<button-component slot="button-right" data-action="save" type="ButtonType.Primary" label="@localizer["saveButton"]"></button-component>
</slide-out-component>

@{
	var kebabEntity = "DataStoreContext".Kebaberize();
}
<script type="module" defer>
	const enumeration = document.getElementById('@(kebabEntity)-list')
	const list = enumeration?.querySelector('& > *')
	if (!list)
		console.warn('Datalist with id: @(kebabEntity)-list was not found.')
			
	@if (ViewData["targetAction"]?.ToString() == "Create")
	{
		@:Page.setPanelInfo(`${Page.getMainPageUrl()}/DataStoreContexts/Create`, {}, '@localizer["list/newItem"]')
	}
	Page.registerCreatePanel(document.getElementById('create-panel'));

	/**
	* opens a dialog to create a new configuration
	* @@param mouseEvent {MouseEvent} button click event
	*/
	document.querySelector('lvl-fab[data-action=add]').addEventListener("click", async () =>
	{
		await Page.showCreatePanel('DataStoreContexts/Create',  {type: Page.getFormData().type}, '@localizer["list/newItem"]')
		Page.setPanelInfo(`${Page.getMainPageUrl()}/DataStoreContexts/Create`, {type: Page.getFormData().type}, '@localizer["list/newItem"]');
		@* // TODO: remove as soon as all forms are lvl - form *@
		const form = Page.createSlideOut.querySelector('lvl-form')
		const parentInput = form.querySelector('input[name="dataStoreId"]')
		if (parentInput && Page.getFormData()?.id) {
			parentInput.value = Page.getFormData().id
			parentInput.dispatchEvent(new Event('change'));
		}
	})
	
	const handleCreateSaveButtonClick = async () => {
		const result = await Form.storeData(Page.createSlideOut.querySelector('form, lvl-form'), '/Api/DataStoreContexts', 'POST')
		if (!result)
			return
		Page.createSlideOut.open = false
		enumeration?.reload()
	}
	
	const createSaveButton = Page.createSlideOut.querySelector('[data-action="save"]')
	createSaveButton?.addEventListener('click', handleCreateSaveButtonClick, { signal: Page.getPageChangeSignal() })
	Page.createSlideOut.setAttribute('initDone', '')		
	Page.registerEditPanel(document.getElementById('edit-panel'))
	
	/**
	* Click on table row opens the detail view to edit the entity configuration
	* @@param rowContent {object} complete value map of the entity configuration 
	* @@param rowIndex {number}
	*/
	list.onRowClick = async (rowContent, index) => {
		const displayValue = rowContent.data['name']
		const slugValue = rowContent.data.slug ?? rowContent.data.id
		
		// load page and update page info
		if (Page.editSlideOut.open === false)
			await Page.showEditPanel(`${Page.getMainPageUrl()}/DataStoreContexts/${slugValue}`, {type: Page.getFormData().type}, displayValue)
		Page.setPanelInfo(`${Page.getMainPageUrl()}/DataStoreContexts/${slugValue}`, {type: Page.getFormData().type}, displayValue )
			
		// load user info
		let json = await Page.getJSON(`/Api/DataStoreContexts/${rowContent.data.id}`)
		await Page.setFormData(Page.editSlideOut.querySelector('form, lvl-form'), json.data)
		
		// set field name as panel heading (only if there is no static heading!)
		@if (!string.IsNullOrEmpty(contextName))
		{
			@:Page.editSlideOut.setAttribute('heading', json.data['name'])
		}
		
		// set navigation
		Page.editSlideOut.setAttribute('page-number', index + 1)
		Page.editSlideOut.setAttribute('page-count', list.count)
	}
	
	Page.editSlideOut.addEventListener('move-previous:click', () => {
		list.previous()
	})
	
	Page.editSlideOut.addEventListener('move-next:click', () => {
		list.next()
	})
	
	const handleEditSaveButtonClick = async () => {
		const form = Page.editSlideOut.querySelector('form, lvl-form')
		const result = await Form.storeData(form, `/Api/DataStoreContexts/${form.querySelector('.item__value[name="id"]').value}`, 'PATCH')
		if (!result)
			return
		Page.editSlideOut.open = false
		enumeration.reload()
	}
	
	const editSaveButton = Page.editSlideOut.querySelector('[data-action="save"]')
	editSaveButton?.addEventListener('click', handleEditSaveButtonClick, { signal: Page.getPageChangeSignal() })
	Page.editSlideOut.setAttribute('initDone', '')
</script>