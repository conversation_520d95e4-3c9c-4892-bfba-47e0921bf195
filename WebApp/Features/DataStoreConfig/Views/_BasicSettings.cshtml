@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@model Levelbuild.Frontend.WebApp.Features.DataStoreConfig.ViewModels.DataStoreConfigForm
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("DataStoreConfig", "detailForm");
	var typeLocalizer = LocalizerFactory.Create("DataStoreType", "");
}
<style>	
</style>
<div class="grid--centered @(Model.ViewType == ViewType.Edit ? "vanishing-scrollbar static-scrollbar" : "")">
	<form-component id="data-store-config-form" skeleton="@(Model is {ViewType: ViewType.Edit, DataStoreConfig: null})">
		<config-section label="@localizer["sectionInfo"]">
			<div class="form__item">
				<input type="hidden" class="item__value" name="id" value="@Model.DataStoreConfig?.Id"/>
			</div>
			<div class="form__item">
				<config-label target="data-store-name" label="@localizer["name"]"></config-label>
				<input-component id="data-store-name" name="name" class="item__value" required value="@Model.DataStoreConfig?.Name"></input-component>
			</div>
			@{
				List<AutocompleteOptionDefinition> typeOptions = Model.DataStoreTypes.Select(type => new AutocompleteOptionDefinition(type.ToString(), typeLocalizer[type.ToString()])).ToList();
			}
			<div class="form__item">
				<config-label target="data-store-type" label="@localizer["type"]" description=""></config-label>
				<autocomplete-component
					readonly
					type="InputDataType.String"
					id="data-store-type"
					options="typeOptions"
					name="type"
					value="@(Model.DataStoreConfig != null ? Model.DataStoreConfig.Type.ToString() : Model.DataStoreType?.ToString() ?? "")"
					class="item__value"
					placeholder="@localizer["pleaseChoose"]"
					required="true">
				</autocomplete-component>
			</div>
			<div class="form__item">
				<config-label target="data-store-enabled" label="@localizer["enabled"]" description="@localizer["enabledDescription"]"></config-label>
				<toggle-component id="data-store-enabled" name="enabled" class="item__value" value="@(Model.DataStoreConfig != null ? Model.DataStoreConfig.Enabled.ToString() : "false")"></toggle-component>
			</div>
		</config-section>
	</form-component>
</div>
