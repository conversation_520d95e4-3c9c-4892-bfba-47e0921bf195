using Levelbuild.Core.FrontendDtos.News;
using Levelbuild.Frontend.WebApp.Features.ViewManagement;

namespace Levelbuild.Frontend.WebApp.Features.News.ViewModels;

public class NewsModel
{
	/// <summary>
	/// is this a create or a edit view?
	/// </summary>
	public ViewType ViewType { get; init; }

	public NewsDto? News { get; init; }
	
	public NewsModel(ViewType viewType = ViewType.Create)
	{
		ViewType = viewType;
	}
}