@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@model Levelbuild.Frontend.WebApp.Features.News.ViewModels.NewsContentModel
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("NewsContent", "");
}
<div class="grid--centered @(Model.ViewType == ViewType.Edit ? "vanishing-scrollbar static-scrollbar" : "")">
	<form-component id="news-content-form" class="form" skeleton="@(Model.NewsContent == null && Model.ViewType == ViewType.Edit)">
		<config-section label="@(Model.ViewType == ViewType.Edit ? localizer["sectionSettings"] : null)" allow-collapse="false">
			<div class="form__item">
				<input type="hidden" class="item__value" name="id" value="@Model.NewsContent?.Id"/>
				<input type="hidden" class="item__value" name="newsId" value="@Model.News?.Id"/>
			</div>
			<div class="form__item">
				<config-label target="cultureId" label="@localizer["culture"]"></config-label>
				<autocomplete-component name="culture" output-name="cultureId" class="item__value" type="InputDataType.Guid" required="true" url="@(Model.News != null ? $"/Api/News/{Model.News.Id}/cultureAutocomplete" : "")"
				                        value="@Model.NewsContent?.CultureId" display-value="@Model.NewsContent?.CultureName" display-field="displayName" readonly="@(Model.ViewType == ViewType.Edit)"></autocomplete-component>
			</div>
			<div class="form__item">
				<config-label target="headline" label="@localizer["headline"]"></config-label>
				<input-component id="headline" name="headline" class="item__value" required value="@Model.NewsContent?.Headline"></input-component>
			</div>
			<div class="form__item">
				<div class="form__colspan_2">
					<config-label target="text" label="@localizer["text"]"></config-label>
					<rich-text-component id="text" name="text" class="item__value" size="large" value="@Model.NewsContent?.Text" allow-images="false" required></rich-text-component>
				</div>
			</div>
		</config-section>
	</form-component>
</div>