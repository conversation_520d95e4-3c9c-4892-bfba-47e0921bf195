@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@model Levelbuild.Frontend.WebApp.Features.Page.ViewModels.PageList
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("News", "list");

	List<BaseColumnComponentTagHelper> columns =
	[
		new ListColumnComponentTagHelper() { Type = ListColumnType.Flag, Name="categoryIcon", WithConverter = true },
		new ListDataColumnComponentTagHelper { Name = "headline", Label = localizer["headline"], HideLabel = true },
		new ListDataColumnComponentTagHelper { Name = "categoryName", Label = localizer["category"] },
		new ListDataColumnComponentTagHelper { Name = "customerName", Label = localizer["context"] },
		new ListDataColumnComponentTagHelper { Name = "created", Label = localizer["created"], Type = DataColumnType.Date },
		new ListDataColumnComponentTagHelper { Name = "released", Label = localizer["releaseDate"], Type = DataColumnType.Date },
		new ListColumnComponentTagHelper { Name = "releasedIcon", WithConverter = true },
		new ListColumnComponentTagHelper { Type = ListColumnType.OptionMenu, Name = "options", WithConverter = true }
	];
	List<DropdownMenuItemComponentTagHelper> releasedColumns =
	[
		new()
		{
			Action = "Revoke",
			Label = localizer["revoke"],
			IconLeft = "rotate-left",
			ClickFunction = "revokeNews"
		},
		new()
		{
			Action = "Delete",
			Label = localizer["delete"],
			IconLeft = "trash",
			IconLeftColor = "var(--clr-signal-error)",
			ClickFunction = "deleteNews",
			TextColor = "var(--clr-signal-error)"
		}
	];
	List<DropdownMenuItemComponentTagHelper> unreleasedColumns =
	[
		new()
		{
			Action = "Release",
			Label = localizer["release"],
			IconLeft = "check",
			ClickFunction = "releaseNews"
		},
		new()
		{
			Action = "Delete",
			Label = localizer["delete"],
			IconLeft = "trash",
			IconLeftColor = "var(--clr-signal-error)",
			ClickFunction = "deleteNews",
			TextColor = "var(--clr-signal-error)"
		}
	];
	List<QueryParamSortingDto> sortings =
	[
		new QueryParamSortingDto { OrderColumn = "created", Direction = SortDirection.Desc }
	];
	IList<FilterPanelSectionComponentTagHelper> sections =
	[
		new() { Name = "customer.displayName", Label = localizer["context"], MultiValue = true, ValuePreview = true },
		new() { Name = "created", Label = localizer["created"], Type = InputDataType.DateTime },
		new() { Name = "released", Label = localizer["releaseDateLong"], Type = InputDataType.DateTime }
	];
}

<script type="module" defer>
	// TODO: enable as soon as we are able to filter over joined columns
	Page.globalSearchbar.disabled = true
	Page.setMainPage('/Admin/News')
	Page.setBreadcrumbs([{ label: '@localizer["list/pageTitle"]', url: Page.getMainPageUrl() }])

	function getModuleIcon(data) {
		let icon = ''
		if (data.categoryIcon)
			icon = data.categoryIcon

		return `<i class="fa-light fa-${icon}"></i>`
	}

	const renderFlagIcon = (data) => getModuleIcon(data)
	const defaultSortingEnumeration = document.getElementById('news-list')
	defaultSortingEnumeration.querySelector('lvl-list-column[name="categoryIcon"]').converter = renderFlagIcon
	
	function getReleaseIcon(data) {
		const tooltip = data.released ? `@localizer["released"]` : '@localizer["notReleased"]'
		return `<i class="state-icon" style="--state-color:var(${data.released ? '--clr-wf-positive-primary' : '--clr-wf-progress-primary'});" data-tooltip="stateTooltip"></i><lvl-tooltip name="stateTooltip">${tooltip}</lvl-tooltip>`
	}
	const renderReleaseIcon = (data) => getReleaseIcon(data)
	defaultSortingEnumeration.querySelector('lvl-list-column[name="releasedIcon"]').converter = renderReleaseIcon

	const renderOptionMenu = (data) => {
		let optionMenuHtml
		if (data.released) {
			optionMenuHtml = `
				<lvl-menu class="filter-menu">
					@foreach (var option in releasedColumns)
					{
						@: @Html.Raw(option.CreateTagHelperOutput())
					}
				</lvl-menu>`
		} else {
			optionMenuHtml = `
				<lvl-menu class="filter-menu">
					@foreach (var option in unreleasedColumns)
					{
						@: @Html.Raw(option.CreateTagHelperOutput())
					}
				</lvl-menu>`
		}
		return optionMenuHtml
	}
	defaultSortingEnumeration.querySelector('lvl-list-column[name="options"]').converter = renderOptionMenu
</script>

<!-- Page list -->
<vc:filter-panel entity="News" route-name="News" sections="@sections" in-admin-config="true"></vc:filter-panel>
<vc:admin-list-page entity="News" route-name="News" columns="@columns" sorting="@sortings" localizer="@localizer"
                    open-on-row-click="true" filter-by-menu="true" display-property-name="headline">
</vc:admin-list-page>
<vc:create-panel entity="News" route-name="News" localizer="@localizer"></vc:create-panel>

<script>
	document.getElementById('news-list')?.addEventListener('list-row:click', async customEvent => {
		const rowData = customEvent.detail.rowData
		const menu = document.getElementById("news-menu")

		// CreatedBy
		menu.setInfoData(rowData, "info-createdBy-left", "createdBy")
		menu.setInfoData(rowData, "info-createdBy-right", "created")

		// ReleasedBy
		menu.setInfoData(rowData, "info-releasedBy-left", "releasedBy")
		menu.setInfoData(rowData, "info-releasedBy-right", "released")
		
		// show releaseInfo if news has not been released yet
		document.getElementById('releaseInfo').classList.remove('skeleton__block')
		document.getElementById('releaseInfo').dataset.state = rowData.released ? 'released' : 'unreleased'
	})
	
	async function releaseNews(event) {
		event.stopPropagation()
		const currentRow = getCurrentRow(event.target)
		
		Overlay.showWait("@localizer["releasing"]")
		const response = await fetch(`/Api/News/${currentRow.id}/Release`, { method: 'PATCH' })
		if (response.ok)
			document.getElementById('news-list').reload()
		Overlay.hideWait()
	}

	async function revokeNews(event) {
		event.stopPropagation()
		const currentRow = getCurrentRow(event.target)

		Overlay.showWait("@localizer["releasing"]")
		const response = await fetch(`/Api/News/${currentRow.id}/Revoke`, { method: 'PATCH' })
		if (response.ok)
			document.getElementById('news-list').reload()
		Overlay.hideWait()
	}
	
	async function deleteNews(event) {
		event.stopPropagation()
		const currentRow = getCurrentRow(event.target)

		const callback = async (elementId) => {
			Overlay.showWait("@localizer["deleting"]")
			const response = await fetch(`/Api/News/${elementId}`, { method: 'DELETE' })
			if (response.ok)
				document.getElementById('news-list').reload()
			Overlay.hideWait()
		}

		Page.showDeletionWarningToast('@localizer["deletionWarningHeading"]', '@localizer["deletionWarning"]', async () => await callback(currentRow.id), '@localizer["ok"]', '@localizer["abort"]')
	}
	
	function getCurrentRow(element) {
		const listLine = element.closest('lvl-list-line')
		const position = Number(listLine.dataset['position'])
		const newsList = document.getElementById('news-list')
		return newsList.querySelector('lvl-list').rows[position]
	}
</script>