@using Levelbuild.Core.EntityInterface
@inject IExtendedStringLocalizerFactory LocalizerFactory
@{
	var localizer = LocalizerFactory.Create("News", "List");
}
<script type="module" defer>
	Page.globalSearchbar.disabled = false
	Page.setMainPage(`/Public/News/`)
	Page.setInfo(Page.getMainPageUrl())
	Page.setBreadcrumbs([
		{ label: '@localizer["list/pageTitle"]', url: '/Public/News' }
	])

	// set user searchbar value 
	Page.globalSearchbar.value = sessionStorage.getItem('lvl:component:list:news:searchbar') ?? ''

	// Integrate global searchbar
	const enumeration = document.getElementById('news-list')
	Page.globalSearchbar.addEventListener('change', () => {
		sessionStorage.setItem('lvl:component:list:news:searchbar', Page.globalSearchbar.value)
		enumeration.term = Page.globalSearchbar.value
	}, { signal: Page.getPageChangeSignal() })

</script>
<enumeration-component id="news-list" url="/Api/News/Elements" limit="24">
	<lvl-news-list></lvl-news-list>
</enumeration-component>