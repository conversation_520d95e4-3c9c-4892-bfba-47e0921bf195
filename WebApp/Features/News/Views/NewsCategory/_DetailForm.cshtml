@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Core.SharedDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@model Levelbuild.Frontend.WebApp.Features.News.ViewModels.NewsCategoryModel
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var localizer = LocalizerFactory.Create("NewsCategory", "");
}

<div class="grid--centered @(Model.ViewType == ViewType.Edit ? "vanishing-scrollbar static-scrollbar" : "")">
	<form-component id="news-category-form" class="form" skeleton="@(Model.NewsCategory == null && Model.ViewType == ViewType.Edit)">
		<config-section label="@(Model.ViewType == ViewType.Edit ? localizer["sectionSettings"] : null)" allow-collapse="false">
			<div class="form__item">
				<input type="hidden" class="item__value" name="id" value="@Model.NewsCategory?.Id"/>
			</div>
			<div class="form__item">
				@{
					var customerColumns = new List<AutocompleteColumnDefinition>
					{
						new("displayName", localizer["name"], DataType.String)
					};
				}
				<config-label target="customer-id" label="@localizer["customer"]"></config-label>
				<autocomplete-component id="customer" name="customer" output-name="customerId" display-field="displayName"
				                        type="InputDataType.Guid" Url="/Api/Customers/autocomplete" columns="customerColumns"
				                        value="@(Model.NewsCategory?.CustomerId.ToString() ?? string.Empty)"
				                        display-value="@(Model.NewsCategory?.Customer?.DisplayName)"
				                        class="item__value" placeholder="@localizer["pleaseChoose"]" edit-link-url="/Admin/Customers/{slug}">
				</autocomplete-component>
			</div>
			<div class="form__item">
				<config-label target="name" label="@localizer["name"]"></config-label>
				<input-component id="name" name="name" class="item__value" required value="@Model.NewsCategory?.Name" type="InputDataType.Translation" translation-prefix="/NewsCategory"></input-component>
			</div>
			<div class="form__item">
				<config-label target="icon" label="@localizer["icon"]"></config-label>
				<input-component id="icon" name="icon" class="item__value" required value="@Model.NewsCategory?.Icon" type="InputDataType.Icon"></input-component>
			</div>
			<div class="form__item">
				<config-label target="imageId" label="@localizer["defaultImage"]" description="@localizer["defaultImageDescription"]"></config-label>
				<image-upload-component id="imageId" name="imageId" class="item__value" value="@Model.NewsCategory?.ImageId" required
				                        upload-url="/Api/NewsCategories/Images" download-url="/Api/NewsCategories/Images/{imageId}"></image-upload-component>
			</div>
		</config-section>
	</form-component>
</div>
