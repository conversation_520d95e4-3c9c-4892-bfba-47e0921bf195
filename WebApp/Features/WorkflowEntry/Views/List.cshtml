@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.Enums

@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp

<style>
	.list__content {
		width: 100%;
		display: grid;
		grid-template-rows: max-content auto;
		overflow: hidden;
		margin: var(--size-spacing-xs) var(--size-spacing-l) var(--size-spacing-xxl) var(--size-spacing-m);
		box-shadow: 0 0 0.2rem 0 var(--clr-shadow-weak), 0 0.2rem 0.4rem 0 var(--clr-shadow-weak);
		border-radius: var(--size-radius-m);
		background-color: var(--clr-background-lvl-0);

		& .list-view__wrapper {
			overflow: hidden;
		}

		& .multi-data-view {
			height: 100%;
		}
	}
</style>

@{
	var localizer = LocalizerFactory.Create("WorkflowEntry", "");

	var openFilters = new List<QueryParamFilterDto>
	{
		new()
		{
			FilterColumn = "executionDate",
			Operator = QueryParamFilterOperator.IsNull
		}
	};
	
	var escalationListFilters = new List<QueryParamFilterDto>
	{
	};
	
	var doneListFilters = new List<QueryParamFilterDto>
	{
		new()
		{
			FilterColumn = "executionDate",
			Operator = QueryParamFilterOperator.IsNotNull
		}
	};
}

<side-nav-component heading="@localizer["TabNavigation"]" width="228">
	<side-nav-item-component label="@localizer["Open"]" icon="clipboard-list-check" value="open" badge-state="BadgeColorState.Info"></side-nav-item-component>
	<side-nav-item-component label="@localizer["Escalation"]" icon="light-emergency-on" value="escalation" badge-state="BadgeColorState.Attention" disabled="true"></side-nav-item-component>
	<side-nav-item-component label="@localizer["Done"]" icon="clipboard-check" value="done" slot="bottom"></side-nav-item-component>
</side-nav-component>
<div class="list__content">
	<query-view-action-bar-component with-select-all="true"></query-view-action-bar-component>
	<div class="list-view__wrapper">
		<multi-data-view-component id="workflow-entry-multi-view" url="/Api/WorkflowEntries" height="100%" no-footer="true">
			<table-component id="workflow-entry-table" identity-column="id" clickable="true">
				<table-select-column-component></table-select-column-component>
				<table-data-column-component name="taskName" label="@localizer["taskName"]"></table-data-column-component>
				<table-data-column-component name="executionDate" type="InputDataType.Date" label="@localizer["executionDate"]" hidden="true"></table-data-column-component>
				<table-data-column-component name="executionUserName" label="@localizer["executionUser"]" hidden="true"></table-data-column-component>
				<table-data-column-component name="reference" label="@localizer["reference"]" with-converter></table-data-column-component>
				<table-data-column-component name="details" label="@localizer["details"]"></table-data-column-component>
				<table-data-column-component name="dueDate" type="InputDataType.Date" label="@localizer["dueDate"]"></table-data-column-component>
				<table-data-column-component name="receivedDate" type="InputDataType.DateTime" label="@localizer["receivedDate"]"></table-data-column-component>
				<div slot="no-data" style="display: grid; justify-items: center; gap: var(--size-spacing-m);">
					<span class="fa-light fa-file-magnifying-glass" style="font-size: var(--size-text-xl);"></span>
					<span>@localizer["noEntriesTitle"]</span>
				</div>
			</table-component>
		</multi-data-view-component>
	</div>
</div>

<script type="module" defer>
	Page.setTitle("@localizer["pageTitle"]");
	Page.setMainPage('/Public/Tasks')
	Page.setBreadcrumbs([{ label: '@localizer["pageTitle"]', url: Page.getMainPageUrl() }])
	
	let multiView = document.getElementById('workflow-entry-multi-view')
	let table = multiView.querySelector('#workflow-entry-table')
	let sideNav = document.querySelector('lvl-side-nav')
	switch(sessionStorage.getItem('workflow-entry-menu')) {
		case 'open':
			multiView.filters = @Json.Serialize(openFilters)
			sideNav.querySelector('lvl-side-nav-item[value="open"]').selected = true;
			table.querySelector('lvl-table-data-column[name=executionDate]').hidden = true;
			table.querySelector('lvl-table-data-column[name=executionUserName]').hidden = true;
			table.querySelector('lvl-table-data-column[name=dueDate]').hidden = false;
			break;
		case "escalation":
			/*multiView.filters = @Json.Serialize(escalationListFilters)
			sideNav.querySelector('lvl-side-nav-item[value="escalation"]').selected = true;
			table.querySelector('lvl-table-data-column[name=executionDate]').hidden = true;
			table.querySelector('lvl-table-data-column[name=executionUserName]').hidden = true;
			table.querySelector('lvl-table-data-column[name=dueDate]').hidden = false;*/
			break;
		case "done":
			multiView.filters = @Json.Serialize(doneListFilters)
			sideNav.querySelector('lvl-side-nav-item[value="done"]').selected = true;
			table.querySelector('lvl-table-data-column[name=executionDate]').hidden = false;
			table.querySelector('lvl-table-data-column[name=executionUserName]').hidden = false;
			table.querySelector('lvl-table-data-column[name=dueDate]').hidden = true;
			break;
		default:
			multiView.filters = @Json.Serialize(openFilters);
			sideNav.querySelector('lvl-side-nav-item[value="open"]').selected = true;
			table.querySelector('lvl-table-data-column[name=executionDate]').hidden = true;
			table.querySelector('lvl-table-data-column[name=executionUserName]').hidden = true;
			table.querySelector('lvl-table-data-column[name=dueDate]').hidden = false;
			break;
	}
	
	async function loadCounts() {
		try {
			const response = await fetch('/Api/WorkflowEntries/Counts');
			if (!response.ok) {
				console.error(`Error getting counts for workflow entries: ${response.status}`)
				return
			}
			const json = await response.json();
			if(json.error) {
				console.error(`Error getting counts for workflow entries: ${json.error}`);
				return
			}
			const counts = json.data;
			
			// Fill the different counts
			sideNav.querySelector('lvl-side-nav-item[value="open"]').withBadge = typeof counts.open != 'undefined';
			sideNav.querySelector('lvl-side-nav-item[value="open"]').badgeText = typeof counts.open != 'undefined' ? counts.open : null;
		} catch (error) {
			console.error(`Error getting counts for workflow entries.`)
		}
	}
	
	loadCounts(); // TODO: Execute function whenever necessary, but not to often ;)
	
	
	document.querySelector('#workflow-entry-table lvl-table-data-column[name="reference"]').converter = (data) => {
		if(data.referenceIcon != null && data.referenceIcon.length > 0){
			return `<span><i class="fa-light fa-${data.referenceIcon}" style="margin-right:var(--size-spacing-m)"></i>${data.referenceDisplayValue}</span>`;
		}
		return data.referenceDisplayValue;
	}
	
	document.querySelector('lvl-side-nav').addEventListener('nav-item:click', (event) => {
		let menuItem = event.target;
		if(menuItem != null && menuItem.value != null) {
			let workflowMultiView = document.getElementById("workflow-entry-multi-view");
			switch(menuItem.value){
				case "open":
					sessionStorage.setItem("workflow-entry-menu", "open")
					workflowMultiView.filters = @Json.Serialize(openFilters)
					table.querySelector('lvl-table-data-column[name=executionDate]').hidden = true;
					table.querySelector('lvl-table-data-column[name=executionUserName]').hidden = true;
					table.querySelector('lvl-table-data-column[name=dueDate]').hidden = false;
					break;
				case "escalation":
					/*sessionStorage.setItem("workflow-entry-menu", "escalation")
					table.filters = @Json.Serialize(escalationListFilters)
					table.querySelector('lvl-table-data-column[name=executionDate]').hidden = true;
					table.querySelector('lvl-table-data-column[name=executionUserName]').hidden = true;
					table.querySelector('lvl-table-data-column[name=dueDate]').hidden = false;
					*/
					break;
				case "done":
					sessionStorage.setItem("workflow-entry-menu", "done")
					workflowMultiView.filters = @Json.Serialize(doneListFilters)
					table.querySelector('lvl-table-data-column[name=executionDate]').hidden = false;
					table.querySelector('lvl-table-data-column[name=executionUserName]').hidden = false;
					table.querySelector('lvl-table-data-column[name=dueDate]').hidden = true;
					break;
			}
		}
	})
	
	table.onRowClick = async (record, _, newTab) => {
		if (record.inactive === true) {
			const toaster = document.getElementById('toaster')
			toaster.notifySimple({ heading: `${record.data.referenceDataSourceName} - @localizer["noDefaultPage"]`, type: 'warning' })
			return;
		}
		
		var url = `/Public/Pages/${record.data.defaultDetailPageSlug}/${record.data.referenceElementId}`;
		if (newTab)
			Page.openNewTab(url)
		else
			Page.load(url)
	}
	
</script>	