@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Core.FrontendDtos.Extensions
@using Levelbuild.Core.FrontendDtos.PageView
@using Levelbuild.Core.SharedDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.SinglePage.ViewModels
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.Extensions
@using Microsoft.IdentityModel.Tokens
@model Levelbuild.Frontend.WebApp.Features.SinglePage.ViewModels.SectionModel;
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var scriptLocalizer = LocalizerFactory.Create("ScriptMessages", "");
	var gridElements = Model.Section.GetGridSortedElements(out _);
	var sectionId = Model.Section.Id + (Model.ViewType == ViewType.Dialog ? "-dialog" : "");
}
<section-component id="@(sectionId)" heading="@(Model.Section.ShowTitle == true && Model.ViewType != ViewType.Dialog ? Model.Section.TitleTranslated : "")"
                   allow-collapse="@(Model.Section.AllowMinimize == true && Model.ViewType != ViewType.Dialog)" sticky="true" collapsed="@(Model.Section is { AllowMinimize: true, StartMinimized: true})">
	@if (Model.TextView == true)
	{
		<button-component slot="action" data-action="edit" size="FontSize.Medium" color="ColorState.Active" icon="edit" icon-style="IconStyle.Solid"></button-component>
	}
	<div class="section-grid">
		@foreach (var rowElements in gridElements.OrderBy(it => it.Key))
		{
			<span class="rowGroup @(rowElements.Value.Values.Where(element => element.Type == GridViewSectionElementType.Field && ((GridViewFieldDto)element).HideIfEmpty != true).ToList().Count == 0 ? "hide-empty" : "")">
				@foreach (var element in rowElements.Value.OrderBy(it => it.Key))
				{
					switch (element.Value.Type)
					{
						case GridViewSectionElementType.Field:
						{
							var fieldDto = (GridViewFieldDto)element.Value;
							if (fieldDto.DataType == InputDataType.Annotation)
							{
								<partial name="_Annotation.cshtml" model="@(new GridViewFieldModel(Model, fieldDto))"/>
								continue;
							}
							var label = !fieldDto.Label.IsNullOrEmpty() ? fieldDto.LabelTranslated : fieldDto.DataField?.Name ?? fieldDto.DataTypeTranslated;
							if (Model.TextView == true)
							{
								if (!fieldDto.DataFieldId.IsNullOrEmpty())
								{
									var isRichText = fieldDto.IsRichText.HasValue && fieldDto.IsRichText.Value;
									var fieldType = fieldDto.DataField?.Type == DataType.Guid ? fieldDto.DataField?.LookupDisplayField?.Type : fieldDto.DataField?.Type;

									<span class="grid-element-wrapper overview" style="grid-row-start: @fieldDto.RowStart; grid-row-end: @fieldDto.RowEnd; grid-column-start: @fieldDto.ColStart; grid-column-end: @fieldDto.ColEnd;">
										<label for="@fieldDto.DataField?.Name">@label</label>
										<lvl-value-formatter class="@(isRichText ? "html__wrapper" : "")"
										                     name="@fieldDto.DataField?.Name"
										                     type="@((fieldType ?? DataType.String).ToInputDataType().GetTypeAsString().ToLower())"
										                     format-type="@fieldDto.DataField?.FormatType?.ToString().ToLowerFirstChar()"
										                     label-true="@(fieldDto.DataField?.FormatType == DataFieldFormatType.CustomText ? fieldDto.DataField?.LabelTrue : null)"
										                     label-false="@(fieldDto.DataField?.FormatType == DataFieldFormatType.CustomText ? fieldDto.DataField?.LabelFalse : null)"
										                     text-align="@fieldDto.TextAlign?.ToString().ToLower()"
										                     sign="@(fieldDto.DataField?.Sign ?? "")"
										                     @(isRichText ? "rich-text" : "")></lvl-value-formatter>
									</span>
								}
							}
							else {
								if (!string.IsNullOrEmpty(fieldDto.DataFieldId))
								{
									<partial name="_Field.cshtml" model="@(new GridViewFieldModel(Model, fieldDto))"/>
								}
							}

							break;
						}
						case GridViewSectionElementType.Text:
							<partial name="_Text.cshtml" model="@((GridViewTextDto)element.Value)"/>
							break;
						case GridViewSectionElementType.Page when Model.ViewType != ViewType.Dialog:
						{
							var gridViewPageElement = (GridViewPageDto)element.Value;
							var embeddedPageModel = new EmbeddedPageModel()
							{
								Page = gridViewPageElement,
								TextView = Model.TextView
							};
							<span class="grid-element-wrapper" style="grid-row-start: @gridViewPageElement.RowStart; grid-row-end: @gridViewPageElement.RowEnd; grid-column-start: @gridViewPageElement.ColStart; grid-column-end: @gridViewPageElement.ColEnd;">
								@if (gridViewPageElement.GridViewPageType == GridViewPageType.Tile)
								{
									<partial name="_EmbeddedTile.cshtml" model="@embeddedPageModel"/>
								}
								else
								{
									<partial name="_EmbeddedPage.cshtml" model="@embeddedPageModel"/>
								}
							</span>
							break;
						}
					}
				}
			</span>
		}
	</div>
</section-component>
<script type="text/javascript">
	document.getElementById('@(Model.Section.Id)').querySelector('lvl-button[data-action=edit]')?.addEventListener('click', async () => {
		let editDialog = document.getElementById('section-@(Model.Section.Id)-edit')
		if (editDialog) {
			editDialog.setAttribute('open', '')
			
			// set form to skeleton, reset and fill with current data
			let form = editDialog.querySelector('lvl-form')
			form.setAttribute('skeleton', '')
			form.reset()
			form.setValues(Page.getFormData(), true)
			form.removeAttribute('skeleton')
			
			return
		}
		
		editDialog = document.createElement('lvl-dialog')
		editDialog.setAttribute('id', 'section-@(Model.Section.Id)-edit')
		editDialog.setAttribute('heading', '@(Model.Section.ShowTitle == true ? Model.Section.TitleTranslated : "")')
		editDialog.setAttribute('width', '700') // TODO: make configurable?!
		editDialog.setAttribute('modal', '')
		
		const saveButton = document.createElement('lvl-button')
		saveButton.setAttribute('slot', 'button-right')
		saveButton.setAttribute('label', '@scriptLocalizer["save"]')
		saveButton.setAttribute('type', 'primary')
		saveButton.addEventListener('click', async () => {
			let form = editDialog.querySelector('lvl-form')
			
			if (!form.hasChanges()) {
				let toaster = document.getElementById('toaster')
				return toaster.notifySimple({ heading: "@scriptLocalizer["NothingToStore"]", type: 'info' })
			}
			
			Overlay.showWait("@scriptLocalizer["elementIsSaved"]")
			const result = await Form.storeData(form, `/Api/DataSources/@(Model.Page?.DataSourceId)/Elements/${Page.getFormData().Id}`, 'PATCH')

			// update form data (formData is overwritten inside form-service but not from serverData which leads to destroyed lookup fields)
			if (result != null && !result.error) {
				Page.saveFormData(result.data.values)
				await Page.setFormData(document.getElementById(`page-detail`).querySelector('lvl-form'), result.data.values) // await is important to make sure form contains new data before updating blueprint!
				
				// update pinSource
				Page.parseBlueprintConfig(result.data)
				
				editDialog.removeAttribute('open')
			}
			
			Overlay.hideWait()
		})
		editDialog.append(saveButton)
			
		const abortButton = document.createElement('lvl-button')
		abortButton.setAttribute('slot', 'button-left')
		abortButton.setAttribute('label', '@scriptLocalizer["abort"]')
		abortButton.setAttribute('type', 'tertiary')
		abortButton.setAttribute('color', 'info')
		abortButton.addEventListener('click', () => {
			// reset blueprint preview on abort
			let blueprintPreview = editDialog.querySelector('lvl-blueprint')
			if (blueprintPreview)
				blueprintPreview.previewTarget = { ...blueprintPreview.previewTarget, x: Page.getFormData().AnnotationX, y: Page.getFormData().AnnotationY }
			editDialog.removeAttribute('open')
		})
		editDialog.append(abortButton)
		
		// append dialog
		document.getElementById('content').append(editDialog)
		
		// load form and set to skeleton mode
		const overlay = document.getElementById('overlay');
		if (overlay) {
			overlay.text = '@scriptLocalizer["oneMomentPlease"]';
			overlay.open = true;
		}
		fetch('/Public/GridViewSections/@(Model.Section.Id)?inline').then(function (response) {
			return response.text()
		}).then(function (html) {
			let form = document.createElement('lvl-form')
			form.setAttribute('class', 'section-dialog')
			form.setAttribute('skeleton', '')
			Page.setInnerHTML(form, html)
			editDialog.append(form)
			editDialog.setAttribute('open', '')

			// fill with data
			form.setValues(Page.getFormData(), true)

			// disable skeleton
			form.removeAttribute('skeleton')
			overlay.open = false
		})
	})
</script>