@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Core.FrontendDtos.Extensions
@using Levelbuild.Core.SharedDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@using Microsoft.IdentityModel.Tokens
@using SharpCompress
@model Levelbuild.Frontend.WebApp.Features.SinglePage.ViewModels.GridViewFieldModel
@inject IExtendedStringLocalizerFactory LocalizerFactory

@{
	var id = Model.Field.Id.ToString();
	var name = Model.Field.DataField?.Name;
	var label = !Model.Field.Label.IsNullOrEmpty() ? Model.Field.LabelTranslated : Model.Field.DataField?.Name ?? Model.Field.DataTypeTranslated;
	var isRequired = Model.Field.Required == true;
	var isReadonly = Model.Readonly || Model.Field.Readonly == true || Model.Field.DataFieldId.IsNullOrEmpty() || Model.Field.DataField?.VirtualDataFieldId != null || Model.Field.DataField?.SystemField == true;
	var defaultValue = Model.ViewType == ViewType.Create ? !string.IsNullOrEmpty(Model.Field.DefaultValue) ? Model.Field.DefaultValue : Model.Field.DataField?.DefaultValue : null;
	var fieldLocalizer = LocalizerFactory.Create("DataField", "");
}
<span class="grid-element-wrapper" style="grid-row-start: @Model.Field.RowStart; grid-row-end: @Model.Field.RowEnd; grid-column-start: @Model.Field.ColStart; grid-column-end: @Model.Field.ColEnd;">
	@switch (Model.Field.DataType)
	{
		case InputDataType.Boolean:
			<text>
				<toggle-component id="@id"
				                  name="@name"
				                  label="@label"
				                  default="@defaultValue"
				                  readonly="@isReadonly"></toggle-component>
			</text>
			break;
		case InputDataType.Text:
			if (Model.Field.IsRichText is true)
			{
				<rich-text-component id="@id"
				                     name="@name"
				                     label="@label"
				                     default="@defaultValue"
				                     required="@isRequired"
				                     readonly="@isReadonly"
				                     placeholder="@(Model.Field.PlaceholderTranslated)"
				                     lookup-field="@Model.Field.DataField?.VirtualLookupField?.Name"
				                     style="height:@((Model.Field.RowEnd - Model.Field.RowStart) * 50)px;"
				></rich-text-component>
			}
			else
			{
				<textarea-component id="@id"
				                    name="@name"
				                    label="@label"
				                    default="@defaultValue"
				                    required="@isRequired"
				                    readonly="@isReadonly"
				                    placeholder="@(Model.Field.PlaceholderTranslated)"
				                    lookup-field="@Model.Field.DataField?.VirtualLookupField?.Name"
				                    style="min-height:@((Model.Field.RowEnd - Model.Field.RowStart) * 50)px;"></textarea-component>
			}
			break;
		case InputDataType.Guid:
			var columns = new List<AutocompleteColumnDefinition>();

			// set display field as first column
			var displayField = Model.Field.DataField?.LookupDisplayField;

			if (displayField != null)
				columns.Add(new AutocompleteColumnDefinition("LABEL", fieldLocalizer[displayField.Name!], displayField.Type ?? DataType.String)
				{
					DecimalPlaces = displayField.DecimalPlaces,
					Sign = displayField.Sign
				});

			Model.Field.DataField?.Columns?.OrderBy(column => column.Position).ForEach(column =>
			{
				if (column.DisplayField == null)
					return;
				columns.Add(new AutocompleteColumnDefinition(column.DisplayField.Name!, fieldLocalizer[column.DisplayField.Name!], column.DisplayField.Type ?? DataType.String)
				{
					DecimalPlaces = column.DisplayField.DecimalPlaces,
					Sign = column.DisplayField.Sign
				});
			});
			var expandedFilters = Model.Field.DataField?.Filters?.Select(QueryParamFilterDto.FromDataFieldFilter).ToList() ?? [];
			if (Model.Field.DataField?.FieldType == DataFieldType.LookupField && (Model.Field.DataField?.FilterSelf.GetValueOrDefault() ?? false))
			{
				expandedFilters.Add(new QueryParamFilterDto
				{
					FilterColumn = "Id",
					Operator = QueryParamFilterOperator.NotEquals,
					CompareValue = "##Id##"
				});
			}

			<text>
				<autocomplete-component id="@id"
				                        name="@name"
				                        url="/Api/LookupFields/@(Model.Field.DataFieldId)/Elements"
				                        filters="@expandedFilters"
				                        label="@label"
				                        default="@defaultValue"
				                        type="@(Model.Field.DataType ?? InputDataType.String)"
				                        nullable="@(Model.Field.DataType == InputDataType.Guid)"
				                        required="@isRequired"
				                        readonly="@isReadonly"
				                        placeholder="@Model.Field.PlaceholderTranslated"
				                        tooltip="@Model.Field.HelpTextTranslated"
				                        columns="columns"
				                        column-view="@(Model.Field.DataField?.ColumnView == true)">
				</autocomplete-component>
				<script>
					document.getElementById('@id').addEventListener('virtual-field-change', (event) => {
						const form = event.target.closest('lvl-form')
						const currentValue = event.target.value
						if (!currentValue) {
							form.querySelectorAll(`[lookup-field="${event.target.name}"]`).forEach(virtualField => virtualField.value = null)
							return
						}
						
						const rowData = event.target.rowData
						if (!rowData?.virtualFieldValues)
							return

						for (const virtualFieldName of Object.keys(rowData.virtualFieldValues))
							form.setValue(virtualFieldName, rowData.virtualFieldValues[virtualFieldName])
					})
				</script>
			</text>
			break;
		default:
			<text>
				<input-component id="@id"
				                 name="@name"
				                 label="@label"
				                 default="@defaultValue"
				                 type="@(Model.Field.DataField?.InputDataType ?? Model.Field.DataField?.Type?.ToInputDataType() ?? InputDataType.String)"
				                 ignore-timezone="@(Model.Field.DataField?.Type is DataType.TimeFixed or DataType.DateTimeFixed)"
				                 multi-value="@(Model.Field.DataField?.Multi == true)"
				                 required="@isRequired"
				                 readonly="@isReadonly"
				                 placeholder="@Model.Field.PlaceholderTranslated"
				                 tooltip="@Model.Field.HelpTextTranslated"
				                 lookup-field="@Model.Field.DataField?.VirtualLookupField?.Name"
				                 sign="@(Model.Field.DataField?.Sign)">
				</input-component>
			</text>
			break;
	}
</span>