using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Net.Mime;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.DataStoreInterface.Filter;
using Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.DataSource;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Core.SharedDtos.Enums;
using Levelbuild.Entities;
using Levelbuild.Entities.Extensions.Features;
using Levelbuild.Entities.Features.DataField;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.MultiPage.Controllers;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Features.Viewer.Email.Services;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Extensions;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Levelbuild.Frontend.WebApp.Shared.Services.Files;
using Levelbuild.Frontend.WebApp.Shared.Utils;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using SharpCompress;

namespace Levelbuild.Frontend.WebApp.Features.SinglePage.Controllers;

/// <summary>
/// Controller for single data pages in the user section
/// </summary>
public class SinglePageController : FrontendController
{
	private readonly IDbContextFactory<CoreDatabaseContext> _contextFactory;
	private readonly CoreDatabaseContext _databaseContext;

	private readonly UserManager _userManager;
	private readonly IEmailReader _emailReader;

	private readonly IDeepZoomHelperService _deepZoomHelperService;
	private readonly IThumbnailHelperService _thumbnailHelperService;
	private IExtendedStringLocalizerFactory _stringLocalizerFactory;

	/// <summary>
	/// default constructor
	/// </summary>
	/// <param name="logManager"></param>
	/// <param name="contextFactory"></param>
	/// <param name="userManager">injected UserManager</param>
	/// <param name="versionReader">injected VersionReader</param>
	/// <param name="deepZoomHelperService">injected IDeepZoomHelperService</param>
	/// <param name="thumbnailHelperService">injected IThumbnailHelperService</param>
	/// <param name="emailReader">injected EmailReader</param>
	/// <param name="stringLocalizerFactory"></param>
	public SinglePageController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager,
								IVersionReader versionReader, IDeepZoomHelperService deepZoomHelperService, IThumbnailHelperService thumbnailHelperService, IEmailReader emailReader,
								IExtendedStringLocalizerFactory stringLocalizerFactory) : base(logManager, logManager.GetLoggerForClass<MultiPageController>(), versionReader)
	{
		_contextFactory	= contextFactory;
		_databaseContext = contextFactory.CreateDbContext();
		_userManager = userManager;
		_deepZoomHelperService = deepZoomHelperService;
		_thumbnailHelperService = thumbnailHelperService;
		_emailReader = emailReader;
		_stringLocalizerFactory = stringLocalizerFactory;
	}

	/// <summary>
	/// loads a dataset from the given data source by its ID
	/// </summary>
	/// <param name="dataSourceId">ID of a DataSourceEntity</param>
	/// <param name="elementId">ID of a dataset within this data source</param>
	/// <returns></returns>
	[ExcludeFromCodeCoverage]
	[HttpGet("/Api/DataSources/{dataSourceId:guid}/Elements/{elementId}")]
	public async Task<ActionResult<FrontendResponse>> GetElementAsync(Guid dataSourceId, string elementId)
	{
		if (elementId.IsNullOrEmpty())
			return GetNotFoundResponse($"elementId may not be empty");

		var element = await ElementUtils.GetElementWithVirtualFieldsAsync(_stringLocalizerFactory, _databaseContext, _deepZoomHelperService, dataSourceId, elementId);
		if (element == null)
			return GetNotFoundResponse($"Element with id: {elementId} could not be found in DataSource with id {dataSourceId}");

		return GetOkResponse(element);
	}

	/// <summary>
	/// loads a file from the given data source by its ID
	/// </summary>
	/// <param name="dataSourceId">ID of a DataSourceEntity</param>
	/// <param name="fileId">ID of a file within this data source</param>
	/// <returns></returns>
	[HttpGet("/Api/DataSources/{dataSourceId:guid}/Files/{fileId}")]
	public async Task<ActionResult<object>> GetFileAsync(Guid dataSourceId, string fileId)
	{
		DataStoreFileStream? fileStream = null;
		try
		{
			var dataSource = await _databaseContext.DataSources.FindAsync(dataSourceId);

			if (dataSource == null)
				return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");

			fileStream = await dataSource.GetFileAsync(fileId);

			if (fileStream == null)
				return GetNotFoundResponse($"File with id: {fileId} could not be found.");

			return File(fileStream, MediaTypeNames.Application.Octet, fileStream.Name);
		}
		catch (Exception e)
		{
			fileStream?.Dispose();

			Logger.Error(e, "File could not be loaded");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// returns possible lookup fields for the given autocomplete field inside the single data page
	/// </summary>
	/// <param name="lookupFieldId">ID of the current field</param>
	/// <param name="parameters">query parameters</param>
	/// <returns></returns>
	[HttpGet("/Api/LookupFields/{lookupFieldId:guid}/Elements")]
	public async Task<ActionResult<FrontendResponse>> GetLookupFieldElementsAsync(Guid lookupFieldId, QueryParamsDto parameters)
	{
		DataFieldEntity? dataFieldEntity = _databaseContext.DataFields
			.Include(field => field.LookupSource)
			.Include(field => field.LookupDisplayField)
			.Include(field => field.Columns)
			.ThenInclude(column => column.DisplayField)
			.ThenInclude(field => field!.LookupDisplayField)
			.FirstOrDefault(field => field.Id == lookupFieldId);
		if (dataFieldEntity == null)
			return GetBadRequestResponse($"DataFieldEntity with id: {lookupFieldId} could not be found");

		if (dataFieldEntity.LookupSource == null)
			return GetBadRequestResponse($"DataFieldEntity with id: {lookupFieldId} is no valid LookupField");

		// select values from referenced lookup definition
		var fields = new List<DataStoreQueryField>
		{
			new("Id")
		};

		if (dataFieldEntity.LookupDisplayField != null)
			fields.Add(new(dataFieldEntity.LookupDisplayField.Name));

		// include additional display columns
		foreach (var column in dataFieldEntity.Columns)
		{
			var displayField = column.DisplayField;
			if (displayField == null)
				continue;

			switch (displayField)
			{
				case { FieldType: DataFieldType.VirtualField, VirtualDataStoreQueryName: not null }:
					fields.Add(new DataStoreQueryField(displayField.VirtualDataStoreQueryName, displayField.Name));
					break;
				case { FieldType: DataFieldType.LookupField, LookupDisplayField: not null }:
					fields.Add(new DataStoreQueryField(displayField.Name + "." + displayField.LookupDisplayField.Name, displayField.Name));
					break;
				default:
				{
					if (displayField.FieldType == DataFieldType.DataField)
						fields.Add(new DataStoreQueryField(displayField.Name));
					break;
				}
			}
		}

		// are there additional virtual fields based on this field we need to select?
		var virtualFields = _databaseContext.DataFields
			.Where(field => field.VirtualLookupFieldId == lookupFieldId && field.HasVirtualData && field.VirtualDataFieldId != null)
			.Include(field => field.VirtualDataField)
			.ThenInclude(dataField => dataField!.LookupDisplayField);

		foreach (var virtualField in virtualFields)
		{
			string? queryName = null;
			switch (virtualField.VirtualDataField!.FieldType)
			{
				case DataFieldType.VirtualField:
				{
					queryName = virtualField.VirtualDataField.VirtualDataStoreQueryName;
					break;
				}
				case DataFieldType.LookupField:
				{
					if (virtualField.VirtualDataField.LookupDisplayField != null)
						queryName = virtualField.VirtualDataField.Name + "." + virtualField.VirtualDataField.LookupDisplayField.Name;
					break;
				}
				case DataFieldType.DataField:
				{
					queryName = virtualField.VirtualDataField.Name;
					break;
				}
			}

			if (virtualField.Type == DataType.Guid)
			{
				if (queryName == null)
					continue;

				var queryParts = queryName.Split('.');
				queryParts[^1] = "Id";
				var queryId = string.Join('.', queryParts);

				fields.Add(new DataStoreQueryField(queryId, virtualField.Name + ".Id"));
				fields.Add(new DataStoreQueryField(queryName, virtualField.Name + ".Name"));
			}
			else if (queryName != null)
				fields.Add(new DataStoreQueryField(queryName, virtualField.Name));
		}

		// Remove duplicate entries, since Storage may crash otherwise
		fields = fields.GroupBy(queryField => queryField.Alias ?? queryField.Name).Select(x => x.First()).ToList();

		var query = new DataStoreQuery(fields).WithPaging(parameters.Limit, parameters.Offset);

		// create term filter
		var termFilter = new QueryFilterGroup(QueryFilterLinkType.Or);
		if (!string.IsNullOrEmpty(parameters.Term))
		{
			if (Guid.TryParse(parameters.Term, out _))
				termFilter.AddFilter(new EqualsFilter(new QueryFilterField("Id"), parameters.Term));
			else
			{
				// display field
				if (dataFieldEntity.LookupDisplayField != null)
					termFilter.AddFilter(PrepareFilter(dataFieldEntity.LookupDisplayField, parameters.Term));

				// additional display columns
				dataFieldEntity.Columns.ForEach(column =>
				{
					var displayField = column.DisplayField;
					switch (displayField)
					{
						case null:
							return;
						case { FieldType: DataFieldType.VirtualField, VirtualDataStoreQueryName: not null }:
							termFilter.AddFilter(PrepareFilter(displayField.Type, displayField.VirtualDataStoreQueryName, parameters.Term));
							break;
						case { FieldType: DataFieldType.LookupField, LookupDisplayField: not null }:
							termFilter.AddFilter(PrepareFilter(displayField.LookupDisplayField.Type,
															   displayField.Name + "." + displayField.LookupDisplayField.Name, parameters.Term));
							break;
						default:
						{
							if (displayField.FieldType == DataFieldType.DataField)
								termFilter.AddFilter(PrepareFilter(displayField, parameters.Term));
							break;
						}
					}
				});
			}
		}

		// parse additional configured filters
		QueryFilterGroup? filters = null;
		if (parameters.Filters?.Count > 0)
		{
			var lookupSource = _databaseContext.DataSources
				.Include(dataSource => dataSource.Fields)
				.FirstOrDefault(dataSource => dataSource.Id == dataFieldEntity.LookupSourceId);
			if (lookupSource != null)
				filters = ParseFrontendFilters(lookupSource, parameters.Filters);
		}

		// combine and apply filters
		if (termFilter.Filters.Count > 0)
		{
			if (filters != null)
				filters.AddFilterGroup(termFilter);
			else
				filters = termFilter;
		}

		// add filters (if any)
		if (filters != null)
			query.WithFilter(filters);

		query.WithOrderBy(new List<DataStoreElementSort> { new(dataFieldEntity.LookupDisplayField?.Name ?? "Id") }).WithCountAll();
		
		var elements = await dataFieldEntity.LookupSource?.GetElementsAsync(query);
		var elementList = elements?.ToList().Select(element =>
		{
			// basic result
			var autocompleteDto = new AutocompleteDto
			{
				Value = element.Id,
				Label = element.Values[dataFieldEntity.LookupDisplayField?.Name ?? "Id"]?.ToString(),
			};

			// additional columns
			if (dataFieldEntity.Columns.Any())
				autocompleteDto.ColumnValues = dataFieldEntity.Columns.Where(column => column.DisplayField != null).ToList()
					.Select(column => new KeyValuePair<string, object?>(column.DisplayField!.Name,
																		element.Values.TryGetValue(column.DisplayField.Name, out var value) ? value : null))
					.ToDictionary(x => x.Key, x => x.Value);

			// additional virtual fields if any
			if (virtualFields.Any())
				autocompleteDto.VirtualFieldValues = virtualFields.ToList()
					.Select(field =>
					{
						if (field.VirtualDataField?.Type == DataType.Guid)
						{
							var valueMap = new Dictionary<string, object>
							{
								{ "id", element.Values[field.Name + ".Id"] ?? "" },
								{ "name", element.Values[field.Name + ".Name"] ?? "" }
							};
							return new KeyValuePair<string, object?>(field.Name, valueMap);
						}

						element.Values.TryGetValue(field.Name, out var value);
						return new KeyValuePair<string, object?>(field.Name, value);
					})
					.ToDictionary(x => x.Key, x => x.Value);

			return autocompleteDto;
		}).ToList();

		// create result
		var queryResult = new AutocompleteQueryResultDto
		{
			Rows = elementList ?? new List<AutocompleteDto>(),
			Count = elements?.Count ?? 0,
			CountTotal = elements?.CountTotal ?? 0
		};
		return GetOkResponse(queryResult);
	}

	/// <summary>
	/// Creates a new dataset inside the given datasource 
	/// </summary>
	[HttpPost("/Api/DataSources/{dataSourceId:guid}/Elements")]
	public async Task<ActionResult<FrontendResponse>> CreateElement(Guid dataSourceId, [FromBody] Dictionary<string, object?> values,
																	[FromQuery] string? fileUploadId)
	{
		try
		{
			var dataSource = await _databaseContext.DataSources
								 .Include(dataSource => dataSource.Fields).ThenInclude(field => field.LookupDisplayField)
								 .Include(dataSource => dataSource.Fields).ThenInclude(field => field.VirtualLookupField)
								 .FirstOrDefaultAsync(entity => entity.Id == dataSourceId);
			if (dataSource == null)
				throw new ElementNotFoundException($"DataSource configuration with id: {dataSourceId} could not be found");
			
			if (fileUploadId != null && !dataSource.AllowFile)
				return GetBadRequestResponse("File upload is not allowed!");
			
			var user = await _userManager.GetCurrentUserAsync();

			var virtualFieldNames = dataSource.Fields.Where(field => field.FieldType == DataFieldType.VirtualField)
				.Select(field => field.Name)
				.ToList();
			foreach (var virtualFieldName in virtualFieldNames)
			{
				values.Remove(virtualFieldName);
			}

			var elementData = !fileUploadId.IsNullOrEmpty()
								  ? new DataStoreElementData(values, new List<string> { "testgroup" }, fileUploadId!)
								  : new DataStoreElementData(values, new List<string> { "testgroup" });
			elementData.PrepareValuesForDataStore(dataSource.Fields);

			var lookupDisplayQueryFields = dataSource.Fields.Where(field => field is { FieldType: DataFieldType.LookupField, LookupDisplayFieldId: not null })
				.Select(field => new DataStoreQueryField(field.Name + "." + field.LookupDisplayField!.Name, field.Name + ".Display"))
				.ToList();

			var origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, user.DisplayName);
			var successInfo = await dataSource.CreateElementAsync(elementData, origin, lookupFields: lookupDisplayQueryFields);
			if (successInfo.ElementId == "")
				return GetBadRequestResponse($"Failed to save file");

			//cache deep zoom image version of file if necessary
			if (dataSource.Type == DataSourceType.Blueprint && successInfo.ElementData?.FileInfo?.Id != null)
				_ = _deepZoomHelperService.GetAndCacheDeepZoomImageAsync(_databaseContext, successInfo.ElementData.FileInfo.Id, dataSource);

			return GetOkResponse();
		}
		catch (Exception e)
		{
			Logger.Error(e, "Element could not be created");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Updates an existing dataset inside the given datasource with the given elementId
	/// </summary>
	[HttpPatch("/Api/DataSources/{dataSourceId:guid}/Elements/{elementId:guid}")]
	public async Task<ActionResult<FrontendResponse>> UpdateElement(Guid dataSourceId, Guid elementId, [FromBody] Dictionary<string, object?>? values,
																	[FromQuery] string? fileUploadId)
	{
		try
		{
			var dataSource = await _databaseContext.DataSources
								 .Include(dataSource => dataSource.Fields).ThenInclude(field => field.LookupDisplayField)
								 .Include(dataSource => dataSource.Fields).ThenInclude(field => field.VirtualLookupField)
								 .Include(dataSource => dataSource.Workflows).ThenInclude(workflowEntity => workflowEntity.Nodes)
								 .Include(dataSourceEntity => dataSourceEntity.MainField)
								 .FirstOrDefaultAsync(entity => entity.Id == dataSourceId);
			if (dataSource == null)
				throw new ElementNotFoundException($"DataSource configuration with id: {dataSourceId} could not be found");

			var user = await _userManager.GetCurrentUserAsync();

			var virtualFieldNames = dataSource.Fields.Where(field => field.FieldType == DataFieldType.VirtualField)
				.Select(field => field.Name)
				.ToList();
			foreach (var virtualFieldName in virtualFieldNames)
			{
				values?.Remove(virtualFieldName);
			}

			string? oldFileId = null;
			if (fileUploadId != null)
				oldFileId = (await dataSource.GetElementAsync(elementId.ToString()))?.FileInfo?.Id;
			
			if (fileUploadId != null && oldFileId == null && !dataSource.AllowFile)
				return GetBadRequestResponse("File upload is not allowed!");

			var elementData = new DataStoreElementData(elementId.ToString(), values ?? new Dictionary<string, object?>(), fileUploadId: fileUploadId);
			elementData.PrepareValuesForDataStore(dataSource.Fields);

			var lookupDisplayQueryFields = dataSource.Fields.Where(field => field is { FieldType: DataFieldType.LookupField, LookupDisplayFieldId: not null })
				.Select(field => new DataStoreQueryField(field.Name + "." + field.LookupDisplayField!.Name, field.Name + ".Display"))
				.ToList();

			var origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, user.DisplayName);

			if (elementData.Values.Count == 0 && !fileUploadId.IsNullOrEmpty())
				await dataSource.UpdateFileAsync(elementId.ToString(), fileUploadId!, origin);
			else
				await dataSource.UpdateElementAsync(elementData, origin, lookupDisplayQueryFields);

			// load element fresh (because we need the virtual fields and those can't be accessed through the update method currently)
			var element = await ElementUtils.GetElementWithVirtualFieldsAsync(_stringLocalizerFactory, _databaseContext, _deepZoomHelperService, dataSourceId, elementId.ToString());
			if (element == null)
				return GetNotFoundResponse($"Element with id: {elementId} could not be found in DataSource with id {dataSourceId}");

			if (oldFileId != null && element.FileInfo?.FileId != oldFileId)
			{
				_ = _thumbnailHelperService.DeleteThumbnailsAsync(await _contextFactory.CreateDbContextAsync(), oldFileId);
				_ = _deepZoomHelperService.DeleteDeepZoomImagesAsync(await _contextFactory.CreateDbContextAsync(), oldFileId);
			}

			_ = ElementUtils.GenerateWorkflowEntries(_databaseContext, element, dataSource, user);
			
			return GetOkResponse(element);
		}
		catch (ElementNotFoundException)
		{
			return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");
		}
		catch (Exception e)
		{
			Logger.Error(e, "Element could not be updated");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Updates an existing dataset inside the given datasource with the given elementId and removes its file if there is one
	/// </summary>
	[HttpPatch("/Api/DataSources/{dataSourceId:guid}/Elements/{elementId:guid}/RemoveFile")]
	public async Task<ActionResult<FrontendResponse>> RemoveFileFromElement(Guid dataSourceId, Guid elementId, [FromBody] Dictionary<string, object?> values)
	{
		try
		{
			var dataSource = await _databaseContext.DataSources
								 .Include(dataSource => dataSource.Fields).ThenInclude(field => field.LookupDisplayField)
								 .Include(dataSource => dataSource.Fields).ThenInclude(field => field.VirtualLookupField)
								 .FirstOrDefaultAsync(entity => entity.Id == dataSourceId);
			if (dataSource == null)
				throw new ElementNotFoundException($"DataSource configuration with id: {dataSourceId} could not be found");

			var oldFileId = (await dataSource.GetElementAsync(elementId.ToString()))?.FileInfo?.Id;

			var user = await _userManager.GetCurrentUserAsync();

			var origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, user.DisplayName);

			await dataSource.RemoveFileAsync(elementId.ToString(), origin);

			// load element fresh (because we need the virtual fields and those can't be accessed through the update method currently)
			var element = await ElementUtils.GetElementWithVirtualFieldsAsync(_stringLocalizerFactory, _databaseContext, _deepZoomHelperService, dataSourceId, elementId.ToString());
			if (element == null)
				return GetNotFoundResponse($"Element with id: {elementId} could not be found in DataSource with id {dataSourceId}");

			if (oldFileId != null)
			{
				_ = _thumbnailHelperService.DeleteThumbnailsAsync(await _contextFactory.CreateDbContextAsync(), oldFileId);
				_ = _deepZoomHelperService.DeleteDeepZoomImagesAsync(await _contextFactory.CreateDbContextAsync(), oldFileId);
			}

			return GetOkResponse(element);
		}
		catch (ElementNotFoundException)
		{
			return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found.");
		}
		catch (Exception e)
		{
			Logger.Error(e, "Element could not be updated");
			return GetBadRequestResponse(e.Message);
		}
	}

	/// <summary>
	/// Execute an action on a specific record
	/// </summary>
	/// <param name="sourceGuid">ID of the data source configuration</param>
	/// <param name="elementGuid">ID of a data record for executing the action</param>
	/// <param name="actionDto">Dto which contains the action</param>
	[HttpPatch("/Api/DataSources/{sourceGuid:guid}/Elements/{elementGuid:guid}/Action")]
	public async Task<ActionResult<FrontendResponse>> ExecuteAction(Guid sourceGuid, Guid elementGuid, [FromBody] ElementActionDto actionDto)
	{
		try
		{
			var dataSource = await _databaseContext.DataSources
								 .FirstOrDefaultAsync(entity => entity.Id == sourceGuid);
			if (dataSource == null)
				throw new ElementNotFoundException($"DataSource configuration with id: {sourceGuid} could not be found");

			await dataSource.ExecuteElementAction(actionDto, elementGuid.ToString());

			return GetOkResponse();
		}
		catch (ElementNotFoundException)
		{
			return GetNotFoundResponse($"DataSource with id: {sourceGuid} could not be found.");
		}
		catch (Exception e)
		{
			Logger.Error(e, "Element could not be updated");
			return GetBadRequestResponse(e.Message);
		}
	}

	private static QueryFilter PrepareFilter(DataFieldEntity dataField, string term)
	{
		return PrepareFilter(dataField.Type, dataField.Name, term);
	}

	private static QueryFilter PrepareFilter(DataType dataType, string fieldName, string term)
	{
		if (dataType.IsNumeric())
		{
			// try to parse term as HR Value with the current user culture
			// -> if successful, convert HR to plain value and search for plain value
			if (double.TryParse(term, CultureInfo.CurrentCulture, out var result))
				term = result.ToString(CultureInfo.InvariantCulture);
		}

		return new LikeFilter(new QueryFilterField(fieldName), "%" + term + "%");
	}
}