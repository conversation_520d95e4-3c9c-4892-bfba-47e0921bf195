@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.AdminListPage
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.CreatePanel
@model Levelbuild.Frontend.WebApp.Features.DataStoreConfig.ViewModels.DataStoreConfigList
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp
@{
	var localizer = LocalizerFactory.Create("Culture", "");
	List<BaseColumnComponentTagHelper> columns =
	[
		new ListDataColumnComponentTagHelper()
		{
			Name = "displayName",
			Label = localizer["displayName"]
		},
		
		new ListDataColumnComponentTagHelper()
		{
			Name = "name",
			Label = localizer["name"]
		},

		new ListDataColumnComponentTagHelper()
		{
			Name = "translationCount",
			Label = localizer["list/translationCount"],
			Type = DataColumnType.Integer
		},

		new ListDataColumnComponentTagHelper()
		{
			Name = "userCount",
			Label = localizer["list/userCount"],
			Type = DataColumnType.Integer,
		}
	];
	List<QueryParamSortingDto> sorting = [ new QueryParamSortingDto { OrderColumn = "Name", Direction = SortDirection.Asc } ];
}
<script type="module" defer>
	Page.setMainPage('/Admin/Cultures')
	Page.setBreadcrumbs([{ label: '@localizer["list/pageTitle"]', url: Page.getMainPageUrl() }])
</script>
<vc:admin-list-page entity="Culture" route-name="Cultures" localizer="@localizer" columns="@columns" open-on-row-click="true" sorting="@sorting"></vc:admin-list-page>
<vc:create-panel entity="Culture" route-name="Cultures" localizer="@localizer"></vc:create-panel>
