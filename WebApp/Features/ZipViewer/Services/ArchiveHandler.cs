using System.Net.Mime;
using System.Security.Cryptography;
using Ionic.Zip;
using Levelbuild.Core.FrontendDtos.ZipViewer;
using Levelbuild.Entities;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Microsoft.AspNetCore.Mvc;
using SharpCompress.Archives;
using SharpCompress.Readers;
using IonicZipFile = Ionic.Zip.ZipFile;

namespace Levelbuild.Frontend.WebApp.Features.ZipViewer.Services;

/// <summary>
/// Unified archive handler that uses IonicZip for ZIP files and SharpCompress for other formats
/// </summary>
public class ArchiveHandler
{
	private const string ZipExtension = "zip";
	private const string InvalidPasswordMessage = "Invalid password for encrypted ZIP file.";
	private const string UseZipMethodMessage = "Use ExtractZipEntriesWithPasswordCheck for ZIP files";
	
	private static readonly IReadOnlySet<string> PasswordKeywords = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
	{
		"password", "encrypted", "invalid password", "wrong password",
		"bad password", "incorrect password", "authentication",
		"decrypt", "cipher", "key", "crc"
	};


	#region Helper Methods for Controller Operations

	
	/// <summary>
	/// Comprehensive validation and context creation for archive operations
	/// </summary>
	/// <param name="dbContext">Database context</param>
	/// <param name="dataSourceId">ID of the data source</param>
	/// <param name="fileId">ID of the file</param>
	/// <param name="password">Optional password for encrypted archives</param>
	/// <param name="entryPath">Optional entry path within the archive</param>
	/// <returns>Tuple containing the extraction context (if valid) and error message (if any)</returns>
	public static async Task<ExtractionContextDto?> 
		GetValidatedArchiveContextAsync(CoreDatabaseContext dbContext, Guid dataSourceId, string fileId, 
			string? password = null, string? entryPath = null)
	{
		ArgumentNullException.ThrowIfNull(dbContext);
		ArgumentException.ThrowIfNullOrWhiteSpace(fileId);
		
		var dataSource = await dbContext.DataSources.FindAsync(dataSourceId)
			?? throw new InvalidOperationException($"Data source with ID {dataSourceId} not found.");
		
		var fileStream = await dataSource.GetFileAsync(fileId)
			?? throw new InvalidOperationException($"File with ID {fileId} not found.");
		
		try
		{
			var memoryStream = new MemoryStream();
			await fileStream.CopyToAsync(memoryStream);
			memoryStream.Position = 0;
			
			var fileExtension = Path.GetExtension(fileStream.Name).ToLowerInvariant().TrimStart('.');
			
			return new ExtractionContextDto
			{
				ArchiveStream = memoryStream,
				FileExtension = fileExtension,
				Password = password,
				EntryPath = entryPath
			};
		}
		finally
		{
			await fileStream.DisposeAsync();
		}
	}

	/// <summary>
	/// Gets archive contents with a password protection check and handles password-required scenarios
	/// </summary>
	/// <param name="dbContext">Database context</param>
	/// <param name="dataSourceId">ID of the data source</param>
	/// <param name="fileId">ID of the file</param>
	/// <param name="logManager">Log manager instance</param>
	/// <param name="password">Optional password for encrypted archives</param>
	/// <returns>Tuple containing archive contents dto (if successful) and error message (if any)</returns>
	public static async Task<ArchiveContentsDto> GetArchiveContentsWithPasswordCheckAsync(CoreDatabaseContext dbContext, Guid dataSourceId, string fileId, 
																						  ILogManager logManager, string? password = null)
	{
		ArgumentNullException.ThrowIfNull(logManager);
		
		var context = await GetValidatedArchiveContextAsync(dbContext, dataSourceId, fileId, password)
			?? throw new InvalidOperationException("Failed to create extraction context.");
		
		var fileExtension = context.FileExtension.TrimStart('.').ToLowerInvariant();

		return fileExtension == ZipExtension 
			? await HandleZipArchive(context, password)
			: await HandleGenericArchive(context, password);
	}

	/// <summary>
	/// Extracts a specific entry from an archive based on the provided context and returns the extracted data
	/// or an appropriate HTTP response in case of an error or restricted access.
	/// </summary>
	/// <param name="context">The context containing details about the archive, entry path, and other metadata necessary for extraction.</param>
	/// <returns>
	/// An <see cref="ActionResult{T}"/> containing the extracted data of the specified entry as an object if the operation succeeds,
	/// or an appropriate HTTP response indicating the error (e.g., Unauthorized or BadRequest).
	/// </returns>
	/// <exception cref="ArgumentException">Thrown when the entry path in the provided context is null or empty.</exception>
	public static async Task<ActionResult<object>> ExtractArchiveEntryAsync(ExtractionContextDto context)
	{
		ArgumentNullException.ThrowIfNull(context);
		
		var extractedData = await ExtractArchiveEntryCommonAsync(context, null);
		var sanitizedEntryPath = SanitizeEntryPath(context.EntryPath ?? string.Empty, true);
		var fileName = Path.GetFileName(sanitizedEntryPath);
		
		var contentType = GetMimeTypeFromExtension(Path.GetExtension(fileName));
		return new FileContentResult(extractedData, contentType) { FileDownloadName = fileName };
	}

	/// <summary>
	/// Gets the entire archive file for download using the same validation patterns as archive entry extraction.
	/// </summary>
	/// <param name="dbContext">Database context</param>
	/// <param name="dataSourceId">ID of the data source</param>
	/// <param name="fileId">ID of the file</param>
	/// <returns>A task that represents the asynchronous operation. The task result contains an ActionResult wrapping the downloadable archive file.</returns>
	public static async Task<ActionResult> GetArchiveFileAsync(CoreDatabaseContext dbContext, Guid dataSourceId, string fileId)
	{
		var dataSource = await dbContext.DataSources.FindAsync(dataSourceId) ?? throw new InvalidOperationException($"Data source with ID {dataSourceId} not found.");
		
		var fileStream = await dataSource.GetFileAsync(fileId) ?? throw new InvalidOperationException($"File with ID {fileId} not found.");
		
		try
		{
			// Read the file content into memory using the same pattern as other archive operations
			var memoryStream = new MemoryStream();
			await fileStream.CopyToAsync(memoryStream);
			memoryStream.Position = 0;
			
			// Determine content type based on file extension
			var contentType = GetMimeTypeFromExtension(Path.GetExtension(fileStream.Name));
			
			return new FileContentResult(memoryStream.ToArray(), contentType) { FileDownloadName = fileStream.Name };
		}
		finally
		{
			await fileStream.DisposeAsync();
		}
	}
	
	#endregion

	#region Private Methods

	// Handles the extraction of archive contents based on the file type, either ZIP or generic archive
	private static async Task<ArchiveContentsDto> HandleZipArchive(ExtractionContextDto context, string? password)
	{
		var zipResult = await ExtractZipEntriesWithPasswordCheck(context);
		
		if (zipResult.IsPasswordProtected && password == null)
			return new ArchiveContentsDto { Entries = [], IsPasswordProtected = true };
		
		return new ArchiveContentsDto { Entries = zipResult.Entries, IsPasswordProtected = zipResult.IsPasswordProtected };
	}
	
	// Handles the extraction of archive contents for non-ZIP formats using SharpCompress
	private static async Task<ArchiveContentsDto> HandleGenericArchive(ExtractionContextDto context, string? password)
	{
		var isPasswordProtected = await IsPasswordProtectedAsync(context);

		if (isPasswordProtected && password == null)
			return new ArchiveContentsDto { Entries = [], IsPasswordProtected = true };
		
		var entries = ExtractArchiveEntries(context);
		return new ArchiveContentsDto { Entries = entries, IsPasswordProtected = isPasswordProtected };
	}

	// Extracts entries from an archive based on the provided context, handling both ZIP and other formats
	private static List<ArchiveDto> ExtractArchiveEntries(ExtractionContextDto context)
	{
		context.ResetStream();
		using var archive = ArchiveFactory.Open(context.ArchiveStream, new ReaderOptions { Password = context.Password });

		return archive.Entries.Select(entry =>
		{
			var entryKey = entry.Key ?? "";
			var sanitizedPath = SanitizeEntryPath(entryKey, context.SanitizeEntryPaths);

			return new ArchiveDto
			{
				Path = sanitizedPath,
				Name = Path.GetFileName(sanitizedPath),
				UncompressedSize = entry.Size,
				CompressedSize = entry.CompressedSize,
				IsDirectory = entry.IsDirectory
			};
		}).ToList();
	}
	
	// Extracts entries from a ZIP archive with integrated password checking to reduce file access
	private static Task<ZipExtractionResult> ExtractZipEntriesWithPasswordCheck(ExtractionContextDto context)
	{
		context.ResetStream();
		using var zipFile = IonicZipFile.Read(context.ArchiveStream);
		if (!string.IsNullOrEmpty(context.Password)) 
			zipFile.Password = context.Password.Trim();

		var isPasswordProtected = CheckZipPasswordProtection(zipFile);
		ValidateZipPassword(zipFile, context.Password, isPasswordProtected);

		var entries = ConvertZipEntriesToDtos(zipFile.Entries, context);

		return Task.FromResult(new ZipExtractionResult
		{
			Entries = entries,
			IsPasswordProtected = isPasswordProtected
		});
	}
	
	// Checks if a ZIP file is password-protected by looking for encrypted entries or attempting extraction
	private static bool CheckZipPasswordProtection(IonicZipFile zipFile)
	{
		// Check for obvious encryption
		var hasEncryptedEntries = zipFile.Entries.Any(entry => 
			!entry.IsDirectory && entry.Encryption != EncryptionAlgorithm.None);
		
		if (hasEncryptedEntries) return true;

		// Test extraction to detect hidden password protection
		var firstNonDirectoryEntry = zipFile.Entries.FirstOrDefault(e => !e.IsDirectory);
		if (firstNonDirectoryEntry == null) return false;

		try
		{
			using var testStream = new MemoryStream();
			firstNonDirectoryEntry.Extract(testStream);
			return false;
		}
		catch (Exception ex) when (IsPasswordRelatedError(ex))
		{
			return true;
		}
	}
	
	// Validates the ZIP file password by attempting to extract an entry
	private static void ValidateZipPassword(IonicZipFile zipFile, string? password, bool hasEncryptedEntries)
	{
		if (!hasEncryptedEntries || string.IsNullOrEmpty(password)) return;

		var firstEncryptedEntry = zipFile.Entries
			.FirstOrDefault(entry => !entry.IsDirectory && entry.Encryption != EncryptionAlgorithm.None);

		if (firstEncryptedEntry == null) return;

		try
		{
			using var testStream = new MemoryStream();
			firstEncryptedEntry.Extract(testStream);
		}
		catch (Exception ex) when (IsPasswordRelatedError(ex))
		{
			throw new UnauthorizedAccessException(InvalidPasswordMessage, ex);
		}
	}
	
	// Converts ZIP entries to DTOs with sanitized paths
	private static List<ArchiveDto> ConvertZipEntriesToDtos(IEnumerable<ZipEntry> entries, ExtractionContextDto context)
	{
		return entries.Select(entry =>
		{
			var sanitizedPath = SanitizeEntryPath(entry.FileName, context.SanitizeEntryPaths);
			return new ArchiveDto
			{
				Path = sanitizedPath,
				Name = Path.GetFileName(sanitizedPath),
				UncompressedSize = entry.UncompressedSize,
				CompressedSize = entry.CompressedSize,
				IsDirectory = entry.IsDirectory
			};
		}).ToList();
	}
	
	// Checks if non-ZIP archives are password-protected using SharpCompress
	private static async Task<bool> IsPasswordProtectedAsync(ExtractionContextDto context)
	{
		var fileExtension = context.FileExtension.TrimStart('.').ToLowerInvariant();

		return fileExtension switch
		{
			ZipExtension => throw new InvalidOperationException(UseZipMethodMessage),
			_ => await SharpCompressArchivePasswordProtection(context)
		};
	}

	// Checks if the archive is password-protected using SharpCompress
	private static async Task<bool> SharpCompressArchivePasswordProtection(ExtractionContextDto context)
	{
		context.ResetStream();
		using var archive = ArchiveFactory.Open(context.ArchiveStream);

		var firstNonDirectoryEntry = archive.Entries.FirstOrDefault(e => !e.IsDirectory);
		if (firstNonDirectoryEntry == null) return false;
		
		try
		{
			await using var entryStream = firstNonDirectoryEntry.OpenEntryStream();
			var buffer = new byte[1];
			await entryStream.ReadExactlyAsync(buffer, 0, 1);
			return false;
		}
		catch
		{
			return false;
		}
	}
	
	// Extracts the specified entry from the archive based on the provided context
	private static async Task<byte[]> ExtractArchiveEntryCommonAsync(ExtractionContextDto context, ILogManager? logManager)
	{
		var sanitizedPath = string.IsNullOrWhiteSpace(context.EntryPath!) ? string.Empty : context.EntryPath!.Replace("..", "").Replace("\\", "/").TrimStart('/');
		var fileExtension = context.FileExtension.TrimStart('.').ToLowerInvariant();

		return fileExtension switch
		{
			ZipExtension => await ExtractZipEntry(context, sanitizedPath, logManager),
			_ => await ExtractGenericArchiveEntry(context, sanitizedPath, logManager)
		};
	}

	// Extracts a specific entry from a ZIP archive using IonicZip
	private static Task<byte[]> ExtractZipEntry(ExtractionContextDto context, string sanitizedPath, ILogManager? logManager)
	{
		context.ResetStream();
		using var zipFile = IonicZipFile.Read(context.ArchiveStream);
		if (!string.IsNullOrEmpty(context.Password)) 
			zipFile.Password = context.Password.Trim();

		var targetEntry = FindZipEntry(zipFile, sanitizedPath)
			?? throw new FileNotFoundException($"Entry '{sanitizedPath}' not found in archive.");

		ValidateEntrySizeLimit(targetEntry.UncompressedSize, context.MaxFileSize, sanitizedPath, logManager, "ZIP entry");

		using var memoryStream = new MemoryStream();
		targetEntry.Extract(memoryStream);
		return Task.FromResult(memoryStream.ToArray());
	}
	
	// Finds a specific entry in the ZIP file based on the sanitized path
	private static ZipEntry? FindZipEntry(IonicZipFile zipFile, string sanitizedPath)
	{
		return zipFile.Entries.FirstOrDefault(e =>
		{
			var entryFileName = SanitizeEntryPath(e.FileName ?? string.Empty, true);
			return string.Equals(entryFileName, sanitizedPath, StringComparison.OrdinalIgnoreCase);
		});
	}

	// Extracts a specific entry from a generic archive format using SharpCompress
	private static async Task<byte[]> ExtractGenericArchiveEntry(ExtractionContextDto context, string sanitizedPath, ILogManager? logManager)
	{
		context.ResetStream();

		using var archive = ArchiveFactory.Open(context.ArchiveStream, new ReaderOptions
		{
			Password = context.Password
		});

		var targetEntry = FindArchiveEntry(archive, sanitizedPath)
			?? throw new FileNotFoundException($"Entry '{sanitizedPath}' not found in archive.");

		ValidateEntrySizeLimit(targetEntry.Size, context.MaxFileSize, sanitizedPath, logManager, "Archive entry");

		await using var entryStream = targetEntry.OpenEntryStream();
		using var memoryStream = new MemoryStream();
		await entryStream.CopyToAsync(memoryStream);
		return memoryStream.ToArray();
	}
	
	// Finds a specific entry in the archive based on the sanitized path
	private static IArchiveEntry? FindArchiveEntry(IArchive archive, string sanitizedPath)
	{
		return archive.Entries.FirstOrDefault(e =>
		{
			var sanitizedEntryKey = SanitizeEntryPath(e.Key ?? string.Empty, true);
			return string.Equals(sanitizedEntryKey, sanitizedPath, StringComparison.OrdinalIgnoreCase);
		});
	}
	
	// Validates entry size against the maximum allowed file size limit
	private static void ValidateEntrySizeLimit(long entrySize, long maxFileSize, string sanitizedPath, ILogManager? logManager, string entryType)
	{
		if (entrySize <= maxFileSize) return;
		
		var errorMessage = $"Entry size {entrySize:N0} exceeds maximum allowed size {maxFileSize:N0}";
		logManager?.GetLoggerForClass<ArchiveHandler>()
			.Error("{EntryType} size exceeds limit for path {EntryPath}: {ErrorMessage}", 
				entryType, sanitizedPath, errorMessage);
		throw new InvalidOperationException(errorMessage);
	}

	// Sanitizes the entry path by removing "." and replacing backslashes with forward slashes
	private static string SanitizeEntryPath(string path, bool shouldSanitize)
	{
		if (!shouldSanitize || string.IsNullOrWhiteSpace(path))
			return path;

		return path.Replace("..", string.Empty, StringComparison.Ordinal)
				   .Replace("\\", "/", StringComparison.Ordinal)
				   .TrimStart('/');
	}

	// Checks if the exception is related to password issues or archive corruption
	private static bool IsPasswordRelatedError(Exception exception) =>
		exception is CryptographicException or UnauthorizedAccessException or InvalidDataException ||
		exception.GetType().FullName?.Contains("Ionic.Zip.Bad") == true ||
		exception.GetType().FullName?.Contains("Ionic.Zip.ZipException") == true ||
		PasswordKeywords.Any(keyword => exception.Message.Contains(keyword, StringComparison.OrdinalIgnoreCase)) ||
		(exception.Message.Contains("corrupt", StringComparison.OrdinalIgnoreCase) && 
		 (exception.Message.Contains("zip", StringComparison.OrdinalIgnoreCase) || exception.Message.Contains("archive", StringComparison.OrdinalIgnoreCase)));

	// Gets the MIME type based on the file extension
	private static string GetMimeTypeFromExtension(string extension)
	{
		return extension.ToLowerInvariant() switch
		{
			".pdf" => "application/pdf",
			".jpg" or ".jpeg" => "image/jpeg",
			".png" => "image/png",
			".gif" => "image/gif",
			".webp" => "image/webp",
			".txt" => "text/plain",
			".html" or ".htm" => "text/html",
			".css" => "text/css",
			".js" => "application/javascript",
			".json" => "application/json",
			".xml" => "application/xml",
			".zip" => "application/zip",
			".doc" or ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			".xls" or ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
			".ppt" or ".pptx" => "application/vnd.openxmlformats-officedocument.presentationml.presentation",
			_ => MediaTypeNames.Application.Octet
		};
	}
	
	#endregion
}

// Result object for combined ZIP extraction and password checking
internal class ZipExtractionResult
{
	public List<ArchiveDto> Entries { get; init; } = [];
	public bool IsPasswordProtected { get; init; }
}