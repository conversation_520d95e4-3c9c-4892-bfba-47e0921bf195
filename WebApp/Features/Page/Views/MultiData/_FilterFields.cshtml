@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.MultiPageFilterField.ViewModels
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.Services
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.EditPanel
@model Levelbuild.Frontend.WebApp.Features.Page.ViewModels.MultiDataForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@inject IAssetService AssetService
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("Page", "filterFields");
	var filterFieldSorting = new List<QueryParamSortingDto>() { new() { OrderColumn = "Position", Direction = SortDirection.Asc }};
	var fieldSorting = new List<QueryParamSortingDto>() { new() { OrderColumn = "Name", Direction = SortDirection.Asc }};
	var fieldName = "";
	if (ViewData["targetViewModel"] != null && ViewData["targetAction"]?.ToString() == "Detail")
	{
		fieldName = (ViewData["targetViewModel"] as MultiPageFilterFieldForm)?.FilterField?.FieldName;
	}
}
<style>
	.main-content {
		overflow-y: auto;
	}
</style>
<div class="grid--centered main-content vanishing-scrollbar static-scrollbar">
	<config-section class="grid--gap-m" label="@localizer["sectionFilterFields"]">
			<enumeration-component id="multi-page-filter-field-list" class="section-span-all" sorting="@filterFieldSorting">
				<list-component size="LineSize.Small">
					<list-column-component name="type" type="ListColumnType.Flag" with-converter></list-column-component>
					<list-data-column-component name="fieldName"></list-data-column-component> 
					<list-data-column-component name="displayInPanel" type="DataColumnType.Icon" icon="sidebar" live-editable="true" width="30" width-unit="DimensionUnit.Pixel"></list-data-column-component>
					<list-column-component name="delete" with-converter width="30"></list-column-component>
				</list-component>
			</enumeration-component>
	</config-section>
</div>
<div class="fixed-panel">
	<div class="panel__header flex--centered"><i class="fal fa-circle-plus"></i>@localizer["sectionAddFields"]</div>
	<div class="panel__content">
		<enumeration-component id="data-field-list" class="section-span-all" sorting="@fieldSorting">
			<list-component size="LineSize.Small" rows="new List<Dictionary<string, object>?>() { null, null, null, null, null }">
				<list-column-component name="fieldType" type="ListColumnType.Flag" with-converter></list-column-component>
				<list-data-column-component name="name"></list-data-column-component>
				<list-data-column-component name="type" with-converter text-align="Alignment.Right"></list-data-column-component>
			</list-component>
		</enumeration-component>
	</div>
</div>
<vc:edit-panel entity="MultiPageFilterField" route-name="MultiPageFilterFields" localizer="@localizer" menu-entry="FilterFields" heading="@fieldName" display-property-name="fieldName" skeleton="@(Model.Page == null)" ></vc:edit-panel>
<script type="module" defer>
	import { getTypeLabel, getFieldTypeIcon } from '@(AssetService.SolvePath("/data-field/utils.ts"))'

	// disable save button
	Page.buttonConfig.saveButton.disabled = true

	// reactivate save button when content changes
	Page.getContentChangeSignal().addEventListener("abort", () => {
		Page.buttonConfig.saveButton.disabled = false
	})

	const filterFieldEnumeration = document.getElementById('multi-page-filter-field-list')
	filterFieldEnumeration.querySelector('lvl-list-column[name="type"]').converter = (data) => getFieldTypeIcon(data)
	filterFieldEnumeration.querySelector('lvl-list-column[name="delete"]').converter = () => `<i class="fa-light fa-trash clickable" style="color: var(--clr-signal-error)" data-action="delete" onclick="removeFilterField(event)"></i>`
	filterFieldEnumeration.filters = [{
		filterColumn: 'pageId',
		operator: 'Equals',
		compareValue: Page.getFormData()['id'] ?? ''
	}]
	filterFieldEnumeration.url = '/Api/MultiPageFilterFields'

	// field list on the right side
	const fieldEnumeration = document.getElementById('data-field-list')
	fieldEnumeration.querySelector('lvl-list-column[name="fieldType"]').converter = (data) => data.type !== 'false' ? '<i class="fa-light fa-grip-dots-vertical"></i>' : ''
	fieldEnumeration.querySelector('lvl-list-data-column[name="type"]').converter = (data) => getTypeLabel(data)
	fieldEnumeration.filters = [{
		filterColumn: 'dataSourceId',
		operator: 'Equals',
		compareValue: Page.getFormData()['dataSourceId'] ?? ''
	}]
	fieldEnumeration.url = '/Api/DataFields'
	
	await Page.isReady()

	const fieldList = fieldEnumeration.querySelector('lvl-list')
	fieldList.onRowClick = (row, index) => addFilterField(row.data, index)

	let reloadTimeout
	async function addFilterField(fieldData) {
		const response = await fetch('/Api/MultiPageFilterFields', {
			body: JSON.stringify({
				'pageId': Page.getFormData().id,
				'fieldId': fieldData.id,
				'valuePreview': fieldData.fieldType === 'LookupField'
			}),
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
		})

		if (!response.ok)
			return

		const jsonData = await response.json()
		if (jsonData.error != null)
			return

		// reload columns
		clearTimeout(reloadTimeout)
		reloadTimeout = setTimeout(() => filterFieldEnumeration.reload(), 500)
	}
</script>
@* normal script for event handlers directly at the dom element (onclick,...) *@
<script>
	async function removeFilterField(event) {
		event.stopPropagation()
		const listLine = event.target.closest('lvl-list-line')
		const columnList = document.getElementById('multi-page-filter-field-list')
		await removeItem(listLine, columnList)
	}

	async function removeItem(listLine, enumeration) {
		const list = enumeration.querySelector('lvl-list')
		const position = Number(listLine.dataset['position'])
		const currentRow = list.rows[position]
		const response = await fetch(`/Api/MultiPageFilterFields/${currentRow.id}`, { method: 'DELETE' })
		if (response.ok)
			enumeration.reload()
	}
</script>