@using System.Text.Json
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.DataStoreConfig
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.ViewModels
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.BasicMenu
@using Microsoft.OpenApi.Extensions
@model Levelbuild.Frontend.WebApp.Features.Page.ViewModels.PageList
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp

@{
	var localizer = LocalizerFactory.Create("Page", "list");
	var typeLocalizer = LocalizerFactory.Create("PageType", "");
	var storeSlug = ViewContext.RouteData.Values["dataStoreSlug"]?.ToString();
	DataStoreConfigDto? selectedDataStore = Model.DataStores.FirstOrDefault(dataStore => dataStore.Slug == storeSlug);

	List<BaseColumnComponentTagHelper> columns =
	[
		new ListDataColumnComponentTagHelper { Name = "name", Label = localizer["name"], HideLabel = true },
		new ListDataColumnComponentTagHelper { Name = "dataSourceName", Label = localizer["dataSource"], ReferenceField = "dataSource.name" },
		new ListDataColumnComponentTagHelper { Name = "typeName", Label = localizer["type"], Type = DataColumnType.Enum, ReferenceField = "type" },
		new ListDataColumnComponentTagHelper { Name = "module", Label = localizer["module"], ReferenceField = "dataSource.module.name" },
		new ListDataColumnComponentTagHelper { Name = "lastModifiedText", Label = localizer["lastModifiedText"], ReferenceField = "lastModified", TextAlign = Alignment.Right}
	];
	List<QueryParamSortingDto> sortings =
	[
		new QueryParamSortingDto { OrderColumn = "name", Direction = SortDirection.Asc }
	];
	
	var typeValues = Enum.GetValues(typeof(PageType))
		.Cast<PageType>()
		.Select(type => new FilterFieldQueryItemResultDto(){ Value = type, Label = typeLocalizer[type.GetDisplayName()]})
		.OrderBy(item => item.Label)
		.ToList();
	IList<FilterPanelSectionComponentTagHelper> sections =
	[
		new() { Name = "name", Label = localizer["name"], MultiValue = true },
		new() { Name = "dataSource.module.name", Label = localizer["module"], ValuePreview = true },
		new() { Name = "dataSource.name", Label = localizer["dataSource"], MultiValue = true, ValuePreview = true },
		new() { Name = "type", Label = localizer["type"], ValuePreview = true, MultiValue = true, Type = InputDataType.Enum, PossibleValues = typeValues},
		new() { Name = "lastModified", Label = localizer["lastModified"], Type = InputDataType.Date },
		new() { Name = "lastModifiedBy", Label = localizer["lastModifiedBy"], ValuePreview = true }
	];

	List<MenuInfo> menuInfos = [MenuInfo.GetLinkInstance("info-dataStore-link", "dataStore", selectedDataStore?.Name ?? "", $"/Admin/DataStores/{selectedDataStore?.Slug}")];
	List<MenuItem> menuItems = Model.DataStores.Select(dataStore => new MenuItem(dataStore.Name ?? "", dataStore.Id!.Value.ToString(), null, dataStore.Slug == storeSlug, dataStore.Enabled, dataStore)).ToList();
}

<script type="module" defer>
	// store selected menu item before rendering menu
	@if (selectedDataStore != null)
	{
		@:Page.saveFormData(@(Html.Raw(JsonSerializer.Serialize(selectedDataStore, ConfigHelper.DefaultJsonOptions))))
		@:Page.setMainPage('/Admin/DataStores/@(selectedDataStore.Slug)/Pages')
		@:Page.setBreadcrumbs([{ label: '@localizer["pageTitle"]', url: Page.getMainPageUrl() }])
	}
	Page.globalSearchbar.disabled = false
</script>

<!-- DataStore Menu -->
<vc:basic-menu type="@BasicMenuType.ListFilter" entity="Page" route-name="Pages" base-route="Admin/DataStores/-/Pages"
               menu-items="@menuItems" menu-infos="@menuInfos" title="@localizer["menuTitle"]">
</vc:basic-menu>

<!-- Page filter -->
<vc:filter-panel entity="Page" route-name="Pages" sections="@sections" in-admin-config="true"></vc:filter-panel>

<!-- Page list -->
<vc:admin-list-page entity="Page" route-name="Pages" route-params="{type:'type'}" columns="@columns" sorting="@sortings" localizer="@localizer"
                    parent-property-name="dataStoreId" open-on-row-click="true" filter-by-menu="true">
</vc:admin-list-page>

<!-- Page Create -->
<vc:create-panel entity="Page" route-name="Pages" localizer="@localizer" parent-property-name="dataStoreId"></vc:create-panel>

<script type="module" defer>
	const menu = document.getElementById('page-menu')

	// set backend info
	@if (selectedDataStore == null)
	{
		<text>
			// no backends available
			if(Page.getFormData()?.name == null) {
				document.querySelector('[data-action=add]').disabled = true
				const infoBackendLabel = document.querySelector('#info-dataStore-link span')
				if (infoBackendLabel)
					infoBackendLabel.textContent = '@localizer["menu/info/noBackend"]'
				const infoLinkButton = document.querySelector('#info-dataStore-link .menu-info__link')
				infoLinkButton.classList.add('hide')
				menu.querySelector('.menu__no-data lvl-button')?.addEventListener('click', () => window.open('/Admin/DataStores', '_blank'))
			} else {
				setBackend()
			}
		</text>
	}
	
	menu?.addEventListener('nav-item:click', () => setBackend())

	// set info data on row click
	document.getElementById('page-list')?.addEventListener('list-row:click', async customEvent => {
		if (customEvent?.detail == null || customEvent.detail.rowData == null)
			return

		const rowData = customEvent.detail.rowData
		const menu = document.getElementById("page-menu")
		await Component.waitForComponentInitialization('lvl-side-nav')
		const dataStoreSlug = Page.getValueByPath(rowData, "dataSource.dataStore.slug")
		const dataSourceSlug = Page.getValueByPath(rowData, "dataSource.slug")

		// add currenMenu to path to make sure setting the view location via ../Views is working properly
		Page.setMainPage(Page.getMainPageUrl(), 'BasicSettings')
		Page.setInfo(Page.getMainPageUrl()+'/BasicSettings')
		
		if (!menu)
			return

		// Backend
		menu.setInfoData(rowData, "info-dataStore-link-left", "dataSource.dataStore.name")
		menu.setInfoUrl("info-dataStore-link-right", `/Admin/DataStores/${dataStoreSlug}`)

		// DataSource
		menu.setInfoData(rowData, "info-dataSource-link-left", "dataSourceName")
		menu.setInfoUrl("info-dataSource-link-right", `/Admin/DataStores/${dataStoreSlug}/DataSources/${dataSourceSlug}`)

		// CreatedBy
		menu.setInfoData(rowData, "info-createdBy-left", "createdBy")
		menu.setInfoData(rowData, "info-createdBy-right", "created")

		// LastModifiedBy
		menu.setInfoData(rowData, "info-lastModifiedBy-left", "lastModifiedBy")
		menu.setInfoData(rowData, "info-lastModifiedBy-right", "lastModified")
	})

	async function setBackend() {
		const formData = Page.getFormData()
		const pageMenu = document.getElementById("page-menu")
		await Component.waitForComponentInitialization('lvl-side-nav')
		pageMenu.setInfoData(formData, "info-dataStore-link-left", "name")
		pageMenu.setInfoUrl("info-dataStore-link-right", `/Admin/DataStores/${formData.slug}`)
	}
</script>