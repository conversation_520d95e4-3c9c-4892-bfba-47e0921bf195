using Levelbuild.Frontend.WebApp.Features.PublicApi.Filters;
using Microsoft.AspNetCore.Mvc;

namespace Levelbuild.Frontend.WebApp.Features.PublicApi.Attributes;

/// <summary>
/// Attribute used to enable API key authentication for API controllers
/// </summary>
public class ApiKeyAuthAttribute : ServiceFilterAttribute
{
	/// <summary>
	/// Constructor.
	/// </summary>
	public ApiKeyAuthAttribute() : base(typeof(ApiKeyAuthFilter))
	{
	}
}