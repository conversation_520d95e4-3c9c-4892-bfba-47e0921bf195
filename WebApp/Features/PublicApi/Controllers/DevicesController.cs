using System.Security.Authentication;
using System.Text.Json;
using System.Text.Json.Nodes;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.Device;
using Levelbuild.Entities;
using Levelbuild.Entities.Features.Device;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.PublicApi;
using Levelbuild.Frontend.WebApp.Features.PublicApi.Attributes;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Swashbuckle.AspNetCore.Annotations;

namespace Levelbuild.Frontend.WebApp.Features.PublicApi.Controllers;

/// <summary>
/// Public API controller for device management.
/// </summary>
[SwaggerGroup("Authorization", 0)]
public class DevicesController : ConfigController
{
	private readonly UserManager _userManager;
	
	/// <inheritdoc />
	public DevicesController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, UserManager userManager, IVersionReader versionReader) : 
		base(logManager.GetLoggerForClass<DevicesController>(), contextFactory, versionReader)
	{
		_userManager = userManager;
	}
	
	/// <inheritdoc />
	[SwaggerOperation("Get all of my devices")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<IList<DeviceDto>>))]
	public override async Task<ActionResult<PublicApiResponse>> List()
	{
		try
		{
			var currentUser = await _userManager.GetCurrentUserAsync();

			return await HandleListRequestAsync<DeviceEntity, DeviceDto>(
					   DatabaseContext.Devices.Where(
						   device => device.UserId == currentUser.Id
					   )
				   );
		}
		catch (AuthenticationException e)
		{
			return GetUnauthorizedResponse(e.Message);
		}
		catch (Exception e)
		{
			Logger.Error(e, "Device list could not be loaded");
			return GetBadRequestResponse(e.Message);
		}
	}
	
	/// <inheritdoc />
	[SwaggerOperation("Get one of my devices")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<DeviceDto>))]
	public override async Task<ActionResult<PublicApiResponse>> Get(Guid id)
	{
		try
		{
			var currentUser = await _userManager.GetCurrentUserAsync();
			
			var device = await DatabaseContext.Devices.FindAsync(id);
			
			if(device == null)
				return GetNotFoundResponse($"Device with id: {id} could not be found");
			
			if(device.UserId != currentUser.Id)
				return GetUnauthorizedResponse($"Missing authorization to get Device with id: {id}");
			
			var json = JsonSerializer.SerializeToElement(device.ToDto(), SerializerOptions);
			return GetOkResponse(json);
		}
		catch (AuthenticationException e)
		{
			return GetUnauthorizedResponse(e.Message);
		}
		catch (Exception e)
		{
			Logger.Error(e, "Device could not be loaded");
			return GetBadRequestResponse(e.Message);
		}
	}
	
	/// <summary>
	/// Creates a new device.
	/// </summary>
	/// <param name="dto"></param>
	/// <returns></returns>
	[HttpPost]
	[SwaggerOperation("Register a new device")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<DeviceDto>))]
	public async Task<ActionResult<PublicApiResponse>> Create(DeviceDto dto)
	{
		try
		{
			var currentUser = await _userManager.GetCurrentUserAsync();
			
			dto.UserId = currentUser.Id;
			
			var device = DeviceEntity.FromDto(dto, currentUser, DatabaseContext);
			DatabaseContext.Devices.Add(device);
			await DatabaseContext.SaveChangesAsync();

			var json = JsonSerializer.SerializeToElement(device.ToDto(), SerializerOptions);
			return GetOkResponse(json);
		}
		catch (AuthenticationException e)
		{
			return GetUnauthorizedResponse(e.Message);
		}
		catch (Exception e)
		{
			Logger.Error(e, "Device could not be created");
			return GetBadRequestResponse(e.Message);
		}
	}
	
	// TODO: Can probably be removed once the App doesn't use this anymore
	/// <summary>
	/// Creates a new device and register a new passkey for it.
	/// </summary>
	/// <param name="dto"></param>
	/// <param name="authCode">A one time auth code provided by the WebApp for passkey registration.</param>
	/// <returns></returns>
	[AllowAnonymous]
	[HttpPost("{authCode}")]
	[SwaggerOperation("Register a new device with passkey authentication")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<DeviceDto>))]
	public async Task<ActionResult<PublicApiResponse>> CreateWithPasskey(DeviceDto dto, string authCode)
	{
		try
		{
			var verificationResult = await _userManager.VerifyOneTimeAuthCodeAsync(authCode);
			if(!verificationResult.Success || verificationResult.AuthenticationCodeEntity == null)
				return GetUnauthorizedResponse("Invalid authorization code!");
			
			dto.UserId = verificationResult.AuthenticationCodeEntity.UserId;
			
			var device = DeviceEntity.FromDto(dto, await _userManager.GetCurrentUserAsync(), DatabaseContext);
			
			var passkeyResponse = await _userManager.GetPasskeyRegistrationCredentialsAsync();
			device.PasskeyId = passkeyResponse.PasskeyId;
			
			await DatabaseContext.Devices.AddAsync(device);
			await DatabaseContext.SaveChangesAsync();

			var responseDto = device.ToDto();
			responseDto.PasskeyCredentials = passkeyResponse.Credentials;
			responseDto.PasskeyRegistrationUrl = await _userManager.GetPasskeyRegistrationLinkAsync();
			
			var json = JsonSerializer.SerializeToElement(responseDto, SerializerOptions);
			return GetOkResponse(json);
		}
		catch (AuthenticationException e)
		{
			return GetUnauthorizedResponse(e.Message);
		}
		catch (Exception e)
		{
			Logger.Error(e, "Device could not be created");
			return GetBadRequestResponse(e.Message);
		}
	}
	
	/// <summary>
	/// Updates a specific device.
	/// </summary>
	/// <param name="id"></param>
	/// <param name="dto"></param>
	/// <returns></returns>
	[HttpPatch("{id:guid}")]
	[SwaggerOperation("Update a device")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<DeviceDto>))]
	public async Task<ActionResult<PublicApiResponse>> Update(Guid id, DeviceDto dto)
	{
		try
		{
			var currentUser = await _userManager.GetCurrentUserAsync();
			
			var device = await DatabaseContext.Devices.FindAsync(id);
			
			if(device == null)
				return GetNotFoundResponse($"Device with id: {id} could not be found");
			
			if(device.UserId != currentUser.Id)
				return GetUnauthorizedResponse($"Missing authorization to get Device with id: {id}");
			
			device.UpdatePartial(dto);
			await DatabaseContext.SaveChangesAsync();
			
			var json = JsonSerializer.SerializeToElement((await DatabaseContext.Devices.FindAsync(id))!.ToDto(), SerializerOptions);
			return GetOkResponse(json);
		}
		catch (AuthenticationException e)
		{
			return GetUnauthorizedResponse(e.Message);
		}
		catch (Exception e)
		{
			Logger.Error(e, "Device could not be updated");
			return GetBadRequestResponse(e.Message);
		}
	}
	
	// TODO: Maybe not necessary
	/*/// <summary>
	/// Start passkey registration for a new device.
	/// </summary>
	/// <param name="deviceId"></param>
	/// <param name="authCode"></param>
	/// <returns></returns>
	[AllowAnonymous]
	[HttpPost("{deviceId:guid}/Passkey/{authCode}")]
	[SwaggerOperation("Retrieve passkey registration credentials for a new device")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<DeviceDto>))]
	public async Task<ActionResult<PublicApiResponse>> CreatePasskey(Guid deviceId, string authCode)
	{
		try
		{
			var device = await DatabaseContext.Devices.FindAsync(deviceId);
			if (device == null)
				return GetNotFoundResponse($"Device with id '{deviceId}' could not be found.");
			
			var verificationResult = await _userManager.VerifyOneTimeAuthCodeAsync(authCode);
			if(!verificationResult.Success || verificationResult.AuthenticatedUserId == null)
				return GetUnauthorizedResponse("Invalid authorization code!");
			
			if (device.UserId != verificationResult.AuthenticatedUserId)
				return GetUnauthorizedResponse($"Missing authorization to get Device with id: {deviceId}");
			
			var response = _userManager.GetPasskeyRegistrationCredentials();
			
			device.PasskeyId = response.PasskeyId;
			await DatabaseContext.SaveChangesAsync();
			
			return GetOkResponse(response.Credentials);
		}
		catch (AuthenticationException e)
		{
			return GetUnauthorizedResponse(e.Message);
		}
		catch (Exception e)
		{
			return GetBadRequestResponse(e.Message);
		}
	}
	
	/// <summary>
	/// Get the linked passkey of the device.
	/// </summary>
	/// <param name="deviceId"></param>
	/// <returns></returns>
	[HttpGet("{deviceId:guid}/Passkey")]
	[SwaggerOperation("Get the linked passkey of the device")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<DeviceDto>))]
	public ActionResult<PublicApiResponse> GetPasskey(Guid deviceId)
	{
		var device = DatabaseContext.Devices.Find(deviceId);
		if (device == null)
			return GetNotFoundResponse($"Device with id '{deviceId}' could not be found.");
		
		if(device.UserId != _userManager.GetCurrentUser()!.Id)
			return GetUnauthorizedResponse($"Missing authorization to get Device with id: {deviceId}");
		
		if (string.IsNullOrEmpty(device.PasskeyId))
			return GetNotFoundResponse($"No active passkey found for device '{device.DisplayName}' of user '{_userManager.GetCurrentUser()!.DisplayName}'.");
		
		var passkey = _userManager.GetPasskeyInfo(device.PasskeyId);
		return GetOkResponse(new PasskeyDto()
		{
			Id = passkey.Id,
			Name = passkey.Name,
			State = passkey.State.ToString()
		});
	}
	
	/// <summary>
	/// Verify the linked passkey of the device.
	/// </summary>
	/// <param name="deviceId"></param>
	/// <param name="passkeyDto"></param>
	/// <returns></returns>
	[HttpPost("{deviceId:guid}/Passkey/Verify")]
	[SwaggerOperation("Verify the linked passkey of the device")]
	[SwaggerResponse(StatusCodes.Status200OK, Type = typeof(PublicApiResponse<bool>))]
	public ActionResult<PublicApiResponse> VerifyPasskey(Guid deviceId, [FromBody] PasskeyRegistrationDto passkeyDto)
	{
		var device = DatabaseContext.Devices.Find(deviceId);
		if (device == null)
			return GetNotFoundResponse($"Device with id '{deviceId}' could not be found.");
		
		if(device.UserId != _userManager.GetCurrentUser()!.Id)
			return GetUnauthorizedResponse($"Missing authorization to get Device with id: {deviceId}");
		
		if (string.IsNullOrEmpty(device.PasskeyId))
			return GetNotFoundResponse($"No active passkey found for device '{device.DisplayName}' of user '{_userManager.GetCurrentUser()!.DisplayName}'.");
		
		if (string.IsNullOrEmpty(passkeyDto.Name) || passkeyDto.Credentials == null)
			return GetBadRequestResponse($"");
		
		var success = _userManager.VerifyPasskey(device.PasskeyId, passkeyDto.Name, passkeyDto.Credentials.Value);
		/*if (success)
		{
			device.IsPasskeyActivated = true;
			DatabaseContext.SaveChanges();
		}#1#
		
		return GetOkResponse(success);
	}*/
}