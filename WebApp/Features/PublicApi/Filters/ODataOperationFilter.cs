using Levelbuild.Frontend.WebApp.Features.PublicApi.Constants;
using Levelbuild.Frontend.WebApp.Features.PublicApi.OData.Attributes;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Levelbuild.Frontend.WebApp.Features.PublicApi.Filters;

/// <summary>
/// Operation filter for Public API endpoints that adds OData params to swagger doc if necessary.
/// </summary>
public class ODataOperationFilter : IOperationFilter
{
	/// <summary>
	/// Ensures that OData params are displayed and documented correctly in SwaggerUI when the <see cref="EnableOdataQueryAttribute"/> is present.
	/// </summary>
	/// <param name="operation"></param>
	/// <param name="context"></param>
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
	{
		var attribute = (EnableOdataQueryAttribute?) context.MethodInfo.GetCustomAttributes(typeof(EnableOdataQueryAttribute), true).FirstOrDefault();
        if (attribute != null)
		{
			// Remove queryOptions from params
			// would otherwise be a huge object that slows swagger down and is not needed, since we expose the OData params manually anyway
			var queryOptionsParam = operation.Parameters.SingleOrDefault(param => param.Name.Equals("queryOptions"));
			if (queryOptionsParam != null)
			{
				operation.Parameters.Remove(queryOptionsParam);
			}
			
			// Remove queryOptions from body
			// Same reason as above
			operation.RequestBody = null;
			
			if (attribute.Whitelist != null)
				ApplyWhitelistedParams(operation, attribute.Whitelist);
			else
				ApplyAllParams(operation);
        }
    }
	
	private static void ApplyAllParams(OpenApiOperation operation)
	{
		operation.Parameters.Add(new OpenApiParameter()
        {
        	Name = ODataParameterConstants.Filter,
        	Description = "Filter the results using OData syntax. SysFavorite (boolean) can be used to filter for favorites.",
        	Required = false,
        	In = ParameterLocation.Query,
			Schema = new OpenApiSchema()
			{
				Type = "string",
			}
        });
		
		operation.Parameters.Add(new OpenApiParameter()
		{
			Name = ODataParameterConstants.Select,
			Description = "Select which fields should be returned.",
			Required = false,
			In = ParameterLocation.Query,
			Schema = new OpenApiSchema()
			{
				Type = "string",
			}
		});

        operation.Parameters.Add(new OpenApiParameter()
        {
            Name = ODataParameterConstants.OrderBy,
            Description = "Order the results using OData syntax.",
            Required = false,
            In = ParameterLocation.Query,         
			Schema = new OpenApiSchema()
			{
				Type = "string",
			}
        });
        
        operation.Parameters.Add(new OpenApiParameter()
        {
            Name = ODataParameterConstants.Skip,
            Description = "The number of results to skip.",
            Required = false,
            In = ParameterLocation.Query,      
			Schema = new OpenApiSchema()
			{
				Type = "integer",
				Format = "int32",
			}
        });

        operation.Parameters.Add(new OpenApiParameter()
        {
            Name = ODataParameterConstants.Top,
            Description = "The number of results to return.",
            Required = false,
            In = ParameterLocation.Query,          
			Schema = new OpenApiSchema()
			{
				Type = "integer",
				Format = "int32",
			}
        });
		
		operation.Parameters.Add(new OpenApiParameter()
		{
			Name = ODataParameterConstants.Apply,
			Description = "Apply aggregation functions. (Currently only supports groupby!)",
			Required = false,
			In = ParameterLocation.Query,
			Schema = new OpenApiSchema()
			{
				Type = "string",
			}
		});
        
        operation.Parameters.Add(new OpenApiParameter()
        {
            Name = ODataParameterConstants.Count,
            Description = "Return the total count.",
            Required = false,
            In = ParameterLocation.Query,   
			Schema = new OpenApiSchema()
			{
				Type = "boolean",
			}
        });
		
		operation.Parameters.Add(new OpenApiParameter()
		{
			Name = ODataParameterConstants.Search,
			Description = "Fulltext search using the specified string",
			Required = false,
			In = ParameterLocation.Query,
			Schema = new OpenApiSchema()
			{
				Type = "string",
			}
		});
	}
	
	private static void ApplyWhitelistedParams(OpenApiOperation operation, string[] whitelist)
	{
		if (whitelist.Contains(ODataParameterConstants.Filter))
		{
			operation.Parameters.Add(new OpenApiParameter()
			{
				Name = ODataParameterConstants.Filter,
				Description = "Filter the results using OData syntax. SysFavorite (boolean) can be used to filter for favorites.",
				Required = false,
				In = ParameterLocation.Query,
				Schema = new OpenApiSchema()
				{
					Type = "string",
				}
			});
		}
		
		if (whitelist.Contains(ODataParameterConstants.Select))
		{
			operation.Parameters.Add(new OpenApiParameter()
			{
				Name = ODataParameterConstants.Select,
				Description = "Select which fields should be returned.",
				Required = false,
				In = ParameterLocation.Query,
				Schema = new OpenApiSchema()
				{
					Type = "string",
				}
			});
		}
		
		if (whitelist.Contains(ODataParameterConstants.OrderBy))
		{
			operation.Parameters.Add(new OpenApiParameter()
			{
				Name = ODataParameterConstants.OrderBy,
				Description = "Order the results using OData syntax.",
				Required = false,
				In = ParameterLocation.Query,
				Schema = new OpenApiSchema()
				{
					Type = "string",
				}
			});
		}
		
		if (whitelist.Contains(ODataParameterConstants.Skip))
		{
			operation.Parameters.Add(new OpenApiParameter()
			{
				Name = ODataParameterConstants.Skip,
				Description = "The number of results to skip.",
				Required = false,
				In = ParameterLocation.Query,
				Schema = new OpenApiSchema()
				{
					Type = "integer",
					Format = "int32",
				}
			});
		}
		
		if (whitelist.Contains(ODataParameterConstants.Top))
		{
			operation.Parameters.Add(new OpenApiParameter()
			{
				Name = ODataParameterConstants.Top,
				Description = "The number of results to return.",
				Required = false,
				In = ParameterLocation.Query,
				Schema = new OpenApiSchema()
				{
					Type = "integer",
					Format = "int32",
				}
			});
		}
		
		if (whitelist.Contains(ODataParameterConstants.Apply))
		{
			operation.Parameters.Add(new OpenApiParameter()
			{
				Name = ODataParameterConstants.Apply,
				Description = "Apply aggregation functions. (Currently only supports groupby!)",
				Required = false,
				In = ParameterLocation.Query,
				Schema = new OpenApiSchema()
				{
					Type = "string",
				}
			});
		}
		
		if (whitelist.Contains(ODataParameterConstants.Count))
		{
			operation.Parameters.Add(new OpenApiParameter()
			{
				Name = ODataParameterConstants.Count,
				Description = "Return the total count.",
				Required = false,
				In = ParameterLocation.Query,
				Schema = new OpenApiSchema()
				{
					Type = "boolean",
				}
			});
		}

		if (whitelist.Contains(ODataParameterConstants.Search))
		{
			operation.Parameters.Add(new OpenApiParameter()
			{
				Name = ODataParameterConstants.Search,
				Description = "Fulltext search using the specified string",
				Required = false,
				In = ParameterLocation.Query,
				Schema = new OpenApiSchema()
				{
					Type = "string",
				}
			});
		}
	}
}