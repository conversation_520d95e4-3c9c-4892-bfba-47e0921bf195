using Levelbuild.Core.FrontendDtos;
using Swashbuckle.AspNetCore.Filters;

namespace Levelbuild.Frontend.WebApp.Features.PublicApi.ExampleProviders;

/// <summary>
/// Generic Swagger Examples for <see cref="PublicApiResponse"/>
/// </summary>
public class PublicApiResponseExample : IExamplesProvider<PublicApiResponse>
{
	/// <inheritdoc />
	public PublicApiResponse GetExamples()
	{
		return new PublicApiResponse("localhost", new (1, 0, 0, 0))
		{
			Error = new (123, "error message", "456")
		};
	}
	
	/// <summary>
	/// May be used by specific example providers to generate a default <see cref="PublicApiResponse" /> object and append a specific payload.
	/// </summary>
	/// <param name="data">The payload.</param>
	/// <typeparam name="TData"></typeparam>
	/// <returns></returns>
	public static PublicApiResponse<TData> GetExample<TData>(TData data)
	{
		return new PublicApiResponse<TData>("localhost", new (1, 0, 0, 0), data);
	}
}