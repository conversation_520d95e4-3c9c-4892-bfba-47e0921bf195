namespace Levelbuild.Frontend.WebApp.Features.Auth.Dtos;

/// <summary>
/// DTO containing impersonation metadata.
/// </summary>
public class UserImpersonationInfoDto
{
	/// <summary>
	/// The actor's user id.
	/// </summary>
	public Guid ActorId { get; set; }
	
	/// <summary>
	/// The subject's user id.
	/// </summary>
	public Guid SubjectId { get; set; }
	
	/// <summary>
	/// The impersonation access token.
	/// </summary>
	public required string AccessToken { get; set; }
	
	/// <summary>
	/// The impersonated user's ID token.
	/// </summary>
	public required string IdToken { get; set; }
	
	/// <summary>
	/// The impersonation refresh token.
	/// </summary>
	public required string RefreshToken { get; set; }
	
	/// <summary>
	/// The access token's expiry date.
	/// </summary>
	public DateTime ExpiryDate { get; set; }
}