@using System.Text.Json
@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Core.SharedDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.ViewManagement
@using Levelbuild.Frontend.WebApp.Shared.ControllerDtos
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers.Extensions
@model Levelbuild.Frontend.WebApp.Features.Product.ViewModels.ProductForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp
@{
	var localizer = LocalizerFactory.Create("Product", "");
	var sourceLocalizer = LocalizerFactory.Create("Module", "");
	List<QueryParamSortingDto> sourceSortings = [new () { OrderColumn = "Name", Direction = SortDirection.Asc }];

	<vc:admin-list-page entity="Module" route-name="Modules" localizer="@sourceLocalizer" parent-property-name="productId" parent-property-value="@(Model.Product?.Id)" use-custom-list="true" menu-entry="BasicSettings"></vc:admin-list-page>
}
<script type="module" defer>
	const sourceList = document.querySelector('#module-list > lvl-list')

	// navigate to source on row click
	sourceList.onRowClick = async (rowContent, _, newTab) => {
		const loadData = Page.getJSON(`/Api/Modules/${rowContent.data.id}`)
		let json = await loadData
		const dataStoreSlug = Page.getValueByPath(json.data, "model.dataStore.slug")
		if (newTab) {
			const completeUrl = `/Admin/DataStores/${dataStoreSlug}/Modules/${rowContent.data.slug}`
			Page.openNewTab(completeUrl, true)
			return
		}

		await Page.load('/Admin/Modules/Edit')
		Page.setMainPage(`/Admin/DataStores/${dataStoreSlug}/Modules/${rowContent.data.slug}`, 'BasicSettings')
		Page.setInfo(Page.getMainPageUrl()+'/BasicSettings', { params: {}, title: rowContent.data.name })

		await Page.addBreadcrumb({ label: rowContent.data.name, url: Page.getMainPageUrl() })

		// save form data
		const form = document.getElementById("module-form")
		Page.saveFormData(json.data)
		if (form != null)
			await Page.setFormData(form, json.data)
		Page.buttonConfig.showAdminButtons()
		
		const infoSection = document.getElementById('module-menu')
		await Component.waitForComponentInitialization('lvl-side-nav')

		// Backend
		infoSection.setInfoData(json.data, "info-dataStore-link-left", "page.dataSource.dataStore.name")
		infoSection.setInfoUrl("info-dataStore-link-right", `/Admin/DataStores/${dataStoreSlug}`)

		// disable skeleton on menu and fab
		if (form != null)
			form.skeleton = false
		const menu = document.getElementById("module-menu")
		if (menu != null)
			menu.skeleton = false
	}
</script>
<div class="grid--centered @(Model.ViewType == ViewType.Edit ? "vanishing-scrollbar static-scrollbar" : "")">
	<form-component id="product-form" skeleton="@(Model is { ViewType: ViewType.Edit, Product: null })">
		<div class="form__item">
			<input type="hidden" class="item__value" name="id" value="@Model.Product?.Id"/>
		</div>
		<config-section label="@localizer["sectionSettings"]">
			<div class="form__item">
				<config-label target="product-name" label="@localizer["name"]"></config-label>
				<input-component id="product-name" name="name" class="item__value" value="@(Model.Product?.Name)" type="InputDataType.Translation" translation-prefix="/Products/" required></input-component>
			</div>
			<div class="form__item">
				<config-label target="responsible" label="@localizer["responsible"]"></config-label>
				@{
				var responsibleColumns = new List<AutocompleteColumnDefinition>
				{
				new("displayName", localizer["name"], DataType.String),
				};
				<autocomplete-component type="InputDataType.Guid" id="product-responsible" nullable="true" 
				                        url="/Api/Users/<USER>"
				                        columns="responsibleColumns" display-field="username" name="responsible" output-name="responsibleId"
				                        value="@(Model.Product?.ResponsibleId)" display-value="@(Model.Product?.Responsible?.Username)" class="item__value"
				                        placeholder="@localizer["pleaseChoose"]" edit-link-url="/Admin/Users/<USER>">
				</autocomplete-component>
				}
			</div>
			<div class="form__item">
				<config-label target="description" label="@localizer["description"]"></config-label>
				<textarea-component id="product-description" name="description" class="item__value" value="@(Model.Product?.Description)"></textarea-component>
			</div>
			<div class="form__item">
				<config-label target="product-keywords" label="@localizer["keywords"]" description="@localizer["keywordsDescription"]"></config-label>
				<input-component id="product-keywords" name="keywords" value="@(JsonSerializer.Serialize(Model.Product?.Keywords ?? []))" class="item__value" multi-value="true"></input-component>
			</div>
		</config-section>
	</form-component>
	<config-section label="@localizer["sectionModules"]">
		<enumeration-component id="module-list" class="section-span-all" sorting="@sourceSortings">
			<list-component skeleton="@(Model.Product == null)">
				<list-data-column-component name="name" label="@sourceLocalizer["list/name"]" hide-label></list-data-column-component>
			</list-component>
		</enumeration-component>
	</config-section>
</div>



