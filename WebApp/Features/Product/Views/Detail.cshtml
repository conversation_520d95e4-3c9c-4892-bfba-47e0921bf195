@using Levelbuild.Core.EntityInterface
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.BasicMenu
@using Levelbuild.Frontend.WebApp.Shared.ViewModels
@model Levelbuild.Frontend.WebApp.Features.Product.ViewModels.ProductForm
@inject IExtendedStringLocalizerFactory LocalizerFactory
@addTagHelper *, WebApp
@{
var localizer = LocalizerFactory.Create("Product", "");
List<MenuItem> menuItems =
[
	new("basic", "BasicSettings", "screwdriver-wrench")
];

string currentMenu = ViewData["targetMenu"] == null ? "BasicSettings" : ViewData["targetMenu"]!.ToString()!;
}
<script type="module" defer>
	@if (Model.Product != null)
	{
	<text>
	Page.setMainPage(`/Admin/Products/@(Model.Product.Slug)`, '@(currentMenu)')
	Page.setBreadcrumbs([
		{ label: '@localizer["list/pageTitle"]', url: `/Admin/Products` },
		{ label: '@(Model.Product?.Name)', url: `/Admin/Products/@(Model.Product?.Slug)` }
	], true)
	</text>
	}
</script>
<vc:basic-menu type="@BasicMenuType.ViewSwitcher" entity="Product" route-name="Products" menu-items="@menuItems" skeleton="@(Model.Product == null)" width="250"></vc:basic-menu>
<vc:admin-detail-page model="@Model" entity="Product" route-name="Products" title="@(Model.Product?.Name)" menu-item="@currentMenu" show-default-buttons="@(Model.Product != null)"></vc:admin-detail-page>
