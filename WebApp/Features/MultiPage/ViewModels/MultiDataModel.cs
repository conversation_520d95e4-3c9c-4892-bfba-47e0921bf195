using Levelbuild.Core.FrontendDtos.Page;
using Levelbuild.Core.FrontendDtos.PageView;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;

namespace Levelbuild.Frontend.WebApp.Features.MultiPage.ViewModels;

/// <summary>
/// Dto for multi data page 
/// </summary>
public class MultiDataModel
{
	/// <summary>
	/// Dto of the page configuration
	/// </summary>
	public required MultiDataPageDto Page { get; init; }
	
	/// <summary>
	/// Dto of the single data page if the current page was embedded
	/// </summary>
	public PageDto? ParentPage { get; init; }
	
	/// <summary>
	/// ElementID for 
	/// </summary>
	public string? ParentElementId { get; init; }
	
	/// <summary>
	/// Wrapper if the page was embedded
	/// </summary>
	public GridViewPageDto? GridViewPage { get; init; }

	/// <summary>
	/// Checks if the Model is a MultiDataPage or a GridViewPage alias emb. page
	/// </summary>
	/// <returns></returns>
	public bool IsMaximizedGridViewPage => ParentPage != null && ParentElementId != null && GridViewPage != null;

	/// <summary>
	/// Is the MultiPage rendered within a SinglePage?
	/// </summary>
	public bool Embedded { get; set; } = false;
	
	/// <summary>
	/// Transforms the default Sorting of the page to list of QueryParamSortings
	/// </summary>
	/// <returns></returns>
	public List<QueryParamSortingDto> GetQueryParamSorting()
	{
		return Page.DefaultSorting?.OrderBy(sorting => sorting.Position).Select(defaultSorting => new QueryParamSortingDto(defaultSorting.FieldName!, defaultSorting.Direction)).ToList() ?? [];
	}
}