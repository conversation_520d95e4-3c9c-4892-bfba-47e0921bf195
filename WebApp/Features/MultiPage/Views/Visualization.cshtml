@using Levelbuild.Core.EntityInterface
@using Levelbuild.Core.FrontendDtos.Extensions
@using Levelbuild.Core.SharedDtos.Enums
@using Levelbuild.Frontend.WebApp.Features.User.Services
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@using Levelbuild.Frontend.WebApp.Shared.ViewModels
@using Levelbuild.Frontend.WebApp.Shared.Views.Components.FilterPanel
@model Levelbuild.Frontend.WebApp.Features.MultiPage.ViewModels.MultiDataModel
@inject IExtendedStringLocalizerFactory LocalizerFactory
@inject UserManager UserManager
@addTagHelper *, WebApp

@{
	/* Without views nothing can be rendered */
	var pageLocalizer = LocalizerFactory.Create("Pages", "", true);
	var mainPageUrl = Model.IsMaximizedGridViewPage ? $"/Public/Pages/{Model.ParentPage!.Slug}/{Model.ParentElementId}/EmbeddedPages/{Model.GridViewPage!.Id}" : $"/Public/Pages/{Model.Page.Slug}";
	var breadcrumbLabel = (Model.IsMaximizedGridViewPage ? Model.GridViewPage?.TitleTranslated : Model.Page.NameTranslated) ?? "";
	var showFilterPanel = Model.Page.FilterFields?.Count > 0 || (Model.Page.DataSource?.Inactive ?? false) || (Model.Page.DataSource?.Favor ?? false);
	var filterPanelSections = Model.Page.FilterFields?
		                          .OrderBy(filterField => filterField.Position)
		                          .Select(filterField => new FilterPanelSectionComponentTagHelper()
		                          {
			                          Name = filterField.Field?.Name,
			                          Label = filterField.Field?.NameTranslated,
			                          Type = (filterField.Field?.Type ?? DataType.String).ToInputDataType(),
			                          IsMultiField = filterField.Field?.Multi == true,
			                          MultiValue = filterField.MultiValue ?? false,
			                          ValuePreview = filterField.ValuePreview ?? false,
			                          FormatType = filterField.Field?.FormatType,
			                          LabelTrue = filterField.Field?.LabelTrueTranslated,
			                          LabelFalse = filterField.Field?.LabelFalseTranslated,
			                          DecimalPlaces = filterField.Field?.DecimalPlaces,
			                          Url = filterField.ValuePreview.HasValue && filterField.ValuePreview.Value ? $"/Api/DataFields/{filterField.FieldId}/Elements/" : null,
			                          Hidden = !filterField.DisplayInPanel.HasValue || !filterField.DisplayInPanel.Value
		                          }).ToList() ?? [];
	var multiPageName = Model.Embedded ? $"grid-view-page_{Model.GridViewPage?.Id}" : $"page_{Model.Page.Id}";
}

<style>
	main {
		display: grid;
		grid-template-columns: @(showFilterPanel ? "max-content " : "")auto;
		transition: grid-template-columns var(--animation-time-medium) ease-in-out;
		background-color: var(--clr-background-lvl-1);
	}

	.page__content {
		display: grid;
		grid-template-rows: max-content auto;
		overflow: hidden;
		margin: var(--size-spacing-xs) 1.3rem 1rem var(--size-spacing-m);
		box-shadow: 0 0 0.2rem 0 var(--clr-shadow-weak), 0 0.2rem 0.4rem 0 var(--clr-shadow-weak);
		border-radius: var(--size-radius-m);
		background-color: var(--clr-background-lvl-0);

		& .page-view__wrapper {
			overflow: hidden;
		}

		& .page-view {
		    height: 100%;
		}
	}
</style>

@if (Model.Page.Views is { Count: > 0 })
{
	<script type="module" defer>
		Page.setMainPage('@mainPageUrl')
		@if (ViewData["targetAction"]?.ToString() == "Create")
		{
			@:Page.setInfo('/Public/Pages/@(Model.Page.Slug)/Create', { title: '@pageLocalizer[Model.Page.Name!]' })
			@:document.title = '@Model.Page.CreatePage?.NameTranslated'
		}
		else
		{
			@:Page.setInfo(Page.getMainPageUrl(), { title: '@pageLocalizer[Model.Page.Name!]' })
		}

		// restore stored breadcrumb
		Page.loadBreadcrumbs({ label: '@breadcrumbLabel', url: Page.getMainPageUrl() })

		// Enable edit button for admins
		@if (await UserManager.IsAdminAsync())
		{
			<text>
				Page.buttonConfig.edit = true
				Page.buttonConfig.editButton.addEventListener('click', () => {
					window.open(`/Admin/DataStores/@(Model.Page.DataSource?.DataStore?.Slug)/Pages/@(Model.Page.Slug)`, '_blank')
				}, { signal: Page.getPageChangeSignal() })
				Page.buttonConfig.editButton.skeleton = false
			</text>
		}

		// Enable searchbar
		@if (Model.Page.DataSource?.FulltextSearch ?? false)
		{
			<text>
				Page.globalSearchbar.disabled = false
				Page.globalSearchbar.value = Page.restorePageOptionQueryParameters('@multiPageName').fulltext
			</text>
		}
	</script>

	@if (showFilterPanel)
	{
		<vc:filter-panel entity="page-@(Model.Page.Id)" count-url="/Api/DataSources/@(Model.Page.DataSourceId)/Elements/Count" list-selector="lvl-multi-data-view"
		                 with-favorite-filter="@(Model.Page.DataSource?.Favor ?? false)" with-inactive-filter="@(Model.Page.DataSource?.Inactive ?? false)"
		                 sections="@(filterPanelSections)" skeleton="true"></vc:filter-panel>
		<!-- script for Amazon filter panel -->
		<script>
			(() => {
				const pageFilter = document.getElementById('page-@(Model.Page.Id)-filter')

				// get user state
				pageFilter.open = localStorage.getItem('lvl:public:page:@(Model.Page.Id):panel-open') !== 'false'

				pageFilter.addEventListener('toggle-panel:click', () => {
					localStorage.setItem('lvl:public:page:@(Model.Page.Id):panel-open', pageFilter.open.toString())
				})
			})()
		</script>
	}

	<script type="module" defer>
		const pageFilter = document.getElementById('page-@(Model.Page.Id)-filter')
		Page.globalSearchbar.addEventListener('change', () => {
			const multiView = document.querySelector('lvl-multi-data-view')
			if (!multiView)
				return

			// no page filter? apply filters to table directly
			if (!pageFilter) {
				multiView.filters = [ ...Page.globalSearchbar.addToFilters(multiView.filters) ]
				return
			}

			pageFilter.searchText = Page.globalSearchbar.value
			multiView.filters = pageFilter.filters
		}, { signal: Page.getPageChangeSignal() })

		pageFilter?.addEventListener('clear-search-text:click', () => {
			Page.globalSearchbar.value = ''
		}, { signal: Page.getPageChangeSignal() })

		document.querySelector('.multi_page_content')?.setAttribute('data-name', '@(Model.Page.Name)')
	</script>

	<!-- Switchable page view in the center -->
	<div class="page__content multi_page_content" data-multi-view-container>
		@await Html.PartialAsync("_PageView.cshtml", Model)
	</div>
}
<script type="module">
	// Get references to elements
	const actionBar = document.querySelector('lvl-query-view-action-bar')

	// Add event listener for import button - using new lvl-excel-importer component
	if (actionBar) {
		actionBar.addEventListener('action-import:click', () => {
			document.getElementById('excel-importer').startImport?.();
		}, { signal: Page.getPageChangeSignal() })
	}
	
	// Add event listener for export button - using new lvl-excel-exporter component
	if (actionBar) {
		actionBar.addEventListener('action-export:click', () => {
			document.getElementById('excel-exporter')?.startExport?.();
		}, { signal: Page.getPageChangeSignal() })
	}
</script>

@if (Model.Page.CreatePageId != null)
{
	@await Html.PartialAsync("CreatePage/SlideOut", new CreatePageModel(Model.Page.CreatePageId, Model.Page.CreatePage?.NameTranslated, Model.Page.CreatePage?.SaveButtonLabel, Model.Page.DataSource?.AllowFile, ViewData["targetAction"]?.ToString() == "Create", Model.Page.DataSourceId, Model.GridViewPage?.KeyField?.Name, Model.ParentElementId))
	<script type="module">
		// Get references to elements
		const actionBar = document.querySelector('lvl-query-view-action-bar')
		const multiDataView = document.querySelector('lvl-multi-data-view')

		// open create page
		if (actionBar) {
			actionBar.addEventListener('action-create:click', handleCreateButtonClick)
		}

		if (multiDataView) {
			multiDataView.onCreateClick = handleCreateButtonClick
		}

		const defaultValues = {}
		@if (Model.GridViewPage != null)
		{
			@:defaultValues['@Model.GridViewPage.KeyField?.Name'] = '@Model.ParentElementId'
			@foreach (var filters in Model.GridViewPage.Filters.GroupBy(filter => filter.FilterFieldId))
			{
				@if (filters.Count() == 1 && filters.First().Operator == CompareOperator.Equals)
				{
					var filter = filters.First();
					@:defaultValues['@filter.FilterFieldName'] = Page.replacePlaceholders('@filter.CompareValue')
				}
			}
		}

		function handleCreateButtonClick() {
			FileManager.showCreatePage({
				target: document.querySelector('body'),
				dataSourceId: '@Model.Page.DataSourceId',
				createPageId: '@Model.Page.CreatePageId',
				pageTitle: '@Model.Page.CreatePage?.NameTranslated',
				defaultValues: defaultValues,
				allowFile: @(Model.Page.DataSource?.AllowFile == true ? "true" : "false")
			}).then((success) => {
				if (success)
					document.querySelector('lvl-multi-data-view')?.reload()
			})
		}

		@if (Model.Page.DataSource?.AllowFile == true)
		{
			<text>
				FileManager.initDropzone({
					target: document.querySelector('body'),
					dataSourceId: '@Model.Page.DataSourceId',
					createPageId: '@Model.Page.CreatePageId',
					defaultValues: defaultValues
				})
			</text>
		}
	</script>
}

<!-- Excel Import Component - utility component for import functionality -->
<lvl-excel-importer
	id="excel-importer"
	data-source-id="@Model.Page.DataSourceId"
	page-data-source-id="@Model.Page.DataSourceId"
	@if (Model.Page.Views?.Any() == true)
	{
		@:view-id="@Model.Page.Views.First().Id"
	}>
</lvl-excel-importer>

<!-- Excel Export Component - utility component for export functionality -->
<lvl-excel-exporter
	id="excel-exporter"
	data-source-id="@Model.Page.DataSourceId"
	page-data-source-id="@Model.Page.DataSourceId"
	@if (Model.Page.Views?.Any() == true)
	{
		@:view-id="@Model.Page.Views.First().Id"
	}>
</lvl-excel-exporter>

