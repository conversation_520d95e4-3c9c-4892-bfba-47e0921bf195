@using Levelbuild.Core.FrontendDtos.Enums
@using Levelbuild.Core.SharedDtos.Enums
@using Levelbuild.Frontend.WebApp.Shared.TagHelpers
@model Levelbuild.Frontend.WebApp.Features.MultiPage.ViewModels.CardViewModel

@{
	var titleField = Model.View.TitleField;
	var titleReferenceField = titleField?.FieldType == DataFieldType.LookupField ? titleField.LookupDisplayField : titleField;
	var textOneField = Model.View.TextOneField;
	var textOneReferenceField = textOneField?.FieldType == DataFieldType.LookupField ? textOneField.LookupDisplayField : textOneField;
	var textTwoField = Model.View.TextTwoField;
	var textTwoReferenceField = textTwoField?.FieldType == DataFieldType.LookupField ? textTwoField.LookupDisplayField : textTwoField;
}
<card-grid-component embedded="@Model.Embedded" identity-column="id" selectable="true" allow-favorite="@(Model.Page?.DataSource?.Favor ?? false)" allow-inactive="@(Model.Page?.DataSource?.Inactive ?? false)"
                   display-workflow="@(Model.View.Page?.DataSource?.Workflows?.Count > 0)" display-thumbnail="@(Model.View.ShowPreview ?? false)"
                   clickable="@(Model.Page?.DetailPageId != null)">
	@if (titleField != null && titleReferenceField != null)
	{
		<card-info-component
			name="@(titleField.Name!)"
			type="@((InputDataType?)titleReferenceField.Type)"
			sign="@(titleReferenceField.Sign)"
			decimal-places="@(titleReferenceField.DecimalPlaces)"
			with-thousand-separators="@(titleReferenceField.Type is DataType.Integer or DataType.Double or DataType.Long)"
			rich-text="@(titleReferenceField.RichText.HasValue && titleReferenceField.RichText.Value)">
		</card-info-component>
	}
	@if (textOneField != null && textOneReferenceField != null)
	{
		<card-info-component
			name="@(textOneField.Name!)"
			type="@((InputDataType?)textOneReferenceField.Type)"
			sign="@(textOneReferenceField.Sign)"
			decimal-places="@(textOneReferenceField.DecimalPlaces)"
			with-thousand-separators="@(textOneReferenceField.Type is DataType.Integer or DataType.Double or DataType.Long)"
			rich-text="@(textOneReferenceField.RichText.HasValue && textOneReferenceField.RichText.Value)">
		</card-info-component>
	}
	@if (textTwoField != null && textTwoReferenceField != null)
	{
		<card-info-component
			name="@(textTwoField.Name!)"
			type="@((InputDataType?)textTwoReferenceField.Type)"
			sign="@(textTwoReferenceField.Sign)"
			decimal-places="@(textTwoReferenceField.DecimalPlaces)"
			with-thousand-separators="@(textTwoReferenceField.Type is DataType.Integer or DataType.Double or DataType.Long)"
			rich-text="@(textTwoReferenceField.RichText.HasValue && textTwoReferenceField.RichText.Value)">
		</card-info-component>
	}
</card-grid-component>
