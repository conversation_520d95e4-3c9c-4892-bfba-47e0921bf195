using System.Globalization;
using System.Text.RegularExpressions;
using ClosedXML.Excel;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.DataStoreInterface.Filter;
using Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;
using Levelbuild.Core.EntityInterface;
using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.DataSource;
using Levelbuild.Core.FrontendDtos.Enums;
using Levelbuild.Core.FrontendDtos.ExcelPresets;
using Levelbuild.Core.FrontendDtos.FileUpload;
using Levelbuild.Core.FrontendDtos.Shared;
using Levelbuild.Entities;
using Levelbuild.Entities.Extensions.Features;
using Levelbuild.Entities.Features.DataSource;
using Levelbuild.Entities.Features.FileUpload;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.Progress.Interfaces;
using Levelbuild.Frontend.WebApp.Features.User.Services;
using Levelbuild.Frontend.WebApp.Shared.ControllerDtos;
using Levelbuild.Frontend.WebApp.Shared.Enums;
using Levelbuild.Frontend.WebApp.Shared.Extensions;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Levelbuild.Frontend.WebApp.Shared.Utils;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Extensions;
using Newtonsoft.Json;
using SharpCompress;
using DataType = Levelbuild.Core.SharedDtos.Enums.DataType;

namespace Levelbuild.Frontend.WebApp.Features.MultiPage.Controllers;

/// <summary>
/// Controller for multi data pages in the user section
/// </summary>
public partial class MultiPageController : FrontendController
{
	private readonly CoreDatabaseContext _databaseContext;
	private readonly UserManager _userManager;
	private readonly IExtendedStringLocalizerFactory _stringLocalizerFactory;

	/// <summary>
	/// default constructor
	/// </summary>
	/// <param name="logManager"></param>
	/// <param name="userManager">injected UserManager</param>
	/// <param name="contextFactory"></param>
	/// <param name="versionReader">injected VersionReader</param>
	/// <param name="stringLocalizerFactory">injected String Localizer Factory</param>
	public MultiPageController(ILogManager logManager, UserManager userManager, IDbContextFactory<CoreDatabaseContext> contextFactory,
							   IVersionReader versionReader, IExtendedStringLocalizerFactory stringLocalizerFactory) : base(
		logManager, logManager.GetLoggerForClass<MultiPageController>(), versionReader)
	{
		_databaseContext = contextFactory.CreateDbContext();
		_userManager = userManager;
		_stringLocalizerFactory = stringLocalizerFactory;
	}

	/// <summary>
	/// Returns a mutated list of data store elements as JSON.
	/// </summary>
	/// <param name="sourceGuid">ID of the data source configuration</param>
	/// <param name="parameters">an object to query a Subset of elements</param>
	[HttpGet("/Api/DataSources/{sourceGuid:guid}/Elements")]
	public async Task<ActionResult<FrontendResponse>> QueryAsync(Guid sourceGuid, QueryParamsDto parameters)
	{
		var dataSourceEntity = _databaseContext.DataSources
			.Include(dataSource => dataSource.Fields).ThenInclude(field => field.LookupDisplayField)
			.Include(dataSource => dataSource.Fields).ThenInclude(field => field.VirtualLookupField)
			.Include(dataSource => dataSource.Fields).ThenInclude(field => field.VirtualDataField)
			.Include(dataSource => dataSource.Workflows).ThenInclude(workflow => workflow.StatusField)
			.Include(dataSource => dataSource.Workflows).ThenInclude(workflow => workflow.Nodes)
			.AsSplitQuery()
			.FirstOrDefault(source => source.Id == sourceGuid);

		if (dataSourceEntity == null)
			throw new ElementNotFoundException($"DataSource with id: {sourceGuid} could not be found");

		var parameterFields = parameters.Fields?.Select(field => field.ToLower()).ToList();
		var dataFields = dataSourceEntity.Fields.Where(field => parameterFields == null || parameterFields.Count == 0 || parameterFields.Contains(field.Name.ToLower())).ToList();
		var queryFields = dataFields
			.Where(field => field.FieldType == DataFieldType.DataField)
			.Select(field => new DataStoreQueryField(field.Name)).ToList();

		var lookupFields = dataFields.Where(field => field is { LookupSourceId: not null })
			.Select(field => new DataStoreQueryField(field.LookupDisplayFieldId != null ? field.Name + "." + field.LookupDisplayField!.Name : field.Name,
													 field.Name)).ToList();

		if (lookupFields.Count > 0)
			queryFields.AddRange(lookupFields);

		var virtualFields = dataFields.Where(field => field.HasVirtualData)
			.Select(field => new DataStoreQueryField(field.VirtualDataStoreQueryName!, field.Name)).ToList();
		if (virtualFields.Count > 0)
			queryFields.AddRange(virtualFields);

		// add file fields if needed
		if (parameters.IncludeFileInfo)
		{
			var tempFieldNames = new List<string> { "SysFileSize", "SysFileDate", "SysFileType", "SysModifyUser", "SysCreateUser" };
			var tempQueryFields = tempFieldNames.Where(tempQueryField => queryFields.All(queryField => queryField.Name != tempQueryField))
				.Select(name => new DataStoreQueryField(name)).ToList();
			queryFields.AddRange(tempQueryFields);
		}

		// WF fields
		dataSourceEntity.Workflows.Where(workflow => workflow.Enabled).ForEach(workflow =>
		{
			if (workflow.StatusField != null && queryFields.All(field => field.Name != workflow.StatusField.Name))
				queryFields.Add(new DataStoreQueryField(workflow.StatusField.Name));
		});

		// we always need to load the pk field
		// TODO: currently this field is always called "id" and we can't select which field of the definition is the primary key -> this needs to change somewhere in the future ;)
		if (queryFields.Find(field => field.Name == "Id") == null)
			queryFields.Add(new DataStoreQueryField("Id"));

		var query = new DataStoreQuery(queryFields);
		var filters = parameters.Filters?.Select(filter => filter).ToList() ?? [];
		
		// parentField -> add filter for current parent node
		var parentField = string.IsNullOrEmpty(parameters.ParentField) ? null : dataSourceEntity.Fields.FirstOrDefault(field => string.Equals(field.Name, parameters.ParentField, StringComparison.CurrentCultureIgnoreCase));
		if (parentField != null)
		{
			filters.Add(new QueryParamFilterDto
			{
				FilterColumn = parentField.Name,
				Operator = string.IsNullOrEmpty(parameters.ParentId) ? QueryParamFilterOperator.IsNull : QueryParamFilterOperator.Equals,
				CompareValue = parameters.ParentId
			});
		}

		// add filters
		if (parameters.Filters != null)
		{
			var frontendFilters = ParseFrontendFilters(dataSourceEntity, filters);
			if (frontendFilters != null)
			{
				query.WithFilter(frontendFilters);
			}
		}

		// add sorting
		if (parameters.Sortings is { Count: > 0 })
		{
			query.WithOrderBy(parameters.Sortings
								  .Select(sorting => new DataStoreElementSort(sorting.OrderColumn,
																			  DataStoreElementSortDirectionExtensions.Parse(
																				  sorting.Direction.GetDisplayName())))
								  .ToList());
		}

		// add pagination
		if (parameters.Limit > 0)
		{
			query.WithPaging(parameters.Limit, parameters.Offset);
			query.WithCountAll();
		}
		
		query.PrepareQueryForDataStore(dataSourceEntity.Fields);
		
		// send request to the data store and query the data
		QueryResultDto<DataElementDto> queryResultDto;
		IList<DataElementDto> elements = [];
		try
		{
			var result = await dataSourceEntity.GetElementsAsync(query);

			foreach (var element in result)
			{
				element.PrepareValuesForResponse(dataSourceEntity.Fields, queryFields);
				
				var dataElement = new DataElementDto(element.Id, element.Values)
				{
					Favorite = element.IsFavourite,
					Inactive = element.IsInactive,
					WorkflowInfos = GetWorkflowInfos(_stringLocalizerFactory, dataSourceEntity, element)
				};
				elements.Add(dataElement);
				
				// if this record is a parent node then we have to select for the number of children
				// TODO: Team Storage has to deliver that function to reduce the number of single requests, ... maybe as subquery
				if (parentField != null)
					await AppendChildrenToDataElement(sourceGuid, parameters, dataElement);

				if (element.FileInfo == null)
					continue;

				var fileInfoDto = element.FileInfo.ToDto(dataSourceEntity.Id, element.Id);
					
				// TODO: this has to be part of the storage FileInfo!
				if (element.Values.TryGetValue("SysFileType", out var fileType))
					fileInfoDto.FileType = (string)fileType!;
				if (element.Values.TryGetValue("SysFileDate", out var fileDate))
					fileInfoDto.FileDate = ((DateTime)fileDate!).ToUniversalTime();
				if (element.Values.TryGetValue("SysModifyUser", out var modifyUser))
					fileInfoDto.FileChangedUser = (string)modifyUser!;
				if (element.Values.TryGetValue("SysCreateUser", out var createUser))
					fileInfoDto.FileCreateUser = (string)createUser!;

				dataElement.FileInfo = fileInfoDto;
			}

			queryResultDto = new QueryResultDto<DataElementDto>()
			{
				Rows = elements,
				CountTotal = result.CountTotal
			};
		}
		catch (Exception exception)
		{
			Logger.Error(exception, "Failed to load data from data source: {DataSourceName} (GUID: {DataSourceId})", dataSourceEntity.Name,
						 dataSourceEntity.Id);
			queryResultDto = new QueryResultDto<DataElementDto>()
			{
				Rows = [],
				CountTotal = 0
			};
		}

		return GetOkResponse(queryResultDto);

		async Task AppendChildrenToDataElement(Guid guid, QueryParamsDto queryParamsDto, DataElementDto dataElement)
		{
			var queryChildren = queryParamsDto.OpenNodes?.Contains(dataElement.Id) ?? false;
			if (queryChildren)
			{
				var subQueryParams = queryParamsDto;
				subQueryParams.ParentId = dataElement.Id;
				if ((await QueryAsync(guid, subQueryParams)).Result is ObjectResult subqueryResult)
				{
					var subQueryResult = subqueryResult.Value as FrontendResponse<QueryResultDto<DataElementDto>>;
					dataElement.Children = subQueryResult?.Data.Rows;
					dataElement.ChildCount = subQueryResult?.Data.CountTotal ?? 0L;
					dataElement.Expanded = true;
				}
			}
			else
			{
				var subFilters = queryParamsDto.Filters?.Select(filter => filter).ToList() ?? [];
				subFilters.Add(new QueryParamFilterDto
				{
					FilterColumn = parentField.Name,
					Operator = QueryParamFilterOperator.Equals,
					CompareValue = dataElement.Id
				});

				var subQueryParams = new QueryParamsDto()
				{
					Filters = subFilters
				};
				if ((await CountAsync(guid, subQueryParams)).Result is ObjectResult subqueryResult)
					dataElement.ChildCount = (subqueryResult.Value as FrontendResponse<QueryCountDto>)?.Data.Count ?? 0L;
			}
		}
	}

	/// <summary>
	/// load Annotations for datasource
	/// </summary>
	/// <param name="sourceGuid"></param>
	/// <param name="parameters"></param>
	/// <returns></returns>
	/// TODO: make endpoint cancelable (request may get aborted if user leaves dataset before request is finished)
	[HttpGet("/Api/DataSources/{sourceGuid:guid}/Annotations")]
	public async Task<ActionResult<FrontendResponse>> QueryAnnotations(Guid sourceGuid, QueryParamsDto parameters)
	{
		var dataSourceEntity = _databaseContext.DataSources
			.Include(dataSource => dataSource.Fields)
			.Include(dataSource => dataSource.AnnotationGroupByField)
			.Include(dataSource => dataSource.Workflows)
				.ThenInclude(workflow => workflow.StatusField)
			.Include(dataSource => dataSource.Workflows)
				.ThenInclude(workflow => workflow.Nodes)
			.FirstOrDefault(dataSource => dataSource.Id == sourceGuid);

		if (dataSourceEntity == null)
			throw new ElementNotFoundException($"DataSource with id: {sourceGuid} could not be found");

		var queryFields = new List<DataStoreQueryField>
		{
			new("Id"),
			new("AnnotationX"),
			new("AnnotationY"),
			new("SysCreateDate", "AnnotationCreateDate"),
			new("SysCreateUser", "AnnotationCreateUser")
		};

		// Description field(s)
		if (dataSourceEntity.AnnotationLabel?.Contains("##") == true)
		{
			PlaceholderRegex().Matches(dataSourceEntity.AnnotationLabel).ForEach(match =>
			{
				var fieldName = match.Groups[1].Value;
				if (queryFields.Find(queryField => queryField.Name == fieldName) == null &&
					dataSourceEntity.Fields.FirstOrDefault(field => field.Name == fieldName) != null)
					queryFields.Add(new DataStoreQueryField(fieldName));
			});
		}

		// file info fields
		var tempFieldNames = new List<string> { "SysFileSize", "SysFileDate", "SysFileType", "SysModifyUser", "SysCreateUser" };
		var tempQueryFields = tempFieldNames.Where(tempQueryField => queryFields.All(queryField => queryField.Name != tempQueryField))
			.Select(name => new DataStoreQueryField(name)).ToList();
		queryFields.AddRange(tempQueryFields);

		// WF fields
		dataSourceEntity.Workflows.Where(workflow => workflow.Enabled).ForEach(workflow =>
		{
			if (workflow.StatusField != null)
				queryFields.Add(new DataStoreQueryField(workflow.StatusField.Name));
		});

		if (dataSourceEntity.AnnotationGroupByField != null)
			queryFields.Add(new DataStoreQueryField(dataSourceEntity.AnnotationGroupByField.Name, "AnnotationGroupById"));

		var query = new DataStoreQuery(queryFields);

		// add filters
		if (parameters.Filters != null)
		{
			var frontendFilters = ParseFrontendFilters(dataSourceEntity, parameters.Filters);
			if (frontendFilters != null)
				query.WithFilter(frontendFilters);
		}

		// query elements
		var elements = await dataSourceEntity.GetElementsAsync(query);
		var queryResultDto = new QueryResultDto<DataElementDto>
		{
			Rows = elements.Select(element =>
			{
				// prepare Label
				element.Values.Add("AnnotationDescription", PageUtils.ReplacePlaceholders(dataSourceEntity.AnnotationLabel ?? "", element.Values));

				var dataElement = new DataElementDto(element.Id, element.Values);

				if (element.FileInfo != null)
				{
					var fileInfoDto = element.FileInfo.ToDto(dataSourceEntity.Id, element.Id);
					
					// TODO: this has to be part of the storage FileInfo!
					if (element.Values.TryGetValue("SysFileType", out var fileType))
						fileInfoDto.FileType = (string)fileType!;
					if (element.Values.TryGetValue("SysFileDate", out var fileDate))
						fileInfoDto.FileDate = ((DateTime)fileDate!).ToUniversalTime();
					if (element.Values.TryGetValue("SysModifyUser", out var modifyUser))
						fileInfoDto.FileChangedUser = (string)modifyUser!;
					if (element.Values.TryGetValue("SysCreateUser", out var createUser))
						fileInfoDto.FileCreateUser = (string)createUser!;

					dataElement.FileInfo = fileInfoDto;
				}

				dataElement.WorkflowInfos = GetWorkflowInfos(_stringLocalizerFactory, dataSourceEntity, element, true);
				return dataElement;
			}).ToList(),
			CountTotal = elements.CountTotal
		};
		return GetOkResponse(queryResultDto);
	}

	/// <summary>
	/// Returns a mutated list of data store elements grouped by the value including counts.
	/// </summary>
	/// <param name="dataFieldGuid">ID of the used data field configuration</param>
	/// <param name="parameters">an object to query a Subset of elements</param>
	[HttpGet("/Api/DataFields/{dataFieldGuid:guid}/Elements/")]
	public async Task<ActionResult<FrontendResponse>> GroupFilterFieldValuesAsync(Guid dataFieldGuid, QueryParamsDto parameters)
	{
		var dataFieldEntity = _databaseContext.DataFields
			.Include(dataField => dataField.LookupDisplayField)
			.Include(dataField => dataField.VirtualDataField)
			.Include(dataField => dataField.VirtualLookupField)
			.FirstOrDefault(dataField => dataField.Id == dataFieldGuid);

		if (dataFieldEntity == null)
		{
			throw new ElementNotFoundException($"Field with id: {dataFieldGuid} could not be found");
		}

		var dataSourceEntity = _databaseContext.DataSources
			.Include(dataSource => dataSource.Fields).ThenInclude(field => field.LookupDisplayField)
			.Include(dataSource => dataSource.Fields).ThenInclude(field => field.VirtualLookupField)
			.Include(dataSource => dataSource.Fields).ThenInclude(field => field.VirtualDataField)
			.AsSplitQuery()
			.First(dataSource => dataSource.Id == dataFieldEntity.DataSourceId);

		const string countAlias = "Count";
		const string countName = "Count(*)";
		var filterFieldLabel = dataFieldEntity.FieldType switch
		{
			DataFieldType.LookupField => $"{dataFieldEntity.Name}.{dataFieldEntity.LookupDisplayField?.Name}",
			DataFieldType.VirtualField => dataFieldEntity.VirtualDataStoreQueryName ?? dataFieldEntity.Name,
			_ => dataFieldEntity.Name
		};
		var queryFields = new List<DataStoreQueryField> { new(filterFieldLabel), new(countName, countAlias) };
		var query = new DataStoreQuery(queryFields);

		// add filters
		if (parameters.Filters is { Count: > 0 })
		{
			var frontendFilters = ParseFrontendFilters(dataSourceEntity, parameters.Filters);
			if (frontendFilters != null)
			{
				query.WithFilter(frontendFilters);
				query.PrepareQueryForDataStore(dataSourceEntity.Fields);
			}
		}

		query.WithOrderBy([new DataStoreElementSort(countName, DataStoreElementSortDirection.Desc), new DataStoreElementSort(filterFieldLabel)]);
		query.WithGroupBy([filterFieldLabel]);
		query.WithPaging(51);

		// send request to the data store and query the data
		QueryResultDto<FilterFieldQueryItemResultDto> queryResultDto;
		try
		{
			var result = await dataSourceEntity.GetElementsAsync(query);

			foreach (var element in result)
			{
				element.PrepareValuesForResponse(dataSourceEntity.Fields, queryFields);
			}

			// combine null and empty string values together
			var rows = result
				.GroupBy(element => string.IsNullOrEmpty(element.Values[filterFieldLabel]?.ToString()) ? "-" : element.Values[filterFieldLabel]?.ToString())
				.Select(grouping => grouping.Aggregate(new FilterFieldQueryItemResultDto
				{
					Label = grouping.Key,
					Value = "",
					Count = 0
				}, (itemDto, element) =>
				{
					itemDto.Value = element.Values[filterFieldLabel] ?? "";
					itemDto.Count += (long)(element.Values[countAlias] ?? 0L);
					return itemDto;
				}));

			queryResultDto = new QueryResultDto<FilterFieldQueryItemResultDto>
			{
				Rows = rows.ToList(),
				CountTotal = result.CountTotal
			};
		}
		catch (Exception exception)
		{
			Logger.Error(exception, "Failed to load data from data source: {DataSourceName} (GUID: {DataSourceId})", dataSourceEntity.Name,
						 dataSourceEntity.Id);
			queryResultDto = new QueryResultDto<FilterFieldQueryItemResultDto>()
			{
				Rows = [],
				CountTotal = 0
			};
		}

		return GetOkResponse(queryResultDto);
	}

	/// <summary>
	/// Returns the count of elements within a data source.
	/// </summary>
	/// <param name="dataSourceGuid">ID of the used data source configuration</param>
	/// <param name="parameters">an object to query a Subset of elements</param>
	[HttpGet("/Api/DataSources/{dataSourceGuid:guid}/Elements/Count")]
	public async Task<ActionResult<FrontendResponse>> CountAsync(Guid dataSourceGuid, QueryParamsDto parameters)
	{
		var dataSourceEntity = _databaseContext.DataSources
			.Include(dataSource => dataSource.Fields).ThenInclude(field => field.LookupDisplayField)
			.Include(dataSource => dataSource.Fields).ThenInclude(field => field.VirtualLookupField)
			.Include(dataSource => dataSource.Fields).ThenInclude(field => field.VirtualDataField)
			.AsSplitQuery()
			.FirstOrDefault(dataSource => dataSource.Id == dataSourceGuid);

		if (dataSourceEntity == null)
		{
			throw new ElementNotFoundException($"Source with id: {dataSourceGuid} could not be found");
		}

		const string countAlias = "Count";
		const string countName = "Count(*)";
		var queryFields = new List<DataStoreQueryField> { new(countName, countAlias) };
		var query = new DataStoreQuery(queryFields);

		// add filters
		if (parameters.Filters is { Count: > 0 })
		{
			var frontendFilters = ParseFrontendFilters(dataSourceEntity, parameters.Filters);
			if (frontendFilters != null)
			{
				query.WithFilter(frontendFilters);
			}
		}
		
		query.PrepareQueryForDataStore(dataSourceEntity.Fields);
		
		// send request to the data store and query the data
		QueryCountDto queryResultDto;
		try
		{
			var result = await dataSourceEntity.GetElementsAsync(query);
			queryResultDto = new QueryCountDto
			{
				Count = (long)(result.FirstOrDefault()?.Values[countAlias] ?? 0L)
			};
		}
		catch (Exception exception)
		{
			Logger.Error(exception, "Failed to load data count from data source: {DataSourceName} (GUID: {DataSourceId})", dataSourceEntity.Name,
						 dataSourceEntity.Id);
			return GetBadRequestResponse(exception.Message);
		}

		return GetOkResponse(queryResultDto);
	}

	/// <summary>
	/// Execute an action on a specific record
	/// </summary>
	/// <param name="sourceGuid">ID of the data source configuration</param>
	/// <param name="actionDto">Dto which contains the action</param>
	[HttpPatch("/Api/DataSources/{sourceGuid:guid}/Elements/Action")]
	public async Task<ActionResult<FrontendResponse>> ExecuteAction(Guid sourceGuid, [FromBody] ElementActionDto actionDto)
	{
		try
		{
			var dataSource = await _databaseContext.DataSources
								 .FirstOrDefaultAsync(entity => entity.Id == sourceGuid);
			if (dataSource == null)
				throw new ElementNotFoundException($"DataSource configuration with id: {sourceGuid} could not be found");

			if (actionDto.Elements != null)
			{
				var tasks = actionDto.Elements.Select(elementGuid => dataSource.ExecuteElementAction(actionDto, elementGuid.ToString()));
				await Task.WhenAll(tasks);
			}

			return GetOkResponse();
		}
		catch (ElementNotFoundException)
		{
			return GetNotFoundResponse($"DataSource with id: {sourceGuid} could not be found.");
		}
		catch (Exception e)
		{
			Logger.Error(e, "Element could not be updated");
			return GetBadRequestResponse(e.Message);
		}
	}

	public static IList<WorkflowInfoDto> GetWorkflowInfos(IExtendedStringLocalizerFactory localizerFactory, DataSourceEntity dataSourceEntity, DataStoreElement element, bool includeNodes = false)
	{
		IList<WorkflowInfoDto> workflowInfos = [];
		dataSourceEntity.Workflows.OrderBy(workflow => workflow.Slot).ForEach(workflow =>
		{
			if (!workflow.Enabled)
				return;

			if (workflow.StatusField == null)
				return;

			// Status value might be empty or not a valid Guid -> take start node if that happens
			Guid statusValue;
			try
			{
				statusValue = Guid.Parse((element.Values[workflow.StatusField!.Name] as string)!);
			}
			catch (Exception e)
			{
				if (e is FormatException or ArgumentNullException)
					statusValue = workflow.Nodes.First(node => node.State == WorkflowNodeState.Start).Id;
				else
					throw;
			}
			var node = workflow.Nodes.FirstOrDefault(node => node.Id == statusValue);
			if (node == null)
				return;

			var workflowLocalizer = localizerFactory.Create($"Workflow", workflow.Id.ToString());
			workflowInfos.Add(new WorkflowInfoDto()
			{
				Slot = workflow.Slot,
				WorkflowId = workflow.Id,
				WorkflowName = workflowLocalizer[workflow.Name],
				NodeId = node.Id,
				NodeName = workflowLocalizer[node.Name],
				NodeIcon = node.Icon,
				State = node.State,
				Nodes = includeNodes ? workflow.Nodes.OrderBy(nodeDefinition => nodeDefinition.Sorting).Select(nodeDefinition => new WorkflowNodeInfoDto()
				{
					Id = nodeDefinition.Id,
					Name = workflowLocalizer[nodeDefinition.Name],
					Icon = nodeDefinition.Icon,
					State = nodeDefinition.State
				}).ToList() : []
			});
		});

		return workflowInfos;
	}
	
	/// <summary>
	/// loads a dataset from the given data source by its ID
	/// </summary>
	/// <param name="dataSourceId">ID of a DataSourceEntity</param>
	/// <param name="aggregationFieldId">ID of a dataset within this data source</param>
	/// <param name="aggregationMethod">ID of a dataset within this data source</param>
	/// <param name="parameters">an object to query a Subset of elements</param>
	/// <returns></returns>
	[HttpGet("/Api/DataSources/{dataSourceId:guid}/Elements/DataFields/{aggregationFieldId:guid}/Method/{aggregationMethod}")]
	public async Task<ActionResult<FrontendResponse>> Aggregation(Guid dataSourceId, Guid aggregationFieldId, string aggregationMethod, QueryParamsDto parameters)
	{
		var dataSource = await _databaseContext.DataSources
							 .Include(dataSource => dataSource.Fields).ThenInclude(field => field.LookupDisplayField)
							 .Include(dataSource => dataSource.Fields).ThenInclude(field => field.VirtualLookupField)
							 .Include(dataSource => dataSource.Fields).ThenInclude(field => field.VirtualDataField)
							 .AsSplitQuery()
							 .FirstOrDefaultAsync(dataSource => dataSource.Id == dataSourceId);
		
		if(dataSource == null)
			return GetNotFoundResponse($"DataSource with id: {dataSourceId} could not be found");

		var aggregationField = dataSource.Fields.FirstOrDefault(entity => entity.Id == aggregationFieldId);
		if(aggregationField == null)
			return GetNotFoundResponse($"DataField with id: {aggregationFieldId} could not be found");
		
		if(!Enum.IsDefined(typeof(AggregationMethod), aggregationMethod))
			return GetBadRequestResponse($"Aggregation method {aggregationMethod} is not defined");

		if (aggregationMethod == AggregationMethod.Average.ToString())
			aggregationMethod = "avg";

		string? fieldName = null;
		switch (aggregationField.FieldType)
		{
			case DataFieldType.DataField:
				fieldName = aggregationField.Name;
				break;
			case DataFieldType.LookupField:
				fieldName = $"{aggregationField.Name}.{aggregationField.LookupDisplayField!.Name}"; 
				break;
			case DataFieldType.VirtualField:
				if(!aggregationField.HasVirtualData)
					return GetBadRequestResponse($"Field {aggregationField.Name} could not be found");
				fieldName = aggregationField.VirtualDataStoreQueryName!;
				break;
		}
		
		var queryFields = new List<DataStoreQueryField> { new($"{aggregationMethod}({fieldName})", aggregationMethod) };
		var query = new DataStoreQuery(queryFields);
		
		// add filters
		if (parameters.Filters is { Count: > 0 })
		{
			var frontendFilters = ParseFrontendFilters(dataSource, parameters.Filters);
			if (frontendFilters != null)
			{
				query.WithFilter(frontendFilters);
				query.PrepareQueryForDataStore(dataSource.Fields);
			}
		}
		
		AggregationQueryResultDto queryResultDto;
		try
		{
			var result = await dataSource.GetElementsAsync(query);
			if (result.FirstOrDefault()?.Values.TryGetValue(aggregationMethod, out var value) == true)
			{
				queryResultDto = new AggregationQueryResultDto()
				{
					Value = Convert.ToDouble(value ?? 0D),
				};
			}
			else
			{
				return GetBadRequestResponse($"Failed to load {aggregationMethod}({aggregationField.Name}) from data source: {dataSource.Name} (GUID: {dataSource.Id})");
			}

		}
		catch (Exception exception)
		{
			Logger.Error(exception, "Failed to load {Method}({DataField}) from data source: {DataSourceName} (GUID: {DataSourceId})", aggregationMethod, aggregationField.Name, dataSource.Name,
						 dataSource.Id);
			return GetBadRequestResponse($"Failed to load {aggregationMethod}({aggregationField.Name}) from data source: {dataSource.Name} (GUID: {dataSource.Id})");
		}
		return GetOkResponse(queryResultDto);
	}

	[GeneratedRegex("##([a-zA-Z0-9_-]+)##")]
	private static partial Regex PlaceholderRegex();

	/// <summary>
	/// Export data to Excel
	/// </summary>
	/// <param name="redisProgressService">Redis progress service</param>
	/// <returns>Excel file as a stream</returns>
	[HttpPost("/Api/Stream/Export/Data")]
	public async Task<IActionResult> ExportExcelData([FromServices] IRedisProgressService redisProgressService)
	{
		// Get the process ID from the request header
		var processId = Request.Headers["X-Process-ID"].ToString();

		// Check if process ID is provided
		if (string.IsNullOrEmpty(processId))
		{
			// Log the error
			return BadRequest("Process ID is required");
		}

		// Get current user for Redis progress service
		var currentUser = await _userManager.GetCurrentUserAsync();
		var userId = currentUser.Id;

		try
		{
			// Read the request body only once and store it
			string requestBody;

			// Enable buffering to allow the request body to be read multiple times if needed
			using (var streamReader = new StreamReader(Request.Body))
			{
				// Read the request body
				requestBody = await streamReader.ReadToEndAsync();
			}

			// Deserialize the request body with custom settings to handle unused field conversion issues
			var jsonSettings = new JsonSerializerSettings
			{
				Error = (_, args) =>
				{
					// Ignore conversion errors for unused fields (fieldId, id, dataType, fieldType)
					// Note: columnOrder is kept as it's used for preset-based exports
					var path = args.ErrorContext.Path;
					if (path.Contains("fieldId") || path.Contains(".id") ||
					    path.Contains("dataType") || path.Contains("fieldType"))
					{
						args.ErrorContext.Handled = true;
					}
				}
			};
			var exportRequestData = JsonConvert.DeserializeObject<ExportRequestDto>(requestBody, jsonSettings);

			// Check if the request is valid
			if (exportRequestData?.Columns == null || exportRequestData.Columns.Count == 0)
			{
				// Send error message to the client
				await redisProgressService.PublishErrorMessageAsync(userId, processId, "No data found");
				// Return bad request
				return BadRequest("No data found");
			}

			// Get the data source ID using field IDs for more reliable identification
			Guid? dataSourceId = exportRequestData.DataSourceId;

			// Get the data source with lookup display fields for GUID field handling
			var dataSource = _databaseContext.DataSources
				.Include(dataSource => dataSource.Fields)
					.ThenInclude(field => field.LookupDisplayField)
				.FirstOrDefault(dataSource => dataSource.Id == dataSourceId);

			
			// Check if the data source exists
			if (dataSource == null)
			{
				// Send error message to the client
				throw new ElementNotFoundException($"DataSource with id: {dataSourceId} could not be found");
			}

			// Get all fields using the key (field name) instead of display, resolving virtual fields
			var queryFields = exportRequestData.Columns
				.Select(column => ResolveVirtualFieldForExport(dataSource, column.key))
				.ToList();

			// Add additional query fields for GUID fields using the standard system method
			var additionalQueryFields = dataSource.SelectLookupAndVirtualFields();

			// Create query with both original and additional fields
			var allQueryFields = new List<DataStoreQueryField>();
			allQueryFields.AddRange(queryFields);
			allQueryFields.AddRange(additionalQueryFields);

			var dataStoreQuery = new DataStoreQuery(allQueryFields);
			// Prepare query for data store
			dataStoreQuery.PrepareQueryForDataStore(dataSource.Fields);

			// Add filters
			var filtersToApply = new List<QueryParamFilterDto>();
			
			// Convert search text to fulltext search filter
			if (!string.IsNullOrWhiteSpace(exportRequestData.SearchText))
			{
				filtersToApply.Add(new QueryParamFilterDto
				{
					FilterColumn = "fulltext",
					Operator = QueryParamFilterOperator.FulltextSearch,
					CompareValue = exportRequestData.SearchText
				});
			}
			
			// Add additional filters from the request
			if (exportRequestData.Filters != null)
			{
				// Convert the filters object to QueryParamFilterDto list
				var filtersJson = JsonConvert.SerializeObject(exportRequestData.Filters);
				var filtersList = JsonConvert.DeserializeObject<List<QueryParamFilterDto>>(filtersJson);
				if (filtersList != null && filtersList.Count > 0)
				{
					filtersToApply.AddRange(filtersList);
				}
			}
			
			// Apply frontend filters using the existing method
			if (filtersToApply.Count > 0)
			{
				var frontendFilters = ParseFrontendFilters(dataSource, filtersToApply);
				if (frontendFilters != null)
				{
					dataStoreQuery.WithFilter(frontendFilters);
				}
			}
			
			// Add selected IDs filter if provided
			if (exportRequestData.SelectedIds != null && exportRequestData.SelectedIds.Count > 0)
			{
				// Create filter group for selected IDs
				var selectedIdsFilterGroup = new QueryFilterGroup();
				// Add filter
				selectedIdsFilterGroup.AddFilter(new InFilter(new QueryFilterField("Id"), exportRequestData.SelectedIds));
				// Add filter group to query
				dataStoreQuery.WithFilter(selectedIdsFilterGroup);
			}

			// Get total count
			var queryResult = await dataSource.GetElementsAsync(dataStoreQuery);
			// Get total count
			var totalEntries = queryResult.Count();

			// Send initial progress
			await redisProgressService.PublishProgressUpdateAsync(userId, processId, 0, totalEntries, "Starting export process");

			// Create Excel workbook
			using var excelWorkbook = new XLWorkbook();
			// Add worksheet
			var excelWorksheet = excelWorkbook.Worksheets.Add("Data");

			// Add headers using display names from the request
			for (var columnIndex = 0; columnIndex < queryFields.Count; columnIndex++)
			{
				// Find the corresponding column in the request to get the display name
				// For virtual fields, use the alias (original field name) to match the request columns
				var fieldKey = queryFields[columnIndex].Alias ?? queryFields[columnIndex].Name;
				var requestColumn = exportRequestData.Columns.FirstOrDefault(col => col.key == fieldKey);
				var headerName = requestColumn?.key ?? fieldKey;

				// Get localized header name
				excelWorksheet.Cell(1, columnIndex + 1).Value = GetLocalizedHeaderName(headerName);
			}

			// Add data rows with progress updates
			var currentRowIndex = 2;
			var processedRecordsCount = 0;
			var batchSize = 100; // Process in batches to prevent timeouts
			var currentBatchNumber = 0;

			// Process in batches
			foreach (var elementBatch in queryResult.Chunk(batchSize))
			{
				// Process each element in the batch
				foreach (var dataElement in elementBatch)
				{
					// Use the standard system method to parse composite values and prepare for response
					dataElement.ParseCompositeValuesForResponse(additionalQueryFields);
					dataElement.PrepareValuesForResponse(dataSource.Fields, allQueryFields);

					// Add data row
					for (var currentColumnIndex = 0; currentColumnIndex < queryFields.Count; currentColumnIndex++)
					{
						// Get field using alias for virtual fields or original name for others
						var fieldKey = queryFields[currentColumnIndex].Alias ?? queryFields[currentColumnIndex].Name;
						var dataField = dataSource.Fields.First(field => field.Name == fieldKey);
						// Get value using the alias for virtual fields
						object? cellValue;
						// ReSharper disable once PossibleMultipleEnumeration
						dataElement.Values.TryGetValue(fieldKey, out cellValue);

						// Get cell
						var excelCell = excelWorksheet.Cell(currentRowIndex, currentColumnIndex + 1);
						// Set value
						if (cellValue != null)
						{
							// Convert value based on data type
							switch (dataField.Type)
							{
								case DataType.Integer:
									if (int.TryParse(cellValue.ToString(), out int integerValue))
										excelCell.SetValue(integerValue);
									else
										excelCell.SetValue(cellValue.ToString());
									break;
								case DataType.Long:
									if (long.TryParse(cellValue.ToString(), out long longValue))
										excelCell.SetValue(longValue);
									else
										excelCell.SetValue(cellValue.ToString());
									break;
								case DataType.Double:
									if (double.TryParse(cellValue.ToString(), out double doubleValue))
										excelCell.SetValue(doubleValue);
									else
										excelCell.SetValue(cellValue.ToString());
									break;
								case DataType.Date:
									if (DateTime.TryParse(cellValue.ToString(), out DateTime dateValue))
									{
										// For Date fields, ensure we only use the date part without timezone conversion
										// Database values are stored in UTC, but for date-only fields we want the date part
										excelCell.SetValue(dateValue.Date);
										excelCell.Style.DateFormat.Format = "MM/dd/yyyy";
									}
									else
										excelCell.SetValue(cellValue.ToString());
									break;
								case DataType.DateTime:
									if (DateTime.TryParse(cellValue.ToString(), out DateTime dateTimeValue))
									{
										// Database values are stored in UTC, convert to local time for Excel export
										// to prevent timezone offset issues
										if (dateTimeValue.Kind == DateTimeKind.Utc)
										{
											dateTimeValue = dateTimeValue.ToLocalTime();
										}
										excelCell.SetValue(dateTimeValue);
										excelCell.Style.DateFormat.Format = "MM/dd/yyyy hh:mm AM/PM";
									}
									else
										excelCell.SetValue(cellValue.ToString());
									break;
								case DataType.Time:
									if (cellValue is DateTime timeAsDateTime)
									{
										// Handle Time values that come as DateTime from database
										// Use the time portion directly as a DateTime value for Excel
										var timeForExcel = new DateTime(1900, 1, 1, timeAsDateTime.Hour, timeAsDateTime.Minute, timeAsDateTime.Second);
										excelCell.SetValue(timeForExcel);
										excelCell.Style.DateFormat.Format = "h:mm AM/PM";
									}
									else if (TimeSpan.TryParse(cellValue.ToString(), out TimeSpan timeValue))
									{
										// Convert TimeSpan to DateTime for Excel formatting
										var timeForExcel = new DateTime(1900, 1, 1).Add(timeValue);
										excelCell.SetValue(timeForExcel);
										excelCell.Style.DateFormat.Format = "h:mm AM/PM";
									}
									else if (DateTime.TryParse(cellValue.ToString(), out DateTime parsedDateTime))
									{
										// If we can parse it as DateTime, extract time portion
										var timeForExcel = new DateTime(1900, 1, 1, parsedDateTime.Hour, parsedDateTime.Minute, parsedDateTime.Second);
										excelCell.SetValue(timeForExcel);
										excelCell.Style.DateFormat.Format = "h:mm AM/PM";
									}
									else
										excelCell.SetValue(cellValue.ToString());
									break;
								case DataType.Boolean:
									if (bool.TryParse(cellValue.ToString(), out var boolValue))
										excelCell.SetValue(boolValue);
									else
										excelCell.SetValue(cellValue.ToString());
									break;
								case DataType.Guid:
									// Handle GUID lookup fields which should display their name/display value instead of the raw ID
									if (cellValue is Dictionary<string, object> guidData)
									{
										// If the GUID field is a dictionary with name/id, use the name (display value)
										if (guidData.TryGetValue("name", out var nameValue) && nameValue != null)
										{
											excelCell.SetValue(nameValue.ToString() ?? "");
										}
										else if (guidData.TryGetValue("DisplayValue", out var displayValue) && displayValue != null)
										{
											excelCell.SetValue(displayValue.ToString() ?? "");
										}
										else if (guidData.TryGetValue("id", out var idValue))
										{
											excelCell.SetValue(idValue.ToString() ?? "");
										}
										else
										{
											excelCell.SetValue(cellValue.ToString() ?? "");
										}
									}
									else
									{
										// Fallback to string representation if not in expected format
										excelCell.SetValue(cellValue.ToString() ?? "");
									}
									break;
								case DataType.String:
								case DataType.Text:
								default:
									excelCell.SetValue(cellValue.ToString() ?? "");
									break;
							}
						}
						else
						{
							excelCell.SetValue("");
						}
					}
					currentRowIndex++;
					processedRecordsCount++;
				}

				// Update progress after each batch
				currentBatchNumber++;
				await redisProgressService.PublishProgressUpdateAsync(
					userId,
					processId,
					processedRecordsCount,
					totalEntries,
					$"Processing batch {currentBatchNumber} of {(totalEntries + batchSize - 1) / batchSize}"
				);

				// Small delay between batches to prevent overwhelming the connection
				await Task.Delay(100);
			}

			// Format headers and adjust columns
			var headerRow = excelWorksheet.Row(1);
			// Make headers bold
			headerRow.Style.Font.Bold = true;

			// Apply column formatting
			for (var currentColumnIndex = 0; currentColumnIndex < queryFields.Count; currentColumnIndex++)
			{
				var fieldKey = queryFields[currentColumnIndex].Alias ?? queryFields[currentColumnIndex].Name;
				var dataField = dataSource.Fields.First(field => field.Name == fieldKey);
				var excelColumn = excelWorksheet.Column(currentColumnIndex + 1);

				// Apply column formatting
				switch (dataField.Type)
				{
					case DataType.Integer:
					case DataType.Long:
						excelColumn.Style.NumberFormat.Format = "#,##0";
						break;
					case DataType.Double:
						excelColumn.Style.NumberFormat.Format = dataField.DecimalPlaces > 0 ?
							$"#,##0.{new string('0', dataField.DecimalPlaces)}" : "#,##0";
						break;
					case DataType.Date:
						excelColumn.Style.DateFormat.Format = "MM/dd/yyyy";
						break;
					case DataType.DateTime:
						excelColumn.Style.DateFormat.Format = "MM/dd/yyyy hh:mm AM/PM";
						break;
					case DataType.Time:
						excelColumn.Style.DateFormat.Format = "h:mm AM/PM";
						break;
				}
			}

			// Adjust column widths
			excelWorksheet.Columns().AdjustToContents();

			// Convert to bytes
			using var memoryStream = new MemoryStream();
			// Save workbook
			excelWorkbook.SaveAs(memoryStream);
			// Get bytes
			var excelFileBytes = memoryStream.ToArray();

			// Create file name
			var exportFileName = $"{dataSource.Name}_export_{DateTime.Now:yyyy-MM-dd}.xlsx";

			// Add metadata headers
			Response.Headers.Append("X-Total-Records", queryResult.Count().ToString());
			Response.Headers.Append("X-Columns-Count", queryFields.Count.ToString());
			Response.Headers.Append("Content-Length", excelFileBytes.Length.ToString());

			// Send completion message
			await redisProgressService.PublishCompletionMessageAsync(userId, processId, true, "Export completed successfully");

			// Return file
			return File(excelFileBytes,
				"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
				exportFileName);
		}
		catch (Exception exportException)
		{
			// Log the error
			Logger.Error(exportException, "Error during export process");
			// Send error message
			await redisProgressService.PublishErrorMessageAsync(userId, processId, "Export failed: " + exportException.Message);
			// Return error
			return StatusCode(400, new { error = "Export failed", message = exportException.Message });
		}
	}

	/// <summary>
	/// Returns a list of available columns for a data source.
	/// </summary>
	/// <param name="pageViewGuid">ID of the page view</param>
	[HttpGet("/Api/PageViews/{pageViewGuid:guid}/Columns")]
	public ActionResult<FrontendResponse> GetPageViewColumns(Guid pageViewGuid)
	{
		// Get all columns for the given page view
		var listViewColumns = _databaseContext.ListViewColumns
			.Include(listViewColumn => listViewColumn.Field)
			.Where(listViewColumn =>
				listViewColumn.ListViewId == pageViewGuid &&
				listViewColumn.Field != null)
			.OrderBy(listViewColumn => listViewColumn.Position)
			.ToList();

		// Check if there are any columns
		if (!listViewColumns.Any())
			return GetOkResponse(new PresetColumnsResultDto
			{
				visibleColumns = new List<PresetColumnInfoDto>(),
				InvisibleColumns = new List<PresetColumnInfoDto>(),
				DataSourceId = null
			});

		// Get the first column to find the DataSourceId
		var firstListViewColumn = listViewColumns.FirstOrDefault();
		// Check if the first column exists and has a Field
		if (firstListViewColumn?.Field == null)
			return GetOkResponse(new PresetColumnsResultDto
			{
				visibleColumns = new List<PresetColumnInfoDto>(),
				InvisibleColumns = new List<PresetColumnInfoDto>(),
				DataSourceId = null
			});

		// Get the DataSourceId from the first column's Field
		var dataSourceId = firstListViewColumn.Field.DataSourceId;
		// Check if the DataSourceId exists
		if (dataSourceId == null)
			return GetOkResponse(new PresetColumnsResultDto
			{
				visibleColumns = new List<PresetColumnInfoDto>(),
				InvisibleColumns = new List<PresetColumnInfoDto>(),
				DataSourceId = null
			});

		// Get all visible columns
		var visibleColumnsList = listViewColumns
			.Select(listViewColumn => new PresetColumnInfoDto
			{
				Key = listViewColumn.Field!.Name.ToLower(),
				Display = GetLocalizedHeaderName(listViewColumn.Field.Name),
				FieldType = listViewColumn.Field.Type.ToString(),
				FieldId = listViewColumn.Field.Id,
				IsRequired = listViewColumn.Field.Mandatory
			})
			.ToList();

		// Get all invisible columns from the same data source
		var invisibleColumnsData = _databaseContext.DataFields
			.Where(dataField =>
				dataField.DataSourceId == dataSourceId &&
				dataField.SystemField == false &&
				dataField.AutoGenerated == false &&
				!listViewColumns.Select(listViewColumn => listViewColumn.FieldId).Contains(dataField.Id))
			.Select(dataField => new
			{
				Name = dataField.Name,
				FieldType = dataField.Type.ToString(),
				FieldId = dataField.Id,
				IsRequired = dataField.Mandatory
			})
			.ToList();

		// Apply localization after the database query
		var invisibleColumnsList = invisibleColumnsData
			.Select(dataField => new PresetColumnInfoDto
			{
				Key = dataField.Name.ToLower(),
				Display = GetLocalizedHeaderName(dataField.Name),
				FieldType = dataField.FieldType,
				FieldId = dataField.FieldId
			})
			.ToList();

		// Create the result
		var columnsResult = new PresetColumnsResultDto
		{
			visibleColumns = visibleColumnsList,
			InvisibleColumns = invisibleColumnsList,
			DataSourceId = dataSourceId
		};

		// Return the result
		return GetOkResponse(columnsResult);
	}

	/// <summary>
	/// Import data from Excel file
	/// </summary>
	/// <param name="redisProgressService">Redis progress service</param>
	/// <returns>Import result with summary and any errors</returns>
	[HttpPost("/Api/PageViews/ExcelImport")]
	public async Task<ActionResult<FrontendResponse>> ImportExcelData([FromServices] IRedisProgressService redisProgressService)
	{
		// Get the process ID from the request header
		var processId = Request.Headers["X-Process-ID"].ToString();
		// Check if process ID is provided
		if (string.IsNullOrEmpty(processId))
		{
			// Log the error
			return BadRequest("Process ID is required");
		}

		// Get current user for Redis progress service
		var currentUser = await _userManager.GetCurrentUserAsync();
		var userId = currentUser.Id;

		try
		{
			// Get the import parameters from the request
			var importParametersJson = Request.Form["importParams"].ToString();
			// Deserialize the import parameters
			var importParameters = JsonConvert.DeserializeObject<ExcelImportParametersDto>(importParametersJson);

			// Check if the import parameters are valid
			if (importParameters == null)
			{
				// Log the error
				return BadRequest("Invalid import parameters");
			}

			// Get the data source
			var dataSource = await _databaseContext.DataSources
								 .Include(dataSource => dataSource.Fields).ThenInclude(dataFieldEntity => dataFieldEntity.LookupDisplayField!)
								 .FirstOrDefaultAsync(dataSource => dataSource.Id == importParameters.DataSourceId);

			// Check if the data source exists
			if (dataSource == null)
			{
				// Log the error
				throw new ElementNotFoundException($"DataSource with id: {importParameters.DataSourceId} could not be found");
			}

			// Get the Excel file from the request
			var uploadedFile = Request.Form.Files.FirstOrDefault();
			// Check if the file exists
			if (uploadedFile == null || uploadedFile.Length == 0)
			{
				// Log the error
				return BadRequest("No file uploaded");
			}



			byte[] fileContentBytes;
			// Read the file content as a byte array for storage in the database
			using (var fileMemoryStream = new MemoryStream())
			{
				// Copy the file content to the memory stream
				uploadedFile.OpenReadStream().CopyTo(fileMemoryStream);
				// Get the byte array from the memory stream
				fileContentBytes = fileMemoryStream.ToArray();
			}

			// Save file details to the database using FileUploadDto
			// Note: currentUser already retrieved above
			// Create the FileUploadDto
			var fileUploadDto = new FileUploadDto
			{
				DataSourceId = dataSource.Id,
				FileName = uploadedFile.FileName,
				IsPublic = false,
				IsReadOnly = true,
				IsDeleted = false,
				Created = DateTime.Now,
				UploadedBy = currentUser.DisplayName,
				LastModified = DateTime.Now,
				LastModifiedBy = currentUser.DisplayName,
				Revision = Guid.NewGuid()
			};

			// Create the FileUploadEntity and save it to the database
			var fileUploadEntity = FileUploadEntity.FromDto(fileUploadDto, currentUser, _databaseContext);
			// Set the file content directly in the entity
			fileUploadEntity.File = fileContentBytes;
			// Initialize import statistics and status
			fileUploadEntity.Status = "Import in progress";
			// Add the entity to the context
			_databaseContext.FileUploads.Add(fileUploadEntity);

			try
			{
				// Save changes to the database and ensure the file is properly saved before proceeding
				await _databaseContext.SaveChangesAsync();

				// Only proceed with Excel processing if the file was successfully saved
				Logger.Information("File successfully saved to database with ID: {FileEntityId}", fileUploadEntity.Id);

				// Read the Excel file
				using var fileStream = uploadedFile.OpenReadStream();
				// Create the workbook
				using var excelWorkbook = new XLWorkbook(fileStream);
				// Get the first worksheet
				var excelWorksheet = excelWorkbook.Worksheet(1); // Get the first worksheet

				// Check if the worksheet exists and has data
				if (excelWorksheet == null || excelWorksheet.LastRowUsed() == null)
				{
					return BadRequest("Excel file is empty or has invalid format");
				}

				// Get the header row
				var headerRow = excelWorksheet.Row(1);
				// Get all headers
				var excelHeaders = new List<string>();
				// Get the last cell used in the header row
				var lastUsedCell = headerRow.LastCellUsed();
				// Check if there are any headers
				if (lastUsedCell != null)
				{
					// Get all headers
					for (var columnIndex = 1; columnIndex <= lastUsedCell.Address.ColumnNumber; columnIndex++)
					{
						// Add the header to the list
						excelHeaders.Add(headerRow.Cell(columnIndex).Value.ToString());
					}
				}

				// Map Excel columns to data fields
				var excelColumnMappings = new List<ExcelColumnMappingDto>();
				// Loop through all selected mappings
				foreach (var selectedMapping in importParameters.SelectedMappings)
				{
					// Excel columns are 1-based, so add 1 to the index
					var excelColumnIndex = selectedMapping.index + 1;

					// Check if the Excel column index is valid
					if (excelColumnIndex > 0 && excelColumnIndex <= excelHeaders.Count)
					{
						// Get the header name for the Excel column
						// Add the mapping
						excelColumnMappings.Add(selectedMapping);
					}
				}

				// Process data rows
				var importResult = new ExcelRecordDto();
				// Get the last row
				var lastUsedRow = excelWorksheet.LastRowUsed();
				// Check if there are any rows
				if (lastUsedRow == null)
				{
					return BadRequest("Excel file is empty or has invalid format");
				}
				// Get the row count
				var totalRowCount = lastUsedRow.RowNumber();
				// Get the start row
				var startRowIndex = importParameters.IsFirstLineHeader ? 2 : 1;
				var processedRowsCount = 0;
				// Get the current user
				var currentUserEntity = await _userManager.GetCurrentUserAsync();

				// PERFORMANCE OPTIMIZATION: Get unique set columns once outside the loop
				var uniqueSetColumns = _databaseContext.UniqueSetColumns
					.Include(uniqueSetColumn => uniqueSetColumn.DataField)
					.Where(uniqueSetColumn => uniqueSetColumn.DataSourceId == dataSource.Id)
					.Select(uniqueSetColumn => uniqueSetColumn.ColumnName)
					.ToList();

				// PERFORMANCE OPTIMIZATION: Cache for duplicate checking to avoid repeated queries
				var duplicateCheckCache = new Dictionary<string, string>(); // Key: hash of values, Value: existing element ID

				// Send initial progress
				await redisProgressService.PublishProgressUpdateAsync(userId, processId, 0, totalRowCount - startRowIndex + 1, "Starting import process");

				// Process each row
				for (int currentRowIndex = startRowIndex; currentRowIndex <= totalRowCount; currentRowIndex++)
				{
					// Create a dictionary to store the row data
					Dictionary<string, object?> currentRowData = new();
					// Keep track of the last processed column and value for error reporting
					string? lastProcessedColumnKey = null;
					// Keep track of the last processed value for error reporting
					string? lastProcessedCellValue = null;
					// Track if there were any conversion errors in this row
					bool hasConversionErrors = false;

					try
					{
						// Process each column mapping
						foreach (var columnMapping in excelColumnMappings)
						{
							// Excel columns are 1-based, so add 1 to the index
							var excelColumnIndex = columnMapping.index + 1;
							// Get the cell
							var excelCell = excelWorksheet.Cell(currentRowIndex, excelColumnIndex);
							// Get the value
							var cellValue = excelCell.DataType == XLDataType.Number ? excelCell.GetValue<double>().ToString(CultureInfo.InvariantCulture) : excelCell.Value.ToString();
							// Update the last processed column and value
							lastProcessedColumnKey = columnMapping.ColumnKey;
							// Update the last processed value
							lastProcessedCellValue = cellValue;

							// Convert the value based on the data type with proper error handling
							object? convertedValue = null;
							string? conversionError = null;

							try
							{
								convertedValue = columnMapping.DataType switch
								{
									"Integer" => ConvertToInteger(cellValue, columnMapping.ColumnKey),
									"Long" => ConvertToLong(cellValue, columnMapping.ColumnKey),
									"Double" => ConvertToDouble(cellValue, columnMapping.ColumnKey),
									"Date" => ConvertToDate(cellValue, columnMapping.ColumnKey, dateOnly: true),
									"DateTime" => ConvertToDate(cellValue, columnMapping.ColumnKey, dateOnly: false),
									"Time" => ConvertToTime(cellValue, columnMapping.ColumnKey),
									"Boolean" => ConvertToBoolean(cellValue, columnMapping.ColumnKey),
									_ => string.IsNullOrWhiteSpace(cellValue) ? null : cellValue
								};
							}
							catch (ArgumentException ex)
							{
								// Capture conversion error but don't throw - we'll handle it below
								conversionError = ex.Message;
								hasConversionErrors = true;
							}

							// Add the converted value to the row data if successful
							if (conversionError == null && convertedValue != null)
							{
								currentRowData[columnMapping.ColumnKey] = convertedValue;
							}
							else if (conversionError != null)
							{
								// Log conversion error
								importResult.Errors.Add(new ExcelImportError
								{
									RowNumber = currentRowIndex,
									Error = conversionError,
									ColumnKey = columnMapping.ColumnKey,
									Value = cellValue
								});
							}
						}

						// If there were conversion errors, skip this row
						if (hasConversionErrors)
						{
							importResult.Summary.Failed++;
							continue;
						}

						// Create element data
						var dataStoreElementData = new DataStoreElementData(currentRowData, new List<string> { "testgroup" });

						// Validate that we have data to insert
						if (currentRowData.Count == 0)
						{
							// Log the error
							importResult.Summary.Failed++;
							// Add the error to the result - try to identify the first mapped column for context
							var firstColumnMapping = excelColumnMappings.FirstOrDefault();
							importResult.Errors.Add(new ExcelImportError
							{
								RowNumber = currentRowIndex,
								Error = "Row contains no data to import",
								ColumnKey = firstColumnMapping?.ColumnKey,
								Value = "(no data)"
							});
							continue;
						}

						// Validate that we have at least one non-null value
						if (!currentRowData.Any(dataEntry => dataEntry.Value != null))
						{
							// Log the error
							importResult.Summary.Failed++;
							// Add the error to the result - try to identify the first mapped column for context
							var firstColumnMapping = excelColumnMappings.FirstOrDefault();
							importResult.Errors.Add(new ExcelImportError
							{
								RowNumber = currentRowIndex,
								Error = "Row contains only null values",
								ColumnKey = firstColumnMapping?.ColumnKey,
								Value = "(all null)"
							});
							continue;
						}

						// Validate required fields - check if any mandatory fields are missing after conversion
						var requiredFieldErrors = new List<(string fieldName, string? value, string errorMessage)>();
						var mandatoryFields = dataSource.Fields.Where(field => field.Mandatory && field.FieldType != DataFieldType.VirtualField && !field.SystemField && !field.AutoGenerated).ToList();

						foreach (var mandatoryField in mandatoryFields)
						{
							// Check if the field has a value in currentRowData (after successful conversion)
							// Use case-insensitive comparison to handle field name casing differences
							var fieldDataEntry = currentRowData.FirstOrDefault(dataEntry =>
								string.Equals(dataEntry.Key, mandatoryField.Name, StringComparison.OrdinalIgnoreCase));
							var fieldValue = fieldDataEntry.Key != null ? fieldDataEntry.Value : null;

							if (fieldValue == null)
							{
								// Field is missing or null - get the original Excel cell value for error reporting
								var columnMapping = excelColumnMappings.FirstOrDefault(mapping =>
									string.Equals(mapping.ColumnKey, mandatoryField.Name, StringComparison.OrdinalIgnoreCase));
								var originalCellValue = "(not mapped)";

								if (columnMapping != null)
								{
									var excelColumnIndex = columnMapping.index + 1;
									var excelCell = excelWorksheet.Cell(currentRowIndex, excelColumnIndex);
									originalCellValue = excelCell.Value.ToString();

									// If the original cell has a value but it's not in currentRowData,
									// it means the conversion failed or the value was empty
									if (!string.IsNullOrWhiteSpace(originalCellValue))
									{
										originalCellValue = $"{originalCellValue} (failed to convert)";
									}
									else
									{
										originalCellValue = "(empty)";
									}
								}

								var errorMessage = $"Required field '{mandatoryField.Name}' cannot be empty";
								requiredFieldErrors.Add((mandatoryField.Name, originalCellValue, errorMessage));
							}
						}

						// If there are required field validation errors, skip this row
						if (requiredFieldErrors.Any())
						{
							importResult.Summary.Failed++;
							foreach (var (fieldName, cellValue, errorMessage) in requiredFieldErrors)
							{
								importResult.Errors.Add(new ExcelImportError
								{
									RowNumber = currentRowIndex,
									Error = errorMessage,
									ColumnKey = fieldName,
									Value = cellValue ?? "(empty)"
								});
							}
							continue;
						}

						// Remove virtual fields as they are handled differently
						var virtualFieldNames = dataSource.Fields
							.Where(dataField => dataField.FieldType == DataFieldType.VirtualField)
							.Select(dataField => dataField.Name)
							.ToList();

						// Remove virtual fields from the element data
						foreach (var virtualFieldName in virtualFieldNames)
						{
							// ReSharper disable once PossibleNullReferenceException
							dataStoreElementData.Values.Remove(virtualFieldName);
						}

						// Prepare values for data store
						dataStoreElementData.PrepareValuesForDataStore(dataSource.Fields);

						// Always check for duplicates (using pre-loaded unique set columns)
						// Get the values to match on
						var duplicateMatchValues = uniqueSetColumns
							.Where(columnKey => currentRowData.ContainsKey(columnKey) && currentRowData[columnKey] != null)
							.ToDictionary(columnKey => columnKey, columnKey => currentRowData[columnKey]);

						// Check for duplicates
						if (duplicateMatchValues.Any())
						{
							// Send progress update for duplicate checking
							if (processedRowsCount % 20 == 0) // Update every 20 rows during duplicate checking
							{
								await redisProgressService.PublishProgressUpdateAsync(
									userId,
									processId,
									processedRowsCount,
									totalRowCount - startRowIndex + 1,
									$"Checking for duplicates in row {currentRowIndex} of {totalRowCount}"
								);
							}

							// PERFORMANCE OPTIMIZATION: Create cache key for duplicate checking
							var cacheKey = string.Join("|", duplicateMatchValues.Select(matchValue => $"{matchValue.Key}:{matchValue.Value}"));
							string? existingElementId;

							// Check cache first
							if (duplicateCheckCache.TryGetValue(cacheKey, out existingElementId))
							{
								// Found in cache, use cached result
							}
							else
							{
								// Not in cache, perform database query
								var duplicateCheckQuery = new DataStoreQuery(new List<DataStoreQueryField> { new("Id") });
								var duplicateFilterGroup = new QueryFilterGroup();

								// Add filters
								foreach (var (columnKey, columnValue) in duplicateMatchValues)
								{
									if (columnValue != null)
									{
										duplicateFilterGroup.AddFilter(new EqualsFilter(new QueryFilterField(columnKey), columnValue));
									}
								}

								// Add filter group to query
								duplicateCheckQuery.WithFilter(duplicateFilterGroup);
								var existingElements = await dataSource.GetElementsAsync(duplicateCheckQuery);

								// Cache the result
								if (existingElements.Any())
								{
									existingElementId = existingElements.First().Id;
									duplicateCheckCache[cacheKey] = existingElementId;
								}
								else
								{
									duplicateCheckCache[cacheKey] = null!; // Cache "no duplicate found"
								}
							}

							// Check if we have any duplicates (using cached or fresh result)
							if (!string.IsNullOrEmpty(existingElementId))
							{
								switch (importParameters.ActionForDuplicates?.ToLower())
								{
									case "skip":
										importResult.Summary.Skipped++;
										continue;

									case "update":
										var updateElementData = new DataStoreElementData(existingElementId, currentRowData, fileUploadId: null);
										updateElementData.PrepareValuesForDataStore(dataSource.Fields);

										var operationOrigin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1,  currentUserEntity.DisplayName);
										var lookupDisplayQueryFields = dataSource.Fields.Where(dataField => dataField is { FieldType: DataFieldType.LookupField, LookupDisplayFieldId: not null })
											.Select(dataField => new DataStoreQueryField(dataField.Name + "." + dataField.LookupDisplayField?.Name, dataField.Name + ".Display"))
											.ToList();

										var elementUpdateResult = await dataSource.UpdateElementAsync(updateElementData, operationOrigin, lookupDisplayQueryFields);

										if (elementUpdateResult.ElementData != null)
										{
											importResult.Summary.Updated++;
										}
										else
										{
											Logger.Warning("Failed to update record with id {Id} in data source {DataSourceName}",
												existingElementId, dataSource.Name);
											importResult.Summary.Failed++;
										}
										continue;

									default: // "PerElement" or any other value
										importResult.Duplicates.Add(new ExcelDuplicateRecordDto
										{
											Record = currentRowData,
											RowIndex = currentRowIndex,
											ColumnKey = duplicateMatchValues.First().Key,
											Value = duplicateMatchValues.First().Value?.ToString(),
											Id = Guid.Parse(existingElementId)
										});
										importResult.Summary.Skipped++;
										continue;
								}
							}
						}

						// Create new record
						var elementCreateResult = await dataSource.CreateElementAsync(dataStoreElementData,
							new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 0, currentUserEntity.DisplayName));

						// Check if the create was successful
						if (elementCreateResult.ElementData != null)
						{
							// Update the summary
							importResult.Summary.Inserted++;
						}
						else
						{
							importResult.Summary.Failed++;
							importResult.Errors.Add(new ExcelImportError
							{
								RowNumber = currentRowIndex,
								Error = "Failed to create record",
								ColumnKey = lastProcessedColumnKey,
								Value = lastProcessedCellValue
							});
						}
					}
					catch (Exception rowProcessingException)
					{
						importResult.Summary.Failed++;
						importResult.Errors.Add(new ExcelImportError
						{
							RowNumber = currentRowIndex,
							Error = rowProcessingException.Message.Replace("'", ""),
							ColumnKey = lastProcessedColumnKey,
							Value = lastProcessedCellValue
						});
					}

					processedRowsCount++;
					// Update progress every 5 rows for better responsiveness during duplicate checking
					if (processedRowsCount % 5 == 0) // Update progress every 5 rows
					{
						await redisProgressService.PublishProgressUpdateAsync(
							userId,
							processId,
							processedRowsCount,
							totalRowCount - startRowIndex + 1,
							$"Processing row {currentRowIndex} of {totalRowCount}"
						);
					}
				}

				// Include the entity ID in the response
				importResult.FileEntityId = fileUploadEntity.Id.ToString();

				// Update the file upload entity with final statistics
				fileUploadEntity.Updated = importResult.Summary.Updated;
				fileUploadEntity.Imported = importResult.Summary.Inserted;
				fileUploadEntity.Skipped = importResult.Summary.Skipped;
				fileUploadEntity.Failed = importResult.Summary.Failed;
				fileUploadEntity.Status = importResult.Summary.Failed > 0
					? "Completed with errors"
					: "Completed successfully";

				// Save the updated entity
				await _databaseContext.SaveChangesAsync();
				Logger.Information("Updated file upload entity with final statistics: Updated={Updated}, Imported={Imported}, Skipped={Skipped}, Failed={Failed}, Status={Status}",
					fileUploadEntity.Updated, fileUploadEntity.Imported, fileUploadEntity.Skipped, fileUploadEntity.Failed, fileUploadEntity.Status);

				// Send final progress update to show 100% completion
				await redisProgressService.PublishProgressUpdateAsync(
					userId,
					processId,
					totalRowCount - startRowIndex + 1,
					totalRowCount - startRowIndex + 1,
					"Processing completed"
				);

				// Generate Excel file with errors if there are any
				if (importResult.Errors.Count > 0)
				{
					// Create Excel workbook for errors
					using var errorExcelWorkbook = new XLWorkbook();
					var errorExcelWorksheet = errorExcelWorkbook.Worksheets.Add("Import Errors");

					// Add headers
					errorExcelWorksheet.Cell(1, 2).Value = "Element";
					errorExcelWorksheet.Cell(1, 1).Value = "Line Number";
					errorExcelWorksheet.Cell(1, 3).Value = "Value";
					errorExcelWorksheet.Cell(1, 4).Value = "Error Description";

					// Format headers
					var errorHeaderRow = errorExcelWorksheet.Row(1);
					errorHeaderRow.Style.Font.Bold = true;

					// Add data rows
					var errorRowIndex = 2;
					foreach (var importError in importResult.Errors)
					{
						errorExcelWorksheet.Cell(errorRowIndex, 2).Value = importError.ColumnKey ?? string.Empty;
						errorExcelWorksheet.Cell(errorRowIndex, 1).Value = importError.RowNumber;
						errorExcelWorksheet.Cell(errorRowIndex, 3).Value = importError.Value ?? string.Empty;
						errorExcelWorksheet.Cell(errorRowIndex, 4).Value = importError.Error;
						errorRowIndex++;
					}

					// Adjust column widths
					errorExcelWorksheet.Columns().AdjustToContents();

					// Convert to bytes
					using var errorMemoryStream = new MemoryStream();
					errorExcelWorkbook.SaveAs(errorMemoryStream);
					var errorFileBytes = errorMemoryStream.ToArray();

					// Convert to Base64 for sending in the response
					importResult.ErrorsExcelBase64 = Convert.ToBase64String(errorFileBytes);
				}

				// Send completion message for all successful imports
				// The frontend will handle duplicates based on the result data
				await redisProgressService.PublishCompletionMessageAsync(
					userId,
					processId,
					true,
					importResult.Duplicates.Count > 0
						? "Import completed - duplicates found"
						: "Import completed successfully",
					new
					{
						result = importResult,
						fileEntityId = fileUploadEntity.Id.ToString(),
						hasDuplicates = importResult.Duplicates.Count > 0
					}
				);

				return GetOkResponse(importResult);
			}
			catch (Exception importProcessingException)
			{
				Logger.Error(importProcessingException, "Error during import process");

				// Update the fileUploadEntity with error status
				fileUploadEntity.Status = "Failed: " + importProcessingException.Message;
				await _databaseContext.SaveChangesAsync();
				Logger.Information("Updated file upload entity with error status: {Status}", fileUploadEntity.Status);

				await redisProgressService.PublishErrorMessageAsync(
					userId,
					processId,
					"Import failed: " + importProcessingException.Message
				);
				return StatusCode(400, new { error = "Import failed", message = importProcessingException.Message });
			}
		}
		catch (Exception generalImportException)
		{
			Logger.Error(generalImportException, "Error during import process");

			await redisProgressService.PublishErrorMessageAsync(
				userId,
				processId,
				"Import failed: " + generalImportException.Message
			);
			return StatusCode(400, new { error = "Import failed", message = generalImportException.Message });
		}
	}

	/// <summary>
	/// Updates duplicate records from Excel import
	/// </summary>
	/// <param name="redisProgressService">Redis progress service</param>
	/// <param name="updateDuplicatesData">DTO containing the duplicates to update</param>
	[HttpPost("/Api/PageViews/ExcelImport/UpdateDuplicate")]
	public async Task<ActionResult<FrontendResponse>> UpdateDuplicates([FromServices] IRedisProgressService redisProgressService, [FromBody] ExcelUpdateDuplicatesDto updateDuplicatesData)
	{
		try
		{
			// Get the process ID from headers for Redis progress updates
			var processId = Request.Headers["X-Process-ID"].FirstOrDefault();

			// Check if process ID is provided
			if (string.IsNullOrEmpty(processId))
			{
				return BadRequest("Process ID is required");
			}

			// Get current user for Redis progress service
			var currentUser = await _userManager.GetCurrentUserAsync();
			var userId = currentUser.Id;

			var dataSource = await _databaseContext.DataSources
				.Include(dataSource => dataSource.Fields).ThenInclude(dataField => dataField.LookupDisplayField)
				.Include(dataSource => dataSource.Fields).ThenInclude(dataField => dataField.VirtualLookupField)
				.FirstOrDefaultAsync(dataSourceEntity => dataSourceEntity.Id == updateDuplicatesData.DataSourceId);

			_ = dataSource ?? throw new ElementNotFoundException($"DataSource with id: {updateDuplicatesData.DataSourceId} could not be found");

			var updateResult = new ExcelUpdateDuplicatesResultDto
			{
				Summary = new ExcelImportSummaryDto()
			};

			// Get the file upload entity if fileEntityId is provided
			FileUploadEntity? fileUploadEntity = null;
			if (!string.IsNullOrEmpty(updateDuplicatesData.FileEntityId))
			{
				fileUploadEntity = await _databaseContext.FileUploads.FindAsync(Guid.Parse(updateDuplicatesData.FileEntityId));
			}

			// Send initial progress update
			var totalDuplicates = updateDuplicatesData.Duplicates.Count;
			await redisProgressService.PublishProgressUpdateAsync(userId, processId, 0, totalDuplicates, "Starting duplicate processing...");

			var processedCount = 0;
			foreach (var duplicateRecord in updateDuplicatesData.Duplicates)
			{
				if (duplicateRecord.Id == null)
				{
					updateResult.Summary.Skipped++;
					processedCount++;

					// Send progress update
					await redisProgressService.PublishProgressUpdateAsync(userId, processId, processedCount, totalDuplicates, $"Processing duplicate {processedCount} of {totalDuplicates}");
					continue;
				}

				var existingDataElement = await dataSource.GetElementAsync(duplicateRecord.Id.Value.ToString());
				if (existingDataElement == null)
				{
					Logger.Warning("Record with id {Id} not found in data source {DataSourceName}",
						duplicateRecord.Id, dataSource.Name);
					updateResult.Summary.Failed++;
					processedCount++;

					// Send progress update
					await redisProgressService.PublishProgressUpdateAsync(userId, processId, processedCount, totalDuplicates, $"Processing duplicate {processedCount} of {totalDuplicates}");
					continue;
				}

				var duplicateRowData = duplicateRecord.Record;
				var updateElementData = new DataStoreElementData(existingDataElement.Id, duplicateRowData, fileUploadId: null);
				// Note: currentUser already retrieved above
				updateElementData.PrepareValuesForDataStore(dataSource.Fields);

				var operationOrigin = new DataStoreOperationOrigin(DataStoreOperationOriginType.User, 1, currentUser.DisplayName);
				var lookupDisplayQueryFields = dataSource.Fields.Where(dataField => dataField is { FieldType: DataFieldType.LookupField, LookupDisplayFieldId: not null })
					.Select(dataField => new DataStoreQueryField(dataField.Name + "." + dataField.LookupDisplayField?.Name, dataField.Name + ".Display"))
					.ToList();

				var elementUpdateResult = await dataSource.UpdateElementAsync(updateElementData, operationOrigin, lookupDisplayQueryFields);

				if (elementUpdateResult.ElementData != null)
				{
					updateResult.Summary.Updated++;
					// If we have a file entity, update its statistics
					if (fileUploadEntity != null)
					{
						fileUploadEntity.Skipped--; // Reduce skipped count as we're processing this item
						fileUploadEntity.Updated++; // Increase updated count
					}
				}
				else
				{
					Logger.Warning("Failed to update record with id {Id} in data source {DataSourceName}",
						duplicateRecord.Id, dataSource.Name);
					updateResult.Summary.Failed++;
					// If we have a file entity, update its statistics
					if (fileUploadEntity != null)
					{
						fileUploadEntity.Skipped--; // Reduce skipped count as we're processing this item
						fileUploadEntity.Failed++; // Increase failed count
					}
				}

				processedCount++;

				// Send progress update
				await redisProgressService.PublishProgressUpdateAsync(userId, processId, processedCount, totalDuplicates, $"Processing duplicate {processedCount} of {totalDuplicates}");
			}

			// If we have a file entity, update its status and save changes
			if (fileUploadEntity != null)
			{
				fileUploadEntity.Status = updateResult.Summary.Failed > 0
					? "Completed with errors"
					: "Completed successfully";
				await _databaseContext.SaveChangesAsync();
			}

			// Send completion message
			await redisProgressService.PublishCompletionMessageAsync(userId, processId, true, "Duplicate processing completed");

			return GetOkResponse(updateResult);
		}
		catch (Exception updateException)
		{
			Logger.Error(updateException, "Error during update process");
			return StatusCode(400, new { error = "Update failed", message = updateException.Message });
		}
	}

	private DataStoreQueryField ResolveVirtualFieldForExport(DataSourceEntity dataSource, string fieldKey)
	{
		// Check if this field key corresponds to a virtual field
		var virtualField = dataSource.Fields.FirstOrDefault(field => 
			field.FieldType == DataFieldType.VirtualField && 
			field.Name == fieldKey && 
			field.HasVirtualData && 
			!string.IsNullOrEmpty(field.VirtualDataStoreQueryName));

		if (virtualField != null)
		{
			// Return the virtual field with its resolved query name but keep the original alias
			return new DataStoreQueryField(virtualField.VirtualDataStoreQueryName!, fieldKey);
		}

		// For non-virtual fields, return as-is
		return new DataStoreQueryField(fieldKey);
	}

	private string GetLocalizedHeaderName(string name)
	{
		if (string.IsNullOrEmpty(name))
			return string.Empty;

		// Create a localizer for field headers using database-based localization like table component
		var fieldLocalizer = _stringLocalizerFactory.Create("ListViewColumn", "", true);

		// Try to get localized value first
		var localizedValue = fieldLocalizer[name];

		// If the localized value is the same as the key, it means no translation was found
		// In that case, fall back to the formatted name
		if (localizedValue == name)
		{
			return FormatHeaderName(name);
		}

		return localizedValue;
	}

	private static string FormatHeaderName(string name)
	{
		if (string.IsNullOrEmpty(name))
			return string.Empty;

		// Split by dots for nested properties (e.g., "User.Name")
		var parts = name.Split('.');

		// Format each part
		var formattedParts = parts.Select(part =>
		{
			// Replace underscores with spaces
			part = part.Replace('_', ' ');

			// Capitalize first letter of each word
			return CultureInfo.CurrentCulture.TextInfo.ToTitleCase(part.ToLower());
		});

		// Join back with dots
		return string.Join(".", formattedParts);
	}

	/// <summary>
	/// Parses a date value from Excel import with enhanced handling for various date formats
	/// </summary>
	/// <param name="cellValue">The cell value to parse</param>
	/// <param name="dateOnly">Whether to return date only (without time) or full datetime</param>
	/// <returns>Parsed DateTime object or null if parsing fails</returns>
	private static DateTime? ParseDateValue(string cellValue, bool dateOnly)
	{
		if (string.IsNullOrWhiteSpace(cellValue))
			return null;

		// Try standard DateTime parsing first
		if (DateTime.TryParse(cellValue, out var standardDate))
		{
			return dateOnly ? standardDate.Date : standardDate;
		}

		// Try parsing as Excel serial number (common when Excel exports dates as numbers)
		if (double.TryParse(cellValue, out var serialNumber))
		{
			try
			{
				// Excel serial date conversion
				// Excel's epoch starts at 1900-01-01, but Excel incorrectly treats 1900 as a leap year
				var excelEpoch = new DateTime(1900, 1, 1);

				// Handle Excel's leap year bug (day 60 = Feb 29, 1900 which doesn't exist)
				if (serialNumber >= 60)
					serialNumber -= 1;

				var convertedDate = excelEpoch.AddDays(serialNumber - 1);

				// Validate that the converted date is reasonable (between 1900 and 2100)
				if (convertedDate.Year >= 1900 && convertedDate.Year <= 2100)
				{
					return dateOnly ? convertedDate.Date : convertedDate;
				}
			}
			catch
			{
				// If Excel serial conversion fails, continue to other methods
			}
		}

		// Try parsing with specific culture-invariant formats commonly used in Excel
		var dateFormats = new[]
		{
			"yyyy-MM-dd",
			"yyyy-MM-dd HH:mm:ss",
			"yyyy-MM-dd HH:mm",
			"yyyy-MM-dd hh:mm:ss tt",
			"yyyy-MM-dd hh:mm tt",
			"yyyy-MM-dd h:mm:ss tt",
			"yyyy-MM-dd h:mm tt",
			"MM/dd/yyyy",
			"MM/dd/yyyy HH:mm:ss",
			"MM/dd/yyyy HH:mm",
			"MM/dd/yyyy hh:mm:ss tt",
			"MM/dd/yyyy hh:mm tt",
			"MM/dd/yyyy h:mm:ss tt",
			"MM/dd/yyyy h:mm tt",
			"dd/MM/yyyy",
			"dd/MM/yyyy HH:mm:ss",
			"dd/MM/yyyy HH:mm",
			"dd/MM/yyyy hh:mm:ss tt",
			"dd/MM/yyyy hh:mm tt",
			"dd/MM/yyyy h:mm:ss tt",
			"dd/MM/yyyy h:mm tt",
			"yyyy/MM/dd",
			"yyyy/MM/dd HH:mm:ss",
			"yyyy/MM/dd HH:mm",
			"yyyy/MM/dd hh:mm:ss tt",
			"yyyy/MM/dd hh:mm tt",
			"yyyy/MM/dd h:mm:ss tt",
			"yyyy/MM/dd h:mm tt",
			"dd-MM-yyyy",
			"dd-MM-yyyy HH:mm:ss",
			"dd-MM-yyyy HH:mm",
			"dd-MM-yyyy hh:mm:ss tt",
			"dd-MM-yyyy hh:mm tt",
			"dd-MM-yyyy h:mm:ss tt",
			"dd-MM-yyyy h:mm tt",
			"dd.MM.yyyy HH:mm:ss",
			"dd-MM-yyyy h:mm tt",
			"dd.MM.yyyy HH:mm:ss",
			"dd.MM.yyyy HH:mm",
			"dd.MM.yyyy hh:mm:ss tt",
			"dd.MM.yyyy hh:mm tt",
			"dd.MM.yyyy h:mm:ss tt",
			"dd.MM.yyyy h:mm tt"
		};

		foreach (var format in dateFormats)
		{
			if (DateTime.TryParseExact(cellValue, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedDate))
			{
				return dateOnly ? parsedDate.Date : parsedDate;
			}
		}

		// If all parsing attempts fail, return null
		return null;
	}

	/// <summary>
	/// Converts a cell value to integer with descriptive error messages
	/// </summary>
	private static int? ConvertToInteger(string? cellValue, string columnKey)
	{
		if (string.IsNullOrWhiteSpace(cellValue))
			return null;

		if (int.TryParse(cellValue, out var integerValue))
			return integerValue;

		throw new ArgumentException($"Cannot convert '{cellValue}' to integer for column '{columnKey}'. Please ensure the value is a whole number.");
	}

	/// <summary>
	/// Converts a cell value to long with descriptive error messages
	/// </summary>
	private static long? ConvertToLong(string? cellValue, string columnKey)
	{
		if (string.IsNullOrWhiteSpace(cellValue))
			return null;

		if (long.TryParse(cellValue, out var longValue))
			return longValue;

		throw new ArgumentException($"Cannot convert '{cellValue}' to long integer for column '{columnKey}'. Please ensure the value is a whole number.");
	}

	/// <summary>
	/// Converts a cell value to double with descriptive error messages
	/// </summary>
	private static double? ConvertToDouble(string? cellValue, string columnKey)
	{
		if (string.IsNullOrWhiteSpace(cellValue))
			return null;

		if (double.TryParse(cellValue, out var doubleValue))
			return doubleValue;

		throw new ArgumentException($"Cannot convert '{cellValue}' to decimal number for column '{columnKey}'. Please ensure the value is a valid number.");
	}

	/// <summary>
	/// Converts a cell value to date/datetime with descriptive error messages
	/// </summary>
	private static DateTime? ConvertToDate(string? cellValue, string columnKey, bool dateOnly)
	{
		if (string.IsNullOrWhiteSpace(cellValue))
			return null;

		var result = ParseDateValue(cellValue, dateOnly);
		if (result.HasValue)
			return result;

		var expectedFormat = dateOnly ? "date (e.g., YYYY.MM.DD or MM/DD/YYYY)" : "date and time (e.g., YYYY.MM.DD HH:MM:SS or MM/DD/YYYY HH:MM AM/PM)";
		throw new ArgumentException($"Cannot convert '{cellValue}' to {expectedFormat} for column '{columnKey}'. Please ensure the value is in a valid date format.");
	}

	/// <summary>
	/// Converts a cell value to time with descriptive error messages
	/// </summary>
	private static TimeSpan? ConvertToTime(string? cellValue, string columnKey)
	{
		if (string.IsNullOrWhiteSpace(cellValue))
			return null;

		if (TimeSpan.TryParse(cellValue, out var timeSpanValue))
			return timeSpanValue;

		if (DateTime.TryParse(cellValue, out var dateTimeValue))
			return dateTimeValue.TimeOfDay;

		throw new ArgumentException($"Cannot convert '{cellValue}' to time for column '{columnKey}'. Please ensure the value is in a valid time format (e.g., HH:MM:SS or HH:MM AM/PM).");
	}

	/// <summary>
	/// Converts a cell value to boolean with descriptive error messages
	/// </summary>
	private static bool? ConvertToBoolean(string? cellValue, string columnKey)
	{
		if (string.IsNullOrWhiteSpace(cellValue))
			return null;

		if (bool.TryParse(cellValue.ToString(), out var boolValue))
			return boolValue;

		var lowerValue = cellValue.ToLower().Trim();
		if (lowerValue == "yes" || lowerValue == "y" || lowerValue == "1")
			return true;
		if (lowerValue == "no" || lowerValue == "n" || lowerValue == "0")
			return false;

		throw new ArgumentException($"Cannot convert '{cellValue}' to true/false for column '{columnKey}'. Please use values like: true/false, yes/no, 1/0.");
	}

}
