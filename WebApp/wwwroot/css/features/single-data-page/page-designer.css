#content-buttons {
	transition: opacity 150ms ease-in;
}

#content-buttons.hiddenByEditPanel {
	opacity: 0;
	pointer-events: none;
}

.page-designer__section {
	border: 1px solid var(--clr-border-medium);
	border-radius: var(--size-radius-m);
	background-color: var(--clr-background-lvl-1);
	padding: var(--size-spacing-m) var(--size-spacing-l);
	display: flex;
	flex-direction: column;
	transition: margin 100ms ease-in-out;
	position: relative;
}

.page-designer__section[data-type=EmbeddedPage] {
	background-color: var(--clr-background-lvl-2);
}

.page-designer__section.resetMoveDown {
	transition: none;
	margin-top: 0;
}

.page-designer__section.moveDown {
	margin-top: 6.5rem;
}

.page-designer__section:last-child.moveDown,
.page-designer__section.moveDown ~ .page-designer__section:last-child {
	margin-bottom: -6.5rem;
}

.page-designer__section.embedded {
	padding: var(--size-spacing-m) var(--size-spacing-l) var(--size-spacing-m);
}

.page-designer__section > label {
	font-size: var(--size-text-l);
	line-height: 40px;
	color: var(--clr-text-secondary);
	display: block;
	flex-grow: 0;
	cursor: inherit;
}

.page-designer__section[data-type=Section] > label {
	border-bottom: 1px solid var(--clr-border-medium);
	margin-bottom: var(--size-spacing-m);
}

.page-designer__section[data-type=EmbeddedPage] > label:before,
.columnDragElement[data-type=EmbeddedPage] > label:before {
	content: "\f12e";
	font-family: "Font Awesome 6 Pro";
	font-weight: 300;
	margin-right: var(--size-spacing-s);
}

.page-designer__section.embedded > label {
	border-bottom: none;
}

.grid-element {
	display: grid;
	align-items: center;
}

lvl-page-designer section > label {
	user-select: none;
}

lvl-page-designer section {
	position: relative;
	transition: margin 100ms ease-in-out, max-height 100ms ease-in-out;
	cursor: pointer;
}

lvl-page-designer section.dragActive {
	position: absolute;
	pointer-events: none;
	opacity: 0;
}

.dragging {
	opacity: 0.8;
	z-index: 9999;
	transition: opacity 50ms ease-in-out;
}

.dragElement {
	position: absolute;
	border: 1px dashed #71717A;
	border-radius: var(--size-radius-m);
	background-color: rgba(250, 250, 250, 0.7);
	top: calc(var(--top) * 1px);
	left: calc(var(--left) * 1px);
	transform: translate(calc(var(--dragX) * 1px), calc(var(--dragY) * 1px));
	color: #27272A;
	cursor: move;
}

.dragElement.Headline1 {
	font-size: var(--size-text-l);
}

.dragElement.Headline2 {
	font-size: 1.4rem;
}

.dragElement.Separator {
	font-size: 0;
}

.dragElement.Separator:before {
	position: absolute;
	content: "";
	width: calc(100% - 16px);
	border-bottom: 1px solid #CCCCCC;
	top: 16px;
}

.gridDragElement {
	height: 64px;
	font-size: var(--size-text-m);
	transition: opacity 50ms ease-in-out;
	background-color: var(--clr-background-lvl-1);
}

.gridDragElement.doubleHeight {
	height: 136px;
}

.inputPreview {
	display: block;
	padding: 0.3rem 0.5rem;
	font-size: 0;
	line-height: 0;
	height: 100%;
	--clr-state-readonly: var(--clr-background-lvl-2);
}

/*prevent clicks on actual input */
.inputPreview:after {
	content: "";
	position: absolute;
	inset: 0;
}

.dragElement .inputPreview:after {
	z-index: 999;
}

.inputPreview > lvl-textarea {
	height: 100%;
}

lvl-grid-element[selected] .inputPreview:after {
	z-index: 2;
}

.annotationPreview {
	height: 100%;
	font-size: var(--size-text-xl);
	color: var(--clr-text-tertiary);
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: var(--clr-background-lvl-2);
	border-radius: var(--size-radius-m);
	border: 1px solid var(--clr-border-medium);
	position: relative;

	& i.disconnected {
		font-size: 1.4rem;
		line-height: 1.4rem;
		font-family: "Font Awesome 6 Pro";
		font-style: normal;
		font-weight: 300;
		color: var(--clr-signal-error);
		position: absolute;
		bottom: var(--size-spacing-m);
		right: var(--size-spacing-m);
		z-index: 1;
	}

	& i.disconnected::before {
		content: "\f1e6";
	}
}

.pagePreview {
	display: block;
	margin: 0.35rem 0.5rem;
	padding: var(--size-spacing-m) var(--size-spacing-l);
	font-size: 0;
	line-height: 0;
	border: 1px solid var(--clr-border-medium);
	border-radius: var(--size-radius-m);
	background-color: var(--clr-background-lvl-2);
	user-select: none;
}

.pagePreview > label {
	font-size: var(--size-text-l);
	line-height:38px;
	color: var(--clr-text-secondary);
	display: block;
	flex-grow: 0;
	cursor: inherit;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.pagePreview:not(.tilePreview) > label:before {
	content: "\f12e";
	font-family: "Font Awesome 6 Pro";
	font-weight: 300;
	margin-right: var(--size-spacing-s);
}

.tilePreview > label:before {
	content: "\f360";
	font-family: "Font Awesome 6 Pro";
	font-weight: 300;
	margin-right: var(--size-spacing-s);
}

.gridDragElement.existingElement {
	border: 1px solid var(--clr-state-active);
	outline: 1px solid var(--clr-state-active);
	border-top-right-radius: 0;
}

.gridDragElement .element-flag {
	display: block;
}

.gridDragElement .element-flag > i {
	display: inline-block;
	margin-left: 0.5rem;
}

.gridDragElement.snapped {
	opacity: 1;
	border: 1px solid var(--clr-state-active);
	outline: 1px solid var(--clr-state-active);
	width: var(--widthPreview) !important;
	transition: transform 50ms ease-in-out, width 50ms ease-in-out, opacity 50ms ease-in-out;
}

.columnDragElement {
	position: absolute;
	width: 498px;
	height: 56px;
	padding: var(--size-spacing-m) var(--size-spacing-l);
	border: 1px dashed #71717A;
	border-radius: var(--size-radius-m);
	background-color: var(--clr-background-lvl-1);
	top: calc(var(--top) * 1px);
	left: calc(var(--left) * 1px);
	transform: translate(calc(var(--dragX) * 1px), calc(var(--dragY) * 1px));
	font-size: var(--size-text-l);
	line-height: 40px;
	color: var(--clr-text-secondary);
	user-select: none;
}

.columnDragElement.existingElement {
	border: 1px solid var(--clr-state-active);
	outline: 1px solid var(--clr-state-active);
	border-top-right-radius: 0;
}

/* pages embedded into a section have a special background color */
.columnDragElement.gridDragElement {
	padding: 0;
}

.columnDragElement.gridDragElement:not(.snappedToGrid) > .pagePreview {
	margin: 0;
	border-width: 0;
}

.columnDragElement.gridDragElement.snappedToGrid {
	height: 64px;
}

.columnDragElement.snapped {
	opacity: 1;
	width: var(--widthPreview) !important;
	transition: transform 50ms ease-in-out, width 50ms ease-in-out, opacity 50ms ease-in-out;
}

.columnDragElement.resetPosition {
	transition: transform 100ms ease-in-out, opacity 100ms ease-in-out;
	opacity: 0;
}

.dummyElement {
	width: 100%;
	height: 56px;
}

.columnDragElement > label {
	pointer-events: none;
}

.columnDragElement.allowed {
	opacity: 1;
}

.draggableElement.resetPosition,
.gridDragElement.resetPosition {
	transition: transform 100ms ease-in-out;
}

.draggableElement.noDrop {
	cursor: no-drop !important;
}

.page-designer__section.selected {
	border-color: var(--clr-state-active);
	outline: 1px solid var(--clr-state-active);
	border-top-right-radius: 0;
}

@keyframes skeleton-gradient {
	 0% {
		 background-position: right;
	 }

	 100% {
		 background-position: left;
	 }
 }

@keyframes skeleton-fade {
	 0%, 100% {
		 opacity: 0.25;
	 }

	 50% {
		 opacity: 1;
	 }
 }

:host, :root {
	--clr-skeleton-block: var(--clr-background-lvl-2);
	--clr-skeleton-text: var(--clr-background-lvl-2);
	--skeleton-animation-speed: 3s;
}

.skeleton__block, .skeleton__text {
	display: block;
	position: relative;
	overflow: hidden;
	color: transparent;
	background-color: transparent !important;
	border-color: var(--clr-background-lvl-4);
}

.skeleton__block * {
	opacity: 0 !important;
	user-select: none;
}

.skeleton__block *::selection,
.skeleton__block *::placeholder {
	color: transparent
}

.skeleton__block lvl-input-icon,
.skeleton__block lvl-input-button {
	opacity: 0;
}

.skeleton__block:before {
	content: '';
	position: absolute;
	inset: var(--skeleton-inset, 0);
	background-color: var(--clr-skeleton-block);
	border-radius: var(--size-radius-s);
	animation: skeleton-fade var(--skeleton-animation-speed, 0) linear;
	animation-iteration-count: infinite;
	cursor: default;
	z-index: 9999;
}

.skeleton__text:before {
	content: '';
	position: absolute;
	inset: 0;
	background-image: linear-gradient(90deg,
	var(--clr-skeleton-text) 0%,
	var(--clr-skeleton-text) 35%,
	transparent,
	var(--clr-skeleton-text) 65%,
	var(--clr-skeleton-text) 100%);
	background-size: 300%;
	animation: skeleton-gradient var(--skeleton-animation-speed, 0) linear;
	animation-iteration-count: infinite;
}

.section-flag,
.element-flag {
	position: absolute;
	top: -22px;
	right: -1px;
	background-color: var(--clr-state-active);
	color: #FFF;
	font-size: var(--size-text-m);
	line-height: 20px;
	outline: 1px solid var(--clr-state-active);
	border-top-left-radius: 4px;
	border-top-right-radius: 4px;
	padding: 0 0.5rem;
	display: none;
	user-select: none;
	white-space: nowrap;
	max-width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
	cursor: move;
}

.section-flag > i {
	margin-left: 0.5rem;
}

.columnDragElement .section-flag {
	display: block;
	padding-bottom: 0.1rem;
}

.columnDragElement > label {
	color: var(--clr-text-secondary);
}

section.selected > .section-flag {
	display: block;
}

:host-context([selected]) .element-flag {
	display: block;
}

.element-flag > i {
	display: none;
}

.page-designer__text {
	display: block;
	padding: 0.3rem 0.7rem;
	border-radius: var(--size-radius-m);
	cursor: pointer;
	position: relative;
	height: 100%;
	overflow: hidden;
	user-select: none;
}

.page-designer__text > label {
	cursor: pointer;
	white-space: nowrap;
	overflow: hidden;
}

.page-designer__text.Headline1 {
	font-size: var(--size-text-l);
	line-height: 56px;
}

.page-designer__text.Headline2 {
	font-size: 1.4rem;
	font-weight: 600;
	line-height: 58px;
}

.page-designer__text.Headline3 {
	font-size: var(--size-text-m);
	font-weight: 600;
	line-height: 60px;
}

.page-designer__text label {
	display: block;
	user-select: none;
	text-overflow: ellipsis;
}

.page-designer__text.empty,
.page-designer__text.empty label {
	color: var(--clr-text-secondary);
}

.page-designer__text.Headline1 > label {
	border-bottom: 1px solid #27272A;
}

.page-designer__text.Headline1.empty > label {
	border-bottom-color: var(--clr-border-medium);
}

.page-designer__separator {
	position: absolute;
	top: 50%;
	width: calc(100% - 10px);
	margin: 0 0.5rem;
	border: none;
	border-top: 1px solid var(--clr-border-medium);
}

.page-designer__section lvl-grid {
	margin: var(--size-spacing-m) calc(var(--size-spacing-m) * -1px) 0;
}