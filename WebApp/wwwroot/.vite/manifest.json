{"_chunks-DFXgfaXN.js": {"file": "js/vendor/chunks-DFXgfaXN.js", "name": "js/vendor/chunks", "isDynamicEntry": true, "imports": ["main.ts"]}, "_date-fns-CIlE6naG.js": {"file": "js/vendor/date-fns-CIlE6naG.js", "name": "js/vendor/date-fns"}, "_font-awesome-CqXYKreN.js": {"file": "js/vendor/font-awesome-CqXYKreN.js", "name": "js/vendor/font-awesome", "imports": ["_lit-BDEraP0y.js"]}, "_iconCategoryLibrary-BoKlFF-e.js": {"file": "js/asset-chunks/iconCategoryLibrary-BoKlFF-e.js", "name": "js/asset-chunks/iconCategoryLibrary"}, "_iconLibrary-BS8x7Ufj.js": {"file": "js/asset-chunks/iconLibrary-BS8x7Ufj.js", "name": "js/asset-chunks/iconLibrary"}, "_lit-BDEraP0y.js": {"file": "js/vendor/lit-BDEraP0y.js", "name": "js/vendor/lit"}, "_prose-mirror-D0FHoUV3.js": {"file": "js/vendor/prose-mirror-D0FHoUV3.js", "name": "js/vendor/prose-mirror", "imports": ["_chunks-DFXgfaXN.js"]}, "_tip-tap-B1HCRBLN.js": {"file": "js/vendor/tip-tap-B1HCRBLN.js", "name": "js/vendor/tip-tap", "imports": ["_prose-mirror-D0FHoUV3.js", "_chunks-DFXgfaXN.js"]}, "main.ts": {"file": "js/main-CUwCIWQv.js", "name": "main", "src": "main.ts", "isEntry": true, "imports": ["_lit-BDEraP0y.js", "_font-awesome-CqXYKreN.js", "_chunks-DFXgfaXN.js", "_tip-tap-B1HCRBLN.js", "_date-fns-CIlE6naG.js", "_iconLibrary-BS8x7Ufj.js", "_iconCategoryLibrary-BoKlFF-e.js", "main.ts", "_prose-mirror-D0FHoUV3.js"], "dynamicImports": ["_chunks-DFXgfaXN.js", "_chunks-DFXgfaXN.js", "src/features/data-field/utils.ts", "src/features/data-store-config/configuration.ts", "src/features/page/visualization.ts"]}, "src/.modules/individual.ts": {"file": "js/components/individual-CtOALNVp.js", "name": "individual", "src": "src/.modules/individual.ts", "isEntry": true, "imports": ["_lit-BDEraP0y.js", "main.ts", "_font-awesome-CqXYKreN.js", "_chunks-DFXgfaXN.js", "_tip-tap-B1HCRBLN.js", "_prose-mirror-D0FHoUV3.js", "_date-fns-CIlE6naG.js", "_iconLibrary-BS8x7Ufj.js", "_iconCategoryLibrary-BoKlFF-e.js"]}, "src/features/data-field/utils.ts": {"file": "js/features/utils-5iKknebK.js", "name": "utils", "src": "src/features/data-field/utils.ts", "isEntry": true, "isDynamicEntry": true}, "src/features/data-store-config/configuration.ts": {"file": "js/features/configuration-BJqes5pi.js", "name": "configuration", "src": "src/features/data-store-config/configuration.ts", "isEntry": true, "isDynamicEntry": true}, "src/features/page/visualization.ts": {"file": "js/features/visualization-DnSOtrEq.js", "name": "visualization", "src": "src/features/page/visualization.ts", "isEntry": true, "isDynamicEntry": true}}