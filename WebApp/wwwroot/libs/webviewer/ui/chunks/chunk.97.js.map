{"version": 3, "sources": ["webpack:///./src/ui/src/components/ViewControlsOverlay/ViewControlsOverlay.js", "webpack:///./src/ui/src/components/ViewControlsOverlay/index.js"], "names": ["ViewControlsOverlay", "t", "useTranslation", "store", "useStore", "useSelector", "state", "selectors", "getTotalPages", "getDisplayMode", "isElementDisabled", "DataElements", "VIEW_CONTROLS_OVERLAY", "isReaderMode", "isMultiViewerMode", "isFullScreen", "getActiveDocumentViewerKey", "getIsMultiTab", "getIsMultiViewerModeAvailable", "totalPages", "displayMode", "isDisabled", "activeDocumentViewerKey", "isMultiTab", "isMultiViewerModeAvailable", "isPageTransitionEnabled", "documentViewer", "core", "getDocumentViewer", "displayModeManager", "getDisplayModeManager", "isVirtualDisplayEnabled", "pageTransition", "layout", "showCompareButton", "isIE11", "handleClick", "setDisplayMode", "displayModeObject", "displayModeObjects", "find", "obj", "exitReaderMode", "setTimeout", "showReaderButton", "isFullPDFEnabled", "getDocument", "getType", "FlyoutMenu", "menu", "trigger", "VIEW_CONTROLS_OVERLAY_BUTTON", "aria<PERSON><PERSON><PERSON>", "DataElementWrapper", "dataElement", "className", "classNames", "row", "active", "onClick", "<PERSON><PERSON>", "title", "img", "isActive", "role", "enterReaderMode", "rotateClockwise", "ActionButton", "rotateCounterClockwise", "dispatch", "actions", "setIsMultiViewerMode", "isIOS", "isIOSFullScreenSupported", "toggleFullscreen"], "mappings": "y2CA0ReA,MAzQf,WAA+B,MACtBC,EAAqB,EAAhBC,cAAgB,GAApB,GACFC,EAAQC,cAsBZ,IAVEC,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAUC,cAAcF,GACxBC,IAAUE,eAAeH,GACzBC,IAAUG,kBAAkBJ,EAAOK,IAAaC,uBAChDL,IAAUM,aAAaP,GACvBC,IAAUO,kBAAkBR,GAC5BC,IAAUQ,aAAaT,GACvBC,IAAUS,2BAA2BV,GACrCC,IAAUU,cAAcX,GACxBC,IAAUW,8BAA8BZ,OACxC,GAnBAa,EAAU,KACVC,EAAW,KACXC,EAAU,KACVR,EAAY,KACZC,EAAiB,KACjBC,EAAY,KACZO,EAAuB,KACvBC,EAAU,KACVC,EAA0B,KAcxBC,EAA0BN,EADH,IAGrBO,EAAiBC,IAAKC,oBACtBC,EAAqBH,aAAc,EAAdA,EAAgBI,wBACvCD,GAAsBA,EAAmBE,4BAC3CN,GAA0B,GAE5B,IAkCIO,EACAC,EAnCEC,GAAqBC,MAAWZ,GAAcC,EAK9CY,EAAc,SAACJ,EAAgBC,GACnC,IAAMI,EAAiB,WACrB,IAAMC,EAAoBC,IAAmBC,MAC3C,SAACC,GAAG,OAAKA,EAAIT,iBAAmBA,GAAkBS,EAAIR,SAAWA,KAEnEN,IAAKU,eAAeC,EAAkBlB,cAGpCP,GACF6B,YAAevC,GACfwC,YAAW,WACTN,QAGFA,KAWJ,GAAIhB,EACF,OAAO,KAMT,IAAMiB,EAAoBC,IAAmBC,MAAK,SAACC,GAAG,OAAKA,EAAIrB,cAAgBA,KAC3EkB,IACFN,EAAiBM,EAAkBN,eACnCC,EAASK,EAAkBL,QAG7B,IAAMW,EAAmBjB,IAAKkB,oBAAwD,SAAhB,QAAlB,EAAAlB,IAAKmB,qBAAa,aAAlB,EAAoBC,WA2BxE,OACE,kBAACC,EAAA,EAAU,CACTC,KAAMtC,IAAaC,sBACnBsC,QAASvC,IAAawC,6BACtBC,UAAWnD,EAAE,2BAEZwB,GACC,oCACE,kBAAC4B,EAAA,EAAkB,CACjBC,YAAY,uBACZC,UAAU,OACVH,UAAWnD,EAAE,sCAEZA,EAAE,sCAEL,kBAACoD,EAAA,EAAkB,CACjBE,UAAWC,IAAW,CAAEC,KAAK,EAAMC,OAA4B,eAAnB1B,IAAoCnB,IAChF8C,QAAS,kBAAMvB,EAAY,aAAcH,IACzCqB,YAAY,kCAEZ,kBAACM,EAAA,EAAM,CACLC,MAAM,mCACNC,IAAI,qEACJC,SAA6B,eAAnB/B,IAAoCnB,EAC9CmD,KAAK,WAEP,yBAAKT,UAAU,SAAStD,EAAE,sCAE5B,kBAACoD,EAAA,EAAkB,CACjBE,UAAWC,IAAW,CAAEC,KAAK,EAAMC,OAA4B,YAAnB1B,IAAiCnB,IAC7E8C,QAAS,kBAAMvB,EAAY,UAAWH,IACtCqB,YAAY,+BAEZ,kBAACM,EAAA,EAAM,CACLC,MAAM,gCACNC,IAAI,kEACJC,SAA6B,YAAnB/B,IAAiCnB,EAC3CmD,KAAK,WAEP,yBAAKT,UAAU,SAAStD,EAAE,mCAE3B2C,GACC,kBAACS,EAAA,EAAkB,CACjBE,UAAWC,IAAW,CAAEC,KAAK,EAAMC,OAAQ7C,IAC3C8C,QAAS,WA1Ff9C,GAGJoD,YAAgB9D,IAwFNmD,YAAY,8BAEZ,kBAACM,EAAA,EAAM,CACLC,MAAM,+BACNC,IAAI,uDACJC,SAAUlD,EACVmD,KAAK,WAEP,yBAAKT,UAAU,SAAStD,EAAE,mCAG5BY,GACA,kBAACwC,EAAA,EAAkB,CACjBC,YAAY,uBACZC,UAAU,cAKhB1C,GACA,oCACE,kBAACwC,EAAA,EAAkB,CACjBC,YAAY,eACZC,UAAU,OACVH,UAAWnD,EAAE,kBAEZA,EAAE,kBAEL,kBAACoD,EAAA,EAAkB,CAACE,UAAU,MAAMI,QAAS,kBAAMhC,IAAKuC,gBAAgB5C,IAA0BgC,YAAY,yBAC5G,kBAACa,EAAA,EAAY,CACXN,MAAM,yBACNC,IAAI,6DACJE,KAAK,WAEP,yBAAKT,UAAU,SAAStD,EAAE,4BAE5B,kBAACoD,EAAA,EAAkB,CAACE,UAAU,MAAMI,QAAS,kBAAMhC,IAAKyC,uBAAuB9C,IAA0BgC,YAAY,gCACnH,kBAACa,EAAA,EAAY,CACXN,MAAM,gCACNC,IAAI,oEACJE,KAAK,WAEP,yBAAKT,UAAU,SAAStD,EAAE,mCAE5B,kBAACoD,EAAA,EAAkB,CACjBC,YAAY,uBACZC,UAAU,YAEZ,kBAACF,EAAA,EAAkB,CACjBC,YAAY,eACZC,UAAU,OACVH,UAAWnD,EAAE,8BAEZA,EAAE,8BAEL,kBAACoD,EAAA,EAAkB,CACjBE,UAAWC,IAAW,CAAEC,KAAK,EAAMC,OAAmB,WAAXzB,IAC3C0B,QAAS,kBAAMvB,EAAYJ,EAAgB,WAC3CsB,YAAY,sBAEZ,kBAACM,EAAA,EAAM,CACLC,MAAM,uBACNC,IAAI,6DACJC,SAAqB,WAAX9B,EACV+B,KAAK,WAEP,yBAAKT,UAAU,SAAStD,EAAE,0BAE5B,kBAACoD,EAAA,EAAkB,CACjBE,UAAWC,IAAW,CAAEC,KAAK,EAAMC,OAAmB,WAAXzB,IAC3C0B,QAAS,kBAAMvB,EAAYJ,EAAgB,WAC3CsB,YAAY,sBAEZ,kBAACM,EAAA,EAAM,CACLC,MAAM,uBACNC,IAAI,6DACJC,SAAqB,WAAX9B,EACV+B,KAAK,WAEP,yBAAKT,UAAU,SAAStD,EAAE,0BAE5B,kBAACoD,EAAA,EAAkB,CACjBE,UAAWC,IAAW,CAAEC,KAAK,EAAMC,OAAmB,UAAXzB,IAC3C0B,QAAS,kBAAMvB,EAAYJ,EAAgB,UAC3CsB,YAAY,qBAEZ,kBAACM,EAAA,EAAM,CACLC,MAAM,sBACNC,IAAI,uDACJC,SAAqB,UAAX9B,EACV+B,KAAK,WAEP,yBAAKT,UAAU,SAAStD,EAAE,yBAE3BiC,GACC,kBAACmB,EAAA,EAAkB,CACjBE,UAAWC,IAAW,CAAEC,KAAK,EAAMC,OAAQ5C,IAC3C6C,QAnNc,WACxBxD,EAAMkE,SAASC,IAAQC,sBAAsBzD,KAmNnCwC,YAAY,2BAEZ,kBAACM,EAAA,EAAM,CACLC,MAAM,sBACNC,IAAI,sBACJC,SAAUjD,EACVkD,KAAK,WAEP,yBAAKT,UAAU,SAAStD,EAAE,4BA/KhCuE,MAAUC,IACL,KAGP,oCACE,kBAACpB,EAAA,EAAkB,CACjBC,YAAY,uBACZC,UAAU,YAEZ,kBAACF,EAAA,EAAkB,CACjBE,UAAU,MACVI,QAASe,IACTpB,YAAY,oBAEZ,kBAACM,EAAA,EAAM,CACLE,IAAK/C,EAAe,+BAAiC,0BACrDiD,KAAK,WAEP,yBAAKT,UAAU,SAAwBtD,EAAfc,EAAiB,wBAA6B,+BCjHjEf", "file": "chunks/chunk.97.js", "sourcesContent": ["import classNames from 'classnames';\nimport ActionButton from 'components/ActionButton';\nimport Button from 'components/Button';\nimport displayModeObjects from 'constants/displayModeObjects';\nimport core from 'core';\nimport React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useSelector, useStore } from 'react-redux';\nimport selectors from 'selectors';\nimport FlyoutMenu from '../FlyoutMenu/FlyoutMenu';\nimport DataElementWrapper from 'components/DataElementWrapper';\nimport { enterReaderMode, exitReaderMode } from 'helpers/readerMode';\nimport actions from 'actions';\nimport toggleFullscreen from 'helpers/toggleFullscreen';\nimport DataElements from 'src/constants/dataElement';\nimport { isIE11, isIOS, isIOSFullScreenSupported } from 'helpers/device';\n\nfunction ViewControlsOverlay() {\n  const [t] = useTranslation();\n  const store = useStore();\n\n  const [\n    totalPages,\n    displayMode,\n    isDisabled,\n    isReaderMode,\n    isMultiViewerMode,\n    isFullScreen,\n    activeDocumentViewerKey,\n    isMultiTab,\n    isMultiViewerModeAvailable,\n  ] = useSelector((state) => [\n    selectors.getTotalPages(state),\n    selectors.getDisplayMode(state),\n    selectors.isElementDisabled(state, DataElements.VIEW_CONTROLS_OVERLAY),\n    selectors.isReaderMode(state),\n    selectors.isMultiViewerMode(state),\n    selectors.isFullScreen(state),\n    selectors.getActiveDocumentViewerKey(state),\n    selectors.getIsMultiTab(state),\n    selectors.getIsMultiViewerModeAvailable(state),\n  ]);\n\n  const totalPageThreshold = 1000;\n  let isPageTransitionEnabled = totalPages < totalPageThreshold;\n\n  const documentViewer = core.getDocumentViewer();\n  const displayModeManager = documentViewer?.getDisplayModeManager();\n  if (displayModeManager && displayModeManager.isVirtualDisplayEnabled()) {\n    isPageTransitionEnabled = true;\n  }\n  const showCompareButton = !isIE11 && !isMultiTab && isMultiViewerModeAvailable;\n  const toggleCompareMode = () => {\n    store.dispatch(actions.setIsMultiViewerMode(!isMultiViewerMode));\n  };\n\n  const handleClick = (pageTransition, layout) => {\n    const setDisplayMode = () => {\n      const displayModeObject = displayModeObjects.find(\n        (obj) => obj.pageTransition === pageTransition && obj.layout === layout,\n      );\n      core.setDisplayMode(displayModeObject.displayMode);\n    };\n\n    if (isReaderMode) {\n      exitReaderMode(store);\n      setTimeout(() => {\n        setDisplayMode();\n      });\n    } else {\n      setDisplayMode();\n    }\n  };\n\n  const handleReaderModeClick = () => {\n    if (isReaderMode) {\n      return;\n    }\n    enterReaderMode(store);\n  };\n\n  if (isDisabled) {\n    return null;\n  }\n\n  let pageTransition;\n  let layout;\n\n  const displayModeObject = displayModeObjects.find((obj) => obj.displayMode === displayMode);\n  if (displayModeObject) {\n    pageTransition = displayModeObject.pageTransition;\n    layout = displayModeObject.layout;\n  }\n\n  const showReaderButton = core.isFullPDFEnabled() && core.getDocument()?.getType() === 'pdf';\n  // Full screen is supported in iPad OS for non-video elements, but not in iOS\n  const renderFullScreenToggle = () => {\n    if (isIOS && !isIOSFullScreenSupported) {\n      return null;\n    }\n    return (\n      <>\n        <DataElementWrapper\n          dataElement=\"viewControlsDivider3\"\n          className=\"divider\"\n        />\n        <DataElementWrapper\n          className=\"row\"\n          onClick={toggleFullscreen}\n          dataElement=\"fullScreenButton\"\n        >\n          <Button\n            img={isFullScreen ? 'icon-header-full-screen-exit' : 'icon-header-full-screen'}\n            role=\"option\"\n          />\n          <div className=\"title\">{isFullScreen ? t('action.exitFullscreen') : t('action.enterFullscreen')}</div>\n        </DataElementWrapper>\n      </>\n    );\n  };\n\n  return (\n    <FlyoutMenu\n      menu={DataElements.VIEW_CONTROLS_OVERLAY}\n      trigger={DataElements.VIEW_CONTROLS_OVERLAY_BUTTON}\n      ariaLabel={t('component.viewControls')}\n    >\n      {isPageTransitionEnabled && (\n        <>\n          <DataElementWrapper\n            dataElement=\"pageTransitionHeader\"\n            className=\"type\"\n            ariaLabel={t('option.displayMode.pageTransition')}\n          >\n            {t('option.displayMode.pageTransition')}\n          </DataElementWrapper>\n          <DataElementWrapper\n            className={classNames({ row: true, active: (pageTransition === 'continuous' && !isReaderMode) })}\n            onClick={() => handleClick('continuous', layout)}\n            dataElement=\"continuousPageTransitionButton\"\n          >\n            <Button\n              title=\"option.pageTransition.continuous\"\n              img=\"icon-header-page-manipulation-page-transition-continuous-page-line\"\n              isActive={pageTransition === 'continuous' && !isReaderMode}\n              role=\"option\"\n            />\n            <div className=\"title\">{t('option.pageTransition.continuous')}</div>\n          </DataElementWrapper>\n          <DataElementWrapper\n            className={classNames({ row: true, active: (pageTransition === 'default' && !isReaderMode) })}\n            onClick={() => handleClick('default', layout)}\n            dataElement=\"defaultPageTransitionButton\"\n          >\n            <Button\n              title=\"option.pageTransition.default\"\n              img=\"icon-header-page-manipulation-page-transition-page-by-page-line\"\n              isActive={pageTransition === 'default' && !isReaderMode}\n              role=\"option\"\n            />\n            <div className=\"title\">{t('option.pageTransition.default')}</div>\n          </DataElementWrapper>\n          {showReaderButton && (\n            <DataElementWrapper\n              className={classNames({ row: true, active: isReaderMode })}\n              onClick={() => handleReaderModeClick()}\n              dataElement=\"readerPageTransitionButton\"\n            >\n              <Button\n                title=\"option.pageTransition.reader\"\n                img=\"icon-header-page-manipulation-page-transition-reader\"\n                isActive={isReaderMode}\n                role=\"option\"\n              />\n              <div className=\"title\">{t('option.pageTransition.reader')}</div>\n            </DataElementWrapper>\n          )}\n          {!isReaderMode && (\n            <DataElementWrapper\n              dataElement=\"viewControlsDivider1\"\n              className=\"divider\"\n            />\n          )}\n        </>\n      )}\n      {!isReaderMode && (\n        <>\n          <DataElementWrapper\n            dataElement=\"rotateHeader\"\n            className=\"type\"\n            ariaLabel={t('action.rotate')}\n          >\n            {t('action.rotate')}\n          </DataElementWrapper>\n          <DataElementWrapper className=\"row\" onClick={() => core.rotateClockwise(activeDocumentViewerKey)} dataElement=\"rotateClockwiseButton\">\n            <ActionButton\n              title=\"action.rotateClockwise\"\n              img=\"icon-header-page-manipulation-page-rotation-clockwise-line\"\n              role=\"option\"\n            />\n            <div className=\"title\">{t('action.rotateClockwise')}</div>\n          </DataElementWrapper>\n          <DataElementWrapper className=\"row\" onClick={() => core.rotateCounterClockwise(activeDocumentViewerKey)} dataElement=\"rotateCounterClockwiseButton\">\n            <ActionButton\n              title=\"action.rotateCounterClockwise\"\n              img=\"icon-header-page-manipulation-page-rotation-counterclockwise-line\"\n              role=\"option\"\n            />\n            <div className=\"title\">{t('action.rotateCounterClockwise')}</div>\n          </DataElementWrapper>\n          <DataElementWrapper\n            dataElement=\"viewControlsDivider2\"\n            className=\"divider\"\n          />\n          <DataElementWrapper\n            dataElement=\"layoutHeader\"\n            className=\"type\"\n            ariaLabel={t('option.displayMode.layout')}\n          >\n            {t('option.displayMode.layout')}\n          </DataElementWrapper>\n          <DataElementWrapper\n            className={classNames({ row: true, active: layout === 'single' })}\n            onClick={() => handleClick(pageTransition, 'single')}\n            dataElement=\"singleLayoutButton\"\n          >\n            <Button\n              title=\"option.layout.single\"\n              img=\"icon-header-page-manipulation-page-layout-single-page-line\"\n              isActive={layout === 'single'}\n              role=\"option\"\n            />\n            <div className=\"title\">{t('option.layout.single')}</div>\n          </DataElementWrapper>\n          <DataElementWrapper\n            className={classNames({ row: true, active: layout === 'double' })}\n            onClick={() => handleClick(pageTransition, 'double')}\n            dataElement=\"doubleLayoutButton\"\n          >\n            <Button\n              title=\"option.layout.double\"\n              img=\"icon-header-page-manipulation-page-layout-double-page-line\"\n              isActive={layout === 'double'}\n              role=\"option\"\n            />\n            <div className=\"title\">{t('option.layout.double')}</div>\n          </DataElementWrapper>\n          <DataElementWrapper\n            className={classNames({ row: true, active: layout === 'cover' })}\n            onClick={() => handleClick(pageTransition, 'cover')}\n            dataElement=\"coverLayoutButton\"\n          >\n            <Button\n              title=\"option.layout.cover\"\n              img=\"icon-header-page-manipulation-page-layout-cover-line\"\n              isActive={layout === 'cover'}\n              role=\"option\"\n            />\n            <div className=\"title\">{t('option.layout.cover')}</div>\n          </DataElementWrapper>\n          {showCompareButton && (\n            <DataElementWrapper\n              className={classNames({ row: true, active: isMultiViewerMode })}\n              onClick={toggleCompareMode}\n              dataElement=\"toggleCompareModeButton\"\n            >\n              <Button\n                title=\"action.comparePages\"\n                img=\"icon-header-compare\"\n                isActive={isMultiViewerMode}\n                role=\"option\"\n              />\n              <div className=\"title\">{t('action.sideBySideView')}</div>\n            </DataElementWrapper>\n          )}\n        </>\n      )}\n      {renderFullScreenToggle()}\n    </FlyoutMenu>\n  );\n}\n\nexport default ViewControlsOverlay;\n", "import ViewControlsOverlay from './ViewControlsOverlay';\n\nexport default ViewControlsOverlay;"], "sourceRoot": ""}