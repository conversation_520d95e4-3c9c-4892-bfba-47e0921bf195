(window.webpackJsonp=window.webpackJsonp||[]).push([[37,49],{1543:function(e,t,n){"use strict";n(49),n(53),n(97),n(36),n(78),n(126),n(41),n(16),n(19),n(12),n(13),n(8),n(14),n(10),n(9),n(11),n(15),n(20),n(18);var o=n(0),i=n.n(o),r=n(17),a=n.n(r),l=(n(1606),n(429)),c=n(4),s=n.n(c),d=n(2),p=n(6),u=n(54),m=n(21),y=n(3),f=n(48),S=n(99),h=n(122);function b(e){return function(e){if(Array.isArray(e))return v(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||x(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,r,a,l=[],c=!0,s=!1;try{if(r=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=r.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,i=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw i}}return l}}(e,t)||x(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function x(e,t){if(e){if("string"==typeof e)return v(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?v(e,t):void 0}}function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var w=function(e){var t,n;if(!e)return e;var o=e;return null!==(t=o)&&void 0!==t&&t.toHexString&&(o=o.toHexString()),null!==(n=o)&&void 0!==n&&n.toLowerCase&&(o=o.toLowerCase()),o},T=i.a.createElement("svg",{width:"100%",height:"100%",className:a()("transparent")},i.a.createElement("line",{stroke:"#d82e28",x1:"0",y1:"100%",x2:"100%",y2:"0",strokeWidth:"2",strokeLinecap:"round"})),C={color:s.a.any,ariaTypeLabel:s.a.string},E=function(e){var t=e.onColorChange,n=e.hasTransparentColor,r=void 0!==n&&n,c=e.color,s=e.activeTool,x=e.type,v=e.ariaTypeLabel,C=Object.values(window.Core.Tools.ToolNames).includes(s)?s:window.Core.Tools.ToolNames.EDIT,E=Object(p.f)(),P=Object(l.a)().t,k=Object(p.d)(),O=g(Object(p.e)((function(e){return[y.a.getColors(e,C,x)]})),1)[0],A=g(Object(o.useState)(),2),F=A[0],j=A[1],L=g(Object(o.useState)(!1),2),R=L[0],N=L[1],I=Object(o.useRef)(!0);Object(o.useEffect)((function(){I.current=!0}),[C,c]),Object(o.useEffect)((function(){c&&j(w(c))}),[c]);var _=function(){var e=y.a.getCustomColor(E.getState());return k(d.a.setCustomColor(null)),e},D=Object(o.useCallback)((function(){k(d.a.openElement("ColorPickerModal"));Object(m.c)().addEventListener(u.a.VISIBILITY_CHANGED,(function e(n){var o=n.detail,i=o.element,r=o.isVisible;if("ColorPickerModal"===i&&!r){var a=w(_());if(a)if(O.includes(a))j(a),t(a);else{var l=[].concat(b(O),[a]);k(d.a.setColors(l,C,x,!0)),j(a),t(a)}Object(m.c)().removeEventListener(u.a.VISIBILITY_CHANGED,e)}}))}),[null==O?void 0:O.length,k,j,t,_,x,C]),M=Object(S.a)(D),z=O.map((function(e){return e.toLowerCase()}));r&&z.push("transparent"),F||j("transparent"),z.indexOf(F)>6&&!R&&I.current&&(N(!0),I.current=!1);var H=z.length<=7,W=!(F&&!z.includes(F)),B=z.length<=1||!W;return R||(z=z.slice(0,7)),i.a.createElement(i.a.Fragment,null,i.a.createElement("div",{className:a()("ColorPalette")},z.map((function(e){return w(e)})).map((function(e,n){var o,r,l;return e?i.a.createElement(h.a,{content:"".concat(P("option.colorPalette.colorLabel")," ").concat(null==e||null===(o=e.toUpperCase)||void 0===o?void 0:o.call(e)),key:null==e||null===(r=e.toUpperCase)||void 0===r?void 0:r.call(e)},i.a.createElement("button",{className:"cell-container",onClick:function(){j(e),t(e)},"aria-label":"".concat(v," ").concat(P("option.colorPalette.colorLabel")," ").concat(null==e||null===(l=e.toUpperCase)||void 0===l?void 0:l.call(e)),"aria-current":w(F)===e||!w(F)&&"transparent"===e},i.a.createElement("div",{className:a()({"cell-outer":!0,active:w(F)===e||!w(F)&&"transparent"===e})},i.a.createElement("div",{className:a()({cell:!0,border:!0}),style:{backgroundColor:e}},"transparent"===e&&T)))):i.a.createElement("div",{key:n,className:"dummy-cell"})}))),i.a.createElement("div",{className:"palette-controls"},i.a.createElement("div",{className:"button-container"},i.a.createElement(f.a,{img:"icon-header-zoom-in-line",title:P("action.addNewColor"),onClick:M,className:"control-button",dataElement:"addCustomColor",ariaLabel:"".concat(v," ").concat(P("action.addNewColor")," ").concat(P("action.fromCustomColorPicker"))}),i.a.createElement(f.a,{img:"icon-delete-line",title:P("action.deleteColor"),onClick:function(){var e=w(F),n=b(O),o=n.indexOf(e);if(o>-1){var i=o===n.length-1?0:o+1;j(O[i]),t(O[i]),n.splice(o,1),k(d.a.setColors(n,C,x,!0))}},disabled:B,className:"control-button",dataElement:"deleteSelectedColor",ariaLabel:"".concat(v," ").concat(P("action.deleteColor")," ").concat(F)}),i.a.createElement(f.a,{img:"icon-copy2",title:P("action.copySelectedColor"),onClick:function(){var e=w(F),t=[].concat(b(O),[e]);k(d.a.setColors(t,C,x,!0))},disabled:W,className:"control-button",dataElement:"copySelectedColor",ariaLabel:"".concat(v," ").concat(P("action.copySelectedColor")," ").concat(F)})),i.a.createElement("button",{className:a()("show-more-button control-button",{hidden:H}),onClick:function(){N(!R)},"aria-label":"".concat(v," ").concat(P(P(R?"action.showLessColors":"action.showMoreColors")))},P(R?"message.showLess":"message.showMore"))))};E.propTypes=C;var P=E;t.a=P},1606:function(e,t,n){var o=n(32),i=n(1607);"string"==typeof(i=i.__esModule?i.default:i)&&(i=[[e.i,i,""]]);var r={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const i=t[o];if(0===o)i.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);i.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(i,r);e.exports=i.locals||{}},1607:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.StylePicker .ColorPalette{display:flex;flex-wrap:wrap;display:grid;grid-template-columns:repeat(7,1fr);grid-row-gap:8px;row-gap:8px;justify-items:center}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.StylePicker .ColorPalette{width:196px}}.StylePicker .ColorPalette.padding{padding:4px 12px 8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .ColorPalette{max-width:450px;width:auto}}@media(max-width:640px)and (-ms-high-contrast:active),(max-width:640px)and (-ms-high-contrast:none){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .ColorPalette{width:308px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .ColorPalette{max-width:450px;width:auto}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .ColorPalette{width:308px}}}.StylePicker .ColorPalette .cell-container{padding:0;border:none;background-color:transparent;flex:1 0 14%;cursor:pointer;width:var(--cell-border-size);height:var(--cell-border-size);display:flex;align-items:center;justify-content:center}:host(:not([data-tabbing=true])) .StylePicker .ColorPalette .cell-container,html:not([data-tabbing=true]) .StylePicker .ColorPalette .cell-container{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .ColorPalette .cell-container{width:44px;height:44px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .ColorPalette .cell-container{width:44px;height:44px}}.StylePicker .ColorPalette .cell-container .cell-outer.active{border:1px solid var(--color-palette-border);width:var(--cell-outer-border-size);height:var(--cell-outer-border-size);border-radius:10000000px;display:flex;align-items:center;justify-content:center}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .ColorPalette .cell-container .cell-outer.active{width:36px;height:36px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .ColorPalette .cell-container .cell-outer.active{width:36px;height:36px}}.StylePicker .ColorPalette .cell-container .cell-outer .cell{width:18px;height:18px;border-radius:10000000px}.StylePicker .ColorPalette .cell-container .cell-outer .cell .transparent{border:2px solid var(--faded-text);border-radius:10000000px}.StylePicker .ColorPalette .cell-container .cell-outer .cell.border{border:1px solid var(--white-color-palette-border)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .ColorPalette .cell-container .cell-outer .cell{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .ColorPalette .cell-container .cell-outer .cell{width:24px;height:24px}}.StylePicker .palette-controls{padding-right:12px;padding-left:2px;display:flex;justify-content:space-between}.StylePicker .palette-controls .button-container{display:flex;grid-gap:8px;gap:8px}.StylePicker .palette-controls .control-button{display:flex;align-items:center;justify-content:center;text-align:center;min-width:32px;min-height:32px;padding:0;border:none;background-color:transparent;cursor:pointer;border-radius:4px}:host(:not([data-tabbing=true])) .StylePicker .palette-controls .control-button,html:not([data-tabbing=true]) .StylePicker .palette-controls .control-button{outline:none}.StylePicker .palette-controls .control-button.show-more-button{color:var(--ribbon-active-color)}.StylePicker .palette-controls .control-button.show-more-button:hover{background:none;color:var(--primary-button-hover)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .palette-controls .control-button.show-more-button{color:var(--ribbon-active-color)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .palette-controls .control-button.show-more-button{color:var(--ribbon-active-color)}}.StylePicker .palette-controls .control-button:disabled{cursor:no-drop}.StylePicker .palette-controls .control-button:disabled .Icon{color:var(--disabled-icon)}.StylePicker .palette-controls .control-button.hidden{display:none}.StylePicker .palette-controls .control-button.focus-visible,.StylePicker .palette-controls .control-button:focus-visible{outline:var(--focus-visible-outline)}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1608:function(e,t,n){var o=n(32),i=n(1609);"string"==typeof(i=i.__esModule?i.default:i)&&(i=[[e.i,i,""]]);var r={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const i=t[o];if(0===o)i.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);i.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(i,r);e.exports=i.locals||{}},1609:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.RichTextStyleEditor{margin-bottom:16px}.RichTextStyleEditor .menu-items{margin-bottom:8px!important}.RichTextStyleEditor .menu-items .icon-grid{padding-top:12px;grid-row-gap:12px;row-gap:12px}.RichTextStyleEditor .menu-items .icon-grid .row{padding-top:0}.RichTextStyleEditor .menu-items .icon-grid .row.isRedaction{padding-bottom:8px}.RichTextStyleEditor .menu-items .icon-grid .auto-size-checkbox{padding-top:4px;padding-bottom:8px}.RichTextStyleEditor .menu-items .icon-grid .auto-size-checkbox .ui__choice__input__check--focus{outline:var(--focus-visible-outline)}.RichTextStyleEditor .Dropdown__wrapper{width:100%}.RichTextStyleEditor .Dropdown__wrapper .Dropdown{width:100%!important}.RichTextStyleEditor .Dropdown__wrapper .Dropdown__items{right:unset;width:100%!important}.RichTextStyleEditor .FontSizeDropdown{width:100%!important}.RichTextStyleEditor .ColorPalette{padding-bottom:8px}.RichTextStyleEditor .text-size-slider{margin-top:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RichTextStyleEditor .icon-grid{display:flex;flex-direction:column}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RichTextStyleEditor .icon-grid{display:flex;flex-direction:column}}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1668:function(e,t,n){"use strict";n.r(t);n(36),n(49),n(53),n(19),n(88),n(344),n(436),n(438),n(12),n(13),n(8),n(14),n(10),n(9),n(11),n(16),n(15),n(20),n(18),n(26),n(27),n(25),n(22),n(30),n(28),n(45),n(23),n(24),n(47),n(46),n(60),n(44);var o=n(0),i=n.n(o),r=n(6),a=n(4),l=n.n(a),c=n(1543),s=n(1),d=n(2),p=n(3),u=(n(1608),n(5)),m=n(295),y=n(1500),f=n(429);function S(e){return(S="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function h(){return(h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){x(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function x(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==S(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==S(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===S(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function v(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,r,a,l=[],c=!0,s=!1;try{if(r=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=r.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,i=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw i}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return w(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return w(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function w(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var T={annotation:l.a.object,editor:l.a.object,style:l.a.shape({TextColor:l.a.string,RichTextStyle:l.a.any}),isFreeTextAutoSize:l.a.bool,onFreeTextSizeToggle:l.a.func,onPropertyChange:l.a.func,onRichTextStyleChange:l.a.func,isRedaction:l.a.bool,isRichTextEditMode:l.a.bool,setIsRichTextEditMode:l.a.func,isWidget:l.a.bool},C=function(e){var t,n,a,l,S,b,w,T,C,E,P,k,O,A=e.annotation,F=e.editor,j=e.style,L=e.isFreeTextAutoSize,R=e.onFreeTextSizeToggle,N=e.onPropertyChange,I=e.onRichTextStyleChange,_=e.isRichTextEditMode,D=e.setIsRichTextEditMode,M=e.isRedaction,z=e.isWidget,H=e.activeTool,W=v(Object(r.e)((function(e){return[p.a.getFonts(e)]}),r.c),1)[0],B=v(Object(o.useState)({}),2),V=B[0],Y=B[1],U=Object(o.useRef)(null),q=Object(o.useRef)(null),K=Object(o.useRef)({}),X=Object(r.d)(),G=Object(o.useRef)(),$=Object(o.useRef)();$.current=_;var J=v(Object(f.a)(),1)[0];Object(o.useEffect)((function(){var e=function(e,t){!e&&t&&U.current&&U.current.setSelection(t.index,t.length),e&&U.current&&Y(Q(e))},t=function(){var e;Y(Q(null===(e=U.current)||void 0===e?void 0:e.getSelection()))};return s.a.addEventListener("editorSelectionChanged",e),s.a.addEventListener("editorTextChanged",t),X(d.a.disableElements([u.a.ANNOTATION_STYLE_POPUP])),function(){s.a.removeEventListener("editorSelectionChanged",e),s.a.removeEventListener("editorTextChanged",t),X(d.a.enableElements([u.a.ANNOTATION_STYLE_POPUP]))}}),[]),Object(o.useEffect)((function(){var e;if(U.current=F,q.current=A,_&&A){var t,n,o,i,r,a,l="solid";try{l="dash"===A.Style?"".concat(A.Style,",").concat(A.Dashes):A.Style}catch(e){console.error(e)}var c=A.getRichTextStyle()[0];K.current={Font:A.Font,FontSize:A.FontSize,TextAlign:A.TextAlign,TextVerticalAlign:A.TextVerticalAlign,bold:null!==(t="bold"===(null==c?void 0:c["font-weight"]))&&void 0!==t&&t,italic:null!==(n="italic"===(null==c?void 0:c["font-style"]))&&void 0!==n&&n,underline:(null==c||null===(o=c["text-decoration"])||void 0===o?void 0:o.includes("underline"))||(null==c||null===(i=c["text-decoration"])||void 0===i?void 0:i.includes("word")),strikeout:null!==(r=null==c||null===(a=c["text-decoration"])||void 0===a?void 0:a.includes("line-through"))&&void 0!==r&&r,size:null==c?void 0:c["font-size"],font:null==c?void 0:c["font-family"],StrokeStyle:l,calculatedFontSize:A.getCalculatedFontSize()}}Y(Q(null===(e=U.current)||void 0===e?void 0:e.getSelection())),G.current&&(U.current.setSelection(G.current),G.current=null)}),[A,F,_]),Object(o.useEffect)((function(){var e=function(){U.current=null,q.current=null,D(!1)},t=function(){D(!0)};return s.a.addEventListener("editorBlur",e),s.a.addEventListener("editorFocus",t),function(){s.a.removeEventListener("editorBlur",e),s.a.removeEventListener("editorFocus",t)}}),[X]);var Z,Q=function(e){if(!e)return{};var t=U.current.getFormat(e.index,e.length);if("string"==typeof t.color)t.color=new window.Core.Annotations.Color(t.color);else if(Array.isArray(t.color)){var n=new window.Core.Annotations.Color(t.color[t.color.length-1]);t.color=n}else t.color||(t.color=q.current.TextColor);for(var o=0,i=["font","size","originalSize"];o<i.length;o++){var r=i[o];t[r]&&Array.isArray(t[r])&&(t[r]=void 0)}return t},ee=function(e,t){var n,o;"size"===e?null===(n=U.current)||void 0===n||n.format("applyCustomFontSize",t):null===(o=U.current)||void 0===o||o.format(e,t);"color"===e&&(t=new window.Core.Annotations.Color(t)),Y(g(g({},V),{},x({},e,t)))},te=function(e,t){if($.current){var n=U.current.getSelection(),o=n.index,i=n.length,r=q.current;r[e]=t,U.current.blur(),"FontSize"!==e&&"Font"!==e||Object(y.a)(r),setTimeout((function(){G.current={index:o,length:i},s.a.getAnnotationManager().getEditBoxManager().focusBox(r)}),0)}else N(e,t)},ne=j.RichTextStyle,oe={bold:null!==(t="bold"===(null==ne||null===(n=ne[0])||void 0===n?void 0:n["font-weight"]))&&void 0!==t&&t,italic:null!==(a="italic"===(null==ne||null===(l=ne[0])||void 0===l?void 0:l["font-style"]))&&void 0!==a&&a,underline:(null==ne||null===(S=ne[0])||void 0===S||null===(b=S["text-decoration"])||void 0===b?void 0:b.includes("underline"))||(null==ne||null===(w=ne[0])||void 0===w||null===(T=w["text-decoration"])||void 0===T?void 0:T.includes("word")),strikeout:null!==(C=null==ne||null===(E=ne[0])||void 0===E||null===(P=E["text-decoration"])||void 0===P?void 0:P.includes("line-through"))&&void 0!==C&&C,font:null==ne||null===(k=ne[0])||void 0===k?void 0:k["font-family"],size:null==ne||null===(O=ne[0])||void 0===O?void 0:O["font-size"],StrokeStyle:"solid"};Z=g(g({},j),oe),_&&A&&(K.current.bold=V.bold,K.current.italic=V.italic,K.current.underline=V.underline,K.current.strikeout=V.strike,K.current.quillFont=V.font||K.current.Font,K.current.quillFontSize=V.originalSize||K.current.FontSize);var ie={fonts:W,onPropertyChange:te,properties:Z,stateless:!0,isFreeText:!M},re={onRichTextStyleChange:function(e,t){if($.current){var n={"font-weight":"bold","font-style":"italic",underline:"underline","line-through":"strike","font-family":"font","font-size":"size"};if("font-family"===e||"font-size"===e){ee(n[e],t);var o=q.current;if(o.isAutoSized())s.a.getAnnotationManager().getEditBoxManager().resizeAnnotation(o)}else!function(e){return function(){var t=U.current.getSelection(),n=t.index,o=t.length;if(0===o){G.current={index:n,length:o};var i=U.current.getSelection();n=i.index,o=i.length}var r=U.current.getFormat(n,o);ee(e,!r[e])}}(n[e])()}else I(e,t)},properties:_?K.current:Z,isFreeTextAutoSize:L,isRichTextEditMode:_,isRedaction:M,onFreeTextSizeToggle:R},ae={onRichTextStyleChange:te,isFreeTextAutoSize:!1,isRichTextEditMode:!1,isRedaction:!1,isWidget:z};return i.a.createElement("div",{className:"RichTextStyleEditor",onMouseDown:function(e){"touchstart"!==e.type&&_&&e.preventDefault()}},i.a.createElement("div",{className:"menu-items"},i.a.createElement(m.a,h({},ie,z?ae:re))),i.a.createElement(c.a,{onColorChange:function(e){!function(e,t){$.current?ee("color",t.toHexString()):N(e,t)}("TextColor",new window.Core.Annotations.Color(e))},color:_?V.color:j.TextColor,activeTool:H,type:"Text",ariaTypeLabel:J("option.stylePopup.textStyle")}))};C.propTypes=T;var E=i.a.memo(C);t.default=E},1688:function(e,t,n){var o=n(32),i=n(1689);"string"==typeof(i=i.__esModule?i.default:i)&&(i=[[e.i,i,""]]);var r={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const i=t[o];if(0===o)i.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);i.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(i,r);e.exports=i.locals||{}},1689:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.StylePicker{display:flex;flex-direction:column}.StylePicker .slider-property{font-size:14px;font-weight:700;margin-bottom:8px!important}.StylePicker .StyleOption{margin-bottom:16px}.StylePicker .StyleOption .styles-container .styles-title{margin:0 0 8px;font-size:14px;font-weight:700}.StylePicker .StyleOption .styles-container [data-element=borderStylePicker]{margin-top:8px}.StylePicker .StyleOption .slider:only-child{margin-bottom:0}.StylePicker .StyleOption .slider .slider-element-container{margin-left:-3px}.StylePicker .StyleOption:last-child{margin-bottom:0}.StylePicker .PanelSection~.PanelSection .CollapsibleSection>.collapsible-page-group-header{margin-top:16px}.StylePicker .PanelSection .CollapsibleSection>.collapsible-page-group-header>button{font-size:16px;padding:0;font-weight:700;height:31.5px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .PanelSection .CollapsibleSection>.collapsible-page-group-header>button{font-size:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .PanelSection .CollapsibleSection>.collapsible-page-group-header>button{font-size:16px}}.StylePicker .PanelSection .CollapsibleSection:first-of-type{margin-bottom:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .PanelSection .CollapsibleSection{border-bottom:none}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .PanelSection .CollapsibleSection{border-bottom:none}}.StylePicker .PanelSection .panel-section-wrapper.Opacity{margin-top:16px}.StylePicker .PanelSection:first-child .panel-section-wrapper.Opacity{margin-top:0}.StylePicker .PanelSection .collapsible-page-group-header+.collapsible-content{margin-top:16px}.StylePicker .PanelSection .PanelSubsection{margin-bottom:12px}.StylePicker .PanelSection .PanelSubsection .menu-subtitle{font-size:14px;font-weight:700;margin-bottom:12px}.StylePicker .PanelSection .divider{background-color:var(--divider);width:100%;height:1px}.StylePicker .PanelSection .menu-items{margin-bottom:16px}.StylePicker .PanelSection .menu-items:only-child{margin-bottom:0}.StylePicker .PanelSection .menu-items .ColorPalette{margin-bottom:8px}.StylePicker .PanelSection .slider{margin-bottom:16px}.StylePicker .PanelSection .slider:last-child,.StylePicker .PanelSection:empty,.StylePicker .PanelSection:last-child{margin-bottom:0}.StylePicker .PanelSection .snapping-option{margin-top:16px}.StylePicker .spacer{width:100%}.StylePicker .Dropdown,.StylePicker .FontSizeDropdown,.StylePicker .overlay-text-input{height:32px}.StylePicker .overlay-text-input:focus{border-color:var(--blue-5)}.StylePicker .lineStyleContainer{margin-bottom:0!important}.StylePicker .lineStyleContainer .StylePicker-LineStyle{display:flex;flex-direction:row;grid-column-gap:8px;-moz-column-gap:8px;column-gap:8px;justify-content:space-between}.StylePicker .lineStyleContainer .StylePicker-LineStyle div.Dropdown{width:100%!important}.StylePicker .lineStyleContainer .StylePicker-LineStyle .Dropdown__items,.StylePicker .lineStyleContainer .StylePicker-LineStyle .Dropdown__wrapper{width:100%}.StylePicker .lineStyleContainer .StylePicker-LineStyle .linestyle-image svg{width:35px;margin-top:11px}.StylePicker .lineStyleContainer .StylePicker-LineStyle .linestyle-image.shift-alignment svg{margin-top:8px}.StylePicker .lineStyleContainer .StylePicker-LineStyle .Dropdown__items .linestyle-image svg{width:45px}.StylePicker .lineStyleContainer .StylePicker-LineStyle [data-element=middleLineStyleDropdown] .linestyle-image.shift-alignment{padding-top:0}.StylePicker .lineStyleContainer .StylePicker-LineStyle [data-element=middleLineStyleDropdown] .linestyle-image.shift-alignment svg{margin-top:11px}.StylePicker .lineStyleContainer .StylePicker-LineStyle [data-element=middleLineStyleDropdown] .Dropdown__items{top:-197px}.StylePicker .lineStyleContainer .StylePicker-LineStyle .StyleOptions{max-width:80px}.StylePicker .lineStyleContainer .StylePicker-LineStyle>*{flex-grow:1;flex-basis:0}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker{padding:0 16px 16px;overflow:auto}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker{padding:0 16px 16px;overflow:auto}}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1690:function(e,t,n){var o=n(32),i=n(1691);"string"==typeof(i=i.__esModule?i.default:i)&&(i=[[e.i,i,""]]);var r={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let t;t=document.getElementsByTagName("apryse-webviewer"),t.length||(t=function e(t,n=document){const o=[];return n.querySelectorAll(t).forEach(e=>o.push(e)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...e(t,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<t.length;o++){const i=t[o];if(0===o)i.shadowRoot.appendChild(e),e.onload=function(){n.length>0&&n.forEach(t=>{t.innerHTML=e.innerHTML})};else{const t=e.cloneNode(!0);i.shadowRoot.appendChild(t),n.push(t)}}},singleton:!1};o(i,r);e.exports=i.locals||{}},1691:function(e,t,n){(t=e.exports=n(33)(!1)).push([e.i,":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.StylePanel{display:flex;flex-direction:column;background-color:var(--panel-background);padding-bottom:75px}.StylePanel .style-panel-header{font-size:16px;font-weight:700;margin-top:0;margin-bottom:16px}.StylePanel .label{padding-top:16px;font-size:14px;font-weight:700}.StylePanel .no-tool-selected{padding-top:36px;display:flex;flex-direction:column;align-items:center;flex:1 1 auto}.StylePanel .no-tool-selected .msg{padding-top:24px;font-size:13px;text-align:center}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePanel .no-tool-selected .msg{line-height:15px;width:146px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePanel .no-tool-selected .msg{line-height:15px;width:146px}}.StylePanel .no-tool-selected .empty-icon,.StylePanel .no-tool-selected .empty-icon svg{width:55px;height:56px}.StylePanel .no-tool-selected .empty-icon *{fill:var(--gray-6);color:var(--gray-6)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePanel{width:100%;height:100%;padding-bottom:16px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePanel .style-panel-header{margin:0 16px 16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePanel{width:100%;height:100%;padding-bottom:16px}.App.is-web-component:not(.is-in-desktop-only-mode) .StylePanel .style-panel-header{margin:0 16px 16px}}",""]),t.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1765:function(e,t,n){"use strict";n.r(t);var o=n(0),i=n.n(o),r=(n(15),n(36),n(49),n(53),n(23),n(8),n(24),n(19),n(12),n(13),n(14),n(10),n(9),n(11),n(16),n(20),n(18),n(26),n(27),n(25),n(22),n(30),n(28),n(45),n(47),n(46),n(6)),a=n(429),l=n(3),c=n(43),s=(n(114),n(343),n(143),n(60),n(44),n(4)),d=n.n(s),p=(n(1688),n(1543)),u=n(441),m=n(5),y=n(407),f=n(184),S=n(57),h=n(1),b=n(29),g=n(288),x=n(235),v=n(2),w=function(e){var t,n,o=e.Scale,a=e.Precision,l=e.isSnapModeEnabled,c=Object(r.d)(),s=(null===(t=h.a.getDocument())||void 0===t?void 0:t.getType())===S.a.WEBVIEWER_SERVER&&h.a.getDocument().isWebViewerServerDocument(),d=(null===(n=h.a.getDocument())||void 0===n?void 0:n.getType().toLowerCase())===S.a.PDF||s,p=o&&a&&d&&h.a.isFullPDFEnabled();return i.a.createElement(i.a.Fragment,null,p&&i.a.createElement("div",{className:"snapping-option"},i.a.createElement(g.a,{dataElement:"measurementSnappingOption",id:"measurement-snapping",type:"checkbox",label:b.a.t("option.shared.enableSnapping"),checked:l,onChange:function(e){if(h.a.isFullPDFEnabled()){var t=e.target.checked,n=t?h.a.getDocumentViewer().SnapMode.e_DefaultSnapMode|h.a.getDocumentViewer().SnapMode.POINT_ON_LINE:null;Object(x.a)().forEach((function(e){var o;null===(o=e.setSnapMode)||void 0===o||o.call(e,n),c(v.a.setEnableSnapMode({toolName:e.name,isEnabled:t}))}))}}})))},T=(n(104),n(141),n(153),window.Core.Tools),C=function(e){return[T.AddParagraphTool,T.AddImageContentTool,T.CropCreateTool,T.SnippingCreateTool].some((function(t){return h.a.getTool(e)instanceof t}))},E=function(e){return[T.RedactionCreateTool,T.EraserTool,T.TextFormFieldCreateTool,T.ListBoxFormFieldCreateTool,T.ComboBoxFormFieldCreateTool,T.SignatureFormFieldCreateTool,T.CheckBoxFormFieldCreateTool,T.RadioButtonFormFieldCreateTool].some((function(t){return h.a.getTool(e)instanceof t}))},P=function(e){return[T.RedactionCreateTool].some((function(t){return h.a.getTool(e)instanceof t}))},k=function(e,t){var n={AnnotationCreateRedaction:{Title:"component.redaction",StrokeColor:"stylePanel.headings.redactionMarkOutline",FillColor:"stylePanel.headings.redactionFill"}};return n[e]&&n[e][t]},O=function(e){var t=window.Core.Tools.ToolNames;return[t.TEXT_FORM_FIELD,t.LIST_BOX_FIELD,t.COMBO_BOX_FIELD].includes(e)},A=function(e,t){return 1===e.length&&function(e,t){return t.some((function(t){return e instanceof t}))}(e[0],t)},F=n(458),j=n(1668),L=n(459),R=n(193),N=n(80);function I(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,r,a,l=[],c=!0,s=!1;try{if(r=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=r.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,i=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw i}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return _(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var D=function(e){var t=e.showFillColorAndCollapsablePanelSections,n=e.isStamp,o=e.onStrokeColorChange,r=e.onStyleChange,l=e.strokeColor,c=e.activeTool,s=e.hideStrokeSlider,d=e.strokethicknessComponent,u=e.showLineStyleOptions,m=e.renderSlider,y=e.strokeStyle,f=e.isInFormFieldCreationMode,S=e.isFreeText,h=e.isFreeHand,b=e.isArc,g=e.onStartLineStyleChange,x=e.startingLineStyle,v=e.isStyleOptionDisabled,w=e.onStrokeStyleChange,T=e.strokeLineStyle,C=e.middleLineSegmentLabel,E=e.isEllipse,P=e.withCloudyStyle,k=e.onEndLineStyleChange,O=e.endingLineStyle,A=e.defaultStartLineStyles,F=e.defaultStrokeStyles,j=e.defaultEndLineStyles,L=e.openStrokeStyleContainer,_=e.isStrokeStyleContainerActive,D=e.stylePanelSectionTitles,M=I(Object(a.a)(),1)[0],z=i.a.createElement("div",{className:"panel-section-wrapper"},!n&&i.a.createElement(i.a.Fragment,null,i.a.createElement("div",{className:"menu-items"},i.a.createElement(p.a,{onColorChange:o,onStyleChange:r,color:l,activeTool:c,type:"Stroke",ariaTypeLabel:M("option.annotationColor.StrokeColor")})),!s&&d&&d,u&&i.a.createElement("div",{className:"StyleOption"},m("opacity")),!!y&&!(f&&!S)&&!h&&!b&&i.a.createElement("div",{className:"StyleOption"},i.a.createElement("div",{className:"styles-container lineStyleContainer"},i.a.createElement("div",{className:"styles-title"},M("option.styleOption.style")),i.a.createElement("div",{className:"StylePicker-LineStyle"},u&&i.a.createElement(N.a,{id:"startLineStyleDropdown",translationPrefix:"stylePanel.lineEnding.start",className:"StylePicker-StartLineStyleDropdown",dataElement:"startLineStyleDropdown",images:A,onClickItem:g,currentSelectionKey:x,showLabelInList:!0}),!v&&i.a.createElement(N.a,{id:"middleLineStyleDropdown",translationPrefix:C,className:"StylePicker-StrokeLineStyleDropdown".concat(y&&!u?" StyleOptions":""),dataElement:"middleLineStyleDropdown",images:E||u?F:P,onClickItem:w,currentSelectionKey:T,showLabelInList:!0}),u&&i.a.createElement(N.a,{id:"endLineStyleDropdown",translationPrefix:"stylePanel.lineEnding.end",className:"StylePicker-EndLineStyleDropdown",dataElement:"endLineStyleDropdown",images:j,onClickItem:k,currentSelectionKey:O,showLabelInList:!0}))))));return t?i.a.createElement(R.a,{header:M(D(c,"StrokeColor")||"option.annotationColor.StrokeColor"),headingLevel:2,isInitiallyExpanded:!1,onToggle:L,shouldShowHeading:t,isExpanded:_||!t},z):z},M=D;function z(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,r,a,l=[],c=!0,s=!1;try{if(r=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=r.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,i=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw i}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return H(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return H(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function H(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}D.propTypes={showFillColorAndCollapsablePanelSections:d.a.bool,isStamp:d.a.bool,onStrokeColorChange:d.a.func,onStyleChange:d.a.func,strokeColor:d.a.oneOfType([d.a.string,d.a.object]),activeTool:d.a.string,hideStrokeSlider:d.a.bool,strokethicknessComponent:d.a.node,showLineStyleOptions:d.a.bool,renderSlider:d.a.func,strokeStyle:d.a.string,isInFormFieldCreationMode:d.a.bool,isFreeText:d.a.bool,isFreeHand:d.a.bool,isArc:d.a.bool,onStartLineStyleChange:d.a.func,startingLineStyle:d.a.string,isStyleOptionDisabled:d.a.bool,onStrokeStyleChange:d.a.func,strokeLineStyle:d.a.string,middleLineSegmentLabel:d.a.string,isEllipse:d.a.bool,withCloudyStyle:d.a.array,onEndLineStyleChange:d.a.func,endingLineStyle:d.a.string,defaultStartLineStyles:d.a.array,defaultStrokeStyles:d.a.array,defaultEndLineStyles:d.a.array,openStrokeStyleContainer:d.a.func,isStrokeStyleContainerActive:d.a.bool,stylePanelSectionTitles:d.a.func};var W=function e(t){var n=t.showFillColorAndCollapsablePanelSections,o=t.shouldHideOpacitySlider,r=t.activeTool,l=t.showLineStyleOptions,c=t.renderSlider,s=t.isOpacityContainerActive,p=t.openOpacityContainer,u=z(Object(a.a)(),1)[0];e.propTypes={showFillColorAndCollapsablePanelSections:d.a.bool,shouldHideOpacitySlider:d.a.func,activeTool:d.a.string,showLineStyleOptions:d.a.bool,renderSlider:d.a.func,isOpacityContainerActive:d.a.bool,openOpacityContainer:d.a.func};var m=i.a.createElement("div",{className:"panel-section-wrapper Opacity"},!l&&!o(r)&&i.a.createElement("div",{className:"StyleOption"},c("opacity",n)));return!n||o(r)?m:i.a.createElement(R.a,{header:u("option.slider.opacity"),headingLevel:2,isInitiallyExpanded:!1,isExpanded:s||!n,onToggle:p},m)};function B(e){return(B="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function V(){return(V=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}function Y(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==B(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==B(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===B(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function U(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,r,a,l=[],c=!0,s=!1;try{if(r=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=r.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,i=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw i}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return q(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return q(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function q(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var K=f.d.concat(f.a),X={activeType:d.a.string,endLineStyle:d.a.string,handleRichTextStyleChange:d.a.func,isArc:d.a.bool,isEllipse:d.a.bool,isFreeHand:d.a.bool,isFreeText:d.a.bool,isFreeTextAutoSize:d.a.bool,isInFormFieldCreationMode:d.a.bool,isRedaction:d.a.bool,isStamp:d.a.bool,isTextStylePickerHidden:d.a.bool,isWidget:d.a.bool,onFreeTextSizeToggle:d.a.func,onLineStyleChange:d.a.func,onStyleChange:d.a.func.isRequired,redactionLabelProperties:d.a.object,showLineStyleOptions:d.a.bool,sliderProperties:d.a.arrayOf(d.a.string),startLineStyle:d.a.string,strokeStyle:d.a.string,style:d.a.object.isRequired,toolName:d.a.string},G=function(e){var t,n,c=e.onStyleChange,s=e.style,d=e.isFreeText,S=e.isEllipse,b=e.isRedaction,g=e.isWidget,x=e.isFreeHand,C=e.showLineStyleOptions,O=e.isArc,A=e.isStamp,N=e.isInFormFieldCreationMode,I=e.startLineStyle,_=e.endLineStyle,D=e.strokeStyle,z=e.onLineStyleChange,H=e.onFreeTextSizeToggle,B=e.isFreeTextAutoSize,q=e.handleRichTextStyleChange,X=e.activeTool,G=e.saveEditorInstance,$=U(Object(a.a)(),1)[0],J=Object(r.d)(),Z=U(Object(o.useState)(!1),2),Q=Z[0],ee=Z[1],te=U(Object(o.useState)(s.StrokeColor),2),ne=te[0],oe=te[1],ie=U(Object(o.useState)(I),2),re=ie[0],ae=ie[1],le=U(Object(o.useState)(_),2),ce=le[0],se=le[1],de=U(Object(o.useState)(D),2),pe=de[0],ue=de[1],me=U(Object(o.useState)(s.FillColor),2),ye=me[0],fe=me[1],Se=(n=X,[T.RubberStampCreateTool,T.StampCreateTool,T.EraserTool].some((function(e){return h.a.getTool(n)instanceof e}))),he=function(e){return[T.RectangleCreateTool,T.EllipseCreateTool,T.PolygonCreateTool,T.PolygonCloudCreateTool,T.EllipseMeasurementCreateTool,T.AreaMeasurementCreateTool,T.FreeTextCreateTool,T.CalloutCreateTool,T.RedactionCreateTool,T.TextFormFieldCreateTool,T.RadioButtonFormFieldCreateTool,T.CheckBoxFormFieldCreateTool,T.ListBoxFormFieldCreateTool,T.ComboBoxFormFieldCreateTool].some((function(t){return h.a.getTool(e)instanceof t}))}(X),be=function(e){return[T.RubberStampCreateTool,T.StampCreateTool,T.EraserTool].some((function(t){return h.a.getTool(e)instanceof t}))}(X),ge=function(e){return[T.TextUnderlineCreateTool,T.TextHighlightCreateTool,T.TextSquigglyCreateTool,T.TextStrikeoutCreateTool,T.CountMeasurementCreateTool,T.RubberStampCreateTool,T.FileAttachmentCreateTool,T.StampCreateTool,T.StickyCreateTool,T.MarkInsertTextCreateTool,T.MarkReplaceTextCreateTool].some((function(t){return h.a.getTool(e)instanceof t}))}(X),xe=function(e){return[T.DistanceMeasurementCreateTool,T.ArcMeasurementCreateTool,T.PerimeterMeasurementCreateTool,T.AreaMeasurementCreateTool,T.RectangularAreaMeasurementCreateTool,T.CloudyRectangularAreaMeasurementCreateTool].some((function(t){return h.a.getTool(e)instanceof t}))}(X),ve=function(e){return[T.FreeTextCreateTool,T.CalloutCreateTool,T.RedactionCreateTool,T.TextFormFieldCreateTool,T.ListBoxFormFieldCreateTool,T.ComboBoxFormFieldCreateTool].some((function(t){return h.a.getTool(e)instanceof t}))}(X);Object(o.useEffect)((function(){he&&J(ve?v.a.openElement(m.a.RICH_TEXT_STYLE_CONTAINER):v.a.openElement(m.a.STROKE_STYLE_CONTAINER))}),[X]),Object(o.useEffect)((function(){Q&&J(v.a.closeElement(m.a.ANNOTATION_POPUP))}),[Q]),Object(o.useEffect)((function(){oe(s.StrokeColor),fe(s.FillColor)}),[ne,ye,s]),Object(o.useEffect)((function(){oe(s.StrokeColor),ae(I),ue(D),se(_)}),[I,_,D]);var we=function(e,t){null==c||c(e,t)};b&&(s.Opacity=null,s.StrokeThickness=null);var Te=U(Object(r.e)((function(e){return[l.a.isSnapModeEnabled(e),l.a.isElementDisabled(e,m.a.STYLE_OPTION),l.a.isElementOpen(e,m.a.STROKE_STYLE_CONTAINER),l.a.isElementOpen(e,m.a.FILL_COLOR_CONTAINER),l.a.isElementOpen(e,m.a.OPACITY_CONTAINER),l.a.isElementOpen(e,m.a.RICH_TEXT_STYLE_CONTAINER)]})),6),Ce=Te[0],Ee=Te[1],Pe=Te[2],ke=Te[3],Oe=Te[4],Ae=Te[5],Fe=(Y(t={},m.a.STROKE_STYLE_CONTAINER,Pe),Y(t,m.a.FILL_COLOR_CONTAINER,ke),Y(t,m.a.OPACITY_CONTAINER,Oe),Y(t,m.a.RICH_TEXT_STYLE_CONTAINER,Ae),t),je=function(e){J(Fe[e]?v.a.closeElement(e):v.a.openElement(e))},Le=function(e,t){var n=function(e){var t=s.Opacity,n=s.StrokeThickness,o=s.FontSize;switch(e.toLowerCase()){case"opacity":return null===t?null:{property:"Opacity",displayProperty:"opacity",value:100*t,getDisplayValue:function(e){return"".concat(Math.round(e),"%")},dataElement:m.a.OPACITY_SLIDER,withInputField:!0,inputFieldType:"number",min:0,max:100,step:1,getLocalValue:function(e){return parseInt(e)/100}};case"strokethickness":return null===n?null:{property:"StrokeThickness",displayProperty:"thickness",value:n,getDisplayValue:y.a,dataElement:m.a.STROKE_THICKNESS_SLIDER,withInputField:!0,inputFieldType:"number",min:0,max:23,step:1,steps:Object(y.b)(d)};case"fontsize":return null===o?null:{property:"FontSize",displayProperty:"text",value:o,getDisplayValue:function(e){return"".concat(Math.round(parseInt(e,10)),"pt")},dataElement:m.a.FONT_SIZE_SLIDER,min:5,max:45,step:1,withInputField:!0,inputFieldType:"number",getLocalValue:function(e){return"".concat(parseFloat(e).toFixed(2),"pt")}}}}(e);return n?i.a.createElement(u.a,V({key:e},n,{onStyleChange:we,onSliderChange:we,shouldHideSliderTitle:t,customCircleRadius:8,customLineStrokeWidth:5})):null},Re=function(){if(he)return i.a.createElement("div",{className:"divider"})},Ne=Object(F.a)(G),Ie=Le("strokethickness"),_e=C?"stylePanel.lineEnding.middle":"stylePanel.borderStyle";return i.a.createElement("div",{className:"StylePicker",onMouseDown:function(e){"touchstart"!==e.type&&"INPUT"!==e.target.tagName.toUpperCase()&&e.preventDefault()}},ve&&i.a.createElement("div",{className:"PanelSection TextStyle"},i.a.createElement(R.a,{header:$(k(X,"OverlayText")||"option.stylePopup.textStyle"),headingLevel:2,isInitiallyExpanded:!1,isExpanded:Ae,onToggle:function(){J(v.a.openElements(m.a.RICH_TEXT_EDITOR)),je(m.a.RICH_TEXT_STYLE_CONTAINER)}},i.a.createElement("div",{className:"panel-section-wrapper"},b&&i.a.createElement("div",{className:"PanelSubsection RedactionTextLabel"},i.a.createElement("div",{className:"menu-subtitle"},$("stylePanel.headings.redactionTextLabel")),i.a.createElement(L.a,{properties:s,onPropertyChange:c,placeholderText:" "})),i.a.createElement(j.default,V({style:s},Ne,{property:"TextColor",colorMapKey:"freeText",isFreeTextAutoSize:B,onFreeTextSizeToggle:H,onPropertyChange:c,onRichTextStyleChange:q,isRichTextEditMode:Q,setIsRichTextEditMode:ee,isRedaction:b,activeTool:X,isWidget:g})))),Re()),!Se&&i.a.createElement("div",{className:"PanelSection"},i.a.createElement(M,{showFillColorAndCollapsablePanelSections:he,isStamp:A,onStrokeColorChange:function(e){null==c||c("StrokeColor",e),oe(e)},onStyleChange:c,strokeColor:ne,activeTool:X,hideStrokeSlider:ge,strokethicknessComponent:Ie,showLineStyleOptions:C,renderSlider:Le,strokeStyle:pe,isInFormFieldCreationMode:N,isFreeText:d,isFreeHand:x,isArc:O,onStartLineStyleChange:function(e){null==z||z("start",e),ae(e)},startingLineStyle:re,isStyleOptionDisabled:Ee,onStrokeStyleChange:function(e){null==z||z("middle",e),ue(e)},strokeLineStyle:pe,middleLineSegmentLabel:_e,isEllipse:S,withCloudyStyle:K,onEndLineStyleChange:function(e){null==z||z("end",e),se(e)},endingLineStyle:ce,defaultStartLineStyles:f.c,defaultStrokeStyles:f.d,defaultEndLineStyles:f.b,openStrokeStyleContainer:function(){return je(m.a.STROKE_STYLE_CONTAINER)},isStrokeStyleContainerActive:Pe,stylePanelSectionTitles:k}),Re()),Se&&!ge&&Ie&&Ie,he&&!be&&i.a.createElement("div",{className:"PanelSection"},i.a.createElement(R.a,{header:$(k(X,"FillColor")||"option.annotationColor.FillColor"),headingLevel:2,isInitiallyExpanded:!1,isExpanded:ke,onToggle:function(){return je(m.a.FILL_COLOR_CONTAINER)}},i.a.createElement("div",{className:"panel-section-wrapper"},i.a.createElement("div",{className:"menu-items"},i.a.createElement(p.a,{onColorChange:function(e){null==c||c("FillColor",e),fe(e)},onStyleChange:c,color:ye,hasTransparentColor:!P(X),activeTool:X,type:"Fill",ariaTypeLabel:$("option.annotationColor.FillColor")})))),!E(X)&&Re()),i.a.createElement("div",{className:"PanelSection"},i.a.createElement(W,{showFillColorAndCollapsablePanelSections:he,shouldHideOpacitySlider:E,activeTool:X,showLineStyleOptions:C,renderSlider:Le,isOpacityContainerActive:Oe,openOpacityContainer:function(){return je(m.a.OPACITY_CONTAINER)}}),xe&&Re()),xe&&i.a.createElement(i.a.Fragment,null,!he&&i.a.createElement("div",{className:"spacer"}),i.a.createElement("div",{className:"PanelSection"},i.a.createElement(w,{Scale:s.Scale,Precision:s.Precision,isSnapModeEnabled:Ce}))))};G.propTypes=X;var $=G,J=n(453),Z=n(247),Q=n(158),ee=n(194),te=n(1500),ne=n(50),oe=n(1522),ie=n(449),re=n(72);function ae(e){return(ae="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function le(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function ce(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?le(Object(n),!0).forEach((function(t){se(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):le(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function se(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==ae(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==ae(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===ae(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function de(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,i,r,a,l=[],c=!0,s=!1;try{if(r=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=r.call(n)).done)&&(l.push(o.value),l.length!==t);c=!0);}catch(e){s=!0,i=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw i}}return l}}(e,t)||pe(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pe(e,t){if(e){if("string"==typeof e)return ue(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ue(e,t):void 0}}function ue(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var me=window.Core.Tools.ToolNames,ye=window.Core.Annotations,fe=function(){var e=de(Object(a.a)(),1)[0],t=de(Object(r.e)((function(e){return[l.a.isElementOpen(e,"stylePanel"),l.a.getToolButtonObjects(e),l.a.isAnnotationToolStyleSyncingEnabled(e),l.a.getActiveDocumentViewerKey(e)]})),4),n=t[0],s=t[1],d=t[2],p=t[3],u=h.a.getToolMode(),m=null==u?void 0:u.name,y=["StrokeColor","FillColor"],f=de(Object(o.useState)(!1),2),S=f[0],b=f[1],g=de(Object(o.useState)(C(m)),2),x=g[0],w=g[1],T=de(Object(o.useState)(!1),2),E=T[0],P=T[1],F=de(Object(o.useState)(!1),2),j=F[0],L=F[1],R=de(Object(o.useState)(m===me.REDACTION),2),N=R[0],I=R[1],_=de(Object(o.useState)(m!==re.a&&O(m)),2),D=_[0],M=_[1],z=de(Object(o.useState)(!1),2),H=z[0],W=z[1],B=de(Object(o.useState)(!1),2),V=B[0],Y=B[1],U=de(Object(o.useState)(!1),2),q=U[0],K=U[1],X=de(Object(o.useState)(!1),2),G=X[0],ae=X[1],le=de(Object(o.useState)({}),2),ue=le[0],fe=le[1],Se=de(Object(o.useState)(),2),he=Se[0],be=Se[1],ge=de(Object(o.useState)(),2),xe=ge[0],ve=ge[1],we=de(Object(o.useState)(),2),Te=we[0],Ce=we[1],Ee=de(Object(o.useState)(e("stylePanel.headings.styles")),2),Pe=Ee[0],ke=Ee[1],Oe=Object(J.a)(),Ae=de(Object(o.useState)(m!==re.a&&Object(ne.e)(Object(ne.j)(m)).hasLineEndings),2),Fe=Ae[0],je=Ae[1],Le=de(Object(o.useState)(ue.isAutoSizeFont),2),Re=Le[0],Ne=Le[1],Ie=de(Object(o.useState)(m||"Edit"),2),_e=Ie[0],De=Ie[1],Me=de(Object(o.useState)(null),2),ze=Me[0],He=Me[1],We=Object(r.d)(),Be=[ye.PushButtonWidgetAnnotation];Object(o.useEffect)((function(){"AnnotationCreateRubberStamp"===(null==u?void 0:u.name)&&h.a.setToolMode(re.a),Ve(u)}),[u]);var Ve=function(e){if(h.a.isFullPDFEnabled()&&e&&e.getSnapMode){var t=!!e.getSnapMode();We(v.a.setEnableSnapMode({toolName:e.name,isEnabled:t}))}},Ye=function(t){var n;if(t.isContentEditPlaceholder())return ke("".concat(e("stylePanel.headings.contentEdit")," ").concat(e("stylePanel.headings.annotation"))),void w(!0);ke("".concat(e(k(t.ToolName,"Title")||(null===(n=s[t.ToolName])||void 0===n?void 0:n.title))," ").concat(e("stylePanel.headings.annotation")))},Ue=function(t){var n,o=t.name,i=null===(n=s[o])||void 0===n?void 0:n.title;ke("".concat(e(k(o,"Title")||i)," ").concat(e("stylePanel.headings.tool")))},qe=function(e){var t={};if(e instanceof ye.FreeTextAnnotation){var n="solid";try{n="dash"===e.Style?"".concat(e.Style,",").concat(e.Dashes):e.Style}catch(e){console.error(e)}t.TextColor=e.TextColor,t.RichTextStyle=e.getRichTextStyle(),t.Font=e.Font,t.FontSize=e.FontSize,t.TextAlign=e.TextAlign,t.TextVerticalAlign=e.TextVerticalAlign,t.calculatedFontSize=e.getCalculatedFontSize(),t.StrokeStyle=n,t.isAutoSizeFont=e.isAutoSizeFont(),Ne(e.isAutoSizeFont());var o=function(e,t){var n=new Set,o=new Set;for(var i in e)if(e.hasOwnProperty(i)){var r=parseInt(i,10);!isNaN(r)&&" "!==t[r]&&e[i]["font-family"]&&n.add(e[i]["font-family"].trim()),!isNaN(r)&&" "!==t[r]&&e[i]["font-size"]&&o.add(e[i]["font-size"].trim())}return{fonts:Array.from(n),sizes:Array.from(o)}}(t.RichTextStyle,e.getContents()),i=o.fonts,r=o.sizes;(i.length>=2||1===i.length&&i[0]!==t.Font)&&(t.Font=void 0),(r.length>=2||1===r.length&&r[0]!==t.FontSize)&&(t.FontSize=void 0)}e instanceof ye.RedactionAnnotation&&(t.OverlayText=e.OverlayText,t.Font=e.Font,t.FontSize=e.FontSize,t.TextAlign=e.TextAlign),e instanceof ye.WidgetAnnotation&&void 0!==e.FontSize&&(t.FontSize=e.FontSize),fe(ce(ce({},ue),{},{StrokeColor:e.StrokeColor,StrokeThickness:e.StrokeThickness,Opacity:e.Opacity,FillColor:e.FillColor},t)),be(e.getStartStyle?e.getStartStyle():"None"),ve(e.getEndStyle?e.getEndStyle():"None"),Ce(function(e){var t=e.Style,n=e.Dashes;return"dash"!==t?t:"".concat(t,",").concat(n)}(e))};Object(o.useEffect)((function(){var t=function(e){if(Oe.includes(null==e?void 0:e.name))if(Pe){if(C(null==e?void 0:e.name))return w(!0),b(!0),void Ue(e);w(!1),De(e.name),je(Object(ne.e)(Object(ne.j)(e.name)).hasLineEndings),P(e.name===me.ELLIPSE),L(e.name===me.FREETEXT),I(e.name===me.REDACTION),M(O(e.name)),W(e.name===me.FREEHAND||e.name===me.FREEHAND_HIGHLIGHT),Y(e.name===me.ARC),K(e.name===me.STAMP),ae(h.a.getFormFieldCreationManager().isInFormFieldCreationMode());var t=Object(Q.a)(e.name);(e.name.includes("FreeText")||e.name.includes("Callout"))&&(t.isAutoSizeFont=e.defaults.isAutoSizeFont,Ne(e.defaults.isAutoSizeFont)),fe(t),be(t.StartLineStyle),Ce(t.StrokeStyle),ve(t.EndLineStyle),b(!0),Ue(e)}else b(!1);else b(!1)},n=function(n,o){if("selected"===o){if(A(n,Be))return w(!1),void b(!1);b(!0),1===n.length?(i=n[0],Ye(i),C(i.ToolName)?w(!0):(w(!1),De(i.ToolName),P(i instanceof ye.EllipseAnnotation),L(i instanceof ye.FreeTextAnnotation),I(i instanceof ye.RedactionAnnotation),M(O(i.ToolName)),W(i instanceof ye.FreeHandAnnotation),Y(i instanceof ye.ArcAnnotation),K(i instanceof ye.StampAnnotation),ae(h.a.getFormFieldCreationManager().isInFormFieldCreationMode()),je(Object(ne.e)(Object(ne.j)(i.ToolName)).hasLineEndings),qe(i))):function(t){ke("".concat(e("stylePanel.headings.annotations")," (").concat(t.length,")")),t.forEach((function(e){qe(e)}))}(n)}else"deselected"===o&&function(){var e=h.a.getToolMode();e instanceof window.Core.Tools.AnnotationEditTool&&b(!1),t(e),h.a.setToolMode(e.name)}();var i},o=function(){var e,t=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=pe(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var o=0,i=function(){};return{s:i,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,a=!0,l=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){l=!0,r=e},f:function(){try{a||null==n.return||n.return()}finally{if(l)throw r}}}}(h.a.getSelectedAnnotations());try{for(t.s();!(e=t.n()).done;){var n=e.value;qe(n)}}catch(e){t.e(e)}finally{t.f()}};return h.a.addEventListener("annotationSelected",n),h.a.addEventListener("toolModeUpdated",t),h.a.addEventListener("annotationChanged",o),function(){h.a.removeEventListener("annotationSelected",n),h.a.removeEventListener("toolModeUpdated",t),h.a.removeEventListener("annotationChanged",o)}}),[]);Object(o.useEffect)((function(){if(n){var t=h.a.getSelectedAnnotations();if(A(t,Be))return w(!1),void b(!1);if(1===t.length){b(!0);var o=t[0];qe(o),function(e){P(e instanceof ye.EllipseAnnotation),L(e instanceof ye.FreeTextAnnotation),I(e instanceof ye.RedactionAnnotation),W(e instanceof ye.FreeHandAnnotation),Y(e instanceof ye.ArcAnnotation),K(e instanceof ye.StampAnnotation),je(Object(ne.e)(Object(ne.j)(e.ToolName)).hasLineEndings)}(o),Ye(o)}else if(t.length>1)b(!0),ke("".concat(e("stylePanel.headings.annotations")," (").concat(t.length,")")),t.forEach((function(e){qe(e)}));else{var i=h.a.getToolMode();if(i&&i.name!==re.a){b(!0);var r=Object(Q.a)(i.name);r&&(fe(r),be(r.StartLineStyle),ve(r.EndLineStyle),Ce(r.StrokeStyle)),Ue(i)}}}}),[n,Re]);var Ke=i.a.createElement(i.a.Fragment,null,i.a.createElement("h2",{className:"style-panel-header"},e("stylePanel.headings.styles")),i.a.createElement("div",{className:"no-tool-selected"},i.a.createElement("div",null,i.a.createElement(c.a,{className:"empty-icon",glyph:"style-panel-no-tool-selected"})),i.a.createElement("div",{className:"msg"},e("stylePanel.noToolSelected"))));return S?i.a.createElement(i.a.Fragment,null,i.a.createElement("h2",{className:"style-panel-header"},Pe),x?i.a.createElement("div",{className:"no-tool-selected"},i.a.createElement("div",null,i.a.createElement(c.a,{className:"empty-icon",glyph:"style-panel-no-tool-selected"})),i.a.createElement("div",{className:"msg"},e("stylePanel.noToolStyle"))):i.a.createElement($,{sliderProperties:["Opacity","StrokeThickness"],style:ue,onStyleChange:function(e,t){var n=ce({},ue);n[e]=t,fe(n);var o=h.a.getSelectedAnnotations();if(0===o.length&&ze&&"FillColor"===e){var i=ze[0];if(null!=i&&i.hasFocus()){var r=ze[1];return i.setStyle({background:t}),void(r.FillColor=new ye.Color(t))}}if(o.length>0)o.forEach((function(n){if(y.includes(e)){var o=Object(Z.b)(t),i=new ye.Color(o.r,o.g,o.b,o.a);h.a.setAnnotationStyles(n,se({},e,i),p),d&&Object(ee.a)(n.ToolName,e,i)}else h.a.setAnnotationStyles(n,se({},e,t),p),n instanceof ye.FreeTextAnnotation&&("FontSize"!==e&&"Font"!==e&&"StrokeThickness"!==e||Object(te.a)(n)),d&&Object(ee.a)(n.ToolName,e,t);h.a.getAnnotationManager().redrawAnnotation(n),n instanceof ye.WidgetAnnotation&&n.refresh()}));else{var a=h.a.getToolMode();if(a)if(y.includes(e)){var l=Object(Z.b)(t),c=new ye.Color(l.r,l.g,l.b,l.a);Object(ee.a)(a.name,e,c)}else"Opacity"===e?Object(ee.a)(a.name,"Opacity",t):"StrokeThickness"===e?Object(ee.a)(a.name,"StrokeThickness",t):Object(ee.a)(a.name,e,t)}},isFreeText:j,isEllipse:E,isRedaction:N,isWidget:D,isFreeHand:H,isArc:V,isStamp:q,isInFormFieldCreationMode:G,showLineStyleOptions:Fe,startLineStyle:he,endLineStyle:xe,strokeStyle:Te,onLineStyleChange:function(e,t){var n={start:"StartLineStyle",middle:"StrokeStyle",end:"EndLineStyle"};"start"===e?be(t):"middle"===e?Ce(t):"end"===e&&ve(t);var o=h.a.getSelectedAnnotations();if(o.length>0)o.forEach((function(o){if("start"===e)o.setStartStyle(t);else if("middle"===e){var i=t.split(","),r=i.shift();o.Style=r,o.Dashes=i}else"end"===e&&o.setEndStyle(t);h.a.getAnnotationManager(p).redrawAnnotation(o),d&&Object(ee.a)(o.ToolName,n[e],t)})),h.a.getAnnotationManager(p).trigger("annotationChanged",[o,"modify",{}]);else{var i=h.a.getToolMode();i&&Object(ee.a)(i.name,n[e],t)}},onFreeTextSizeToggle:function(){var e=h.a.getSelectedAnnotations()[0];if(e)Object(oe.a)(e,Ne,Re);else{var t=h.a.getToolMode();t&&(Object(ee.a)(t.name,"isAutoSizeFont",!ue.isAutoSizeFont),Ne(!Re))}},isFreeTextAutoSize:Re,handleRichTextStyleChange:function(e,t){var n,o=e,i=t,r=null===(n=ue.RichTextStyle)||void 0===n?void 0:n[0];"underline"!==e&&"line-through"!==e||(t=Object(ie.a)(se({},e,t),r),e="text-decoration");var a={0:ce(ce({},r),{},se({},e,t))},l=h.a.getSelectedAnnotations();if(l.length>0)l.forEach((function(e){h.a.updateAnnotationRichTextStyle(e,se({},o,i),p)})),fe(ce(ce({},ue),{},{RichTextStyle:a}));else{var c=h.a.getToolMode();c&&("function"==typeof c.complete&&c.complete(),Object(ee.a)(c.name,"RichTextStyle",a))}},activeTool:_e,saveEditorInstance:He})):Ke},Se=n(76),he=(n(1690),function(){return i.a.createElement(Se.a,{dataElement:"stylePanel",className:"Panel StylePanel"},i.a.createElement(fe,null))});t.default=he}}]);
//# sourceMappingURL=chunk.37.js.map