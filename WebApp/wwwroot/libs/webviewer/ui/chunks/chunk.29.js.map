{"version": 3, "sources": ["webpack:///./src/ui/src/components/Note/Context.js", "webpack:///./src/ui/src/helpers/setAnnotationRichTextStyle.js", "webpack:///./src/ui/src/helpers/convertNewlinesToParagraphs.js", "webpack:///./src/ui/src/helpers/quillModules.js", "webpack:///./src/ui/src/components/NoteTextarea/CommentTextarea/CommentTextarea.js", "webpack:///./src/ui/src/components/NoteTextarea/NoteTextarea.js", "webpack:///./src/ui/src/components/NoteTextarea/index.js", "webpack:///./src/ui/src/helpers/NoteStateUtils.js", "webpack:///./src/ui/src/components/NoteTextPreview/NoteTextPreview.js", "webpack:///./src/ui/src/components/ModularComponents/NoteStateFlyout/NoteStateFlyout.js", "webpack:///./src/ui/src/components/ModularComponents/NoteStateFlyout/index.js", "webpack:///./src/ui/src/components/NoteTextPreview/NoteTextPreview.scss?2f3f", "webpack:///./src/ui/src/components/NoteTextPreview/NoteTextPreview.scss", "webpack:///./src/ui/src/constants/quill.scss?ee40", "webpack:///./src/ui/src/constants/quill.scss", "webpack:///./src/ui/src/components/NoteTextarea/CommentTextarea/CommentTextarea.scss?e393", "webpack:///./src/ui/src/components/NoteTextarea/CommentTextarea/CommentTextarea.scss", "webpack:///./src/ui/src/components/NotePopup/NotePopup.scss?1d48", "webpack:///./src/ui/src/components/NotePopup/NotePopup.scss", "webpack:///./src/ui/src/components/NoteHeader/NoteHeader.scss?f278", "webpack:///./src/ui/src/components/NoteHeader/NoteHeader.scss", "webpack:///./src/ui/src/components/ReplyAttachmentList/ReplyAttachmentList.scss?6b77", "webpack:///./src/ui/src/components/ReplyAttachmentList/ReplyAttachmentList.scss", "webpack:///./src/ui/src/components/NoteContent/NoteContent.scss?62b0", "webpack:///./src/ui/src/components/NoteContent/NoteContent.scss", "webpack:///./src/ui/src/components/Note/ReplyArea/ReplyArea.scss?59a5", "webpack:///./src/ui/src/components/Note/ReplyArea/ReplyArea.scss", "webpack:///./src/ui/src/components/AnnotationNoteConnectorLine/AnnotationNoteConnectorLine.scss?7f2c", "webpack:///./src/ui/src/components/AnnotationNoteConnectorLine/AnnotationNoteConnectorLine.scss", "webpack:///./src/ui/src/components/Note/Note.scss?66f6", "webpack:///./src/ui/src/components/Note/Note.scss", "webpack:///./src/ui/src/components/NotesPanel/ReplyAttachmentPicker.js", "webpack:///./src/ui/src/components/NotePopup/NotePopup.js", "webpack:///./src/ui/src/components/NotePopup/NotePopupContainer.js", "webpack:///./src/ui/src/components/NotePopup/index.js", "webpack:///./src/ui/src/components/NoteState/NoteState.js", "webpack:///./src/ui/src/components/NoteState/NoteStateContainer.js", "webpack:///./src/ui/src/components/NoteState/index.js", "webpack:///./src/ui/src/components/NoteUnpostedCommentIndicator/NoteUnpostedCommentIndicator.js", "webpack:///./src/ui/src/components/NoteUnpostedCommentIndicator/NoteUnpostedCommentIndicatorContainer.js", "webpack:///./src/ui/src/components/NoteUnpostedCommentIndicator/index.js", "webpack:///./src/ui/src/components/NoteHeader/NoteHeader.js", "webpack:///./src/ui/src/components/NoteHeader/index.js", "webpack:///./src/ui/src/components/NoteTextPreview/NoteTextPreviewContainer.js", "webpack:///./src/ui/src/components/NoteTextPreview/index.js", "webpack:///./src/ui/src/helpers/ReplyAttachmentManager.js", "webpack:///./src/ui/src/helpers/sanitizeSVG.js", "webpack:///./src/ui/src/components/ReplyAttachmentList/ReplyAttachmentList.js", "webpack:///./src/ui/src/components/ReplyAttachmentList/index.js", "webpack:///./src/ui/src/helpers/setReactQuillContent.js", "webpack:///./src/ui/src/components/NoteContent/NoteContent.js", "webpack:///./src/ui/src/components/NoteContent/index.js", "webpack:///./src/ui/src/components/Note/ReplyArea/ReplyArea.js", "webpack:///./src/ui/src/components/Note/ReplyArea/index.js", "webpack:///./src/ui/src/components/Note/NoteGroupSection.js", "webpack:///./src/ui/src/components/AnnotationNoteConnectorLine/AnnotationNoteConnectorLine.js", "webpack:///./src/ui/src/components/AnnotationNoteConnectorLine/index.js", "webpack:///./src/ui/src/components/Note/Note.js", "webpack:///./src/ui/src/components/Note/index.js", "webpack:///./src/ui/src/components/InlineCommentingPopup/InlineCommentingPopup.scss?7354", "webpack:///./src/ui/src/components/InlineCommentingPopup/InlineCommentingPopup.scss", "webpack:///./src/ui/src/components/InlineCommentingPopup/InlineCommentingPopup.js", "webpack:///./src/ui/src/components/InlineCommentingPopup/InlineCommentingPopupContainer.js", "webpack:///./src/ui/src/components/InlineCommentingPopup/index.js"], "names": ["NoteContext", "React", "createContext", "setAnnotationRichTextStyle", "editor", "annotation", "richTextStyle", "ops", "getContents", "breakpoint", "for<PERSON>ach", "item", "attributes", "isMention", "insert", "mention", "value", "denotationChar", "cssStyle", "undefined", "bold", "italic", "color", "underline", "strike", "size", "font", "length", "setRichTextStyle", "convertNewlinesToParagraphs", "valueSplit", "split", "map", "text", "join", "Clipboard", "<PERSON><PERSON><PERSON>", "quillShadowDOMWorkaround", "window", "Core", "QuillPasteExtra", "quill", "options", "Keyboard", "CustomKeyboard", "DEFAULTS", "bindings", "BlurInputModule", "event", "key", "blurQuill", "shouldSkipInput", "preventDefault", "moveFocus", "shift<PERSON>ey", "this", "noteContainer", "root", "closest", "addEventListener", "handleKeyDown", "bind", "blur", "container", "tabIndex", "focus", "backwards", "focusableElements", "Array", "from", "querySelectorAll", "currentIndex", "indexOf", "i", "globalUserData", "formats", "register", "mentionModule", "<PERSON><PERSON><PERSON><PERSON>", "mentionDenotationChars", "mentionContainerClass", "mentionListClass", "listItemClass", "renderItem", "div", "document", "createElement", "innerText", "email", "para", "className", "append<PERSON><PERSON><PERSON>", "source", "searchTerm", "renderList", "mentionsSearchFunction", "mentions<PERSON>anager", "getMentionLookupCallback", "foundUsers", "CommentTextarea", "forwardRef", "ref", "onChange", "onKeyDown", "onBlur", "onFocus", "userData", "isReply", "t", "useTranslation", "isAddReplyAttachmentDisabled", "useSelector", "state", "selectors", "isElementDisabled", "DataElements", "NotesPanel", "ADD_REPLY_ATTACHMENT_BUTTON", "transformTextForQuill", "baseModule", "blurInput", "onClick", "e", "stopPropagation", "onScroll", "style", "overflowY", "ele", "getEditor", "aria<PERSON><PERSON><PERSON>", "modules", "theme", "placeholder", "<PERSON><PERSON>", "dataElement", "img", "title", "getRootNode", "querySelector", "click", "displayName", "propTypes", "PropTypes", "string", "func", "isRequired", "onSubmit", "NoteTextarea", "props", "forwardedRef", "getUserData", "isNoteSubmissionWithEnterEnabled", "getAutoFocusNoteOnAnnotationSelection", "getIsNoteEditing", "shallowEqual", "canSubmitByEnter", "resize", "useContext", "textareaRef", "useRef", "prevHeightRef", "useLayoutEffect", "boxDOMElement", "current", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "boundingBox", "getBoundingClientRect", "height", "textareaProps", "el", "throttle", "content", "delta", "replace", "getText", "trim", "target", "doesDeltaContainMention", "formattedText", "getFormattedTextFromDeltas", "mentionData", "extractMentionDataFromStr", "totalTextLength", "plainTextValue", "textareaEditor", "setTimeout", "setSelection", "which", "isSubmittingByEnter", "isSubmittingByCtrlEnter", "metaKey", "ctrl<PERSON>ey", "createStateAnnotation", "documentViewerKey", "stateAnnotation", "Annotations", "StickyAnnotation", "core", "getCurrentUser", "enableSkipAutoLink", "displayAuthor", "getDisplayAuthor", "stateMessage", "i18next", "toLowerCase", "contents", "setContents", "NoteTextPreview", "children", "panelWidth", "linesToBreak", "renderRichText", "comment", "beforeContent", "useState", "expanded", "setExpand", "previewElementWidth", "setPreviewWidth", "charsPerLine", "setCharsperLine", "showPrompt", "setShowPrompt", "textToDisplay", "substring", "prompt", "noteTextPreviewClass", "classNames", "useEffect", "textNodeWidth", "clientWidth", "textWidth", "context", "getContext", "measureText", "width", "getTextWidth", "averageCharWidth", "Math", "floor", "aria-live", "number", "any", "bool", "createFlyoutItem", "option", "icon", "label", "noteStateFlyoutItems", "NoteStateFlyout", "noteId", "handleStateChange", "isMultiSelectMode", "dispatch", "useDispatch", "selectorSuffix", "flyoutSelector", "NOTE_STATE_FLYOUT", "currentFlyout", "getFlyout", "noteStateFlyout", "items", "noteState", "actions", "updateFlyout", "addFlyout", "api", "__esModule", "default", "module", "styleTag", "isApryseWebViewerWebComponent", "head", "webComponents", "getElementsByTagName", "findNestedWebComponents", "tagName", "elements", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "ReplyAttachmentPicker", "annotationId", "addAttachments", "replyAttachmentHandler", "getReplyAttachmentHandler", "file", "files", "attachment", "url", "name", "type", "id", "display", "notePopupFlyoutItems", "handleEdit", "handleDelete", "isEditable", "isDeletable", "noop", "NotePopup", "customizableUI", "getFeatureFlags", "NOTE_POPUP_FLYOUT", "notePopupButtonClass", "optionsClass", "ToggleElementButton", "toggleElement", "disabled", "handleClick", "selection", "NotePopupFlyout", "filter", "notePopupFlyout", "NotePopupContainer", "activeDocumentViewerKey", "getActiveDocumentViewerKey", "setIsEditing", "noteIndex", "canModify", "setCanModify", "canModifyContents", "setCanModifyContents", "onUpdateAnnotationPermission", "removeEventListener", "passProps", "useCallback", "FreeTextAnnotation", "getAnnotationManager", "isFreeTextEditingEnabled", "trigger", "deleteAnnotations", "getGroupedChildren", "NoDelete", "Id", "object", "NoteState", "annotationState", "getStatus", "NoteStateContainer", "isNoteStateDisabled", "useFocusOnClose", "newValue", "addReply", "annotationManager", "addAnnotation", "getRootAnnotation", "pendingEditTextMap", "pendingReplyMap", "pendingAttachmentMap", "NoteUnpostedCommentIndicator", "hasUnpostedComment", "setHasUnpostedComment", "hasUnpostedReply", "setHasUnpostedReply", "hasUnpostedAttachment", "setHasUnpostedAttachment", "data-element", "<PERSON><PERSON><PERSON>", "Icon", "glyph", "NoteUnpostedCommentIndicatorContainer", "isDisabled", "iconColor", "fillColor", "language", "noteDateFormat", "isSelected", "notesShowLastUpdatedDate", "isUnread", "renderAuthorName", "isEditing", "sortStrategy", "activeTheme", "isMultiSelected", "handleMultiSelect", "isGroupMember", "showAnnotationNumbering", "isTrackedChange", "<PERSON><PERSON><PERSON><PERSON>", "date", "timezone", "dateCreated", "NotesPanelSortStrategy", "MODIFIED_DATE", "CREATED_DATE", "getLatestActivityDate", "DateCreated", "datetimeStr", "toLocaleString", "timeZone", "Date", "noteDateAndTime", "dayjs", "locale", "format", "numberOfReplies", "getReplies", "toHexString", "Theme", "DARK", "isDarkColorHex", "COMMON_COLORS", "LIGHT", "isLightColorHex", "getColor", "FillColor", "annotationAssociatedNumber", "getAssociatedNumber", "annotationDisplayedAssociatedNumber", "authorAndDateClass", "noteHeaderClass", "parent", "PageNumber", "Choice", "aria-label", "checked", "trackedChangeId", "getCustomData", "OFFICE_EDITOR_TRACKED_CHANGE_KEY", "getOfficeEditor", "acceptTrackedChange", "iconClassName", "rejectTrackedChange", "NoteTextPreviewContainer", "notePanelWidth", "getNotesPanelWidth", "icons", "FileAttachmentUtils", "decompressFileContent", "decompressWithFlateDecode", "setAnnotationAttachments", "setAttachments", "isImage", "startsWith", "getAttachmentIcon", "pop", "readAsText", "svg", "Promise", "resolve", "toString", "fileReader", "FileReader", "result", "isSVG", "sanitizeSVG", "svgText", "forbiddenTags", "DOMPurify", "addHook", "_", "hookEvent", "allowedTags", "clean", "sanitize", "Blob", "isDirty", "ImagePreview", "src", "setSrc", "isDirtySVG", "setIsDirtySvg", "fileToSanitize", "isImageFromPDF", "File", "URL", "createObjectURL", "processImagePreview", "ReplyAttachmentList", "fileDeleted", "getTabManager", "isReplyAttachmentPreviewEnabled", "tabManager", "previewEnabled", "console", "warn", "fileData", "addTab", "filename", "setActive", "saveCurrentActiveTabState", "onDownload", "saveAs", "onDelete", "getAttributtes", "element", "attr", "decoration", "includes", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nt", "getRichTextStyle", "indexes", "Object", "keys", "isNaN", "lastIndex", "textSlice", "slice", "extend", "LocalizedFormat", "isNonReplyNoteRead", "onReplyClicked", "handleNoteClick", "<PERSON><PERSON><PERSON><PERSON>", "getNoteDateFormat", "getIconColor", "mapAnnotationToKey", "getCurrentLanguage", "canCollapseTextPreview", "isNotesPanelTextCollapsingEnabled", "canCollapseReplyPreview", "isNotesPanelRepliesCollapsingEnabled", "getActiveTheme", "getTimezone", "searchInput", "onTopNoteContentClicked", "setPendingEditText", "annotationMapKeys", "TRACKED_CHANGE", "attachments", "getAttachments", "annotationChangedListener", "annotations", "action", "annot", "useDidUpdate", "finishNoteEditing", "customData", "highlightSearchInput", "skipAutoLink", "getSkipAutoLink", "renderContents", "fontColor", "autolinkerContent", "Autolinker", "link", "stripPrefix", "stripTrailingSlash", "replaceFn", "match", "href", "getAnchorHref", "anchorText", "getAnchorText", "offset", "getOffset", "getType", "start", "end", "getMatchedText", "highlightResult", "fontWeight", "contentToRender", "strIdx", "anchorData", "forIdx", "rel", "getDataWithKey", "JSON", "parse", "escape", "sanitizeContent", "contentsToRender", "textColor", "Color", "a", "textAreaValue", "thereIsNoUnpostedEdit", "handleContentsClicked", "getSelection", "noteContentClass", "unread", "clicked", "useMemo", "contentStyle", "onTextAreaValueChange", "pendingText", "textPreview", "highlightSearchResult", "shouldCollapseAnnotationText", "isString", "DataElementWrapper", "paddingRight", "header", "ContentArea", "getIsMentionEnabled", "INLINE_COMMENT_POPUP", "isElementOpen", "NOTES_PANEL", "isAnyCustomPanelOpen", "autoFocusNoteOnAnnotationSelection", "isMentionEnabled", "isInlineCommentDisabled", "isInlineCommentOpen", "isNotesPanelOpen", "setCurAnnotId", "deleteAttachment", "clearAttachments", "shouldNotFocusOnInput", "isMobile", "setText", "ids", "textLength", "<PERSON><PERSON><PERSON><PERSON>", "pendingAttachments", "disableSkipAutoLink", "extractMentionDataFromAnnot", "mentions", "setCustomData", "stringify", "drawAnnotationsFromList", "contentClassName", "relatedTarget", "getAttribute", "getRichTextSpan", "fontStyle", "textDecoration", "styles", "indices", "Number", "sort", "b", "index", "min", "max", "styleIndices", "fullText", "loweredText", "loweredSearchInput", "lastFoundInstance", "allFoundPositions", "regexSafeSearchInput", "RegExp", "test", "position", "idx", "ReplyArea", "onPendingReplyChange", "isDocumentReadOnly", "getIsReplyDisabled", "isReadOnly", "isReplyDisabled", "isReplyDisabledForAnnotation", "isNoteEditingTriggeredByAnnotationPopup", "isContentEditable", "setPendingReply", "isExpandedFromSearch", "scrollToSelectedAnnot", "isFocused", "setIsFocused", "postReply", "replyText", "replyAnnotation", "createMentionReply", "addAnnotations", "createAnnotationReply", "ifReplyNotAllowed", "replyAreaClass", "onMouseDown", "handleNoteTextareaChange", "isSubmitType", "groupAnnotations", "array", "NoteGroupSection", "isViewingGroupAnnots", "setIsViewingGroupAnnots", "ViewAllAnnotsButton", "CloseAllAnnotsButton", "groupAnnotation", "selectAnnotation", "jumpToAnnotation", "openElement", "LineConnectorPortal", "mount", "setAttribute", "ANNOTATION_NOTE_CONNECTOR_LINE", "<PERSON><PERSON><PERSON><PERSON>", "createPortal", "AnnotationNoteConnectorLine", "noteContainerRef", "isCustomPanelOpen", "getDocumentContainerWidth", "getDocumentContainerHeight", "lineIsOpen", "notePanelIsOpen", "isLineDisabled", "documentContainerWidth", "documentContainerHeight", "rightHorizontalLineWidth", "setRightHorizontalLineWidth", "rightHorizontalLineTop", "setRightHorizontalLineTop", "rightHorizontalLineRight", "setRightHorizontalLineRight", "leftHorizontalLineWidth", "setLeftHorizontalLineWidth", "leftHorizontalLineTop", "setLeftHorizontalLineTop", "leftHorizontalLineRight", "setLeftHorizontalLineRight", "getAnnotationPosition", "annotationBottomRight", "bottomRight", "annotationTopLeft", "topLeft", "getAnnotationLineOffset", "Subject", "getScrollViewElement", "scrollTop", "scrollLeft", "closeElement", "annotWidthInPixels", "x", "annotHeightInPixels", "y", "viewerWidth", "host", "innerWidth", "viewerOffsetTop", "offsetTop", "top", "lineWidth", "noZoomRefPoint", "getNoZoomReferencePoint", "noZoomRefShiftX", "NoZoom", "noZoomRefShiftY", "onPageNumberUpdated", "verticalHeight", "abs", "verticalTop", "right", "isInNotesPanel", "currId", "Note", "shouldHideConnectorLine", "containerRef", "containerHeightRef", "isEditingMap", "setIsEditingMap", "unreadReplyIdSet", "Set", "getNoteTransformFunction", "getCustomNoteSelectionFunction", "getUnreadAnnotationIdSet", "isCommentThreadExpansionEnabled", "isRightClickAnnotationPopupEnabled", "getIsOfficeEditorMode", "getOfficeEditorEditMode", "noteTransformFunction", "customNoteSelectionFunction", "unreadAnnotationIdSet", "shouldExpandCommentThread", "isOfficeEditorMode", "officeEditorEditMode", "replies", "r", "has", "add", "setAnnotationReadState", "isRead", "prevHeight", "currHeight", "round", "notesPanelElement", "child", "parentNode", "deselectAllAnnotations", "OfficeEditorEditMode", "PREVIEW", "ANNOTATION_POPUP", "moveCursorToTrackedChange", "freezeMainCursor", "hasUnreadReplies", "noteClass", "repliesClass", "hidden", "reply", "showReplyArea", "values", "some", "val", "handleReplyClicked", "markAllRepliesRead", "repliesSetToRead", "selectAnnotations", "getGroupAnnotations", "isGroup", "lastReplyId", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON>urrent", "isUndraggable", "isNotesPanelClosed", "popupRef", "closeAndReset", "commentingAnnotation", "contextValue", "annotationForAttachment", "InlineCommentingPopup", "isExpanded", "setExpanded", "inlineCommentPopup", "Popup", "open", "trackedChangePopup", "onMouseMove", "INLINE_COMMENT_POPUP_EXPAND_BUTTON", "INLINE_COMMENT_POPUP_CLOSE_BUTTON", "Provider", "cancel", "InlineCommentingPopupContainer", "getNotesInLeftPanel", "LEFT_PANEL", "getActiveLeftPanel", "isAnnotationNumberingEnabled", "getSortStrategy", "notesInLeftPanel", "isLeftPanelOpen", "activeLeftPanel", "left", "setPosition", "isPhone", "isMobileDevice", "isIE", "isNotesPanelOpenOrActive", "useOnClickOutside", "notesPanel", "clickedInNotesPanel", "contains", "clickedInNoteStateFlyout", "datePicker", "getDatePicker", "warningModal", "getOpenedWarningModal", "colorPicker", "getOpenedColorPicker", "setPopupPosition", "getPopupPosition", "handleResize", "debounce", "setPendingAttachmentMap", "annotationID", "setAnnotationForAttachment", "setPendingEditTextMap", "setPendingReplyMap", "pendingReply", "getDocument", "workerTypes", "OFFICE_EDITOR", "attachmentList", "splice"], "mappings": "4FAAA,WAEMA,EAFN,OAEoBC,EAAMC,gBAEXF,O,wECyCAG,IA7CoB,SAACC,EAAQC,GAC1C,IAAMC,EAAgB,GAChBC,EAAMH,EAAOI,cAAcD,IAC7BE,EAAa,EACjBF,EAAIG,SAAQ,SAACC,GAAS,MACdC,EAAaD,EAAKC,WAClBC,EAAuB,QAAd,EAAGF,EAAKG,cAAM,aAAX,EAAaC,QAC3BC,EAAQL,EAAKG,OACjB,GAAID,EAAW,CACb,IAAME,EAAUJ,EAAKG,OAAOC,QAC5BC,EAAQD,EAAQE,eAAiBF,EAAQC,MAE3C,IAAME,EAAW,IACbN,aAAkDO,EAAYP,EAAWQ,QAC3EF,EAAS,eAAiB,SAExBN,aAAkDO,EAAYP,EAAWS,UAC3EH,EAAS,cAAgB,WAEvBN,aAAkDO,EAAYP,EAAWU,SAC3EJ,EAAgB,MAAIN,EAAWU,QAE7BV,aAAkDO,EAAYP,EAAWW,aAC3EL,EAAS,mBAAqB,SAE5BN,aAAkDO,EAAYP,EAAWY,UACvEN,EAAS,mBACXA,EAAS,mBAAqB,GAAH,OAAMA,EAAS,mBAAkB,iBAE5DA,EAAS,mBAAqB,iBAG9BN,aAAkDO,EAAYP,EAAWa,QAC3EP,EAAS,aAAeN,EAAWa,OAEjCb,aAAkDO,EAAYP,EAAWc,QAC3ER,EAAS,eAAiBN,EAAWc,MAGvCpB,EAAcG,GAAcS,EAC5BT,GAAcO,EAAMW,UAEtBtB,EAAWuB,iBAAiBtB,K,4ZClBfuB,MAdf,SAAqCb,GACnC,IAAKA,EACH,OAAOA,EAET,IAAMc,EAAad,EAAMe,MAAM,MAE/B,OADyBD,EAAWH,OAAS,EAKxBG,EACDE,KAAI,SAACC,GAAI,mBAAWA,GAAQ,OAAM,WAAQC,KAAK,IAJ1DlB,G,k1FCdX,IAAMmB,EAAYC,QAAK,OAAQ,qBACvBC,EAA6BC,OAAOC,KAApCF,yBAEKG,EAAe,8BAC1B,WAAYC,EAAOC,GACe,OADN,UAC1BL,EAAyBI,GAAO,YAC1BA,EAAOC,GACd,YAJyB,CAASP,GAQ/BQ,EAAWP,QAAK,OAAQ,oBAEjBQ,EAAc,iGAASD,GAQnC,EARYC,EAAc,kBAEpBD,EAASE,UAAQ,IACpBC,SAAU,EAAF,KACHH,EAASE,SAASC,UAAQ,IAC7B,qBAAiB3B,OAKhB,IAAM4B,EAAe,WAC1B,WAAYN,GAAO,+CAQH,SAACO,GACG,WAAdA,EAAMC,IACR,EAAKC,YACkB,QAAdF,EAAMC,KAAiB,EAAKE,kBACrCH,EAAMI,iBACN,EAAKC,UAAUL,EAAMM,cAZvBC,KAAKd,MAAQA,EACbc,KAAKC,cAAgBf,EAAMgB,KAAKC,QAAQ,SACxCH,KAAKJ,iBAAkB,EAEL,QAAlB,EAAAI,KAAKC,qBAAa,OAAlB,EAAoBG,iBAAiB,UAAWJ,KAAKK,cAAcC,KAAKN,OAiCzE,OAhCA,4BAWD,WACEA,KAAKJ,iBAAkB,EACvBI,KAAKd,MAAMqB,OACXP,KAAKd,MAAMsB,UAAUC,SAAW,EAChCT,KAAKd,MAAMsB,UAAUE,UACtB,uBAED,SAAUC,GAER,IAKyB,EALnBC,EAAoBC,MAAMC,KAC9Bd,KAAKC,cAAcc,iBAAiB,6CAEhCC,EAAeJ,EAAkBK,QAAQjB,KAAKd,MAAMsB,YAEpC,IAAlBQ,IAEyB,QAA3B,EAAAJ,EADiBD,EAAYK,EAAe,EAAIA,EAAe,UACpC,OAA3B,EAA6BN,SAG/BV,KAAKJ,iBAAkB,EACvBI,KAAKd,MAAMsB,UAAUC,UAAY,MAClC,EAvCyB,G,m7ECzB5B,8lGAAAS,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,+XAcA,IAAIC,EAAiB,GAIfC,EAAU,CACd,aACA,OACA,QACA,OACA,OACA,SACA,OACA,OACA,SACA,SACA,YACA,aACA,SACA,SACA,OACA,QACA,YACA,aACA,UACA,WAGFvC,QAAMwC,SAAS,mBAAoBhC,GAAgB,GACnDR,QAAMwC,SAAS,oBAAqBpC,GAAiB,GACrDJ,QAAMwC,SAAS,oBAAqB7B,GAGpC,IAAM8B,EAAgB,CACpB9D,QAAS,CACP+D,aAAc,4BACdC,uBAAwB,CAAC,IAAK,KAC9BC,sBAAuB,mBACvBC,iBAAkB,6BAClBC,cAAe,6BACfC,WAAU,SAACxE,GAET,IAAMyE,EAAMC,SAASC,cAAc,OAEnC,GADAF,EAAIG,UAAY5E,EAAKK,MACjBL,EAAK6E,MAAO,CACd,IAAMC,EAAOJ,SAASC,cAAc,KACpCG,EAAKF,UAAY5E,EAAK6E,MACtBC,EAAKC,UAAY,QACjBN,EAAIO,YAAYF,GAElB,OAAOL,GAEHQ,OAAM,SAACC,EAAYC,GAAY,OAjEzC,EAiEyC,gGACsC,OAAnEC,EAAyBC,IAAgBC,2BAA0B,SAChDF,EAAuBrB,EAAgBmB,GAAW,OAArEK,EAAa,EAAH,KAChBJ,EAAWI,EAAYL,GAAY,0CApEzC,0LAyEMM,EAAkBlG,IAAMmG,YAC5B,WAUEC,GACG,QATDrF,aAAK,IAAG,KAAE,EACVsF,EAAQ,EAARA,SACAC,EAAS,EAATA,UACAC,EAAM,EAANA,OACAC,EAAO,EAAPA,QACAC,EAAQ,EAARA,SACAC,EAAO,EAAPA,QAIKC,EAAqB,EAAhBC,cAAgB,GAApB,GAEFC,EAA+BC,aAAY,SAACC,GAAK,OAAKC,IAAUC,kBAAkBF,EAAOG,IAAaC,WAAWC,gCAEvH3C,EAAiBgC,EAgBjB1F,EAAQsG,EAAsBtG,GAC9B,IAAMuG,EAAa,CAAEC,UAAW,IAGhC,OACE,yBAAK9B,UAAU,mBAAmBc,OAAQA,EAAQC,QAASA,EAASgB,QAftD,SAACC,GACfA,EAAEtE,iBACFsE,EAAEC,mBAaoFC,SAVvE,SAACF,GAChBA,EAAEtE,iBACFsE,EAAEC,oBASA,kBAAC,IAAU,CACTjC,UAAU,0CACVmC,MAAO,CAAEC,UAAW,WACpBzB,IAAK,SAAC0B,GAIJ,OAHIA,IACFA,EAAIC,YAAYvE,KAAKwE,UAAY,GAAH,OAAgBrB,EAAVD,EAAY,eAAoB,oBAE/DN,EAAI0B,IAEbG,QAASxB,GAAYA,EAAS/E,OAAS,EAAI,OAAK4F,GAAe1C,GAAkB0C,EACjFY,MAAM,OACNnH,MAAOA,EACPoH,YAAW,UAAexB,EAAVD,EAAY,eAAoB,kBAAiB,OACjEL,SAAUA,EACVC,UAAWA,EACX5B,QAASA,IAEVgC,IAAYG,GACX,kBAACuB,EAAA,EAAM,CACL3C,UAAU,iBACV4C,YAAanB,IAAaC,WAAWC,4BACrCkB,IAAI,yBACJC,MAAK,UAAK5B,EAAE,cAAa,YAAIA,EAAE,+BAC/Ba,QA3Cc,WAAM,MAC6B,QAAvD,EAAAgB,cAAcC,cAAc,mCAA2B,OAAvD,EAAyDC,eAiD/DxC,EAAgByC,YAAc,kBAEfzC,Q,ywECxIf,IAAM0C,GAAY,CAEhB7H,MAAO8H,IAAUC,OAEjBX,YAAaU,IAAUC,OAEvBzC,SAAUwC,IAAUE,KAAKC,WAEzBzC,OAAQsC,IAAUE,KAElBvC,QAASqC,IAAUE,KAEnBE,SAAUJ,IAAUE,MAGhBG,GAAelJ,IAAMmG,YAAW,SAACgD,EAAOC,GAC5C,IAWC,KARGtC,aACF,SAACC,GAAK,MAAK,CACTC,IAAUqC,YAAYtC,GACtBC,IAAUsC,iCAAiCvC,GAC3CC,IAAUuC,sCAAsCxC,GAChDC,IAAUwC,iBAAiBzC,MAE7B0C,KACD,GAVChD,EAAQ,KACRiD,EAAgB,KAWVC,EAAWC,qBAAW7J,KAAtB4J,OACFE,EAAcC,mBACdC,EAAgBD,mBAEtBE,2BAAgB,WAAM,QAGdC,EAAmC,QAAtB,EAAGJ,EAAYK,eAAO,OAAQ,QAAR,EAAnB,EAAqB/J,cAAM,WAAR,EAAnB,EAA6B2D,UAAUqG,kBACvDC,GAAcH,aAAa,EAAbA,EAAeI,0BAA2B,GAC1DN,EAAcG,SAAWH,EAAcG,UAAYE,EAAYE,QACjEX,IAEFI,EAAcG,QAAUE,EAAYE,SAGnC,CAACnB,EAAMpI,MAAO4I,IAGjB,IA8CMY,EAAgB,SACjBpB,GAAK,IACR/C,IAAK,SAACoE,GACJX,EAAYK,QAAUM,EACtBpB,EAAaoB,IAEfnE,SAAUoE,KAvCS,SAACC,EAASC,EAAOhF,EAAQxF,GAI5C,GAFAuK,EAAUA,EAAQE,QAAQ,UAAW,KAEjCf,EAAYK,QAAS,CAIvB,IACInJ,EAAQ,GAaZ,GAdgBZ,GAAsC,KAA5BA,EAAO0K,UAAUC,QAA6B,gBAAZJ,IAI1D3J,EAAQ2J,EAAQK,OAASL,EAAQK,OAAOhK,MAAQ2J,GAElDvB,EAAM9C,SAAStF,GAMcgF,IAAgBiF,wBAAwBL,EAAMrK,KAEjD,OAClB2K,EAAgBlF,IAAgBmF,2BAA2BP,EAAMrK,KACjE6K,EAAcpF,IAAgBqF,0BAA0BH,GAExDI,EADkBlL,EAAO0K,UACSnJ,OAASyJ,EAAYG,eAAe5J,OACtE6J,EAAoC,QAAtB,EAAG1B,EAAYK,eAAO,aAAnB,EAAqB/J,OAC5CqL,YAAW,kBAAMD,aAAc,EAAdA,EAAgBE,aAAaJ,EAAiBA,KAAkB,OAWpD,KACjC/E,UArDoB,SAACmB,GAGrB,GAFiB,KACOA,EAAEiE,MACL,CACnB,IAAMC,EAAsBjC,EACtBkC,EAA2BnE,EAAEoE,SAAWpE,EAAEqE,SAE5CH,GAAuBC,IACzBzC,EAAMF,SAASxB,KA8CnBhB,aAGF,OACE,oCACE,kBAAC,EAAoB8D,OAK3BrB,GAAaP,YAAc,eAC3BO,GAAaN,UAAYA,GAEVM,UCzHAA,Q,6FCCf,SAAS6C,EAAsB3L,EAAY2G,GAA8B,IAAvBiF,EAAoB,UAAH,6CAAG,EAO9DC,EAAkB,IAAI5J,OAAOC,KAAK4J,YAAYC,iBAEpDF,EAA2B,UAAI7L,EAAe,GAC9C6L,EAAmB,EAAI7L,EAAc,EACrC6L,EAAmB,EAAI7L,EAAc,EACrC6L,EAA4B,WAAI7L,EAAuB,WACvD6L,EAAyB,QAAI,cAC7BA,EAAwB,OAAIG,IAAKC,iBACjCJ,EAAuB,MAAIlF,EAC3BkF,EAA4B,WAAc,WAAVlF,GAAgC,aAAVA,EAAuB,SAAW,SACxFkF,EAAwB,QAAI,EAC5BA,EAAgBK,qBAEhB,IAAMC,EAAgBH,IAAKI,iBAAiBP,EAAwB,OAAGD,GACjES,EAAeC,IAAQ/F,EAAE,gBAAD,OAAiBI,EAAM4F,gBAC/CC,EAAW,GAAH,OAAMH,EAAY,YAAIC,IAAQ/F,EAAE,sBAAqB,YAAI4F,GAGvE,OAFAN,EAAgBY,YAAYD,GAErBX,I,otCCrBT,SAASa,EAAgB3D,GAEvB,IAAMnH,EAAOmH,EAAM4D,SAASnC,QAAQ,MAAO,IAEzCoC,EASE7D,EATF6D,WACAC,EAQE9D,EARF8D,aACAC,EAOE/D,EAPF+D,eACA7M,EAME8I,EANF9I,cACAsJ,EAKER,EALFQ,OACA/B,EAIEuB,EAJFvB,MAAK,EAIHuB,EAFFgE,eAAO,IAAG,GAAK,IAEbhE,EADFiE,qBAAa,IAAG,eAAQ,EAEmB,IAAfC,oBAAS,GAAM,GAAtCC,EAAQ,KAAEC,EAAS,KACmC,IAAdF,mBAAS,MAAK,GAAtDG,EAAmB,KAAEC,EAAe,KACW,IAAdJ,mBAAS,MAAK,GAA/CK,EAAY,KAAEC,EAAe,KACe,IAAfN,oBAAS,GAAM,GAA5CO,EAAU,KAAEC,EAAa,KAC1BzH,EAAMpG,IAAM8J,OAAO,MACjBnD,EAAMC,cAAND,EAQFmH,EAAgBR,EAAWtL,EAAOA,EAAK+L,UAAU,EAAGL,EAAeT,GACnEe,EAAoBrH,EAAX2G,EAAa,kBAAuB,mBAC7CW,EAAuBC,IAAW,oBAAqB,CAAE,kBAAmBf,IA0BlF,OAxBAgB,qBAAU,WACR,IAAMC,EAAgBhI,EAAI8D,QAAQmE,YAClCZ,EAAgBW,KACf,CAACpB,IAGJhD,2BAAgB,WASd,IAAMsE,EARN,SAAsBtM,GACpB,IACMuM,EADSnJ,SAASC,cAAc,UACfmJ,WAAW,MAGlC,OAFAD,EAAQ9M,KAAO,kBAER8M,EAAQE,YAAYzM,GAAM0M,MAGjBC,CAAa3M,GACzB4M,EAAmBN,EAAYtM,EAAKN,OACpCgM,EAAemB,KAAKC,MAAMtB,EAAsBoB,GACtDjB,EAAgBD,GAGhBG,EADmBS,EAAYd,EACJP,KAC1B,CAACjL,EAAMwL,IAGR,yBAAK/H,UAAWwI,EAAsB7H,IAAKA,EAAKwB,MAAOA,EAAOmH,YAAU,UACrE3B,IACAF,GAAkB7M,EACf6M,EAAeY,EAAezN,EAAe,GAC7CyN,EAAc,IAAEF,GAAc,4BAAQnI,UAAU,2BAA2B+B,QAvC5D,SAACzE,GACtBA,EAAM2E,kBACN6F,GAAWD,GACX3D,GAAUA,MAoCiGqE,IAK/GlB,EAAgBlE,UAAY,CAC1BoE,WAAYnE,IAAUmG,OACtB/B,aAAcpE,IAAUmG,OACxB9B,eAAgBrE,IAAUE,KAC1B1I,cAAewI,IAAUoG,IACzBtF,OAAQd,IAAUE,KAClBnB,MAAOiB,IAAUoG,IACjB9B,QAAStE,IAAUqG,KACnB9B,cAAevE,IAAUE,MAGZ+D,O,k7CC5Ef,IAAMqC,EAAmB,SAACC,EAAQC,EAAMhH,GAAW,MAAM,CACvDgH,OACAC,MAAO,gBAAF,OAAkBF,EAAOzC,eAC9BpE,MAAO,gBAAF,OAAkB6G,EAAOzC,eAC9ByC,SACA/G,gBAGWkH,EAAuB,CAClCJ,EAAiB,WAAY,kCAAmC,iCAChEA,EAAiB,WAAY,kCAAmC,iCAChEA,EAAiB,YAAa,mCAAoC,kCAClEA,EAAiB,YAAa,mCAAoC,kCAClEA,EAAiB,OAAQ,8BAA+B,6BACxDA,EAAiB,SAAU,gCAAiC,+BAC5DA,EAAiB,WAAY,kCAAmC,kCAG5DK,EAAkB,SAACrG,GACvB,IACEsG,EAGEtG,EAHFsG,OAAM,EAGJtG,EAFFuG,yBAAiB,IAAG,eAAQ,IAE1BvG,EADFwG,yBAAiB,IAAG,GAAK,EAGrBC,EAAWC,cAEXC,EAAiBH,EAAoB,GAAK,IAAH,OAAOF,GAC9CM,EAAiB,GAAH,OAAM7I,IAAa8I,mBAAiB,OAAGF,GACrDG,EAAgBnJ,aAAY,SAACC,GAAK,OAAKC,IAAUkJ,UAAUnJ,EAAOgJ,MAyBxE,OAnBA/F,2BAAgB,WACd,IAAMmG,EAAkB,CACtB9H,YAAa0H,EACbtK,UAAW,kBACX2K,MAAOb,EAAqBxN,KAAI,SAACrB,GAC/B,OAAO,EAAP,KACKA,GAAI,IACP8G,QAAS,kBAXI6I,EAWc3P,EAAK0O,YAVtCM,EAAkBW,GADA,IAACA,SAmBjBT,EAHGK,EAGMK,IAAQC,aAAaJ,EAAgB9H,YAAa8H,GAFlDG,IAAQE,UAAUL,MAI5B,CAACT,IAEG,MAGTF,EAAgB5G,UAAY,CAC1B6G,OAAQ5G,IAAUC,OAClB4G,kBAAmB7G,IAAUE,KAC7B4G,kBAAmB9G,IAAUqG,MAGhBM,QCpEAA,O,qBCFf,IAAIiB,EAAM,EAAQ,IACF/F,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQgG,WAAahG,EAAQiG,QAAUjG,KAG/CA,EAAU,CAAC,CAACkG,EAAOpM,EAAIkG,EAAS,MAG9C,IAAIjI,EAAU,CAEd,OAAiB,SAAUoO,GAgBX,IAAKxO,OAAOyO,8BAEV,YADA1L,SAAS2L,KAAKrL,YAAYmL,GAI5B,IAAIG,EAEJA,EAAgB5L,SAAS6L,qBAAqB,oBAEzCD,EAActP,SACjBsP,EAzBF,SAASE,EAAwBC,EAAS3N,EAAO4B,UAC/C,MAAMgM,EAAW,GAYjB,OATA5N,EAAKa,iBAAiB8M,GAAS1Q,QAAQ+J,GAAM4G,EAASC,KAAK7G,IAG3DhH,EAAKa,iBAAiB,KAAK5D,QAAQ+J,IAC7BA,EAAG8G,YACLF,EAASC,QAAQH,EAAwBC,EAAS3G,EAAG8G,eAIlDF,EAYSF,CAAwB,qBAG1C,MAAMK,EAAkB,GACxB,IAAK,IAAI/M,EAAI,EAAGA,EAAIwM,EAActP,OAAQ8C,IAAK,CAC7C,MAAMgN,EAAeR,EAAcxM,GACnC,GAAU,IAANA,EACFgN,EAAaF,WAAW5L,YAAYmL,GACpCA,EAASY,OAAS,WACZF,EAAgB7P,OAAS,GAC3B6P,EAAgB9Q,QAASiR,IAEvBA,EAAUC,UAAYd,EAASc,iBAIhC,CACL,MAAMD,EAAYb,EAASe,WAAU,GACrCJ,EAAaF,WAAW5L,YAAYgM,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPjB,EAAI/F,EAASjI,GAI1BmO,EAAOiB,QAAUnH,EAAQoH,QAAU,I,sBClEzBlB,EAAOiB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACT,EAAOpM,EAAI,uxBAAwxB,M,qBCLjzB,IAAIiM,EAAM,EAAQ,IACF/F,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQgG,WAAahG,EAAQiG,QAAUjG,KAG/CA,EAAU,CAAC,CAACkG,EAAOpM,EAAIkG,EAAS,MAG9C,IAAIjI,EAAU,CAEd,OAAiB,SAAUoO,GAgBX,IAAKxO,OAAOyO,8BAEV,YADA1L,SAAS2L,KAAKrL,YAAYmL,GAI5B,IAAIG,EAEJA,EAAgB5L,SAAS6L,qBAAqB,oBAEzCD,EAActP,SACjBsP,EAzBF,SAASE,EAAwBC,EAAS3N,EAAO4B,UAC/C,MAAMgM,EAAW,GAYjB,OATA5N,EAAKa,iBAAiB8M,GAAS1Q,QAAQ+J,GAAM4G,EAASC,KAAK7G,IAG3DhH,EAAKa,iBAAiB,KAAK5D,QAAQ+J,IAC7BA,EAAG8G,YACLF,EAASC,QAAQH,EAAwBC,EAAS3G,EAAG8G,eAIlDF,EAYSF,CAAwB,qBAG1C,MAAMK,EAAkB,GACxB,IAAK,IAAI/M,EAAI,EAAGA,EAAIwM,EAActP,OAAQ8C,IAAK,CAC7C,MAAMgN,EAAeR,EAAcxM,GACnC,GAAU,IAANA,EACFgN,EAAaF,WAAW5L,YAAYmL,GACpCA,EAASY,OAAS,WACZF,EAAgB7P,OAAS,GAC3B6P,EAAgB9Q,QAASiR,IAEvBA,EAAUC,UAAYd,EAASc,iBAIhC,CACL,MAAMD,EAAYb,EAASe,WAAU,GACrCJ,EAAaF,WAAW5L,YAAYgM,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPjB,EAAI/F,EAASjI,GAI1BmO,EAAOiB,QAAUnH,EAAQoH,QAAU,I,sBClEzBlB,EAAOiB,QAAU,EAAQ,GAAR,EAA+D,IAKlFR,KAAK,CAACT,EAAOpM,EAAI,khrBAA2orB,M,qBCLpqrB,IAAIiM,EAAM,EAAQ,IACF/F,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQgG,WAAahG,EAAQiG,QAAUjG,KAG/CA,EAAU,CAAC,CAACkG,EAAOpM,EAAIkG,EAAS,MAG9C,IAAIjI,EAAU,CAEd,OAAiB,SAAUoO,GAgBX,IAAKxO,OAAOyO,8BAEV,YADA1L,SAAS2L,KAAKrL,YAAYmL,GAI5B,IAAIG,EAEJA,EAAgB5L,SAAS6L,qBAAqB,oBAEzCD,EAActP,SACjBsP,EAzBF,SAASE,EAAwBC,EAAS3N,EAAO4B,UAC/C,MAAMgM,EAAW,GAYjB,OATA5N,EAAKa,iBAAiB8M,GAAS1Q,QAAQ+J,GAAM4G,EAASC,KAAK7G,IAG3DhH,EAAKa,iBAAiB,KAAK5D,QAAQ+J,IAC7BA,EAAG8G,YACLF,EAASC,QAAQH,EAAwBC,EAAS3G,EAAG8G,eAIlDF,EAYSF,CAAwB,qBAG1C,MAAMK,EAAkB,GACxB,IAAK,IAAI/M,EAAI,EAAGA,EAAIwM,EAActP,OAAQ8C,IAAK,CAC7C,MAAMgN,EAAeR,EAAcxM,GACnC,GAAU,IAANA,EACFgN,EAAaF,WAAW5L,YAAYmL,GACpCA,EAASY,OAAS,WACZF,EAAgB7P,OAAS,GAC3B6P,EAAgB9Q,QAASiR,IAEvBA,EAAUC,UAAYd,EAASc,iBAIhC,CACL,MAAMD,EAAYb,EAASe,WAAU,GACrCJ,EAAaF,WAAW5L,YAAYgM,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPjB,EAAI/F,EAASjI,GAI1BmO,EAAOiB,QAAUnH,EAAQoH,QAAU,I,sBClEnCD,EAAUjB,EAAOiB,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACT,EAAOpM,EAAI,ovFAAqvF,KAG9wFqN,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIrB,EAAM,EAAQ,IACF/F,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQgG,WAAahG,EAAQiG,QAAUjG,KAG/CA,EAAU,CAAC,CAACkG,EAAOpM,EAAIkG,EAAS,MAG9C,IAAIjI,EAAU,CAEd,OAAiB,SAAUoO,GAgBX,IAAKxO,OAAOyO,8BAEV,YADA1L,SAAS2L,KAAKrL,YAAYmL,GAI5B,IAAIG,EAEJA,EAAgB5L,SAAS6L,qBAAqB,oBAEzCD,EAActP,SACjBsP,EAzBF,SAASE,EAAwBC,EAAS3N,EAAO4B,UAC/C,MAAMgM,EAAW,GAYjB,OATA5N,EAAKa,iBAAiB8M,GAAS1Q,QAAQ+J,GAAM4G,EAASC,KAAK7G,IAG3DhH,EAAKa,iBAAiB,KAAK5D,QAAQ+J,IAC7BA,EAAG8G,YACLF,EAASC,QAAQH,EAAwBC,EAAS3G,EAAG8G,eAIlDF,EAYSF,CAAwB,qBAG1C,MAAMK,EAAkB,GACxB,IAAK,IAAI/M,EAAI,EAAGA,EAAIwM,EAActP,OAAQ8C,IAAK,CAC7C,MAAMgN,EAAeR,EAAcxM,GACnC,GAAU,IAANA,EACFgN,EAAaF,WAAW5L,YAAYmL,GACpCA,EAASY,OAAS,WACZF,EAAgB7P,OAAS,GAC3B6P,EAAgB9Q,QAASiR,IAEvBA,EAAUC,UAAYd,EAASc,iBAIhC,CACL,MAAMD,EAAYb,EAASe,WAAU,GACrCJ,EAAaF,WAAW5L,YAAYgM,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPjB,EAAI/F,EAASjI,GAI1BmO,EAAOiB,QAAUnH,EAAQoH,QAAU,I,sBClEnCD,EAAUjB,EAAOiB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACT,EAAOpM,EAAI,kyGAAmyG,KAG5zGqN,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIrB,EAAM,EAAQ,IACF/F,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQgG,WAAahG,EAAQiG,QAAUjG,KAG/CA,EAAU,CAAC,CAACkG,EAAOpM,EAAIkG,EAAS,MAG9C,IAAIjI,EAAU,CAEd,OAAiB,SAAUoO,GAgBX,IAAKxO,OAAOyO,8BAEV,YADA1L,SAAS2L,KAAKrL,YAAYmL,GAI5B,IAAIG,EAEJA,EAAgB5L,SAAS6L,qBAAqB,oBAEzCD,EAActP,SACjBsP,EAzBF,SAASE,EAAwBC,EAAS3N,EAAO4B,UAC/C,MAAMgM,EAAW,GAYjB,OATA5N,EAAKa,iBAAiB8M,GAAS1Q,QAAQ+J,GAAM4G,EAASC,KAAK7G,IAG3DhH,EAAKa,iBAAiB,KAAK5D,QAAQ+J,IAC7BA,EAAG8G,YACLF,EAASC,QAAQH,EAAwBC,EAAS3G,EAAG8G,eAIlDF,EAYSF,CAAwB,qBAG1C,MAAMK,EAAkB,GACxB,IAAK,IAAI/M,EAAI,EAAGA,EAAIwM,EAActP,OAAQ8C,IAAK,CAC7C,MAAMgN,EAAeR,EAAcxM,GACnC,GAAU,IAANA,EACFgN,EAAaF,WAAW5L,YAAYmL,GACpCA,EAASY,OAAS,WACZF,EAAgB7P,OAAS,GAC3B6P,EAAgB9Q,QAASiR,IAEvBA,EAAUC,UAAYd,EAASc,iBAIhC,CACL,MAAMD,EAAYb,EAASe,WAAU,GACrCJ,EAAaF,WAAW5L,YAAYgM,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPjB,EAAI/F,EAASjI,GAI1BmO,EAAOiB,QAAUnH,EAAQoH,QAAU,I,sBClEnCD,EAAUjB,EAAOiB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACT,EAAOpM,EAAI,whJAAyhJ,KAGljJqN,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIrB,EAAM,EAAQ,IACF/F,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQgG,WAAahG,EAAQiG,QAAUjG,KAG/CA,EAAU,CAAC,CAACkG,EAAOpM,EAAIkG,EAAS,MAG9C,IAAIjI,EAAU,CAEd,OAAiB,SAAUoO,GAgBX,IAAKxO,OAAOyO,8BAEV,YADA1L,SAAS2L,KAAKrL,YAAYmL,GAI5B,IAAIG,EAEJA,EAAgB5L,SAAS6L,qBAAqB,oBAEzCD,EAActP,SACjBsP,EAzBF,SAASE,EAAwBC,EAAS3N,EAAO4B,UAC/C,MAAMgM,EAAW,GAYjB,OATA5N,EAAKa,iBAAiB8M,GAAS1Q,QAAQ+J,GAAM4G,EAASC,KAAK7G,IAG3DhH,EAAKa,iBAAiB,KAAK5D,QAAQ+J,IAC7BA,EAAG8G,YACLF,EAASC,QAAQH,EAAwBC,EAAS3G,EAAG8G,eAIlDF,EAYSF,CAAwB,qBAG1C,MAAMK,EAAkB,GACxB,IAAK,IAAI/M,EAAI,EAAGA,EAAIwM,EAActP,OAAQ8C,IAAK,CAC7C,MAAMgN,EAAeR,EAAcxM,GACnC,GAAU,IAANA,EACFgN,EAAaF,WAAW5L,YAAYmL,GACpCA,EAASY,OAAS,WACZF,EAAgB7P,OAAS,GAC3B6P,EAAgB9Q,QAASiR,IAEvBA,EAAUC,UAAYd,EAASc,iBAIhC,CACL,MAAMD,EAAYb,EAASe,WAAU,GACrCJ,EAAaF,WAAW5L,YAAYgM,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPjB,EAAI/F,EAASjI,GAI1BmO,EAAOiB,QAAUnH,EAAQoH,QAAU,I,sBClEzBlB,EAAOiB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACT,EAAOpM,EAAI,woDAAyoD,M,qBCLlqD,IAAIiM,EAAM,EAAQ,IACF/F,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQgG,WAAahG,EAAQiG,QAAUjG,KAG/CA,EAAU,CAAC,CAACkG,EAAOpM,EAAIkG,EAAS,MAG9C,IAAIjI,EAAU,CAEd,OAAiB,SAAUoO,GAgBX,IAAKxO,OAAOyO,8BAEV,YADA1L,SAAS2L,KAAKrL,YAAYmL,GAI5B,IAAIG,EAEJA,EAAgB5L,SAAS6L,qBAAqB,oBAEzCD,EAActP,SACjBsP,EAzBF,SAASE,EAAwBC,EAAS3N,EAAO4B,UAC/C,MAAMgM,EAAW,GAYjB,OATA5N,EAAKa,iBAAiB8M,GAAS1Q,QAAQ+J,GAAM4G,EAASC,KAAK7G,IAG3DhH,EAAKa,iBAAiB,KAAK5D,QAAQ+J,IAC7BA,EAAG8G,YACLF,EAASC,QAAQH,EAAwBC,EAAS3G,EAAG8G,eAIlDF,EAYSF,CAAwB,qBAG1C,MAAMK,EAAkB,GACxB,IAAK,IAAI/M,EAAI,EAAGA,EAAIwM,EAActP,OAAQ8C,IAAK,CAC7C,MAAMgN,EAAeR,EAAcxM,GACnC,GAAU,IAANA,EACFgN,EAAaF,WAAW5L,YAAYmL,GACpCA,EAASY,OAAS,WACZF,EAAgB7P,OAAS,GAC3B6P,EAAgB9Q,QAASiR,IAEvBA,EAAUC,UAAYd,EAASc,iBAIhC,CACL,MAAMD,EAAYb,EAASe,WAAU,GACrCJ,EAAaF,WAAW5L,YAAYgM,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPjB,EAAI/F,EAASjI,GAI1BmO,EAAOiB,QAAUnH,EAAQoH,QAAU,I,sBClEnCD,EAAUjB,EAAOiB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACT,EAAOpM,EAAI,0nLAA2nL,KAGppLqN,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIrB,EAAM,EAAQ,IACF/F,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQgG,WAAahG,EAAQiG,QAAUjG,KAG/CA,EAAU,CAAC,CAACkG,EAAOpM,EAAIkG,EAAS,MAG9C,IAAIjI,EAAU,CAEd,OAAiB,SAAUoO,GAgBX,IAAKxO,OAAOyO,8BAEV,YADA1L,SAAS2L,KAAKrL,YAAYmL,GAI5B,IAAIG,EAEJA,EAAgB5L,SAAS6L,qBAAqB,oBAEzCD,EAActP,SACjBsP,EAzBF,SAASE,EAAwBC,EAAS3N,EAAO4B,UAC/C,MAAMgM,EAAW,GAYjB,OATA5N,EAAKa,iBAAiB8M,GAAS1Q,QAAQ+J,GAAM4G,EAASC,KAAK7G,IAG3DhH,EAAKa,iBAAiB,KAAK5D,QAAQ+J,IAC7BA,EAAG8G,YACLF,EAASC,QAAQH,EAAwBC,EAAS3G,EAAG8G,eAIlDF,EAYSF,CAAwB,qBAG1C,MAAMK,EAAkB,GACxB,IAAK,IAAI/M,EAAI,EAAGA,EAAIwM,EAActP,OAAQ8C,IAAK,CAC7C,MAAMgN,EAAeR,EAAcxM,GACnC,GAAU,IAANA,EACFgN,EAAaF,WAAW5L,YAAYmL,GACpCA,EAASY,OAAS,WACZF,EAAgB7P,OAAS,GAC3B6P,EAAgB9Q,QAASiR,IAEvBA,EAAUC,UAAYd,EAASc,iBAIhC,CACL,MAAMD,EAAYb,EAASe,WAAU,GACrCJ,EAAaF,WAAW5L,YAAYgM,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPjB,EAAI/F,EAASjI,GAI1BmO,EAAOiB,QAAUnH,EAAQoH,QAAU,I,sBClEnCD,EAAUjB,EAAOiB,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACT,EAAOpM,EAAI,khFAAmhF,KAG5iFqN,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIrB,EAAM,EAAQ,IACF/F,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQgG,WAAahG,EAAQiG,QAAUjG,KAG/CA,EAAU,CAAC,CAACkG,EAAOpM,EAAIkG,EAAS,MAG9C,IAAIjI,EAAU,CAEd,OAAiB,SAAUoO,GAgBX,IAAKxO,OAAOyO,8BAEV,YADA1L,SAAS2L,KAAKrL,YAAYmL,GAI5B,IAAIG,EAEJA,EAAgB5L,SAAS6L,qBAAqB,oBAEzCD,EAActP,SACjBsP,EAzBF,SAASE,EAAwBC,EAAS3N,EAAO4B,UAC/C,MAAMgM,EAAW,GAYjB,OATA5N,EAAKa,iBAAiB8M,GAAS1Q,QAAQ+J,GAAM4G,EAASC,KAAK7G,IAG3DhH,EAAKa,iBAAiB,KAAK5D,QAAQ+J,IAC7BA,EAAG8G,YACLF,EAASC,QAAQH,EAAwBC,EAAS3G,EAAG8G,eAIlDF,EAYSF,CAAwB,qBAG1C,MAAMK,EAAkB,GACxB,IAAK,IAAI/M,EAAI,EAAGA,EAAIwM,EAActP,OAAQ8C,IAAK,CAC7C,MAAMgN,EAAeR,EAAcxM,GACnC,GAAU,IAANA,EACFgN,EAAaF,WAAW5L,YAAYmL,GACpCA,EAASY,OAAS,WACZF,EAAgB7P,OAAS,GAC3B6P,EAAgB9Q,QAASiR,IAEvBA,EAAUC,UAAYd,EAASc,iBAIhC,CACL,MAAMD,EAAYb,EAASe,WAAU,GACrCJ,EAAaF,WAAW5L,YAAYgM,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPjB,EAAI/F,EAASjI,GAI1BmO,EAAOiB,QAAUnH,EAAQoH,QAAU,I,sBClEnCD,EAAUjB,EAAOiB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACT,EAAOpM,EAAI,4gDAA6gD,KAGtiDqN,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIrB,EAAM,EAAQ,IACF/F,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQgG,WAAahG,EAAQiG,QAAUjG,KAG/CA,EAAU,CAAC,CAACkG,EAAOpM,EAAIkG,EAAS,MAG9C,IAAIjI,EAAU,CAEd,OAAiB,SAAUoO,GAgBX,IAAKxO,OAAOyO,8BAEV,YADA1L,SAAS2L,KAAKrL,YAAYmL,GAI5B,IAAIG,EAEJA,EAAgB5L,SAAS6L,qBAAqB,oBAEzCD,EAActP,SACjBsP,EAzBF,SAASE,EAAwBC,EAAS3N,EAAO4B,UAC/C,MAAMgM,EAAW,GAYjB,OATA5N,EAAKa,iBAAiB8M,GAAS1Q,QAAQ+J,GAAM4G,EAASC,KAAK7G,IAG3DhH,EAAKa,iBAAiB,KAAK5D,QAAQ+J,IAC7BA,EAAG8G,YACLF,EAASC,QAAQH,EAAwBC,EAAS3G,EAAG8G,eAIlDF,EAYSF,CAAwB,qBAG1C,MAAMK,EAAkB,GACxB,IAAK,IAAI/M,EAAI,EAAGA,EAAIwM,EAActP,OAAQ8C,IAAK,CAC7C,MAAMgN,EAAeR,EAAcxM,GACnC,GAAU,IAANA,EACFgN,EAAaF,WAAW5L,YAAYmL,GACpCA,EAASY,OAAS,WACZF,EAAgB7P,OAAS,GAC3B6P,EAAgB9Q,QAASiR,IAEvBA,EAAUC,UAAYd,EAASc,iBAIhC,CACL,MAAMD,EAAYb,EAASe,WAAU,GACrCJ,EAAaF,WAAW5L,YAAYgM,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPjB,EAAI/F,EAASjI,GAI1BmO,EAAOiB,QAAUnH,EAAQoH,QAAU,I,sBClEnCD,EAAUjB,EAAOiB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACT,EAAOpM,EAAI,+5FAAg6F,KAGz7FqN,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,skBCTvB,8lGAAAtN,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,+XAoCeuN,IAjCe,SAAH,GAAyC,IAAnCC,EAAY,EAAZA,aAAcC,EAAc,EAAdA,eACvCC,EAAyBpL,aAAY,SAACC,GAAK,OAAKC,IAAUmL,0BAA0BpL,MAEpFV,EAAQ,eANhB,EAMgB,GANhB,EAMgB,UAAG,WAAOoB,GAAC,2EACO,KAAxB2K,EAAO3K,EAAEsD,OAAOsH,MAAM,IAClB,CAAF,eACe,GAAjBC,EAAaF,GACbF,EAAwB,CAAF,+BACNA,EAAuBE,GAAK,OAAxCG,EAAM,EAAH,KACTD,EAAa,CACXC,MACAC,KAAMJ,EAAKI,KACXhR,KAAM4Q,EAAK5Q,KACXiR,KAAML,EAAKK,MACX,OAEJR,EAAeD,EAAc,CAACM,IAAa,0CAnBjD,+KAqBG,gBAfa,sCAiBd,OACE,2BACEI,GAAG,0BACHD,KAAK,OACL7K,MAAO,CAAE+K,QAAS,QAClBtM,SAAUA,EACVmB,QAAS,SAACC,GACRA,EAAEsD,OAAOhK,MAAQ,Q,qzFCpBzB,IAAMoO,EAAmB,SAACC,EAAQC,EAAMhH,GAAW,MAAM,CACvDgH,OACAC,MAAO,UAAF,OAAYF,EAAOzC,eACxBpE,MAAO,UAAF,OAAY6G,EAAOzC,eACxByC,SACA/G,gBAGWuK,EAAuB,CAClCzD,EAAiB,OAAQ,GAAI,iBAC7BA,EAAiB,SAAU,GAAI,oBAG3BvG,EAAY,CAChBiK,WAAYhK,IAAUE,KACtB+J,aAAcjK,IAAUE,KACxBgK,WAAYlK,IAAUqG,KACtB8D,YAAanK,IAAUqG,KACvBO,OAAQ5G,IAAUC,QAGpB,SAASmK,KAET,SAASC,EAAU/J,GACjB,MAOIA,EANF0J,kBAAU,IAAG,EAAAI,EAAI,IAMf9J,EALF2J,oBAAY,IAAG,EAAAG,EAAI,EACnBF,EAIE5J,EAJF4J,WACAC,EAGE7J,EAHF6J,YACAtM,EAEEyC,EAFFzC,QACA+I,EACEtG,EADFsG,OAGI0D,EAAiBrM,aAAY,SAACC,GAAK,aAAqC,QAArC,EAAKC,IAAUoM,gBAAgBrM,UAAM,aAAhC,EAAkCoM,kBAC1EpD,EAAiB,GAAH,OAAM7I,IAAamM,kBAAiB,YAAI5D,GACrD9I,EAAqB,EAAhBC,cAAgB,GAApB,GAUR,IAAKmM,IAAeC,EAClB,OAAO,KAGT,IAAMM,EAAuBpF,IAAW,sCAClCqF,EAAerF,IAAW,uCAAwC,CAAE,gBAAiBxH,EAAS,aAAcyM,IAClH,OACE,yBAAK1N,UAAW8N,GACd,kBAACC,EAAA,EAAmB,CAClBnL,YAAW,oBAAeoH,GAC1BhK,UAAW6N,EACXhL,IAAI,kBACJC,MAAO5B,EAAE,oCACT8M,cAAe1D,EACf2D,UAAU,IAEZ,kBAAC,EAAe,CACd3D,eAAgBA,EAChB4D,YA1Bc,SAACC,GACD,SAAdA,EACFf,IACuB,WAAde,GACTd,KAuBEC,WAAYA,EACZC,YAAaA,KAOrB,IAAMa,EAAkB,SAAH,GAKf,IAJJ9D,EAAc,EAAdA,eACA4D,EAAW,EAAXA,YACAZ,EAAU,EAAVA,WACAC,EAAW,EAAXA,YAEMpD,EAAWC,cACXI,EAAgBnJ,aAAY,SAACC,GAAK,OAAKC,IAAUkJ,UAAUnJ,EAAOgJ,MACjEpJ,EAAqB,EAAhBC,cAAgB,GAApB,GA8BR,OA5BAoD,2BAAgB,WACd,IAAIoG,EAAQwC,EACPG,EAEOC,IACV5C,EAAQA,EAAM0D,QAAO,SAACpT,GAAI,MAAqB,WAAhBA,EAAK0O,WAFpCgB,EAAQA,EAAM0D,QAAO,SAACpT,GAAI,MAAqB,SAAhBA,EAAK0O,UAKtC,IAAM2E,EAAkB,CACtB1L,YAAa0H,EACbtK,UAAW,kBACX2K,MAAOA,EAAMrO,KAAI,SAACrB,GAChB,OAAO,EAAP,KACKA,GAAI,IACP4O,MAAO3I,EAAEjG,EAAK4O,OACd/G,MAAO5B,EAAEjG,EAAK6H,OACdf,QAAS,kBAAMmM,EAAYjT,EAAK0O,eAQpCQ,EAHGK,EAGMK,IAAQC,aAAawD,EAAgB1L,YAAa0L,GAFlDzD,IAAQE,UAAUuD,MAI5B,CAAChB,EAAYC,IAET,MAGTa,EAAgBjL,UAAY,CAC1BmH,eAAgBlH,IAAUC,OAC1B6K,YAAa9K,IAAUE,KACvBgK,WAAYlK,IAAUqG,KACtB8D,YAAanK,IAAUqG,MAGzBgE,EAAUtK,UAAYA,EAEPsK,Q,mmDC3EAc,ICxDAA,EDIf,SAA4B7K,GAC1B,IACE8K,EAGA,EAFEnN,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAUkN,2BAA2BnN,OACrC,GAHuB,GAIjB3G,EAAwC+I,EAAxC/I,WAAY+T,EAA4BhL,EAA5BgL,aAAcC,EAAcjL,EAAdiL,UAE0C,IAA1CpU,IAAMqN,SAASjB,IAAKiI,UAAUjU,IAAY,GAArEiU,EAAS,KAAEC,EAAY,KACsE,IAAlDtU,IAAMqN,SAASjB,IAAKmI,kBAAkBnU,IAAY,GAA7FmU,EAAiB,KAAEC,EAAoB,KAE9CxU,IAAMmO,WAAU,WACd,SAASsG,IACPH,EAAalI,IAAKiI,UAAUjU,EAAY6T,IACxCO,EAAqBpI,IAAKmI,kBAAkBnU,EAAY6T,IAK1D,OAFAQ,IACArI,IAAK1I,iBAAiB,6BAA8B+Q,OAA8BvT,EAAW+S,GACtF,kBAAM7H,IAAKsI,oBAAoB,6BAA8BD,EAA8BR,MACjG,CAAC7T,EAAY6T,IAEhB,IAiBMU,EAAY,CAChB9B,WAlBiB7S,IAAM4U,aAAY,WAChBxU,aAAsBiC,OAAOC,KAAK4J,YAAY2I,oBAC/CzI,IAAK0I,qBAAqBb,GAAyBc,2BACnE3I,IAAK0I,qBAAqBb,GAAyBe,QAAQ,0BAA2B5U,GAEtF+T,GAAa,EAAMC,KAEpB,CAAChU,EAAY+T,EAAcC,IAY5BtB,aAVmB9S,IAAM4U,aAAY,WACrCxI,IAAK6I,kBAAkB,CAAC7U,GAAU,SAAKA,EAAW8U,4BAAuBhU,EAAW+S,KACnF,CAAC7T,IASF2S,WAPiBwB,EAQjBvB,YAPkBqB,KAAcjU,WAAY+U,UAQ5C1F,OAPcrP,EAAcA,EAAWgV,GAAK,IAU9C,OACE,kBAAC,EAAS,KAAKjM,EAAWwL,K,yiCE/C9B,IAAM/L,EAAY,CAChBxI,WAAYyI,IAAUwM,OAAOrM,WAC7B0G,kBAAmB7G,IAAUE,MAG/B,SAASuM,EAAUnM,GACjB,IACE/I,EAEE+I,EAFF/I,WAAU,EAER+I,EADFuG,yBAAiB,IAAG,eAAS,EAGxB/I,EAAqB,EAAhBC,cAAgB,GAApB,GAEF2O,EAAkBnV,EAAWoV,YAC7BnG,EAAO,0BAAH,OAAiD,KAApBkG,EAAyB,OAASA,EAAgB5I,eACnF+F,EAAKtS,EAAWgV,GAEtB,OACE,oCACE,kBAAC5B,EAAA,EAAmB,CAClBnL,YAAW,oBAAeqK,GAC1BnK,MAAO5B,EAAE,4BACT2B,IAAK+G,EACLoE,cAAa,UAAKvM,IAAa8I,kBAAiB,YAAI0C,KAEtD,kBAAClD,EAAA,EAAe,CACdC,OAAQiD,EACRhD,kBAAmBA,KAM3B4F,EAAU1M,UAAYA,EAEP0M,Q,wPChCf,IAAM1M,GAAY,CAChBxI,WAAYyI,IAAUwM,QAGxB,SAASI,GAAmBtM,GAC1B,IAAM8K,EAA0BnN,aAAY,SAACC,GAAK,OAAKC,IAAUkN,2BAA2BnN,MACtF2O,EAAsB5O,aAAY,SAACC,GAAK,OAAKC,IAAUC,kBAAkBF,EAAO,gBAE9E3G,EAAe+I,EAAf/I,WAEFsP,EAAoBiG,YAAgBf,uBAAY,SAAmCgB,GACvF,IAAM3J,EAAkBF,YAAsB3L,EAAYwV,EAAU3B,GACpE7T,EAAWyV,SAAS5J,GACpB,IAAM6J,EAAoB1J,IAAK0I,qBAAqBb,GACpD6B,EAAkBC,cAAc9J,GAChC6J,EAAkBd,QAAQ,WAAY,CAAC/I,EAAiB7L,EAAY0V,EAAkBE,kBAAkB5V,OACvG,CAACA,EAAY6T,KAEhB,OAASyB,GACP,6BACE,kBAAC,EAAS,IAAChG,kBAAmBA,GAAuBvG,KAK3DsM,GAAmB7M,UAAYA,GAChB6M,IClCAA,GDkCAA,G,sjCE9Bf,IAAM7M,GAAY,CAChBoJ,aAAcnJ,IAAUC,OACxBd,UAAWa,IAAUC,OACrBmN,mBAAoBpN,IAAUwM,OAC9Ba,gBAAiBrN,IAAUwM,OAC3Bc,qBAAsBtN,IAAUwM,QAG5Be,GAA+B,SAAH,GAA+F,IAAzFpE,EAAY,EAAZA,aAAchK,EAAS,EAATA,UAAWiO,EAAkB,EAAlBA,mBAAoBC,EAAe,EAAfA,gBAAiBC,EAAoB,EAApBA,qBAC5FxP,EAAMC,cAAND,EAC2D,KAAf0G,oBAAS,GAAM,GAA5DgJ,EAAkB,KAAEC,EAAqB,KACe,KAAfjJ,oBAAS,GAAM,GAAxDkJ,EAAgB,KAAEC,EAAmB,KAC6B,KAAfnJ,oBAAS,GAAM,GAAlEoJ,EAAqB,KAAEC,EAAwB,KAQtD,OANAvI,qBAAU,WAAM,UACdmI,GAAsD,QAAhC,EAAAL,EAAmBjE,UAAa,aAAhC,EAAkCtQ,QAAS,GACjE8U,GAAiD,QAA7B,EAAAN,EAAgBlE,UAAa,aAA7B,EAA+BtQ,QAAS,GAC5DgV,GAA2D,QAAlC,EAAAP,EAAqBnE,UAAa,aAAlC,EAAoCtQ,QAAS,KACrE,CAACuU,EAAoBC,EAAiBC,IAGtCE,GAAsBE,GAAoBE,EACzC,yBAAKE,eAAa,4BAChB,kBAACC,GAAA,EAAO,CAAClM,QAAS/D,EAAE,4BAClB,6BACE,kBAACkQ,GAAA,EAAI,CAACpR,UAAU,YAAYqR,MAAO,wBAAyB9O,UAAWA,OAI7E,MAINoO,GAA6BxN,UAAYA,GAE1BwN,UClCTxN,GAAY,CAChBoJ,aAAcnJ,IAAUC,OACxBd,UAAWa,IAAUC,QAGjBiO,GAAwC,SAAH,GAAoC,IAA9B/E,EAAY,EAAZA,aAAchK,EAAS,EAATA,UACvDgP,EAAalQ,aAAY,SAACC,GAAK,OAAKC,IAAUC,kBAAkBF,EAAO,+BAC7E,EAAsE/G,IAAM4J,WAAW7J,KAA/EkW,EAAkB,EAAlBA,mBAAoBC,EAAe,EAAfA,gBAAiBC,EAAoB,EAApBA,qBAE7C,OAAIa,EACK,KAGP,kBAAC,GAA4B,CAC3BhF,aAAcA,EACdhK,UAAWA,EACXiO,mBAAoBA,EACpBC,gBAAiBA,EACjBC,qBAAsBA,KAI5BY,GAAsCnO,UAAYA,GAEnCmO,IC7BAA,GD6BAA,G,moCEPf,IAAMnO,GAAY,CAChByG,KAAMxG,IAAUC,OAChBmO,UAAWpO,IAAUC,OACrBzH,MAAOwH,IAAUC,OACjBoO,UAAWrO,IAAUC,OACrB1I,WAAYyI,IAAUwM,OACtB8B,SAAUtO,IAAUC,OACpBsO,eAAgBvO,IAAUC,OAC1BuO,WAAYxO,IAAUqG,KACtBiF,aAActL,IAAUE,KACxBuO,yBAA0BzO,IAAUqG,KACpCqI,SAAU1O,IAAUqG,KACpBsI,iBAAkB3O,IAAUE,KAC5B2M,oBAAqB7M,IAAUqG,KAC/BuI,UAAW5O,IAAUqG,KACrBkF,UAAWvL,IAAUmG,OACrB0I,aAAc7O,IAAUC,OACxB6O,YAAa9O,IAAUC,OACvB8O,gBAAiB/O,IAAUqG,KAC3BS,kBAAmB9G,IAAUqG,KAC7B2I,kBAAmBhP,IAAUE,KAC7B+O,cAAejP,IAAUqG,KACzB6I,wBAAyBlP,IAAUqG,KACnC8I,gBAAiBnP,IAAUqG,MAG7B,SAAS+I,GAAW9O,GAAO,QA6BrB+O,EA3BF7I,EAuBElG,EAvBFkG,KACA4H,EAsBE9N,EAtBF8N,UACA7W,EAqBE+I,EArBF/I,WACA+W,EAoBEhO,EApBFgO,SACAC,EAmBEjO,EAnBFiO,eACAC,EAkBElO,EAlBFkO,WACAlD,EAiBEhL,EAjBFgL,aACAmD,EAgBEnO,EAhBFmO,yBACA5Q,EAeEyC,EAfFzC,QACA6Q,EAcEpO,EAdFoO,SACAC,EAaErO,EAbFqO,iBACA9B,EAYEvM,EAZFuM,oBACA+B,EAWEtO,EAXFsO,UACArD,EAUEjL,EAVFiL,UACAsD,EASEvO,EATFuO,aACAC,EAQExO,EARFwO,YACAC,EAOEzO,EAPFyO,gBACAjI,EAMExG,EANFwG,kBACAkI,EAKE1O,EALF0O,kBACAC,EAIE3O,EAJF2O,cACAC,EAGE5O,EAHF4O,wBACAI,EAEEhP,EAFFgP,SACAH,EACE7O,EADF6O,gBAGKrR,EAAqB,GAAhBC,cAAgB,GAApB,GAGFwR,EAAeV,IAAiBW,KAAuBC,eAAkBhB,GAA4BI,IAAiBW,KAAuBE,aAAiBC,aAAsBpY,GAAcA,EAAWqY,YACnN,GAAIN,GAAYC,EAAa,CAC3B,IAAMM,EAAcN,EAAYO,eAAe,QAAS,CAAEC,SAAUT,IACpED,EAAO,IAAIW,KAAKH,QAEhBR,EAAOE,EAGT,IAAMU,EAAkBZ,EAAOa,IAAMb,GAAMc,OAAO7B,GAAU8B,OAAO7B,GAAkBzQ,EAAE,wCAEjFuS,EAAkB9Y,EAAW+Y,aAAazX,OAC5CL,EAA6B,QAAxB,EAAGjB,EAAW6W,UAAU,OAAa,QAAb,EAArB,EAAuBmC,mBAAW,WAAb,EAArB,UAERzB,IAAgB0B,KAAMC,MAAQjY,GAASkY,aAAelY,GACxDA,EAAQmY,KAAqB,MACpB7B,IAAgB0B,KAAMI,OAASpY,GAASqY,aAAgBrY,KACjEA,EAAQmY,KAAqB,OAG/B,IAAMtC,EAAYyC,aAASvZ,EAAWwZ,WAChCC,EAA6BzZ,EAAW0Z,sBACxCC,EAAsC,IAAH,OAAOF,EAA0B,OAEpEG,EAAqB9L,IAAW,kBAAmB,CAAExH,YACrDuT,EAAkB/L,IAAW,aAAc,CAAEgM,QAASxT,IAAYoR,IAWxE,OACE,yBAAKrS,UAAWwU,IACZvT,GACA,yBAAKjB,UAAU,uBACZ8R,GACC,yBAAK9R,UAAU,wBAEjB,kBAACoR,GAAA,EAAI,CAACpR,UAAU,YAAYqR,MAAOzH,EAAMhO,MAAOA,EAAO6V,UAAWA,KAGtE,yBAAKzR,UAAWuU,GACd,yBAAKvU,UAAU,uBACb,yBAAKA,UAAU,mBACb,yBAAKA,UAAU,UACZsS,QAA0D7W,IAA/B2Y,GAC1B,0BAAMpU,UAAU,qBAAqBsU,GAEtCvC,EAAiBpX,IAEpB,yBAAKqF,UAAU,wBACb,yBAAKA,UAAU,iBACZqT,EACAhB,GAAiB,UAAJ,OAAc1X,EAAW+Z,WAAU,MAElDjB,EAAkB,IAAM7B,GACvB,yBAAK5R,UAAU,yBACb,kBAACoR,GAAA,EAAI,CAACpR,UAAU,iBAAiBqR,MAAM,qBACvC,yBAAKrR,UAAU,eAAeyT,MAItC,yBAAKzT,UAAU,sBACZkK,IAAsBmI,IAAkBpR,GACvC,kBAAC0T,GAAA,EAAM,CACL1H,GAAE,mCAA8BtS,EAAWgV,IAC3CiF,aAAA,UAAe7C,EAAiBpX,GAAW,YAAIuG,EAAE,wCACjD2T,QAAS1C,EACTpQ,QAAS,SAACC,GACRA,EAAEtE,iBACFsE,EAAEC,kBACFmQ,GAAmBD,MAIzB,kBAAC,GAA4B,CAC3B5F,aAAc5R,EAAWgV,GACzBpN,UAAS,4BAAuBwP,EAAiBpX,GAAW,aAAK0Y,MAEjEpD,IAAwBhP,IAAYiJ,IAAsBmI,IAAkBE,GAC5E,kBAAC,GAAS,CACR5X,WAAYA,EACZiX,WAAYA,KAGdI,GAAaJ,IAAe1H,IAAsBmI,IAAkBE,GACpE,kBAAC,EAAS,CACR5D,UAAWA,EACXhU,WAAYA,EACZ+T,aAAcA,EACdzN,QAASA,IAGZ2Q,GAAcW,IAAoBrI,GACjC,oCACE,kBAACvH,GAAA,EAAM,CACLG,MAAO5B,EAAE,uBACT2B,IAAK,sBACL7C,UAAU,qCACV+B,QAAS,kBA5EjB+S,EA4E2Cna,EA5ENoa,cAAcC,WACzDrO,IAAKsO,kBAAkBC,oBAAoBJ,GAFjB,IACpBA,GA6EQK,cAAc,wBAEhB,kBAACxS,GAAA,EAAM,CACLG,MAAO5B,EAAE,uBACT2B,IAAK,aACL7C,UAAU,qCACV+B,QAAS,kBA/EjB+S,EA+E2Cna,EA/ENoa,cAAcC,WACzDrO,IAAKsO,kBAAkBG,oBAAoBN,GAFjB,IACpBA,GAgFQK,cAAc,6BAWhC3C,GAAWrP,UAAYA,GAERqP,IC1MAA,GD0MAA,G,mxCE1LA6C,IChBAA,GDGf,SAAkC3R,GAChC,IAAO4R,EAKN,GALwBjU,aACvB,SAACC,GAAK,MAAK,CACTC,IAAUgU,mBAAmBjU,MAE/B0C,KACD,GALoB,GAOrB,OACE,kBAACqD,GAAA,EAAe,MAAK3D,EAAK,CAAE6D,WAAY+N,M,0aEb5C,wuNADA,IAAME,GACC,cADDA,GAEG,cAFHA,GAGC,cAHDA,GAIC,cAJDA,GAKC,cALDA,GAMC,cANDA,GAOK,cAGLC,GAAsB7Y,OAAOC,KAAK4J,YAAYgP,oBAE7C,SAAeC,GAAsB,GAAD,gCAE1C,sCAFM,WAAqC/I,GAAI,2FACvC8I,GAAoBE,0BAA0BhJ,EAAK1H,QAAS0H,EAAKK,OAAK,4CAC9E,sBAEM,SAAe4I,GAAyB,GAAD,gCAE7C,sCAFM,WAAwCjb,GAAU,oFAAY,OAAViS,EAAQ,EAAH,6BAAG,GAAE,SAC7DjS,EAAWkb,eAAejJ,GAAM,4CACvC,sBAEM,SAASkJ,GAAQnJ,GACtB,SAAIA,EAAKK,OAAQL,EAAKK,KAAK+I,WAAW,WAMjC,SAASC,GAAkBrJ,GAAM,MACtC,GAAImJ,GAAQnJ,GACV,OAAO6I,GAGT,OAD2B,QAAZ,EAAG7I,EAAKI,YAAI,aAAT,EAAW1Q,MAAM,KAAK4Z,MAAM/O,eAE5C,IAAK,MACH,OAAOsO,GACT,IAAK,MACH,OAAOA,GACT,IAAK,MACL,IAAK,OACH,OAAOA,GACT,IAAK,MACL,IAAK,OACH,OAAOA,GACT,IAAK,MACL,IAAK,OACH,OAAOA,GACT,QACE,OAAOA,I,maC9Cb,kiNACA,IAOMU,GAAa,SAACC,GAClB,OAAO,IAAIC,SAAQ,SAACC,GAClB,QANmB5a,IAMF0a,EANPpa,KAORsa,EAAQF,EAAIG,SAAS,cAChB,CACL,IAAMC,EAAa,IAAIC,WACvBD,EAAWvK,OAAS,kBAAMqK,EAAQE,EAAWE,SAC7CF,EAAWL,WAAWC,QAKfO,GAAQ,SAAC/J,GACpB,MApBoB,kBAoBbA,EAAKK,MAGD2J,GAAW,eAxBxB,EAwBwB,GAxBxB,EAwBwB,WAAG,WAAOhK,GAAI,8FACduJ,GAAWvJ,GAAK,OAAzB,GAAPiK,EAAU,EAAH,KACC,CAAF,wCACH,CAAET,IAAKxJ,IAAM,OAYgC,OAThDkK,EAAgB,GACtBC,KAAUC,QAAQ,uBAAuB,SAACC,EAAGC,GAC3C,IAAQvL,EAAyBuL,EAAzBvL,QAAyBuL,EAAhBC,YACAxL,IACfmL,EAAcjL,KAAKF,MAIjByL,EAAQL,KAAUM,SAASR,GAC3BT,EAAM,IAAIkB,KAAK,CAACF,GAAQ,CAAEnK,KAtCZ,kBAsCkC,kBAC/C,CAAEmJ,MAAKmB,QAAST,EAAc5a,OAAS,IAAG,2CAxCnD,iLAyCC,gBAjBuB,sC,iZCxBxB,gmGAAA8C,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,olBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,8SAAAA,IAAA,4OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAaA,IAAMwY,GAAe,SAAH,GAAiB,IAAX5K,EAAI,EAAJA,KACfzL,EAAqB,GAAhBC,cAAgB,GAApB,GAEwB,KAAVyG,qBAAU,GAAzB4P,EAAG,KAAEC,EAAM,KACiC,KAAf7P,oBAAS,GAAM,GAA5C8P,EAAU,KAAEC,EAAa,KAyBhC,OAvBAjP,qBAAU,YACiB,+BAAG,6FAIiC,GAH3DiP,GAAc,GACVC,EAAiBjL,IAEfkL,IAAmBlL,aAAgBmL,MAAUnL,EAAKG,MACpC,CAAF,+BACO4I,GAAsB/I,GAAK,OAAlDiL,EAAiB,EAAH,iBAGZjL,aAAgBmL,MAAQD,GAAc,qBACpCnB,GAAM/J,GAAO,CAAF,iCACkBgK,GAAYiB,GAAe,iBAAlDzB,EAAG,EAAHA,IAAKmB,EAAO,EAAPA,QACbG,EAAOM,IAAIC,gBAAgB7B,IAC3BwB,EAAcL,GAAS,wBAEvBG,EAAOM,IAAIC,gBAAgBJ,IAAiB,4CAGjD,kBAlBwB,mCAmBzBK,KACC,CAACtL,IAGF,yBAAK3M,UAAWyI,IAAW,CACzB,4BAA4B,EAC5B,MAASiP,KAET,yBAAKF,IAAKA,IACTE,GAAc,0BAAM1X,UAAU,oCAAoCkB,EAAE,2BC/C5DgX,GDoDa,SAAH,GAA0C,IAApCtL,EAAK,EAALA,MAAOoF,EAAS,EAATA,UAAWmG,EAAW,EAAXA,YAI7C,KAHmC9W,aAAY,SAACC,GAAK,MAAK,CAC1DC,IAAU6W,cAAc9W,GACxBC,IAAU8W,gCAAgC/W,OAC1C,GAHKgX,EAAU,KAAEC,EAAc,KAI1BrX,EAAqB,GAAhBC,cAAgB,GAApB,GAEFY,EAAO,+BAAG,WAAOC,EAAG2K,GAAI,wEAER,GADpB3K,EAAEtE,iBACFsE,EAAEC,kBAEGqW,EAAY,CAAF,wCACNE,QAAQC,KAAK,gDAA+C,YAIjE9L,aAAgBmL,MAAI,gBACtBY,EAAW/L,EAAK,2BACPA,EAAKG,IAAK,CAAF,gBACjB4L,EAAW/L,EAAKG,IAAI,yCAEH4I,GAAsB/I,GAAK,QAA5C+L,EAAW,EAAH,aAGVA,GAAYJ,EAAWK,OAAOD,EAAU,CACtCE,SAAUjM,EAAKI,KACf8L,WAAW,EACXC,2BAA2B,IAC1B,4CACJ,gBAtBY,wCA+BPC,EAAU,+BAAG,WAAO/W,EAAG2K,GAAI,wEAEX,GADpB3K,EAAEtE,iBACFsE,EAAEC,mBAEe0K,EAAKG,IAAG,qBAAGH,EAAKG,IAAG,sCAAS4I,GAAsB/I,GAAK,0BAAlE+L,EAAW,EAAH,GACdM,kBAAON,EAAU/L,EAAKI,MAAM,4CAC7B,gBANe,wCAQhB,OACE,yBAAK/M,UAAU,yBACZ4M,EAAMtQ,KAAI,SAACqQ,EAAM5N,GAAC,OACjB,yBACEiB,UAAU,mBACVzC,IAAKwB,EACLgD,QAAS,SAACC,GAAC,OAAKD,EAAQC,EAAG2K,KAE1B4L,GAAkBzC,GAAQnJ,IACzB,kBAAC,GAAY,CAACA,KAAMA,IAEtB,yBAAK3M,UAAU,yBACb,kBAACoR,GAAA,EAAI,CACHpR,UAAU,wBACVqR,MAAO2E,GAAkBrJ,KAE3B,kBAACwE,GAAA,EAAO,CAAClM,QAAS0H,EAAKI,MACrB,yBAAK/M,UAAU,mBAAmB2M,EAAKI,OAExCiF,EACC,kBAACrP,GAAA,EAAM,CACL3C,UAAU,oBACV8C,MAAK,UAAK5B,EAAE,iBAAgB,YAAIA,EAAE,+BAClC2B,IAAI,aACJd,QAAS,SAACC,GAAC,OAvCR,SAACA,EAAG2K,GACnB3K,EAAEtE,iBACFsE,EAAEC,kBAEFkW,EAAYxL,GAmCgBsM,CAASjX,EAAG2K,MAG9B,kBAAChK,GAAA,EAAM,CACL3C,UAAU,oBACV8C,MAAK,UAAK5B,EAAE,mBAAkB,YAAIA,EAAE,+BACpC2B,IAAI,gBACJd,QAAS,SAACC,GAAC,OAAK+W,EAAW/W,EAAG2K,a,qBErGxCuM,GAAiB,SAACC,GACtB,IAAMC,EAAO,GAUb,GATID,EAAQ,gBAA6C,WAA3BA,EAAQ,iBACpCC,EAAW,MAAI,GAEbD,EAAQ,eAA2C,WAA1BA,EAAQ,gBACnCC,EAAa,QAAI,GAEfD,EAAe,QACjBC,EAAY,MAAID,EAAe,OAE7BA,EAAQ,mBAAoB,CAC9B,IAAME,EAAaF,EAAQ,mBAAmB9c,MAAM,KAEhDgd,EAAWC,SAAS,kBACtBF,EAAa,QAAI,GAEfC,EAAWC,SAAS,UACtBF,EAAgB,WAAI,GAIxB,OAAOA,GAGMG,GAlDc,SAAC5e,EAAYD,GAOxC,IANA,IAAME,EAAgBD,EAAW6e,mBAC3BC,EAAUC,OAAOC,KAAK/e,GACtB4K,EAAgBlF,KAAgBmF,2BAA2B/K,EAAOI,eAClEyB,EAAO+D,KAAgBqF,0BAA0BH,GAAeK,eAChEhL,EAAM,GAEHkE,EAAI,EAAGA,EAAI0a,EAAQxd,OAAQ8C,IAAK,CACvC,IAAMoa,EAAUve,EAAc6e,EAAQ1a,IAChCqa,EAAOF,GAAeC,GAE5B,IAAIS,MAAMH,EAAQ1a,IAAlB,CAIA,IAAM8a,EAAYD,MAAMH,EAAQ1a,EAAI,IAAMxC,EAAKN,OAASwd,EAAQ1a,EAAI,GAC9D+a,EAAYvd,EAAKwd,MAAMN,EAAQ1a,GAAI8a,GAEzChf,EAAI+Q,KAAK,CAAExQ,OAAQ0e,EAAW5e,WAAYke,KAG5C1e,EAAO0M,YAAYvM,GACnBH,EAAOsL,aAAazJ,EAAKN,OAAQ,I,sbC1BnC,gmGAAA8C,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,8YAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,8SAAAA,IAAA,4OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAmCAuU,IAAM0G,OAAOC,KAEb,IAAM9W,GAAY,CAChBxI,WAAYyI,IAAUwM,OAAOrM,WAC7ByO,UAAW5O,IAAUqG,KACrBiF,aAActL,IAAUE,KACxBqL,UAAWvL,IAAUmG,OACrBuI,SAAU1O,IAAUqG,KACpByQ,mBAAoB9W,IAAUqG,KAC9B0Q,eAAgB/W,IAAUE,KAC1B6O,gBAAiB/O,IAAUqG,KAC3BS,kBAAmB9G,IAAUqG,KAC7B2I,kBAAmBhP,IAAUE,KAC7B+O,cAAejP,IAAUqG,KACzB2Q,gBAAiBhX,IAAUE,MAGvB+W,GAAc,SAAH,GAaX,MAZJ1f,EAAU,EAAVA,WACAqX,EAAS,EAATA,UACAtD,EAAY,EAAZA,aACAC,EAAS,EAATA,UACAmD,EAAQ,EAARA,SACAoI,EAAkB,EAAlBA,mBACAC,EAAc,EAAdA,eACAhI,EAAe,EAAfA,gBACAjI,EAAiB,EAAjBA,kBACAkI,EAAiB,EAAjBA,kBACAC,EAAa,EAAbA,cACA+H,EAAe,EAAfA,gBAGMzI,EAAiBtQ,aAAY,SAACC,GAAK,OAAKC,IAAU+Y,kBAAkBhZ,MACpEkQ,EAAYnQ,aAAY,SAACC,GAAK,OAAKC,IAAUgZ,aAAajZ,EAAOkZ,aAAmB7f,GAAaqJ,QACjGiM,EAAsB5O,aAAY,SAACC,GAAK,OAAKC,IAAUC,kBAAkBF,EAAO,sBAChFoQ,EAAWrQ,aAAY,SAACC,GAAK,OAAKC,IAAUkZ,mBAAmBnZ,MAC/DuQ,EAA2BxQ,aAAY,SAACC,GAAK,OAAKC,IAAUsQ,yBAAyBvQ,MACrFoZ,EAAyBrZ,aAAY,SAACC,GAAK,OAAKC,IAAUoZ,kCAAkCrZ,MAC5FsZ,EAA0BvZ,aAAY,SAACC,GAAK,OAAKC,IAAUsZ,qCAAqCvZ,MAChG4Q,EAAc7Q,aAAY,SAACC,GAAK,OAAKC,IAAUuZ,eAAexZ,MAC9DoR,EAAWrR,aAAY,SAACC,GAAK,OAAKC,IAAUwZ,YAAYzZ,MACxDoM,EAAiBrM,aAAY,SAACC,GAAK,aAAqC,QAArC,EAAKC,IAAUoM,gBAAgBrM,UAAM,aAAhC,EAAkCoM,kBAEhF,EASIvJ,qBAAW7J,KARbsX,EAAU,EAAVA,WACAoJ,EAAW,EAAXA,YACA9W,EAAM,EAANA,OACAsM,EAAkB,EAAlBA,mBACAyK,EAAuB,EAAvBA,wBACAhJ,EAAY,EAAZA,aACAK,EAAuB,EAAvBA,wBACA4I,EAAkB,EAAlBA,mBAGI/Q,EAAWC,cACVlJ,EAAqB,GAAhBC,cAAgB,GAApB,GAEFF,EAAUtG,EAAWsG,UACrBsR,EAAkBiI,aAAmB7f,KAAgBwgB,KAAkBC,eAE3B,KAAZxT,mBAAS,IAAG,GAA3CyT,EAAW,KAAExF,EAAc,KAElCnN,qBAAU,WACRmN,EAAelb,EAAW2gB,oBACzB,CAAC3gB,IAEJ+N,qBAAU,WACR,IAAM6S,EAA4B,SAACC,EAAaC,GAC/B,WAAXA,GACFD,EAAYxgB,SAAQ,SAAC0gB,GACfA,EAAM/L,KAAOhV,EAAWgV,IAC1BkG,EAAe6F,EAAMJ,sBAO7B,OAFA3U,IAAK1I,iBAAiB,oBAAqBsd,GAEpC,WACL5U,IAAKsI,oBAAoB,oBAAqBsM,MAE/C,CAAC5gB,IAEJghB,cAAa,WACN3J,GACH7H,EAASU,IAAQ+Q,qBAGnB1X,MACC,CAAC8N,IAEJ,IAuII6J,EAvIE9J,EAAmB5C,uBACvB,SAACxU,GACC,IAAMoS,EAAOpG,IAAKI,iBAAiBpM,EAAmB,QAEtD,OAAOoS,EACL+O,GAAqB/O,EAAMiO,GAE3B9Z,EAAE,0CAGN,CAAC8Z,IAGGe,GAAephB,EAAWqhB,iBAAmBrhB,EAAWqhB,kBAExDC,GAAiB9M,uBACrB,SAAChI,EAAUvM,EAAeshB,EAAWH,GACnC,IAAMI,EAAoB,GAyB1B,GAxBKJ,GACHK,IAAWC,KAAKlV,EAAU,CACxBmV,aAAa,EACbC,oBAAoB,EACpBC,UAAS,SAACC,GACR,IAAMC,EAAOD,EAAME,gBACbC,EAAaH,EAAMI,gBACnBC,EAASL,EAAMM,YAErB,OAAQN,EAAMO,WACZ,IAAK,MACL,IAAK,QACL,IAAK,QACHb,EAAkBvQ,KAAK,CACrB8Q,OACAngB,KAAMqgB,EACNK,MAAOH,EACPI,IAAKJ,EAASL,EAAMU,iBAAiBlhB,cAO5CkgB,EAAkBlgB,OAAQ,CAC7B,IAAMmhB,EAAkBtB,GAAqB3U,EAAU6T,EAAapgB,GAQpE,IAAKogB,KAPiC/Z,GAAWyZ,GACrBzZ,GAAW2Z,GAMoC,CAWzE,OACE,kBAAC,GAAe,CACdpT,aAAc,EACdE,SAAO,EACPD,eAAgBA,GAChB7M,cAAeA,EACfsJ,OAAQA,EACR/B,MAAO+Z,EACPvU,cAlBkB,WACpB,IAAK4K,EACH,OAAO,KAET,IAAMhW,EAA2C,IAApC5B,EAA8B,kBAAUuG,EAAE,sBAAwBA,EAAE,wBACjF,OACE,0BAAMiB,MAAO,CAAEvG,MAAOjB,EAAWwZ,UAAUmC,WAAY+G,WAAY,MAAQ9gB,KAc1E4K,GAIP,OAAOiW,EAET,IAAME,EAAkB,GACpBC,EAAS,EAkDb,OA9CApB,EAAkBnhB,SAAQ,SAACwiB,EAAYC,GACrC,IAAQR,EAAqBO,EAArBP,MAAOC,EAAcM,EAAdN,IAAKR,EAASc,EAATd,KAChBa,EAASN,GACXK,EAAgB1R,KACd,0BAAMrO,IAAG,eAAUkgB,IAEf3B,GACE3U,EACA6T,EACApgB,EACA2iB,EACAN,KAMVK,EAAgB1R,KACd,uBACE8Q,KAAMA,EACNpX,OAAO,SACPoY,IAAI,sBACJngB,IAAG,YAAOkgB,IAGR3B,GACE3U,EACA6T,EACApgB,EACAqiB,EACAC,KAKRK,EAASL,KAGPK,EAASpW,EAASlL,OAAS,GAC7BqhB,EAAgB1R,KAAKkQ,GACnB3U,EACA6T,EACApgB,EACA2iB,IAGGD,IAET,CAACtC,IAGGpR,GAAO+T,aAAenD,aAAmB7f,IAAaiP,KAE5D,IACEiS,EAAa+B,KAAKC,MAAMljB,EAAWoa,cAAc,gBACjD,MAAO/S,GACP6Z,EAAalhB,EAAWoa,cAAc,eAGxC,IAAI5N,IAAqB,QAAV,EAAA0U,SAAU,aAAV,EAAY1U,WAAYxM,EAAWG,cAClDqM,GAogBF,SAAyBlC,GACvB,OAAOA,EAAU6Y,IAAO7Y,GAAWA,EArgBxB8Y,CAAgB5W,IAE3B,IAAM6W,GAAmBrjB,EAAWG,cAC9BF,GAAgBD,EAAW6e,mBAC7ByE,GAAYtjB,EAAsB,UAEtC,GAAIuX,IAAgB0B,KAAMC,KACpBoK,IAAanK,aAAemK,GAAUtK,iBACxCsK,GAAY,IAAIrhB,OAAOC,KAAK4J,YAAYyX,MAAM,IAAK,IAAK,IAAK,IAG3DtjB,IACe8e,OAAOC,KAAK/e,IACpBI,SAAQ,SAACmjB,GACZvjB,GAAcujB,GAAU,OAAKrK,aAAelZ,GAAcujB,GAAU,SACtEvjB,GAAcujB,GAAU,MAAIpK,KAAqB,eAIlD,GAAI7B,IAAgB0B,KAAMI,MAAO,CAKtC,GAJIiK,IAAahK,aAAgBgK,GAAUtK,iBACzCsK,GAAY,IAAIrhB,OAAOC,KAAK4J,YAAYyX,MAAM,EAAG,EAAG,EAAG,IAGrDtjB,GACe8e,OAAOC,KAAK/e,IACpBI,SAAQ,SAACmjB,GACZvjB,GAAcujB,GAAU,OAAKlK,aAAgBrZ,GAAcujB,GAAU,SACvEvjB,GAAcujB,GAAU,MAAIpK,KAAqB,UAQzD,IACIqK,GADEC,QAAqE,IAAtC7N,EAAmB7V,EAAWgV,IAGjEyO,GADEjX,IAAYkX,GACElX,GAEAqJ,EAAmB7V,EAAWgV,IAGhD,IAWM2O,GAAwB,SAACtc,GAAM,MACV,QAAzB,EAAIpF,OAAO2hB,sBAAc,OAArB,EAAuBjI,aACzBtU,WAAGC,mBAELmY,EAAgBpY,IAGZwc,GAAmB/V,IAAW,CAClC4R,aAAa,EACbpZ,UACAwd,OAAQ3M,EACR4M,QAASxE,EACT,aAAcxM,IAGVzI,GAAU0Z,mBACd,WACE,IAAMC,EAAe,GAKrB,OAJIX,KACFW,EAAahjB,MAAQqiB,GAAUtK,eAI/B,oCACI3B,GAAaJ,EACb,kBAAC,GAAW,CACVjX,WAAYA,EACZgU,UAAWA,EACXD,aAAcA,EACd0P,cAAeA,GACfS,sBAAuB3D,EACvB4D,YAAatO,EAAmB7V,EAAWgV,MAG7CqO,IACE,yBAAKhe,UAAWyI,IAAW,YAAa,CAAE,gBAAiBxH,IAAYc,QAASuc,IAC7Erd,GAAYoa,EAAYpf,OAAS,GAChC,kBAAC,GAAmB,CAClB2Q,MAAOyO,EACPrJ,WAAW,IAGdiK,GAAe+B,GAAkBpjB,GAAegkB,EAAc7C,QAO3E,CAACphB,EAAYiX,EAAYI,EAAWtD,EAAcvH,GAAU8U,GAAgBmC,GAAelD,EAAoBG,IAG3G9e,GAAO5B,EAAWoa,cAAc,qBAChCgK,GAAcJ,mBAClB,WACE,GAAa,KAATpiB,GACF,OAAO,KAGT,IAAMyiB,EAAwBlD,GAAqBvf,GAAMye,GACnDiE,GAAgChe,GAAWyZ,EAGjD,OAAIwE,IAASF,IAA0BC,EAEnC,kBAACE,GAAA,EAAkB,CACjBnf,UAAU,wBACV4C,YAAY,4BACZ,kBAAC,GAAe,CAAC4E,aAAc,GAAE,WAC1BwX,EAAqB,OAMhC,yBAAKhf,UAAU,wBAAwBmC,MAAO,CAAEid,aAAc,SAC3DJ,KAGJ,CAACziB,GAAMye,IAENqE,GAASV,mBACb,WACE,OACE,kBAAC,GAAU,CACT/U,KAAMA,GACN4H,UAAWA,EACX7W,WAAYA,EACZ+W,SAAUA,EACVC,eAAgBA,EAChBC,WAAYA,EACZlD,aAAcA,EACdmD,yBAA0BA,EAC1B5Q,QAASA,EACT6Q,SAAUA,EACVC,iBAAkBA,EAClB9B,oBAAqBA,EACrB+B,UAAWA,EACXrD,UAAWA,EACXsD,aAAcA,EACdC,YAAaA,EACbE,kBAAmBA,EACnBD,gBAAiBA,EACjBjI,kBAAmBA,EACnBmI,cAAeA,EACfC,wBAAyBA,EACzBI,SAAUA,EACVH,gBAAiBA,MAGpB,CAAC3I,GAAM4H,EAAW7W,EAAY+W,EAAUC,EAAgBC,EAAYlD,EAAcmD,EAA0B5Q,EAAS6Q,EAAUC,EAAkBpL,IAAKI,iBAAiBpM,EAAmB,QAAIsV,EAAqB+B,EAAWrD,EAAWoE,aAAsBpY,GAAasX,EAAcG,EAAmBD,EAAiBjI,EAAmBmI,EAAeK,EAAUH,IAG/W,OACE,yBAAKvS,UAAWwe,GAAkBzc,QA7HH,WAC1BsQ,IACCpR,EACFkZ,EAAexf,GACLqX,GAEViJ,OAwHDoE,GACAN,GACA9Z,KAKPoV,GAAYlX,UAAYA,GAETkX,UAGTiF,GAAc,SAAH,GAOX,IANJ3kB,EAAU,EAAVA,WACAgU,EAAS,EAATA,UACAD,EAAY,EAAZA,aACA0P,EAAa,EAAbA,cACAS,EAAqB,EAArBA,sBACAC,EAAW,EAAXA,YAkBE,KAREzd,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAUuC,sCAAsCxC,GAChDC,IAAUge,oBAAoBje,GAC9BC,IAAUC,kBAAkBF,EAAOG,IAAa+d,sBAChDje,IAAUke,cAAcne,EAAOG,IAAa+d,sBAC5Cje,IAAUke,cAAcne,EAAOG,IAAaie,aAC5Cne,IAAUkN,2BAA2BnN,GACrCC,IAAUoe,qBAAqBre,OAC/B,GAfAse,EAAkC,KAClCC,EAAgB,KAChBC,EAAuB,KACvBC,EAAmB,KACnBC,EAAgB,KAChBxR,EAAuB,KACvBmR,EAAoB,KAUfze,EAAqB,GAAhBC,cAAgB,GAApB,GACFiD,EAAcC,mBACdpD,EAAUtG,EAAWsG,UAC3B,EAMIkD,qBAAW7J,KALb2lB,EAAa,EAAbA,cACAvP,EAAoB,EAApBA,qBACAwP,EAAgB,EAAhBA,iBACAC,EAAgB,EAAhBA,iBACA3T,EAAc,EAAdA,eAGI4T,GAAyBN,GAA2BC,GAAuBM,eAEjF3X,qBAAU,WAER,GAAIiX,IAAyBK,GAAoBD,IAAwB3b,EAAYK,QAAS,CAC5F,IAAM/J,EAAS0J,EAAYK,QAAQnC,YACL3H,GAAcA,aAAsBiC,OAAOC,KAAK4J,YAAY2I,oBACjE1U,EAAO4lB,QAAQ,IAMpCxB,EACFrkB,aAA2BC,EAAQC,GAC1BD,EAAOI,eAChBiL,YAAW,WAET,GAAI8Z,EAAkB,CACpBzB,EAAgB9d,KAAgBmF,2BAA2B/K,EAAOI,eAClE,MAAgCwF,KAAgBqF,0BAA0ByY,GAAlEvY,EAAc,EAAdA,eAAmB,EAAH0a,IAEhBtkB,QACNvB,EAAO4lB,QAAQza,GAQqB,MAJpCua,GAIAR,IACiB,QAAnB,EAAAxb,EAAYK,eAAO,OAAnB,EAAqBlG,QACM5D,EAAW6e,oBAEpCD,GAAqB5e,EAAYD,MAGpC,KAGL,IACM8lB,EAAa9lB,EAAO+lB,YADS,EAGnC,GAAIL,EACF,OAGFra,YAAW,WACLya,GACF9lB,EAAOsL,aAAawa,EAAYA,KAEjC,QAEJ,CAACR,EAAkBD,EAAqBK,IAE3C1X,qBAAU,WACR,GAAIzH,GAAyC,IAA9Byf,EAAmBzkB,OAAc,CAE9C,IAAMof,EAAc1gB,EAAW2gB,iBAC/B9O,EAAe7R,EAAWgV,GAAI0L,MAE/B,IAEH,IAAMjU,EAAW,eAviBnB,EAuiBmB,GAviBnB,EAuiBmB,WAAG,WAAOpF,GAAC,gFAoCzB,OAlCDA,EAAEtE,iBAEIhD,EAAS0J,EAAYK,QAAQnC,YACnC8b,EAAgB9d,KAAgBmF,2BAA2B/K,EAAOI,eAClEL,aAA2BC,EAAQC,GAEAyjB,EAAcniB,OAAS,GAAiD,OAA5CmiB,EAAcA,EAAcniB,OAAS,KAElGmiB,EAAgBA,EAAcrE,MAAM,EAAGqE,EAAcniB,OAAS,IAG3CtB,EAAWqhB,iBAAmBrhB,EAAWqhB,mBAE5DrhB,EAAWgmB,sBAGTd,GAAkB,EACYvf,KAAgBqF,0BAA0ByY,GAAlEvY,EAAc,EAAdA,eAAgB0a,EAAG,EAAHA,IAGCjgB,KAAgBsgB,4BAA4BjmB,GACpDkmB,SAAS7lB,SAAQ,SAACK,GAC7BwK,EAAeyT,SAASje,EAAQC,QAClCilB,EAAI3U,KAAKvQ,EAAQ4R,OAIrBtS,EAAWmmB,cAAc,cAAelD,KAAKmD,UAAU,CACrD5Z,SAAUiX,EACVmC,SAEF5lB,EAAWyM,YAAYvB,IAEvBlL,EAAWyM,YAAYgX,GACxB,UAEKxI,GAAyBjb,EAAY+V,EAAqB/V,EAAWgV,KAAI,QAEzEzP,EAAUvF,aAAsBiC,OAAOC,KAAK4J,YAAY2I,mBAC1D,cAAgB,cACpBzI,IAAK0I,qBAAqBb,GAAyBe,QAAQ,oBAAqB,CAAC,CAAC5U,GAAa,SAAU,CAAE,OAAUuF,KAEjHvF,aAAsBiC,OAAOC,KAAK4J,YAAY2I,oBAChDzI,IAAKqa,wBAAwB,CAACrmB,IAGhC+T,GAAa,EAAOC,GAEE,KAAlByP,GACFS,OAAsBpjB,EAAWd,EAAWgV,IAE9CwQ,EAAiBxlB,EAAWgV,IAAI,2CA5lBpC,iLA6lBG,gBAtDgB,sCAoEXsR,EAAmBxY,IAAW,eAAgB,CAAE,gBAAiBxH,IACjEyf,EAAqBhQ,EAAqB/V,EAAWgV,KAAO,GAElE,OACE,yBAAK3P,UAAWihB,GACbhgB,GAAWyf,EAAmBzkB,OAAS,GACtC,kBAAC,GAAmB,CAClB2Q,MAAO8T,EACP1O,WAAW,EACXmG,YAAa,SAACxL,GAAI,OAAKuT,EAAiBvlB,EAAWgV,GAAIhD,MAG3D,kBAAClJ,EAAA,EAAY,CACX9C,IAAK,SAACoE,GACJX,EAAYK,QAAUM,GAExBzJ,MAAO8iB,EACPxd,SAAU,SAACtF,GAAK,OAAKujB,EAAsBvjB,EAAOX,EAAWgV,KAC7DnM,SAAU4D,EACVnG,QAASA,EACTH,OAhCS,SAACkB,GAAM,QACD,QAAnB,EAAIA,EAAEkf,qBAAa,OAA8B,QAA9B,EAAf,EAAiBC,aAAa,uBAAe,OAA7C,EAA+C7H,SAAS,2BAC1DtX,EAAEsD,OAAO/G,QAGX0hB,OAAcxkB,IA4BVsF,QAzBU,WACdkf,EAActlB,EAAWgV,OA0BvB,yBAAK3P,UAAU,gBACb,kBAAC2C,GAAA,EAAM,CACL3C,UAAU,gBACV6J,MAAO3I,EAAE,iBACTa,QAAS,SAACC,GACRA,EAAEC,kBACFyM,GAAa,EAAOC,GAEpBkQ,OAAsBpjB,EAAWd,EAAWgV,IAC5CwQ,EAAiBxlB,EAAWgV,OAGhC,kBAAChN,GAAA,EAAM,CACL3C,UAAS,qBAAiBoe,EAA8B,GAAd,aAC1CnQ,UAAWmQ,EACXvU,MAAO3I,EAAE,eACTa,QAAS,SAACC,GACRA,EAAEC,kBACFmF,EAAYpF,SAQxBsd,GAAYnc,UAAY,CACtBwL,UAAWvL,IAAUmG,OAAOhG,WAC5B5I,WAAYyI,IAAUwM,OAAOrM,WAC7BmL,aAActL,IAAUE,KAAKC,WAC7B6a,cAAehb,IAAUC,OACzBwb,sBAAuBzb,IAAUE,KAAKC,WACtCub,YAAa1b,IAAUC,QAGzB,IAAM+d,GAAkB,SAAC7kB,EAAM3B,EAAe2C,GAC5C,IAAM4E,EAAQ,CACZkb,WAAYziB,EAAc,eAC1BymB,UAAWzmB,EAAc,cACzB0mB,eAAgB1mB,EAAc,mBAC9BgB,MAAOhB,EAAqB,OAK9B,OAHIuH,EAAMmf,iBACRnf,EAAMmf,eAAiBnf,EAAMmf,eAAenc,QAAQ,OAAQ,cAG5D,0BAAMhD,MAAOA,EAAO5E,IAAKA,GAAMhB,IAI7BkL,GAAiB,SAAClL,EAAM3B,EAAeqiB,GAC3C,IAAKriB,IAAkB2B,EACrB,OAAOA,EAKT,IAFA,IAAMglB,EAAS,GACTC,EAAU9H,OAAOC,KAAK/e,GAAe0B,IAAImlB,QAAQC,MAAK,SAACvD,EAAGwD,GAAC,OAAKxD,EAAIwD,KACjE5iB,EAAI,EAAGA,EAAIyiB,EAAQvlB,OAAQ8C,IAAK,CACvC,IAAI6iB,EAAQJ,EAAQziB,GAAKke,EAGzB,GADAsE,EADAK,EAAQxY,KAAKyY,IAAIzY,KAAK0Y,IAAIF,EAAO,GAAIrlB,EAAKN,SAC1BrB,EAAc4mB,EAAQziB,IAClC6iB,IAAUrlB,EAAKN,OACjB,MAMJ,IAFA,IAAMqhB,EAAkB,GAClByE,EAAerI,OAAOC,KAAK4H,GAAQjlB,IAAImlB,QAAQC,MAAK,SAACvD,EAAGwD,GAAC,OAAKxD,EAAIwD,KAC/D5iB,EAAI,EAAGA,EAAIgjB,EAAa9lB,OAAQ8C,IACvCue,EAAgB1R,KAAKwV,GACnB7kB,EAAKwd,MAAMgI,EAAahjB,EAAI,GAAIgjB,EAAahjB,IAC7CwiB,EAAOQ,EAAahjB,EAAI,IAAG,wBACVA,KAIrB,OAAOue,GAGHxB,GAAuB,SAACkG,EAAUhH,EAAapgB,GAAoD,IAArCqiB,EAAQ,UAAH,6CAAG,EAAGC,EAAM,UAAH,6CAAG8E,EAAS/lB,OACtFM,EAAOylB,EAASjI,MAAMkD,EAAOC,GAC7B+E,EAAc1lB,EAAK2K,cACnBgb,EAAqBlH,EAAY9T,cACnCtM,IACFA,EAAc,GAAOA,EAAc,IAAQ,GAC3CA,EAAconB,EAAS/lB,QAAUrB,EAAconB,EAAS/lB,SAAW,IAErE,IAAIkmB,EAAoBF,EAAYnjB,QAAQojB,GAC5C,IAAKA,EAAmB7c,SAAiC,IAAvB8c,EAChC,OAAO1a,GAAelL,EAAM3B,EAAeqiB,GAE7C,IAAMK,EAAkB,GAClB8E,EAAoB,CAACD,GAErBE,EAAuBH,EAAmB/c,QAAQ,sBAAuB,QAC/E,GAAI,IAAImd,OAAO,IAAD,OAAKD,EAAoB,KAAK,MAAME,KAAKN,GACrD,MAA8B,IAAvBE,IAEsB,KAD3BA,EAAoBF,EAAYnjB,QAAQojB,EAAoBC,EAAoBD,EAAmBjmB,UAEjGmmB,EAAkBxW,KAAKuW,GAiC7B,OA7BAC,EAAkBpnB,SAAQ,SAACwnB,EAAUC,GAGvB,IAARA,GAA0B,IAAbD,GACflF,EAAgB1R,KAAKnE,GAAelL,EAAK+L,UAAU,EAAGka,GAAW5nB,EAAeqiB,IAElFK,EAAgB1R,KACd,0BAAM5L,UAAU,YAAYzC,IAAG,yBAAoBklB,IAE/Chb,GACElL,EAAK+L,UAAUka,EAAUA,EAAWN,EAAmBjmB,QACvDrB,EACAqiB,EAAQuF,KAMdA,EAAWN,EAAmBjmB,OAASgmB,EAAYhmB,QAEhDumB,EAAWN,EAAmBjmB,SAAWmmB,EAAkBK,EAAM,IAEpEnF,EAAgB1R,KAAKnE,GACnBlL,EAAK+L,UAAUka,EAAWN,EAAmBjmB,OAAQmmB,EAAkBK,EAAM,IAC7E7nB,EACAqiB,EAAQuF,EAAWN,EAAmBjmB,YAIrCqhB,GCrwBMjD,U,iZCDf,gmGAAAtb,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,8YAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,8SAAAA,IAAA,4OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAmBA,IAAMoE,GAAY,CAChBxI,WAAYyI,IAAUwM,OAAOrM,YAIzBmf,GAAY,SAAH,GAAuD,IAAjD/nB,EAAU,EAAVA,WAAYmX,EAAQ,EAARA,SAAU6Q,EAAoB,EAApBA,qBAwBxC,KAbGthB,aACF,SAACC,GAAK,YAAK,CACTC,IAAUuC,sCAAsCxC,GAChDC,IAAUqhB,mBAAmBthB,GAC7BC,IAAUC,kBAAkBF,EAAO,aACA,QADY,EAC/CC,IAAUshB,mBAAmBvhB,UAAM,aAAnC,EAAsC3G,GACtC4G,IAAUge,oBAAoBje,GAC9BC,IAAUwC,iBAAiBzC,GAC3BC,IAAUC,kBAAkBF,EAAOG,IAAa+d,sBAChDje,IAAUke,cAAcne,EAAOG,IAAa+d,sBAC5Cje,IAAUkN,2BAA2BnN,MAEvC0C,KACD,GAtBC4b,EAAkC,KAClCkD,EAAU,KACVC,EAAe,KACfC,EAA4B,KAC5BnD,EAAgB,KAChBoD,EAAuC,KACvCnD,EAAuB,KACvBC,EAAmB,KACnBvR,EAAuB,KAezB,EAWIrK,qBAAW7J,KAVb4oB,EAAiB,EAAjBA,kBACAtR,EAAU,EAAVA,WACAnB,EAAe,EAAfA,gBACA0S,EAAe,EAAfA,gBACAC,EAAoB,EAApBA,qBACAC,EAAqB,EAArBA,sBACApD,EAAa,EAAbA,cACAvP,EAAoB,EAApBA,qBACAyP,EAAgB,EAAhBA,iBACAD,EAAgB,EAAhBA,iBAE+C,KAAftY,oBAAS,GAAM,GAA1C0b,EAAS,KAAEC,EAAY,KACxBpZ,EAAWC,cACXhG,EAAcC,mBAEd+b,GAAyBN,GAA2BC,GAAuBM,eAEjF1E,cAAa,WACN2H,GACHnZ,EAASU,IAAQ+Q,uBAElB,CAAC0H,IAEJ5a,qBAAU,WACJ0X,GAKF6C,GACArR,IACCsR,GACDtD,GACAxb,GACAA,EAAYK,SAEZL,EAAYK,QAAQlG,UAErB,CAAC2kB,EAAmBD,EAAyCrR,EAAYwO,IAE5E1X,qBAAU,WAaR,IAVK0a,GAAwBC,GAE3Btd,YAAW,WAGL3B,GAAeA,EAAYK,SAAWmb,GACxCxb,EAAYK,QAAQlG,UAErB,KAED6F,GAAeA,EAAYK,QAAS,CACtC,GAAI2b,EACF,OAGF,IAEMI,EAFSpc,EAAYK,QAAQnC,YAETme,YADS,EAEnC1a,YAAW,WACL3B,EAAYK,SACdL,EAAYK,QAAQ/J,OAAOsL,aAAawa,EAAYA,KAErD,QAEJ,IAEH,IAAMgD,EAAS,eAvHjB,EAuHiB,GAvHjB,EAuHiB,WAAG,WAAOxhB,GAAC,8EAM0D,GAJlFA,EAAEtE,iBACFsE,EAAEC,kBAEIvH,EAAS0J,EAAYK,QAAQnC,aAC7BmhB,EAAYnjB,KAAgBmF,2BAA2B/K,EAAOI,gBAErDuK,OAAQ,CAAF,oDAIjBwa,EAAkB,CAAF,gBAEkC,OAD9C6D,EAAkBpjB,KAAgBqjB,mBAAmBhpB,EAAY8oB,GACvEhpB,aAA2BC,EAAQgpB,GAAiB,UAC9C9N,GAAyB8N,EAAiBhT,EAAqB/V,EAAWgV,KAAI,QACpFhJ,IAAKid,eAAe,CAACF,GAAkBlV,GAAyB,wBAGZ,OAD9CkV,EAAkB/c,IAAKkd,sBAAsBlpB,EAAY8oB,GAC/DhpB,aAA2BC,EAAQgpB,GAAiB,UAC9C9N,GAAyB8N,EAAiBhT,EAAqB/V,EAAWgV,KAAI,QACpFhJ,IAAK0I,qBAAqBb,GAAyBe,QAAQ,oBAAqB,CAAC,CAACmU,GAAkB,SAAU,KAAK,QAGrHP,EAAgB,GAAIxoB,EAAWgV,IAC/BwQ,EAAiBxlB,EAAWgV,IAAI,2CAhJpC,iLAiJG,gBA1Bc,sCA4BTmU,EACJhB,GACAC,GACAC,EAEIe,EAAiBtb,IAAW,CAChC,cAAc,EACdgW,OAAQ3M,IAkBJ4O,EAAqBhQ,EAAqB/V,EAAWgV,KAAO,GAElE,OAAQmU,IAAsBlS,EAAc,KAC1C,0BAAMpO,SAAUggB,EAAWxjB,UAAU,wBAClC0gB,EAAmBzkB,OAAS,GAC3B,kBAAC,GAAmB,CAClB2Q,MAAO8T,EACP1O,WAAW,EACXmG,YAAa,SAACxL,GAAI,OAAKuT,EAAiBvlB,EAAWgV,GAAIhD,MAG3D,yBAAK3M,UAAU,0BACb,yBACEA,UAAW+jB,EAGXC,YAAa,SAAChiB,GAAC,OAAKA,EAAEC,oBAEtB,kBAACwB,EAAA,EAAY,CACX9C,IAAK,SAACoE,GACJX,EAAYK,QAAUM,GAExBzJ,MAAOmV,EAAgB9V,EAAWgV,IAClC/O,SAAU,SAACtF,GAAK,OAtCO,SAACA,GAChC6nB,EAAgB7nB,EAAOX,EAAWgV,IAClCgT,GAAwBA,IAoCKsB,CAAyB3oB,IAC9CkI,SAAUggB,EACV1iB,OAnCK,WACbyiB,GAAa,GACbtD,OAAcxkB,IAkCNsF,QA/BM,WACdwiB,GAAa,GACbtD,EAActlB,EAAWgV,KA8BjB1O,SAAO,KAGX,yBAAKjB,UAAU,0BACb,kBAAC2C,GAAA,EAAM,CACLE,IAAI,kBACJ7C,UAAU,eACV8C,MAAO,gBACPmL,UAAWwC,EAAgB9V,EAAWgV,IACtC5N,QAASyhB,EACTU,cAAY,QAQxBxB,GAAUvf,UAAYA,GAEPuf,IC1NAA,GD0NAA,G,miCEhNf,IAAMvf,GAAY,CAChBghB,iBAAkB/gB,IAAUghB,MAAM7gB,WAClC2G,kBAAmB9G,IAAUqG,KAAKlG,YAG9B8gB,GAAmB,SAAH,GAGhB,IAFJF,EAAgB,EAAhBA,iBACAja,EAAiB,EAAjBA,kBAEOhJ,EAAqB,GAAhBC,cAAgB,GAApB,GACFgJ,EAAWC,cACsD,KAAfxC,oBAAS,GAAM,GAAhE0c,EAAoB,KAAEC,EAAuB,KAC9C7W,EAAiBrM,aAAY,SAACC,GAAK,aAAqC,QAArC,EAAKC,IAAUoM,gBAAgBrM,UAAM,aAAhC,EAAkCoM,kBAK1E8W,EACJ,kBAAC7hB,GAAA,EAAM,CACLZ,QAAS,SAACC,GACRA,EAAEtE,iBACFsE,EAAEC,kBACFsiB,GAAwB,IAE1BvkB,UAAU,cACVuC,UAAWrB,EAAE,mCACb2I,MAAO3I,EAAE,mCACT2B,IAZc,+BAeZ4hB,EACJ,kBAAC9hB,GAAA,EAAM,CACLZ,QAAS,SAACC,GACRA,EAAEtE,iBACFsE,EAAEC,kBACFsiB,GAAwB,IAE1BvkB,UAAU,cACVuC,UAAWrB,EAAE,oCACb2I,MAAO3I,EAAE,oCACT2B,IA1BY,6BA8BhB,OACE,yBACE7C,UAAWyI,IAAW,CACpB,iBAAiB,EACjB,aAAciF,KAGf4W,EAAuBG,EAAuBD,EAC9CF,GACCH,EAAiB7nB,KAAI,SAACooB,EAAiB3lB,GAErC,OAAU,IAANA,EACK,KAGP,kBAAC4D,GAAA,EAAM,CACLpF,IAAKmnB,EAAgB/U,GACrB3P,UAAU,cACV+B,QAAS,SAACC,GACRA,EAAEtE,iBACFsE,EAAEC,kBACF0E,IAAKge,iBAAiBD,GACtB/d,IAAKie,iBAAiBF,GACtBva,EAASU,IAAQga,YAAY,sBAG/B,kBAAC,GAAW,CACVtnB,IAAKmnB,EAAgB/U,GACrBhV,WAAY+pB,EACZ5S,UAAU,EACVO,eAAe,EACfnI,kBAAmBA,UAUnCma,GAAiBlhB,UAAYA,GAEdkhB,U,wkCCvFf,IAAMS,GAAsB,SAAH,GAAqB,IAAfxd,EAAQ,EAARA,SACvByd,EAAQhiB,eAAcC,cAAc,wBACpC+B,EAAKpF,SAASC,cAAc,OAQlC,OAPAmF,EAAGigB,aAAa,eAAgBvjB,IAAawjB,gCAE7Cvc,qBAAU,WAER,OADAqc,EAAM9kB,YAAY8E,GACX,kBAAMggB,EAAMG,YAAYngB,MAC9B,CAACA,EAAIggB,IAEDI,wBAAa7d,EAAUvC,ICpBjBqgB,GDuBqB,SAAH,GAA4D,IAAtDzqB,EAAU,EAAVA,WAAY0qB,EAAgB,EAAhBA,iBAAkBC,EAAiB,EAAjBA,kBAoBlE,KAXGjkB,aACF,SAACC,GAAK,MAAK,CACTC,IAAUgU,mBAAmBjU,GAC7BC,IAAUke,cAAcne,EAAOG,IAAawjB,gCAC5C1jB,IAAUke,cAAcne,EAAOG,IAAaie,aAC5Cne,IAAUC,kBAAkBF,EAAOG,IAAawjB,gCAChD1jB,IAAUgkB,0BAA0BjkB,GACpCC,IAAUikB,2BAA2BlkB,GACrCC,IAAUkN,2BAA2BnN,MAEvC0C,KACD,GAlBCsR,EAAc,KACdmQ,EAAU,KACVC,EAAe,KACfC,EAAc,KACdC,EAAsB,KACtBC,EAAuB,KACvBrX,EAAuB,KAcnBrE,EAAWC,cAG0D,KAAXxC,mBAAS,GAAE,GAApEke,EAAwB,KAAEC,EAA2B,KACW,KAAXne,mBAAS,GAAE,GAAhEoe,EAAsB,KAAEC,EAAyB,KACmB,KAAXre,mBAAS,GAAE,GAApEse,EAAwB,KAAEC,EAA2B,KAGa,KAAXve,mBAAS,GAAE,GAAlEwe,EAAuB,KAAEC,EAA0B,KACW,KAAXze,mBAAS,GAAE,GAA9D0e,EAAqB,KAAEC,EAAwB,KACmB,KAAX3e,mBAAS,GAAE,GAAlE4e,EAAuB,KAAEC,EAA0B,KAE1D,EAGIC,aAAsB/rB,EAAY6T,GAFvBmY,EAAqB,EAAlCC,YACSC,EAAiB,EAA1BC,QAGIC,EAA0B5X,uBAAY,WAC1C,MAA2B,SAAvBxU,EAAWqsB,QACN,EAEF,KACN,CAACrsB,IA0CJ,GAxCA+N,qBAAU,WACR,MAAkC/B,IAAKsgB,qBAAqBzY,GAApD0Y,EAAS,EAATA,UAAWC,EAAU,EAAVA,WAGnB,KADsCR,GAAyBE,GAE7D,OAAO,WACL1c,EAASU,IAAQuc,aAAa3lB,IAAawjB,kCAG/C,IAAMoC,EAAqBV,EAAsBW,EAAIT,EAAkBS,EACjEC,EAAsBZ,EAAsBa,EAAIX,EAAkBW,EAElEC,EAAc7qB,OAAOyO,8BAAgCtI,eAAc2kB,KAAK9e,YAAchM,OAAO+qB,WAC7FC,EAAkBhrB,OAAOyO,8BAAgCtI,eAAc2kB,KAAKG,UAAY,EAE9F1B,EAA4B7Q,EAbC,IAc7B2Q,EAA0BZ,EAAiB5gB,QAAQG,wBAAwBkjB,IAAMF,GACjF,IAAMG,EAAYN,EAAcnS,EAAiBuR,EAAkBS,EAftC,GAeiEH,EAAaE,EAE3GtB,EADsC,IACVgC,GAC5B,IAAMC,EAAiBrtB,EAAWstB,0BAC5BC,EAAmBvtB,EAAWwtB,QAAUH,EAAeV,EAAKU,EAAeV,EAAIC,EAAsB,EAC3GlB,EAA2B0B,EAAYjC,EAA2BiB,IAA4BmB,GAE9FzB,EAA2BnR,EAtBE,GAsBsCwQ,GAEnE,IAAMsC,EAAmBztB,EAAWwtB,QAAUH,EAAeR,EAAKQ,EAAeR,EAAID,EAAsB,EAC3GhB,EAAyBM,EAAkBW,EAAKD,EAAsB,EAAKL,EAAYkB,GAEvF,IAAMC,EAAsB,WAC1Ble,EAASU,IAAQuc,aAAa3lB,IAAawjB,kCAK7C,OAFAte,IAAK1I,iBAAiB,oBAAqBoqB,OAAqB5sB,EAAW+S,GAEpE,WACL7H,IAAKsI,oBAAoB,oBAAqBoZ,EAAqB7Z,MAEpE,CAAC6W,EAAkB/P,EAAgBqR,EAAuBE,EAAmBjB,EAAwBC,EAAyB1b,EAAUqE,IAEvIiX,IAAeC,GAAmBJ,KAAuBK,EAAgB,CAC3E,IAAM2C,EAAiBlf,KAAKmf,IAAIvC,EAAyBM,GAGnDkC,EAAcxC,EAAyBM,EAAwBA,EAFxC,EAEuFN,EAEpH,OACE,kBAAC,GAAmB,KAClB,yBAAKhmB,UAAU,iBAAiBmC,MAAO,CAAE8G,MAAO6c,EAA0B2C,MAAOvC,EAA0B4B,IAAK9B,KAChH,yBAAKhmB,UAAU,eAAemC,MAAO,CAAE0C,OAAQyjB,EAAgBR,IAAKU,EAAaC,MAAOvC,EAA2BJ,KACnH,yBAAK9lB,UAAU,iBAAiBmC,MAAO,CAAE8G,MAAOmd,EAAyBqC,MAAOjC,EAAyBsB,IAAKxB,IAC5G,yBAAKtmB,UAAU,gBAIvB,OAAO,M,w5CE7HT,gmGAAAjB,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,8YAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,8SAAAA,IAAA,4OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAuBA,IAAMoE,GAAY,CAChBxI,WAAYyI,IAAUwM,OAAOrM,WAC7B4O,gBAAiB/O,IAAUqG,KAC3BS,kBAAmB9G,IAAUqG,KAC7Bif,eAAgBtlB,IAAUqG,KAC1B2I,kBAAmBhP,IAAUE,MAG3BqlB,GAAS,EAEPC,GAAO,SAAH,GAQJ,IAPJjuB,EAAU,EAAVA,WACAwX,EAAe,EAAfA,gBACAjI,EAAiB,EAAjBA,kBACAwe,EAAc,EAAdA,eACAtW,EAAiB,EAAjBA,kBACAkT,EAAiB,EAAjBA,kBACAuD,EAAuB,EAAvBA,wBAEA,EASI1kB,qBAAW7J,KARbsX,EAAU,EAAVA,WACA1N,EAAM,EAANA,OACAsM,EAAkB,EAAlBA,mBACA0S,EAAiB,EAAjBA,kBACAN,EAAkB,EAAlBA,mBACAQ,EAAoB,EAApBA,qBAEAnD,EAAa,EAAbA,cAEI6I,EAAezkB,mBACf0kB,EAAqB1kB,mBACyB,KAAZuD,mBAAS,IAAG,GAA7CohB,EAAY,KAAEC,EAAe,KAC9B1I,EAAMlc,iBAAO,IACb8F,EAAWC,cACVlJ,EAAqB,GAAhBC,cAAgB,GAApB,GACF+nB,EAAmB,IAAIC,IAuB5B,KAZG9nB,aACF,SAACC,GAAK,MAAK,CACTC,IAAU6nB,yBAAyB9nB,GACnCC,IAAU8nB,+BAA+B/nB,GACzCC,IAAU+nB,yBAAyBhoB,GACnCC,IAAUgoB,gCAAgCjoB,GAC1CC,IAAUioB,mCAAmCloB,GAC7CC,IAAUkN,2BAA2BnN,GACrCC,IAAUkoB,sBAAsBnoB,GAChCC,IAAUmoB,wBAAwBpoB,MAEpC0C,KACD,GApBC2lB,EAAqB,KACrBC,EAA2B,KAC3BC,EAAqB,KACrBC,EAAyB,KACzBN,EAAkC,KAClCjjB,EAAiB,KACjBwjB,EAAkB,KAClBC,EAAoB,KAehBC,EAAUtvB,EACb+Y,aACAgO,MAAK,SAACvD,EAAGwD,GAAC,OAAKxD,EAAe,YAAIwD,EAAe,eAEpDsI,EAAQ5b,QAAO,SAAC6b,GAAC,OAAKL,EAAsBM,IAAID,EAAEva,OAAK3U,SAAQ,SAACkvB,GAAC,OAAKhB,EAAiBkB,IAAIF,EAAEva,OAE7FjH,qBAAU,WACR,IAAM6S,EAA4B,SAACC,EAAaC,GAC/B,WAAXA,GACFD,EAAYxgB,SAAQ,SAAC0gB,GACfmO,EAAsBM,IAAIzO,EAAM/L,KAClCxF,EAASU,IAAQwf,uBAAuB,CAAEC,QAAQ,EAAM/d,aAAcmP,EAAM/L,UAOpF,OAFAhJ,IAAK1I,iBAAiB,oBAAqBsd,OAA2B9f,EAAW8K,GAE1E,WACLI,IAAKsI,oBAAoB,oBAAqBsM,EAA2BhV,MAE1E,CAACsjB,IAEJnhB,qBAAU,WACR,IAAM6hB,EAAaxB,EAAmBtkB,QAChC+lB,EAAa1B,EAAarkB,QAAQG,wBAAwBC,OAChEkkB,EAAmBtkB,QAAU+lB,EAKzBD,GAAcnhB,KAAKqhB,MAAMF,KAAgBnhB,KAAKqhB,MAAMD,IACtDtmB,OAIJwE,qBAAU,WACR,GAAIihB,EAAuB,CACzB,IAAMe,EAAoB3nB,eAAcnE,iBAAiB,eAAe,GACxE2hB,EAAI9b,QAAQzJ,SAAQ,SAACiS,GACnB,IAAM0d,EAAQD,EAAkB1nB,cAAc,kCAAD,OAAmCiK,EAAE,MAC9E0d,GACFA,EAAMC,WAAW1F,YAAYyF,MAIjCpK,EAAI9b,QAAU,GAEd,IAAMnD,EAAQ,CACZ3G,aACAiX,cAGF+X,EAAsBb,EAAarkB,QAASnD,GAAO,WAAe,MAC1D6X,GAAU,EAAAxZ,UAASC,cAAa,mBAChCqN,EAAK,kBAAH,OAAqB0b,IAQ7B,OAPAA,KACApI,EAAI9b,QAAQmH,KAAKqB,GACjBkM,EAAQ6L,aAAa,gCAAiC/X,GACtDkM,EAAQlb,iBAAiB,aAAa,SAAC+D,GACrCA,EAAEC,qBAGGkX,SAKbzQ,qBAAU,WAGY,KADA8H,EAAmB7V,EAAWgV,KACxBuT,IAAsBN,GAC9ClU,GAAa,EAAM,KAEpB,CAACkU,EAAoBM,EAAmBxU,EAAc/T,EAAYuP,IAErEyR,cAAa,YACPiH,GAAuBM,GACzBxU,GAAa,EAAO,KAErB,CAACkU,EAAoBM,EAAmBxU,IAE3C,IAAM0L,EAAe,eArKvB,EAqKuB,GArKvB,EAqKuB,WAAG,WAAOpY,GAAC,wEAGL,GAAzBA,GAAKA,EAAEC,mBAEHiI,EAAmB,CAAF,eACiB,OAApCkI,GAAmBD,GAAiB,0BAarC,GAVG0X,EAAsBM,IAAIxvB,EAAWgV,KACvCxF,EAASU,IAAQwf,uBAAuB,CAAEC,QAAQ,EAAM/d,aAAc5R,EAAWgV,MAGnFia,GAA+BA,EAA4BjvB,GACtDiX,IACHjL,IAAKkkB,uBAAuBtkB,GAG5BR,YAAW,kBAAMoE,EAASU,IAAQga,YAAYpjB,IAAawjB,mCAAkC,OAE3FyD,GAAoBqB,GAAsBC,IAAyBc,KAAqBC,QAAQ,iBAMjG,GALDpkB,IAAKge,iBAAiBhqB,EAAY4L,GAClC0Z,EAActlB,EAAWgV,IACzBhJ,IAAKie,iBAAiBjqB,EAAY4L,GAC7BijB,GACHrf,EAASU,IAAQga,YAAYpjB,IAAaupB,oBAExCjB,EAAoB,CAAF,gBAC8D,OAA5EjV,EAAkBna,EAAWoa,cAAcC,MAAiC,UAC5ErO,IAAKsO,kBAAkBgW,0BAA0BnW,GAAgB,QACvEnO,IAAKsO,kBAAkBiW,mBAAmB,2CAnMlD,iLAsMG,gBAjCoB,sCAmCfC,EAAmBjC,EAAiBntB,KAAO,EAE3CqvB,EAAY3iB,IAAW,CAC3BmgB,MAAM,EACN/gB,SAAU+J,EACV,oBAAqBO,EACrBsM,OAAQoL,EAAsBM,IAAIxvB,EAAWgV,KAAOwb,EACpD,SAAYpB,GAAsBC,IAAyBc,KAAqBC,UAG5EM,EAAe5iB,IAAW,CAC9BwhB,SAAS,EACTqB,QAAS1Z,IAGXlJ,qBAAU,WAGHwB,GACH+f,EAAQjvB,SAAQ,SAACuwB,EAAO3J,GACtB,IAAM9C,EAActO,EAAmB+a,EAAM5b,IACxB,KAAhBmP,QAA6C,IAAhBA,GAAgClN,GAChElD,GAAa,EAAM,EAAIkT,QAI5B,CAAChQ,EAAY1H,IAEhBxB,qBAAU,WACJwB,GACFwE,GAAa,EAAO,KAErB,CAACxE,IAEJ,IAAMshB,GAAiB9R,OAAO+R,OAAOzC,GAAc0C,MAAK,SAACC,GAAG,OAAKA,KAE3DC,EAAqB,SAACL,GAEtBrC,EAAiBiB,IAAIoB,EAAM5b,MAC7BxF,EAASU,IAAQwf,uBAAuB,CAAEC,QAAQ,EAAM/d,aAAcgf,EAAM5b,MAC5EhJ,IAAK0I,qBAAqB9I,GAAmBoe,iBAAiB4G,KAI5DM,EAAqB,WAEzB,GAAI3C,EAAiBntB,KAAO,EAAG,CAC7B,IAAM+vB,EAAmB7B,EAAQ5b,QAAO,SAAC6b,GAAC,OAAKhB,EAAiBiB,IAAID,EAAEva,OACtEhJ,IAAK0I,qBAAqB9I,GAAmBwlB,kBAAkBD,GAC/DA,EAAiB9wB,SAAQ,SAACkvB,GAAC,OAAK/f,EAASU,IAAQwf,uBAAuB,CAAEC,QAAQ,EAAM/d,aAAc2d,EAAEva,WAItGjB,EAAeS,uBACnB,SAAC6C,EAAW4P,GACVqH,GAAgB,SAAC3sB,GAAG,gBACfA,GAAG,SACLslB,EAAQ5P,SAGb,CAACiX,IAGG9E,EAAmBxd,IAAKqlB,oBAAoBrxB,EAAY4L,GACxD0lB,GAAU9H,EAAiBloB,OAAS,EACpCsW,GAAkBiI,aAAmB7f,KAAgBwgB,KAAkBC,eAEvE8Q,GAAcjC,EAAQhuB,OAAS,EAAIguB,EAAQA,EAAQhuB,OAAS,GAAG0T,GAAK,KAE1E,OACE,yBACEhP,IAAKmoB,EACL9oB,UAAWorB,EACXne,GAAE,eAAUtS,EAAWgV,KAEvB,kBAAChN,GAAA,EAAM,CACL3C,UAAU,cACV+B,QAAS,SAACC,GAAC,OAAKoY,EAAgBpY,IAChCmqB,eAAc,eAAUxxB,EAAWgV,IACnCyc,YAAaxa,EACbhP,YAAY,qBAEd,kBAAC,GAAW,CACV+L,UAAW,EACXhU,WAAYA,EACZ+T,aAAcA,EACd0L,gBAAiBA,EACjBpI,UAAWgX,EAAa,GACxB9O,oBAAqB2P,EAAsBM,IAAIxvB,EAAWgV,IAC1DmC,SAAU+X,EAAsBM,IAAIxvB,EAAWgV,KAAOwb,EACtD/Y,kBAAmB,SAACpQ,GAClBie,EAActlB,EAAWgV,IACzByC,EAAkBpQ,IAEpBmQ,gBAAiBA,EACjBjI,kBAAmBA,KAEnB0H,GAAcwR,GAAwB0G,KAA+BvX,IACrE,oCACG0X,EAAQhuB,OAAS,GAChB,yBAAK+D,UAAWqrB,GACbF,GACC,kBAACxoB,GAAA,EAAM,CACLC,YAAY,oBACZ5C,UAAU,uBACV6J,MAAO3I,EAAE,sBACTa,QAAS8pB,IAGZ5B,EAAQ3tB,KAAI,SAACivB,EAAOxsB,GAAC,OACpB,yBAAKiB,UAAU,QAAQiN,GAAE,qBAAgBse,EAAM5b,IAAMpS,IAAG,qBAAgBguB,EAAM5b,KAC5E,kBAAC,GAAW,CACVhB,UAAW5P,EAAI,EACfxB,IAAKguB,EAAM5b,GACXhV,WAAY4wB,EACZ7c,aAAcA,EACdsD,UAAWgX,EAAajqB,EAAI,GAC5Bob,eAAgByR,EAChB9Z,SAAU+X,EAAsBM,IAAIoB,EAAM5b,IAC1CyC,kBAAmBA,EACnBD,gBAAiBA,EACjBjI,kBAAmBA,EACnBkQ,gBAAiBA,SAM1B6R,IACC,kBAAC,GAAgB,CACf9H,iBAAkBA,EAClBja,kBAAmBA,IAEtBshB,IAAkBthB,GACjB,kBAAC,GAAS,CACR4H,SAAUoa,IAAerC,EAAsBM,IAAI+B,IACnDvJ,qBAAsBkJ,EACtBlxB,WAAYA,KAKnBiX,IAAe8W,GAAkBpD,KAAuBuD,GACvD,kBAAC,GAA2B,CAC1BluB,WAAYA,EACZ0qB,iBAAkByD,EAClBxD,kBAAmBA,MAO7BsD,GAAKzlB,UAAYA,GAEFylB,UClWAA,Q,qBCFf,IAAI5d,EAAM,EAAQ,IACF/F,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQgG,WAAahG,EAAQiG,QAAUjG,KAG/CA,EAAU,CAAC,CAACkG,EAAOpM,EAAIkG,EAAS,MAG9C,IAAIjI,EAAU,CAEd,OAAiB,SAAUoO,GAgBX,IAAKxO,OAAOyO,8BAEV,YADA1L,SAAS2L,KAAKrL,YAAYmL,GAI5B,IAAIG,EAEJA,EAAgB5L,SAAS6L,qBAAqB,oBAEzCD,EAActP,SACjBsP,EAzBF,SAASE,EAAwBC,EAAS3N,EAAO4B,UAC/C,MAAMgM,EAAW,GAYjB,OATA5N,EAAKa,iBAAiB8M,GAAS1Q,QAAQ+J,GAAM4G,EAASC,KAAK7G,IAG3DhH,EAAKa,iBAAiB,KAAK5D,QAAQ+J,IAC7BA,EAAG8G,YACLF,EAASC,QAAQH,EAAwBC,EAAS3G,EAAG8G,eAIlDF,EAYSF,CAAwB,qBAG1C,MAAMK,EAAkB,GACxB,IAAK,IAAI/M,EAAI,EAAGA,EAAIwM,EAActP,OAAQ8C,IAAK,CAC7C,MAAMgN,EAAeR,EAAcxM,GACnC,GAAU,IAANA,EACFgN,EAAaF,WAAW5L,YAAYmL,GACpCA,EAASY,OAAS,WACZF,EAAgB7P,OAAS,GAC3B6P,EAAgB9Q,QAASiR,IAEvBA,EAAUC,UAAYd,EAASc,iBAIhC,CACL,MAAMD,EAAYb,EAASe,WAAU,GACrCJ,EAAaF,WAAW5L,YAAYgM,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEPjB,EAAI/F,EAASjI,GAI1BmO,EAAOiB,QAAUnH,EAAQoH,QAAU,I,sBClEnCD,EAAUjB,EAAOiB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACT,EAAOpM,EAAI,6pTAA8pT,KAGvrTqN,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,0lFCOvB,IAAMlJ,EAAY,CAChBkd,SAAUjd,IAAUqG,KACpB4iB,cAAejpB,IAAUqG,KACzB6iB,mBAAoBlpB,IAAUqG,KAC9B8iB,SAAUnpB,IAAUoG,IACpBgZ,SAAUpf,IAAUwM,OACpB4c,cAAeppB,IAAUE,KACzBmpB,qBAAsBrpB,IAAUwM,OAChC8c,aAActpB,IAAUwM,OACxB+c,wBAAyBvpB,IAAUC,OACnCmJ,eAAgBpJ,IAAUE,MAGtBspB,EAAwB,SAAH,GAWrB,IAVJvM,EAAQ,EAARA,SACAgM,EAAa,EAAbA,cACAC,EAAkB,EAAlBA,mBACAC,EAAQ,EAARA,SACA/J,EAAQ,EAARA,SACAgK,EAAa,EAAbA,cACAC,EAAoB,EAApBA,qBACAC,EAAY,EAAZA,aACAC,EAAuB,EAAvBA,wBACAngB,EAAc,EAAdA,eAEOtL,EAAqB,EAAhBC,cAAgB,GAApB,GACyC,IAAfyG,oBAAS,GAAM,GAA1CilB,EAAU,KAAEC,EAAW,KAExBva,EAAkBiI,YAAmBiS,KAA0BtR,IAAkBC,eAEjF2R,EACJ,yBACE/sB,UAAWyI,IAAW,CACpBukB,OAAO,EACPJ,uBAAuB,EACvBK,KAAMX,EACNY,mBAAoB3a,IAEtB5R,IAAK4rB,EACLrb,eAAczP,IAAa+d,qBAC3Brd,MAAK,KAAOqgB,GACZ2K,YAAa,SAACnrB,GACZA,EAAEC,mBAEJ+hB,YAAa,SAAChiB,GACRqe,IACFre,EAAEC,kBACFuqB,MAGJ3rB,UAAW,SAACmB,GACI,WAAVA,EAAEzE,KACJivB,MAIJ,yBACExsB,UAAWyI,IAAW,CACpB,4BAA4B,EAC5B,SAAYokB,IAEd7I,YAAa,SAAChiB,GACRqe,GACFre,EAAEC,oBAILoe,GACC,yBAAKrgB,UAAU,yBACb,kBAAC2C,EAAA,EAAM,CACLE,IAAKgqB,EAAa,oBAAsB,kBACxC7sB,UAAU,eACV4C,YAAanB,IAAa2rB,mCAC1BrrB,QAAS,kBAAM+qB,GAAaD,MAE9B,0BAAM7sB,UAAU,+BAA+BkB,EAAE,mBACjD,kBAACyB,EAAA,EAAM,CACLE,IAAK,aACLD,YAAanB,IAAa4rB,kCAC1BtrB,QAASyqB,KAIf,kBAAC,IAAYc,SAAQ,CAAChyB,MAAOoxB,GAC3B,kBAAC9D,EAAA,EAAI,CACHjuB,WAAY8xB,EACZta,iBAAiB,EACjBjI,mBAAmB,EACnBkI,kBAAmB,eAErB,kBAAC9F,EAAA,EAAqB,CACpBC,aAAcogB,EACdngB,eAAgBA,OAO1B,OAAO6f,GAAiB9Z,EACtBwa,EAEA,kBAAC,IAAS,CAACQ,OAAO,0EAA0ER,IAIhGH,EAAsBzpB,UAAYA,EAEnBypB,Q,6rFC7Gf,IAAMzpB,EAAY,CAChBxI,WAAYyI,IAAUwM,OACtB4c,cAAeppB,IAAUE,MAGrBkqB,EAAiC,SAAH,GAAsC,IAAhC7yB,EAAU,EAAVA,WAAY6xB,EAAa,EAAbA,cAsBnD,IAZGnrB,aACF,SAACC,GAAK,MAAK,CACTC,IAAUke,cAAcne,EAAOG,IAAaie,aAC5Cne,IAAUksB,oBAAoBnsB,GAC9BC,IAAUke,cAAcne,EAAOG,IAAaisB,YAC5CnsB,IAAUosB,mBAAmBrsB,GAC7BC,IAAUqsB,6BAA6BtsB,GACvCC,IAAUssB,gBAAgBvsB,GAC1BC,IAAUqhB,mBAAmBthB,GAC7BC,IAAUkN,2BAA2BnN,MAEvC0C,KACD,GApBCgc,EAAgB,KAChB8N,EAAgB,KAChBC,EAAe,KACfC,EAAe,KACf1b,EAAuB,KACvBL,EAAY,KACZ2Q,EAAkB,KAClBpU,EAAuB,KAcnBrE,EAAWC,cAC4C,IAA7BxC,mBAAS,CAAEqmB,KAAM,EAAGnG,IAAK,IAAI,GAAtDtF,EAAQ,KAAE0L,EAAW,KACtB3B,EAAWloB,mBAEXgc,EAAW8N,cACX9B,EAAgBhM,KAAc+N,KAAkBC,IAChDC,EAA2BtO,GAAqB8N,GAAoBC,GAAuC,eAApBC,EAG7FO,YAAkBhC,GAAU,SAACvqB,GAC3B,IAAMwsB,EAAazrB,cAAcC,cAAc,+BACzCyrB,EAAsBD,aAAU,EAAVA,EAAYE,SAAS1sB,EAAEsD,QAC7CoF,EAAkB3H,cAAcC,cAAc,kCAAD,OAAmCrI,EAAWgV,GAAE,OAC7Fgf,EAA2BjkB,aAAe,EAAfA,EAAiBgkB,SAAS1sB,EAAEsD,QACvDspB,EAAaC,cACbC,EAAeC,cACfC,EAAcC,cAMfR,GAAwBE,GAA6BG,GAAiBE,GAAgBJ,GACzFzkB,EAASU,IAAQuc,aAAa3lB,IAAa+d,0BAI/C,IAAM8M,GAAsBgC,EAEtBY,EAAmB,WACnB5C,GAAsBC,EAAS9nB,UAAY4b,GAC7C6N,EAAYiB,YAAiBx0B,EAAY4xB,EAAU/d,KAIvDjK,2BAAgB,WACd2qB,MACC,CAAC1gB,EAAyB7T,IAE7B,IAAMy0B,EAAeC,KAAS,WAC5BH,MAjCoC,GAkCJ,CAAE,UAAY,EAAM,SAAW,IAEjE3qB,2BAAgB,WAGd,OAFA3H,OAAOqB,iBAAiB,SAAUmxB,GAE3B,WACLxyB,OAAOqS,oBAAoB,SAAUmgB,MAEtC,IAGH,IAAoE,IAAZxnB,mBAAS,IAAG,GAA7D8I,EAAoB,KAAE4e,EAAuB,KAC9C9iB,EAAiB,SAAC+iB,EAAclU,GACpCiU,GAAwB,SAAChzB,GAAG,cACvBA,GAAG,QACLizB,EAAY,YAAQjzB,EAAIizB,IAAiB,IAAK,EAAGlU,UAI2B,IAAnBzT,wBAASnM,GAAU,GAA1EkxB,EAAuB,KAAE6C,EAA0B,KAEM,IAAZ5nB,mBAAS,IAAG,GAAzD4I,EAAkB,KAAEif,EAAqB,KAC1CvU,EAAqB/L,uBACzB,SAAC2P,EAAayQ,GACZE,GAAsB,SAACnzB,GAAG,cACrBA,GAAG,QACLizB,EAAezQ,SAGpB,CAAC2Q,IAGuD,IAAZ7nB,mBAAS,IAAG,GAAnD6I,GAAe,KAAEif,GAAkB,KACpCvM,GAAkBhU,uBACtB,SAACwgB,EAAcJ,GACbG,IAAmB,SAACpzB,GAAG,cAClBA,GAAG,QACLizB,EAAeI,SAGpB,CAACD,KAsBGhD,GAAe,CACnB1R,YAAa,GACb9W,OAAQ,WAAM,OACU,QAAlB,EAAAyC,IAAKipB,qBAAa,aAAlB,EAAoB5S,aAAc6S,IAAYC,eAChD5B,EAAYiB,YAAiBx0B,EAAY4xB,EAAU/d,KAGvDoD,YAAY,EACZsR,kBAAmBvc,IAAKmI,kBAAkBnU,KAAgBA,EAAWG,cACrE0V,qBACA0K,qBACAzK,mBACA0S,mBACAP,qBACA3H,wBAAyB,aACzBmI,sBAAsB,EACtBC,uBAAuB,EACvBpR,eACAK,0BACA2N,cAAeuP,EACf9e,uBACAlE,iBACA2T,iBA1CuB,SAACoP,GACxBD,GAAwB,SAAChzB,GAAG,cACvBA,GAAG,QACLizB,EAAe,SAwClBrP,iBArCuB,SAACqP,EAAc1iB,GACtC,IAAMkjB,EAAiBrf,EAAqB6e,GAC5C,IAAIQ,aAAc,EAAdA,EAAgB9zB,QAAS,EAAG,CAC9B,IAAM2lB,EAAQmO,EAAejxB,QAAQ+N,GACjC+U,GAAS,IACXmO,EAAeC,OAAOpO,EAAO,GAC7B0N,GAAwB,SAAChzB,GAAG,cACvBA,GAAG,QACLizB,EAAY,EAAOQ,YAgC5B,OACE,kBAAC,EAAqB,CACpB1P,SAAUA,EACVgM,cAAeA,EACfC,mBAAoBA,EACpBC,SAAUA,EACV/J,SAAUA,EACVgK,cAAeA,EACfC,qBAAsB9xB,EACtB+xB,aAAcA,GACdC,wBAAyBA,EACzBngB,eAAgBA,KAKtBghB,EAA+BrqB,UAAYA,EAE5BqqB,QC7LAA", "file": "chunks/chunk.29.js", "sourcesContent": ["import React from 'react';\n\nconst NoteContext = React.createContext();\n\nexport default NoteContext;", "const setAnnotationRichTextStyle = (editor, annotation) => {\n  const richTextStyle = {};\n  const ops = editor.getContents().ops;\n  let breakpoint = 0;\n  ops.forEach((item) => {\n    const attributes = item.attributes;\n    const isMention = item.insert?.mention;\n    let value = item.insert;\n    if (isMention) {\n      const mention = item.insert.mention;\n      value = mention.denotationChar + mention.value;\n    }\n    const cssStyle = {};\n    if (attributes === null || attributes === undefined ? undefined : attributes.bold) {\n      cssStyle['font-weight'] = 'bold';\n    }\n    if (attributes === null || attributes === undefined ? undefined : attributes.italic) {\n      cssStyle['font-style'] = 'italic';\n    }\n    if (attributes === null || attributes === undefined ? undefined : attributes.color) {\n      cssStyle['color'] = attributes.color;\n    }\n    if (attributes === null || attributes === undefined ? undefined : attributes.underline) {\n      cssStyle['text-decoration'] = 'word';\n    }\n    if (attributes === null || attributes === undefined ? undefined : attributes.strike) {\n      if (cssStyle['text-decoration']) {\n        cssStyle['text-decoration'] = `${cssStyle['text-decoration']} line-through`;\n      } else {\n        cssStyle['text-decoration'] = 'line-through';\n      }\n    }\n    if (attributes === null || attributes === undefined ? undefined : attributes.size) {\n      cssStyle['font-size'] = attributes.size;\n    }\n    if (attributes === null || attributes === undefined ? undefined : attributes.font) {\n      cssStyle['font-family'] = attributes.font;\n    }\n\n    richTextStyle[breakpoint] = cssStyle;\n    breakpoint += value.length;\n  });\n  annotation.setRichTextStyle(richTextStyle);\n};\n\nexport default setAnnotationRichTextStyle;\n", "\n/**\n * @ignore\n * Converts text with newlines (\"\\n\") into <p>...</p> elements to ensure proper\n * multiline handling in the editor. If no newlines are present, the original\n * text is returned.\n *\n * @param {string} value - The text to convert.\n * @returns {string} The converted text with <p> tags around each line.\n */\nfunction convertNewlinesToParagraphs(value) {\n  if (!value) {\n    return value;\n  }\n  const valueSplit = value.split('\\n');\n  const containsNewlines = valueSplit.length > 1;\n  if (!containsNewlines) {\n    return value;\n  }\n\n  const contentArray = valueSplit;\n  return contentArray.map((text) => `<p>${text || '<br>'}</p>`).join('');\n}\n\nexport default convertNewlinesToParagraphs;", "import { Quill } from 'react-quill';\n\n// Overriding clipboard module to fix cursor issue after pasting text\nconst Clipboard = Quill.import('modules/clipboard');\nconst { quillShadowDOMWorkaround } = window.Core;\n\nexport class QuillPasteExtra extends Clipboard {\n  constructor(quill, options) {\n    quillShadowDOMWorkaround(quill);\n    super(quill, options);\n  }\n}\n\n// We override the default keyboard module to disable the list autofill feature\nconst Keyboard = Quill.import('modules/keyboard');\n\nexport class CustomKeyboard extends Keyboard {\n  static DEFAULTS = {\n    ...Keyboard.DEFAULTS,\n    bindings: {\n      ...Keyboard.DEFAULTS.bindings,\n      'list autofill': undefined,\n    }\n  };\n}\n\nexport class BlurInputModule {\n  constructor(quill) {\n    this.quill = quill;\n    this.noteContainer = quill.root.closest('.Note');\n    this.shouldSkipInput = false;\n\n    this.noteContainer?.addEventListener('keydown', this.handleKeyDown.bind(this));\n  }\n\n  handleKeyDown = (event) => {\n    if (event.key === 'Escape') {\n      this.blurQuill();\n    } else if (event.key === 'Tab' && this.shouldSkipInput) {\n      event.preventDefault();\n      this.moveFocus(event.shiftKey);\n    }\n  };\n\n  blurQuill() {\n    this.shouldSkipInput = true;\n    this.quill.blur();\n    this.quill.container.tabIndex = 0;\n    this.quill.container.focus();\n  }\n\n  moveFocus(backwards) {\n    // .ql-container.ql-snow is the quill editor selector\n    const focusableElements = Array.from(\n      this.noteContainer.querySelectorAll('.ql-container.ql-snow, button.modular-ui')\n    );\n    const currentIndex = focusableElements.indexOf(this.quill.container);\n\n    if (currentIndex !== -1) {\n      const newIndex = backwards ? currentIndex - 1 : currentIndex + 1;\n      focusableElements[newIndex]?.focus();\n    }\n\n    this.shouldSkipInput = false;\n    this.quill.container.tabIndex = -1;\n  }\n}\n", "import React from 'react';\nimport ReactQuill, { Quill } from 'react-quill';\nimport 'quill-mention';\nimport mentions<PERSON>anager from 'helpers/MentionsManager';\nimport Button from 'components/Button';\nimport { useTranslation } from 'react-i18next';\nimport { useSelector } from 'react-redux';\nimport DataElements from 'constants/dataElement';\nimport selectors from 'selectors';\nimport getRootNode from 'helpers/getRootNode';\nimport transformTextForQuill from 'helpers/convertNewlinesToParagraphs';\nimport { CustomKeyboard, BlurInputModule, QuillPasteExtra } from 'helpers/quillModules';\nimport '../../../constants/quill.scss';\nimport './CommentTextarea.scss';\n\nlet globalUserData = [];\n\n// These are the formats that will be accepted by quill\n// removed images and videos\nconst formats = [\n  'background',\n  'bold',\n  'color',\n  'font',\n  'code',\n  'italic',\n  'link',\n  'size',\n  'strike',\n  'script',\n  'underline',\n  'blockquote',\n  'header',\n  'indent',\n  'list',\n  'align',\n  'direction',\n  'code-block',\n  'formula',\n  'mention',\n];\n\nQuill.register('modules/keyboard', CustomKeyboard, true);\nQuill.register('modules/clipboard', QuillPasteExtra, true);\nQuill.register('modules/blurInput', BlurInputModule);\n\n// mentionsModule has to be outside the funtion to be able to access it without it being destroyed and recreated\nconst mentionModule = {\n  mention: {\n    allowedChars: /^[A-Za-z\\sÅÄÖåäö0-9\\-_]*$/,\n    mentionDenotationChars: ['@', '#'],\n    mentionContainerClass: 'mention__element',\n    mentionListClass: 'mention__suggestions__list',\n    listItemClass: 'mention__suggestions__item',\n    renderItem(item) {\n      // quill-mentions does not support jsx being returned\n      const div = document.createElement('div');\n      div.innerText = item.value;\n      if (item.email) {\n        const para = document.createElement('p');\n        para.innerText = item.email;\n        para.className = 'email';\n        div.appendChild(para);\n      }\n      return div;\n    },\n    async source(searchTerm, renderList) {\n      const mentionsSearchFunction = mentionsManager.getMentionLookupCallback();\n      const foundUsers = await mentionsSearchFunction(globalUserData, searchTerm);\n      renderList(foundUsers, searchTerm);\n    }\n  }\n};\n\nconst CommentTextarea = React.forwardRef(\n  (\n    {\n      value = '',\n      onChange,\n      onKeyDown,\n      onBlur,\n      onFocus,\n      userData,\n      isReply,\n    },\n    ref\n  ) => {\n    const [t] = useTranslation();\n\n    const isAddReplyAttachmentDisabled = useSelector((state) => selectors.isElementDisabled(state, DataElements.NotesPanel.ADD_REPLY_ATTACHMENT_BUTTON));\n\n    globalUserData = userData;\n\n    const addAttachment = () => {\n      getRootNode().querySelector('#reply-attachment-picker')?.click();\n    };\n\n    const onClick = (e) => {\n      e.preventDefault();\n      e.stopPropagation();\n    };\n\n    const onScroll = (e) => {\n      e.preventDefault();\n      e.stopPropagation();\n    };\n\n    value = transformTextForQuill(value);\n    const baseModule = { blurInput: {} };\n\n    // onBlur and onFocus have to be outside in the div because of quill bug\n    return (\n      <div className='comment-textarea' onBlur={onBlur} onFocus={onFocus} onClick={onClick} onScroll={onScroll}>\n        <ReactQuill\n          className='comment-textarea ql-container ql-editor'\n          style={{ overflowY: 'visible' }}\n          ref={(ele) => {\n            if (ele) {\n              ele.getEditor().root.ariaLabel = `${isReply ? t('action.reply') : t('action.comment')}`;\n            }\n            return ref(ele);\n          }}\n          modules={userData && userData.length > 0 ? { ...baseModule, ...mentionModule } : baseModule }\n          theme=\"snow\"\n          value={value}\n          placeholder={`${isReply ? t('action.reply') : t('action.comment')}...`}\n          onChange={onChange}\n          onKeyDown={onKeyDown}\n          formats={formats}\n        />\n        {isReply && !isAddReplyAttachmentDisabled &&\n          <Button\n            className='add-attachment'\n            dataElement={DataElements.NotesPanel.ADD_REPLY_ATTACHMENT_BUTTON}\n            img='ic_fileattachment_24px'\n            title={`${t('action.add')} ${t('option.type.fileattachment')}`}\n            onClick={addAttachment}\n          />\n        }\n      </div>\n    );\n  });\n\nCommentTextarea.displayName = 'CommentTextarea';\n\nexport default CommentTextarea;\n", "import React, { useLayoutEffect, useRef, useContext } from 'react';\nimport PropTypes from 'prop-types';\nimport { useSelector, shallowEqual } from 'react-redux';\nimport throttle from 'lodash/throttle';\nimport NoteContext from 'components/Note/Context';\nimport selectors from 'selectors';\nimport CommentTextarea from './CommentTextarea/CommentTextarea';\nimport mentionsManager from 'helpers/MentionsManager';\n\nconst propTypes = {\n  // same the value attribute of a HTML textarea element\n  value: PropTypes.string,\n  // same the placeholder attribute of a HTML textarea element\n  placeholder: PropTypes.string,\n  // same the onChange attribute of a HTML textarea element\n  onChange: PropTypes.func.isRequired,\n  // same the onBlur attribute of a HTML textarea element\n  onBlur: PropTypes.func,\n  // same the onBlur attribute of a HTML textarea element\n  onFocus: PropTypes.func,\n  // a function that will be invoked when Ctrl + Enter or Cmd + Enter or only Enter are pressed\n  onSubmit: PropTypes.func,\n};\n\nconst NoteTextarea = React.forwardRef((props, forwardedRef) => {\n  const [\n    userData,\n    canSubmitByEnter,\n  ] = useSelector(\n    (state) => [\n      selectors.getUserData(state),\n      selectors.isNoteSubmissionWithEnterEnabled(state),\n      selectors.getAutoFocusNoteOnAnnotationSelection(state),\n      selectors.getIsNoteEditing(state),\n    ],\n    shallowEqual,\n  );\n\n  const { resize } = useContext(NoteContext);\n  const textareaRef = useRef();\n  const prevHeightRef = useRef();\n\n  useLayoutEffect(() => {\n    // when the height of the textarea changes, we also want to call resize\n    // to clear the cell measurer cache and update the note height in the virtualized list\n    const boxDOMElement = textareaRef.current?.editor?.container.firstElementChild;\n    const boundingBox = boxDOMElement?.getBoundingClientRect() || {};\n    if (prevHeightRef.current && prevHeightRef.current !== boundingBox.height) {\n      resize();\n    }\n    prevHeightRef.current = boundingBox.height;\n    // we need value to be in the dependency array because the height will only change when value changes\n    // eslint-disable-next-line\n  }, [props.value, resize]);\n\n\n  const handleKeyDown = (e) => {\n    const enterKey = 13;\n    const enterKeyPressed = e.which === enterKey;\n    if (enterKeyPressed) {\n      const isSubmittingByEnter = canSubmitByEnter;\n      const isSubmittingByCtrlEnter = (e.metaKey || e.ctrlKey);\n\n      if (isSubmittingByEnter || isSubmittingByCtrlEnter) {\n        props.onSubmit(e);\n      }\n    }\n  };\n\n  const handleChange = (content, delta, source, editor) => {\n    // Removes Non-breaking Space and replaces with regular space\n    content = content.replace(/&nbsp;/g, ' ');\n\n    if (textareaRef.current) {\n      /* For the React Quill editor, the text won't ever be empty, at least a '\\n'\n       * will be there, so it is necessary to trim the value to check if it is empty\n       */\n      const isEmpty = editor && editor.getText().trim() === '' && content === '<p><br></p>';\n      let value = '';\n\n      if (!isEmpty) {\n        value = content.target ? content.target.value : content;\n      }\n      props.onChange(value);\n\n      // If the delta contains a mention, then move the cursor to the end of the editor.\n      // This is necessary instead of using debounce when mentioning a user because\n      // the debounce was generating the following bug\n      // https://apryse.atlassian.net/browse/WVR-2380\n      const deltaContainsMention = mentionsManager.doesDeltaContainMention(delta.ops);\n\n      if (deltaContainsMention) {\n        const formattedText = mentionsManager.getFormattedTextFromDeltas(delta.ops);\n        const mentionData = mentionsManager.extractMentionDataFromStr(formattedText);\n        const editortextValue = editor.getText();\n        const totalTextLength = editortextValue.length + mentionData.plainTextValue.length;\n        const textareaEditor = textareaRef.current?.editor;\n        setTimeout(() => textareaEditor?.setSelection(totalTextLength, totalTextLength), 1);\n      }\n    }\n  };\n\n  const textareaProps = {\n    ...props,\n    ref: (el) => {\n      textareaRef.current = el;\n      forwardedRef(el);\n    },\n    onChange: throttle(handleChange, 100),\n    onKeyDown: handleKeyDown,\n    userData,\n  };\n\n  return (\n    <>\n      <CommentTextarea {...textareaProps}/>\n    </>\n  );\n});\n\nNoteTextarea.displayName = 'NoteTextarea';\nNoteTextarea.propTypes = propTypes;\n\nexport default NoteTextarea;\n", "import NoteTextarea from './NoteTextarea';\n\nexport default NoteTextarea;", "import i18next from 'i18next';\nimport core from 'core';\n\nfunction createStateAnnotation(annotation, state, documentViewerKey = 1) {\n  // TODO: the code below is copied from annotManager.updateAnnotationState in WebViewer to work around the issue\n  // in https://github.com/PDFTron/webviewer-ui/issues/620\n  // the implement before wasn't causing any actual issues, but it was confusing and unnecessary to trigger two annotationChanged events when a status is set\n  // A proper fix should be done once https://trello.com/c/zWlkygNb/1023-consider-adding-a-setlocalizationhandler-to-corecontrols is implemented\n  // at that time, we could use the translation handler(t) internally in updateAnnotationState before setting the contents, and use that function instead in this component\n\n  const stateAnnotation = new window.Core.Annotations.StickyAnnotation();\n\n  stateAnnotation['InReplyTo'] = annotation['Id'];\n  stateAnnotation['X'] = annotation['X'];\n  stateAnnotation['Y'] = annotation['Y'];\n  stateAnnotation['PageNumber'] = annotation['PageNumber'];\n  stateAnnotation['Subject'] = 'Sticky Note';\n  stateAnnotation['Author'] = core.getCurrentUser();\n  stateAnnotation['State'] = state;\n  stateAnnotation['StateModel'] = state === 'Marked' || state === 'Unmarked' ? 'Marked' : 'Review';\n  stateAnnotation['Hidden'] = true;\n  stateAnnotation.enableSkipAutoLink();\n\n  const displayAuthor = core.getDisplayAuthor(stateAnnotation['Author'], documentViewerKey);\n  const stateMessage = i18next.t(`option.state.${state.toLowerCase()}`);\n  const contents = `${stateMessage} ${i18next.t('option.state.setBy')} ${displayAuthor}`;\n  stateAnnotation.setContents(contents);\n\n  return stateAnnotation;\n}\n\nexport {\n  createStateAnnotation,\n};\n", "import React, { useState, useEffect, useLayoutEffect } from 'react';\nimport PropTypes from 'prop-types';\nimport { useTranslation } from 'react-i18next';\nimport classNames from 'classnames';\n\nimport './NoteTextPreview.scss';\n\nfunction NoteTextPreview(props) {\n  /* This replace is to remove the break line that the React Quill component add into the text */\n  const text = props.children.replace(/\\n$/, '');\n  const {\n    panelWidth,\n    linesToBreak,\n    renderRichText,\n    richTextStyle,\n    resize,\n    style,\n    /* If text being previewed is a comment it gets a darker font color */\n    comment = false,\n    beforeContent = () => {},\n  } = props;\n  const [expanded, setExpand] = useState(false);\n  const [previewElementWidth, setPreviewWidth] = useState(null);\n  const [charsPerLine, setCharsperLine] = useState(null);\n  const [showPrompt, setShowPrompt] = useState(false);\n  const ref = React.useRef(null);// Must import this way in order to mock it properly\n  const { t } = useTranslation();\n\n  const onClickHandler = (event) => {\n    event.stopPropagation();\n    setExpand(!expanded);\n    resize && resize();\n  };\n\n  const textToDisplay = expanded ? text : text.substring(0, charsPerLine * linesToBreak);\n  const prompt = expanded ? t('action.showLess') : t('action.showMore');\n  const noteTextPreviewClass = classNames('note-text-preview', { 'preview-comment': comment });\n\n  useEffect(() => {\n    const textNodeWidth = ref.current.clientWidth;\n    setPreviewWidth(textNodeWidth);\n  }, [panelWidth]);\n\n  // useLayoutEffect to avoid a flicker before we get the final text prop\n  useLayoutEffect(() => {\n    function getTextWidth(text) {\n      const canvas = document.createElement('canvas');\n      const context = canvas.getContext('2d');\n      context.font = '13px sans-serif';\n\n      return context.measureText(text).width;\n    }\n\n    const textWidth = getTextWidth(text);\n    const averageCharWidth = textWidth / text.length;\n    const charsPerLine = Math.floor(previewElementWidth / averageCharWidth);\n    setCharsperLine(charsPerLine);\n\n    const totalLines = textWidth / previewElementWidth;\n    setShowPrompt(totalLines > linesToBreak);\n  }, [text, previewElementWidth]);\n\n  return (\n    <div className={noteTextPreviewClass} ref={ref} style={style} aria-live=\"polite\">\n      {beforeContent()}\n      {renderRichText && richTextStyle\n        ? renderRichText(textToDisplay, richTextStyle, 0)\n        : textToDisplay} {showPrompt && <button className=\"note-text-preview-prompt\" onClick={onClickHandler}>{prompt}</button>}\n    </div>\n  );\n}\n\nNoteTextPreview.propTypes = {\n  panelWidth: PropTypes.number,\n  linesToBreak: PropTypes.number,\n  renderRichText: PropTypes.func,\n  richTextStyle: PropTypes.any,\n  resize: PropTypes.func,\n  style: PropTypes.any,\n  comment: PropTypes.bool,\n  beforeContent: PropTypes.func,\n};\n\nexport default NoteTextPreview;", "import { useLayoutEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport DataElements from 'src/constants/dataElement';\nimport actions from 'actions';\nimport selectors from 'selectors';\n\nconst createFlyoutItem = (option, icon, dataElement) => ({\n  icon,\n  label: `option.state.${option.toLowerCase()}`,\n  title: `option.state.${option.toLowerCase()}`,\n  option,\n  dataElement,\n});\n\nexport const noteStateFlyoutItems = [\n  createFlyoutItem('Accepted', 'icon-annotation-status-accepted', 'noteStateFlyoutAcceptedOption'),\n  createFlyoutItem('Rejected', 'icon-annotation-status-rejected', 'noteStateFlyoutRejectedOption'),\n  createFlyoutItem('Cancelled', 'icon-annotation-status-cancelled', 'noteStateFlyoutCancelledOption'),\n  createFlyoutItem('Completed', 'icon-annotation-status-completed', 'noteStateFlyoutCompletedOption'),\n  createFlyoutItem('None', 'icon-annotation-status-none', 'noteStateFlyoutNoneOption'),\n  createFlyoutItem('Marked', 'icon-annotation-status-marked', 'noteStateFlyoutMarkedOption'),\n  createFlyoutItem('Unmarked', 'icon-annotation-status-unmarked', 'noteStateFlyoutUnmarkedOption'),\n];\n\nconst NoteStateFlyout = (props) => {\n  const {\n    noteId,\n    handleStateChange = () => {},\n    isMultiSelectMode = false,\n  } = props;\n\n  const dispatch = useDispatch();\n\n  const selectorSuffix = isMultiSelectMode ? '' : `-${noteId}`;\n  const flyoutSelector = `${DataElements.NOTE_STATE_FLYOUT}${selectorSuffix}`;\n  const currentFlyout = useSelector((state) => selectors.getFlyout(state, flyoutSelector));\n\n  const handleClick = (noteState) => {\n    handleStateChange(noteState);\n  };\n\n  useLayoutEffect(() => {\n    const noteStateFlyout = {\n      dataElement: flyoutSelector,\n      className: 'NoteStateFlyout',\n      items: noteStateFlyoutItems.map((item) => {\n        return {\n          ...item,\n          onClick: () => handleClick(item.option),\n        };\n      }),\n    };\n\n    if (!currentFlyout) {\n      dispatch(actions.addFlyout(noteStateFlyout));\n    } else {\n      dispatch(actions.updateFlyout(noteStateFlyout.dataElement, noteStateFlyout));\n    }\n  }, [handleStateChange]);\n\n  return null;\n};\n\nNoteStateFlyout.propTypes = {\n  noteId: PropTypes.string,\n  handleStateChange: PropTypes.func,\n  isMultiSelectMode: PropTypes.bool,\n};\n\nexport default NoteStateFlyout;", "import NoteStateFlyout from './NoteStateFlyout';\n\nexport default NoteStateFlyout;", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./NoteTextPreview.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".note-text-preview{font-size:13px;color:var(--gray-7);padding-right:var(--note-content-right-padding-width);-webkit-user-select:text!important;-moz-user-select:text!important;user-select:text!important;cursor:text;height:-moz-fit-content;height:fit-content;width:calc(100% - var(--note-content-right-padding-width));overflow:hidden}.note-text-preview>*{pointer-events:all}.preview-comment{color:var(--text-color)}.note-text-preview-prompt{cursor:pointer;color:var(--primary-button);text-decoration:underline;position:relative;pointer-events:auto;background:transparent;border:none;padding:0}.note-text-preview-prompt:hover{color:var(--primary-button-hover)}.trackedChangePopup .note-text-preview{max-height:400px;overflow-y:auto;width:calc(100% + var(--note-content-right-padding-width))}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../node_modules/css-loader/index.js!../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../node_modules/sass-loader/dist/cjs.js!./quill.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".ql-container{box-sizing:border-box;font-family:Helvetica,Arial,sans-serif;font-size:13px;height:100%;margin:0;position:relative}.ql-container.ql-disabled .ql-tooltip{visibility:hidden}.ql-container.ql-disabled .ql-editor ul[data-checked]>li:before{pointer-events:none}.ql-clipboard{left:-100000px;height:1px;overflow-y:hidden;position:absolute;top:50%}.ql-clipboard p{margin:0;padding:0}.ql-container .ql-editor{box-sizing:border-box;line-height:1.42;height:100%;outline:none;overflow-y:auto;padding:12px 15px;-o-tab-size:4;tab-size:4;-moz-tab-size:4;text-align:left;white-space:pre-wrap;word-wrap:break-word;-webkit-user-select:text;-moz-user-select:text;user-select:text}.ql-editor>*{cursor:text}.ql-editor blockquote,.ql-editor h1,.ql-editor h2,.ql-editor h3,.ql-editor h4,.ql-editor h5,.ql-editor h6,.ql-editor ol,.ql-editor p,.ql-editor pre,.ql-editor ul{counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor p{margin:0;padding:0}.ql-editor ol,.ql-editor ul{padding-left:1.5em}.ql-editor ol>li,.ql-editor ul>li{list-style-type:none}.ql-editor ul>li:before{content:\\\"\\\\2022\\\"}.ql-editor ul[data-checked=false],.ql-editor ul[data-checked=true]{pointer-events:none}.ql-editor ul[data-checked=false]>li *,.ql-editor ul[data-checked=true]>li *{pointer-events:all}.ql-editor ul[data-checked=false]>li:before,.ql-editor ul[data-checked=true]>li:before{color:#777;cursor:pointer;pointer-events:all}.ql-editor ul[data-checked=true]>li:before{content:\\\"\\\\2611\\\"}.ql-editor ul[data-checked=false]>li:before{content:\\\"\\\\2610\\\"}.ql-editor li:before{display:inline-block;white-space:nowrap;width:1.2em}.ql-editor li:not(.ql-direction-rtl):before{margin-left:-1.5em;margin-right:.3em;text-align:right}.ql-editor li.ql-direction-rtl:before{margin-left:.3em;margin-right:-1.5em}.ql-editor ol li:not(.ql-direction-rtl),.ql-editor ul li:not(.ql-direction-rtl){padding-left:1.5em}.ql-editor ol li.ql-direction-rtl,.ql-editor ul li.ql-direction-rtl{padding-right:1.5em}.ql-editor ol li{counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;counter-increment:list-0}.ql-editor ol li:before{content:counter(list-0,decimal) \\\". \\\"}.ql-editor ol li.ql-indent-1{counter-increment:list-1}.ql-editor ol li.ql-indent-1:before{content:counter(list-1,lower-alpha) \\\". \\\"}.ql-editor ol li.ql-indent-1{counter-reset:list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-2{counter-increment:list-2}.ql-editor ol li.ql-indent-2:before{content:counter(list-2,lower-roman) \\\". \\\"}.ql-editor ol li.ql-indent-2{counter-reset:list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-3{counter-increment:list-3}.ql-editor ol li.ql-indent-3:before{content:counter(list-3,decimal) \\\". \\\"}.ql-editor ol li.ql-indent-3{counter-reset:list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-4{counter-increment:list-4}.ql-editor ol li.ql-indent-4:before{content:counter(list-4,lower-alpha) \\\". \\\"}.ql-editor ol li.ql-indent-4{counter-reset:list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-5{counter-increment:list-5}.ql-editor ol li.ql-indent-5:before{content:counter(list-5,lower-roman) \\\". \\\"}.ql-editor ol li.ql-indent-5{counter-reset:list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-6{counter-increment:list-6}.ql-editor ol li.ql-indent-6:before{content:counter(list-6,decimal) \\\". \\\"}.ql-editor ol li.ql-indent-6{counter-reset:list-7 list-8 list-9}.ql-editor ol li.ql-indent-7{counter-increment:list-7}.ql-editor ol li.ql-indent-7:before{content:counter(list-7,lower-alpha) \\\". \\\"}.ql-editor ol li.ql-indent-7{counter-reset:list-8 list-9}.ql-editor ol li.ql-indent-8{counter-increment:list-8}.ql-editor ol li.ql-indent-8:before{content:counter(list-8,lower-roman) \\\". \\\"}.ql-editor ol li.ql-indent-8{counter-reset:list-9}.ql-editor ol li.ql-indent-9{counter-increment:list-9}.ql-editor ol li.ql-indent-9:before{content:counter(list-9,decimal) \\\". \\\"}.ql-editor .ql-indent-1:not(.ql-direction-rtl){padding-left:3em}.ql-editor li.ql-indent-1:not(.ql-direction-rtl){padding-left:4.5em}.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:3em}.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:4.5em}.ql-editor .ql-indent-2:not(.ql-direction-rtl){padding-left:6em}.ql-editor li.ql-indent-2:not(.ql-direction-rtl){padding-left:7.5em}.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:6em}.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:7.5em}.ql-editor .ql-indent-3:not(.ql-direction-rtl){padding-left:9em}.ql-editor li.ql-indent-3:not(.ql-direction-rtl){padding-left:10.5em}.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:9em}.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:10.5em}.ql-editor .ql-indent-4:not(.ql-direction-rtl){padding-left:12em}.ql-editor li.ql-indent-4:not(.ql-direction-rtl){padding-left:13.5em}.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:12em}.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:13.5em}.ql-editor .ql-indent-5:not(.ql-direction-rtl){padding-left:15em}.ql-editor li.ql-indent-5:not(.ql-direction-rtl){padding-left:16.5em}.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:15em}.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:16.5em}.ql-editor .ql-indent-6:not(.ql-direction-rtl){padding-left:18em}.ql-editor li.ql-indent-6:not(.ql-direction-rtl){padding-left:19.5em}.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:18em}.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:19.5em}.ql-editor .ql-indent-7:not(.ql-direction-rtl){padding-left:21em}.ql-editor li.ql-indent-7:not(.ql-direction-rtl){padding-left:22.5em}.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:21em}.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:22.5em}.ql-editor .ql-indent-8:not(.ql-direction-rtl){padding-left:24em}.ql-editor li.ql-indent-8:not(.ql-direction-rtl){padding-left:25.5em}.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:24em}.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:25.5em}.ql-editor .ql-indent-9:not(.ql-direction-rtl){padding-left:27em}.ql-editor li.ql-indent-9:not(.ql-direction-rtl){padding-left:28.5em}.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:27em}.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:28.5em}.ql-editor .ql-video{display:block;max-width:100%}.ql-editor .ql-video.ql-align-center{margin:0 auto}.ql-editor .ql-video.ql-align-right{margin:0 0 0 auto}.ql-editor .ql-bg-black{background-color:#000}.ql-editor .ql-bg-red{background-color:#e60000}.ql-editor .ql-bg-orange{background-color:#f90}.ql-editor .ql-bg-yellow{background-color:#ff0}.ql-editor .ql-bg-green{background-color:#008a00}.ql-editor .ql-bg-blue{background-color:#06c}.ql-editor .ql-bg-purple{background-color:#93f}.ql-editor .ql-color-white{color:#fff}.ql-editor .ql-color-red{color:#e60000}.ql-editor .ql-color-orange{color:#f90}.ql-editor .ql-color-yellow{color:#ff0}.ql-editor .ql-color-green{color:#008a00}.ql-editor .ql-color-blue{color:#06c}.ql-editor .ql-color-purple{color:#93f}.ql-editor .ql-font-serif{font-family:Georgia,Times New Roman,serif}.ql-editor .ql-font-monospace{font-family:Monaco,Courier New,monospace}.ql-editor .ql-size-small{font-size:.75em}.ql-editor .ql-size-large{font-size:1.5em}.ql-editor .ql-size-huge{font-size:2.5em}.ql-editor .ql-direction-rtl{direction:rtl;text-align:inherit}.ql-editor .ql-align-center{text-align:center}.ql-editor .ql-align-justify{text-align:justify}.ql-editor .ql-align-right{text-align:right}.ql-editor.ql-blank:before{color:rgba(0,0,0,.6);content:attr(data-placeholder);font-style:italic;left:15px;pointer-events:none;position:absolute;right:15px}.ql-snow.ql-toolbar:after,.ql-snow .ql-toolbar:after{clear:both;content:\\\"\\\";display:table}.ql-snow.ql-toolbar button,.ql-snow .ql-toolbar button{background:none;border:none;cursor:pointer;display:inline-block;float:left;height:24px;padding:3px 5px;width:28px}.ql-snow.ql-toolbar button svg,.ql-snow .ql-toolbar button svg{float:left;height:100%}.ql-snow.ql-toolbar button:active:hover,.ql-snow .ql-toolbar button:active:hover{outline:none}.ql-snow.ql-toolbar input.ql-image[type=file],.ql-snow .ql-toolbar input.ql-image[type=file]{display:none}.ql-snow.ql-toolbar .ql-picker-item.ql-selected,.ql-snow .ql-toolbar .ql-picker-item.ql-selected,.ql-snow.ql-toolbar .ql-picker-item:hover,.ql-snow .ql-toolbar .ql-picker-item:hover,.ql-snow.ql-toolbar .ql-picker-label.ql-active,.ql-snow .ql-toolbar .ql-picker-label.ql-active,.ql-snow.ql-toolbar .ql-picker-label:hover,.ql-snow .ql-toolbar .ql-picker-label:hover,.ql-snow.ql-toolbar button.ql-active,.ql-snow .ql-toolbar button.ql-active,.ql-snow.ql-toolbar button:focus,.ql-snow .ql-toolbar button:focus,.ql-snow.ql-toolbar button:hover,.ql-snow .ql-toolbar button:hover{color:#06c}.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill,.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,.ql-snow.ql-toolbar button.ql-active .ql-fill,.ql-snow .ql-toolbar button.ql-active .ql-fill,.ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill,.ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill,.ql-snow.ql-toolbar button:focus .ql-fill,.ql-snow .ql-toolbar button:focus .ql-fill,.ql-snow.ql-toolbar button:focus .ql-stroke.ql-fill,.ql-snow .ql-toolbar button:focus .ql-stroke.ql-fill,.ql-snow.ql-toolbar button:hover .ql-fill,.ql-snow .ql-toolbar button:hover .ql-fill,.ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill,.ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill{fill:#06c}.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,.ql-snow.ql-toolbar button.ql-active .ql-stroke,.ql-snow .ql-toolbar button.ql-active .ql-stroke,.ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,.ql-snow .ql-toolbar button.ql-active .ql-stroke-miter,.ql-snow.ql-toolbar button:focus .ql-stroke,.ql-snow .ql-toolbar button:focus .ql-stroke,.ql-snow.ql-toolbar button:focus .ql-stroke-miter,.ql-snow .ql-toolbar button:focus .ql-stroke-miter,.ql-snow.ql-toolbar button:hover .ql-stroke,.ql-snow .ql-toolbar button:hover .ql-stroke,.ql-snow.ql-toolbar button:hover .ql-stroke-miter,.ql-snow .ql-toolbar button:hover .ql-stroke-miter{stroke:#06c}@media(pointer:coarse){.ql-snow.ql-toolbar button:hover:not(.ql-active),.ql-snow .ql-toolbar button:hover:not(.ql-active){color:#444}.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-fill,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-fill,.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill{fill:#444}.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke,.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter{stroke:#444}}.ql-snow,.ql-snow *{box-sizing:border-box}.ql-snow .ql-hidden{display:none}.ql-snow .ql-out-bottom,.ql-snow .ql-out-top{visibility:hidden}.ql-snow .ql-tooltip{position:absolute;transform:translateY(10px)}.ql-snow .ql-tooltip a{cursor:pointer;text-decoration:none}.ql-snow .ql-tooltip.ql-flip{transform:translateY(-10px)}.ql-snow .ql-formats{display:inline-block;vertical-align:middle}.ql-snow .ql-formats:after{clear:both;content:\\\"\\\";display:table}.ql-snow .ql-stroke{fill:none;stroke:#444;stroke-linecap:round;stroke-linejoin:round;stroke-width:2}.ql-snow .ql-stroke-miter{fill:none;stroke:#444;stroke-miterlimit:10;stroke-width:2}.ql-snow .ql-fill,.ql-snow .ql-stroke.ql-fill{fill:#444}.ql-snow .ql-empty{fill:none}.ql-snow .ql-even{fill-rule:evenodd}.ql-snow .ql-stroke.ql-thin,.ql-snow .ql-thin{stroke-width:1}.ql-snow .ql-transparent{opacity:.4}.ql-snow .ql-direction svg:last-child{display:none}.ql-snow .ql-direction.ql-active svg:last-child{display:inline}.ql-snow .ql-direction.ql-active svg:first-child{display:none}.ql-snow .ql-editor h1{font-size:2em}.ql-snow .ql-editor h2{font-size:1.5em}.ql-snow .ql-editor h3{font-size:1.17em}.ql-snow .ql-editor h4{font-size:1em}.ql-snow .ql-editor h5{font-size:.83em}.ql-snow .ql-editor h6{font-size:.67em}.ql-snow .ql-editor a{text-decoration:underline}.ql-snow .ql-editor blockquote{border-left:4px solid #ccc;margin-bottom:5px;margin-top:5px;padding-left:16px}.ql-snow .ql-editor code,.ql-snow .ql-editor pre{background-color:#f0f0f0;border-radius:3px}.ql-snow .ql-editor pre{white-space:pre-wrap;margin-bottom:5px;margin-top:5px;padding:5px 10px}.ql-snow .ql-editor code{font-size:85%;padding:2px 4px}.ql-snow .ql-editor pre.ql-syntax{background-color:#23241f;color:#f8f8f2;overflow:visible}.ql-snow .ql-editor img{max-width:100%}.ql-snow .ql-picker{color:#444;display:inline-block;float:left;font-size:14px;font-weight:500;height:24px;position:relative;vertical-align:middle}.ql-snow .ql-picker-label{cursor:pointer;display:inline-block;height:100%;padding-left:8px;padding-right:2px;position:relative;width:100%}.ql-snow .ql-picker-label:before{display:inline-block;line-height:22px}.ql-snow .ql-picker-options{background-color:#fff;display:none;min-width:100%;padding:4px 8px;position:absolute;white-space:nowrap}.ql-snow .ql-picker-options .ql-picker-item{cursor:pointer;display:block;padding-bottom:5px;padding-top:5px}.ql-snow .ql-picker.ql-expanded .ql-picker-label{color:#ccc;z-index:2}.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill{fill:#ccc}.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke{stroke:#ccc}.ql-snow .ql-picker.ql-expanded .ql-picker-options{display:block;margin-top:-1px;top:100%;z-index:1}.ql-snow .ql-color-picker,.ql-snow .ql-icon-picker{width:28px}.ql-snow .ql-color-picker .ql-picker-label,.ql-snow .ql-icon-picker .ql-picker-label{padding:2px 4px}.ql-snow .ql-color-picker .ql-picker-label svg,.ql-snow .ql-icon-picker .ql-picker-label svg{right:4px}.ql-snow .ql-icon-picker .ql-picker-options{padding:4px 0}.ql-snow .ql-icon-picker .ql-picker-item{height:24px;width:24px;padding:2px 4px}.ql-snow .ql-color-picker .ql-picker-options{padding:3px 5px;width:152px}.ql-snow .ql-color-picker .ql-picker-item{border:1px solid transparent;float:left;height:16px;margin:2px;padding:0;width:16px}.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg{position:absolute;margin-top:-9px;right:0;top:50%;width:18px}.ql-snow .ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=\\\"\\\"]):before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=\\\"\\\"]):before,.ql-snow .ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=\\\"\\\"]):before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=\\\"\\\"]):before,.ql-snow .ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=\\\"\\\"]):before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=\\\"\\\"]):before{content:attr(data-label)}.ql-snow .ql-picker.ql-header{width:98px}.ql-snow .ql-picker.ql-header .ql-picker-item:before,.ql-snow .ql-picker.ql-header .ql-picker-label:before{content:\\\"Normal\\\"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"1\\\"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\\\"1\\\"]:before{content:\\\"Heading 1\\\"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"2\\\"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\\\"2\\\"]:before{content:\\\"Heading 2\\\"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"3\\\"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\\\"3\\\"]:before{content:\\\"Heading 3\\\"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"4\\\"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\\\"4\\\"]:before{content:\\\"Heading 4\\\"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"5\\\"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\\\"5\\\"]:before{content:\\\"Heading 5\\\"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"6\\\"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\\\"6\\\"]:before{content:\\\"Heading 6\\\"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"1\\\"]:before{font-size:2em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"2\\\"]:before{font-size:1.5em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"3\\\"]:before{font-size:1.17em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"4\\\"]:before{font-size:1em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"5\\\"]:before{font-size:.83em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\\\"6\\\"]:before{font-size:.67em}.ql-snow .ql-picker.ql-font{width:108px}.ql-snow .ql-picker.ql-font .ql-picker-item:before,.ql-snow .ql-picker.ql-font .ql-picker-label:before{content:\\\"Sans Serif\\\"}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]:before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=serif]:before{content:\\\"Serif\\\"}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=monospace]:before{content:\\\"Monospace\\\"}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]:before{font-family:Georgia,Times New Roman,serif}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before{font-family:Monaco,Courier New,monospace}.ql-snow .ql-picker.ql-size{width:98px}.ql-snow .ql-picker.ql-size .ql-picker-item:before,.ql-snow .ql-picker.ql-size .ql-picker-label:before{content:\\\"Normal\\\"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=small]:before{content:\\\"Small\\\"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=large]:before{content:\\\"Large\\\"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=huge]:before{content:\\\"Huge\\\"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]:before{font-size:10px}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]:before{font-size:18px}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]:before{font-size:32px}.ql-snow .ql-color-picker.ql-background .ql-picker-item{background-color:#fff}.ql-snow .ql-color-picker.ql-color .ql-picker-item{background-color:#000}.ql-toolbar.ql-snow{border:1px solid #ccc;box-sizing:border-box;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;padding:8px}.ql-toolbar.ql-snow .ql-formats{margin-right:15px}.ql-toolbar.ql-snow .ql-picker-label{border:1px solid transparent}.ql-toolbar.ql-snow .ql-picker-options{border:1px solid transparent;box-shadow:0 2px 8px rgba(0,0,0,.2)}.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label,.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options{border-color:#ccc}.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item.ql-selected,.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item:hover{border-color:#000}.ql-toolbar.ql-snow+.ql-container.ql-snow{border-top:0}.ql-snow .ql-tooltip{background-color:#fff;border:1px solid #ccc;box-shadow:0 0 5px #ddd;color:#444;padding:5px 12px;white-space:nowrap}.ql-snow .ql-tooltip:before{content:\\\"Visit URL:\\\";line-height:26px;margin-right:8px}.ql-snow .ql-tooltip input[type=text]{display:none;border:1px solid #ccc;font-size:13px;height:26px;margin:0;padding:3px 5px;width:170px}.ql-snow .ql-tooltip a.ql-preview{display:inline-block;max-width:200px;overflow-x:hidden;text-overflow:ellipsis;vertical-align:top}.ql-snow .ql-tooltip a.ql-action:after{border-right:1px solid #ccc;content:\\\"Edit\\\";margin-left:16px;padding-right:8px}.ql-snow .ql-tooltip a.ql-remove:before{content:\\\"Remove\\\";margin-left:8px}.ql-snow .ql-tooltip a{line-height:26px}.ql-snow .ql-tooltip.ql-editing a.ql-preview,.ql-snow .ql-tooltip.ql-editing a.ql-remove{display:none}.ql-snow .ql-tooltip.ql-editing input[type=text]{display:inline-block}.ql-snow .ql-tooltip.ql-editing a.ql-action:after{border-right:0;content:\\\"Save\\\";padding-right:0}.ql-snow .ql-tooltip[data-mode=link]:before{content:\\\"Enter link:\\\"}.ql-snow .ql-tooltip[data-mode=formula]:before{content:\\\"Enter formula:\\\"}.ql-snow .ql-tooltip[data-mode=video]:before{content:\\\"Enter video:\\\"}.ql-snow a{color:#06c}.ql-container.ql-snow{border:1px solid #ccc}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./CommentTextarea.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.comment-textarea{position:relative}.comment-textarea .ql-toolbar{display:none}.comment-textarea .ql-container{border:none}.comment-textarea .ql-container .ql-editor{width:100%;padding:4px 6px;border:1px solid var(--border);border-radius:4px;color:var(--text-color);resize:none;overflow:hidden;box-sizing:border-box}.comment-textarea .ql-container .ql-editor:focus{outline:none;border:1px solid var(--focus-border)}.comment-textarea .ql-container .ql-editor.ql-blank:before{left:8px;list-style-type:none;font-style:normal;color:var(--placeholder-text)}.comment-textarea .ql-container .ql-editor p{margin:0;word-break:break-word}.comment-textarea .ql-container .ql-editor ul>li:before{content:none!important}.comment-textarea .ql-container.ql-snow{border:none}.comment-textarea .ql-container.ql-snow:focus{outline:var(--focus-visible-outline);border-radius:4px}.comment-textarea .add-attachment{position:absolute;bottom:2px;right:2px;width:24px;height:24px}.comment-textarea .add-attachment:hover{background-color:var(--blue-1)}.comment-textarea .add-attachment .Icon{padding:3px}.ql-editor ul>li:before{content:none!important}.mention__element{width:170px;z-index:9001!important;max-height:200px;overflow-y:auto;overflow-y:overlay;overflow-x:hidden;background-color:var(--component-background);border:1px solid var(--border);border-radius:4px}.mention__suggestions__list{width:100%;font-size:14px;margin-top:0;padding-left:0!important;list-style:none;word-wrap:break-word;border-radius:4px}.mention__suggestions__item{background-color:var(--component-background);white-space:nowrap;padding-left:0;text-overflow:clip;padding:7px 5px 1px!important;margin:0;width:100%;cursor:pointer}.mention__suggestions__item .email{margin-top:2px;font-size:12px;white-space:normal;color:var(--faded-text)}.mention__suggestions__item.selected{background-color:var(--view-header-button-active)!important}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./NotePopup.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.NotePopup .options.modular-ui .option:hover{cursor:pointer;background:var(--primary-button-hover);color:var(--gray-0)}.NotePopup{flex-grow:0;display:flex;justify-content:flex-end;-webkit-user-select:none;-moz-user-select:none;user-select:none;width:28px;height:28px;position:relative}.NotePopup .Button.overflow{width:28px;height:28px;border-radius:4px;display:flex;justify-content:center;align-items:center}.NotePopup .Button.overflow .Icon{width:24px;height:24px}.NotePopup .Button.overflow:hover{background:var(--blue-1)}.NotePopup .Button.overflow.active{background:var(--popup-button-active)}.NotePopup .options{display:flex;flex-direction:column;box-shadow:0 0 3px 0 var(--box-shadow);z-index:80;position:absolute;border-radius:4px;background:var(--component-background);top:40px;width:-moz-max-content;width:max-content}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.NotePopup .options{right:0}}.NotePopup .options .note-popup-option{padding:0;border:none;background-color:transparent;align-items:flex-start}:host(:not([data-tabbing=true])) .NotePopup .options .note-popup-option,html:not([data-tabbing=true]) .NotePopup .options .note-popup-option{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NotePopup .options .note-popup-option{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NotePopup .options .note-popup-option{font-size:13px}}.NotePopup .options.options-reply{top:30px}.NotePopup .options .option{display:flex;flex-direction:column;justify-content:center;height:28px;padding-left:8px;padding-right:17px;border-radius:0}.NotePopup .options .option:hover{background-color:var(--popup-button-hover)}.NotePopup .options .option:first-child{border-top-right-radius:4px;border-top-left-radius:4px}.NotePopup .options .option:last-child{border-bottom-right-radius:4px}.NotePopup .Button{height:28px}.NotePopup .Button.active{background:var(--popup-button-active)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NotePopup .Button.note-popup-toggle-trigger{width:28px;height:28px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NotePopup .Button.note-popup-toggle-trigger{width:28px;height:28px}}.NotePopupFlyout{min-width:unset!important;max-width:unset!important}.NotePopupFlyout .flyout-item-container{height:unset!important;margin:unset!important}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./NoteHeader.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.NoteHeader{padding-right:12px;position:relative;flex:1;color:var(--text-color);display:flex}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.NoteHeader{flex:none}}.NoteHeader .type-icon{margin:2px;width:24px;height:24px}.NoteHeader .type-icon-container{padding-right:13px}.NoteHeader .type-icon-container .unread-notification{position:absolute;width:13px;height:13px;right:-2px;top:-2px;border-radius:10000px;border:2px solid var(--component-background);background:#00a5e4}.NoteHeader .author-and-date{flex:1;min-width:0;position:relative}.NoteHeader .author-and-date.isReply{padding-left:0;padding-top:0;font-size:10px}.NoteHeader .author-and-date .author-and-overflow{display:flex;justify-content:space-between}.NoteHeader .author-and-date .author-and-overflow .author-and-time{display:flex;flex-direction:column;word-break:break-word}.NoteHeader .author-and-date .author-and-overflow .author-and-time .author{font-weight:700;font-size:13px}.NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies{display:flex}.NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .date-and-time{font-size:10px;color:var(--faded-text)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .date-and-time{font-size:10px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .date-and-time{font-size:10px}}.NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .num-replies-container{display:flex;flex-grow:1;padding-left:10px}.NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .num-replies-container .num-reply-icon{height:12px;width:12px}.NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .num-replies-container .num-replies{font-size:10px;color:var(--gray-7)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .num-replies-container .num-replies{font-size:12px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NoteHeader .author-and-date .author-and-overflow .author-and-time .date-and-num-replies .num-replies-container .num-replies{font-size:12px}}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow{display:flex;flex:1;justify-content:flex-end}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow>*{pointer-events:auto}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow .tracked-change-icon-wrapper .tracked-change-icon{margin:2px;width:24px;height:24px}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow .tracked-change-icon-wrapper:hover.accept{background-color:#d5f5ca}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow .tracked-change-icon-wrapper:hover.reject{background-color:#ffe8e8}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow .ToggleElementButton button{width:28px;height:28px;margin:0 8px 0 13px}.NoteHeader .author-and-date .author-and-overflow .state-and-overflow .ToggleElementButton button .Icon{width:20px;height:20px}.NoteHeader .author-name{font-weight:700}.NoteHeader .note-popup-toggle-trigger{padding:0;margin-right:0!important;margin-left:0!important;min-width:28px!important}.NoteHeader .note-popup-toggle-trigger .Icon{width:24px!important;height:24px!important}.parent{padding-left:12px;padding-top:12px}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./ReplyAttachmentList.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".reply-attachment-list{display:flex;flex-direction:column;cursor:default}.reply-attachment-list .reply-attachment{background-color:var(--gray-1);border-radius:4px;cursor:pointer;pointer-events:auto}.reply-attachment-list .reply-attachment:not(:last-child){margin-bottom:8px}.reply-attachment-list .reply-attachment .reply-attachment-preview{width:100%;max-height:80px;display:flex;justify-content:center}.reply-attachment-list .reply-attachment .reply-attachment-preview.dirty{position:relative;margin-bottom:15px}.reply-attachment-list .reply-attachment .reply-attachment-preview img{max-width:100%;max-height:100%;-o-object-fit:contain;object-fit:contain}.reply-attachment-list .reply-attachment .reply-attachment-preview .reply-attachment-preview-message{font-size:11px;color:var(--error-text-color);position:absolute;bottom:-15px;left:10px}.reply-attachment-list .reply-attachment .reply-attachment-info{display:flex;align-items:center;height:40px;padding:8px}.reply-attachment-list .reply-attachment .reply-attachment-info .reply-attachment-icon{height:24px;min-height:24px;width:24px;min-width:24px}.reply-attachment-list .reply-attachment .reply-attachment-info .reply-file-name{height:16px;width:100%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-left:8px;margin-right:8px}.reply-attachment-list .reply-attachment .reply-attachment-info .attachment-button{height:24px;min-height:24px;width:24px;min-width:24px}.reply-attachment-list .reply-attachment .reply-attachment-info .attachment-button:hover{background-color:var(--blue-1)}.reply-attachment-list .reply-attachment .reply-attachment-info .attachment-button .Icon{height:16px;width:16px}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./NoteContent.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.NoteContent.modular-ui .edit-content .edit-buttons .save-button.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.NoteContent.modular-ui .edit-content .edit-buttons .save-button.disabled span{color:var(--primary-button-text)}.NoteContent{--note-content-right-padding-width:12px;position:relative;display:flex;flex-direction:column;align-items:left;flex:1;color:var(--text-color);padding-bottom:12px;pointer-events:none}.NoteContent.isReply{padding-bottom:0}.NoteContent.unread.isReply{background:rgba(0,165,228,.08)}.NoteContent.unread.clicked .author-and-time span{font-weight:400}.NoteContent.unread .author-and-time span{font-weight:700}.NoteContent .container{margin-top:8px;overflow:hidden;white-space:pre-wrap;word-wrap:break-word;-webkit-user-select:text;-moz-user-select:text;user-select:text;cursor:text;padding-left:52px;padding-right:var(--note-content-right-padding-width)}.NoteContent .container>*{pointer-events:all}.NoteContent .container-reply{margin-top:8px;overflow:hidden;white-space:pre-wrap;word-wrap:break-word;-webkit-user-select:text;-moz-user-select:text;user-select:text;cursor:text}.NoteContent .edit-content{margin-top:7px;display:flex;flex-direction:column;position:relative;flex:1;padding-left:52px;padding-right:12px;padding-bottom:12px;pointer-events:auto}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.NoteContent .edit-content{flex:none}}.NoteContent .edit-content textarea{width:100%;padding-left:8px;border:1px solid var(--border);border-radius:4px;color:var(--text-color);padding-top:4px;padding-bottom:4px;resize:none;overflow:hidden;box-sizing:border-box}.NoteContent .edit-content textarea:focus{outline:none;border:1px solid var(--focus-border)}.NoteContent .edit-content textarea::-moz-placeholder{color:var(--placeholder-text)}.NoteContent .edit-content textarea::placeholder{color:var(--placeholder-text)}.NoteContent .edit-content .edit-buttons{display:flex;flex-direction:row;justify-content:flex-end;margin-top:8px}.NoteContent .edit-content .edit-buttons>div{margin-right:4px}.NoteContent .edit-content .edit-buttons .save-button{background-color:transparent;cursor:pointer;flex-shrink:0;background:var(--primary-button);border-radius:4px;width:-moz-fit-content;width:fit-content;border:none;height:28px;display:flex;align-items:center;justify-content:center;position:relative;color:var(--primary-button-text);padding:0 13px}:host(:not([data-tabbing=true])) .NoteContent .edit-content .edit-buttons .save-button,html:not([data-tabbing=true]) .NoteContent .edit-content .edit-buttons .save-button{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NoteContent .edit-content .edit-buttons .save-button{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NoteContent .edit-content .edit-buttons .save-button{font-size:13px}}.NoteContent .edit-content .edit-buttons .save-button:hover{background:var(--primary-button-hover);color:var(--primary-button-text)}.NoteContent .edit-content .edit-buttons .save-button.disabled{background:var(--gray-6)!important;border-color:var(--gray-6)!important;cursor:not-allowed}.NoteContent .edit-content .edit-buttons .save-button.disabled span{color:var(--primary-button-text)}.NoteContent .edit-content .edit-buttons .cancel-button{border:none;background-color:transparent;color:var(--secondary-button-text);padding:0 10px;width:-moz-fit-content;width:fit-content;height:28px;display:flex;align-items:center;justify-content:center;cursor:pointer;margin-right:2px}:host(:not([data-tabbing=true])) .NoteContent .edit-content .edit-buttons .cancel-button,html:not([data-tabbing=true]) .NoteContent .edit-content .edit-buttons .cancel-button{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .NoteContent .edit-content .edit-buttons .cancel-button{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .NoteContent .edit-content .edit-buttons .cancel-button{font-size:13px}}.NoteContent .edit-content .edit-buttons .cancel-button:hover{color:var(--secondary-button-hover)}.NoteContent .reply-content{padding-left:0}.NoteContent .contents{white-space:pre-wrap;color:var(--text-color);margin-right:5px;padding:0;word-break:normal;word-wrap:break-word;-webkit-user-select:text;-moz-user-select:text;user-select:text}.NoteContent .contents .highlight{background:#fffc95;color:#333}.NoteContent .highlight{background:#fffc95}.NoteContent .selected-text-preview{padding-left:52px;padding-top:8px}.NoteContent .reply-attachment-list{margin-bottom:8px}.NoteContent.modular-ui .highlight{font-weight:700;color:var(--blue-5);background:none}.NoteContent.modular-ui .edit-content .edit-buttons .save-button.disabled{border:none}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./ReplyArea.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.reply-area-container{border-top:1px solid var(--divider);display:flex;flex-direction:column;margin-bottom:0}.reply-area-container .reply-attachment-list{margin:12px 12px 0}.reply-area-container .reply-area-with-button{display:flex}.reply-area-container .reply-area-with-button .reply-area{position:relative;flex:1;margin:12px 17px 12px 12px;border-radius:4px;align-items:center}.reply-area-container .reply-area-with-button .reply-area.unread{background:rgba(0,165,228,.08)}.reply-area-container .reply-area-with-button .reply-area .comment-textarea .ql-container .ql-editor.ql-blank{padding:4px}.reply-area-container .reply-area-with-button .reply-area .comment-textarea .ql-container .ql-editor.ql-blank:before{left:4px}.reply-area-container .reply-area-with-button .reply-button-container{display:flex;flex-direction:column;justify-content:flex-end}.reply-area-container .reply-area-with-button .reply-button-container .reply-button{width:28px;height:28px;padding:0;border:none;background-color:transparent;right:10px;bottom:12px}:host(:not([data-tabbing=true])) .reply-area-container .reply-area-with-button .reply-button-container .reply-button,html:not([data-tabbing=true]) .reply-area-container .reply-area-with-button .reply-button-container .reply-button{outline:none}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.reply-area-container .reply-area-with-button .reply-button-container .reply-button{width:80px}}.reply-area-container .reply-area-with-button .reply-button-container .reply-button:hover{background:var(--blue-1)}.reply-area-container .reply-area-with-button .reply-button-container .reply-button.disabled{cursor:not-allowed}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./AnnotationNoteConnectorLine.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}@media print{#line-connector-root{opacity:0}}#line-connector-root{position:relative;z-index:69}.horizontalLine{height:2px}.horizontalLine,.verticalLine{background-color:rgba(30,120,235,.5);position:fixed}.verticalLine{width:2px}.arrowHead{position:absolute;top:0;left:0;margin:auto;width:0;height:0;border-top:6px solid transparent;border-bottom:6px solid transparent;border-right:7px solid rgba(30,120,235,.5);transform:translateX(-100%) translateY(-50%) translateY(1px)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./Note.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.Note{padding:0;border:none;background-color:transparent;display:block;text-align:left;border-radius:4px;box-shadow:0 0 3px 0 var(--note-box-shadow);margin-bottom:8px;margin-left:2px;background:var(--component-background);cursor:pointer;position:relative}:host(:not([data-tabbing=true])) .Note,html:not([data-tabbing=true]) .Note{outline:none}.Note.unread{border:1.2px solid #00a5e4}.Note.expanded{box-shadow:0 4px 16px var(--note-box-shadow-expanded),0 0 4px 0 var(--note-box-shadow)}.Note.is-multi-selected{box-shadow:0 4px 16px rgba(134,142,150,.24),0 0 4px 0 var(--note-box-shadow)}.Note.disabled{opacity:.5;pointer-events:none}.Note .note-button{position:absolute;width:100%;height:100%;top:0;left:0}.Note .mark-all-read-button{background:#00a5e4;text-align:center;color:#fff;height:16px;font-size:12px;width:100%;border-radius:0}.Note .divider{height:1px;width:100%;background:var(--divider)}.Note .reply-divider{background:var(--reply-divider);height:1px;width:100%}.Note .replies{margin-left:52px;padding-bottom:12px}.Note .reply{padding-left:12px;padding-bottom:24px;border-left:1px solid var(--reply-divider)}.Note .reply:last-of-type{padding-bottom:0}.Note .group-section{margin-left:52px;padding-bottom:12px;display:flex;flex-direction:column;grid-row-gap:4px;row-gap:4px;padding-right:12px}.Note .group-section.modular-ui .group-child:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);background:var(--faded-component-background);border-radius:4px}.Note .text-button{color:var(--secondary-button-text);display:flex;position:relative;width:auto;height:auto;flex-direction:row-reverse;justify-content:flex-end}.Note .text-button .Icon{color:var(--secondary-button-text);height:18px;width:18px}.Note .group-child{position:relative;width:auto;height:auto;display:block;text-align:left;padding-top:4px;padding-bottom:4px}.Note .group-child:hover{background:var(--view-header-button-hover)}.Note .group-child .NoteContent{padding-bottom:0}.Note:focus{outline:none}.Note.focus-visible,.Note:focus-visible{outline:var(--focus-visible-outline)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React from 'react';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\n\nconst ReplyAttachmentPicker = ({ annotationId, addAttachments }) => {\n  const replyAttachmentHandler = useSelector((state) => selectors.getReplyAttachmentHandler(state));\n\n  const onChange = async (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      let attachment = file;\n      if (replyAttachmentHandler) {\n        const url = await replyAttachmentHandler(file);\n        attachment = {\n          url,\n          name: file.name,\n          size: file.size,\n          type: file.type\n        };\n      }\n      addAttachments(annotationId, [attachment]);\n    }\n  };\n\n  return (\n    <input\n      id=\"reply-attachment-picker\"\n      type=\"file\"\n      style={{ display: 'none' }}\n      onChange={onChange}\n      onClick={(e) => {\n        e.target.value = '';\n      }}\n    />\n  );\n};\n\nexport default ReplyAttachmentPicker;\n", "import React, { useLayoutEffect } from 'react';\nimport PropTypes from 'prop-types';\nimport { useTranslation } from 'react-i18next';\nimport classNames from 'classnames';\nimport selectors from 'selectors';\nimport DataElements from 'src/constants/dataElement';\nimport actions from 'actions';\nimport './NotePopup.scss';\nimport { useDispatch, useSelector } from 'react-redux';\nimport ToggleElementButton from 'components/ModularComponents/ToggleElementButton';\n\nconst createFlyoutItem = (option, icon, dataElement) => ({\n  icon,\n  label: `action.${option.toLowerCase()}`,\n  title: `action.${option.toLowerCase()}`,\n  option,\n  dataElement,\n});\n\nexport const notePopupFlyoutItems = [\n  createFlyoutItem('Edit', '', 'notePopupEdit'),\n  createFlyoutItem('Delete', '', 'notePopupDelete'),\n];\n\nconst propTypes = {\n  handleEdit: PropTypes.func,\n  handleDelete: PropTypes.func,\n  isEditable: PropTypes.bool,\n  isDeletable: PropTypes.bool,\n  noteId: PropTypes.string,\n};\n\nfunction noop() { }\n\nfunction NotePopup(props) {\n  const {\n    handleEdit = noop,\n    handleDelete = noop,\n    isEditable,\n    isDeletable,\n    isReply,\n    noteId,\n  } = props;\n\n  const customizableUI = useSelector((state) => selectors.getFeatureFlags(state)?.customizableUI);\n  const flyoutSelector = `${DataElements.NOTE_POPUP_FLYOUT}-${noteId}`;\n  const [t] = useTranslation();\n\n  const handleClick = (selection) => {\n    if (selection === 'Edit') {\n      handleEdit();\n    } else if (selection === 'Delete') {\n      handleDelete();\n    }\n  };\n\n  if (!isEditable && !isDeletable) {\n    return null;\n  }\n\n  const notePopupButtonClass = classNames('overflow note-popup-toggle-trigger');\n  const optionsClass = classNames('NotePopup options note-popup-options', { 'options-reply': isReply, 'modular-ui': customizableUI });\n  return (\n    <div className={optionsClass}>\n      <ToggleElementButton\n        dataElement={`notePopup-${noteId}`}\n        className={notePopupButtonClass}\n        img=\"icon-tools-more\"\n        title={t('formField.formFieldPopup.options')}\n        toggleElement={flyoutSelector}\n        disabled={false}\n      />\n      <NotePopupFlyout\n        flyoutSelector={flyoutSelector}\n        handleClick={handleClick}\n        isEditable={isEditable}\n        isDeletable={isDeletable}\n      />\n    </div>\n  );\n}\n\n\nconst NotePopupFlyout = ({\n  flyoutSelector,\n  handleClick,\n  isEditable,\n  isDeletable,\n}) => {\n  const dispatch = useDispatch();\n  const currentFlyout = useSelector((state) => selectors.getFlyout(state, flyoutSelector));\n  const [t] = useTranslation();\n\n  useLayoutEffect(() => {\n    let items = notePopupFlyoutItems;\n    if (!isEditable) {\n      items = items.filter((item) => item.option !== 'Edit');\n    } else if (!isDeletable) {\n      items = items.filter((item) => item.option !== 'Delete');\n    }\n\n    const notePopupFlyout = {\n      dataElement: flyoutSelector,\n      className: 'NotePopupFlyout',\n      items: items.map((item) => {\n        return {\n          ...item,\n          label: t(item.label),\n          title: t(item.title),\n          onClick: () => handleClick(item.option),\n        };\n      }),\n    };\n\n    if (!currentFlyout) {\n      dispatch(actions.addFlyout(notePopupFlyout));\n    } else {\n      dispatch(actions.updateFlyout(notePopupFlyout.dataElement, notePopupFlyout));\n    }\n  }, [isEditable, isDeletable]);\n\n  return null;\n};\n\nNotePopupFlyout.propTypes = {\n  flyoutSelector: PropTypes.string,\n  handleClick: PropTypes.func,\n  isEditable: PropTypes.bool,\n  isDeletable: PropTypes.bool,\n};\n\nNotePopup.propTypes = propTypes;\n\nexport default NotePopup;\n", "import React from 'react';\nimport core from 'core';\nimport NotePopup from './NotePopup';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\n\nfunction NotePopupContainer(props) {\n  const [\n    activeDocumentViewerKey,\n  ] = useSelector((state) => [\n    selectors.getActiveDocumentViewerKey(state),\n  ]);\n  const { annotation, setIsEditing, noteIndex } = props;\n\n  const [canModify, setCanModify] = React.useState(core.canModify(annotation));\n  const [canModifyContents, setCanModifyContents] = React.useState(core.canModifyContents(annotation));\n\n  React.useEffect(() => {\n    function onUpdateAnnotationPermission() {\n      setCanModify(core.canModify(annotation, activeDocumentViewerKey));\n      setCanModifyContents(core.canModifyContents(annotation, activeDocumentViewerKey));\n    }\n\n    onUpdateAnnotationPermission();\n    core.addEventListener('updateAnnotationPermission', onUpdateAnnotationPermission, undefined, activeDocumentViewerKey);\n    return () => core.removeEventListener('updateAnnotationPermission', onUpdateAnnotationPermission, activeDocumentViewerKey);\n  }, [annotation, activeDocumentViewerKey]);\n\n  const handleEdit = React.useCallback(() => {\n    const isFreeText = annotation instanceof window.Core.Annotations.FreeTextAnnotation;\n    if (isFreeText && core.getAnnotationManager(activeDocumentViewerKey).isFreeTextEditingEnabled()) {\n      core.getAnnotationManager(activeDocumentViewerKey).trigger('annotationDoubleClicked', annotation);\n    } else {\n      setIsEditing(true, noteIndex);\n    }\n  }, [annotation, setIsEditing, noteIndex]);\n\n  const handleDelete = React.useCallback(() => {\n    core.deleteAnnotations([annotation, ...annotation.getGroupedChildren()], undefined, activeDocumentViewerKey);\n  }, [annotation]);\n\n  const isEditable = canModifyContents;\n  const isDeletable = canModify && !annotation?.NoDelete;\n  const noteId = (annotation) ? annotation.Id : '';\n\n  const passProps = {\n    handleEdit,\n    handleDelete,\n    isEditable,\n    isDeletable,\n    noteId,\n  };\n\n  return (\n    <NotePopup {...props} {...passProps} />\n  );\n}\n\nexport default NotePopupContainer;\n", "import NotePopupContainer from './NotePopupContainer';\n\nexport default NotePopupContainer;\n", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport { useTranslation } from 'react-i18next';\nimport NoteStateFlyout from 'components/ModularComponents/NoteStateFlyout';\nimport ToggleElementButton from 'components/ModularComponents/ToggleElementButton';\nimport DataElements from 'constants/dataElement';\n\nconst propTypes = {\n  annotation: PropTypes.object.isRequired,\n  handleStateChange: PropTypes.func\n};\n\nfunction NoteState(props) {\n  const {\n    annotation,\n    handleStateChange = () => { },\n  } = props;\n\n  const [t] = useTranslation();\n\n  const annotationState = annotation.getStatus();\n  const icon = `icon-annotation-status-${annotationState === '' ? 'none' : annotationState.toLowerCase()}`;\n  const id = annotation.Id;\n\n  return (\n    <>\n      <ToggleElementButton\n        dataElement={`noteState-${id}`}\n        title={t('option.notesOrder.status')}\n        img={icon}\n        toggleElement={`${DataElements.NOTE_STATE_FLYOUT}-${id}`}\n      />\n      <NoteStateFlyout\n        noteId={id}\n        handleStateChange={handleStateChange}\n      />\n    </>\n  );\n}\n\nNoteState.propTypes = propTypes;\n\nexport default NoteState;", "import React, { useCallback } from 'react';\nimport PropTypes from 'prop-types';\nimport core from 'core';\n\nimport NoteState from './NoteState';\nimport { createStateAnnotation } from 'helpers/NoteStateUtils';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport useFocusOnClose from 'hooks/useFocusOnClose';\n\nconst propTypes = {\n  annotation: PropTypes.object,\n};\n\nfunction NoteStateContainer(props) {\n  const activeDocumentViewerKey = useSelector((state) => selectors.getActiveDocumentViewerKey(state));\n  const isNoteStateDisabled = useSelector((state) => selectors.isElementDisabled(state, 'noteState'));\n\n  const { annotation } = props;\n\n  const handleStateChange = useFocusOnClose(useCallback(function handleStateChangeCallback(newValue) {\n    const stateAnnotation = createStateAnnotation(annotation, newValue, activeDocumentViewerKey);\n    annotation.addReply(stateAnnotation);\n    const annotationManager = core.getAnnotationManager(activeDocumentViewerKey);\n    annotationManager.addAnnotation(stateAnnotation);\n    annotationManager.trigger('addReply', [stateAnnotation, annotation, annotationManager.getRootAnnotation(annotation)]);\n  }, [annotation, activeDocumentViewerKey]));\n\n  return (!isNoteStateDisabled &&\n    <div>\n      <NoteState handleStateChange={handleStateChange} {...props} />\n    </div>\n  );\n}\n\nNoteStateContainer.propTypes = propTypes;\nexport default NoteStateContainer;\n", "import NoteStateContainer from './NoteStateContainer';\n\nexport default NoteStateContainer;\n", "import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport Tooltip from 'components/Tooltip';\nimport Icon from 'components/Icon';\nimport PropTypes from 'prop-types';\n\nconst propTypes = {\n  annotationId: PropTypes.string,\n  ariaLabel: PropTypes.string,\n  pendingEditTextMap: PropTypes.object,\n  pendingReplyMap: PropTypes.object,\n  pendingAttachmentMap: PropTypes.object,\n};\n\nconst NoteUnpostedCommentIndicator = ({ annotationId, ariaLabel, pendingEditTextMap, pendingReplyMap, pendingAttachmentMap }) => {\n  const { t } = useTranslation();\n  const [hasUnpostedComment, setHasUnpostedComment] = useState(false);\n  const [hasUnpostedReply, setHasUnpostedReply] = useState(false);\n  const [hasUnpostedAttachment, setHasUnpostedAttachment] = useState(false);\n\n  useEffect(() => {\n    setHasUnpostedComment(pendingEditTextMap[annotationId]?.length > 0);\n    setHasUnpostedReply(pendingReplyMap[annotationId]?.length > 0);\n    setHasUnpostedAttachment(pendingAttachmentMap[annotationId]?.length > 0);\n  }, [pendingEditTextMap, pendingReplyMap, pendingAttachmentMap]);\n\n  return (\n    (hasUnpostedComment || hasUnpostedReply || hasUnpostedAttachment) ?\n      <div data-element=\"unpostedCommentIndicator\">\n        <Tooltip content={t('message.unpostedComment')}>\n          <div>\n            <Icon className=\"type-icon\" glyph={'icon-unposted-comment'} ariaLabel={ariaLabel}/>\n          </div>\n        </Tooltip>\n      </div> :\n      null\n  );\n};\n\nNoteUnpostedCommentIndicator.propTypes = propTypes;\n\nexport default NoteUnpostedCommentIndicator;\n", "import React from 'react';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport NoteContext from 'components/Note/Context';\nimport NoteUnpostedCommentIndicator from './NoteUnpostedCommentIndicator';\nimport PropTypes from 'prop-types';\n\nconst propTypes = {\n  annotationId: PropTypes.string,\n  ariaLabel: PropTypes.string,\n};\n\nconst NoteUnpostedCommentIndicatorContainer = ({ annotationId, ariaLabel }) => {\n  const isDisabled = useSelector((state) => selectors.isElementDisabled(state, 'unpostedCommentIndicator'));\n  const { pendingEditTextMap, pendingReplyMap, pendingAttachmentMap } = React.useContext(NoteContext);\n\n  if (isDisabled) {\n    return null;\n  }\n  return (\n    <NoteUnpostedCommentIndicator\n      annotationId={annotationId}\n      ariaLabel={ariaLabel}\n      pendingEditTextMap={pendingEditTextMap}\n      pendingReplyMap={pendingReplyMap}\n      pendingAttachmentMap={pendingAttachmentMap}\n    />);\n};\n\nNoteUnpostedCommentIndicatorContainer.propTypes = propTypes;\n\nexport default NoteUnpostedCommentIndicatorContainer;", "import NoteUnpostedCommentIndicatorContainer from './NoteUnpostedCommentIndicatorContainer';\n\nexport default NoteUnpostedCommentIndicatorContainer;", "import React from 'react';\nimport PropTypes from 'prop-types';\n\nimport NotePopup from 'components/NotePopup';\nimport NoteState from 'components/NoteState';\nimport Icon from 'components/Icon';\nimport NoteUnpostedCommentIndicator from 'components/NoteUnpostedCommentIndicator';\nimport Choice from 'components/Choice';\nimport Button from 'components/Button';\n\nimport getLatestActivityDate from 'helpers/getLatestActivityDate';\nimport getColor from 'helpers/getColor';\nimport { isDarkColorHex, isLightColorHex } from 'helpers/color';\nimport dayjs from 'dayjs';\nimport classNames from 'classnames';\nimport { useTranslation } from 'react-i18next';\nimport core from 'core';\nimport { NotesPanelSortStrategy } from 'constants/sortStrategies';\nimport Theme from 'constants/theme';\nimport { OFFICE_EDITOR_TRACKED_CHANGE_KEY } from 'constants/officeEditor';\nimport { COMMON_COLORS } from 'constants/commonColors';\n\nimport './NoteHeader.scss';\n\nconst propTypes = {\n  icon: PropTypes.string,\n  iconColor: PropTypes.string,\n  color: PropTypes.string,\n  fillColor: PropTypes.string,\n  annotation: PropTypes.object,\n  language: PropTypes.string,\n  noteDateFormat: PropTypes.string,\n  isSelected: PropTypes.bool,\n  setIsEditing: PropTypes.func,\n  notesShowLastUpdatedDate: PropTypes.bool,\n  isUnread: PropTypes.bool,\n  renderAuthorName: PropTypes.func,\n  isNoteStateDisabled: PropTypes.bool,\n  isEditing: PropTypes.bool,\n  noteIndex: PropTypes.number,\n  sortStrategy: PropTypes.string,\n  activeTheme: PropTypes.string,\n  isMultiSelected: PropTypes.bool,\n  isMultiSelectMode: PropTypes.bool,\n  handleMultiSelect: PropTypes.func,\n  isGroupMember: PropTypes.bool,\n  showAnnotationNumbering: PropTypes.bool,\n  isTrackedChange: PropTypes.bool,\n};\n\nfunction NoteHeader(props) {\n  const {\n    icon,\n    iconColor,\n    annotation,\n    language,\n    noteDateFormat,\n    isSelected,\n    setIsEditing,\n    notesShowLastUpdatedDate,\n    isReply,\n    isUnread,\n    renderAuthorName,\n    isNoteStateDisabled,\n    isEditing,\n    noteIndex,\n    sortStrategy,\n    activeTheme,\n    isMultiSelected,\n    isMultiSelectMode,\n    handleMultiSelect,\n    isGroupMember,\n    showAnnotationNumbering,\n    timezone,\n    isTrackedChange,\n  } = props;\n\n  const [t] = useTranslation();\n\n  let date;\n  const dateCreated = (sortStrategy === NotesPanelSortStrategy.MODIFIED_DATE || (notesShowLastUpdatedDate && sortStrategy !== NotesPanelSortStrategy.CREATED_DATE)) ? getLatestActivityDate(annotation) : annotation.DateCreated;\n  if (timezone && dateCreated) {\n    const datetimeStr = dateCreated.toLocaleString('en-US', { timeZone: timezone });\n    date = new Date(datetimeStr);\n  } else {\n    date = dateCreated;\n  }\n\n  const noteDateAndTime = date ? dayjs(date).locale(language).format(noteDateFormat) : t('option.notesPanel.noteContent.noDate');\n\n  const numberOfReplies = annotation.getReplies().length;\n  let color = annotation[iconColor]?.toHexString?.();\n\n  if (activeTheme === Theme.DARK && color && isDarkColorHex(color)) {\n    color = COMMON_COLORS['white'];\n  } else if (activeTheme === Theme.LIGHT && color && isLightColorHex(color)) {\n    color = COMMON_COLORS['black'];\n  }\n\n  const fillColor = getColor(annotation.FillColor);\n  const annotationAssociatedNumber = annotation.getAssociatedNumber();\n  const annotationDisplayedAssociatedNumber = `#${annotationAssociatedNumber} - `;\n\n  const authorAndDateClass = classNames('author-and-date', { isReply });\n  const noteHeaderClass = classNames('NoteHeader', { parent: !isReply && !isGroupMember });\n\n  const acceptTrackedChange = (trackedChangeAnnot) => {\n    const trackedChangeId = trackedChangeAnnot.getCustomData(OFFICE_EDITOR_TRACKED_CHANGE_KEY);\n    core.getOfficeEditor().acceptTrackedChange(trackedChangeId);\n  };\n  const rejectTrackedChange = (trackedChangeAnnot) => {\n    const trackedChangeId = trackedChangeAnnot.getCustomData(OFFICE_EDITOR_TRACKED_CHANGE_KEY);\n    core.getOfficeEditor().rejectTrackedChange(trackedChangeId);\n  };\n\n  return (\n    <div className={noteHeaderClass}>\n      {!isReply &&\n        <div className=\"type-icon-container\">\n          {isUnread &&\n            <div className=\"unread-notification\"></div>\n          }\n          <Icon className=\"type-icon\" glyph={icon} color={color} fillColor={fillColor} />\n        </div>\n      }\n      <div className={authorAndDateClass}>\n        <div className=\"author-and-overflow\">\n          <div className=\"author-and-time\">\n            <div className=\"author\">\n              {showAnnotationNumbering && annotationAssociatedNumber !== undefined &&\n                <span className=\"annotation-number\">{annotationDisplayedAssociatedNumber}</span>\n              }\n              {renderAuthorName(annotation)}\n            </div>\n            <div className=\"date-and-num-replies\">\n              <div className=\"date-and-time\">\n                {noteDateAndTime}\n                {isGroupMember && ` (Page ${annotation.PageNumber})`}\n              </div>\n              {numberOfReplies > 0 && !isSelected &&\n                <div className=\"num-replies-container\">\n                  <Icon className=\"num-reply-icon\" glyph='icon-chat-bubble' />\n                  <div className=\"num-replies\">{numberOfReplies}</div>\n                </div>}\n            </div>\n          </div>\n          <div className=\"state-and-overflow\">\n            {isMultiSelectMode && !isGroupMember && !isReply &&\n              <Choice\n                id={`note-multi-select-toggle_${annotation.Id}`}\n                aria-label={`${renderAuthorName(annotation)} ${t('option.notesPanel.toggleMultiSelect')}`}\n                checked={isMultiSelected}\n                onClick={(e) => {\n                  e.preventDefault();\n                  e.stopPropagation();\n                  handleMultiSelect(!isMultiSelected);\n                }}\n              />\n            }\n            <NoteUnpostedCommentIndicator\n              annotationId={annotation.Id}\n              ariaLabel={`Unposted Comment, ${renderAuthorName(annotation)}, ${noteDateAndTime}`}\n            />\n            {!isNoteStateDisabled && !isReply && !isMultiSelectMode && !isGroupMember && !isTrackedChange &&\n              <NoteState\n                annotation={annotation}\n                isSelected={isSelected}\n              />\n            }\n            {!isEditing && isSelected && !isMultiSelectMode && !isGroupMember && !isTrackedChange &&\n              <NotePopup\n                noteIndex={noteIndex}\n                annotation={annotation}\n                setIsEditing={setIsEditing}\n                isReply={isReply}\n              />\n            }\n            {isSelected && isTrackedChange && !isMultiSelectMode &&\n              <>\n                <Button\n                  title={t('officeEditor.accept')}\n                  img={'icon-menu-checkmark'}\n                  className=\"tracked-change-icon-wrapper accept\"\n                  onClick={() => acceptTrackedChange(annotation)}\n                  iconClassName=\"tracked-change-icon\"\n                />\n                <Button\n                  title={t('officeEditor.reject')}\n                  img={'icon-close'}\n                  className=\"tracked-change-icon-wrapper reject\"\n                  onClick={() => rejectTrackedChange(annotation)}\n                  iconClassName=\"tracked-change-icon\"\n                />\n              </>\n            }\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nNoteHeader.propTypes = propTypes;\n\nexport default NoteHeader;\n", "import NoteHeader from './NoteHeader';\n\nexport default NoteHeader;", "import React from 'react';\nimport { useSelector, shallowEqual } from 'react-redux';\nimport selectors from 'selectors';\nimport NoteTextPreview from './NoteTextPreview';\n\nfunction NoteTextPreviewContainer(props) {\n  const [notePanelWidth] = useSelector(\n    (state) => [\n      selectors.getNotesPanelWidth(state),\n    ],\n    shallowEqual,\n  );\n\n  return (\n    <NoteTextPreview {...props} panelWidth={notePanelWidth} />\n  );\n}\n\nexport default NoteTextPreviewContainer;\n", "import NoteTextPreviewContainer from './NoteTextPreviewContainer';\n\nexport default NoteTextPreviewContainer;", "const icons = {\n  pdf: 'ic-file-pdf',\n  image: 'ic-file-img',\n  cad: 'ic-file-cad',\n  doc: 'ic-file-doc',\n  ppt: 'ic-file-ppt',\n  xls: 'ic-file-xls',\n  unknown: 'ic-file-etc'\n};\n\nconst FileAttachmentUtils = window.Core.Annotations.FileAttachmentUtils;\n\nexport async function decompressFileContent(file) {\n  return FileAttachmentUtils.decompressWithFlateDecode(file.content, file.type);\n}\n\nexport async function setAnnotationAttachments(annotation, files = []) {\n  await annotation.setAttachments(files);\n}\n\nexport function isImage(file) {\n  if (file.type && file.type.startsWith('image/')) {\n    return true;\n  }\n  return false;\n}\n\nexport function getAttachmentIcon(file) {\n  if (isImage(file)) {\n    return icons.image;\n  }\n  const extension = file.name?.split('.').pop().toLowerCase();\n  switch (extension) {\n    case 'pdf':\n      return icons.pdf;\n    case 'cad':\n      return icons.cad;\n    case 'doc':\n    case 'docx':\n      return icons.doc;\n    case 'ppt':\n    case 'pptx':\n      return icons.ppt;\n    case 'xls':\n    case 'xlsx':\n      return icons.xls;\n    default:\n      return icons.unknown;\n  }\n}\n", "import DOMPurify from 'dompurify';\n\nconst SVG_MIME_TYPE = 'image/svg+xml';\n\nconst hasFileSize = (file) => {\n  return file.size !== undefined;\n};\n\n// Taken from https://github.com/mattkrick/sanitize-svg/blob/master/src/sanitizeSVG.ts#L31\nconst readAsText = (svg) => {\n  return new Promise((resolve) => {\n    if (!hasFileSize(svg)) {\n      resolve(svg.toString('utf-8'));\n    } else {\n      const fileReader = new FileReader();\n      fileReader.onload = () => resolve(fileReader.result);\n      fileReader.readAsText(svg);\n    }\n  });\n};\n\nexport const isSVG = (file) => {\n  return file.type === SVG_MIME_TYPE;\n};\n\nexport const sanitizeSVG = async (file) => {\n  const svgText = await readAsText(file);\n  if (!svgText) {\n    return { svg: file };\n  }\n\n  const forbiddenTags = [];\n  DOMPurify.addHook('uponSanitizeElement', (_, hookEvent) => {\n    const { tagName, allowedTags } = hookEvent;\n    if (!allowedTags[tagName]) {\n      forbiddenTags.push(tagName);\n    }\n  });\n\n  const clean = DOMPurify.sanitize(svgText);\n  const svg = new Blob([clean], { type: SVG_MIME_TYPE });\n  return { svg, isDirty: forbiddenTags.length > 0 };\n};", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport { useTranslation } from 'react-i18next';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport { saveAs } from 'file-saver';\nimport Button from 'components/Button';\nimport Icon from 'components/Icon';\nimport Tooltip from 'components/Tooltip';\nimport { getAttachmentIcon, isImage, decompressFileContent } from 'helpers/ReplyAttachmentManager';\nimport { isSVG, sanitizeSVG } from 'helpers/sanitizeSVG';\n\nimport './ReplyAttachmentList.scss';\n\nconst ImagePreview = ({ file }) => {\n  const [t] = useTranslation();\n\n  const [src, setSrc] = useState();\n  const [isDirtySVG, setIsDirtySvg] = useState(false);\n\n  useEffect(() => {\n    const processImagePreview = async () => {\n      setIsDirtySvg(false);\n      let fileToSanitize = file;\n\n      const isImageFromPDF = !(file instanceof File) && !file.url;\n      if (isImageFromPDF) {\n        fileToSanitize = await decompressFileContent(file);\n      }\n\n      if (file instanceof File || isImageFromPDF) {\n        if (isSVG(file)) {\n          const { svg, isDirty } = await sanitizeSVG(fileToSanitize);\n          setSrc(URL.createObjectURL(svg));\n          setIsDirtySvg(isDirty);\n        } else {\n          setSrc(URL.createObjectURL(fileToSanitize));\n        }\n      }\n    };\n    processImagePreview();\n  }, [file]);\n\n  return (\n    <div className={classNames({\n      'reply-attachment-preview': true,\n      'dirty': isDirtySVG,\n    })}>\n      <img src={src} />\n      {isDirtySVG && <span className=\"reply-attachment-preview-message\">{t('message.svgMalicious')}</span>}\n    </div>\n  );\n};\n\nconst ReplyAttachmentList = ({ files, isEditing, fileDeleted }) => {\n  const [tabManager, previewEnabled] = useSelector((state) => [\n    selectors.getTabManager(state),\n    selectors.isReplyAttachmentPreviewEnabled(state)\n  ]);\n  const [t] = useTranslation();\n\n  const onClick = async (e, file) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    if (!tabManager) {\n      return console.warn('Can\\'t open attachment in non-multi-tab mode');\n    }\n\n    let fileData;\n    if (file instanceof File) {\n      fileData = file;\n    } else if (file.url) {\n      fileData = file.url;\n    } else {\n      fileData = await decompressFileContent(file);\n    }\n\n    fileData && tabManager.addTab(fileData, {\n      filename: file.name,\n      setActive: true,\n      saveCurrentActiveTabState: true\n    });\n  };\n\n  const onDelete = (e, file) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    fileDeleted(file);\n  };\n\n  const onDownload = async (e, file) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    const fileData = file.url ? file.url : await decompressFileContent(file);\n    saveAs(fileData, file.name);\n  };\n\n  return (\n    <div className=\"reply-attachment-list\">\n      {files.map((file, i) => (\n        <div\n          className=\"reply-attachment\"\n          key={i}\n          onClick={(e) => onClick(e, file)}\n        >\n          {previewEnabled && isImage(file) && (\n            <ImagePreview file={file} />\n          )}\n          <div className=\"reply-attachment-info\">\n            <Icon\n              className=\"reply-attachment-icon\"\n              glyph={getAttachmentIcon(file)}\n            />\n            <Tooltip content={file.name}>\n              <div className=\"reply-file-name\">{file.name}</div>\n            </Tooltip>\n            {isEditing ? (\n              <Button\n                className=\"attachment-button\"\n                title={`${t('action.delete')} ${t('option.type.fileattachment')}`}\n                img='icon-close'\n                onClick={(e) => onDelete(e, file)}\n              />\n            ) : (\n              <Button\n                className=\"attachment-button\"\n                title={`${t('action.download')} ${t('option.type.fileattachment')}`}\n                img='icon-download'\n                onClick={(e) => onDownload(e, file)}\n              />\n            )}\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default ReplyAttachmentList;\n", "import ReplyAttachmentList from './ReplyAttachmentList';\n\nexport default ReplyAttachmentList;", "import mentionsManager from './MentionsManager';\n/*\n * Transforming RichText Style object into the Object acceptable by React Quill component.\n */\n\nconst setReactQuillContent = (annotation, editor) => {\n  const richTextStyle = annotation.getRichTextStyle();\n  const indexes = Object.keys(richTextStyle);\n  const formattedText = mentionsManager.getFormattedTextFromDeltas(editor.getContents());\n  const text = mentionsManager.extractMentionDataFromStr(formattedText).plainTextValue;\n  const ops = [];\n\n  for (let i = 0; i < indexes.length; i++) {\n    const element = richTextStyle[indexes[i]];\n    const attr = getAttributtes(element);\n\n    if (isNaN(indexes[i])) {\n      continue;\n    }\n\n    const lastIndex = isNaN(indexes[i + 1]) ? text.length : indexes[i + 1];\n    const textSlice = text.slice(indexes[i], lastIndex);\n\n    ops.push({ insert: textSlice, attributes: attr });\n  }\n\n  editor.setContents(ops);\n  editor.setSelection(text.length, 0);\n};\n\nconst getAttributtes = (element) => {\n  const attr = {};\n  if (element['font-weight'] && element['font-weight'] !== 'normal') {\n    attr['bold'] = true;\n  }\n  if (element['font-style'] && element['font-style'] !== 'normal') {\n    attr['italic'] = true;\n  }\n  if (element['color']) {\n    attr['color'] = element['color'];\n  }\n  if (element['text-decoration']) {\n    const decoration = element['text-decoration'].split(' ');\n\n    if (decoration.includes('line-through')) {\n      attr['strike'] = true;\n    }\n    if (decoration.includes('word')) {\n      attr['underline'] = true;\n    }\n  }\n\n  return attr;\n};\n\nexport default setReactQuillContent;\n", "import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';\nimport PropTypes from 'prop-types';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport Autolinker from 'autolinker';\nimport dayjs from 'dayjs';\nimport classNames from 'classnames';\nimport LocalizedFormat from 'dayjs/plugin/localizedFormat';\nimport isString from 'lodash/isString';\nimport escape from 'lodash/escape';\nimport NoteTextarea from 'components/NoteTextarea';\nimport NoteContext from 'components/Note/Context';\nimport NoteHeader from 'components/NoteHeader';\nimport NoteTextPreview from 'components/NoteTextPreview';\nimport ReplyAttachmentList from 'components/ReplyAttachmentList';\n\nimport mentionsManager from 'helpers/MentionsManager';\nimport getLatestActivityDate from 'helpers/getLatestActivityDate';\nimport setAnnotationRichTextStyle from 'helpers/setAnnotationRichTextStyle';\nimport setReactQuillContent from 'helpers/setReactQuillContent';\nimport { isDarkColorHex, isLightColorHex } from 'helpers/color';\nimport { setAnnotationAttachments } from 'helpers/ReplyAttachmentManager';\nimport { isMobile } from 'helpers/device';\nimport core from 'core';\nimport { getDataWithKey, mapAnnotationToKey, annotationMapKeys } from 'constants/map';\nimport Theme from 'constants/theme';\nimport useDidUpdate from 'hooks/useDidUpdate';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport DataElements from 'constants/dataElement';\nimport DataElementWrapper from '../DataElementWrapper';\nimport { COMMON_COLORS } from 'constants/commonColors';\nimport Button from 'components/Button';\n\nimport './NoteContent.scss';\n\ndayjs.extend(LocalizedFormat);\n\nconst propTypes = {\n  annotation: PropTypes.object.isRequired,\n  isEditing: PropTypes.bool,\n  setIsEditing: PropTypes.func,\n  noteIndex: PropTypes.number,\n  isUnread: PropTypes.bool,\n  isNonReplyNoteRead: PropTypes.bool,\n  onReplyClicked: PropTypes.func,\n  isMultiSelected: PropTypes.bool,\n  isMultiSelectMode: PropTypes.bool,\n  handleMultiSelect: PropTypes.func,\n  isGroupMember: PropTypes.bool,\n  handleNoteClick: PropTypes.func,\n};\n\nconst NoteContent = ({\n  annotation,\n  isEditing,\n  setIsEditing,\n  noteIndex,\n  isUnread,\n  isNonReplyNoteRead,\n  onReplyClicked,\n  isMultiSelected,\n  isMultiSelectMode,\n  handleMultiSelect,\n  isGroupMember,\n  handleNoteClick,\n}) => {\n\n  const noteDateFormat = useSelector((state) => selectors.getNoteDateFormat(state));\n  const iconColor = useSelector((state) => selectors.getIconColor(state, mapAnnotationToKey(annotation), shallowEqual));\n  const isNoteStateDisabled = useSelector((state) => selectors.isElementDisabled(state, 'noteStateFlyout'));\n  const language = useSelector((state) => selectors.getCurrentLanguage(state));\n  const notesShowLastUpdatedDate = useSelector((state) => selectors.notesShowLastUpdatedDate(state));\n  const canCollapseTextPreview = useSelector((state) => selectors.isNotesPanelTextCollapsingEnabled(state));\n  const canCollapseReplyPreview = useSelector((state) => selectors.isNotesPanelRepliesCollapsingEnabled(state));\n  const activeTheme = useSelector((state) => selectors.getActiveTheme(state));\n  const timezone = useSelector((state) => selectors.getTimezone(state));\n  const customizableUI = useSelector((state) => selectors.getFeatureFlags(state)?.customizableUI);\n\n  const {\n    isSelected,\n    searchInput,\n    resize,\n    pendingEditTextMap,\n    onTopNoteContentClicked,\n    sortStrategy,\n    showAnnotationNumbering,\n    setPendingEditText\n  } = useContext(NoteContext);\n\n  const dispatch = useDispatch();\n  const [t] = useTranslation();\n\n  const isReply = annotation.isReply();\n  const isTrackedChange = mapAnnotationToKey(annotation) === annotationMapKeys.TRACKED_CHANGE;\n\n  const [attachments, setAttachments] = useState([]);\n\n  useEffect(() => {\n    setAttachments(annotation.getAttachments());\n  }, [annotation]);\n\n  useEffect(() => {\n    const annotationChangedListener = (annotations, action) => {\n      if (action === 'modify') {\n        annotations.forEach((annot) => {\n          if (annot.Id === annotation.Id) {\n            setAttachments(annot.getAttachments());\n          }\n        });\n      }\n    };\n    core.addEventListener('annotationChanged', annotationChangedListener);\n\n    return () => {\n      core.removeEventListener('annotationChanged', annotationChangedListener);\n    };\n  }, [annotation]);\n\n  useDidUpdate(() => {\n    if (!isEditing) {\n      dispatch(actions.finishNoteEditing());\n    }\n\n    resize();\n  }, [isEditing]);\n\n  const renderAuthorName = useCallback(\n    (annotation) => {\n      const name = core.getDisplayAuthor(annotation['Author']);\n\n      return name ? (\n        highlightSearchInput(name, searchInput)\n      ) : (\n        t('option.notesPanel.noteContent.noName')\n      );\n    },\n    [searchInput],\n  );\n\n  const skipAutoLink = annotation.getSkipAutoLink && annotation.getSkipAutoLink();\n\n  const renderContents = useCallback(\n    (contents, richTextStyle, fontColor, skipAutoLink) => {\n      const autolinkerContent = [];\n      if (!skipAutoLink) {\n        Autolinker.link(contents, {\n          stripPrefix: false,\n          stripTrailingSlash: false,\n          replaceFn(match) {\n            const href = match.getAnchorHref();\n            const anchorText = match.getAnchorText();\n            const offset = match.getOffset();\n\n            switch (match.getType()) {\n              case 'url':\n              case 'email':\n              case 'phone':\n                autolinkerContent.push({\n                  href,\n                  text: anchorText,\n                  start: offset,\n                  end: offset + match.getMatchedText().length\n                });\n            }\n          }\n        });\n      }\n\n      if (!autolinkerContent.length) {\n        const highlightResult = highlightSearchInput(contents, searchInput, richTextStyle);\n        const shouldCollapseAnnotationText = !isReply && canCollapseTextPreview;\n        const shouldCollapseReply = isReply && canCollapseReplyPreview;\n\n        /*\n         * Case there is no value on Search input, and the collapse of the text is allowed,\n         * just render the value with Text preview component\n         */\n        if (!searchInput && (shouldCollapseAnnotationText || shouldCollapseReply)) {\n          const beforeContent = () => {\n            if (!isTrackedChange) {\n              return null;\n            }\n            const text = annotation['TrackedChangeType'] === 1 ? t('officeEditor.added') : t('officeEditor.deleted');\n            return (\n              <span style={{ color: annotation.FillColor.toString(), fontWeight: 700 }}>{text}</span>\n            );\n          };\n\n          return (\n            <NoteTextPreview\n              linesToBreak={3}\n              comment\n              renderRichText={renderRichText}\n              richTextStyle={richTextStyle}\n              resize={resize}\n              style={fontColor}\n              beforeContent={beforeContent}\n            >\n              {contents}\n            </NoteTextPreview>\n          );\n        }\n        return highlightResult;\n      }\n      const contentToRender = [];\n      let strIdx = 0;\n      // Iterate through each case detected by Autolinker, wrap all content\n      // before the current link in a span tag, and wrap the current link\n      // in our own anchor tag\n      autolinkerContent.forEach((anchorData, forIdx) => {\n        const { start, end, href } = anchorData;\n        if (strIdx < start) {\n          contentToRender.push(\n            <span key={`span_${forIdx}`}>\n              {\n                highlightSearchInput(\n                  contents,\n                  searchInput,\n                  richTextStyle,\n                  strIdx,\n                  start\n                )\n              }\n            </span>\n          );\n        }\n        contentToRender.push(\n          <a\n            href={href}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            key={`a_${forIdx}`}\n          >\n            {\n              highlightSearchInput(\n                contents,\n                searchInput,\n                richTextStyle,\n                start,\n                end\n              )\n            }\n          </a>\n        );\n        strIdx = end;\n      });\n      // Ensure any content after the last link is accounted for\n      if (strIdx < contents.length - 1) {\n        contentToRender.push(highlightSearchInput(\n          contents,\n          searchInput,\n          richTextStyle,\n          strIdx\n        ));\n      }\n      return contentToRender;\n    },\n    [searchInput]\n  );\n\n  const icon = getDataWithKey(mapAnnotationToKey(annotation)).icon;\n  let customData;\n  try {\n    customData = JSON.parse(annotation.getCustomData('trn-mention'));\n  } catch (e) {\n    customData = annotation.getCustomData('trn-mention');\n  }\n\n  let contents = customData?.contents || annotation.getContents();\n  contents = sanitizeContent(contents);\n\n  const contentsToRender = annotation.getContents();\n  const richTextStyle = annotation.getRichTextStyle();\n  let textColor = annotation['TextColor'];\n\n  if (activeTheme === Theme.DARK) {\n    if (textColor && isDarkColorHex(textColor.toHexString())) {\n      textColor = new window.Core.Annotations.Color(255, 255, 255, 1);\n    }\n\n    if (richTextStyle) {\n      const sections = Object.keys(richTextStyle);\n      sections.forEach((a) => {\n        if (richTextStyle[a]['color'] && isDarkColorHex(richTextStyle[a]['color'])) {\n          richTextStyle[a]['color'] = COMMON_COLORS['white'];\n        }\n      });\n    }\n  } else if (activeTheme === Theme.LIGHT) {\n    if (textColor && isLightColorHex(textColor.toHexString())) {\n      textColor = new window.Core.Annotations.Color(0, 0, 0, 1);\n    }\n\n    if (richTextStyle) {\n      const sections = Object.keys(richTextStyle);\n      sections.forEach((a) => {\n        if (richTextStyle[a]['color'] && isLightColorHex(richTextStyle[a]['color'])) {\n          richTextStyle[a]['color'] = COMMON_COLORS['black'];\n        }\n      });\n    }\n  }\n  // This is the text placeholder passed to the ContentArea\n  // It ensures that if we try and edit, we get the right placeholder\n  // depending on whether the comment has been saved to the annotation or not\n  const thereIsNoUnpostedEdit = typeof pendingEditTextMap[annotation.Id] === 'undefined';\n  let textAreaValue;\n  if (contents && thereIsNoUnpostedEdit) {\n    textAreaValue = contents;\n  } else {\n    textAreaValue = pendingEditTextMap[annotation.Id];\n  }\n\n  const handleNoteContentClicked = () => {\n    if (!isGroupMember) {\n      if (isReply) {\n        onReplyClicked(annotation);\n      } else if (!isEditing) {\n        // collapse expanded note when top noteContent is clicked if it's not being edited\n        onTopNoteContentClicked();\n      }\n    }\n  };\n\n  const handleContentsClicked = (e) => {\n    if (window.getSelection()?.toString()) {\n      e?.stopPropagation();\n    }\n    handleNoteClick(e);\n  };\n\n  const noteContentClass = classNames({\n    NoteContent: true,\n    isReply,\n    unread: isUnread, // The note content itself is unread or it has unread replies\n    clicked: isNonReplyNoteRead, // The top note content is read\n    'modular-ui': customizableUI,\n  });\n\n  const content = useMemo(\n    () => {\n      const contentStyle = {};\n      if (textColor) {\n        contentStyle.color = textColor.toHexString();\n      }\n\n      return (\n        <>\n          {(isEditing && isSelected) ? (\n            <ContentArea\n              annotation={annotation}\n              noteIndex={noteIndex}\n              setIsEditing={setIsEditing}\n              textAreaValue={textAreaValue}\n              onTextAreaValueChange={setPendingEditText}\n              pendingText={pendingEditTextMap[annotation.Id]}\n            />\n          ) : (\n            contentsToRender && (\n              <div className={classNames('container', { 'reply-content': isReply })} onClick={handleContentsClicked}>\n                {isReply && (attachments.length > 0) && (\n                  <ReplyAttachmentList\n                    files={attachments}\n                    isEditing={false}\n                  />\n                )}\n                {renderContents(contentsToRender, richTextStyle, contentStyle, skipAutoLink)}\n              </div>\n            )\n          )}\n        </>\n      );\n    },\n    [annotation, isSelected, isEditing, setIsEditing, contents, renderContents, textAreaValue, setPendingEditText, attachments]\n  );\n\n  const text = annotation.getCustomData('trn-annot-preview');\n  const textPreview = useMemo(\n    () => {\n      if (text === '') {\n        return null;\n      }\n\n      const highlightSearchResult = highlightSearchInput(text, searchInput);\n      const shouldCollapseAnnotationText = !isReply && canCollapseTextPreview;\n      // If we have a search result do not use text\n      // preview but instead show the entire text\n      if (isString(highlightSearchResult) && shouldCollapseAnnotationText) {\n        return (\n          <DataElementWrapper\n            className=\"selected-text-preview\"\n            dataElement=\"notesSelectedTextPreview\">\n            <NoteTextPreview linesToBreak={3}>\n              {`\"${highlightSearchResult}\"`}\n            </NoteTextPreview>\n          </DataElementWrapper>\n        );\n      }\n      return (\n        <div className=\"selected-text-preview\" style={{ paddingRight: '12px' }}>\n          {highlightSearchResult}\n        </div>\n      );\n    }, [text, searchInput]);\n\n  const header = useMemo(\n    () => {\n      return (\n        <NoteHeader\n          icon={icon}\n          iconColor={iconColor}\n          annotation={annotation}\n          language={language}\n          noteDateFormat={noteDateFormat}\n          isSelected={isSelected}\n          setIsEditing={setIsEditing}\n          notesShowLastUpdatedDate={notesShowLastUpdatedDate}\n          isReply={isReply}\n          isUnread={isUnread}\n          renderAuthorName={renderAuthorName}\n          isNoteStateDisabled={isNoteStateDisabled}\n          isEditing={isEditing}\n          noteIndex={noteIndex}\n          sortStrategy={sortStrategy}\n          activeTheme={activeTheme}\n          handleMultiSelect={handleMultiSelect}\n          isMultiSelected={isMultiSelected}\n          isMultiSelectMode={isMultiSelectMode}\n          isGroupMember={isGroupMember}\n          showAnnotationNumbering={showAnnotationNumbering}\n          timezone={timezone}\n          isTrackedChange={isTrackedChange}\n        />\n      );\n    }, [icon, iconColor, annotation, language, noteDateFormat, isSelected, setIsEditing, notesShowLastUpdatedDate, isReply, isUnread, renderAuthorName, core.getDisplayAuthor(annotation['Author']), isNoteStateDisabled, isEditing, noteIndex, getLatestActivityDate(annotation), sortStrategy, handleMultiSelect, isMultiSelected, isMultiSelectMode, isGroupMember, timezone, isTrackedChange]\n  );\n\n  return (\n    <div className={noteContentClass} onClick={handleNoteContentClicked}>\n      {header}\n      {textPreview}\n      {content}\n    </div>\n  );\n};\n\nNoteContent.propTypes = propTypes;\n\nexport default NoteContent;\n\n// a component that contains the content textarea, the save button and the cancel button\nconst ContentArea = ({\n  annotation,\n  noteIndex,\n  setIsEditing,\n  textAreaValue,\n  onTextAreaValueChange,\n  pendingText\n}) => {\n  const [\n    autoFocusNoteOnAnnotationSelection,\n    isMentionEnabled,\n    isInlineCommentDisabled,\n    isInlineCommentOpen,\n    isNotesPanelOpen,\n    activeDocumentViewerKey,\n    isAnyCustomPanelOpen,\n  ] = useSelector((state) => [\n    selectors.getAutoFocusNoteOnAnnotationSelection(state),\n    selectors.getIsMentionEnabled(state),\n    selectors.isElementDisabled(state, DataElements.INLINE_COMMENT_POPUP),\n    selectors.isElementOpen(state, DataElements.INLINE_COMMENT_POPUP),\n    selectors.isElementOpen(state, DataElements.NOTES_PANEL),\n    selectors.getActiveDocumentViewerKey(state),\n    selectors.isAnyCustomPanelOpen(state),\n  ]);\n  const [t] = useTranslation();\n  const textareaRef = useRef();\n  const isReply = annotation.isReply();\n  const {\n    setCurAnnotId,\n    pendingAttachmentMap,\n    deleteAttachment,\n    clearAttachments,\n    addAttachments\n  } = useContext(NoteContext);\n\n  const shouldNotFocusOnInput = !isInlineCommentDisabled && isInlineCommentOpen && isMobile();\n\n  useEffect(() => {\n    // on initial mount, focus the last character of the textarea\n    if (isAnyCustomPanelOpen || (isNotesPanelOpen || isInlineCommentOpen) && textareaRef.current) {\n      const editor = textareaRef.current.getEditor();\n      const isFreeTextAnnnotation = annotation && annotation instanceof window.Core.Annotations.FreeTextAnnotation;\n      isFreeTextAnnnotation && editor.setText('');\n\n      /**\n       * If there is a pending text we should update the annotation rich text style\n       * with this pending text style.\n       */\n      if (pendingText) {\n        setAnnotationRichTextStyle(editor, annotation);\n      } else if (editor.getContents()) {\n        setTimeout(() => {\n          // need setTimeout because textarea seem to rerender and unfocus\n          if (isMentionEnabled) {\n            textAreaValue = mentionsManager.getFormattedTextFromDeltas(editor.getContents());\n            const { plainTextValue, ids } = mentionsManager.extractMentionDataFromStr(textAreaValue);\n\n            if (ids.length) {\n              editor.setText(plainTextValue);\n            }\n          }\n\n          if (shouldNotFocusOnInput) {\n            return;\n          }\n\n          if (autoFocusNoteOnAnnotationSelection) {\n            textareaRef.current?.focus();\n            const annotRichTextStyle = annotation.getRichTextStyle();\n            if (annotRichTextStyle) {\n              setReactQuillContent(annotation, editor);\n            }\n          }\n        }, 100);\n      }\n\n      const lastNewLineCharacterLength = 1;\n      const textLength = editor.getLength() - lastNewLineCharacterLength;\n\n      if (shouldNotFocusOnInput) {\n        return;\n      }\n\n      setTimeout(() => {\n        if (textLength) {\n          editor.setSelection(textLength, textLength);\n        }\n      }, 100);\n    }\n  }, [isNotesPanelOpen, isInlineCommentOpen, shouldNotFocusOnInput]);\n\n  useEffect(() => {\n    if (isReply && pendingAttachments.length === 0) {\n      // Load attachments\n      const attachments = annotation.getAttachments();\n      addAttachments(annotation.Id, attachments);\n    }\n  }, []);\n\n  const setContents = async (e) => {\n    // prevent the textarea from blurring out which will unmount these two buttons\n    e.preventDefault();\n\n    const editor = textareaRef.current.getEditor();\n    textAreaValue = mentionsManager.getFormattedTextFromDeltas(editor.getContents());\n    setAnnotationRichTextStyle(editor, annotation);\n\n    const hasTrailingNewlineToRemove = textAreaValue.length > 1 && textAreaValue[textAreaValue.length - 1] === '\\n';\n    if (hasTrailingNewlineToRemove) {\n      textAreaValue = textAreaValue.slice(0, textAreaValue.length - 1);\n    }\n\n    const skipAutoLink = annotation.getSkipAutoLink && annotation.getSkipAutoLink();\n    if (skipAutoLink) {\n      annotation.disableSkipAutoLink();\n    }\n\n    if (isMentionEnabled) {\n      const { plainTextValue, ids } = mentionsManager.extractMentionDataFromStr(textAreaValue);\n\n      // If modified, double check for ids\n      const annotMentionData = mentionsManager.extractMentionDataFromAnnot(annotation);\n      annotMentionData.mentions.forEach((mention) => {\n        if (plainTextValue.includes(mention.value)) {\n          ids.push(mention.id);\n        }\n      });\n\n      annotation.setCustomData('trn-mention', JSON.stringify({\n        contents: textAreaValue,\n        ids,\n      }));\n      annotation.setContents(plainTextValue);\n    } else {\n      annotation.setContents(textAreaValue);\n    }\n\n    await setAnnotationAttachments(annotation, pendingAttachmentMap[annotation.Id]);\n\n    const source = (annotation instanceof window.Core.Annotations.FreeTextAnnotation)\n      ? 'textChanged' : 'noteChanged';\n    core.getAnnotationManager(activeDocumentViewerKey).trigger('annotationChanged', [[annotation], 'modify', { 'source': source }]);\n\n    if (annotation instanceof window.Core.Annotations.FreeTextAnnotation) {\n      core.drawAnnotationsFromList([annotation]);\n    }\n\n    setIsEditing(false, noteIndex);\n    // Only set comment to unposted state if it is not empty\n    if (textAreaValue !== '') {\n      onTextAreaValueChange(undefined, annotation.Id);\n    }\n    clearAttachments(annotation.Id);\n  };\n\n  const onBlur = (e) => {\n    if (e.relatedTarget?.getAttribute('data-element')?.includes('annotationCommentButton')) {\n      e.target.focus();\n      return;\n    }\n    setCurAnnotId(undefined);\n  };\n\n  const onFocus = () => {\n    setCurAnnotId(annotation.Id);\n  };\n\n  const contentClassName = classNames('edit-content', { 'reply-content': isReply });\n  const pendingAttachments = pendingAttachmentMap[annotation.Id] || [];\n\n  return (\n    <div className={contentClassName}>\n      {isReply && pendingAttachments.length > 0 && (\n        <ReplyAttachmentList\n          files={pendingAttachments}\n          isEditing={true}\n          fileDeleted={(file) => deleteAttachment(annotation.Id, file)}\n        />\n      )}\n      <NoteTextarea\n        ref={(el) => {\n          textareaRef.current = el;\n        }}\n        value={textAreaValue}\n        onChange={(value) => onTextAreaValueChange(value, annotation.Id)}\n        onSubmit={setContents}\n        isReply={isReply}\n        onBlur={onBlur}\n        onFocus={onFocus}\n      />\n      <div className=\"edit-buttons\">\n        <Button\n          className=\"cancel-button\"\n          label={t('action.cancel')}\n          onClick={(e) => {\n            e.stopPropagation();\n            setIsEditing(false, noteIndex);\n            // Clear pending text\n            onTextAreaValueChange(undefined, annotation.Id);\n            clearAttachments(annotation.Id);\n          }}\n        />\n        <Button\n          className={`save-button${!textAreaValue ? ' disabled' : ''}`}\n          disabled={!textAreaValue}\n          label={t('action.save')}\n          onClick={(e) => {\n            e.stopPropagation();\n            setContents(e);\n          }}\n        />\n      </div>\n    </div>\n  );\n};\n\nContentArea.propTypes = {\n  noteIndex: PropTypes.number.isRequired,\n  annotation: PropTypes.object.isRequired,\n  setIsEditing: PropTypes.func.isRequired,\n  textAreaValue: PropTypes.string,\n  onTextAreaValueChange: PropTypes.func.isRequired,\n  pendingText: PropTypes.string\n};\n\nconst getRichTextSpan = (text, richTextStyle, key) => {\n  const style = {\n    fontWeight: richTextStyle['font-weight'],\n    fontStyle: richTextStyle['font-style'],\n    textDecoration: richTextStyle['text-decoration'],\n    color: richTextStyle['color']\n  };\n  if (style.textDecoration) {\n    style.textDecoration = style.textDecoration.replace('word', 'underline');\n  }\n  return (\n    <span style={style} key={key}>{text}</span>\n  );\n};\n\nconst renderRichText = (text, richTextStyle, start) => {\n  if (!richTextStyle || !text) {\n    return text;\n  }\n\n  const styles = {};\n  const indices = Object.keys(richTextStyle).map(Number).sort((a, b) => a - b);\n  for (let i = 0; i < indices.length; i++) {\n    let index = indices[i] - start;\n    index = Math.min(Math.max(index, 0), text.length);\n    styles[index] = richTextStyle[indices[i]];\n    if (index === text.length) {\n      break;\n    }\n  }\n\n  const contentToRender = [];\n  const styleIndices = Object.keys(styles).map(Number).sort((a, b) => a - b);\n  for (let i = 1; i < styleIndices.length; i++) {\n    contentToRender.push(getRichTextSpan(\n      text.slice(styleIndices[i - 1], styleIndices[i]),\n      styles[styleIndices[i - 1]],\n      `richtext_span_${i}`\n    ));\n  }\n\n  return contentToRender;\n};\n\nconst highlightSearchInput = (fullText, searchInput, richTextStyle, start = 0, end = fullText.length) => {\n  const text = fullText.slice(start, end);\n  const loweredText = text.toLowerCase();\n  const loweredSearchInput = searchInput.toLowerCase();\n  if (richTextStyle) {\n    richTextStyle['0'] = richTextStyle['0'] || {};\n    richTextStyle[fullText.length] = richTextStyle[fullText.length] || {};\n  }\n  let lastFoundInstance = loweredText.indexOf(loweredSearchInput);\n  if (!loweredSearchInput.trim() || lastFoundInstance === -1) {\n    return renderRichText(text, richTextStyle, start);\n  }\n  const contentToRender = [];\n  const allFoundPositions = [lastFoundInstance];\n  // Escape all RegExp special characters\n  const regexSafeSearchInput = loweredSearchInput.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n  if (new RegExp(`(${regexSafeSearchInput})`, 'gi').test(loweredText)) {\n    while (lastFoundInstance !== -1) {\n      lastFoundInstance = loweredText.indexOf(loweredSearchInput, lastFoundInstance + loweredSearchInput.length);\n      if (lastFoundInstance !== -1) {\n        allFoundPositions.push(lastFoundInstance);\n      }\n    }\n  }\n  allFoundPositions.forEach((position, idx) => {\n    // Account for any content at the beginning of the string before the first\n    // instance of the searchInput\n    if (idx === 0 && position !== 0) {\n      contentToRender.push(renderRichText(text.substring(0, position), richTextStyle, start));\n    }\n    contentToRender.push(\n      <span className=\"highlight\" key={`highlight_span_${idx}`}>\n        {\n          renderRichText(\n            text.substring(position, position + loweredSearchInput.length),\n            richTextStyle,\n            start + position)\n        }\n      </span>\n    );\n    if (\n      // Ensure that we do not try to make an out-of-bounds access\n      position + loweredSearchInput.length < loweredText.length\n      // Ensure that this is the end of the allFoundPositions array\n      && position + loweredSearchInput.length !== allFoundPositions[idx + 1]\n    ) {\n      contentToRender.push(renderRichText(\n        text.substring(position + loweredSearchInput.length, allFoundPositions[idx + 1]),\n        richTextStyle,\n        start + position + loweredSearchInput.length\n      ));\n    }\n  });\n  return contentToRender;\n};\n\n/**\n * @ignore\n * Sanitizes the given content to prevent XSS attacks by converting HTML characters\n * into their encoded equivalents.\n *\n * @param {string} content - The content to sanitize.\n * @returns {string} The sanitized content, or the original content if no changes are needed.\n */\nfunction sanitizeContent(content) {\n  return content ? escape(content) : content;\n}\n", "import NoteContent from './NoteContent';\n\nexport default NoteContent;", "import React, { useState, useEffect, useRef, useContext } from 'react';\nimport PropTypes from 'prop-types';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport NoteContext from 'components/Note/Context';\nimport NoteTextarea from 'components/NoteTextarea';\nimport ReplyAttachmentList from 'components/ReplyAttachmentList';\nimport Button from 'components/Button';\nimport classNames from 'classnames';\nimport core from 'core';\nimport mentionsManager from 'helpers/MentionsManager';\nimport setAnnotationRichTextStyle from 'helpers/setAnnotationRichTextStyle';\nimport { setAnnotationAttachments } from 'helpers/ReplyAttachmentManager';\nimport useDidUpdate from 'hooks/useDidUpdate';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport { isMobile } from 'src/helpers/device';\nimport DataElements from 'src/constants/dataElement';\n\nimport './ReplyArea.scss';\n\nconst propTypes = {\n  annotation: PropTypes.object.isRequired,\n};\n\n// a component that contains the reply textarea, the reply button and the cancel button\nconst ReplyArea = ({ annotation, isUnread, onPendingReplyChange }) => {\n  const [\n    autoFocusNoteOnAnnotationSelection,\n    isReadOnly,\n    isReplyDisabled,\n    isReplyDisabledForAnnotation,\n    isMentionEnabled,\n    isNoteEditingTriggeredByAnnotationPopup,\n    isInlineCommentDisabled,\n    isInlineCommentOpen,\n    activeDocumentViewerKey,\n  ] = useSelector(\n    (state) => [\n      selectors.getAutoFocusNoteOnAnnotationSelection(state),\n      selectors.isDocumentReadOnly(state),\n      selectors.isElementDisabled(state, 'noteReply'),\n      selectors.getIsReplyDisabled(state)?.(annotation),\n      selectors.getIsMentionEnabled(state),\n      selectors.getIsNoteEditing(state),\n      selectors.isElementDisabled(state, DataElements.INLINE_COMMENT_POPUP),\n      selectors.isElementOpen(state, DataElements.INLINE_COMMENT_POPUP),\n      selectors.getActiveDocumentViewerKey(state),\n    ],\n    shallowEqual\n  );\n  const {\n    isContentEditable,\n    isSelected,\n    pendingReplyMap,\n    setPendingReply,\n    isExpandedFromSearch,\n    scrollToSelectedAnnot,\n    setCurAnnotId,\n    pendingAttachmentMap,\n    clearAttachments,\n    deleteAttachment\n  } = useContext(NoteContext);\n  const [isFocused, setIsFocused] = useState(false);\n  const dispatch = useDispatch();\n  const textareaRef = useRef();\n\n  const shouldNotFocusOnInput = !isInlineCommentDisabled && isInlineCommentOpen && isMobile();\n\n  useDidUpdate(() => {\n    if (!isFocused) {\n      dispatch(actions.finishNoteEditing());\n    }\n  }, [isFocused]);\n\n  useEffect(() => {\n    if (shouldNotFocusOnInput) {\n      return;\n    }\n\n    if (\n      isNoteEditingTriggeredByAnnotationPopup &&\n      isSelected &&\n      !isContentEditable &&\n      autoFocusNoteOnAnnotationSelection &&\n      textareaRef &&\n      textareaRef.current\n    ) {\n      textareaRef.current.focus();\n    }\n  }, [isContentEditable, isNoteEditingTriggeredByAnnotationPopup, isSelected, shouldNotFocusOnInput]);\n\n  useEffect(() => {\n    // on initial mount, focus the last character of the textarea\n    // when search item, should disable auto focus\n    if (!isExpandedFromSearch && scrollToSelectedAnnot) {\n      // use \"setTimeout\" to wait for element to be added before focusing to have the blinking text cursor appear\n      setTimeout(() => {\n        // calling focus() cause the \"NotePanel\" to scroll to note that being focused.\n        // we don't want to jump to the selected annotation when scrolling up and down, so only focus once\n        if (textareaRef && textareaRef.current && autoFocusNoteOnAnnotationSelection) {\n          textareaRef.current.focus();\n        }\n      }, 100);\n    }\n    if (textareaRef && textareaRef.current) {\n      if (shouldNotFocusOnInput) {\n        return;\n      }\n\n      const editor = textareaRef.current.getEditor();\n      const lastNewLineCharacterLength = 1;\n      const textLength = editor.getLength() - lastNewLineCharacterLength;\n      setTimeout(() => {\n        if (textareaRef.current) {\n          textareaRef.current.editor.setSelection(textLength, textLength);\n        }\n      }, 100);\n    }\n  }, []);\n\n  const postReply = async (e) => {\n    // prevent the textarea from blurring out\n    e.preventDefault();\n    e.stopPropagation();\n\n    const editor = textareaRef.current.getEditor();\n    const replyText = mentionsManager.getFormattedTextFromDeltas(editor.getContents());\n\n    if (!replyText.trim()) {\n      return;\n    }\n\n    if (isMentionEnabled) {\n      const replyAnnotation = mentionsManager.createMentionReply(annotation, replyText);\n      setAnnotationRichTextStyle(editor, replyAnnotation);\n      await setAnnotationAttachments(replyAnnotation, pendingAttachmentMap[annotation.Id]);\n      core.addAnnotations([replyAnnotation], activeDocumentViewerKey);\n    } else {\n      const replyAnnotation = core.createAnnotationReply(annotation, replyText);\n      setAnnotationRichTextStyle(editor, replyAnnotation);\n      await setAnnotationAttachments(replyAnnotation, pendingAttachmentMap[annotation.Id]);\n      core.getAnnotationManager(activeDocumentViewerKey).trigger('annotationChanged', [[replyAnnotation], 'modify', {}]);\n    }\n\n    setPendingReply('', annotation.Id);\n    clearAttachments(annotation.Id);\n  };\n\n  const ifReplyNotAllowed =\n    isReadOnly ||\n    isReplyDisabled ||\n    isReplyDisabledForAnnotation;\n\n  const replyAreaClass = classNames({\n    'reply-area': true,\n    unread: isUnread,\n  });\n\n  const handleNoteTextareaChange = (value) => {\n    setPendingReply(value, annotation.Id);\n    onPendingReplyChange && onPendingReplyChange();\n  };\n\n  const onBlur = () => {\n    setIsFocused(false);\n    setCurAnnotId(undefined);\n  };\n\n  const onFocus = () => {\n    setIsFocused(true);\n    setCurAnnotId(annotation.Id);\n  };\n\n  const pendingAttachments = pendingAttachmentMap[annotation.Id] || [];\n\n  return (ifReplyNotAllowed || !isSelected) ? null : (\n    <form onSubmit={postReply} className=\"reply-area-container\">\n      {pendingAttachments.length > 0 && (\n        <ReplyAttachmentList\n          files={pendingAttachments}\n          isEditing={true}\n          fileDeleted={(file) => deleteAttachment(annotation.Id, file)}\n        />\n      )}\n      <div className=\"reply-area-with-button\">\n        <div\n          className={replyAreaClass}\n          // stop bubbling up otherwise the note will be closed\n          // due to annotation deselection\n          onMouseDown={(e) => e.stopPropagation()}\n        >\n          <NoteTextarea\n            ref={(el) => {\n              textareaRef.current = el;\n            }}\n            value={pendingReplyMap[annotation.Id]}\n            onChange={(value) => handleNoteTextareaChange(value)}\n            onSubmit={postReply}\n            onBlur={onBlur}\n            onFocus={onFocus}\n            isReply\n          />\n        </div>\n        <div className=\"reply-button-container\">\n          <Button\n            img=\"icon-post-reply\"\n            className='reply-button'\n            title={'action.submit'}\n            disabled={!pendingReplyMap[annotation.Id]}\n            onClick={postReply}\n            isSubmitType\n          />\n        </div>\n      </div>\n    </form>\n  );\n};\n\nReplyArea.propTypes = propTypes;\n\nexport default ReplyArea;\n", "import ReplyArea from './ReplyArea';\n\nexport default ReplyArea;", "import React, { useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\n\nimport Button from 'components/Button';\nimport NoteContent from 'components/NoteContent';\nimport PropTypes from 'prop-types';\nimport core from 'core';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport classNames from 'classnames';\n\nconst propTypes = {\n  groupAnnotations: PropTypes.array.isRequired,\n  isMultiSelectMode: PropTypes.bool.isRequired,\n};\n\nconst NoteGroupSection = ({\n  groupAnnotations,\n  isMultiSelectMode,\n}) => {\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n  const [isViewingGroupAnnots, setIsViewingGroupAnnots] = useState(false);\n  const customizableUI = useSelector((state) => selectors.getFeatureFlags(state)?.customizableUI);\n\n  const upArrow = 'ic_chevron_up_black_24px';\n  const downArrow = 'ic_chevron_down_black_24px';\n\n  const ViewAllAnnotsButton = (\n    <Button\n      onClick={(e) => {\n        e.preventDefault();\n        e.stopPropagation();\n        setIsViewingGroupAnnots(true);\n      }}\n      className=\"text-button\"\n      ariaLabel={t('component.noteGroupSection.open')}\n      label={t('component.noteGroupSection.open')}\n      img={downArrow}\n    />\n  );\n  const CloseAllAnnotsButton = (\n    <Button\n      onClick={(e) => {\n        e.preventDefault();\n        e.stopPropagation();\n        setIsViewingGroupAnnots(false);\n      }}\n      className=\"text-button\"\n      ariaLabel={t('component.noteGroupSection.close')}\n      label={t('component.noteGroupSection.close')}\n      img={upArrow}\n    />\n  );\n\n  return (\n    <div\n      className={classNames({\n        'group-section': true,\n        'modular-ui': customizableUI,\n      })}\n    >\n      {isViewingGroupAnnots ? CloseAllAnnotsButton : ViewAllAnnotsButton}\n      {isViewingGroupAnnots &&\n        groupAnnotations.map((groupAnnotation, i) => {\n          // Ignore the group primary annotation\n          if (i === 0) {\n            return null;\n          }\n          return (\n            <Button\n              key={groupAnnotation.Id}\n              className=\"group-child\"\n              onClick={(e) => {\n                e.preventDefault();\n                e.stopPropagation();\n                core.selectAnnotation(groupAnnotation);\n                core.jumpToAnnotation(groupAnnotation);\n                dispatch(actions.openElement('annotationPopup'));\n              }}\n            >\n              <NoteContent\n                key={groupAnnotation.Id}\n                annotation={groupAnnotation}\n                isUnread={false}\n                isGroupMember={true}\n                isMultiSelectMode={isMultiSelectMode}\n              />\n            </Button>\n          );\n        })\n      }\n    </div>\n  );\n};\n\nNoteGroupSection.propTypes = propTypes;\n\nexport default NoteGroupSection;", "import React, { useEffect, useState, useCallback } from 'react';\nimport { useSelector, shallowEqual, useDispatch } from 'react-redux';\nimport core from 'core';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport { createPortal } from 'react-dom';\nimport { getAnnotationPosition } from 'helpers/getPopupPosition';\nimport getRootNode from 'helpers/getRootNode';\nimport DataElements from 'constants/dataElement';\n\nimport './AnnotationNoteConnectorLine.scss';\n\nconst LineConnectorPortal = ({ children }) => {\n  const mount = getRootNode().querySelector('#line-connector-root');\n  const el = document.createElement('div');\n  el.setAttribute('data-element', DataElements.ANNOTATION_NOTE_CONNECTOR_LINE);\n\n  useEffect(() => {\n    mount.appendChild(el);\n    return () => mount.removeChild(el);\n  }, [el, mount]);\n\n  return createPortal(children, el);\n};\n\nconst AnnotationNoteConnectorLine = ({ annotation, noteContainerRef, isCustomPanelOpen }) => {\n  const [\n    notePanelWidth,\n    lineIsOpen,\n    notePanelIsOpen,\n    isLineDisabled,\n    documentContainerWidth,\n    documentContainerHeight,\n    activeDocumentViewerKey,\n  ] = useSelector(\n    (state) => [\n      selectors.getNotesPanelWidth(state),\n      selectors.isElementOpen(state, DataElements.ANNOTATION_NOTE_CONNECTOR_LINE),\n      selectors.isElementOpen(state, DataElements.NOTES_PANEL),\n      selectors.isElementDisabled(state, DataElements.ANNOTATION_NOTE_CONNECTOR_LINE),\n      selectors.getDocumentContainerWidth(state),\n      selectors.getDocumentContainerHeight(state),\n      selectors.getActiveDocumentViewerKey(state),\n    ],\n    shallowEqual,\n  );\n\n  const dispatch = useDispatch();\n\n  // Right Horizontal Line\n  const [rightHorizontalLineWidth, setRightHorizontalLineWidth] = useState(0);\n  const [rightHorizontalLineTop, setRightHorizontalLineTop] = useState(0);\n  const [rightHorizontalLineRight, setRightHorizontalLineRight] = useState(0);\n\n  // Left Horizontal Line\n  const [leftHorizontalLineWidth, setLeftHorizontalLineWidth] = useState(0);\n  const [leftHorizontalLineTop, setLeftHorizontalLineTop] = useState(0);\n  const [leftHorizontalLineRight, setLeftHorizontalLineRight] = useState(0);\n\n  const {\n    bottomRight: annotationBottomRight,\n    topLeft: annotationTopLeft\n  } = getAnnotationPosition(annotation, activeDocumentViewerKey);\n\n  const getAnnotationLineOffset = useCallback(() => {\n    if (annotation.Subject === 'Note') {\n      return 4;\n    }\n    return 15;\n  }, [annotation]);\n\n  useEffect(() => {\n    const { scrollTop, scrollLeft } = core.getScrollViewElement(activeDocumentViewerKey);\n    const notePanelLeftPadding = 16;\n    const isAnnotationPositionInvalid = !(annotationBottomRight && annotationTopLeft);\n    if (isAnnotationPositionInvalid) {\n      return () => {\n        dispatch(actions.closeElement(DataElements.ANNOTATION_NOTE_CONNECTOR_LINE));\n      };\n    }\n    const annotWidthInPixels = annotationBottomRight.x - annotationTopLeft.x;\n    const annotHeightInPixels = annotationBottomRight.y - annotationTopLeft.y;\n\n    const viewerWidth = window.isApryseWebViewerWebComponent ? getRootNode().host.clientWidth : window.innerWidth;\n    const viewerOffsetTop = window.isApryseWebViewerWebComponent ? getRootNode().host.offsetTop : 0;\n\n    setRightHorizontalLineRight(notePanelWidth - notePanelLeftPadding);\n    setRightHorizontalLineTop(noteContainerRef.current.getBoundingClientRect().top - viewerOffsetTop);\n    const lineWidth = viewerWidth - notePanelWidth - annotationTopLeft.x + notePanelLeftPadding + scrollLeft - annotWidthInPixels;\n    const rightHorizontalLineWidthRatio = 0.75;\n    setRightHorizontalLineWidth(lineWidth * rightHorizontalLineWidthRatio);\n    const noZoomRefPoint = annotation.getNoZoomReferencePoint();\n    const noZoomRefShiftX = (annotation.NoZoom && noZoomRefPoint.x) ? noZoomRefPoint.x * annotHeightInPixels : 0;\n    setLeftHorizontalLineWidth(lineWidth - rightHorizontalLineWidth - getAnnotationLineOffset() + noZoomRefShiftX);\n\n    setLeftHorizontalLineRight(notePanelWidth - notePanelLeftPadding + rightHorizontalLineWidth);\n\n    const noZoomRefShiftY = (annotation.NoZoom && noZoomRefPoint.y) ? noZoomRefPoint.y * annotHeightInPixels : 0;\n    setLeftHorizontalLineTop(annotationTopLeft.y + (annotHeightInPixels / 2) - scrollTop - noZoomRefShiftY);\n\n    const onPageNumberUpdated = () => {\n      dispatch(actions.closeElement(DataElements.ANNOTATION_NOTE_CONNECTOR_LINE));\n    };\n\n    core.addEventListener('pageNumberUpdated', onPageNumberUpdated, undefined, activeDocumentViewerKey);\n\n    return () => {\n      core.removeEventListener('pageNumberUpdated', onPageNumberUpdated, activeDocumentViewerKey);\n    };\n  }, [noteContainerRef, notePanelWidth, annotationBottomRight, annotationTopLeft, documentContainerWidth, documentContainerHeight, dispatch, activeDocumentViewerKey]);\n\n  if (lineIsOpen && (notePanelIsOpen || isCustomPanelOpen) && !isLineDisabled) {\n    const verticalHeight = Math.abs(rightHorizontalLineTop - leftHorizontalLineTop);\n    const horizontalLineHeight = 2;\n    // Add HorizontalLineHeight of 2px when annot is above note to prevent little gap between lines\n    const verticalTop = rightHorizontalLineTop > leftHorizontalLineTop ? leftHorizontalLineTop + horizontalLineHeight : rightHorizontalLineTop;\n\n    return (\n      <LineConnectorPortal>\n        <div className=\"horizontalLine\" style={{ width: rightHorizontalLineWidth, right: rightHorizontalLineRight, top: rightHorizontalLineTop }} />\n        <div className=\"verticalLine\" style={{ height: verticalHeight, top: verticalTop, right: rightHorizontalLineRight + rightHorizontalLineWidth }} />\n        <div className=\"horizontalLine\" style={{ width: leftHorizontalLineWidth, right: leftHorizontalLineRight, top: leftHorizontalLineTop }}>\n          <div className=\"arrowHead\" />\n        </div>\n      </LineConnectorPortal>);\n  }\n  return null;\n};\n\nexport default AnnotationNoteConnectorLine;\n", "import AnnotationNoteConnectorLine from './AnnotationNoteConnectorLine';\n\nexport default AnnotationNoteConnectorLine;", "import React, { useEffect, useRef, useContext, useState, useCallback } from 'react';\nimport classNames from 'classnames';\nimport PropTypes from 'prop-types';\nimport { useSelector, shallowEqual, useDispatch } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\n\nimport NoteContext from 'components/Note/Context';\nimport NoteContent from 'components/NoteContent';\nimport ReplyArea from 'components/Note/ReplyArea';\nimport NoteGroupSection from 'components/Note/NoteGroupSection';\nimport Button from 'components/Button';\n\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport core from 'core';\nimport AnnotationNoteConnectorLine from 'components/AnnotationNoteConnectorLine';\nimport useDidUpdate from 'hooks/useDidUpdate';\nimport DataElements from 'constants/dataElement';\nimport getRootNode from 'helpers/getRootNode';\nimport { mapAnnotationToKey, annotationMapKeys } from 'constants/map';\nimport { OfficeEditorEditMode, OFFICE_EDITOR_TRACKED_CHANGE_KEY } from 'constants/officeEditor';\n\nimport './Note.scss';\n\nconst propTypes = {\n  annotation: PropTypes.object.isRequired,\n  isMultiSelected: PropTypes.bool,\n  isMultiSelectMode: PropTypes.bool,\n  isInNotesPanel: PropTypes.bool,\n  handleMultiSelect: PropTypes.func,\n};\n\nlet currId = 0;\n\nconst Note = ({\n  annotation,\n  isMultiSelected,\n  isMultiSelectMode,\n  isInNotesPanel,\n  handleMultiSelect,\n  isCustomPanelOpen,\n  shouldHideConnectorLine,\n}) => {\n  const {\n    isSelected,\n    resize,\n    pendingEditTextMap,\n    isContentEditable,\n    isDocumentReadOnly,\n    isExpandedFromSearch,\n    // documentViewerKey,\n    setCurAnnotId,\n  } = useContext(NoteContext);\n  const containerRef = useRef();\n  const containerHeightRef = useRef();\n  const [isEditingMap, setIsEditingMap] = useState({});\n  const ids = useRef([]);\n  const dispatch = useDispatch();\n  const [t] = useTranslation();\n  const unreadReplyIdSet = new Set();\n\n  const [\n    noteTransformFunction,\n    customNoteSelectionFunction,\n    unreadAnnotationIdSet,\n    shouldExpandCommentThread,\n    isRightClickAnnotationPopupEnabled,\n    documentViewerKey,\n    isOfficeEditorMode,\n    officeEditorEditMode,\n  ] = useSelector(\n    (state) => [\n      selectors.getNoteTransformFunction(state),\n      selectors.getCustomNoteSelectionFunction(state),\n      selectors.getUnreadAnnotationIdSet(state),\n      selectors.isCommentThreadExpansionEnabled(state),\n      selectors.isRightClickAnnotationPopupEnabled(state),\n      selectors.getActiveDocumentViewerKey(state),\n      selectors.getIsOfficeEditorMode(state),\n      selectors.getOfficeEditorEditMode(state),\n    ],\n    shallowEqual,\n  );\n\n  const replies = annotation\n    .getReplies()\n    .sort((a, b) => a['DateCreated'] - b['DateCreated']);\n\n  replies.filter((r) => unreadAnnotationIdSet.has(r.Id)).forEach((r) => unreadReplyIdSet.add(r.Id));\n\n  useEffect(() => {\n    const annotationChangedListener = (annotations, action) => {\n      if (action === 'delete') {\n        annotations.forEach((annot) => {\n          if (unreadAnnotationIdSet.has(annot.Id)) {\n            dispatch(actions.setAnnotationReadState({ isRead: true, annotationId: annot.Id }));\n          }\n        });\n      }\n    };\n    core.addEventListener('annotationChanged', annotationChangedListener, undefined, documentViewerKey);\n\n    return () => {\n      core.removeEventListener('annotationChanged', annotationChangedListener, documentViewerKey);\n    };\n  }, [unreadAnnotationIdSet]);\n\n  useEffect(() => {\n    const prevHeight = containerHeightRef.current;\n    const currHeight = containerRef.current.getBoundingClientRect().height;\n    containerHeightRef.current = currHeight;\n\n    // have a prevHeight check here because we don't want to call resize on mount\n    // use Math.round because in some cases in IE11 these two numbers will differ in just 0.00001\n    // and we don't want call resize in this case\n    if (prevHeight && Math.round(prevHeight) !== Math.round(currHeight)) {\n      resize();\n    }\n  });\n\n  useEffect(() => {\n    if (noteTransformFunction) {\n      const notesPanelElement = getRootNode().querySelectorAll('.NotesPanel')[0];\n      ids.current.forEach((id) => {\n        const child = notesPanelElement.querySelector(`[data-webviewer-custom-element=${id}]`);\n        if (child) {\n          child.parentNode.removeChild(child);\n        }\n      });\n\n      ids.current = [];\n\n      const state = {\n        annotation,\n        isSelected,\n      };\n\n      noteTransformFunction(containerRef.current, state, (...params) => {\n        const element = document.createElement(...params);\n        const id = `custom-element-${currId}`;\n        currId++;\n        ids.current.push(id);\n        element.setAttribute('data-webviewer-custom-element', id);\n        element.addEventListener('mousedown', (e) => {\n          e.stopPropagation();\n        });\n\n        return element;\n      });\n    }\n  });\n\n  useEffect(() => {\n    // If this is not a new one, rebuild the isEditing map\n    const pendingText = pendingEditTextMap[annotation.Id];\n    if (pendingText !== '' && isContentEditable && !isDocumentReadOnly) {\n      setIsEditing(true, 0);\n    }\n  }, [isDocumentReadOnly, isContentEditable, setIsEditing, annotation, isMultiSelectMode]);\n\n  useDidUpdate(() => {\n    if (isDocumentReadOnly || !isContentEditable) {\n      setIsEditing(false, 0);\n    }\n  }, [isDocumentReadOnly, isContentEditable, setIsEditing]);\n\n  const handleNoteClick = async (e) => {\n    // stop bubbling up otherwise the note will be closed\n    // due to annotation deselection\n    e && e.stopPropagation();\n\n    if (isMultiSelectMode) {\n      handleMultiSelect(!isMultiSelected);\n      return;\n    }\n    if (unreadAnnotationIdSet.has(annotation.Id)) {\n      dispatch(actions.setAnnotationReadState({ isRead: true, annotationId: annotation.Id }));\n    }\n\n    customNoteSelectionFunction && customNoteSelectionFunction(annotation);\n    if (!isSelected) {\n      core.deselectAllAnnotations(documentViewerKey);\n\n      // Need this delay to ensure all other event listeners fire before we open the line\n      setTimeout(() => dispatch(actions.openElement(DataElements.ANNOTATION_NOTE_CONNECTOR_LINE)), 300);\n    }\n    if (isInNotesPanel && !(isOfficeEditorMode && officeEditorEditMode === OfficeEditorEditMode.PREVIEW)) {\n      core.selectAnnotation(annotation, documentViewerKey);\n      setCurAnnotId(annotation.Id);\n      core.jumpToAnnotation(annotation, documentViewerKey);\n      if (!isRightClickAnnotationPopupEnabled) {\n        dispatch(actions.openElement(DataElements.ANNOTATION_POPUP));\n      }\n      if (isOfficeEditorMode) {\n        const trackedChangeId = annotation.getCustomData(OFFICE_EDITOR_TRACKED_CHANGE_KEY);\n        await core.getOfficeEditor().moveCursorToTrackedChange(trackedChangeId);\n        core.getOfficeEditor().freezeMainCursor();\n      }\n    }\n  };\n\n  const hasUnreadReplies = unreadReplyIdSet.size > 0;\n\n  const noteClass = classNames({\n    Note: true,\n    expanded: isSelected,\n    'is-multi-selected': isMultiSelected,\n    unread: unreadAnnotationIdSet.has(annotation.Id) || hasUnreadReplies,\n    'disabled': isOfficeEditorMode && officeEditorEditMode === OfficeEditorEditMode.PREVIEW,\n  });\n\n  const repliesClass = classNames({\n    replies: true,\n    hidden: !isSelected,\n  });\n\n  useEffect(() => {\n    // Must also restore the isEdit for  any replies, in case someone was editing a\n    // reply when a comment was placed above\n    if (!isMultiSelectMode) {\n      replies.forEach((reply, index) => {\n        const pendingText = pendingEditTextMap[reply.Id];\n        if ((pendingText !== '' && typeof pendingText !== 'undefined') && isSelected) {\n          setIsEditing(true, 1 + index);\n        }\n      });\n    }\n  }, [isSelected, isMultiSelectMode]);\n\n  useEffect(() => {\n    if (isMultiSelectMode) {\n      setIsEditing(false, 0);\n    }\n  }, [isMultiSelectMode]);\n\n  const showReplyArea = !Object.values(isEditingMap).some((val) => val);\n\n  const handleReplyClicked = (reply) => {\n    // set clicked reply as read\n    if (unreadReplyIdSet.has(reply.Id)) {\n      dispatch(actions.setAnnotationReadState({ isRead: true, annotationId: reply.Id }));\n      core.getAnnotationManager(documentViewerKey).selectAnnotation(reply);\n    }\n  };\n\n  const markAllRepliesRead = () => {\n    // set all replies to read state if user starts to type in reply textarea\n    if (unreadReplyIdSet.size > 0) {\n      const repliesSetToRead = replies.filter((r) => unreadReplyIdSet.has(r.Id));\n      core.getAnnotationManager(documentViewerKey).selectAnnotations(repliesSetToRead);\n      repliesSetToRead.forEach((r) => dispatch(actions.setAnnotationReadState({ isRead: true, annotationId: r.Id })));\n    }\n  };\n\n  const setIsEditing = useCallback(\n    (isEditing, index) => {\n      setIsEditingMap((map) => ({\n        ...map,\n        [index]: isEditing,\n      }));\n    },\n    [setIsEditingMap],\n  );\n\n  const groupAnnotations = core.getGroupAnnotations(annotation, documentViewerKey);\n  const isGroup = groupAnnotations.length > 1;\n  const isTrackedChange = mapAnnotationToKey(annotation) === annotationMapKeys.TRACKED_CHANGE;\n  // apply unread reply style to replyArea if the last reply is unread\n  const lastReplyId = replies.length > 0 ? replies[replies.length - 1].Id : null;\n\n  return (\n    <div\n      ref={containerRef}\n      className={noteClass}\n      id={`note_${annotation.Id}`}\n    >\n      <Button\n        className='note-button'\n        onClick={(e) => handleNoteClick(e)}\n        ariaLabelledby={`note_${annotation.Id}`}\n        ariaCurrent={isSelected}\n        dataElement=\"expandNoteButton\"\n      />\n      <NoteContent\n        noteIndex={0}\n        annotation={annotation}\n        setIsEditing={setIsEditing}\n        handleNoteClick={handleNoteClick}\n        isEditing={isEditingMap[0]}\n        isNonReplyNoteRead={!unreadAnnotationIdSet.has(annotation.Id)}\n        isUnread={unreadAnnotationIdSet.has(annotation.Id) || hasUnreadReplies}\n        handleMultiSelect={(e) => {\n          setCurAnnotId(annotation.Id);\n          handleMultiSelect(e);\n        }}\n        isMultiSelected={isMultiSelected}\n        isMultiSelectMode={isMultiSelectMode}\n      />\n      {(isSelected || isExpandedFromSearch || shouldExpandCommentThread) && !isTrackedChange && (\n        <>\n          {replies.length > 0 && (\n            <div className={repliesClass}>\n              {hasUnreadReplies && (\n                <Button\n                  dataElement=\"markAllReadButton\"\n                  className=\"mark-all-read-button\"\n                  label={t('action.markAllRead')}\n                  onClick={markAllRepliesRead}\n                />\n              )}\n              {replies.map((reply, i) => (\n                <div className=\"reply\" id={`note_reply_${reply.Id}`} key={`note_reply_${reply.Id}`}>\n                  <NoteContent\n                    noteIndex={i + 1}\n                    key={reply.Id}\n                    annotation={reply}\n                    setIsEditing={setIsEditing}\n                    isEditing={isEditingMap[i + 1]}\n                    onReplyClicked={handleReplyClicked}\n                    isUnread={unreadAnnotationIdSet.has(reply.Id)}\n                    handleMultiSelect={handleMultiSelect}\n                    isMultiSelected={isMultiSelected}\n                    isMultiSelectMode={isMultiSelectMode}\n                    handleNoteClick={handleNoteClick}\n                  />\n                </div>\n              ))}\n            </div>\n          )}\n          {isGroup &&\n            <NoteGroupSection\n              groupAnnotations={groupAnnotations}\n              isMultiSelectMode={isMultiSelectMode}\n            />}\n          {showReplyArea && !isMultiSelectMode && (\n            <ReplyArea\n              isUnread={lastReplyId && unreadAnnotationIdSet.has(lastReplyId)}\n              onPendingReplyChange={markAllRepliesRead}\n              annotation={annotation}\n            />\n          )}\n        </>\n      )}\n      {isSelected && (isInNotesPanel || isCustomPanelOpen) && !shouldHideConnectorLine && (\n        <AnnotationNoteConnectorLine\n          annotation={annotation}\n          noteContainerRef={containerRef}\n          isCustomPanelOpen={isCustomPanelOpen}\n        />\n      )}\n    </div>\n  );\n};\n\nNote.propTypes = propTypes;\n\nexport default Note;\n", "import Note from './Note';\n\nexport default Note;\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./InlineCommentingPopup.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.InlineCommentingPopup{visibility:visible}.closed.InlineCommentingPopup{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.InlineCommentingPopup{position:absolute;z-index:70;display:flex;justify-content:center;align-items:center}.InlineCommentingPopup:empty{padding:0}.InlineCommentingPopup .buttons{display:flex}.InlineCommentingPopup .Button{margin:4px;width:32px;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Button{width:42px;height:42px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Button{width:42px;height:42px}}.InlineCommentingPopup .Button:hover{background:var(--popup-button-hover)}.InlineCommentingPopup .Button:hover:disabled{background:none}.InlineCommentingPopup .Button .Icon{width:18px;height:18px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Button .Icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Button .Icon{width:24px;height:24px}}.is-vertical.InlineCommentingPopup .Button.main-menu-button{width:100%;border-radius:0;justify-content:flex-start;padding-left:var(--padding-small);padding-right:var(--padding-small);margin:0 0 var(--padding-tiny) 0}.is-vertical.InlineCommentingPopup .Button.main-menu-button:first-child{margin-top:var(--padding-tiny)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .is-vertical.InlineCommentingPopup .Button.main-menu-button{width:100%;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .is-vertical.InlineCommentingPopup .Button.main-menu-button{width:100%;height:32px}}.is-vertical.InlineCommentingPopup .Button.main-menu-button .Icon{margin-right:10px}.is-vertical.InlineCommentingPopup .Button.main-menu-button span{white-space:nowrap}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.InlineCommentingPopup{border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background);align-items:flex-start}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup{position:fixed;left:0;bottom:0;z-index:100;flex-direction:column;justify-content:flex-end;width:100%;background:var(--modal-negative-space)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup{position:fixed;left:0;bottom:0;z-index:100;flex-direction:column;justify-content:flex-end;width:100%;background:var(--modal-negative-space)}}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup{overflow:auto;max-height:calc(100% - 100px)}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup{overflow:auto;max-height:calc(100% - 100px)}}.InlineCommentingPopup .inline-comment-container{display:flex;flex-direction:column}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .inline-comment-container{flex-basis:auto;width:100%;max-height:40%;background:var(--component-background);box-shadow:0 0 3px 0 var(--document-box-shadow);border-radius:4px 4px 0 0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .inline-comment-container{flex-basis:auto;width:100%;max-height:40%;background:var(--component-background);box-shadow:0 0 3px 0 var(--document-box-shadow);border-radius:4px 4px 0 0}}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .inline-comment-container{width:260px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .inline-comment-container{width:260px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .inline-comment-container.expanded{flex-grow:1;max-height:90%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .inline-comment-container.expanded{flex-grow:1;max-height:90%}}.InlineCommentingPopup .inline-comment-container .inline-comment-header{flex-grow:0;flex-shrink:0;display:flex;flex-direction:row;align-items:center}.InlineCommentingPopup .inline-comment-container .inline-comment-header .inline-comment-header-title{flex-grow:1;font-size:16px}.InlineCommentingPopup .inline-comment-container .inline-comment-header .Button{margin:4px}.InlineCommentingPopup .Note{border-radius:0;background:none;margin:0;cursor:default}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Note{flex-grow:1;display:flex;flex-direction:column;overflow:auto;box-shadow:0 0 3px 0 var(--document-box-shadow)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Note{flex-grow:1;display:flex;flex-direction:column;overflow:auto;box-shadow:0 0 3px 0 var(--document-box-shadow)}}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Note{box-shadow:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Note{box-shadow:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Note>div:not(:nth-last-child(2)){flex-grow:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Note>div:not(:nth-last-child(2)){flex-grow:0}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Note>div:nth-last-child(2){flex-grow:1}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Note>div:nth-last-child(2){flex-grow:1}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Note>.NoteContent:only-child{flex-grow:1}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Note>.NoteContent:only-child{flex-grow:1}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Note>.NoteContent:only-child .edit-content{flex-grow:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Note>.NoteContent:only-child .edit-content{flex-grow:0}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .NoteHeader{flex-grow:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .NoteHeader{flex-grow:0}}.InlineCommentingPopup .NoteContent .edit-content{margin-top:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .ql-container,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .ql-editor,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .quill{font-size:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .ql-container,.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .ql-editor,.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .quill{font-size:16px}}.InlineCommentingPopup .Button,.InlineCommentingPopup .Button.add-attachment,.InlineCommentingPopup .Button.reply-button{margin:0}.InlineCommentingPopup .Button.add-attachment .Icon,.InlineCommentingPopup .Button.reply-button .Icon{width:22px;height:22px}.InlineCommentingPopup .Button.add-attachment{width:24px;height:24px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Button.add-attachment{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Button.add-attachment{width:24px;height:24px}}.InlineCommentingPopup .Button.note-popup-toggle-trigger,.InlineCommentingPopup .Button.reply-button{width:28px;height:28px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Button.note-popup-toggle-trigger,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InlineCommentingPopup .Button.reply-button{width:28px;height:28px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Button.note-popup-toggle-trigger,.App.is-web-component:not(.is-in-desktop-only-mode) .InlineCommentingPopup .Button.reply-button{width:28px;height:28px}}.sb-show-main .InlineCommentingPopup .quill.comment-textarea{padding:0}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState } from 'react';\nimport Draggable from 'react-draggable';\nimport classNames from 'classnames';\nimport PropTypes from 'prop-types';\nimport { useTranslation } from 'react-i18next';\n\nimport NoteContext from 'components/Note/Context';\nimport Note from 'components/Note';\nimport ReplyAttachmentPicker from 'components/NotesPanel/ReplyAttachmentPicker';\nimport Button from 'components/Button';\n\nimport DataElements from 'src/constants/dataElement';\n\nimport { mapAnnotationToKey, annotationMapKeys } from 'constants/map';\n\nimport './InlineCommentingPopup.scss';\n\nconst propTypes = {\n  isMobile: PropTypes.bool,\n  isUndraggable: PropTypes.bool,\n  isNotesPanelClosed: PropTypes.bool,\n  popupRef: PropTypes.any,\n  position: PropTypes.object,\n  closeAndReset: PropTypes.func,\n  commentingAnnotation: PropTypes.object,\n  contextValue: PropTypes.object,\n  annotationForAttachment: PropTypes.string,\n  addAttachments: PropTypes.func,\n};\n\nconst InlineCommentingPopup = ({\n  isMobile,\n  isUndraggable,\n  isNotesPanelClosed,\n  popupRef,\n  position,\n  closeAndReset,\n  commentingAnnotation,\n  contextValue,\n  annotationForAttachment,\n  addAttachments,\n}) => {\n  const [t] = useTranslation();\n  const [isExpanded, setExpanded] = useState(false);\n\n  const isTrackedChange = mapAnnotationToKey(commentingAnnotation) === annotationMapKeys.TRACKED_CHANGE;\n\n  const inlineCommentPopup = (\n    <div\n      className={classNames({\n        Popup: true,\n        InlineCommentingPopup: true,\n        open: isNotesPanelClosed,\n        trackedChangePopup: isTrackedChange,\n      })}\n      ref={popupRef}\n      data-element={DataElements.INLINE_COMMENT_POPUP}\n      style={{ ...position }}\n      onMouseMove={(e) => {\n        e.stopPropagation();\n      }}\n      onMouseDown={(e) => {\n        if (isMobile) {\n          e.stopPropagation();\n          closeAndReset();\n        }\n      }}\n      onKeyDown={(e) => {\n        if (e.key === 'Escape') {\n          closeAndReset();\n        }\n      }}\n    >\n      <div\n        className={classNames({\n          'inline-comment-container': true,\n          'expanded': isExpanded,\n        })}\n        onMouseDown={(e) => {\n          if (isMobile) {\n            e.stopPropagation();\n          }\n        }}\n      >\n        {isMobile && (\n          <div className='inline-comment-header'>\n            <Button\n              img={isExpanded ? 'icon-chevron-down' : 'icon-chevron-up'}\n              className=\"expand-arrow\"\n              dataElement={DataElements.INLINE_COMMENT_POPUP_EXPAND_BUTTON}\n              onClick={() => setExpanded(!isExpanded)}\n            />\n            <span className='inline-comment-header-title'>{t('action.comment')}</span>\n            <Button\n              img={'icon-close'}\n              dataElement={DataElements.INLINE_COMMENT_POPUP_CLOSE_BUTTON}\n              onClick={closeAndReset}\n            />\n          </div>\n        )}\n        <NoteContext.Provider value={contextValue}>\n          <Note\n            annotation={commentingAnnotation}\n            isMultiSelected={false}\n            isMultiSelectMode={false}\n            handleMultiSelect={() => { }}\n          />\n          <ReplyAttachmentPicker\n            annotationId={annotationForAttachment}\n            addAttachments={addAttachments}\n          />\n        </NoteContext.Provider>\n      </div>\n    </div>\n  );\n\n  return isUndraggable || isTrackedChange ? (\n    inlineCommentPopup\n  ) : (\n    <Draggable cancel=\".Button, .cell, svg, select, button, input, .quill, .note-text-preview\">{inlineCommentPopup}</Draggable>\n  );\n};\n\nInlineCommentingPopup.propTypes = propTypes;\n\nexport default InlineCommentingPopup;\n", "import React, { useState, useRef, useCallback, useLayoutEffect } from 'react';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport InlineCommentingPopup from './InlineCommentingPopup';\nimport core from 'core';\nimport { getAnnotationPopupPositionBasedOn as getPopupPosition } from 'helpers/getPopupPosition';\nimport { getOpenedWarningModal, getOpenedColorPicker, getDatePicker } from 'helpers/getElements';\nimport useOnClickOutside from 'hooks/useOnClickOutside';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport { isMobile as isPhone, isIE, isMobileDevice } from 'helpers/device';\nimport DataElements from 'constants/dataElement';\nimport getRootNode from 'helpers/getRootNode';\nimport debounce from 'lodash/debounce';\nimport PropTypes from 'prop-types';\nimport { workerTypes } from 'constants/types';\n\nconst propTypes = {\n  annotation: PropTypes.object,\n  closeAndReset: PropTypes.func,\n};\n\nconst InlineCommentingPopupContainer = ({ annotation, closeAndReset }) => {\n  const [\n    isNotesPanelOpen,\n    notesInLeftPanel,\n    isLeftPanelOpen,\n    activeLeftPanel,\n    showAnnotationNumbering,\n    sortStrategy,\n    isDocumentReadOnly,\n    activeDocumentViewerKey,\n  ] = useSelector(\n    (state) => [\n      selectors.isElementOpen(state, DataElements.NOTES_PANEL),\n      selectors.getNotesInLeftPanel(state),\n      selectors.isElementOpen(state, DataElements.LEFT_PANEL),\n      selectors.getActiveLeftPanel(state),\n      selectors.isAnnotationNumberingEnabled(state),\n      selectors.getSortStrategy(state),\n      selectors.isDocumentReadOnly(state),\n      selectors.getActiveDocumentViewerKey(state),\n    ],\n    shallowEqual,\n  );\n  const dispatch = useDispatch();\n  const [position, setPosition] = useState({ left: 0, top: 0 });\n  const popupRef = useRef();\n  // on tablet, the behaviour will be like on desktop, except being draggable\n  const isMobile = isPhone();\n  const isUndraggable = isMobile || !!isMobileDevice || isIE;\n  const isNotesPanelOpenOrActive = isNotesPanelOpen || (notesInLeftPanel && isLeftPanelOpen && activeLeftPanel === 'notesPanel');\n  const sixtyFramesPerSecondIncrement = 16;\n\n  useOnClickOutside(popupRef, (e) => {\n    const notesPanel = getRootNode().querySelector('[data-element=\"notesPanel\"]');\n    const clickedInNotesPanel = notesPanel?.contains(e.target);\n    const noteStateFlyout = getRootNode().querySelector(`[data-element=\"noteStateFlyout-${annotation.Id}\"]`);\n    const clickedInNoteStateFlyout = noteStateFlyout?.contains(e.target);\n    const datePicker = getDatePicker();\n    const warningModal = getOpenedWarningModal();\n    const colorPicker = getOpenedColorPicker();\n\n    // the notes panel has mousedown handlers to handle the opening/closing states of this component\n    // we don't want this handler to run when clicked in the notes panel otherwise the opening/closing states may mess up\n    // for example: click on a note will call core.selectAnnotation which triggers the annotationSelected event\n    // and opens this component. If we don't exclude the notes panel this handler will run and close it after\n    if (!clickedInNotesPanel && !clickedInNoteStateFlyout && !warningModal && !colorPicker && !datePicker) {\n      dispatch(actions.closeElement(DataElements.INLINE_COMMENT_POPUP));\n    }\n  });\n\n  const isNotesPanelClosed = !isNotesPanelOpenOrActive;\n\n  const setPopupPosition = () => {\n    if (isNotesPanelClosed && popupRef.current && !isMobile) {\n      setPosition(getPopupPosition(annotation, popupRef, activeDocumentViewerKey));\n    }\n  };\n\n  useLayoutEffect(() => {\n    setPopupPosition();\n  }, [activeDocumentViewerKey, annotation]);\n\n  const handleResize = debounce(() => {\n    setPopupPosition();\n  }, sixtyFramesPerSecondIncrement, { 'trailing': true, 'leading': false });\n\n  useLayoutEffect(() => {\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      window.removeEventListener('resize', handleResize);\n    };\n  }, []);\n\n  // TO-DO refactor: Lines 189-239 was copied from NotesPanel 228-275\n  const [pendingAttachmentMap, setPendingAttachmentMap] = useState({});\n  const addAttachments = (annotationID, attachments) => {\n    setPendingAttachmentMap((map) => ({\n      ...map,\n      [annotationID]: [...(map[annotationID] || []), ...attachments]\n    }));\n  };\n\n  const [annotationForAttachment, setAnnotationForAttachment] = useState(undefined);\n\n  const [pendingEditTextMap, setPendingEditTextMap] = useState({});\n  const setPendingEditText = useCallback(\n    (pendingText, annotationID) => {\n      setPendingEditTextMap((map) => ({\n        ...map,\n        [annotationID]: pendingText,\n      }));\n    },\n    [setPendingEditTextMap],\n  );\n\n  const [pendingReplyMap, setPendingReplyMap] = useState({});\n  const setPendingReply = useCallback(\n    (pendingReply, annotationID) => {\n      setPendingReplyMap((map) => ({\n        ...map,\n        [annotationID]: pendingReply,\n      }));\n    },\n    [setPendingReplyMap],\n  );\n  const clearAttachments = (annotationID) => {\n    setPendingAttachmentMap((map) => ({\n      ...map,\n      [annotationID]: []\n    }));\n  };\n  const deleteAttachment = (annotationID, attachment) => {\n    const attachmentList = pendingAttachmentMap[annotationID];\n    if (attachmentList?.length > 0) {\n      const index = attachmentList.indexOf(attachment);\n      if (index > -1) {\n        attachmentList.splice(index, 1);\n        setPendingAttachmentMap((map) => ({\n          ...map,\n          [annotationID]: [...attachmentList]\n        }));\n      }\n    }\n  };\n\n  const contextValue = {\n    searchInput: '',\n    resize: () => {\n      if (core.getDocument()?.getType() === workerTypes.OFFICE_EDITOR) {\n        setPosition(getPopupPosition(annotation, popupRef, activeDocumentViewerKey));\n      }\n    },\n    isSelected: true,\n    isContentEditable: core.canModifyContents(annotation) && !annotation.getContents(),\n    pendingEditTextMap,\n    setPendingEditText,\n    pendingReplyMap,\n    setPendingReply,\n    isDocumentReadOnly,\n    onTopNoteContentClicked: () => { },\n    isExpandedFromSearch: false,\n    scrollToSelectedAnnot: false,\n    sortStrategy,\n    showAnnotationNumbering,\n    setCurAnnotId: setAnnotationForAttachment,\n    pendingAttachmentMap,\n    addAttachments,\n    clearAttachments,\n    deleteAttachment,\n  };\n\n  return (\n    <InlineCommentingPopup\n      isMobile={isMobile}\n      isUndraggable={isUndraggable}\n      isNotesPanelClosed={isNotesPanelClosed}\n      popupRef={popupRef}\n      position={position}\n      closeAndReset={closeAndReset}\n      commentingAnnotation={annotation}\n      contextValue={contextValue}\n      annotationForAttachment={annotationForAttachment}\n      addAttachments={addAttachments}\n    />\n  );\n};\n\nInlineCommentingPopupContainer.propTypes = propTypes;\n\nexport default InlineCommentingPopupContainer;\n", "import InlineCommentingPopupContainer from './InlineCommentingPopupContainer';\n\nexport default InlineCommentingPopupContainer;"], "sourceRoot": ""}