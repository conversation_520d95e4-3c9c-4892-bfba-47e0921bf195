{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/it.js"], "names": ["module", "exports", "e", "t", "default", "o", "n", "name", "weekdays", "split", "weekdaysShort", "weekdaysMin", "months", "weekStart", "monthsShort", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "ordinal", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,2DAA2DC,MAAM,KAAKC,cAAc,8BAA8BD,MAAM,KAAKE,YAAY,uBAAuBF,MAAM,KAAKG,OAAO,gGAAgGH,MAAM,KAAKI,UAAU,EAAEC,YAAY,kDAAkDL,MAAM,KAAKM,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,cAAcC,IAAI,oBAAoBC,KAAK,0BAA0BC,aAAa,CAACC,OAAO,SAASC,KAAK,QAAQC,EAAE,kBAAkBC,EAAE,YAAYC,GAAG,YAAYC,EAAE,UAAUC,GAAG,SAASC,EAAE,YAAYC,GAAG,YAAYC,EAAE,UAAUC,GAAG,UAAUC,EAAE,UAAUC,GAAG,WAAWC,QAAQ,SAASlC,GAAG,OAAOA,EAAE,MAAM,OAAOC,EAAEC,QAAQiC,OAAO/B,EAAE,MAAK,GAAIA,EAA9hCD,CAAE,EAAQ", "file": "chunks/chunk.171.js", "sourcesContent": ["!function(e,o){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=o(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],o):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_it=o(e.dayjs)}(this,(function(e){\"use strict\";function o(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=o(e),n={name:\"it\",weekdays:\"domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato\".split(\"_\"),weekdaysShort:\"dom_lun_mar_mer_gio_ven_sab\".split(\"_\"),weekdaysMin:\"do_lu_ma_me_gi_ve_sa\".split(\"_\"),months:\"gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre\".split(\"_\"),weekStart:1,monthsShort:\"gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic\".split(\"_\"),formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},relativeTime:{future:\"tra %s\",past:\"%s fa\",s:\"qualche secondo\",m:\"un minuto\",mm:\"%d minuti\",h:\"un' ora\",hh:\"%d ore\",d:\"un giorno\",dd:\"%d giorni\",M:\"un mese\",MM:\"%d mesi\",y:\"un anno\",yy:\"%d anni\"},ordinal:function(e){return e+\"º\"}};return t.default.locale(n,null,!0),n}));"], "sourceRoot": ""}