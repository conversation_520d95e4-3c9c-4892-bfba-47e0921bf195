{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/hu.js"], "names": ["module", "exports", "e", "t", "default", "n", "r", "name", "weekdays", "split", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "ordinal", "weekStart", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,sDAAsDC,MAAM,KAAKC,cAAc,gCAAgCD,MAAM,KAAKE,YAAY,qBAAqBF,MAAM,KAAKG,OAAO,oGAAoGH,MAAM,KAAKI,YAAY,qDAAqDJ,MAAM,KAAKK,QAAQ,SAASZ,GAAG,OAAOA,EAAE,KAAKa,UAAU,EAAEC,aAAa,CAACC,OAAO,WAAWC,KAAK,KAAKC,EAAE,SAASjB,EAAEG,EAAEF,EAAEG,GAAG,MAAM,oBAAoBA,GAAGD,EAAE,GAAG,MAAMe,EAAE,SAASlB,EAAEG,EAAEF,EAAEG,GAAG,MAAM,YAAYA,GAAGD,EAAE,GAAG,MAAMgB,GAAG,SAASnB,EAAEG,EAAEF,EAAEG,GAAG,OAAOJ,EAAE,SAASI,GAAGD,EAAE,GAAG,MAAMiB,EAAE,SAASpB,EAAEG,EAAEF,EAAEG,GAAG,MAAM,QAAQA,GAAGD,EAAE,MAAM,UAAUkB,GAAG,SAASrB,EAAEG,EAAEF,EAAEG,GAAG,OAAOJ,EAAE,KAAKI,GAAGD,EAAE,MAAM,UAAUmB,EAAE,SAAStB,EAAEG,EAAEF,EAAEG,GAAG,MAAM,QAAQA,GAAGD,EAAE,MAAM,UAAUoB,GAAG,SAASvB,EAAEG,EAAEF,EAAEG,GAAG,OAAOJ,EAAE,KAAKI,GAAGD,EAAE,MAAM,UAAUqB,EAAE,SAASxB,EAAEG,EAAEF,EAAEG,GAAG,MAAM,QAAQA,GAAGD,EAAE,QAAQ,YAAYsB,GAAG,SAASzB,EAAEG,EAAEF,EAAEG,GAAG,OAAOJ,EAAE,KAAKI,GAAGD,EAAE,QAAQ,YAAYuB,EAAE,SAAS1B,EAAEG,EAAEF,EAAEG,GAAG,MAAM,QAAQA,GAAGD,EAAE,KAAK,QAAQwB,GAAG,SAAS3B,EAAEG,EAAEF,EAAEG,GAAG,OAAOJ,EAAE,KAAKI,GAAGD,EAAE,KAAK,SAASyB,QAAQ,CAACC,GAAG,OAAOC,IAAI,UAAUC,EAAE,cAAcC,GAAG,gBAAgBC,IAAI,qBAAqBC,KAAK,6BAA6B,OAAOjC,EAAEC,QAAQiC,OAAO/B,EAAE,MAAK,GAAIA,EAA9+CD,CAAE,EAAQ", "file": "chunks/chunk.166.js", "sourcesContent": ["!function(e,n){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=n(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],n):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_hu=n(e.dayjs)}(this,(function(e){\"use strict\";function n(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=n(e),r={name:\"hu\",weekdays:\"vasárnap_hétfő_kedd_szerda_csütörtök_péntek_szombat\".split(\"_\"),weekdaysShort:\"vas_hét_kedd_sze_csüt_pén_szo\".split(\"_\"),weekdaysMin:\"v_h_k_sze_cs_p_szo\".split(\"_\"),months:\"janu<PERSON>r_febru<PERSON>r_márc<PERSON>_április_május_június_július_augusztus_szeptember_október_november_december\".split(\"_\"),monthsShort:\"jan_feb_márc_ápr_máj_jún_júl_aug_szept_okt_nov_dec\".split(\"_\"),ordinal:function(e){return e+\".\"},weekStart:1,relativeTime:{future:\"%s múlva\",past:\"%s\",s:function(e,n,t,r){return\"néhány másodperc\"+(r||n?\"\":\"e\")},m:function(e,n,t,r){return\"egy perc\"+(r||n?\"\":\"e\")},mm:function(e,n,t,r){return e+\" perc\"+(r||n?\"\":\"e\")},h:function(e,n,t,r){return\"egy \"+(r||n?\"óra\":\"órája\")},hh:function(e,n,t,r){return e+\" \"+(r||n?\"óra\":\"órája\")},d:function(e,n,t,r){return\"egy \"+(r||n?\"nap\":\"napja\")},dd:function(e,n,t,r){return e+\" \"+(r||n?\"nap\":\"napja\")},M:function(e,n,t,r){return\"egy \"+(r||n?\"hónap\":\"hónapja\")},MM:function(e,n,t,r){return e+\" \"+(r||n?\"hónap\":\"hónapja\")},y:function(e,n,t,r){return\"egy \"+(r||n?\"év\":\"éve\")},yy:function(e,n,t,r){return e+\" \"+(r||n?\"év\":\"éve\")}},formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"YYYY.MM.DD.\",LL:\"YYYY. MMMM D.\",LLL:\"YYYY. MMMM D. H:mm\",LLLL:\"YYYY. MMMM D., dddd H:mm\"}};return t.default.locale(r,null,!0),r}));"], "sourceRoot": ""}