{"version": 3, "sources": ["webpack:///./src/ui/node_modules/react-virtualized/dist/es/vendor/detectElementResize.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Grid/utils/calculateSizeAndPositionDataAndUpdateScrollOffset.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Grid/utils/CellSizeAndPositionManager.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Grid/utils/maxElementSize.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Grid/utils/ScalingCellSizeAndPositionManager.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/utils/createCallbackMemoizer.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Grid/utils/updateScrollIndexHelper.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/utils/animationFrame.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Grid/Grid.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/utils/requestAnimationTimeout.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Grid/defaultOverscanIndicesGetter.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Grid/defaultCellRangeRenderer.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Grid/accessibilityOverscanIndicesGetter.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/ArrowKeyStepper/types.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/ArrowKeyStepper/ArrowKeyStepper.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/AutoSizer/AutoSizer.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/CellMeasurer/CellMeasurer.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/CellMeasurer/CellMeasurerCache.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Collection/CollectionView.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Collection/Section.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Collection/SectionManager.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/utils/getUpdatedOffsetForIndex.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Collection/Collection.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Collection/utils/calculateSizeAndPositionData.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Collection/index.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/ColumnSizer/ColumnSizer.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/ColumnSizer/index.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/InfiniteLoader/InfiniteLoader.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/InfiniteLoader/index.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/List/List.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/vendor/binarySearchBounds.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/vendor/intervalTree.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Masonry/PositionCache.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Masonry/Masonry.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Masonry/index.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/MultiGrid/CellMeasurerCacheDecorator.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/MultiGrid/MultiGrid.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/ScrollSync/ScrollSync.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Table/defaultHeaderRowRenderer.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Table/SortDirection.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Table/SortIndicator.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Table/defaultHeaderRenderer.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Table/defaultRowRenderer.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Table/Column.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Table/Table.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Table/defaultCellDataGetter.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Table/defaultCellRenderer.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/Table/index.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/WindowScroller/utils/onScroll.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/WindowScroller/utils/dimensions.js", "webpack:///./src/ui/node_modules/react-virtualized/dist/es/WindowScroller/WindowScroller.js"], "names": ["createDetectElementResize", "nonce", "hostWindow", "_window", "cancel", "raf", "attachEvent", "window", "self", "global", "document", "requestFrame", "requestAnimationFrame", "mozRequestAnimationFrame", "webkitRequestAnimationFrame", "fn", "setTimeout", "cancelFrame", "cancelAnimationFrame", "mozCancelAnimationFrame", "webkitCancelAnimationFrame", "clearTimeout", "id", "resetTriggers", "element", "triggers", "__resizeTriggers__", "expand", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "contract", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "expand<PERSON><PERSON>d", "scrollLeft", "scrollWidth", "scrollTop", "scrollHeight", "style", "width", "offsetWidth", "height", "offsetHeight", "scrollListener", "e", "target", "className", "indexOf", "this", "__resizeRAF__", "__resizeLast__", "checkTriggers", "__resizeListeners__", "for<PERSON>ach", "call", "animation", "keyframeprefix", "animationstartevent", "domPrefixes", "split", "startEvents", "elm", "createElement", "undefined", "animationName", "i", "length", "toLowerCase", "animationKeyframes", "animationStyle", "addResizeListener", "doc", "ownerDocument", "elementStyle", "getComputedStyle", "position", "getElementById", "css", "head", "getElementsByTagName", "type", "setAttribute", "styleSheet", "cssText", "append<PERSON><PERSON><PERSON>", "createTextNode", "createStyles", "resizeTriggersHtml", "trustedTypes", "staticPolicy", "createPolicy", "createHTML", "innerHTML", "addEventListener", "__animationListener__", "push", "removeResizeListener", "detachEvent", "splice", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "calculateSizeAndPositionDataAndUpdateScrollOffset", "_ref", "cellCount", "cellSize", "computeMetadataCallback", "computeMetadataCallbackProps", "nextCellsCount", "nextCellSize", "nextScrollToIndex", "scrollToIndex", "updateScrollOffsetForScrollToIndex", "CellSizeAndPositionManager", "cellSizeGetter", "estimatedCellSize", "_cellSizeGetter", "_cellCount", "_estimatedCellSize", "key", "value", "_ref2", "_lastMeasuredIndex", "index", "Error", "concat", "lastMeasuredCellSizeAndPosition", "getSizeAndPositionOfLastMeasuredCell", "offset", "size", "isNaN", "_cellSizeAndPositionData", "_lastBatchedIndex", "_ref3", "_ref3$align", "align", "containerSize", "currentOffset", "targetIndex", "idealOffset", "datum", "getSizeAndPositionOfCell", "maxOffset", "minOffset", "Math", "max", "min", "totalSize", "getTotalSize", "params", "start", "_findNearestCell", "stop", "high", "low", "middle", "floor", "interval", "_binarySearch", "lastMeasuredIndex", "_exponentialSearch", "getMaxElementSize", "chrome", "ScalingCellSizeAndPositionManager", "_ref$maxScrollSize", "maxScrollSize", "_cellSizeAndPositionManager", "_maxScrollSize", "configure", "getCellCount", "getEstimatedCellSize", "getLastMeasuredIndex", "safeTotalSize", "offsetPercentage", "_getOffsetPercentage", "round", "_safeOffsetToOffset", "getUpdatedOffsetForIndex", "_offsetToSafeOffset", "_ref4", "getVisibleCellRange", "resetCell", "_ref5", "_ref6", "_ref7", "createCallbackMemoizer", "requireAllKeys", "arguments", "cachedIndices", "callback", "indices", "keys", "Object", "allInitialized", "every", "Array", "isArray", "indexChanged", "some", "cachedValue", "join", "updateScrollIndexHelper", "cellSizeAndPositionManager", "previousCellsCount", "previousCellSize", "previousScrollToAlignment", "previousScrollToIndex", "previousSize", "scrollOffset", "scrollToAlignment", "sizeJustIncreasedFromZero", "updateScrollIndexCallback", "hasScrollToIndex", "win", "_class", "_temp", "request", "oRequestAnimationFrame", "msRequestAnimationFrame", "oCancelAnimationFrame", "msCancelAnimationFrame", "caf", "frame", "delay", "Promise", "resolve", "then", "Date", "now", "timeout", "ownKeys", "object", "enumerableOnly", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "apply", "_objectSpread", "source", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "SCROLL_POSITION_CHANGE_REASONS", "_React$PureComponent", "Grid", "props", "_this", "_disablePointerEventsTimeoutId", "setState", "isScrolling", "needToResetStyleCache", "onSectionRendered", "_onGridRenderedMemoizer", "columnOverscanStartIndex", "_columnStartIndex", "columnOverscanStopIndex", "_columnStopIndex", "columnStartIndex", "_renderedColumnStartIndex", "columnStopIndex", "_renderedColumnStopIndex", "rowOverscanStartIndex", "_rowStartIndex", "rowOverscanStopIndex", "_rowStopIndex", "rowStartIndex", "_renderedRowStartIndex", "rowStopIndex", "_renderedRowStopIndex", "ref", "_scrollingContainer", "event", "handleScrollEvent", "columnSizeAndPositionManager", "columnCount", "_wrapSizeGetter", "columnWidth", "_getEstimatedColumnSize", "rowSizeAndPositionManager", "rowCount", "rowHeight", "_getEstimatedRowSize", "state", "instanceProps", "prevColumnWidth", "prevRowHeight", "prevColumnCount", "prevRowCount", "prevIsScrolling", "prevScrollToColumn", "scrollToColumn", "prevScrollToRow", "scrollToRow", "scrollbarSize", "scrollbarSizeMeasured", "scrollDirectionHorizontal", "scrollDirectionVertical", "scrollPositionChangeReason", "_initialScrollTop", "_getCalculatedScrollTop", "_initialScrollLeft", "_getCalculatedScrollLeft", "_ref$alignment", "alignment", "_ref$columnIndex", "columnIndex", "_ref$rowIndex", "rowIndex", "offsetProps", "_ref2$scrollLeft", "scrollLeftParam", "_ref2$scrollTop", "scrollTopParam", "_debounceScrollEnded", "_this$props", "autoHeight", "autoWidth", "totalRowsHeight", "totalColumnsWidth", "newState", "_invokeOnScrollMemoizer", "_deferredInvalidateColumnIndex", "_deferredInvalidateRowIndex", "_this$props2", "_ref4$columnIndex", "_ref4$rowIndex", "_this$props3", "_recomputeScrollLeftFlag", "_recomputeScrollTopFlag", "_styleCache", "_cellCache", "forceUpdate", "_updateScrollLeftForScrollToColumn", "_updateScrollTopForScrollToRow", "_this$props4", "getScrollbarSize", "_handleInvalidatedGridSize", "prevState", "stateUpdate", "_getScrollToPositionStateUpdate", "sizeIsBiggerThanZero", "_invokeOnGridRenderedHelper", "_maybeCallOnScrollbarPresenceChange", "prevProps", "_this2", "_this$props5", "_this$state", "columnOrRowCountJustIncreasedFromZero", "_this$props6", "autoContainerWidth", "containerProps", "containerRole", "containerStyle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "role", "tabIndex", "_this$state2", "_isScrolling", "gridStyle", "boxSizing", "direction", "WebkitOverflowScrolling", "<PERSON><PERSON><PERSON><PERSON>", "_resetStyleCache", "_calculate<PERSON><PERSON>drenToRender", "verticalScrollBarSize", "horizontalScrollBarSize", "_horizontalScrollBarSize", "_verticalScrollBarSize", "_scrollbarPresenceChanged", "overflowX", "overflowY", "childrenToDisplay", "_childrenToDisplay", "showNoContent<PERSON><PERSON><PERSON>", "_setScrollingContainerRef", "onScroll", "_onScroll", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflow", "pointerEvents", "cell<PERSON><PERSON><PERSON>", "cellRange<PERSON><PERSON><PERSON>", "deferredMeasurementCache", "overscanColumnCount", "overscanIndicesGetter", "overscanRowCount", "isScrollingOptOut", "visibleColumnIndices", "visibleRowIndices", "horizontalOffsetAdjustment", "getOffsetAdjustment", "verticalOffsetAdjustment", "overscanColumnIndices", "overscanCellsCount", "scrollDirection", "startIndex", "stopIndex", "overscanRowIndices", "overscanStartIndex", "overscanStopIndex", "hasFixedHeight", "has", "hasFixedWidth", "cellCache", "parent", "styleCache", "scrollingResetTimeInterval", "_debounceScrollEndedCallback", "recomputeGridSize", "_this3", "_onScrollMemoizer", "_this3$props", "clientHeight", "clientWidth", "hasOwnProperty", "Boolean", "onScrollbarPresenceChange", "horizontal", "vertical", "_ref8", "_getScrollLeftForScrollToColumnStateUpdate", "_getScrollTopForScrollToRowStateUpdate", "nextProps", "assign", "maybeStateA", "maybeStateB", "estimatedColumnSize", "estimatedRowSize", "_ref9", "finalColumn", "scrollBarSize", "calculatedScrollLeft", "finalRow", "calculatedScrollTop", "<PERSON><PERSON><PERSON><PERSON>", "areOffsetsAdjusted", "canCacheStyle", "rowDatum", "columnDatum", "isVisible", "left", "top", "cellRendererParams", "renderedCell", "ArrowKeyStepper", "_getPrototypeOf2", "_len", "args", "_key", "disabled", "mode", "_this$_getScrollState", "_getScrollState", "scrollToColumnPrevious", "scrollToRowPrevious", "_this$_getScrollState2", "preventDefault", "_updateScrollState", "children", "_this$_getScrollState3", "onKeyDown", "_onKeyDown", "_onSectionRendered", "isControlled", "onScrollToChange", "_React$Component", "AutoSizer", "defaultHeight", "defaultWidth", "disableHeight", "disable<PERSON><PERSON><PERSON>", "onResize", "_parentNode", "paddingLeft", "parseInt", "paddingRight", "paddingTop", "paddingBottom", "newHeight", "newWidth", "autoSizer", "_autoSizer", "parentNode", "defaultView", "HTMLElement", "_detectElementResize", "_onResize", "outerStyle", "childP<PERSON>ms", "_setRef", "CellMeasurer", "cache", "_this$props$columnInd", "_this$props$rowIndex", "_this$_getCellMeasure", "_getCellMeasurements", "getHeight", "getWidth", "set", "Element", "console", "warn", "_child", "_maybeMeasureCell", "measure", "_measure", "registerChild", "_register<PERSON>hild", "node", "styleWidth", "styleHeight", "ceil", "_this$props2$columnIn", "_this$props2$rowIndex", "_this$_getCellMeasure2", "invalidateCellSizeAfterRender", "CellMeasurerCache", "_keyMapper", "_columnWidthCache", "_defaultWidth", "_rowHeightCache", "_defaultHeight", "fixedHeight", "fixedWidth", "keyMapper", "minHeight", "min<PERSON><PERSON><PERSON>", "_hasFixedHeight", "_hasFixedWidth", "_minHeight", "_minWidth", "defaultKeyMapper", "_cellHeightCache", "_cellWidthCache", "_updateCachedColumnAndRowSizes", "_rowCount", "_columnCount", "_key2", "column<PERSON>ey", "_i", "<PERSON><PERSON><PERSON>", "get", "CollectionView", "cellLayoutManager", "_onSectionRenderedMemoizer", "getLastRenderedIndices", "scrollToCell", "scrollPosition", "getScrollPositionForCell", "cellIndex", "_setScrollPosition", "_enablePointerEventsAfterDelay", "isScrollingChange", "_scrollbarSize", "_cellLayoutManager$ge", "totalHeight", "totalWidth", "cancelable", "_scrollbarSizeMeasured", "_calculateSizeAndPositionDataOnNextUpdate", "_updateScrollPositionForScrollToCell", "_invokeOnSectionRenderedHelper", "_cellLayoutManager$ge2", "horizontalOverscanSize", "verticalOverscanSize", "_this$state3", "_lastRenderedCellCount", "_lastRenderedCellLayoutManager", "calculateSizeAndPositionData", "_cellLayoutManager$ge3", "right", "bottom", "cellRenderers", "x", "y", "collectionStyle", "propTypes", "Section", "_indexMap", "_indices", "SectionManager", "sectionSize", "_sectionSize", "_cellMetadata", "_sections", "getSections", "section", "getCellIndices", "map", "sectionXStart", "sectionXStop", "sectionYStart", "sectionYStop", "sections", "sectionX", "sectionY", "toString", "cellMetadatum", "addCellIndex", "_ref$align", "cellOffset", "Collection", "context", "_lastRenderedCellIndices", "_isScrollingChange", "bind", "_setCollectionViewRef", "_collectionView", "recomputeCellSizesAndPositions", "data", "cellSizeAndPositionGetter", "cellMetadata", "sectionManager", "registerCell", "_sectionManager", "_height", "_width", "cellGroupRenderer", "getCellMetadata", "cellRendererProps", "ColumnSizer", "columnMaxWidth", "column<PERSON><PERSON><PERSON><PERSON><PERSON>", "_registered<PERSON><PERSON>d", "safeColumnMinWidth", "safeColumnMaxWidth", "adjustedWidth", "getColumnWidth", "child", "InfiniteLoader", "_loadMoreRowsMemoizer", "_onRowsRendered", "autoReload", "_doStuff", "_lastRenderedStartIndex", "_lastRenderedStopIndex", "onRowsRendered", "unloadedRanges", "loadMoreRows", "unloadedRange", "promise", "lastRenderedStartIndex", "lastRenderedStopIndex", "component", "currentIndex", "recomputeSize", "recomputeRowHeights", "forceUpdateReactVirtualizedComponent", "isRowLoaded", "minimumBatchSize", "threshold", "rangeStartIndex", "rangeStopIndex", "potentialStopIndex", "_index", "firstUnloadedRange", "_index2", "scanForUnloadedRanges", "squashedUnloaded<PERSON><PERSON>es", "_loadUnloadedRanges", "registeredChild", "List", "<PERSON><PERSON><PERSON><PERSON>", "widthDescriptor", "writable", "getOffsetForCell", "measureAllCells", "_ref6$columnIndex", "_ref6$rowIndex", "scrollToPosition", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "classNames", "_cell<PERSON><PERSON><PERSON>", "ge", "a", "c", "l", "h", "m", "_GEP", "_GEA", "gt", "_GTP", "_GTA", "lt", "_LTP", "_LTA", "le", "_LEP", "_LEA", "eq", "p", "_EQP", "_EQA", "IntervalTreeNode", "mid", "leftPoints", "rightPoints", "count", "proto", "prototype", "copy", "b", "rebuild", "intervals", "ntree", "createIntervalTree", "rebuildWithInterval", "rebuildWithoutInterval", "idx", "reportLeftRange", "arr", "hi", "cb", "r", "reportRightRange", "lo", "reportRange", "compareNumbers", "compareBegin", "d", "compareEnd", "pts", "sort", "leftIntervals", "rightIntervals", "centerIntervals", "s", "slice", "IntervalTree", "root", "result", "insert", "weight", "remove", "n", "queryPoint", "queryInterval", "tproto", "PositionCache", "defaultCellHeight", "unmeasuredCellCount", "tallestColumnSize", "renderCallback", "_intervalTree", "_leftMap", "columnSizeMap", "_columnSizeMap", "columnHeight", "Masonry", "eventScrollTop", "currentTarget", "_getEstimatedTotalHeight", "_debounceResetIsScrolling", "_positionCache", "_invalidateOnUpdateStartIndex", "_invalidateOnUpdateStopIndex", "_populatePositionCache", "_checkInvalidateOnUpdate", "_invokeOnScrollCallback", "_invokeOnCellsRenderedCallback", "_debounceResetIsScrollingId", "cellMeasurerCache", "overscanByPixels", "rowDirection", "estimateTotalHeight", "shortestColumnSize", "measuredCellCount", "range", "_style", "batchSize", "_startIndex", "_stopIndex", "_debounceResetIsScrollingCallback", "estimatedColumnCount", "_onScrollMemoized", "_startIndexMemoized", "_stopIndexMemoized", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cellPositioner", "_cellPositioner", "setPosition", "noop", "CellMeasurerCacheDecorator", "_cellMeasurerCache", "_columnIndexOffset", "_rowIndexOffset", "_params$columnIndexOf", "columnIndexOffset", "_params$rowIndexOffse", "rowIndexOffset", "clear", "clearAll", "MultiGrid", "showHorizontalScrollbar", "showVerticalScrollbar", "_bottomLeftGrid", "_bottomRightGrid", "rest", "fixedRowCount", "fixedColumnCount", "scrollInfo", "_topLeftGrid", "_topRightGrid", "_fixedColumnCount", "_fixedRowCount", "_maybeCalculateCachedStyles", "_deferredMeasurementCacheBottomLeftGrid", "_deferredMeasurementCacheBottomRightGrid", "_deferredMeasurementCacheTopRightGrid", "_ref7$columnIndex", "_ref7$rowIndex", "_ref8$columnIndex", "_ref8$rowIndex", "adjustedColumnIndex", "adjustedRowIndex", "_leftGrid<PERSON>idth", "_topGridHeight", "_this$props7", "_this$props8", "_prepareF<PERSON><PERSON><PERSON>", "_this$state4", "_containerOuterStyle", "_containerTopStyle", "_renderTopLeftGrid", "_renderTopRightGrid", "_containerBottomStyle", "_renderBottomLeftGrid", "_renderBottomRightGrid", "_getTopGridHeight", "leftGridWidth", "_getLeftGridWidth", "topGridHeight", "resetAll", "_this$props9", "enableFixedColumnScroll", "enableFixedRowScroll", "styleBottomLeftGrid", "styleBottomRightGrid", "styleTopLeftGrid", "styleTopRightGrid", "sizeChange", "_lastRenderedHeight", "_last<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "leftSizeChange", "_lastRenderedColumnWidth", "_lastRenderedFixedColumnCount", "topSizeChange", "_lastRenderedFixedRowCount", "_lastRenderedRowHeight", "_lastR<PERSON>edStyle", "_lastRenderedStyleBottomLeftGrid", "_bottomLeftGridStyle", "_lastRenderedStyleBottomRightGrid", "_bottomRightGridStyle", "_lastRenderedStyleTopLeftGrid", "_topLeftGridStyle", "_lastRenderedStyleTopRightGrid", "_topRightGridStyle", "hideBottomLeftGridScrollbar", "additionalRowCount", "_getBottomGridHeight", "gridWidth", "bottomLeftGrid", "_cellRendererBottomLeftGrid", "classNameBottomLeftGrid", "_onScrollTop", "_bottomLeftGridRef", "_rowHeightBottomGrid", "_cellRendererBottomRightGrid", "classNameBottomRightGrid", "_columnWidthRightGrid", "_onScrollbarPresenceChange", "_bottomRightGridRef", "_getRightGridWidth", "classNameTopLeftGrid", "_topLeftGridRef", "hideTopRightGridScrollbar", "_this$state5", "additionalColumnCount", "additionalHeight", "gridHeight", "topRightGrid", "_cellRendererTopRightGrid", "classNameTopRightGrid", "_onScrollLeft", "_topRightGridRef", "ScrollSync", "defaultHeaderRowRenderer", "columns", "ASC", "DESC", "SortIndicator", "sortDirection", "viewBox", "fill", "defaultHeaderRenderer", "dataKey", "label", "sortBy", "showSortIndicator", "title", "defaultRowRenderer", "onRowClick", "onRowDoubleClick", "onRowMouseOut", "onRowMouseOver", "onRowRightClick", "rowData", "a11yProps", "onClick", "onDoubleClick", "onMouseOut", "onMouseOver", "onContextMenu", "Column", "cellDataGetter", "cellData", "String", "defaultSortDirection", "flexGrow", "flexShrink", "<PERSON><PERSON><PERSON><PERSON>", "Table", "scrollbarWidth", "_createColumn", "_createRow", "_ref3$columnIndex", "_ref3$rowIndex", "_Grid", "_setScrollbarWidth", "disable<PERSON>eader", "gridClassName", "headerHeight", "headerRow<PERSON><PERSON><PERSON>", "rowClassName", "rowStyle", "availableRowsHeight", "rowClass", "rowStyleObject", "_cachedColumnStyles", "toArray", "column", "flexStyles", "_getFlexStyleForColumn", "_getHeaderColumns", "onColumnClick", "_column$props", "columnData", "headerOnClick", "headerOnKeyDown", "headerTabIndex", "headerAriaSort", "headerAriaLabel", "headerClassName", "headerStyle", "onHeaderClick", "_column$props2", "disableSort", "sortEnabled", "ReactVirtualized__Table__sortableHeaderColumn", "<PERSON><PERSON><PERSON><PERSON>", "newSortDirection", "rowGetter", "flattenedStyle", "_getRowHeight", "customStyle", "flexValue", "flex", "msFlex", "WebkitFlex", "_this4", "_createHeader", "getScrollbarWidth", "mountedInstances", "originalBodyPointerEvents", "disablePointerEventsTimeoutId", "enablePointerEventsIfDisabled", "body", "enablePointerEventsAfterDelayCallback", "instance", "__resetIsScrolling", "onScrollWindow", "maximumTimeout", "enablePointerEventsAfterDelay", "scrollElement", "__handleWindowScrollEvent", "registerScrollListener", "unregisterScrollListener", "isWindow", "getBoundingBox", "getBoundingClientRect", "getDimensions", "innerHeight", "innerWidth", "serverHeight", "serverWidth", "getPositionOffset", "container", "documentElement", "containerElement", "elementRect", "containerRect", "getScrollOffset", "_elementRect", "_containerRect", "scrollY", "scrollX", "getWindow", "WindowScroller", "updatePosition", "scrollTo", "_positionFromTop", "_isMounted", "_positionFromLeft", "thisNode", "dimensions", "_registerResizeListener", "prevScrollElement", "_unregisterResizeListener", "onChildScroll", "_onChildScroll"], "mappings": "6FAAA,YAYe,SAASA,EAA0BC,EAAOC,GAEvD,IAAIC,EA0BIC,EAVAC,EAJJC,OAA0C,KAT5CH,OADwB,IAAfD,EACCA,EACiB,oBAAXK,OACNA,OACe,oBAATC,KACNA,KAEAC,GAGqBC,UAA4BP,EAAQO,SAASJ,YAE9E,IAAKA,EAAa,CAChB,IAAIK,GACEN,EAAMF,EAAQS,uBAAyBT,EAAQU,0BAA4BV,EAAQW,6BAA+B,SAAUC,GAC9H,OAAOZ,EAAQa,WAAWD,EAAI,KAGzB,SAAUA,GACf,OAAOV,EAAIU,KAIXE,GACEb,EAASD,EAAQe,sBAAwBf,EAAQgB,yBAA2BhB,EAAQiB,4BAA8BjB,EAAQkB,aACvH,SAAUC,GACf,OAAOlB,EAAOkB,KAIdC,EAAgB,SAAuBC,GACzC,IAAIC,EAAWD,EAAQE,mBACnBC,EAASF,EAASG,kBAClBC,EAAWJ,EAASK,iBACpBC,EAAcJ,EAAOC,kBACzBC,EAASG,WAAaH,EAASI,YAC/BJ,EAASK,UAAYL,EAASM,aAC9BJ,EAAYK,MAAMC,MAAQV,EAAOW,YAAc,EAAI,KACnDP,EAAYK,MAAMG,OAASZ,EAAOa,aAAe,EAAI,KACrDb,EAAOK,WAAaL,EAAOM,YAC3BN,EAAOO,UAAYP,EAAOQ,cAOxBM,EAAiB,SAAwBC,GAE3C,KAAIA,EAAEC,OAAOC,WAAmD,mBAA/BF,EAAEC,OAAOC,UAAUC,SAA0BH,EAAEC,OAAOC,UAAUC,QAAQ,oBAAsB,GAAKH,EAAEC,OAAOC,UAAUC,QAAQ,kBAAoB,GAAnL,CAIA,IAAIrB,EAAUsB,KACdvB,EAAcuB,MAEVA,KAAKC,eACP9B,EAAY6B,KAAKC,eAGnBD,KAAKC,cAAgBpC,GAAa,YAjBhB,SAAuBa,GACzC,OAAOA,EAAQc,aAAed,EAAQwB,eAAeX,OAASb,EAAQgB,cAAgBhB,EAAQwB,eAAeT,QAiBvGU,CAAczB,KAChBA,EAAQwB,eAAeX,MAAQb,EAAQc,YACvCd,EAAQwB,eAAeT,OAASf,EAAQgB,aAExChB,EAAQ0B,oBAAoBC,SAAQ,SAAUpC,GAC5CA,EAAGqC,KAAK5B,EAASkB,YAQrBW,GAAY,EACZC,EAAiB,GACjBC,EAAsB,iBACtBC,EAAc,kBAAkBC,MAAM,KACtCC,EAAc,uEAAuED,MAAM,KAGzFE,EAAMxD,EAAQO,SAASkD,cAAc,eAMzC,QAJgCC,IAA5BF,EAAIvB,MAAM0B,gBACZT,GAAY,IAGI,IAAdA,EACF,IAAK,IAAIU,EAAI,EAAGA,EAAIP,EAAYQ,OAAQD,IACtC,QAAoDF,IAAhDF,EAAIvB,MAAMoB,EAAYO,GAAK,iBAAgC,CAE7DT,EAAiB,IADXE,EAAYO,GACSE,cAAgB,IAC3CV,EAAsBG,EAAYK,GAClCV,GAAY,EACZ,MAKR,IAAIS,EAAgB,aAChBI,EAAqB,IAAMZ,EAAiB,aAAeQ,EAAgB,gDAC3EK,EAAiBb,EAAiB,kBAAoBQ,EAAgB,KAmG5E,MAAO,CACLM,kBA1EsB,SAA2B5C,EAAST,GAC1D,GAAIT,EACFkB,EAAQlB,YAAY,WAAYS,OAC3B,CACL,IAAKS,EAAQE,mBAAoB,CAC/B,IAAI2C,EAAM7C,EAAQ8C,cAEdC,EAAepE,EAAQqE,iBAAiBhD,GAExC+C,GAAyC,UAAzBA,EAAaE,WAC/BjD,EAAQY,MAAMqC,SAAW,YAjCd,SAAsBJ,GACvC,IAAKA,EAAIK,eAAe,uBAAwB,CAE9C,IAAIC,GAAOT,GAA0C,IAAM,uBAAyBC,GAAkC,IAA5G,6VACNS,EAAOP,EAAIO,MAAQP,EAAIQ,qBAAqB,QAAQ,GACpDzC,EAAQiC,EAAIT,cAAc,SAC9BxB,EAAMd,GAAK,sBACXc,EAAM0C,KAAO,WAEA,MAAT7E,GACFmC,EAAM2C,aAAa,QAAS9E,GAG1BmC,EAAM4C,WACR5C,EAAM4C,WAAWC,QAAUN,EAE3BvC,EAAM8C,YAAYb,EAAIc,eAAeR,IAGvCC,EAAKM,YAAY9C,IAiBfgD,CAAaf,GACb7C,EAAQwB,eAAiB,GACzBxB,EAAQ0B,oBAAsB,IAC7B1B,EAAQE,mBAAqB2C,EAAIT,cAAc,QAAQhB,UAAY,kBACpE,IAAIyC,EAAqB,oFAEzB,GAAI9E,OAAO+E,aAAc,CACvB,IAAIC,EAAeD,aAAaE,aAAa,+BAAgC,CAC3EC,WAAY,WACV,OAAOJ,KAGX7D,EAAQE,mBAAmBgE,UAAYH,EAAaE,WAAW,SAE/DjE,EAAQE,mBAAmBgE,UAAYL,EAGzC7D,EAAQ0D,YAAY1D,EAAQE,oBAC5BH,EAAcC,GACdA,EAAQmE,iBAAiB,SAAUlD,GAAgB,GAG/Cc,IACF/B,EAAQE,mBAAmBkE,sBAAwB,SAA2BlD,GACxEA,EAAEoB,eAAiBA,GACrBvC,EAAcC,IAIlBA,EAAQE,mBAAmBiE,iBAAiBpC,EAAqB/B,EAAQE,mBAAmBkE,wBAIhGpE,EAAQ0B,oBAAoB2C,KAAK9E,KA6BnC+E,qBAzByB,SAA8BtE,EAAST,GAChE,GAAIT,EACFkB,EAAQuE,YAAY,WAAYhF,QAIhC,GAFAS,EAAQ0B,oBAAoB8C,OAAOxE,EAAQ0B,oBAAoBL,QAAQ9B,GAAK,IAEvES,EAAQ0B,oBAAoBc,OAAQ,CACvCxC,EAAQyE,oBAAoB,SAAUxD,GAAgB,GAElDjB,EAAQE,mBAAmBkE,wBAC7BpE,EAAQE,mBAAmBuE,oBAAoB1C,EAAqB/B,EAAQE,mBAAmBkE,uBAE/FpE,EAAQE,mBAAmBkE,sBAAwB,MAGrD,IACEpE,EAAQE,oBAAsBF,EAAQ0E,YAAY1E,EAAQE,oBAC1D,MAAOgB,QAlNjB,oC,iVCGe,SAASyD,EAAkDC,GACxE,IAAIC,EAAYD,EAAKC,UACjBC,EAAWF,EAAKE,SAChBC,EAA0BH,EAAKG,wBAC/BC,EAA+BJ,EAAKI,6BACpCC,EAAiBL,EAAKK,eACtBC,EAAeN,EAAKM,aACpBC,EAAoBP,EAAKO,kBACzBC,EAAgBR,EAAKQ,cACrBC,EAAqCT,EAAKS,mCAI1CR,IAAcI,IAAuC,iBAAbH,GAAiD,iBAAjBI,GAA8BJ,IAAaI,KACrHH,EAAwBC,GAGpBI,GAAiB,GAAKA,IAAkBD,GAC1CE,K,sBCdF,EAEJ,WAKE,SAASC,EAA2BV,GAClC,IAAIC,EAAYD,EAAKC,UACjBU,EAAiBX,EAAKW,eACtBC,EAAoBZ,EAAKY,kBAE7B,IAAgBlE,KAAMgE,GAEtB,IAAgBhE,KAAM,2BAA4B,IAElD,IAAgBA,KAAM,sBAAuB,GAE7C,IAAgBA,KAAM,qBAAsB,GAE5C,IAAgBA,KAAM,kBAAc,GAEpC,IAAgBA,KAAM,uBAAmB,GAEzC,IAAgBA,KAAM,0BAAsB,GAE5CA,KAAKmE,gBAAkBF,EACvBjE,KAAKoE,WAAab,EAClBvD,KAAKqE,mBAAqBH,EAsQ5B,OAnQA,IAAaF,EAA4B,CAAC,CACxCM,IAAK,qBACLC,MAAO,WACL,OAAO,IAER,CACDD,IAAK,YACLC,MAAO,SAAmBC,GACxB,IAAIjB,EAAYiB,EAAMjB,UAClBW,EAAoBM,EAAMN,kBAC1BD,EAAiBO,EAAMP,eAC3BjE,KAAKoE,WAAab,EAClBvD,KAAKqE,mBAAqBH,EAC1BlE,KAAKmE,gBAAkBF,IAExB,CACDK,IAAK,eACLC,MAAO,WACL,OAAOvE,KAAKoE,aAEb,CACDE,IAAK,uBACLC,MAAO,WACL,OAAOvE,KAAKqE,qBAEb,CACDC,IAAK,uBACLC,MAAO,WACL,OAAOvE,KAAKyE,qBAEb,CACDH,IAAK,sBACLC,MAAO,WACL,OAAO,IAOR,CACDD,IAAK,2BACLC,MAAO,SAAkCG,GACvC,GAAIA,EAAQ,GAAKA,GAAS1E,KAAKoE,WAC7B,MAAMO,MAAM,mBAAmBC,OAAOF,EAAO,4BAA4BE,OAAO5E,KAAKoE,aAGvF,GAAIM,EAAQ1E,KAAKyE,mBAIf,IAHA,IAAII,EAAkC7E,KAAK8E,uCACvCC,EAASF,EAAgCE,OAASF,EAAgCG,KAE7E/D,EAAIjB,KAAKyE,mBAAqB,EAAGxD,GAAKyD,EAAOzD,IAAK,CACzD,IAAI+D,EAAOhF,KAAKmE,gBAAgB,CAC9BO,MAAOzD,IAKT,QAAaF,IAATiE,GAAsBC,MAAMD,GAC9B,MAAML,MAAM,kCAAkCC,OAAO3D,EAAG,cAAc2D,OAAOI,IAC3D,OAATA,GACThF,KAAKkF,yBAAyBjE,GAAK,CACjC8D,OAAQA,EACRC,KAAM,GAERhF,KAAKmF,kBAAoBT,IAEzB1E,KAAKkF,yBAAyBjE,GAAK,CACjC8D,OAAQA,EACRC,KAAMA,GAERD,GAAUC,EACVhF,KAAKyE,mBAAqBC,GAKhC,OAAO1E,KAAKkF,yBAAyBR,KAEtC,CACDJ,IAAK,uCACLC,MAAO,WACL,OAAOvE,KAAKyE,oBAAsB,EAAIzE,KAAKkF,yBAAyBlF,KAAKyE,oBAAsB,CAC7FM,OAAQ,EACRC,KAAM,KAST,CACDV,IAAK,eACLC,MAAO,WACL,IAAIM,EAAkC7E,KAAK8E,uCAI3C,OAH+BD,EAAgCE,OAASF,EAAgCG,MAC/EhF,KAAKoE,WAAapE,KAAKyE,mBAAqB,GACfzE,KAAKqE,qBAe5D,CACDC,IAAK,2BACLC,MAAO,SAAkCa,GACvC,IAAIC,EAAcD,EAAME,MACpBA,OAAwB,IAAhBD,EAAyB,OAASA,EAC1CE,EAAgBH,EAAMG,cACtBC,EAAgBJ,EAAMI,cACtBC,EAAcL,EAAMK,YAExB,GAAIF,GAAiB,EACnB,OAAO,EAGT,IAGIG,EAHAC,EAAQ3F,KAAK4F,yBAAyBH,GACtCI,EAAYF,EAAMZ,OAClBe,EAAYD,EAAYN,EAAgBI,EAAMX,KAGlD,OAAQM,GACN,IAAK,QACHI,EAAcG,EACd,MAEF,IAAK,MACHH,EAAcI,EACd,MAEF,IAAK,SACHJ,EAAcG,GAAaN,EAAgBI,EAAMX,MAAQ,EACzD,MAEF,QACEU,EAAcK,KAAKC,IAAIF,EAAWC,KAAKE,IAAIJ,EAAWL,IAI1D,IAAIU,EAAYlG,KAAKmG,eACrB,OAAOJ,KAAKC,IAAI,EAAGD,KAAKE,IAAIC,EAAYX,EAAeG,MAExD,CACDpB,IAAK,sBACLC,MAAO,SAA6B6B,GAClC,IAAIb,EAAgBa,EAAOb,cACvBR,EAASqB,EAAOrB,OAGpB,GAAkB,IAFF/E,KAAKmG,eAGnB,MAAO,GAGT,IAAIN,EAAYd,EAASQ,EAErBc,EAAQrG,KAAKsG,iBAAiBvB,GAE9BY,EAAQ3F,KAAK4F,yBAAyBS,GAC1CtB,EAASY,EAAMZ,OAASY,EAAMX,KAG9B,IAFA,IAAIuB,EAAOF,EAEJtB,EAASc,GAAaU,EAAOvG,KAAKoE,WAAa,GACpDmC,IACAxB,GAAU/E,KAAK4F,yBAAyBW,GAAMvB,KAGhD,MAAO,CACLqB,MAAOA,EACPE,KAAMA,KAST,CACDjC,IAAK,YACLC,MAAO,SAAmBG,GACxB1E,KAAKyE,mBAAqBsB,KAAKE,IAAIjG,KAAKyE,mBAAoBC,EAAQ,KAErE,CACDJ,IAAK,gBACLC,MAAO,SAAuBiC,EAAMC,EAAK1B,GACvC,KAAO0B,GAAOD,GAAM,CAClB,IAAIE,EAASD,EAAMV,KAAKY,OAAOH,EAAOC,GAAO,GACzCjB,EAAgBxF,KAAK4F,yBAAyBc,GAAQ3B,OAE1D,GAAIS,IAAkBT,EACpB,OAAO2B,EACElB,EAAgBT,EACzB0B,EAAMC,EAAS,EACNlB,EAAgBT,IACzByB,EAAOE,EAAS,GAIpB,OAAID,EAAM,EACDA,EAAM,EAEN,IAGV,CACDnC,IAAK,qBACLC,MAAO,SAA4BG,EAAOK,GAGxC,IAFA,IAAI6B,EAAW,EAERlC,EAAQ1E,KAAKoE,YAAcpE,KAAK4F,yBAAyBlB,GAAOK,OAASA,GAC9EL,GAASkC,EACTA,GAAY,EAGd,OAAO5G,KAAK6G,cAAcd,KAAKE,IAAIvB,EAAO1E,KAAKoE,WAAa,GAAI2B,KAAKY,MAAMjC,EAAQ,GAAIK,KASxF,CACDT,IAAK,mBACLC,MAAO,SAA0BQ,GAC/B,GAAIE,MAAMF,GACR,MAAMJ,MAAM,kBAAkBC,OAAOG,EAAQ,eAK/CA,EAASgB,KAAKC,IAAI,EAAGjB,GACrB,IAAIF,EAAkC7E,KAAK8E,uCACvCgC,EAAoBf,KAAKC,IAAI,EAAGhG,KAAKyE,oBAEzC,OAAII,EAAgCE,QAAUA,EAErC/E,KAAK6G,cAAcC,EAAmB,EAAG/B,GAKzC/E,KAAK+G,mBAAmBD,EAAmB/B,OAKjDf,EAhST,GCEWgD,EAAoB,WAC7B,MARyB,oBAAXvJ,QAILA,OAAOwJ,OAPY,SADC,MCU3B,EAEJ,WACE,SAASC,EAAkC5D,GACzC,IAAI6D,EAAqB7D,EAAK8D,cAC1BA,OAAuC,IAAvBD,EAAgCH,IAAsBG,EACtEf,EAAS,IAAyB9C,EAAM,CAAC,kBAE7C,IAAgBtD,KAAMkH,GAEtB,IAAgBlH,KAAM,mCAA+B,GAErD,IAAgBA,KAAM,sBAAkB,GAGxCA,KAAKqH,4BAA8B,IAAI,EAA2BjB,GAClEpG,KAAKsH,eAAiBF,EA0KxB,OAvKA,IAAaF,EAAmC,CAAC,CAC/C5C,IAAK,qBACLC,MAAO,WACL,OAAOvE,KAAKqH,4BAA4BlB,eAAiBnG,KAAKsH,iBAE/D,CACDhD,IAAK,YACLC,MAAO,SAAmB6B,GACxBpG,KAAKqH,4BAA4BE,UAAUnB,KAE5C,CACD9B,IAAK,eACLC,MAAO,WACL,OAAOvE,KAAKqH,4BAA4BG,iBAEzC,CACDlD,IAAK,uBACLC,MAAO,WACL,OAAOvE,KAAKqH,4BAA4BI,yBAEzC,CACDnD,IAAK,uBACLC,MAAO,WACL,OAAOvE,KAAKqH,4BAA4BK,yBAOzC,CACDpD,IAAK,sBACLC,MAAO,SAA6BC,GAClC,IAAIe,EAAgBf,EAAMe,cACtBR,EAASP,EAAMO,OAEfmB,EAAYlG,KAAKqH,4BAA4BlB,eAE7CwB,EAAgB3H,KAAKmG,eAErByB,EAAmB5H,KAAK6H,qBAAqB,CAC/CtC,cAAeA,EACfR,OAAQA,EACRmB,UAAWyB,IAGb,OAAO5B,KAAK+B,MAAMF,GAAoBD,EAAgBzB,MAEvD,CACD5B,IAAK,2BACLC,MAAO,SAAkCG,GACvC,OAAO1E,KAAKqH,4BAA4BzB,yBAAyBlB,KAElE,CACDJ,IAAK,uCACLC,MAAO,WACL,OAAOvE,KAAKqH,4BAA4BvC,yCAIzC,CACDR,IAAK,eACLC,MAAO,WACL,OAAOwB,KAAKE,IAAIjG,KAAKsH,eAAgBtH,KAAKqH,4BAA4BlB,kBAIvE,CACD7B,IAAK,2BACLC,MAAO,SAAkCa,GACvC,IAAIC,EAAcD,EAAME,MACpBA,OAAwB,IAAhBD,EAAyB,OAASA,EAC1CE,EAAgBH,EAAMG,cACtBC,EAAgBJ,EAAMI,cACtBC,EAAcL,EAAMK,YACxBD,EAAgBxF,KAAK+H,oBAAoB,CACvCxC,cAAeA,EACfR,OAAQS,IAGV,IAAIT,EAAS/E,KAAKqH,4BAA4BW,yBAAyB,CACrE1C,MAAOA,EACPC,cAAeA,EACfC,cAAeA,EACfC,YAAaA,IAGf,OAAOzF,KAAKiI,oBAAoB,CAC9B1C,cAAeA,EACfR,OAAQA,MAKX,CACDT,IAAK,sBACLC,MAAO,SAA6B2D,GAClC,IAAI3C,EAAgB2C,EAAM3C,cACtBR,EAASmD,EAAMnD,OAKnB,OAJAA,EAAS/E,KAAK+H,oBAAoB,CAChCxC,cAAeA,EACfR,OAAQA,IAEH/E,KAAKqH,4BAA4Bc,oBAAoB,CAC1D5C,cAAeA,EACfR,OAAQA,MAGX,CACDT,IAAK,YACLC,MAAO,SAAmBG,GACxB1E,KAAKqH,4BAA4Be,UAAU1D,KAE5C,CACDJ,IAAK,uBACLC,MAAO,SAA8B8D,GACnC,IAAI9C,EAAgB8C,EAAM9C,cACtBR,EAASsD,EAAMtD,OACfmB,EAAYmC,EAAMnC,UACtB,OAAOA,GAAaX,EAAgB,EAAIR,GAAUmB,EAAYX,KAE/D,CACDjB,IAAK,sBACLC,MAAO,SAA6B+D,GAClC,IAAI/C,EAAgB+C,EAAM/C,cACtBR,EAASuD,EAAMvD,OAEfmB,EAAYlG,KAAKqH,4BAA4BlB,eAE7CwB,EAAgB3H,KAAKmG,eAEzB,GAAID,IAAcyB,EAChB,OAAO5C,EAEP,IAAI6C,EAAmB5H,KAAK6H,qBAAqB,CAC/CtC,cAAeA,EACfR,OAAQA,EACRmB,UAAWA,IAGb,OAAOH,KAAK+B,MAAMF,GAAoBD,EAAgBpC,MAGzD,CACDjB,IAAK,sBACLC,MAAO,SAA6BgE,GAClC,IAAIhD,EAAgBgD,EAAMhD,cACtBR,EAASwD,EAAMxD,OAEfmB,EAAYlG,KAAKqH,4BAA4BlB,eAE7CwB,EAAgB3H,KAAKmG,eAEzB,GAAID,IAAcyB,EAChB,OAAO5C,EAEP,IAAI6C,EAAmB5H,KAAK6H,qBAAqB,CAC/CtC,cAAeA,EACfR,OAAQA,EACRmB,UAAWyB,IAGb,OAAO5B,KAAK+B,MAAMF,GAAoB1B,EAAYX,QAKjD2B,EAxLT,GCTe,SAASsB,IACtB,IAAIC,IAAiBC,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,KAAmBA,UAAU,GAChFC,EAAgB,GACpB,OAAO,SAAUrF,GACf,IAAIsF,EAAWtF,EAAKsF,SAChBC,EAAUvF,EAAKuF,QACfC,EAAOC,OAAOD,KAAKD,GACnBG,GAAkBP,GAAkBK,EAAKG,OAAM,SAAU3E,GAC3D,IAAIC,EAAQsE,EAAQvE,GACpB,OAAO4E,MAAMC,QAAQ5E,GAASA,EAAMrD,OAAS,EAAIqD,GAAS,KAExD6E,EAAeN,EAAK5H,SAAW6H,OAAOD,KAAKH,GAAezH,QAAU4H,EAAKO,MAAK,SAAU/E,GAC1F,IAAIgF,EAAcX,EAAcrE,GAC5BC,EAAQsE,EAAQvE,GACpB,OAAO4E,MAAMC,QAAQ5E,GAAS+E,EAAYC,KAAK,OAAShF,EAAMgF,KAAK,KAAOD,IAAgB/E,KAE5FoE,EAAgBE,EAEZG,GAAkBI,GACpBR,EAASC,IChBA,SAASW,EAAwBlG,GAC9C,IAAIE,EAAWF,EAAKE,SAChBiG,EAA6BnG,EAAKmG,2BAClCC,EAAqBpG,EAAKoG,mBAC1BC,EAAmBrG,EAAKqG,iBACxBC,EAA4BtG,EAAKsG,0BACjCC,EAAwBvG,EAAKuG,sBAC7BC,EAAexG,EAAKwG,aACpBC,EAAezG,EAAKyG,aACpBC,EAAoB1G,EAAK0G,kBACzBlG,EAAgBR,EAAKQ,cACrBkB,EAAO1B,EAAK0B,KACZiF,EAA4B3G,EAAK2G,0BACjCC,EAA4B5G,EAAK4G,0BACjC3G,EAAYkG,EAA2BjC,eACvC2C,EAAmBrG,GAAiB,GAAKA,EAAgBP,EAIzD4G,IAHiBnF,IAAS8E,GAAgBG,IAA8BN,GAAwC,iBAAbnG,GAAyBA,IAAamG,GAGlGK,IAAsBJ,GAA6B9F,IAAkB+F,GAC9GK,EAA0BpG,IAEhBqG,GAAoB5G,EAAY,IAAMyB,EAAO8E,GAAgBvG,EAAYmG,IAK/EK,EAAeN,EAA2BtD,eAAiBnB,GAC7DkF,EAA0B3G,EAAY,G,ICjCxC6G,ECQAC,EAAQC,E,UDIRC,GATFH,EADoB,oBAAX3M,OACHA,OACmB,oBAATC,KACVA,KAEA,IAKUI,uBAAyBsM,EAAIpM,6BAA+BoM,EAAIrM,0BAA4BqM,EAAII,wBAA0BJ,EAAIK,yBAA2B,SAAU7B,GACnL,OAAOwB,EAAIlM,WAAW0K,EAAU,IAAO,KAGrCtL,EAAS8M,EAAIhM,sBAAwBgM,EAAI9L,4BAA8B8L,EAAI/L,yBAA2B+L,EAAIM,uBAAyBN,EAAIO,wBAA0B,SAAUnM,GAC7K4L,EAAI7L,aAAaC,IAGRjB,EAAMgN,EACNK,EAAMtN,EElBN,EAAyB,SAAgCuN,GAClE,OAAOD,EAAIC,EAAMrM,KASR,EAA0B,SAAiCoK,EAAUkC,GAC9E,IAAIzE,EAEJ0E,QAAQC,UAAUC,MAAK,WACrB5E,EAAQ6E,KAAKC,SAGf,IAQIN,EAAQ,CACVrM,GAAIjB,GATQ,SAAS6N,IACjBF,KAAKC,MAAQ9E,GAASyE,EACxBlC,EAAStI,OAETuK,EAAMrM,GAAKjB,EAAI6N,OAOnB,OAAOP,GDrBT,SAASQ,EAAQC,EAAQC,GAAkB,IAAIzC,EAAOC,OAAOD,KAAKwC,GAAS,GAAIvC,OAAOyC,sBAAuB,CAAE,IAAIC,EAAU1C,OAAOyC,sBAAsBF,GAAaC,IAAgBE,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAO5C,OAAO6C,yBAAyBN,EAAQK,GAAKE,eAAgB/C,EAAK/F,KAAK+I,MAAMhD,EAAM2C,GAAY,OAAO3C,EAE9U,SAASiD,EAAclM,GAAU,IAAK,IAAIoB,EAAI,EAAGA,EAAIyH,UAAUxH,OAAQD,IAAK,CAAE,IAAI+K,EAAyB,MAAhBtD,UAAUzH,GAAayH,UAAUzH,GAAK,GAAQA,EAAI,EAAKoK,EAAQW,GAAQ,GAAM3L,SAAQ,SAAUiE,GAAO,IAAgBzE,EAAQyE,EAAK0H,EAAO1H,OAAsByE,OAAOkD,0BAA6BlD,OAAOmD,iBAAiBrM,EAAQkJ,OAAOkD,0BAA0BD,IAAmBX,EAAQW,GAAQ3L,SAAQ,SAAUiE,GAAOyE,OAAOoD,eAAetM,EAAQyE,EAAKyE,OAAO6C,yBAAyBI,EAAQ1H,OAAe,OAAOzE,EAkBtf,IAMHuM,EACQ,WADRA,EAES,YAWT,GAAQ9B,EAAQD,EAEpB,SAAUgC,GAIR,SAASC,EAAKC,GACZ,IAAIC,EAEJ,IAAgBxM,KAAMsM,GAEtBE,EAAQ,IAA2BxM,KAAM,IAAgBsM,GAAMhM,KAAKN,KAAMuM,IAE1E,IAAgB,IAAuBC,GAAQ,0BAA2BhE,KAE1E,IAAgB,IAAuBgE,GAAQ,oBAAqBhE,GAAuB,IAE3F,IAAgB,IAAuBgE,GAAQ,iCAAkC,MAEjF,IAAgB,IAAuBA,GAAQ,8BAA+B,MAE9E,IAAgB,IAAuBA,GAAQ,4BAA4B,GAE3E,IAAgB,IAAuBA,GAAQ,2BAA2B,GAE1E,IAAgB,IAAuBA,GAAQ,2BAA4B,GAE3E,IAAgB,IAAuBA,GAAQ,yBAA0B,GAEzE,IAAgB,IAAuBA,GAAQ,6BAA6B,GAE5E,IAAgB,IAAuBA,GAAQ,2BAAuB,GAEtE,IAAgB,IAAuBA,GAAQ,0BAAsB,GAErE,IAAgB,IAAuBA,GAAQ,yBAAqB,GAEpE,IAAgB,IAAuBA,GAAQ,wBAAoB,GAEnE,IAAgB,IAAuBA,GAAQ,sBAAkB,GAEjE,IAAgB,IAAuBA,GAAQ,qBAAiB,GAEhE,IAAgB,IAAuBA,GAAQ,4BAA6B,GAE5E,IAAgB,IAAuBA,GAAQ,2BAA4B,GAE3E,IAAgB,IAAuBA,GAAQ,yBAA0B,GAEzE,IAAgB,IAAuBA,GAAQ,wBAAyB,GAExE,IAAgB,IAAuBA,GAAQ,yBAAqB,GAEpE,IAAgB,IAAuBA,GAAQ,0BAAsB,GAErE,IAAgB,IAAuBA,GAAQ,sCAAkC,GAEjF,IAAgB,IAAuBA,GAAQ,cAAe,IAE9D,IAAgB,IAAuBA,GAAQ,aAAc,IAE7D,IAAgB,IAAuBA,GAAQ,gCAAgC,WAC7EA,EAAMC,+BAAiC,KAEvCD,EAAME,SAAS,CACbC,aAAa,EACbC,uBAAuB,OAI3B,IAAgB,IAAuBJ,GAAQ,+BAA+B,WAC5E,IAAIK,EAAoBL,EAAMD,MAAMM,kBAEpCL,EAAMM,wBAAwB,CAC5BlE,SAAUiE,EACVhE,QAAS,CACPkE,yBAA0BP,EAAMQ,kBAChCC,wBAAyBT,EAAMU,iBAC/BC,iBAAkBX,EAAMY,0BACxBC,gBAAiBb,EAAMc,yBACvBC,sBAAuBf,EAAMgB,eAC7BC,qBAAsBjB,EAAMkB,cAC5BC,cAAenB,EAAMoB,uBACrBC,aAAcrB,EAAMsB,4BAK1B,IAAgB,IAAuBtB,GAAQ,6BAA6B,SAAUuB,GACpFvB,EAAMwB,oBAAsBD,KAG9B,IAAgB,IAAuBvB,GAAQ,aAAa,SAAUyB,GAIhEA,EAAMpO,SAAW2M,EAAMwB,qBACzBxB,EAAM0B,kBAAkBD,EAAMpO,WAIlC,IAAIsO,EAA+B,IAAI,EAAkC,CACvE5K,UAAWgJ,EAAM6B,YACjBnK,eAAgB,SAAwBmC,GACtC,OAAOkG,EAAK+B,gBAAgB9B,EAAM+B,YAA3BhC,CAAwClG,IAEjDlC,kBAAmBoI,EAAKiC,wBAAwBhC,KAE9CiC,EAA4B,IAAI,EAAkC,CACpEjL,UAAWgJ,EAAMkC,SACjBxK,eAAgB,SAAwBmC,GACtC,OAAOkG,EAAK+B,gBAAgB9B,EAAMmC,UAA3BpC,CAAsClG,IAE/ClC,kBAAmBoI,EAAKqC,qBAAqBpC,KAiC/C,OA/BAC,EAAMoC,MAAQ,CACZC,cAAe,CACbV,6BAA8BA,EAC9BK,0BAA2BA,EAC3BM,gBAAiBvC,EAAM+B,YACvBS,cAAexC,EAAMmC,UACrBM,gBAAiBzC,EAAM6B,YACvBa,aAAc1C,EAAMkC,SACpBS,iBAAuC,IAAtB3C,EAAMI,YACvBwC,mBAAoB5C,EAAM6C,eAC1BC,gBAAiB9C,EAAM+C,YACvBC,cAAe,EACfC,uBAAuB,GAEzB7C,aAAa,EACb8C,0BEnLgC,EFoLhCC,wBEpLgC,EFqLhCxQ,WAAY,EACZE,UAAW,EACXuQ,2BAA4B,KAC5B/C,uBAAuB,GAGrBL,EAAM+C,YAAc,IACtB9C,EAAMoD,kBAAoBpD,EAAMqD,wBAAwBtD,EAAOC,EAAMoC,QAGnErC,EAAM6C,eAAiB,IACzB5C,EAAMsD,mBAAqBtD,EAAMuD,yBAAyBxD,EAAOC,EAAMoC,QAGlEpC,EA4iCT,OA3rCA,IAAUF,EAAMD,GAsJhB,IAAaC,EAAM,CAAC,CAClBhI,IAAK,mBACLC,MAAO,WACL,IAAIjB,EAAOoF,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,GAC3EsH,EAAiB1M,EAAK2M,UACtBA,OAA+B,IAAnBD,EAA4BhQ,KAAKuM,MAAMvC,kBAAoBgG,EACvEE,EAAmB5M,EAAK6M,YACxBA,OAAmC,IAArBD,EAA8BlQ,KAAKuM,MAAM6C,eAAiBc,EACxEE,EAAgB9M,EAAK+M,SACrBA,OAA6B,IAAlBD,EAA2BpQ,KAAKuM,MAAM+C,YAAcc,EAE/DE,EAAcvE,EAAc,GAAI/L,KAAKuM,MAAO,CAC9CvC,kBAAmBiG,EACnBb,eAAgBe,EAChBb,YAAae,IAGf,MAAO,CACLnR,WAAYc,KAAK+P,yBAAyBO,GAC1ClR,UAAWY,KAAK6P,wBAAwBS,MAO3C,CACDhM,IAAK,qBACLC,MAAO,WACL,OAAOvE,KAAK4O,MAAMC,cAAcL,0BAA0BrI,iBAM3D,CACD7B,IAAK,uBACLC,MAAO,WACL,OAAOvE,KAAK4O,MAAMC,cAAcV,6BAA6BhI,iBAO9D,CACD7B,IAAK,oBACLC,MAAO,SAA2BC,GAChC,IAAI+L,EAAmB/L,EAAMtF,WACzBsR,OAAuC,IAArBD,EAA8B,EAAIA,EACpDE,EAAkBjM,EAAMpF,UACxBsR,OAAqC,IAApBD,EAA6B,EAAIA,EAItD,KAAIC,EAAiB,GAArB,CAKA1Q,KAAK2Q,uBAEL,IAAIC,EAAc5Q,KAAKuM,MACnBsE,EAAaD,EAAYC,WACzBC,EAAYF,EAAYE,UACxBrR,EAASmR,EAAYnR,OACrBF,EAAQqR,EAAYrR,MACpBsP,EAAgB7O,KAAK4O,MAAMC,cAK3BU,EAAgBV,EAAcU,cAC9BwB,EAAkBlC,EAAcL,0BAA0BrI,eAC1D6K,EAAoBnC,EAAcV,6BAA6BhI,eAC/DjH,EAAa6G,KAAKE,IAAIF,KAAKC,IAAI,EAAGgL,EAAoBzR,EAAQgQ,GAAgBiB,GAC9EpR,EAAY2G,KAAKE,IAAIF,KAAKC,IAAI,EAAG+K,EAAkBtR,EAAS8P,GAAgBmB,GAKhF,GAAI1Q,KAAK4O,MAAM1P,aAAeA,GAAcc,KAAK4O,MAAMxP,YAAcA,EAAW,CAG9E,IAEI6R,EAAW,CACbtE,aAAa,EACb8C,0BAJ8BvQ,IAAec,KAAK4O,MAAM1P,WAAaA,EAAac,KAAK4O,MAAM1P,WE9RjE,GADC,EF+RoIc,KAAK4O,MAAMa,0BAK5KC,wBAJ4BtQ,IAAcY,KAAK4O,MAAMxP,UAAYA,EAAYY,KAAK4O,MAAMxP,UE/R5D,GADC,EFgS8HY,KAAK4O,MAAMc,wBAKtKC,2BAA4BvD,GAGzByE,IACHI,EAAS7R,UAAYA,GAGlB0R,IACHG,EAAS/R,WAAaA,GAGxB+R,EAASrE,uBAAwB,EACjC5M,KAAK0M,SAASuE,GAGhBjR,KAAKkR,wBAAwB,CAC3BhS,WAAYA,EACZE,UAAWA,EACX4R,kBAAmBA,EACnBD,gBAAiBA,OAWpB,CACDzM,IAAK,gCACLC,MAAO,SAAuCa,GAC5C,IAAI+K,EAAc/K,EAAM+K,YACpBE,EAAWjL,EAAMiL,SACrBrQ,KAAKmR,+BAAgF,iBAAxCnR,KAAKmR,+BAA8CpL,KAAKE,IAAIjG,KAAKmR,+BAAgChB,GAAeA,EAC7JnQ,KAAKoR,4BAA0E,iBAArCpR,KAAKoR,4BAA2CrL,KAAKE,IAAIjG,KAAKoR,4BAA6Bf,GAAYA,IAQlJ,CACD/L,IAAK,kBACLC,MAAO,WACL,IAAI8M,EAAerR,KAAKuM,MACpB6B,EAAciD,EAAajD,YAC3BK,EAAW4C,EAAa5C,SACxBI,EAAgB7O,KAAK4O,MAAMC,cAC/BA,EAAcV,6BAA6BvI,yBAAyBwI,EAAc,GAClFS,EAAcL,0BAA0B5I,yBAAyB6I,EAAW,KAQ7E,CACDnK,IAAK,oBACLC,MAAO,WACL,IAAI2D,EAAQQ,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,GAC5E4I,EAAoBpJ,EAAMiI,YAC1BA,OAAoC,IAAtBmB,EAA+B,EAAIA,EACjDC,EAAiBrJ,EAAMmI,SACvBA,OAA8B,IAAnBkB,EAA4B,EAAIA,EAE3CC,EAAexR,KAAKuM,MACpB6C,EAAiBoC,EAAapC,eAC9BE,EAAckC,EAAalC,YAC3BT,EAAgB7O,KAAK4O,MAAMC,cAC/BA,EAAcV,6BAA6B/F,UAAU+H,GACrDtB,EAAcL,0BAA0BpG,UAAUiI,GAIlDrQ,KAAKyR,yBAA2BrC,GAAkB,IElXlB,IFkXwBpP,KAAK4O,MAAMa,0BAAyDU,GAAef,EAAiBe,GAAef,GAC3KpP,KAAK0R,wBAA0BpC,GAAe,IEnXd,IFmXoBtP,KAAK4O,MAAMc,wBAAuDW,GAAYf,EAAce,GAAYf,GAG5JtP,KAAK2R,YAAc,GACnB3R,KAAK4R,WAAa,GAClB5R,KAAK6R,gBAMN,CACDvN,IAAK,eACLC,MAAO,SAAsB8D,GAC3B,IAAI8H,EAAc9H,EAAM8H,YACpBE,EAAWhI,EAAMgI,SACjBjC,EAAcpO,KAAKuM,MAAM6B,YACzB7B,EAAQvM,KAAKuM,MAGb6B,EAAc,QAAqBrN,IAAhBoP,GACrBnQ,KAAK8R,mCAAmC/F,EAAc,GAAIQ,EAAO,CAC/D6C,eAAgBe,UAIHpP,IAAbsP,GACFrQ,KAAK+R,+BAA+BhG,EAAc,GAAIQ,EAAO,CAC3D+C,YAAae,OAIlB,CACD/L,IAAK,oBACLC,MAAO,WACL,IAAIyN,EAAehS,KAAKuM,MACpB0F,EAAmBD,EAAaC,iBAChCxS,EAASuS,EAAavS,OACtBP,EAAa8S,EAAa9S,WAC1BkQ,EAAiB4C,EAAa5C,eAC9BhQ,EAAY4S,EAAa5S,UACzBkQ,EAAc0C,EAAa1C,YAC3B/P,EAAQyS,EAAazS,MACrBsP,EAAgB7O,KAAK4O,MAAMC,cAsB/B,GApBA7O,KAAK4P,kBAAoB,EACzB5P,KAAK8P,mBAAqB,EAG1B9P,KAAKkS,6BAIArD,EAAcW,uBACjBxP,KAAK0M,UAAS,SAAUyF,GACtB,IAAIC,EAAcrG,EAAc,GAAIoG,EAAW,CAC7CvF,uBAAuB,IAKzB,OAFAwF,EAAYvD,cAAcU,cAAgB0C,IAC1CG,EAAYvD,cAAcW,uBAAwB,EAC3C4C,KAIe,iBAAflT,GAA2BA,GAAc,GAA0B,iBAAdE,GAA0BA,GAAa,EAAG,CACxG,IAAIgT,EAAc9F,EAAK+F,gCAAgC,CACrDF,UAAWnS,KAAK4O,MAChB1P,WAAYA,EACZE,UAAWA,IAGTgT,IACFA,EAAYxF,uBAAwB,EACpC5M,KAAK0M,SAAS0F,IAKdpS,KAAKgO,sBAGHhO,KAAKgO,oBAAoB9O,aAAec,KAAK4O,MAAM1P,aACrDc,KAAKgO,oBAAoB9O,WAAac,KAAK4O,MAAM1P,YAG/Cc,KAAKgO,oBAAoB5O,YAAcY,KAAK4O,MAAMxP,YACpDY,KAAKgO,oBAAoB5O,UAAYY,KAAK4O,MAAMxP,YAMpD,IAAIkT,EAAuB7S,EAAS,GAAKF,EAAQ,EAE7C6P,GAAkB,GAAKkD,GACzBtS,KAAK8R,qCAGHxC,GAAe,GAAKgD,GACtBtS,KAAK+R,iCAIP/R,KAAKuS,8BAGLvS,KAAKkR,wBAAwB,CAC3BhS,WAAYA,GAAc,EAC1BE,UAAWA,GAAa,EACxB4R,kBAAmBnC,EAAcV,6BAA6BhI,eAC9D4K,gBAAiBlC,EAAcL,0BAA0BrI,iBAG3DnG,KAAKwS,wCAQN,CACDlO,IAAK,qBACLC,MAAO,SAA4BkO,EAAWN,GAC5C,IAAIO,EAAS1S,KAET2S,EAAe3S,KAAKuM,MACpBsE,EAAa8B,EAAa9B,WAC1BC,EAAY6B,EAAa7B,UACzB1C,EAAcuE,EAAavE,YAC3B3O,EAASkT,EAAalT,OACtBgP,EAAWkE,EAAalE,SACxBzE,EAAoB2I,EAAa3I,kBACjCoF,EAAiBuD,EAAavD,eAC9BE,EAAcqD,EAAarD,YAC3B/P,EAAQoT,EAAapT,MACrBqT,EAAc5S,KAAK4O,MACnB1P,EAAa0T,EAAY1T,WACzByQ,EAA6BiD,EAAYjD,2BACzCvQ,EAAYwT,EAAYxT,UACxByP,EAAgB+D,EAAY/D,cAGhC7O,KAAKkS,6BAKL,IAAIW,EAAwCzE,EAAc,GAA+B,IAA1BqE,EAAUrE,aAAqBK,EAAW,GAA4B,IAAvBgE,EAAUhE,SAMpHkB,IAA+BvD,KAG5B0E,GAAa5R,GAAc,IAAMA,IAAec,KAAKgO,oBAAoB9O,YAAc2T,KAC1F7S,KAAKgO,oBAAoB9O,WAAaA,IAGnC2R,GAAczR,GAAa,IAAMA,IAAcY,KAAKgO,oBAAoB5O,WAAayT,KACxF7S,KAAKgO,oBAAoB5O,UAAYA,IAOzC,IAAI6K,GAAiD,IAApBwI,EAAUlT,OAAoC,IAArBkT,EAAUhT,SAAiBA,EAAS,GAAKF,EAAQ,EAqD3G,GAlDIS,KAAKyR,0BACPzR,KAAKyR,0BAA2B,EAEhCzR,KAAK8R,mCAAmC9R,KAAKuM,QAE7C/C,EAAwB,CACtBC,2BAA4BoF,EAAcV,6BAC1CzE,mBAAoB+I,EAAUrE,YAC9BzE,iBAAkB8I,EAAUnE,YAC5B1E,0BAA2B6I,EAAUzI,kBACrCH,sBAAuB4I,EAAUrD,eACjCtF,aAAc2I,EAAUlT,MACxBwK,aAAc7K,EACd8K,kBAAmBA,EACnBlG,cAAesL,EACfpK,KAAMzF,EACN0K,0BAA2BA,EAC3BC,0BAA2B,WACzB,OAAOwI,EAAOZ,mCAAmCY,EAAOnG,UAK1DvM,KAAK0R,yBACP1R,KAAK0R,yBAA0B,EAE/B1R,KAAK+R,+BAA+B/R,KAAKuM,QAEzC/C,EAAwB,CACtBC,2BAA4BoF,EAAcL,0BAC1C9E,mBAAoB+I,EAAUhE,SAC9B9E,iBAAkB8I,EAAU/D,UAC5B9E,0BAA2B6I,EAAUzI,kBACrCH,sBAAuB4I,EAAUnD,YACjCxF,aAAc2I,EAAUhT,OACxBsK,aAAc3K,EACd4K,kBAAmBA,EACnBlG,cAAewL,EACftK,KAAMvF,EACNwK,0BAA2BA,EAC3BC,0BAA2B,WACzB,OAAOwI,EAAOX,+BAA+BW,EAAOnG,UAM1DvM,KAAKuS,8BAGDrT,IAAeiT,EAAUjT,YAAcE,IAAc+S,EAAU/S,UAAW,CAC5E,IAAI2R,EAAkBlC,EAAcL,0BAA0BrI,eAC1D6K,EAAoBnC,EAAcV,6BAA6BhI,eAEnEnG,KAAKkR,wBAAwB,CAC3BhS,WAAYA,EACZE,UAAWA,EACX4R,kBAAmBA,EACnBD,gBAAiBA,IAIrB/Q,KAAKwS,wCAEN,CACDlO,IAAK,uBACLC,MAAO,WACDvE,KAAKyM,gCACP,EAAuBzM,KAAKyM,kCAU/B,CACDnI,IAAK,SACLC,MAAO,WACL,IAAIuO,EAAe9S,KAAKuM,MACpBwG,EAAqBD,EAAaC,mBAClClC,EAAaiC,EAAajC,WAC1BC,EAAYgC,EAAahC,UACzBhR,EAAYgT,EAAahT,UACzBkT,EAAiBF,EAAaE,eAC9BC,EAAgBH,EAAaG,cAC7BC,EAAiBJ,EAAaI,eAC9BzT,EAASqT,EAAarT,OACtBjB,EAAKsU,EAAatU,GAClB2U,EAAoBL,EAAaK,kBACjCC,EAAON,EAAaM,KACpB9T,EAAQwT,EAAaxT,MACrB+T,EAAWP,EAAaO,SACxB9T,EAAQuT,EAAavT,MACrB+T,EAAetT,KAAK4O,MACpBC,EAAgByE,EAAazE,cAC7BjC,EAAwB0G,EAAa1G,sBAErCD,EAAc3M,KAAKuT,eAEnBC,EAAY,CACdC,UAAW,aACXC,UAAW,MACXjU,OAAQoR,EAAa,OAASpR,EAC9BkC,SAAU,WACVpC,MAAOuR,EAAY,OAASvR,EAC5BoU,wBAAyB,QACzBC,WAAY,aAGVhH,IACF5M,KAAK2R,YAAc,IAKhB3R,KAAK4O,MAAMjC,aACd3M,KAAK6T,mBAIP7T,KAAK8T,2BAA2B9T,KAAKuM,MAAOvM,KAAK4O,OAEjD,IAAIoC,EAAoBnC,EAAcV,6BAA6BhI,eAC/D4K,EAAkBlC,EAAcL,0BAA0BrI,eAI1D4N,EAAwBhD,EAAkBtR,EAASoP,EAAcU,cAAgB,EACjFyE,EAA0BhD,EAAoBzR,EAAQsP,EAAcU,cAAgB,EAEpFyE,IAA4BhU,KAAKiU,0BAA4BF,IAA0B/T,KAAKkU,yBAC9FlU,KAAKiU,yBAA2BD,EAChChU,KAAKkU,uBAAyBH,EAC9B/T,KAAKmU,2BAA4B,GAQnCX,EAAUY,UAAYpD,EAAoB+C,GAAyBxU,EAAQ,SAAW,OACtFiU,EAAUa,UAAYtD,EAAkBiD,GAA2BvU,EAAS,SAAW,OACvF,IAAI6U,EAAoBtU,KAAKuU,mBACzBC,EAAqD,IAA7BF,EAAkBpT,QAAgBzB,EAAS,GAAKF,EAAQ,EACpF,OAAO,gBAAoB,MAAO,IAAS,CACzCwO,IAAK/N,KAAKyU,2BACTzB,EAAgB,CACjB,aAAchT,KAAKuM,MAAM,cACzB,gBAAiBvM,KAAKuM,MAAM,iBAC5BzM,UAAW,kBAAK,yBAA0BA,GAC1CtB,GAAIA,EACJkW,SAAU1U,KAAK2U,UACfvB,KAAMA,EACN9T,MAAOyM,EAAc,GAAIyH,EAAW,GAAIlU,GACxC+T,SAAUA,IACRiB,EAAkBpT,OAAS,GAAK,gBAAoB,MAAO,CAC7DpB,UAAW,+CACXsT,KAAMH,EACN3T,MAAOyM,EAAc,CACnBxM,MAAOwT,EAAqB,OAAS/B,EACrCvR,OAAQsR,EACR6D,SAAU5D,EACV6D,UAAW9D,EACX+D,SAAU,SACVC,cAAepI,EAAc,OAAS,GACtChL,SAAU,YACTuR,IACFoB,GAAoBE,GAAyBrB,OAIjD,CACD7O,IAAK,6BACLC,MAAO,WACL,IAAIgI,EAAQ7D,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK1I,KAAKuM,MACjFqC,EAAQlG,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK1I,KAAK4O,MACjFoG,EAAezI,EAAMyI,aACrBC,EAAoB1I,EAAM0I,kBAC1B7G,EAAc7B,EAAM6B,YACpB8G,EAA2B3I,EAAM2I,yBACjCzV,EAAS8M,EAAM9M,OACf0V,EAAsB5I,EAAM4I,oBAC5BC,EAAwB7I,EAAM6I,sBAC9BC,EAAmB9I,EAAM8I,iBACzB5G,EAAWlC,EAAMkC,SACjBlP,EAAQgN,EAAMhN,MACd+V,EAAoB/I,EAAM+I,kBAC1B7F,EAA4Bb,EAAMa,0BAClCC,EAA0Bd,EAAMc,wBAChCb,EAAgBD,EAAMC,cACtBzP,EAAYY,KAAK4P,kBAAoB,EAAI5P,KAAK4P,kBAAoBhB,EAAMxP,UACxEF,EAAac,KAAK8P,mBAAqB,EAAI9P,KAAK8P,mBAAqBlB,EAAM1P,WAE3EyN,EAAc3M,KAAKuT,aAAahH,EAAOqC,GAI3C,GAFA5O,KAAKuU,mBAAqB,GAEtB9U,EAAS,GAAKF,EAAQ,EAAG,CAC3B,IAAIgW,EAAuB1G,EAAcV,6BAA6BhG,oBAAoB,CACxF5C,cAAehG,EACfwF,OAAQ7F,IAENsW,EAAoB3G,EAAcL,0BAA0BrG,oBAAoB,CAClF5C,cAAe9F,EACfsF,OAAQ3F,IAENqW,EAA6B5G,EAAcV,6BAA6BuH,oBAAoB,CAC9FnQ,cAAehG,EACfwF,OAAQ7F,IAENyW,EAA2B9G,EAAcL,0BAA0BkH,oBAAoB,CACzFnQ,cAAe9F,EACfsF,OAAQ3F,IAGVY,KAAKoN,0BAA4BmI,EAAqBlP,MACtDrG,KAAKsN,yBAA2BiI,EAAqBhP,KACrDvG,KAAK4N,uBAAyB4H,EAAkBnP,MAChDrG,KAAK8N,sBAAwB0H,EAAkBjP,KAC/C,IAAIqP,EAAwBR,EAAsB,CAChD1B,UAAW,aACXnQ,UAAW6K,EACXyH,mBAAoBV,EACpBW,gBAAiBrG,EACjBsG,WAAkD,iBAA/BR,EAAqBlP,MAAqBkP,EAAqBlP,MAAQ,EAC1F2P,UAAgD,iBAA9BT,EAAqBhP,KAAoBgP,EAAqBhP,MAAQ,IAEtF0P,EAAqBb,EAAsB,CAC7C1B,UAAW,WACXnQ,UAAWkL,EACXoH,mBAAoBR,EACpBS,gBAAiBpG,EACjBqG,WAA+C,iBAA5BP,EAAkBnP,MAAqBmP,EAAkBnP,MAAQ,EACpF2P,UAA6C,iBAA3BR,EAAkBjP,KAAoBiP,EAAkBjP,MAAQ,IAGhF4G,EAAmByI,EAAsBM,mBACzC7I,EAAkBuI,EAAsBO,kBACxCxI,EAAgBsI,EAAmBC,mBACnCrI,EAAeoI,EAAmBE,kBAEtC,GAAIjB,EAA0B,CAK5B,IAAKA,EAAyBkB,iBAC5B,IAAK,IAAI/F,EAAW1C,EAAe0C,GAAYxC,EAAcwC,IAC3D,IAAK6E,EAAyBmB,IAAIhG,EAAU,GAAI,CAC9ClD,EAAmB,EACnBE,EAAkBe,EAAc,EAChC,MASN,IAAK8G,EAAyBoB,gBAC5B,IAAK,IAAInG,EAAchD,EAAkBgD,GAAe9C,EAAiB8C,IACvE,IAAK+E,EAAyBmB,IAAI,EAAGlG,GAAc,CACjDxC,EAAgB,EAChBE,EAAeY,EAAW,EAC1B,OAMRzO,KAAKuU,mBAAqBU,EAAkB,CAC1CsB,UAAWvW,KAAK4R,WAChBoD,aAAcA,EACd7G,6BAA8BU,EAAcV,6BAC5ChB,iBAAkBA,EAClBE,gBAAiBA,EACjB6H,yBAA0BA,EAC1BO,2BAA4BA,EAC5B9I,YAAaA,EACb2I,kBAAmBA,EACnBkB,OAAQxW,KACRwO,0BAA2BK,EAAcL,0BACzCb,cAAeA,EACfE,aAAcA,EACd3O,WAAYA,EACZE,UAAWA,EACXqX,WAAYzW,KAAK2R,YACjBgE,yBAA0BA,EAC1BJ,qBAAsBA,EACtBC,kBAAmBA,IAGrBxV,KAAKgN,kBAAoBG,EACzBnN,KAAKkN,iBAAmBG,EACxBrN,KAAKwN,eAAiBG,EACtB3N,KAAK0N,cAAgBG,KASxB,CACDvJ,IAAK,uBACLC,MAAO,WACL,IAAImS,EAA6B1W,KAAKuM,MAAMmK,2BAExC1W,KAAKyM,gCACP,EAAuBzM,KAAKyM,gCAG9BzM,KAAKyM,+BAAiC,EAAwBzM,KAAK2W,6BAA8BD,KAElG,CACDpS,IAAK,6BAMLC,MAAO,WACL,GAAmD,iBAAxCvE,KAAKmR,gCAA2F,iBAArCnR,KAAKoR,4BAA0C,CACnH,IAAIjB,EAAcnQ,KAAKmR,+BACnBd,EAAWrQ,KAAKoR,4BACpBpR,KAAKmR,+BAAiC,KACtCnR,KAAKoR,4BAA8B,KACnCpR,KAAK4W,kBAAkB,CACrBzG,YAAaA,EACbE,SAAUA,OAIf,CACD/L,IAAK,0BACLC,MAAO,SAAiC+D,GACtC,IAAIuO,EAAS7W,KAETd,EAAaoJ,EAAMpJ,WACnBE,EAAYkJ,EAAMlJ,UAClB4R,EAAoB1I,EAAM0I,kBAC1BD,EAAkBzI,EAAMyI,gBAE5B/Q,KAAK8W,kBAAkB,CACrBlO,SAAU,SAAkBL,GAC1B,IAAIrJ,EAAaqJ,EAAMrJ,WACnBE,EAAYmJ,EAAMnJ,UAClB2X,EAAeF,EAAOtK,MACtB9M,EAASsX,EAAatX,QAG1BiV,EAFeqC,EAAarC,UAEnB,CACPsC,aAAcvX,EACdwX,YAHUF,EAAaxX,MAIvBF,aAAc0R,EACd7R,WAAYA,EACZE,UAAWA,EACXD,YAAa6R,KAGjBnI,QAAS,CACP3J,WAAYA,EACZE,UAAWA,OAIhB,CACDkF,IAAK,eACLC,MAAO,WACL,IAAIgI,EAAQ7D,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK1I,KAAKuM,MACjFqC,EAAQlG,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK1I,KAAK4O,MAGrF,OAAO7F,OAAOmO,eAAe5W,KAAKiM,EAAO,eAAiB4K,QAAQ5K,EAAMI,aAAewK,QAAQvI,EAAMjC,eAEtG,CACDrI,IAAK,sCACLC,MAAO,WACL,GAAIvE,KAAKmU,0BAA2B,CAClC,IAAIiD,EAA4BpX,KAAKuM,MAAM6K,0BAC3CpX,KAAKmU,2BAA4B,EACjCiD,EAA0B,CACxBC,WAAYrX,KAAKiU,yBAA2B,EAC5CjP,KAAMhF,KAAK4O,MAAMC,cAAcU,cAC/B+H,SAAUtX,KAAKkU,uBAAyB,OAI7C,CACD5P,IAAK,mBAMLC,MAAO,SAA0BgT,GAC/B,IAAIrY,EAAaqY,EAAMrY,WACnBE,EAAYmY,EAAMnY,UAElBgT,EAAc9F,EAAK+F,gCAAgC,CACrDF,UAAWnS,KAAK4O,MAChB1P,WAAYA,EACZE,UAAWA,IAGTgT,IACFA,EAAYxF,uBAAwB,EACpC5M,KAAK0M,SAAS0F,MAGjB,CACD9N,IAAK,2BACLC,MAAO,WACL,IAAIgI,EAAQ7D,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK1I,KAAKuM,MACjFqC,EAAQlG,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK1I,KAAK4O,MACrF,OAAOtC,EAAKyD,yBAAyBxD,EAAOqC,KAE7C,CACDtK,IAAK,qCACLC,MAAO,WACL,IAAIgI,EAAQ7D,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK1I,KAAKuM,MACjFqC,EAAQlG,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK1I,KAAK4O,MAEjFwD,EAAc9F,EAAKkL,2CAA2CjL,EAAOqC,GAErEwD,IACFA,EAAYxF,uBAAwB,EACpC5M,KAAK0M,SAAS0F,MAGjB,CACD9N,IAAK,0BACLC,MAAO,WACL,IAAIgI,EAAQ7D,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK1I,KAAKuM,MACjFqC,EAAQlG,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK1I,KAAK4O,MACrF,OAAOtC,EAAKuD,wBAAwBtD,EAAOqC,KAE5C,CACDtK,IAAK,mBACLC,MAAO,WACL,IAAIkS,EAAazW,KAAK2R,YAClB4E,EAAYvW,KAAK4R,WACjB0D,EAAoBtV,KAAKuM,MAAM+I,kBAOnCtV,KAAK4R,WAAa,GAClB5R,KAAK2R,YAAc,GAEnB,IAAK,IAAItB,EAAWrQ,KAAKwN,eAAgB6C,GAAYrQ,KAAK0N,cAAe2C,IACvE,IAAK,IAAIF,EAAcnQ,KAAKgN,kBAAmBmD,GAAenQ,KAAKkN,iBAAkBiD,IAAe,CAClG,IAAI7L,EAAM,GAAGM,OAAOyL,EAAU,KAAKzL,OAAOuL,GAC1CnQ,KAAK2R,YAAYrN,GAAOmS,EAAWnS,GAE/BgR,IACFtV,KAAK4R,WAAWtN,GAAOiS,EAAUjS,OAKxC,CACDA,IAAK,iCACLC,MAAO,WACL,IAAIgI,EAAQ7D,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK1I,KAAKuM,MACjFqC,EAAQlG,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK1I,KAAK4O,MAEjFwD,EAAc9F,EAAKmL,uCAAuClL,EAAOqC,GAEjEwD,IACFA,EAAYxF,uBAAwB,EACpC5M,KAAK0M,SAAS0F,OAGhB,CAAC,CACH9N,IAAK,2BACLC,MAAO,SAAkCmT,EAAWvF,GAClD,IAAIlB,EAAW,GAEe,IAA1ByG,EAAUtJ,aAA8C,IAAzB+D,EAAUjT,YAA2C,IAAvBwY,EAAUjJ,UAA0C,IAAxB0D,EAAU/S,WACrG6R,EAAS/R,WAAa,EACtB+R,EAAS7R,UAAY,IAEZsY,EAAUxY,aAAeiT,EAAUjT,YAAcwY,EAAUtI,eAAiB,GAAKsI,EAAUtY,YAAc+S,EAAU/S,WAAasY,EAAUpI,YAAc,IACjKvG,OAAO4O,OAAO1G,EAAU3E,EAAK+F,gCAAgC,CAC3DF,UAAWA,EACXjT,WAAYwY,EAAUxY,WACtBE,UAAWsY,EAAUtY,aAIzB,IAgCIwY,EACAC,EAjCAhJ,EAAgBsD,EAAUtD,cAkF9B,OAhFAoC,EAASrE,uBAAwB,EAE7B8K,EAAUpJ,cAAgBO,EAAcC,iBAAmB4I,EAAUhJ,YAAcG,EAAcE,gBAEnGkC,EAASrE,uBAAwB,GAGnCiC,EAAcV,6BAA6B5G,UAAU,CACnDhE,UAAWmU,EAAUtJ,YACrBlK,kBAAmBoI,EAAKiC,wBAAwBmJ,GAChDzT,eAAgBqI,EAAK+B,gBAAgBqJ,EAAUpJ,eAEjDO,EAAcL,0BAA0BjH,UAAU,CAChDhE,UAAWmU,EAAUjJ,SACrBvK,kBAAmBoI,EAAKqC,qBAAqB+I,GAC7CzT,eAAgBqI,EAAK+B,gBAAgBqJ,EAAUhJ,aAGX,IAAlCG,EAAcG,iBAAwD,IAA/BH,EAAcI,eACvDJ,EAAcG,gBAAkB,EAChCH,EAAcI,aAAe,GAI3ByI,EAAU7G,aAAwC,IAA1B6G,EAAU/K,cAA2D,IAAlCkC,EAAcK,iBAC3EnG,OAAO4O,OAAO1G,EAAU,CACtBtE,aAAa,IAMjBtJ,EAAkD,CAChDE,UAAWsL,EAAcG,gBACzBxL,SAAmD,iBAAlCqL,EAAcC,gBAA+BD,EAAcC,gBAAkB,KAC9FrL,wBAAyB,WACvB,OAAOoL,EAAcV,6BAA6B/F,UAAU,IAE9D1E,6BAA8BgU,EAC9B/T,eAAgB+T,EAAUtJ,YAC1BxK,aAA+C,iBAA1B8T,EAAUpJ,YAA2BoJ,EAAUpJ,YAAc,KAClFzK,kBAAmB6T,EAAUtI,eAC7BtL,cAAe+K,EAAcM,mBAC7BpL,mCAAoC,WAClC6T,EAActL,EAAKkL,2CAA2CE,EAAWvF,MAG7E9O,EAAkD,CAChDE,UAAWsL,EAAcI,aACzBzL,SAAiD,iBAAhCqL,EAAcE,cAA6BF,EAAcE,cAAgB,KAC1FtL,wBAAyB,WACvB,OAAOoL,EAAcL,0BAA0BpG,UAAU,IAE3D1E,6BAA8BgU,EAC9B/T,eAAgB+T,EAAUjJ,SAC1B7K,aAA6C,iBAAxB8T,EAAUhJ,UAAyBgJ,EAAUhJ,UAAY,KAC9E7K,kBAAmB6T,EAAUpI,YAC7BxL,cAAe+K,EAAcQ,gBAC7BtL,mCAAoC,WAClC8T,EAAcvL,EAAKmL,uCAAuCC,EAAWvF,MAGzEtD,EAAcG,gBAAkB0I,EAAUtJ,YAC1CS,EAAcC,gBAAkB4I,EAAUpJ,YAC1CO,EAAcK,iBAA4C,IAA1BwI,EAAU/K,YAC1CkC,EAAcI,aAAeyI,EAAUjJ,SACvCI,EAAcE,cAAgB2I,EAAUhJ,UACxCG,EAAcM,mBAAqBuI,EAAUtI,eAC7CP,EAAcQ,gBAAkBqI,EAAUpI,YAE1CT,EAAcU,cAAgBmI,EAAUzF,wBAEJlR,IAAhC8N,EAAcU,eAChBV,EAAcW,uBAAwB,EACtCX,EAAcU,cAAgB,GAE9BV,EAAcW,uBAAwB,EAGxCyB,EAASpC,cAAgBA,EAClB9C,EAAc,GAAIkF,EAAU,GAAI2G,EAAa,GAAIC,KAEzD,CACDvT,IAAK,0BACLC,MAAO,SAAiCgI,GACtC,MAAoC,iBAAtBA,EAAM+B,YAA2B/B,EAAM+B,YAAc/B,EAAMuL,sBAE1E,CACDxT,IAAK,uBACLC,MAAO,SAA8BgI,GACnC,MAAkC,iBAApBA,EAAMmC,UAAyBnC,EAAMmC,UAAYnC,EAAMwL,mBAEtE,CACDzT,IAAK,kCAMLC,MAAO,SAAyCyT,GAC9C,IAAI7F,EAAY6F,EAAM7F,UAClBjT,EAAa8Y,EAAM9Y,WACnBE,EAAY4Y,EAAM5Y,UAClB6R,EAAW,CACbtB,2BAA4BvD,GAa9B,MAV0B,iBAAflN,GAA2BA,GAAc,IAClD+R,EAASxB,0BAA4BvQ,EAAaiT,EAAUjT,WEjoC9B,GADC,EFmoC/B+R,EAAS/R,WAAaA,GAGC,iBAAdE,GAA0BA,GAAa,IAChD6R,EAASvB,wBAA0BtQ,EAAY+S,EAAU/S,UEtoC3B,GADC,EFwoC/B6R,EAAS7R,UAAYA,GAGG,iBAAfF,GAA2BA,GAAc,GAAKA,IAAeiT,EAAUjT,YAAmC,iBAAdE,GAA0BA,GAAa,GAAKA,IAAc+S,EAAU/S,UAClK6R,EAGF,KAER,CACD3M,IAAK,kBACLC,MAAO,SAAyBA,GAC9B,MAAwB,mBAAVA,EAAuBA,EAAQ,WAC3C,OAAOA,KAGV,CACDD,IAAK,2BACLC,MAAO,SAAkCmT,EAAWvF,GAClD,IAAI/D,EAAcsJ,EAAUtJ,YACxB3O,EAASiY,EAAUjY,OACnBuK,EAAoB0N,EAAU1N,kBAC9BoF,EAAiBsI,EAAUtI,eAC3B7P,EAAQmY,EAAUnY,MAClBL,EAAaiT,EAAUjT,WACvB2P,EAAgBsD,EAAUtD,cAE9B,GAAIT,EAAc,EAAG,CACnB,IAAI6J,EAAc7J,EAAc,EAC5B3I,EAAc2J,EAAiB,EAAI6I,EAAclS,KAAKE,IAAIgS,EAAa7I,GACvE2B,EAAkBlC,EAAcL,0BAA0BrI,eAC1D+R,EAAgBrJ,EAAcW,uBAAyBuB,EAAkBtR,EAASoP,EAAcU,cAAgB,EACpH,OAAOV,EAAcV,6BAA6BnG,yBAAyB,CACzE1C,MAAO0E,EACPzE,cAAehG,EAAQ2Y,EACvB1S,cAAetG,EACfuG,YAAaA,IAIjB,OAAO,IAER,CACDnB,IAAK,6CACLC,MAAO,SAAoDmT,EAAWvF,GACpE,IAAIjT,EAAaiT,EAAUjT,WAEvBiZ,EAAuB7L,EAAKyD,yBAAyB2H,EAAWvF,GAEpE,MAAoC,iBAAzBgG,GAAqCA,GAAwB,GAAKjZ,IAAeiZ,EACnF7L,EAAK+F,gCAAgC,CAC1CF,UAAWA,EACXjT,WAAYiZ,EACZ/Y,WAAY,IAIT,KAER,CACDkF,IAAK,0BACLC,MAAO,SAAiCmT,EAAWvF,GACjD,IAAI1S,EAASiY,EAAUjY,OACnBgP,EAAWiJ,EAAUjJ,SACrBzE,EAAoB0N,EAAU1N,kBAC9BsF,EAAcoI,EAAUpI,YACxB/P,EAAQmY,EAAUnY,MAClBH,EAAY+S,EAAU/S,UACtByP,EAAgBsD,EAAUtD,cAE9B,GAAIJ,EAAW,EAAG,CAChB,IAAI2J,EAAW3J,EAAW,EACtBhJ,EAAc6J,EAAc,EAAI8I,EAAWrS,KAAKE,IAAImS,EAAU9I,GAC9D0B,EAAoBnC,EAAcV,6BAA6BhI,eAC/D+R,EAAgBrJ,EAAcW,uBAAyBwB,EAAoBzR,EAAQsP,EAAcU,cAAgB,EACrH,OAAOV,EAAcL,0BAA0BxG,yBAAyB,CACtE1C,MAAO0E,EACPzE,cAAe9F,EAASyY,EACxB1S,cAAepG,EACfqG,YAAaA,IAIjB,OAAO,IAER,CACDnB,IAAK,yCACLC,MAAO,SAAgDmT,EAAWvF,GAChE,IAAI/S,EAAY+S,EAAU/S,UAEtBiZ,EAAsB/L,EAAKuD,wBAAwB6H,EAAWvF,GAElE,MAAmC,iBAAxBkG,GAAoCA,GAAuB,GAAKjZ,IAAciZ,EAChF/L,EAAK+F,gCAAgC,CAC1CF,UAAWA,EACXjT,YAAa,EACbE,UAAWiZ,IAIR,OAIJ/L,EA5rCT,CA6rCE,iBAAsB,IAAgBjC,EAAQ,YAAqD,MAkLjGC,GAEJ,IAAgB,EAAM,eAAgB,CACpC,aAAc,OACd,iBAAiB,EACjByI,oBAAoB,EACpBlC,YAAY,EACZC,WAAW,EACXmE,kBGv6Ca,SAAkC3R,GA2B/C,IA1BA,IAAIiT,EAAYjT,EAAKiT,UACjBvB,EAAe1R,EAAK0R,aACpB7G,EAA+B7K,EAAK6K,6BACpChB,EAAmB7J,EAAK6J,iBACxBE,EAAkB/J,EAAK+J,gBACvB6H,EAA2B5R,EAAK4R,yBAChCO,EAA6BnS,EAAKmS,2BAClC9I,EAAcrJ,EAAKqJ,YACnB2I,EAAoBhS,EAAKgS,kBACzBkB,EAASlT,EAAKkT,OACdhI,EAA4BlL,EAAKkL,0BACjCb,EAAgBrK,EAAKqK,cACrBE,EAAevK,EAAKuK,aACpB4I,EAAanT,EAAKmT,WAClBd,EAA2BrS,EAAKqS,yBAChCJ,EAAuBjS,EAAKiS,qBAC5BC,EAAoBlS,EAAKkS,kBACzB8C,EAAgB,GAMhBC,EAAqBpK,EAA6BoK,sBAAwB/J,EAA0B+J,qBACpGC,GAAiB7L,IAAgB4L,EAE5BlI,EAAW1C,EAAe0C,GAAYxC,EAAcwC,IAG3D,IAFA,IAAIoI,EAAWjK,EAA0B5I,yBAAyByK,GAEzDF,EAAchD,EAAkBgD,GAAe9C,EAAiB8C,IAAe,CACtF,IAAIuI,EAAcvK,EAA6BvI,yBAAyBuK,GACpEwI,EAAYxI,GAAeoF,EAAqBlP,OAAS8J,GAAeoF,EAAqBhP,MAAQ8J,GAAYmF,EAAkBnP,OAASgK,GAAYmF,EAAkBjP,KAC1KjC,EAAM,GAAGM,OAAOyL,EAAU,KAAKzL,OAAOuL,GACtC7Q,OAAQ,EAERkZ,GAAiB/B,EAAWnS,GAC9BhF,EAAQmX,EAAWnS,GAIf4Q,IAA6BA,EAAyBmB,IAAIhG,EAAUF,GAItE7Q,EAAQ,CACNG,OAAQ,OACRmZ,KAAM,EACNjX,SAAU,WACVkX,IAAK,EACLtZ,MAAO,SAGTD,EAAQ,CACNG,OAAQgZ,EAASzT,KACjB4T,KAAMF,EAAY3T,OAAS0Q,EAC3B9T,SAAU,WACVkX,IAAKJ,EAAS1T,OAAS4Q,EACvBpW,MAAOmZ,EAAY1T,MAErByR,EAAWnS,GAAOhF,GAItB,IAAIwZ,EAAqB,CACvB3I,YAAaA,EACbxD,YAAaA,EACbgM,UAAWA,EACXrU,IAAKA,EACLkS,OAAQA,EACRnG,SAAUA,EACV/Q,MAAOA,GAELyZ,OAAe,GAWdzD,IAAqB3I,GAAiB8I,GAA+BE,EAQxEoD,EAAe/D,EAAa8D,IAPvBvC,EAAUjS,KACbiS,EAAUjS,GAAO0Q,EAAa8D,IAGhCC,EAAexC,EAAUjS,IAMP,MAAhByU,IAAyC,IAAjBA,GAQ5BT,EAAcvV,KAAKgW,GAIvB,OAAOT,GH6zCPrF,cAAe,WACfC,eAAgB,GAChB4E,oBAAqB,IACrBC,iBAAkB,GAClB9F,iBAAkB,UAClBkB,kBAv4Ce,WACf,OAAO,MAu4CPuB,SAAU,aACV0C,0BAA2B,aAC3BvK,kBAAmB,aACnBsI,oBAAqB,EACrBC,sBE76Ca,SAAsC9R,GACnD,IAAIC,EAAYD,EAAKC,UACjBsS,EAAqBvS,EAAKuS,mBAC1BC,EAAkBxS,EAAKwS,gBACvBC,EAAazS,EAAKyS,WAClBC,EAAY1S,EAAK0S,UAErB,OAfoC,IAehCF,EACK,CACLI,mBAAoBnQ,KAAKC,IAAI,EAAG+P,GAChCI,kBAAmBpQ,KAAKE,IAAI1C,EAAY,EAAGyS,EAAYH,IAGlD,CACLK,mBAAoBnQ,KAAKC,IAAI,EAAG+P,EAAaF,GAC7CM,kBAAmBpQ,KAAKE,IAAI1C,EAAY,EAAGyS,KF+5C/CX,iBAAkB,GAClBjC,KAAM,OACNsD,2BA15CiD,IA25CjD1M,kBAAmB,OACnBoF,gBAAiB,EACjBE,aAAc,EACdhQ,MAAO,GACP+T,SAAU,EACViC,mBAAmB,IAGrB,mBAAS,GACM,QI17CA,SAAS,EAA6BhS,GACnD,IAAIC,EAAYD,EAAKC,UACjBsS,EAAqBvS,EAAKuS,mBAC1BC,EAAkBxS,EAAKwS,gBACvBC,EAAazS,EAAKyS,WAClBC,EAAY1S,EAAK0S,UAMrB,OAFAH,EAAqB9P,KAAKC,IAAI,EAAG6P,GAjBG,IAmBhCC,EACK,CACLI,mBAAoBnQ,KAAKC,IAAI,EAAG+P,EAAa,GAC7CI,kBAAmBpQ,KAAKE,IAAI1C,EAAY,EAAGyS,EAAYH,IAGlD,CACLK,mBAAoBnQ,KAAKC,IAAI,EAAG+P,EAAaF,GAC7CM,kBAAmBpQ,KAAKE,IAAI1C,EAAY,EAAGyS,EAAY,IC5B7D,ICQI,EAAQ,EAEZ,SAAS,EAAQ1K,EAAQC,GAAkB,IAAIzC,EAAOC,OAAOD,KAAKwC,GAAS,GAAIvC,OAAOyC,sBAAuB,CAAE,IAAIC,EAAU1C,OAAOyC,sBAAsBF,GAAaC,IAAgBE,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAO5C,OAAO6C,yBAAyBN,EAAQK,GAAKE,eAAgB/C,EAAK/F,KAAK+I,MAAMhD,EAAM2C,GAAY,OAAO3C,EAU9U,IAAI,GAAmB,EAAQ,EAE/B,SAAUuD,GAGR,SAAS2M,IACP,IAAIC,EAEAzM,EAEJ,IAAgBxM,KAAMgZ,GAEtB,IAAK,IAAIE,EAAOxQ,UAAUxH,OAAQiY,EAAO,IAAIjQ,MAAMgQ,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,GAAQ1Q,UAAU0Q,GAkFzB,OA/EA5M,EAAQ,IAA2BxM,MAAOiZ,EAAmB,IAAgBD,IAAkB1Y,KAAKwL,MAAMmN,EAAkB,CAACjZ,MAAM4E,OAAOuU,KAE1I,IAAgB,IAAuB3M,GAAQ,QAAS,CACtD4C,eAAgB,EAChBE,YAAa,EACbT,cAAe,CACbM,mBAAoB,EACpBE,gBAAiB,KAIrB,IAAgB,IAAuB7C,GAAQ,oBAAqB,GAEpE,IAAgB,IAAuBA,GAAQ,mBAAoB,GAEnE,IAAgB,IAAuBA,GAAQ,iBAAkB,GAEjE,IAAgB,IAAuBA,GAAQ,gBAAiB,GAEhE,IAAgB,IAAuBA,GAAQ,cAAc,SAAUyB,GACrE,IAAI2C,EAAcpE,EAAMD,MACpB6B,EAAcwC,EAAYxC,YAC1BiL,EAAWzI,EAAYyI,SACvBC,EAAO1I,EAAY0I,KACnB7K,EAAWmC,EAAYnC,SAE3B,IAAI4K,EAAJ,CAIA,IAAIE,EAAwB/M,EAAMgN,kBAC9BC,EAAyBF,EAAsBnK,eAC/CsK,EAAsBH,EAAsBjK,YAE5CqK,EAAyBnN,EAAMgN,kBAC/BpK,EAAiBuK,EAAuBvK,eACxCE,EAAcqK,EAAuBrK,YAIzC,OAAQrB,EAAM3J,KACZ,IAAK,YACHgL,EAAuB,UAATgK,EAAmBvT,KAAKE,IAAIqJ,EAAc,EAAGb,EAAW,GAAK1I,KAAKE,IAAIuG,EAAMkB,cAAgB,EAAGe,EAAW,GACxH,MAEF,IAAK,YACHW,EAA0B,UAATkK,EAAmBvT,KAAKC,IAAIoJ,EAAiB,EAAG,GAAKrJ,KAAKC,IAAIwG,EAAMQ,kBAAoB,EAAG,GAC5G,MAEF,IAAK,aACHoC,EAA0B,UAATkK,EAAmBvT,KAAKE,IAAImJ,EAAiB,EAAGhB,EAAc,GAAKrI,KAAKE,IAAIuG,EAAMU,iBAAmB,EAAGkB,EAAc,GACvI,MAEF,IAAK,UACHkB,EAAuB,UAATgK,EAAmBvT,KAAKC,IAAIsJ,EAAc,EAAG,GAAKvJ,KAAKC,IAAIwG,EAAMgB,eAAiB,EAAG,GAInG4B,IAAmBqK,GAA0BnK,IAAgBoK,IAC/DzL,EAAM2L,iBAENpN,EAAMqN,mBAAmB,CACvBzK,eAAgBA,EAChBE,YAAaA,SAKnB,IAAgB,IAAuB9C,GAAQ,sBAAsB,SAAUlJ,GAC7E,IAAI6J,EAAmB7J,EAAK6J,iBACxBE,EAAkB/J,EAAK+J,gBACvBM,EAAgBrK,EAAKqK,cACrBE,EAAevK,EAAKuK,aACxBrB,EAAMQ,kBAAoBG,EAC1BX,EAAMU,iBAAmBG,EACzBb,EAAMgB,eAAiBG,EACvBnB,EAAMkB,cAAgBG,KAGjBrB,EAmFT,OA/KA,IAAUwM,EAAiB3M,GA+F3B,IAAa2M,EAAiB,CAAC,CAC7B1U,IAAK,mBACLC,MAAO,SAA0BC,GAC/B,IAAI4K,EAAiB5K,EAAM4K,eACvBE,EAAc9K,EAAM8K,YACxBtP,KAAK0M,SAAS,CACZ4C,YAAaA,EACbF,eAAgBA,MAGnB,CACD9K,IAAK,SACLC,MAAO,WACL,IAAI8M,EAAerR,KAAKuM,MACpBzM,EAAYuR,EAAavR,UACzBga,EAAWzI,EAAayI,SAExBC,EAAyB/Z,KAAKwZ,kBAC9BpK,EAAiB2K,EAAuB3K,eACxCE,EAAcyK,EAAuBzK,YAEzC,OAAO,gBAAoB,MAAO,CAChCxP,UAAWA,EACXka,UAAWha,KAAKia,YACfH,EAAS,CACVjN,kBAAmB7M,KAAKka,mBACxB9K,eAAgBA,EAChBE,YAAaA,OAGhB,CACDhL,IAAK,kBACLC,MAAO,WACL,OAAOvE,KAAKuM,MAAM4N,aAAena,KAAKuM,MAAQvM,KAAK4O,QAEpD,CACDtK,IAAK,qBACLC,MAAO,SAA4Ba,GACjC,IAAIgK,EAAiBhK,EAAMgK,eACvBE,EAAclK,EAAMkK,YACpBkC,EAAexR,KAAKuM,MACpB4N,EAAe3I,EAAa2I,aAC5BC,EAAmB5I,EAAa4I,iBAEJ,mBAArBA,GACTA,EAAiB,CACfhL,eAAgBA,EAChBE,YAAaA,IAIZ6K,GACHna,KAAK0M,SAAS,CACZ0C,eAAgBA,EAChBE,YAAaA,OAIjB,CAAC,CACHhL,IAAK,2BACLC,MAAO,SAAkCmT,EAAWvF,GAClD,OAAIuF,EAAUyC,aACL,GAGLzC,EAAUtI,iBAAmB+C,EAAUtD,cAAcM,oBAAsBuI,EAAUpI,cAAgB6C,EAAUtD,cAAcQ,gBA3KvI,SAAuBxP,GAAU,IAAK,IAAIoB,EAAI,EAAGA,EAAIyH,UAAUxH,OAAQD,IAAK,CAAE,IAAI+K,EAAyB,MAAhBtD,UAAUzH,GAAayH,UAAUzH,GAAK,GAAQA,EAAI,EAAK,EAAQ+K,GAAQ,GAAM3L,SAAQ,SAAUiE,GAAO,IAAgBzE,EAAQyE,EAAK0H,EAAO1H,OAAsByE,OAAOkD,0BAA6BlD,OAAOmD,iBAAiBrM,EAAQkJ,OAAOkD,0BAA0BD,IAAmB,EAAQA,GAAQ3L,SAAQ,SAAUiE,GAAOyE,OAAOoD,eAAetM,EAAQyE,EAAKyE,OAAO6C,yBAAyBI,EAAQ1H,OAAe,OAAOzE,EA4K9e,CAAc,GAAIsS,EAAW,CAClC/C,eAAgBsI,EAAUtI,eAC1BE,YAAaoI,EAAUpI,YACvBT,cAAe,CACbM,mBAAoBuI,EAAUtI,eAC9BC,gBAAiBqI,EAAUpI,eAK1B,OAIJ0J,EAhLT,CAiLE,iBAAsB,IAAgB,EAAQ,YAAqD,MAWjG,GAEJ,IAAgB,EAAiB,eAAgB,CAC/CK,UAAU,EACVc,cAAc,EACdb,KAAM,QACNlK,eAAgB,EAChBE,YAAa,IAGf,mBAAS,GACM,ICrNX,EAAQ,E,UAEZ,SAAS,GAAQhE,EAAQC,GAAkB,IAAIzC,EAAOC,OAAOD,KAAKwC,GAAS,GAAIvC,OAAOyC,sBAAuB,CAAE,IAAIC,EAAU1C,OAAOyC,sBAAsBF,GAAaC,IAAgBE,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAO5C,OAAO6C,yBAAyBN,EAAQK,GAAKE,eAAgB/C,EAAK/F,KAAK+I,MAAMhD,EAAM2C,GAAY,OAAO3C,EAE9U,SAAS,GAAcjJ,GAAU,IAAK,IAAIoB,EAAI,EAAGA,EAAIyH,UAAUxH,OAAQD,IAAK,CAAE,IAAI+K,EAAyB,MAAhBtD,UAAUzH,GAAayH,UAAUzH,GAAK,GAAQA,EAAI,EAAK,GAAQ+K,GAAQ,GAAM3L,SAAQ,SAAUiE,GAAO,IAAgBzE,EAAQyE,EAAK0H,EAAO1H,OAAsByE,OAAOkD,0BAA6BlD,OAAOmD,iBAAiBrM,EAAQkJ,OAAOkD,0BAA0BD,IAAmB,GAAQA,GAAQ3L,SAAQ,SAAUiE,GAAOyE,OAAOoD,eAAetM,EAAQyE,EAAKyE,OAAO6C,yBAAyBI,EAAQ1H,OAAe,OAAOzE,EAI7f,IAAI,IAAa,EAAQ,EAEzB,SAAUwa,GAGR,SAASC,IACP,IAAIrB,EAEAzM,EAEJ,IAAgBxM,KAAMsa,GAEtB,IAAK,IAAIpB,EAAOxQ,UAAUxH,OAAQiY,EAAO,IAAIjQ,MAAMgQ,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,GAAQ1Q,UAAU0Q,GAyDzB,OAtDA5M,EAAQ,IAA2BxM,MAAOiZ,EAAmB,IAAgBqB,IAAYha,KAAKwL,MAAMmN,EAAkB,CAACjZ,MAAM4E,OAAOuU,KAEpI,IAAgB,IAAuB3M,GAAQ,QAAS,CACtD/M,OAAQ+M,EAAMD,MAAMgO,eAAiB,EACrChb,MAAOiN,EAAMD,MAAMiO,cAAgB,IAGrC,IAAgB,IAAuBhO,GAAQ,mBAAe,GAE9D,IAAgB,IAAuBA,GAAQ,kBAAc,GAE7D,IAAgB,IAAuBA,GAAQ,eAAW,GAE1D,IAAgB,IAAuBA,GAAQ,4BAAwB,GAEvE,IAAgB,IAAuBA,GAAQ,aAAa,WAC1D,IAAIoE,EAAcpE,EAAMD,MACpBkO,EAAgB7J,EAAY6J,cAC5BC,EAAe9J,EAAY8J,aAC3BC,EAAW/J,EAAY+J,SAE3B,GAAInO,EAAMoO,YAAa,CAIrB,IAAInb,EAAS+M,EAAMoO,YAAYlb,cAAgB,EAC3CH,EAAQiN,EAAMoO,YAAYpb,aAAe,EAEzCF,GADMkN,EAAMnP,SAAWI,QACXiE,iBAAiB8K,EAAMoO,cAAgB,GACnDC,EAAcC,SAASxb,EAAMub,YAAa,KAAO,EACjDE,EAAeD,SAASxb,EAAMyb,aAAc,KAAO,EACnDC,EAAaF,SAASxb,EAAM0b,WAAY,KAAO,EAC/CC,EAAgBH,SAASxb,EAAM2b,cAAe,KAAO,EACrDC,EAAYzb,EAASub,EAAaC,EAClCE,EAAW5b,EAAQsb,EAAcE,IAEhCN,GAAiBjO,EAAMoC,MAAMnP,SAAWyb,IAAcR,GAAgBlO,EAAMoC,MAAMrP,QAAU4b,KAC/F3O,EAAME,SAAS,CACbjN,OAAQA,EAASub,EAAaC,EAC9B1b,MAAOA,EAAQsb,EAAcE,IAG/BJ,EAAS,CACPlb,OAAQA,EACRF,MAAOA,SAMf,IAAgB,IAAuBiN,GAAQ,WAAW,SAAU4O,GAClE5O,EAAM6O,WAAaD,KAGd5O,EAiFT,OApJA,IAAU8N,EAAWD,GAsErB,IAAaC,EAAW,CAAC,CACvBhW,IAAK,oBACLC,MAAO,WACL,IAAIpH,EAAQ6C,KAAKuM,MAAMpP,MAEnB6C,KAAKqb,YAAcrb,KAAKqb,WAAWC,YAActb,KAAKqb,WAAWC,WAAW9Z,eAAiBxB,KAAKqb,WAAWC,WAAW9Z,cAAc+Z,aAAevb,KAAKqb,WAAWC,sBAAsBtb,KAAKqb,WAAWC,WAAW9Z,cAAc+Z,YAAYC,cAIlPxb,KAAK4a,YAAc5a,KAAKqb,WAAWC,WACnCtb,KAAK3C,QAAU2C,KAAKqb,WAAWC,WAAW9Z,cAAc+Z,YAGxDvb,KAAKyb,qBAAuB,YAA0Bte,EAAO6C,KAAK3C,SAElE2C,KAAKyb,qBAAqBna,kBAAkBtB,KAAK4a,YAAa5a,KAAK0b,WAEnE1b,KAAK0b,eAGR,CACDpX,IAAK,uBACLC,MAAO,WACDvE,KAAKyb,sBAAwBzb,KAAK4a,aACpC5a,KAAKyb,qBAAqBzY,qBAAqBhD,KAAK4a,YAAa5a,KAAK0b,aAGzE,CACDpX,IAAK,SACLC,MAAO,WACL,IAAI8M,EAAerR,KAAKuM,MACpBuN,EAAWzI,EAAayI,SACxBha,EAAYuR,EAAavR,UACzB2a,EAAgBpJ,EAAaoJ,cAC7BC,EAAerJ,EAAaqJ,aAC5Bpb,EAAQ+R,EAAa/R,MACrBsT,EAAc5S,KAAK4O,MACnBnP,EAASmT,EAAYnT,OACrBF,EAAQqT,EAAYrT,MAIpBoc,EAAa,CACf7G,SAAU,WAER8G,EAAc,GAyBlB,OAvBKnB,IACHkB,EAAWlc,OAAS,EACpBmc,EAAYnc,OAASA,GAGlBib,IACHiB,EAAWpc,MAAQ,EACnBqc,EAAYrc,MAAQA,GAgBf,gBAAoB,MAAO,CAChCO,UAAWA,EACXiO,IAAK/N,KAAK6b,QACVvc,MAAO,GAAc,GAAIqc,EAAY,GAAIrc,IACxCwa,EAAS8B,QAITtB,EArJT,CAsJE,aAAkB,IAAgB,EAAQ,YAAqD,MA2B7F,GAEJ,IAAgB,GAAW,eAAgB,CACzCK,SAAU,aACVF,eAAe,EACfC,cAAc,EACdpb,MAAO,K,ICjML,GAAQ,G,UAUR,IAAgB,GAAQ,GAE5B,SAAU+M,GAGR,SAASyP,IACP,IAAI7C,EAEAzM,EAEJ,IAAgBxM,KAAM8b,GAEtB,IAAK,IAAI5C,EAAOxQ,UAAUxH,OAAQiY,EAAO,IAAIjQ,MAAMgQ,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,GAAQ1Q,UAAU0Q,GA4CzB,OAzCA5M,EAAQ,IAA2BxM,MAAOiZ,EAAmB,IAAgB6C,IAAexb,KAAKwL,MAAMmN,EAAkB,CAACjZ,MAAM4E,OAAOuU,KAEvI,IAAgB,IAAuB3M,GAAQ,cAAU,GAEzD,IAAgB,IAAuBA,GAAQ,YAAY,WACzD,IAAIoE,EAAcpE,EAAMD,MACpBwP,EAAQnL,EAAYmL,MACpBC,EAAwBpL,EAAYT,YACpCA,OAAwC,IAA1B6L,EAAmC,EAAIA,EACrDxF,EAAS5F,EAAY4F,OACrByF,EAAuBrL,EAAYP,SACnCA,OAAoC,IAAzB4L,EAAkCzP,EAAMD,MAAM7H,OAAS,EAAIuX,EAEtEC,EAAwB1P,EAAM2P,uBAC9B1c,EAASyc,EAAsBzc,OAC/BF,EAAQ2c,EAAsB3c,MAE9BE,IAAWsc,EAAMK,UAAU/L,EAAUF,IAAgB5Q,IAAUwc,EAAMM,SAAShM,EAAUF,KAC1F4L,EAAMO,IAAIjM,EAAUF,EAAa5Q,EAAOE,GAEpC+W,GAA8C,mBAA7BA,EAAOI,mBAC1BJ,EAAOI,kBAAkB,CACvBzG,YAAaA,EACbE,SAAUA,QAMlB,IAAgB,IAAuB7D,GAAQ,kBAAkB,SAAU9N,IACrEA,GAAaA,aAAmB6d,SAClCC,QAAQC,KAAK,mEAGfjQ,EAAMkQ,OAAShe,EAEXA,GACF8N,EAAMmQ,uBAIHnQ,EAkGT,OAxJA,IAAUsP,EAAczP,GAyDxB,IAAayP,EAAc,CAAC,CAC1BxX,IAAK,oBACLC,MAAO,WACLvE,KAAK2c,sBAEN,CACDrY,IAAK,qBACLC,MAAO,WACLvE,KAAK2c,sBAEN,CACDrY,IAAK,SACLC,MAAO,WACL,IAAIuV,EAAW9Z,KAAKuM,MAAMuN,SAC1B,MAA2B,mBAAbA,EAA0BA,EAAS,CAC/C8C,QAAS5c,KAAK6c,SACdC,cAAe9c,KAAK+c,iBACjBjD,IAEN,CACDxV,IAAK,uBACLC,MAAO,WACL,IAAIwX,EAAQ/b,KAAKuM,MAAMwP,MACnBiB,EAAOhd,KAAK0c,QAAU,uBAAY1c,MAEtC,GAAIgd,GAAQA,EAAKxb,eAAiBwb,EAAKxb,cAAc+Z,aAAeyB,aAAgBA,EAAKxb,cAAc+Z,YAAYC,YAAa,CAC9H,IAAIyB,EAAaD,EAAK1d,MAAMC,MACxB2d,EAAcF,EAAK1d,MAAMG,OAUxBsc,EAAMzF,kBACT0G,EAAK1d,MAAMC,MAAQ,QAGhBwc,EAAM3F,mBACT4G,EAAK1d,MAAMG,OAAS,QAGtB,IAAIA,EAASsG,KAAKoX,KAAKH,EAAKtd,cACxBH,EAAQwG,KAAKoX,KAAKH,EAAKxd,aAU3B,OARIyd,IACFD,EAAK1d,MAAMC,MAAQ0d,GAGjBC,IACFF,EAAK1d,MAAMG,OAASyd,GAGf,CACLzd,OAAQA,EACRF,MAAOA,GAGT,MAAO,CACLE,OAAQ,EACRF,MAAO,KAIZ,CACD+E,IAAK,oBACLC,MAAO,WACL,IAAI8M,EAAerR,KAAKuM,MACpBwP,EAAQ1K,EAAa0K,MACrBqB,EAAwB/L,EAAalB,YACrCA,OAAwC,IAA1BiN,EAAmC,EAAIA,EACrD5G,EAASnF,EAAamF,OACtB6G,EAAwBhM,EAAahB,SACrCA,OAAqC,IAA1BgN,EAAmCrd,KAAKuM,MAAM7H,OAAS,EAAI2Y,EAE1E,IAAKtB,EAAM1F,IAAIhG,EAAUF,GAAc,CACrC,IAAImN,EAAyBtd,KAAKmc,uBAC9B1c,EAAS6d,EAAuB7d,OAChCF,EAAQ+d,EAAuB/d,MAEnCwc,EAAMO,IAAIjM,EAAUF,EAAa5Q,EAAOE,GAEpC+W,GAA0D,mBAAzCA,EAAO+G,+BAC1B/G,EAAO+G,8BAA8B,CACnCpN,YAAaA,EACbE,SAAUA,SAObyL,EAzJT,CA0JE,iBAAsB,IAAgB,GAAQ,YAAqD,MAYjG,IAEJ,IAAgB,GAAc,8BAA8B,GCzLrD,IAOH,GAEJ,WACE,SAAS0B,IACP,IAAIhR,EAAQxM,KAERoG,EAASsC,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,GAEjF,IAAgB1I,KAAMwd,GAEtB,IAAgBxd,KAAM,mBAAoB,IAE1C,IAAgBA,KAAM,kBAAmB,IAEzC,IAAgBA,KAAM,oBAAqB,IAE3C,IAAgBA,KAAM,kBAAmB,IAEzC,IAAgBA,KAAM,sBAAkB,GAExC,IAAgBA,KAAM,qBAAiB,GAEvC,IAAgBA,KAAM,kBAAc,GAEpC,IAAgBA,KAAM,iBAAa,GAEnC,IAAgBA,KAAM,kBAAc,GAEpC,IAAgBA,KAAM,uBAAmB,GAEzC,IAAgBA,KAAM,sBAAkB,GAExC,IAAgBA,KAAM,eAAgB,GAEtC,IAAgBA,KAAM,YAAa,GAEnC,IAAgBA,KAAM,eAAe,SAAUsD,GAC7C,IAAIoB,EAAQpB,EAAKoB,MAEbJ,EAAMkI,EAAMiR,WAAW,EAAG/Y,GAE9B,YAAwC3D,IAAjCyL,EAAMkR,kBAAkBpZ,GAAqBkI,EAAMkR,kBAAkBpZ,GAAOkI,EAAMmR,iBAG3F,IAAgB3d,KAAM,aAAa,SAAUwE,GAC3C,IAAIE,EAAQF,EAAME,MAEdJ,EAAMkI,EAAMiR,WAAW/Y,EAAO,GAElC,YAAsC3D,IAA/ByL,EAAMoR,gBAAgBtZ,GAAqBkI,EAAMoR,gBAAgBtZ,GAAOkI,EAAMqR,kBAGvF,IAAItD,EAAgBnU,EAAOmU,cACvBC,EAAepU,EAAOoU,aACtBsD,EAAc1X,EAAO0X,YACrBC,EAAa3X,EAAO2X,WACpBC,EAAY5X,EAAO4X,UACnBC,EAAY7X,EAAO6X,UACnBC,EAAW9X,EAAO8X,SACtBle,KAAKme,iBAAkC,IAAhBL,EACvB9d,KAAKoe,gBAAgC,IAAfL,EACtB/d,KAAKqe,WAAaJ,GAAa,EAC/Bje,KAAKse,UAAYJ,GAAY,EAC7Ble,KAAKyd,WAAaO,GAAaO,GAC/Bve,KAAK6d,eAAiB9X,KAAKC,IAAIhG,KAAKqe,WAAqC,iBAAlB9D,EAA6BA,EAvE5D,IAwExBva,KAAK2d,cAAgB5X,KAAKC,IAAIhG,KAAKse,UAAmC,iBAAjB9D,EAA4BA,EAvE1D,KAyNzB,OAjIA,IAAagD,EAAmB,CAAC,CAC/BlZ,IAAK,QACLC,MAAO,SAAe8L,GACpB,IAAIF,EAAczH,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,EAElFpE,EAAMtE,KAAKyd,WAAWpN,EAAUF,UAE7BnQ,KAAKwe,iBAAiBla,UACtBtE,KAAKye,gBAAgBna,GAE5BtE,KAAK0e,+BAA+BrO,EAAUF,KAE/C,CACD7L,IAAK,WACLC,MAAO,WACLvE,KAAKwe,iBAAmB,GACxBxe,KAAKye,gBAAkB,GACvBze,KAAK0d,kBAAoB,GACzB1d,KAAK4d,gBAAkB,GACvB5d,KAAK2e,UAAY,EACjB3e,KAAK4e,aAAe,IAErB,CACDta,IAAK,iBACLC,MAAO,WACL,OAAOvE,KAAKme,kBAEb,CACD7Z,IAAK,gBACLC,MAAO,WACL,OAAOvE,KAAKoe,iBAEb,CACD9Z,IAAK,YACLC,MAAO,SAAmB8L,GACxB,IAAIF,EAAczH,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,EAEtF,GAAI1I,KAAKme,gBACP,OAAOne,KAAK6d,eAEZ,IAAIzE,EAAOpZ,KAAKyd,WAAWpN,EAAUF,GAErC,YAAuCpP,IAAhCf,KAAKwe,iBAAiBpF,GAAsBrT,KAAKC,IAAIhG,KAAKqe,WAAYre,KAAKwe,iBAAiBpF,IAASpZ,KAAK6d,iBAGpH,CACDvZ,IAAK,WACLC,MAAO,SAAkB8L,GACvB,IAAIF,EAAczH,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,EAEtF,GAAI1I,KAAKoe,eACP,OAAOpe,KAAK2d,cAEZ,IAAIkB,EAAQ7e,KAAKyd,WAAWpN,EAAUF,GAEtC,YAAuCpP,IAAhCf,KAAKye,gBAAgBI,GAAuB9Y,KAAKC,IAAIhG,KAAKse,UAAWte,KAAKye,gBAAgBI,IAAU7e,KAAK2d,gBAGnH,CACDrZ,IAAK,MACLC,MAAO,SAAa8L,GAClB,IAAIF,EAAczH,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,EAElFpE,EAAMtE,KAAKyd,WAAWpN,EAAUF,GAEpC,YAAsCpP,IAA/Bf,KAAKwe,iBAAiBla,KAE9B,CACDA,IAAK,MACLC,MAAO,SAAa8L,EAAUF,EAAa5Q,EAAOE,GAChD,IAAI6E,EAAMtE,KAAKyd,WAAWpN,EAAUF,GAEhCA,GAAenQ,KAAK4e,eACtB5e,KAAK4e,aAAezO,EAAc,GAGhCE,GAAYrQ,KAAK2e,YACnB3e,KAAK2e,UAAYtO,EAAW,GAI9BrQ,KAAKwe,iBAAiBla,GAAO7E,EAC7BO,KAAKye,gBAAgBna,GAAO/E,EAE5BS,KAAK0e,+BAA+BrO,EAAUF,KAE/C,CACD7L,IAAK,iCACLC,MAAO,SAAwC8L,EAAUF,GAKvD,IAAKnQ,KAAKoe,eAAgB,CAGxB,IAFA,IAAI9P,EAAc,EAETrN,EAAI,EAAGA,EAAIjB,KAAK2e,UAAW1d,IAClCqN,EAAcvI,KAAKC,IAAIsI,EAAatO,KAAKqc,SAASpb,EAAGkP,IAGvD,IAAI2O,EAAY9e,KAAKyd,WAAW,EAAGtN,GAEnCnQ,KAAK0d,kBAAkBoB,GAAaxQ,EAGtC,IAAKtO,KAAKme,gBAAiB,CAGzB,IAFA,IAAIzP,EAAY,EAEPqQ,EAAK,EAAGA,EAAK/e,KAAK4e,aAAcG,IACvCrQ,EAAY3I,KAAKC,IAAI0I,EAAW1O,KAAKoc,UAAU/L,EAAU0O,IAG3D,IAAIC,EAAShf,KAAKyd,WAAWpN,EAAU,GAEvCrQ,KAAK4d,gBAAgBoB,GAAUtQ,KAGlC,CACDpK,IAAK,gBACL2a,IAAK,WACH,OAAOjf,KAAK6d,iBAEb,CACDvZ,IAAK,eACL2a,IAAK,WACH,OAAOjf,KAAK2d,kBAITH,EAjNT,GAsNA,SAASe,GAAiBlO,EAAUF,GAClC,MAAO,GAAGvL,OAAOyL,EAAU,KAAKzL,OAAOuL,GC3NzC,SAAS,GAAQ7E,EAAQC,GAAkB,IAAIzC,EAAOC,OAAOD,KAAKwC,GAAS,GAAIvC,OAAOyC,sBAAuB,CAAE,IAAIC,EAAU1C,OAAOyC,sBAAsBF,GAAaC,IAAgBE,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAO5C,OAAO6C,yBAAyBN,EAAQK,GAAKE,eAAgB/C,EAAK/F,KAAK+I,MAAMhD,EAAM2C,GAAY,OAAO3C,EAE9U,SAAS,GAAcjJ,GAAU,IAAK,IAAIoB,EAAI,EAAGA,EAAIyH,UAAUxH,OAAQD,IAAK,CAAE,IAAI+K,EAAyB,MAAhBtD,UAAUzH,GAAayH,UAAUzH,GAAK,GAAQA,EAAI,EAAK,GAAQ+K,GAAQ,GAAM3L,SAAQ,SAAUiE,GAAO,IAAgBzE,EAAQyE,EAAK0H,EAAO1H,OAAsByE,OAAOkD,0BAA6BlD,OAAOmD,iBAAiBrM,EAAQkJ,OAAOkD,0BAA0BD,IAAmB,GAAQA,GAAQ3L,SAAQ,SAAUiE,GAAOyE,OAAOoD,eAAetM,EAAQyE,EAAKyE,OAAO6C,yBAAyBI,EAAQ1H,OAAe,OAAOzE,EAc7f,IAMI,GACQ,WADR,GAES,YAOT,GAEJ,SAAUwM,GAIR,SAAS6S,IACP,IAAIjG,EAEAzM,EAEJ,IAAgBxM,KAAMkf,GAEtB,IAAK,IAAIhG,EAAOxQ,UAAUxH,OAAQiY,EAAO,IAAIjQ,MAAMgQ,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,GAAQ1Q,UAAU0Q,GAkIzB,OA/HA5M,EAAQ,IAA2BxM,MAAOiZ,EAAmB,IAAgBiG,IAAiB5e,KAAKwL,MAAMmN,EAAkB,CAACjZ,MAAM4E,OAAOuU,KAGzI,IAAgB,IAAuB3M,GAAQ,QAAS,CACtDG,aAAa,EACbzN,WAAY,EACZE,UAAW,IAGb,IAAgB,IAAuBoN,GAAQ,6CAA6C,GAE5F,IAAgB,IAAuBA,GAAQ,6BAA8BhE,KAE7E,IAAgB,IAAuBgE,GAAQ,oBAAqBhE,GAAuB,IAE3F,IAAgB,IAAuBgE,GAAQ,kCAAkC,WAC/E,IAAIoE,EAAcpE,EAAMD,MACpB4S,EAAoBvO,EAAYuO,kBAChCtS,EAAoB+D,EAAY/D,kBAEpCL,EAAM4S,2BAA2B,CAC/BxW,SAAUiE,EACVhE,QAAS,CACPA,QAASsW,EAAkBE,+BAKjC,IAAgB,IAAuB7S,GAAQ,6BAA6B,SAAUuB,GACpFvB,EAAMwB,oBAAsBD,KAG9B,IAAgB,IAAuBvB,GAAQ,wCAAwC,WACrF,IAAI6E,EAAe7E,EAAMD,MACrB4S,EAAoB9N,EAAa8N,kBACjC1f,EAAS4R,EAAa5R,OACtBuK,EAAoBqH,EAAarH,kBACjCsV,EAAejO,EAAaiO,aAC5B/f,EAAQ8R,EAAa9R,MACrBqT,EAAcpG,EAAMoC,MACpB1P,EAAa0T,EAAY1T,WACzBE,EAAYwT,EAAYxT,UAE5B,GAAIkgB,GAAgB,EAAG,CACrB,IAAIC,EAAiBJ,EAAkBK,yBAAyB,CAC9Dla,MAAO0E,EACPyV,UAAWH,EACX7f,OAAQA,EACRP,WAAYA,EACZE,UAAWA,EACXG,MAAOA,IAGLggB,EAAergB,aAAeA,GAAcqgB,EAAengB,YAAcA,GAC3EoN,EAAMkT,mBAAmBH,OAK/B,IAAgB,IAAuB/S,GAAQ,aAAa,SAAUyB,GAIpE,GAAIA,EAAMpO,SAAW2M,EAAMwB,oBAA3B,CAKAxB,EAAMmT,iCAMN,IAAInO,EAAehF,EAAMD,MACrB4S,EAAoB3N,EAAa2N,kBACjC1f,EAAS+R,EAAa/R,OACtBmgB,EAAoBpO,EAAaoO,kBACjCrgB,EAAQiS,EAAajS,MACrBgQ,EAAgB/C,EAAMqT,eAEtBC,EAAwBX,EAAkBhZ,eAC1C4Z,EAAcD,EAAsBrgB,OACpCugB,EAAaF,EAAsBvgB,MAEnCL,EAAa6G,KAAKC,IAAI,EAAGD,KAAKE,IAAI+Z,EAAazgB,EAAQgQ,EAAetB,EAAMpO,OAAOX,aACnFE,EAAY2G,KAAKC,IAAI,EAAGD,KAAKE,IAAI8Z,EAActgB,EAAS8P,EAAetB,EAAMpO,OAAOT,YAKxF,GAAIoN,EAAMoC,MAAM1P,aAAeA,GAAcsN,EAAMoC,MAAMxP,YAAcA,EAAW,CAKhF,IAAIuQ,EAA6B1B,EAAMgS,WAAa,GAA0C,GAEzFzT,EAAMoC,MAAMjC,aACfiT,GAAkB,GAGpBpT,EAAME,SAAS,CACbC,aAAa,EACbzN,WAAYA,EACZyQ,2BAA4BA,EAC5BvQ,UAAWA,IAIfoN,EAAM0E,wBAAwB,CAC5BhS,WAAYA,EACZE,UAAWA,EACX4gB,WAAYA,EACZD,YAAaA,QAIjBvT,EAAMqT,eAAiB,yBAEM9e,IAAzByL,EAAMqT,gBACRrT,EAAM0T,wBAAyB,EAC/B1T,EAAMqT,eAAiB,GAEvBrT,EAAM0T,wBAAyB,EAG1B1T,EAsST,OAnbA,IAAU0S,EAAgB7S,GAsJ1B,IAAa6S,EAAgB,CAAC,CAC5B5a,IAAK,iCACLC,MAAO,WACLvE,KAAKmgB,2CAA4C,EACjDngB,KAAK6R,gBAYN,CACDvN,IAAK,oBACLC,MAAO,WACL,IAAIyN,EAAehS,KAAKuM,MACpB4S,EAAoBnN,EAAamN,kBACjCjgB,EAAa8S,EAAa9S,WAC1BogB,EAAetN,EAAasN,aAC5BlgB,EAAY4S,EAAa5S,UAGxBY,KAAKkgB,yBACRlgB,KAAK6f,eAAiB,oBACtB7f,KAAKkgB,wBAAyB,EAC9BlgB,KAAK0M,SAAS,KAGZ4S,GAAgB,EAClBtf,KAAKogB,wCACIlhB,GAAc,GAAKE,GAAa,IACzCY,KAAK0f,mBAAmB,CACtBxgB,WAAYA,EACZE,UAAWA,IAKfY,KAAKqgB,iCAEL,IAAIC,EAAyBnB,EAAkBhZ,eAC3C4Z,EAAcO,EAAuB7gB,OACrCugB,EAAaM,EAAuB/gB,MAGxCS,KAAKkR,wBAAwB,CAC3BhS,WAAYA,GAAc,EAC1BE,UAAWA,GAAa,EACxB2gB,YAAaA,EACbC,WAAYA,MAGf,CACD1b,IAAK,qBACLC,MAAO,SAA4BkO,EAAWN,GAC5C,IAAIQ,EAAe3S,KAAKuM,MACpB9M,EAASkT,EAAalT,OACtBuK,EAAoB2I,EAAa3I,kBACjCsV,EAAe3M,EAAa2M,aAC5B/f,EAAQoT,EAAapT,MACrB+T,EAAetT,KAAK4O,MACpB1P,EAAaoU,EAAapU,WAC1ByQ,EAA6B2D,EAAa3D,2BAC1CvQ,EAAYkU,EAAalU,UAMzBuQ,IAA+B,KAC7BzQ,GAAc,GAAKA,IAAeiT,EAAUjT,YAAcA,IAAec,KAAKgO,oBAAoB9O,aACpGc,KAAKgO,oBAAoB9O,WAAaA,GAGpCE,GAAa,GAAKA,IAAc+S,EAAU/S,WAAaA,IAAcY,KAAKgO,oBAAoB5O,YAChGY,KAAKgO,oBAAoB5O,UAAYA,IAKrCK,IAAWgT,EAAUhT,QAAUuK,IAAsByI,EAAUzI,mBAAqBsV,IAAiB7M,EAAU6M,cAAgB/f,IAAUkT,EAAUlT,OACrJS,KAAKogB,uCAIPpgB,KAAKqgB,mCAEN,CACD/b,IAAK,uBACLC,MAAO,WACDvE,KAAKyM,gCACPlO,aAAayB,KAAKyM,kCAGrB,CACDnI,IAAK,SACLC,MAAO,WACL,IAAIuO,EAAe9S,KAAKuM,MACpBsE,EAAaiC,EAAajC,WAC1BtN,EAAYuP,EAAavP,UACzB4b,EAAoBrM,EAAaqM,kBACjCrf,EAAYgT,EAAahT,UACzBL,EAASqT,EAAarT,OACtB8gB,EAAyBzN,EAAayN,uBACtC/hB,EAAKsU,EAAatU,GAClB2U,EAAoBL,EAAaK,kBACjC7T,EAAQwT,EAAaxT,MACrBkhB,EAAuB1N,EAAa0N,qBACpCjhB,EAAQuT,EAAavT,MACrBkhB,EAAezgB,KAAK4O,MACpBjC,EAAc8T,EAAa9T,YAC3BzN,EAAauhB,EAAavhB,WAC1BE,EAAYqhB,EAAarhB,WAEzBY,KAAK0gB,yBAA2Bnd,GAAavD,KAAK2gB,iCAAmCxB,GAAqBnf,KAAKmgB,6CACjHngB,KAAK0gB,uBAAyBnd,EAC9BvD,KAAK2gB,+BAAiCxB,EACtCnf,KAAKmgB,2CAA4C,EACjDhB,EAAkByB,gCAGpB,IAAIC,EAAyB1B,EAAkBhZ,eAC3C4Z,EAAcc,EAAuBphB,OACrCugB,EAAaa,EAAuBthB,MAGpCqZ,EAAO7S,KAAKC,IAAI,EAAG9G,EAAaqhB,GAChC1H,EAAM9S,KAAKC,IAAI,EAAG5G,EAAYohB,GAC9BM,EAAQ/a,KAAKE,IAAI+Z,EAAY9gB,EAAaK,EAAQghB,GAClDQ,EAAShb,KAAKE,IAAI8Z,EAAa3gB,EAAYK,EAAS+gB,GACpDlM,EAAoB7U,EAAS,GAAKF,EAAQ,EAAI4f,EAAkB6B,cAAc,CAChFvhB,OAAQshB,EAASlI,EACjBlM,YAAaA,EACbpN,MAAOuhB,EAAQlI,EACfqI,EAAGrI,EACHsI,EAAGrI,IACA,GACDsI,EAAkB,CACpB1N,UAAW,aACXC,UAAW,MACXjU,OAAQoR,EAAa,OAASpR,EAC9BkC,SAAU,WACVgS,wBAAyB,QACzBpU,MAAOA,EACPqU,WAAY,aAKVG,EAAwBgM,EAActgB,EAASO,KAAK6f,eAAiB,EACrE7L,EAA0BgM,EAAazgB,EAAQS,KAAK6f,eAAiB,EAQzE,OAFAsB,EAAgB/M,UAAY4L,EAAajM,GAAyBxU,EAAQ,SAAW,OACrF4hB,EAAgB9M,UAAY0L,EAAc/L,GAA2BvU,EAAS,SAAW,OAClF,gBAAoB,MAAO,CAChCsO,IAAK/N,KAAKyU,0BACV,aAAczU,KAAKuM,MAAM,cACzBzM,UAAW,kBAAK,+BAAgCA,GAChDtB,GAAIA,EACJkW,SAAU1U,KAAK2U,UACfvB,KAAM,OACN9T,MAAO,GAAc,GAAI6hB,EAAiB,GAAI7hB,GAC9C+T,SAAU,GACT9P,EAAY,GAAK,gBAAoB,MAAO,CAC7CzD,UAAW,qDACXR,MAAO,CACLG,OAAQsgB,EACRlL,UAAWkL,EACXnL,SAAUoL,EACVlL,SAAU,SACVC,cAAepI,EAAc,OAAS,GACtCpN,MAAOygB,IAER1L,GAAkC,IAAd/Q,GAAmB4P,OAU3C,CACD7O,IAAK,iCACLC,MAAO,WACL,IAAImO,EAAS1S,KAETA,KAAKyM,gCACPlO,aAAayB,KAAKyM,gCAGpBzM,KAAKyM,+BAAiCvO,YAAW,YAE/C0hB,EADwBlN,EAAOnG,MAAMqT,oBACnB,GAClBlN,EAAOjG,+BAAiC,KAExCiG,EAAOhG,SAAS,CACdC,aAAa,MAtXI,OA0XtB,CACDrI,IAAK,0BACLC,MAAO,SAAiCjB,GACtC,IAAIuT,EAAS7W,KAETd,EAAaoE,EAAKpE,WAClBE,EAAYkE,EAAKlE,UACjB2gB,EAAczc,EAAKyc,YACnBC,EAAa1c,EAAK0c,WAEtBhgB,KAAK8W,kBAAkB,CACrBlO,SAAU,SAAkBpE,GAC1B,IAAItF,EAAasF,EAAMtF,WACnBE,EAAYoF,EAAMpF,UAClB2X,EAAeF,EAAOtK,MACtB9M,EAASsX,EAAatX,QAG1BiV,EAFeqC,EAAarC,UAEnB,CACPsC,aAAcvX,EACdwX,YAHUF,EAAaxX,MAIvBF,aAAc0gB,EACd7gB,WAAYA,EACZE,UAAWA,EACXD,YAAa6gB,KAGjBnX,QAAS,CACP3J,WAAYA,EACZE,UAAWA,OAIhB,CACDkF,IAAK,qBACLC,MAAO,SAA4Ba,GACjC,IAAIlG,EAAakG,EAAMlG,WACnBE,EAAYgG,EAAMhG,UAClB6R,EAAW,CACbtB,2BAA4B,IAG1BzQ,GAAc,IAChB+R,EAAS/R,WAAaA,GAGpBE,GAAa,IACf6R,EAAS7R,UAAYA,IAGnBF,GAAc,GAAKA,IAAec,KAAK4O,MAAM1P,YAAcE,GAAa,GAAKA,IAAcY,KAAK4O,MAAMxP,YACxGY,KAAK0M,SAASuE,MAGhB,CAAC,CACH3M,IAAK,2BACLC,MAAO,SAAkCmT,EAAWvF,GAClD,OAA4B,IAAxBuF,EAAUnU,WAA6C,IAAzB4O,EAAUjT,YAA4C,IAAxBiT,EAAU/S,UAM/DsY,EAAUxY,aAAeiT,EAAUjT,YAAcwY,EAAUtY,YAAc+S,EAAU/S,UACrF,CACLF,WAAoC,MAAxBwY,EAAUxY,WAAqBwY,EAAUxY,WAAaiT,EAAUjT,WAC5EE,UAAkC,MAAvBsY,EAAUtY,UAAoBsY,EAAUtY,UAAY+S,EAAU/S,UACzEuQ,2BAA4B,IAIzB,KAbE,CACLzQ,WAAY,EACZE,UAAW,EACXuQ,2BAA4B,QAc7BuP,EApbT,CAqbE,iBAEF,IAAgB,GAAgB,eAAgB,CAC9C,aAAc,OACdqB,uBAAwB,EACxBpN,kBAAmB,WACjB,OAAO,MAETuB,SAAU,WACR,OAAO,MAET7H,kBAAmB,WACjB,OAAO,MAET7C,kBAAmB,OACnBsV,cAAe,EACfhgB,MAAO,GACPkhB,qBAAsB,IAGxB,GAAeY,UAgGX,GACJ,mBAAS,IACM,UC3kBX,GAEJ,WACE,SAASC,EAAQ/d,GACf,IAAI7D,EAAS6D,EAAK7D,OACdF,EAAQ+D,EAAK/D,MACb0hB,EAAI3d,EAAK2d,EACTC,EAAI5d,EAAK4d,EAEb,IAAgBlhB,KAAMqhB,GAEtBrhB,KAAKP,OAASA,EACdO,KAAKT,MAAQA,EACbS,KAAKihB,EAAIA,EACTjhB,KAAKkhB,EAAIA,EACTlhB,KAAKshB,UAAY,GACjBthB,KAAKuhB,SAAW,GAgClB,OA3BA,IAAaF,EAAS,CAAC,CACrB/c,IAAK,eACLC,MAAO,SAAsBC,GAC3B,IAAIE,EAAQF,EAAME,MAEb1E,KAAKshB,UAAU5c,KAClB1E,KAAKshB,UAAU5c,IAAS,EAExB1E,KAAKuhB,SAASxe,KAAK2B,MAKtB,CACDJ,IAAK,iBACLC,MAAO,WACL,OAAOvE,KAAKuhB,WAIb,CACDjd,IAAK,WACLC,MAAO,WACL,MAAO,GAAGK,OAAO5E,KAAKihB,EAAG,KAAKrc,OAAO5E,KAAKkhB,EAAG,KAAKtc,OAAO5E,KAAKT,MAAO,KAAKqF,OAAO5E,KAAKP,YAInF4hB,EA9CT,GCKI,GAEJ,WACE,SAASG,IACP,IAAIC,EAAc/Y,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAXlE,IAaf,IAAgB1I,KAAMwhB,GAEtBxhB,KAAK0hB,aAAeD,EACpBzhB,KAAK2hB,cAAgB,GACrB3hB,KAAK4hB,UAAY,GA2GnB,OAnGA,IAAaJ,EAAgB,CAAC,CAC5Bld,IAAK,iBACLC,MAAO,SAAwBjB,GAC7B,IAAI7D,EAAS6D,EAAK7D,OACdF,EAAQ+D,EAAK/D,MACb0hB,EAAI3d,EAAK2d,EACTC,EAAI5d,EAAK4d,EACTrY,EAAU,GAYd,OAXA7I,KAAK6hB,YAAY,CACfpiB,OAAQA,EACRF,MAAOA,EACP0hB,EAAGA,EACHC,EAAGA,IACF7gB,SAAQ,SAAUyhB,GACnB,OAAOA,EAAQC,iBAAiB1hB,SAAQ,SAAUqE,GAChDmE,EAAQnE,GAASA,QAIdqE,OAAOD,KAAKD,GAASmZ,KAAI,SAAUtd,GACxC,OAAOmE,EAAQnE,QAKlB,CACDJ,IAAK,kBACLC,MAAO,SAAyBC,GAC9B,IAAIE,EAAQF,EAAME,MAClB,OAAO1E,KAAK2hB,cAAcjd,KAI3B,CACDJ,IAAK,cACLC,MAAO,SAAqBa,GAW1B,IAVA,IAAI3F,EAAS2F,EAAM3F,OACfF,EAAQ6F,EAAM7F,MACd0hB,EAAI7b,EAAM6b,EACVC,EAAI9b,EAAM8b,EACVe,EAAgBlc,KAAKY,MAAMsa,EAAIjhB,KAAK0hB,cACpCQ,EAAenc,KAAKY,OAAOsa,EAAI1hB,EAAQ,GAAKS,KAAK0hB,cACjDS,EAAgBpc,KAAKY,MAAMua,EAAIlhB,KAAK0hB,cACpCU,EAAerc,KAAKY,OAAOua,EAAIzhB,EAAS,GAAKO,KAAK0hB,cAClDW,EAAW,GAENC,EAAWL,EAAeK,GAAYJ,EAAcI,IAC3D,IAAK,IAAIC,EAAWJ,EAAeI,GAAYH,EAAcG,IAAY,CACvE,IAAIje,EAAM,GAAGM,OAAO0d,EAAU,KAAK1d,OAAO2d,GAErCviB,KAAK4hB,UAAUtd,KAClBtE,KAAK4hB,UAAUtd,GAAO,IAAI,GAAQ,CAChC7E,OAAQO,KAAK0hB,aACbniB,MAAOS,KAAK0hB,aACZT,EAAGqB,EAAWtiB,KAAK0hB,aACnBR,EAAGqB,EAAWviB,KAAK0hB,gBAIvBW,EAAStf,KAAK/C,KAAK4hB,UAAUtd,IAIjC,OAAO+d,IAIR,CACD/d,IAAK,uBACLC,MAAO,WACL,OAAOwE,OAAOD,KAAK9I,KAAK4hB,WAAW1gB,SAIpC,CACDoD,IAAK,WACLC,MAAO,WACL,IAAIiI,EAAQxM,KAEZ,OAAO+I,OAAOD,KAAK9I,KAAK4hB,WAAWI,KAAI,SAAUtd,GAC/C,OAAO8H,EAAMoV,UAAUld,GAAO8d,gBAKjC,CACDle,IAAK,eACLC,MAAO,SAAsB2D,GAC3B,IAAIua,EAAgBva,EAAMua,cACtB/d,EAAQwD,EAAMxD,MAClB1E,KAAK2hB,cAAcjd,GAAS+d,EAC5BziB,KAAK6hB,YAAYY,GAAepiB,SAAQ,SAAUyhB,GAChD,OAAOA,EAAQY,aAAa,CAC1Bhe,MAAOA,WAMR8c,EAnHT,GCNe,SAASxZ,GAAyB1E,GAC/C,IAAIqf,EAAarf,EAAKgC,MAClBA,OAAuB,IAAfqd,EAAwB,OAASA,EACzCC,EAAatf,EAAKsf,WAClBpf,EAAWF,EAAKE,SAChB+B,EAAgBjC,EAAKiC,cACrBC,EAAgBlC,EAAKkC,cACrBK,EAAY+c,EACZ9c,EAAYD,EAAYN,EAAgB/B,EAE5C,OAAQ8B,GACN,IAAK,QACH,OAAOO,EAET,IAAK,MACH,OAAOC,EAET,IAAK,SACH,OAAOD,GAAaN,EAAgB/B,GAAY,EAElD,QACE,OAAOuC,KAAKC,IAAIF,EAAWC,KAAKE,IAAIJ,EAAWL,KCfrD,IAAI,GAEJ,SAAU6G,GAGR,SAASwW,EAAWtW,EAAOuW,GACzB,IAAItW,EAWJ,OATA,IAAgBxM,KAAM6iB,IAEtBrW,EAAQ,IAA2BxM,KAAM,IAAgB6iB,GAAYviB,KAAKN,KAAMuM,EAAOuW,KACjFnB,cAAgB,GACtBnV,EAAMuW,yBAA2B,GAEjCvW,EAAMoF,WAAa,GACnBpF,EAAMwW,mBAAqBxW,EAAMwW,mBAAmBC,KAAK,IAAuBzW,IAChFA,EAAM0W,sBAAwB1W,EAAM0W,sBAAsBD,KAAK,IAAuBzW,IAC/EA,EA6JT,OA3KA,IAAUqW,EAAYxW,GAiBtB,IAAawW,EAAY,CAAC,CACxBve,IAAK,cACLC,MAAO,gBACwBxD,IAAzBf,KAAKmjB,iBACPnjB,KAAKmjB,gBAAgBtR,gBAKxB,CACDvN,IAAK,iCACLC,MAAO,WACLvE,KAAK4R,WAAa,GAElB5R,KAAKmjB,gBAAgBC,mCAItB,CACD9e,IAAK,SACLC,MAAO,WACL,IAAIgI,EAAQ,IAAS,GAAIvM,KAAKuM,OAE9B,OAAO,gBAAoB,GAAgB,IAAS,CAClD4S,kBAAmBnf,KACnB4f,kBAAmB5f,KAAKgjB,mBACxBjV,IAAK/N,KAAKkjB,uBACT3W,MAIJ,CACDjI,IAAK,+BACLC,MAAO,WACL,IAAIqM,EAAc5Q,KAAKuM,MAKnB8W,EC5EK,SAAsC/f,GASnD,IARA,IAAIC,EAAYD,EAAKC,UACjB+f,EAA4BhgB,EAAKggB,0BACjC7B,EAAcne,EAAKme,YACnB8B,EAAe,GACfC,EAAiB,IAAI,GAAe/B,GACpChiB,EAAS,EACTF,EAAQ,EAEHmF,EAAQ,EAAGA,EAAQnB,EAAWmB,IAAS,CAC9C,IAAI+d,EAAgBa,EAA0B,CAC5C5e,MAAOA,IAGT,GAA4B,MAAxB+d,EAAchjB,QAAkBwF,MAAMwd,EAAchjB,SAAkC,MAAvBgjB,EAAcljB,OAAiB0F,MAAMwd,EAAcljB,QAA6B,MAAnBkjB,EAAcxB,GAAahc,MAAMwd,EAAcxB,IAAyB,MAAnBwB,EAAcvB,GAAajc,MAAMwd,EAAcvB,GAClO,MAAMvc,MAAM,sCAAsCC,OAAOF,EAAO,iBAAiBE,OAAO6d,EAAcxB,EAAG,QAAQrc,OAAO6d,EAAcvB,EAAG,YAAYtc,OAAO6d,EAAcljB,MAAO,aAAaqF,OAAO6d,EAAchjB,SAGrNA,EAASsG,KAAKC,IAAIvG,EAAQgjB,EAAcvB,EAAIuB,EAAchjB,QAC1DF,EAAQwG,KAAKC,IAAIzG,EAAOkjB,EAAcxB,EAAIwB,EAAcljB,OACxDgkB,EAAa7e,GAAS+d,EACtBe,EAAeC,aAAa,CAC1BhB,cAAeA,EACf/d,MAAOA,IAIX,MAAO,CACL6e,aAAcA,EACd9jB,OAAQA,EACR+jB,eAAgBA,EAChBjkB,MAAOA,GD6CM,CAA8B,CACvCgE,UALcqN,EAAYrN,UAM1B+f,0BAL8B1S,EAAY0S,0BAM1C7B,YALgB7Q,EAAY6Q,cAQ9BzhB,KAAK2hB,cAAgB0B,EAAKE,aAC1BvjB,KAAK0jB,gBAAkBL,EAAKG,eAC5BxjB,KAAK2jB,QAAUN,EAAK5jB,OACpBO,KAAK4jB,OAASP,EAAK9jB,QAMpB,CACD+E,IAAK,yBACLC,MAAO,WACL,OAAOvE,KAAK+iB,2BAMb,CACDze,IAAK,2BACLC,MAAO,SAAkCjB,GACvC,IAAIgC,EAAQhC,EAAKgC,MACbma,EAAYnc,EAAKmc,UACjBhgB,EAAS6D,EAAK7D,OACdP,EAAaoE,EAAKpE,WAClBE,EAAYkE,EAAKlE,UACjBG,EAAQ+D,EAAK/D,MACbgE,EAAYvD,KAAKuM,MAAMhJ,UAE3B,GAAIkc,GAAa,GAAKA,EAAYlc,EAAW,CAC3C,IAAIggB,EAAevjB,KAAK2hB,cAAclC,GACtCvgB,EAAa8I,GAAyB,CACpC1C,MAAOA,EACPsd,WAAYW,EAAatC,EACzBzd,SAAU+f,EAAahkB,MACvBgG,cAAehG,EACfiG,cAAetG,EACfuG,YAAaga,IAEfrgB,EAAY4I,GAAyB,CACnC1C,MAAOA,EACPsd,WAAYW,EAAarC,EACzB1d,SAAU+f,EAAa9jB,OACvB8F,cAAe9F,EACf+F,cAAepG,EACfqG,YAAaga,IAIjB,MAAO,CACLvgB,WAAYA,EACZE,UAAWA,KAGd,CACDkF,IAAK,eACLC,MAAO,WACL,MAAO,CACL9E,OAAQO,KAAK2jB,QACbpkB,MAAOS,KAAK4jB,UAGf,CACDtf,IAAK,gBACLC,MAAO,SAAuBC,GAC5B,IAAIkO,EAAS1S,KAETP,EAAS+E,EAAM/E,OACfkN,EAAcnI,EAAMmI,YACpBpN,EAAQiF,EAAMjF,MACd0hB,EAAIzc,EAAMyc,EACVC,EAAI1c,EAAM0c,EACV7P,EAAerR,KAAKuM,MACpBsX,EAAoBxS,EAAawS,kBACjC7O,EAAe3D,EAAa2D,aAQhC,OANAhV,KAAK+iB,yBAA2B/iB,KAAK0jB,gBAAgB3B,eAAe,CAClEtiB,OAAQA,EACRF,MAAOA,EACP0hB,EAAGA,EACHC,EAAGA,IAEE2C,EAAkB,CACvBtN,UAAWvW,KAAK4R,WAChBoD,aAAcA,EACdsO,0BAA2B,SAAmCle,GAC5D,IAAIV,EAAQU,EAAMV,MAClB,OAAOgO,EAAOgR,gBAAgBI,gBAAgB,CAC5Cpf,MAAOA,KAGXmE,QAAS7I,KAAK+iB,yBACdpW,YAAaA,MAGhB,CACDrI,IAAK,qBACLC,MAAO,SAA4BoI,GAC5BA,IACH3M,KAAK4R,WAAa,MAGrB,CACDtN,IAAK,wBACLC,MAAO,SAA+BwJ,GACpC/N,KAAKmjB,gBAAkBpV,MAIpB8U,EA5KT,CA6KE,iBAEF,IAAgB,GAAY,eAAgB,CAC1C,aAAc,OACdgB,kBAwCF,SAAkC3b,GAChC,IAAIqO,EAAYrO,EAAMqO,UAClBvB,EAAe9M,EAAM8M,aACrBsO,EAA4Bpb,EAAMob,0BAClCza,EAAUX,EAAMW,QAChB8D,EAAczE,EAAMyE,YACxB,OAAO9D,EAAQmZ,KAAI,SAAUtd,GAC3B,IAAI6e,EAAeD,EAA0B,CAC3C5e,MAAOA,IAELqf,EAAoB,CACtBrf,MAAOA,EACPiI,YAAaA,EACbrI,IAAKI,EACLpF,MAAO,CACLG,OAAQ8jB,EAAa9jB,OACrBmZ,KAAM2K,EAAatC,EACnBtf,SAAU,WACVkX,IAAK0K,EAAarC,EAClB3hB,MAAOgkB,EAAahkB,QAOxB,OAAIoN,GACIjI,KAAS6R,IACbA,EAAU7R,GAASsQ,EAAa+O,IAG3BxN,EAAU7R,IAEVsQ,EAAa+O,MAErBrY,QAAO,SAAUqN,GAClB,QAASA,QAxEb,GAAWqI,UAkCP,GE1OW,ICWX,GAEJ,SAAU/U,GAGR,SAAS2X,EAAYzX,EAAOuW,GAC1B,IAAItW,EAMJ,OAJA,IAAgBxM,KAAMgkB,IAEtBxX,EAAQ,IAA2BxM,KAAM,IAAgBgkB,GAAa1jB,KAAKN,KAAMuM,EAAOuW,KAClF/F,eAAiBvQ,EAAMuQ,eAAekG,KAAK,IAAuBzW,IACjEA,EA0DT,OAnEA,IAAUwX,EAAa3X,GAYvB,IAAa2X,EAAa,CAAC,CACzB1f,IAAK,qBACLC,MAAO,SAA4BkO,GACjC,IAAI7B,EAAc5Q,KAAKuM,MACnB0X,EAAiBrT,EAAYqT,eAC7BC,EAAiBtT,EAAYsT,eAC7B9V,EAAcwC,EAAYxC,YAC1B7O,EAAQqR,EAAYrR,MAEpB0kB,IAAmBxR,EAAUwR,gBAAkBC,IAAmBzR,EAAUyR,gBAAkB9V,IAAgBqE,EAAUrE,aAAe7O,IAAUkT,EAAUlT,OACzJS,KAAKmkB,kBACPnkB,KAAKmkB,iBAAiBvN,sBAI3B,CACDtS,IAAK,SACLC,MAAO,WACL,IAAI8M,EAAerR,KAAKuM,MACpBuN,EAAWzI,EAAayI,SACxBmK,EAAiB5S,EAAa4S,eAC9BC,EAAiB7S,EAAa6S,eAC9B9V,EAAciD,EAAajD,YAC3B7O,EAAQ8R,EAAa9R,MACrB6kB,EAAqBF,GAAkB,EACvCG,EAAqBJ,EAAiBle,KAAKE,IAAIge,EAAgB1kB,GAASA,EACxE+O,EAAc/O,EAAQ6O,EAK1B,OAJAE,EAAcvI,KAAKC,IAAIoe,EAAoB9V,GAC3CA,EAAcvI,KAAKE,IAAIoe,EAAoB/V,GAC3CA,EAAcvI,KAAKY,MAAM2H,GAElBwL,EAAS,CACdwK,cAFkBve,KAAKE,IAAI1G,EAAO+O,EAAcF,GAGhDE,YAAaA,EACbiW,eAAgB,WACd,OAAOjW,GAETwO,cAAe9c,KAAK+c,mBAGvB,CACDzY,IAAK,iBACLC,MAAO,SAAwBigB,GAC7B,GAAIA,GAA4C,mBAA5BA,EAAM5N,kBACxB,MAAMjS,MAAM,iFAGd3E,KAAKmkB,iBAAmBK,EAEpBxkB,KAAKmkB,kBACPnkB,KAAKmkB,iBAAiBvN,wBAKrBoN,EApET,CAqEE,iBAGF,GAAY5C,UAuBR,GC5GW,I,sBCgBX,GAEJ,SAAU/U,GAGR,SAASoY,EAAelY,EAAOuW,GAC7B,IAAItW,EAQJ,OANA,IAAgBxM,KAAMykB,IAEtBjY,EAAQ,IAA2BxM,KAAM,IAAgBykB,GAAgBnkB,KAAKN,KAAMuM,EAAOuW,KACrF4B,sBAAwBlc,IAC9BgE,EAAMmY,gBAAkBnY,EAAMmY,gBAAgB1B,KAAK,IAAuBzW,IAC1EA,EAAMuQ,eAAiBvQ,EAAMuQ,eAAekG,KAAK,IAAuBzW,IACjEA,EAmGT,OA9GA,IAAUiY,EAAgBpY,GAc1B,IAAaoY,EAAgB,CAAC,CAC5BngB,IAAK,yBACLC,MAAO,SAAgCqgB,GACrC5kB,KAAK0kB,sBAAwBlc,IAEzBoc,GACF5kB,KAAK6kB,SAAS7kB,KAAK8kB,wBAAyB9kB,KAAK+kB,0BAGpD,CACDzgB,IAAK,SACLC,MAAO,WAEL,OAAOuV,EADQ9Z,KAAKuM,MAAMuN,UACV,CACdkL,eAAgBhlB,KAAK2kB,gBACrB7H,cAAe9c,KAAK+c,mBAGvB,CACDzY,IAAK,sBACLC,MAAO,SAA6B0gB,GAClC,IAAIvS,EAAS1S,KAETklB,EAAellB,KAAKuM,MAAM2Y,aAC9BD,EAAe5kB,SAAQ,SAAU8kB,GAC/B,IAAIC,EAAUF,EAAaC,GAEvBC,GACFA,EAAQna,MAAK,WA8HhB,IAAwB/C,EACzBmd,EACAC,EACAvP,EACAC,EAJyB9N,EA3HA,CACjBmd,uBAAwB3S,EAAOoS,wBAC/BQ,sBAAuB5S,EAAOqS,uBAC9BhP,WAAYoP,EAAcpP,WAC1BC,UAAWmP,EAAcnP,WAwHjCqP,EAAyBnd,EAAMmd,uBAC/BC,EAAwBpd,EAAMod,sBAC9BvP,EAAa7N,EAAM6N,WACnBC,EAAY9N,EAAM8N,UACbD,EAAauP,GAAyBtP,EAAYqP,GA1H3C3S,EAAOyR,kBAmNlB,SAA8CoB,GACnD,IAAIC,EAAe9c,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,EACnF+c,EAAuD,mBAAhCF,EAAU3O,kBAAmC2O,EAAU3O,kBAAoB2O,EAAUG,oBAE5GD,EACFA,EAAcnlB,KAAKilB,EAAWC,GAE9BD,EAAU1T,cAzNE8T,CAAqCjT,EAAOyR,iBAAkBzR,EAAOoS,iCAOhF,CACDxgB,IAAK,kBACLC,MAAO,SAAyBjB,GAC9B,IAAIyS,EAAazS,EAAKyS,WAClBC,EAAY1S,EAAK0S,UACrBhW,KAAK8kB,wBAA0B/O,EAC/B/V,KAAK+kB,uBAAyB/O,EAE9BhW,KAAK6kB,SAAS9O,EAAYC,KAE3B,CACD1R,IAAK,WACLC,MAAO,SAAkBwR,EAAYC,GACnC,IAAIxR,EACAqS,EAAS7W,KAET4Q,EAAc5Q,KAAKuM,MACnBqZ,EAAchV,EAAYgV,YAC1BC,EAAmBjV,EAAYiV,iBAC/BpX,EAAWmC,EAAYnC,SACvBqX,EAAYlV,EAAYkV,UACxBb,EAmGH,SAA+B5c,GAUpC,IATA,IAAIud,EAAcvd,EAAMud,YACpBC,EAAmBxd,EAAMwd,iBACzBpX,EAAWpG,EAAMoG,SACjBsH,EAAa1N,EAAM0N,WACnBC,EAAY3N,EAAM2N,UAClBiP,EAAiB,GACjBc,EAAkB,KAClBC,EAAiB,KAEZthB,EAAQqR,EAAYrR,GAASsR,EAAWtR,IAAS,CAC3CkhB,EAAY,CACvBlhB,MAAOA,IASqB,OAAnBshB,IACTf,EAAeliB,KAAK,CAClBgT,WAAYgQ,EACZ/P,UAAWgQ,IAEbD,EAAkBC,EAAiB,OAVnCA,EAAiBthB,EAEO,OAApBqhB,IACFA,EAAkBrhB,IAaxB,GAAuB,OAAnBshB,EAAyB,CAG3B,IAFA,IAAIC,EAAqBlgB,KAAKE,IAAIF,KAAKC,IAAIggB,EAAgBD,EAAkBF,EAAmB,GAAIpX,EAAW,GAEtGyX,EAASF,EAAiB,EAAGE,GAAUD,IACzCL,EAAY,CACflhB,MAAOwhB,IAFyDA,IAIhEF,EAAiBE,EAMrBjB,EAAeliB,KAAK,CAClBgT,WAAYgQ,EACZ/P,UAAWgQ,IAMf,GAAIf,EAAe/jB,OAGjB,IAFA,IAAIilB,EAAqBlB,EAAe,GAEjCkB,EAAmBnQ,UAAYmQ,EAAmBpQ,WAAa,EAAI8P,GAAoBM,EAAmBpQ,WAAa,GAAG,CAC/H,IAAIqQ,EAAUD,EAAmBpQ,WAAa,EAE9C,GAAK6P,EAAY,CACflhB,MAAO0hB,IAIP,MAFAD,EAAmBpQ,WAAaqQ,EAOtC,OAAOnB,EAxKkBoB,CAAsB,CACzCT,YAAaA,EACbC,iBAAkBA,EAClBpX,SAAUA,EACVsH,WAAYhQ,KAAKC,IAAI,EAAG+P,EAAa+P,GACrC9P,UAAWjQ,KAAKE,IAAIwI,EAAW,EAAGuH,EAAY8P,KAG5CQ,GAA0B9hB,EAAQ,IAAII,OAAOkH,MAAMtH,EAAO,KAAmBygB,EAAejD,KAAI,SAAU5c,GAG5G,MAAO,CAFUA,EAAM2Q,WACP3Q,EAAM4Q,gBAIxBhW,KAAK0kB,sBAAsB,CACzB9b,SAAU,WACRiO,EAAO0P,oBAAoBtB,IAE7Bpc,QAAS,CACPyd,uBAAwBA,OAI7B,CACDhiB,IAAK,iBACLC,MAAO,SAAwBiiB,GAC7BxmB,KAAKmkB,iBAAmBqC,MAIrB/B,EA/GT,CAgHE,iBAMF,IAAgB,GAAgB,eAAgB,CAC9CoB,iBAAkB,GAClBpX,SAAU,EACVqX,UAAW,KAIb,GAAe1E,UA2CX,GC1LW,ICQX,GAAQ,GAcR,IAAQ,GAAQ,GAEpB,SAAU/U,GAGR,SAASoa,IACP,IAAIxN,EAEAzM,EAEJ,IAAgBxM,KAAMymB,GAEtB,IAAK,IAAIvN,EAAOxQ,UAAUxH,OAAQiY,EAAO,IAAIjQ,MAAMgQ,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,GAAQ1Q,UAAU0Q,GAoEzB,OAjEA5M,EAAQ,IAA2BxM,MAAOiZ,EAAmB,IAAgBwN,IAAOnmB,KAAKwL,MAAMmN,EAAkB,CAACjZ,MAAM4E,OAAOuU,KAE/H,IAAgB,IAAuB3M,GAAQ,YAAQ,GAEvD,IAAgB,IAAuBA,GAAQ,iBAAiB,SAAUlJ,GACxE,IAAIkT,EAASlT,EAAKkT,OACdnG,EAAW/M,EAAK+M,SAChB/Q,EAAQgE,EAAKhE,MACbqN,EAAcrJ,EAAKqJ,YACnBgM,EAAYrV,EAAKqV,UACjBrU,EAAMhB,EAAKgB,IACXoiB,EAAcla,EAAMD,MAAMma,YAM1BC,EAAkB5d,OAAO6C,yBAAyBtM,EAAO,SAQ7D,OANIqnB,GAAmBA,EAAgBC,WAGrCtnB,EAAMC,MAAQ,QAGTmnB,EAAY,CACjBhiB,MAAO2L,EACP/Q,MAAOA,EACPqN,YAAaA,EACbgM,UAAWA,EACXrU,IAAKA,EACLkS,OAAQA,OAIZ,IAAgB,IAAuBhK,GAAQ,WAAW,SAAUuB,GAClEvB,EAAMF,KAAOyB,KAGf,IAAgB,IAAuBvB,GAAQ,aAAa,SAAUhI,GACpE,IAAIwS,EAAexS,EAAMwS,aACrB3X,EAAemF,EAAMnF,aACrBD,EAAYoF,EAAMpF,WAEtBsV,EADelI,EAAMD,MAAMmI,UAClB,CACPsC,aAAcA,EACd3X,aAAcA,EACdD,UAAWA,OAIf,IAAgB,IAAuBoN,GAAQ,sBAAsB,SAAUpH,GAC7E,IAAImI,EAAwBnI,EAAMmI,sBAC9BE,EAAuBrI,EAAMqI,qBAC7BE,EAAgBvI,EAAMuI,cACtBE,EAAezI,EAAMyI,cAEzBmX,EADqBxY,EAAMD,MAAMyY,gBAClB,CACb9O,mBAAoB3I,EACpB4I,kBAAmB1I,EACnBsI,WAAYpI,EACZqI,UAAWnI,OAIRrB,EA0IT,OAxNA,IAAUia,EAAMpa,GAiFhB,IAAaoa,EAAM,CAAC,CAClBniB,IAAK,kBACLC,MAAO,WACDvE,KAAKsM,MACPtM,KAAKsM,KAAKuF,gBAKb,CACDvN,IAAK,kBACLC,MAAO,SAAyB2D,GAC9B,IAAI+H,EAAY/H,EAAM+H,UAClBvL,EAAQwD,EAAMxD,MAElB,OAAI1E,KAAKsM,KACqBtM,KAAKsM,KAAKua,iBAAiB,CACrD5W,UAAWA,EACXI,SAAU3L,EACVyL,YAAa,IAEuB/Q,UAKjC,IAIR,CACDkF,IAAK,gCACLC,MAAO,SAAuC8D,GAC5C,IAAI8H,EAAc9H,EAAM8H,YACpBE,EAAWhI,EAAMgI,SAEjBrQ,KAAKsM,MACPtM,KAAKsM,KAAKiR,8BAA8B,CACtClN,SAAUA,EACVF,YAAaA,MAMlB,CACD7L,IAAK,iBACLC,MAAO,WACDvE,KAAKsM,MACPtM,KAAKsM,KAAKwa,oBAKb,CACDxiB,IAAK,oBACLC,MAAO,WACL,IAAI+D,EAAQI,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,GAC5Eqe,EAAoBze,EAAM6H,YAC1BA,OAAoC,IAAtB4W,EAA+B,EAAIA,EACjDC,EAAiB1e,EAAM+H,SACvBA,OAA8B,IAAnB2W,EAA4B,EAAIA,EAE3ChnB,KAAKsM,MACPtM,KAAKsM,KAAKsK,kBAAkB,CAC1BvG,SAAUA,EACVF,YAAaA,MAMlB,CACD7L,IAAK,sBACLC,MAAO,WACL,IAAIG,EAAQgE,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,EAE5E1I,KAAKsM,MACPtM,KAAKsM,KAAKsK,kBAAkB,CAC1BvG,SAAU3L,EACVyL,YAAa,MAMlB,CACD7L,IAAK,mBACLC,MAAO,WACL,IAAInF,EAAYsJ,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,EAEhF1I,KAAKsM,MACPtM,KAAKsM,KAAK2a,iBAAiB,CACzB7nB,UAAWA,MAMhB,CACDkF,IAAK,cACLC,MAAO,WACL,IAAIG,EAAQgE,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,EAE5E1I,KAAKsM,MACPtM,KAAKsM,KAAKgT,aAAa,CACrBnP,YAAa,EACbE,SAAU3L,MAIf,CACDJ,IAAK,SACLC,MAAO,WACL,IAAIqM,EAAc5Q,KAAKuM,MACnBzM,EAAY8Q,EAAY9Q,UACxBonB,EAAiBtW,EAAYsW,eAC7BpjB,EAAgB8M,EAAY9M,cAC5BvE,EAAQqR,EAAYrR,MACpB4nB,EAAa,kBAAK,yBAA0BrnB,GAChD,OAAO,gBAAoB,EAAM,IAAS,GAAIE,KAAKuM,MAAO,CACxDwG,oBAAoB,EACpBiC,aAAchV,KAAKonB,cACnBtnB,UAAWqnB,EACX7Y,YAAa/O,EACb6O,YAAa,EACb+E,kBAAmB+T,EACnBxS,SAAU1U,KAAK2U,UACf9H,kBAAmB7M,KAAKka,mBACxBnM,IAAK/N,KAAK6b,QACVvM,YAAaxL,SAKZ2iB,EAzNT,CA0NE,iBAAsB,IAAgB,GAAQ,YAAqD,MA8EjG,IAEJ,IAAgB,GAAM,eAAgB,CACpC5V,YAAY,EACZkH,iBAAkB,GAClBrD,SAAU,aACVwS,eAAgB,WACd,OAAO,MAETlC,eAAgB,aAChB5P,sBAAuB,EACvBC,iBAAkB,GAClBrL,kBAAmB,OACnBlG,eAAgB,EAChBxE,MAAO,K,yBCxGM,QACb+nB,GA5LF,SAA2BC,EAAGpG,EAAGqG,EAAGC,EAAGC,GACrC,MAAiB,mBAANF,EAnBb,SAAcD,EAAGE,EAAGC,EAAGvG,EAAGqG,GAGxB,IAFA,IAAItmB,EAAIwmB,EAAI,EAELD,GAAKC,GAAG,CACb,IAAIC,EAAIF,EAAIC,IAAM,EAGdF,EAFID,EAAEI,GAEDxG,IAAM,GACbjgB,EAAIymB,EACJD,EAAIC,EAAI,GAERF,EAAIE,EAAI,EAIZ,OAAOzmB,EAKE0mB,CAAKL,OAAS,IAANE,EAAe,EAAQ,EAAJA,OAAa,IAANC,EAAeH,EAAEpmB,OAAS,EAAQ,EAAJumB,EAAOvG,EAAGqG,GAtCrF,SAAcD,EAAGE,EAAGC,EAAGvG,GAGrB,IAFA,IAAIjgB,EAAIwmB,EAAI,EAELD,GAAKC,GAAG,CACb,IAAIC,EAAIF,EAAIC,IAAM,EACVH,EAAEI,IAEDxG,GACPjgB,EAAIymB,EACJD,EAAIC,EAAI,GAERF,EAAIE,EAAI,EAIZ,OAAOzmB,EAyBE2mB,CAAKN,OAAS,IAANC,EAAe,EAAQ,EAAJA,OAAa,IAANC,EAAeF,EAAEpmB,OAAS,EAAQ,EAAJsmB,EAAOtG,IAyLhF2G,GAjJF,SAA2BP,EAAGpG,EAAGqG,EAAGC,EAAGC,GACrC,MAAiB,mBAANF,EAnBb,SAAcD,EAAGE,EAAGC,EAAGvG,EAAGqG,GAGxB,IAFA,IAAItmB,EAAIwmB,EAAI,EAELD,GAAKC,GAAG,CACb,IAAIC,EAAIF,EAAIC,IAAM,EAGdF,EAFID,EAAEI,GAEDxG,GAAK,GACZjgB,EAAIymB,EACJD,EAAIC,EAAI,GAERF,EAAIE,EAAI,EAIZ,OAAOzmB,EAKE6mB,CAAKR,OAAS,IAANE,EAAe,EAAQ,EAAJA,OAAa,IAANC,EAAeH,EAAEpmB,OAAS,EAAQ,EAAJumB,EAAOvG,EAAGqG,GAtCrF,SAAcD,EAAGE,EAAGC,EAAGvG,GAGrB,IAFA,IAAIjgB,EAAIwmB,EAAI,EAELD,GAAKC,GAAG,CACb,IAAIC,EAAIF,EAAIC,IAAM,EACVH,EAAEI,GAEFxG,GACNjgB,EAAIymB,EACJD,EAAIC,EAAI,GAERF,EAAIE,EAAI,EAIZ,OAAOzmB,EAyBE8mB,CAAKT,OAAS,IAANC,EAAe,EAAQ,EAAJA,OAAa,IAANC,EAAeF,EAAEpmB,OAAS,EAAQ,EAAJsmB,EAAOtG,IA8IhF8G,GAtGF,SAA2BV,EAAGpG,EAAGqG,EAAGC,EAAGC,GACrC,MAAiB,mBAANF,EAnBb,SAAcD,EAAGE,EAAGC,EAAGvG,EAAGqG,GAGxB,IAFA,IAAItmB,EAAIumB,EAAI,EAELA,GAAKC,GAAG,CACb,IAAIC,EAAIF,EAAIC,IAAM,EAGdF,EAFID,EAAEI,GAEDxG,GAAK,GACZjgB,EAAIymB,EACJF,EAAIE,EAAI,GAERD,EAAIC,EAAI,EAIZ,OAAOzmB,EAKEgnB,CAAKX,OAAS,IAANE,EAAe,EAAQ,EAAJA,OAAa,IAANC,EAAeH,EAAEpmB,OAAS,EAAQ,EAAJumB,EAAOvG,EAAGqG,GAtCrF,SAAcD,EAAGE,EAAGC,EAAGvG,GAGrB,IAFA,IAAIjgB,EAAIumB,EAAI,EAELA,GAAKC,GAAG,CACb,IAAIC,EAAIF,EAAIC,IAAM,EACVH,EAAEI,GAEFxG,GACNjgB,EAAIymB,EACJF,EAAIE,EAAI,GAERD,EAAIC,EAAI,EAIZ,OAAOzmB,EAyBEinB,CAAKZ,OAAS,IAANC,EAAe,EAAQ,EAAJA,OAAa,IAANC,EAAeF,EAAEpmB,OAAS,EAAQ,EAAJsmB,EAAOtG,IAmGhFiH,GA3DF,SAA2Bb,EAAGpG,EAAGqG,EAAGC,EAAGC,GACrC,MAAiB,mBAANF,EAnBb,SAAcD,EAAGE,EAAGC,EAAGvG,EAAGqG,GAGxB,IAFA,IAAItmB,EAAIumB,EAAI,EAELA,GAAKC,GAAG,CACb,IAAIC,EAAIF,EAAIC,IAAM,EAGdF,EAFID,EAAEI,GAEDxG,IAAM,GACbjgB,EAAIymB,EACJF,EAAIE,EAAI,GAERD,EAAIC,EAAI,EAIZ,OAAOzmB,EAKEmnB,CAAKd,OAAS,IAANE,EAAe,EAAQ,EAAJA,OAAa,IAANC,EAAeH,EAAEpmB,OAAS,EAAQ,EAAJumB,EAAOvG,EAAGqG,GAtCrF,SAAcD,EAAGE,EAAGC,EAAGvG,GAGrB,IAFA,IAAIjgB,EAAIumB,EAAI,EAELA,GAAKC,GAAG,CACb,IAAIC,EAAIF,EAAIC,IAAM,EACVH,EAAEI,IAEDxG,GACPjgB,EAAIymB,EACJF,EAAIE,EAAI,GAERD,EAAIC,EAAI,EAIZ,OAAOzmB,EAyBEonB,CAAKf,OAAS,IAANC,EAAe,EAAQ,EAAJA,OAAa,IAANC,EAAeF,EAAEpmB,OAAS,EAAQ,EAAJsmB,EAAOtG,IAwDhFoH,GAbF,SAA2BhB,EAAGpG,EAAGqG,EAAGC,EAAGC,GACrC,MAAiB,mBAANF,EArBb,SAAcD,EAAGE,EAAGC,EAAGvG,EAAGqG,GAGxB,KAAOC,GAAKC,GAAG,CACb,IAAIC,EAAIF,EAAIC,IAAM,EAEdc,EAAIhB,EADAD,EAAEI,GACGxG,GAEb,GAAU,IAANqH,EACF,OAAOb,EACEa,GAAK,EACdf,EAAIE,EAAI,EAERD,EAAIC,EAAI,EAIZ,OAAQ,EAKCc,CAAKlB,OAAS,IAANE,EAAe,EAAQ,EAAJA,OAAa,IAANC,EAAeH,EAAEpmB,OAAS,EAAQ,EAAJumB,EAAOvG,EAAGqG,GAzCrF,SAAcD,EAAGE,EAAGC,EAAGvG,GAGrB,KAAOsG,GAAKC,GAAG,CACb,IAAIC,EAAIF,EAAIC,IAAM,EACdxG,EAAIqG,EAAEI,GAEV,GAAIzG,IAAMC,EACR,OAAOwG,EACEzG,GAAKC,EACdsG,EAAIE,EAAI,EAERD,EAAIC,EAAI,EAIZ,OAAQ,EA2BCe,CAAKnB,OAAS,IAANC,EAAe,EAAQ,EAAJA,OAAa,IAANC,EAAeF,EAAEpmB,OAAS,EAAQ,EAAJsmB,EAAOtG,KCtNlF,SAASwH,GAAiBC,EAAK/P,EAAMkI,EAAO8H,EAAYC,GACtD7oB,KAAK2oB,IAAMA,EACX3oB,KAAK4Y,KAAOA,EACZ5Y,KAAK8gB,MAAQA,EACb9gB,KAAK4oB,WAAaA,EAClB5oB,KAAK6oB,YAAcA,EACnB7oB,KAAK8oB,OAASlQ,EAAOA,EAAKkQ,MAAQ,IAAMhI,EAAQA,EAAMgI,MAAQ,GAAKF,EAAW1nB,OAGhF,IAAI6nB,GAAQL,GAAiBM,UAE7B,SAASC,GAAK3B,EAAG4B,GACf5B,EAAEqB,IAAMO,EAAEP,IACVrB,EAAE1O,KAAOsQ,EAAEtQ,KACX0O,EAAExG,MAAQoI,EAAEpI,MACZwG,EAAEsB,WAAaM,EAAEN,WACjBtB,EAAEuB,YAAcK,EAAEL,YAClBvB,EAAEwB,MAAQI,EAAEJ,MAGd,SAASK,GAAQnM,EAAMoM,GACrB,IAAIC,EAAQC,GAAmBF,GAC/BpM,EAAK2L,IAAMU,EAAMV,IACjB3L,EAAKpE,KAAOyQ,EAAMzQ,KAClBoE,EAAK8D,MAAQuI,EAAMvI,MACnB9D,EAAK4L,WAAaS,EAAMT,WACxB5L,EAAK6L,YAAcQ,EAAMR,YACzB7L,EAAK8L,MAAQO,EAAMP,MAGrB,SAASS,GAAoBvM,EAAMpW,GACjC,IAAIwiB,EAAYpM,EAAKoM,UAAU,IAC/BA,EAAUrmB,KAAK6D,GACfuiB,GAAQnM,EAAMoM,GAGhB,SAASI,GAAuBxM,EAAMpW,GACpC,IAAIwiB,EAAYpM,EAAKoM,UAAU,IAC3BK,EAAML,EAAUrpB,QAAQ6G,GAE5B,OAAI6iB,EAAM,EA5CI,GAgDdL,EAAUlmB,OAAOumB,EAAK,GACtBN,GAAQnM,EAAMoM,GAhDF,GAkNd,SAASM,GAAgBC,EAAKC,EAAIC,GAChC,IAAK,IAAI5oB,EAAI,EAAGA,EAAI0oB,EAAIzoB,QAAUyoB,EAAI1oB,GAAG,IAAM2oB,IAAM3oB,EAAG,CACtD,IAAI6oB,EAAID,EAAGF,EAAI1oB,IAEf,GAAI6oB,EACF,OAAOA,GAKb,SAASC,GAAiBJ,EAAKK,EAAIH,GACjC,IAAK,IAAI5oB,EAAI0oB,EAAIzoB,OAAS,EAAGD,GAAK,GAAK0oB,EAAI1oB,GAAG,IAAM+oB,IAAM/oB,EAAG,CAC3D,IAAI6oB,EAAID,EAAGF,EAAI1oB,IAEf,GAAI6oB,EACF,OAAOA,GAKb,SAASG,GAAYN,EAAKE,GACxB,IAAK,IAAI5oB,EAAI,EAAGA,EAAI0oB,EAAIzoB,SAAUD,EAAG,CACnC,IAAI6oB,EAAID,EAAGF,EAAI1oB,IAEf,GAAI6oB,EACF,OAAOA,GAyDb,SAASI,GAAe5C,EAAG4B,GACzB,OAAO5B,EAAI4B,EAGb,SAASiB,GAAa7C,EAAG4B,GACvB,IAAIkB,EAAI9C,EAAE,GAAK4B,EAAE,GAEjB,OAAIkB,GAIG9C,EAAE,GAAK4B,EAAE,GAGlB,SAASmB,GAAW/C,EAAG4B,GACrB,IAAIkB,EAAI9C,EAAE,GAAK4B,EAAE,GAEjB,OAAIkB,GAIG9C,EAAE,GAAK4B,EAAE,GAGlB,SAASI,GAAmBF,GAC1B,GAAyB,IAArBA,EAAUloB,OACZ,OAAO,KAKT,IAFA,IAAIopB,EAAM,GAEDrpB,EAAI,EAAGA,EAAImoB,EAAUloB,SAAUD,EACtCqpB,EAAIvnB,KAAKqmB,EAAUnoB,GAAG,GAAImoB,EAAUnoB,GAAG,IAGzCqpB,EAAIC,KAAKL,IACT,IAAIvB,EAAM2B,EAAIA,EAAIppB,QAAU,GACxBspB,EAAgB,GAChBC,EAAiB,GACjBC,EAAkB,GAEtB,IAASzpB,EAAI,EAAGA,EAAImoB,EAAUloB,SAAUD,EAAG,CACzC,IAAI0pB,EAAIvB,EAAUnoB,GAEd0pB,EAAE,GAAKhC,EACT6B,EAAcznB,KAAK4nB,GACVhC,EAAMgC,EAAE,GACjBF,EAAe1nB,KAAK4nB,GAEpBD,EAAgB3nB,KAAK4nB,GAKzB,IAAI/B,EAAa8B,EACb7B,EAAc6B,EAAgBE,QAGlC,OAFAhC,EAAW2B,KAAKJ,IAChBtB,EAAY0B,KAAKF,IACV,IAAI3B,GAAiBC,EAAKW,GAAmBkB,GAAgBlB,GAAmBmB,GAAiB7B,EAAYC,GAItH,SAASgC,GAAaC,GACpB9qB,KAAK8qB,KAAOA,EA/Sd/B,GAAMK,UAAY,SAAU2B,GAW1B,OAVAA,EAAOhoB,KAAK+I,MAAMif,EAAQ/qB,KAAK4oB,YAE3B5oB,KAAK4Y,MACP5Y,KAAK4Y,KAAKwQ,UAAU2B,GAGlB/qB,KAAK8gB,OACP9gB,KAAK8gB,MAAMsI,UAAU2B,GAGhBA,GAGThC,GAAMiC,OAAS,SAAUpkB,GACvB,IAAIqkB,EAASjrB,KAAK8oB,MAAQ9oB,KAAK4oB,WAAW1nB,OAG1C,GAFAlB,KAAK8oB,OAAS,EAEVliB,EAAS,GAAK5G,KAAK2oB,IACjB3oB,KAAK4Y,KACH,GAAK5Y,KAAK4Y,KAAKkQ,MAAQ,GAAK,GAAKmC,EAAS,GAC5C1B,GAAoBvpB,KAAM4G,GAE1B5G,KAAK4Y,KAAKoS,OAAOpkB,GAGnB5G,KAAK4Y,KAAO0Q,GAAmB,CAAC1iB,SAE7B,GAAIA,EAAS,GAAK5G,KAAK2oB,IACxB3oB,KAAK8gB,MACH,GAAK9gB,KAAK8gB,MAAMgI,MAAQ,GAAK,GAAKmC,EAAS,GAC7C1B,GAAoBvpB,KAAM4G,GAE1B5G,KAAK8gB,MAAMkK,OAAOpkB,GAGpB5G,KAAK8gB,MAAQwI,GAAmB,CAAC1iB,QAE9B,CACL,IAAI4gB,EAAI,GAAOH,GAAGrnB,KAAK4oB,WAAYhiB,EAAUujB,IACzCL,EAAI,GAAOzC,GAAGrnB,KAAK6oB,YAAajiB,EAAUyjB,IAC9CrqB,KAAK4oB,WAAW1lB,OAAOskB,EAAG,EAAG5gB,GAC7B5G,KAAK6oB,YAAY3lB,OAAO4mB,EAAG,EAAGljB,KAIlCmiB,GAAMmC,OAAS,SAAUtkB,GACvB,IAAIqkB,EAASjrB,KAAK8oB,MAAQ9oB,KAAK4oB,WAE/B,GAAIhiB,EAAS,GAAK5G,KAAK2oB,IACrB,OAAK3oB,KAAK4Y,KAMN,GAFK5Y,KAAK8gB,MAAQ9gB,KAAK8gB,MAAMgI,MAAQ,GAE5B,GAAKmC,EAAS,GAClBzB,GAAuBxpB,KAAM4G,GA5G9B,KA+GJkjB,EAAI9pB,KAAK4Y,KAAKsS,OAAOtkB,KAGvB5G,KAAK4Y,KAAO,KACZ5Y,KAAK8oB,OAAS,EApHN,QAsHCgB,IACT9pB,KAAK8oB,OAAS,GAGTgB,GA3HK,EA4HP,GAAIljB,EAAS,GAAK5G,KAAK2oB,IAC5B,OAAK3oB,KAAK8gB,MAMN,GAFK9gB,KAAK4Y,KAAO5Y,KAAK4Y,KAAKkQ,MAAQ,GAE1B,GAAKmC,EAAS,GAClBzB,GAAuBxpB,KAAM4G,GAlI9B,KAqIJkjB,EAAI9pB,KAAK8gB,MAAMoK,OAAOtkB,KAGxB5G,KAAK8gB,MAAQ,KACb9gB,KAAK8oB,OAAS,EA1IN,QA4ICgB,IACT9pB,KAAK8oB,OAAS,GAGTgB,GAjJK,EAmJZ,GAAmB,IAAf9pB,KAAK8oB,MACP,OAAI9oB,KAAK4oB,WAAW,KAAOhiB,EAlJrB,EAFI,EA2JZ,GAA+B,IAA3B5G,KAAK4oB,WAAW1nB,QAAgBlB,KAAK4oB,WAAW,KAAOhiB,EAAU,CACnE,GAAI5G,KAAK4Y,MAAQ5Y,KAAK8gB,MAAO,CAI3B,IAHA,IAAIyH,EAAIvoB,KACJmrB,EAAInrB,KAAK4Y,KAENuS,EAAErK,OACPyH,EAAI4C,EACJA,EAAIA,EAAErK,MAGR,GAAIyH,IAAMvoB,KACRmrB,EAAErK,MAAQ9gB,KAAK8gB,UACV,CACL,IAAI0G,EAAIxnB,KAAK4Y,KACTkR,EAAI9pB,KAAK8gB,MACbyH,EAAEO,OAASqC,EAAErC,MACbP,EAAEzH,MAAQqK,EAAEvS,KACZuS,EAAEvS,KAAO4O,EACT2D,EAAErK,MAAQgJ,EAGZb,GAAKjpB,KAAMmrB,GACXnrB,KAAK8oB,OAAS9oB,KAAK4Y,KAAO5Y,KAAK4Y,KAAKkQ,MAAQ,IAAM9oB,KAAK8gB,MAAQ9gB,KAAK8gB,MAAMgI,MAAQ,GAAK9oB,KAAK4oB,WAAW1nB,YAC9FlB,KAAK4Y,KACdqQ,GAAKjpB,KAAMA,KAAK4Y,MAEhBqQ,GAAKjpB,KAAMA,KAAK8gB,OAGlB,OAvLQ,EA0LV,IAAS0G,EAAI,GAAOH,GAAGrnB,KAAK4oB,WAAYhiB,EAAUujB,IAAe3C,EAAIxnB,KAAK4oB,WAAW1nB,QAC/ElB,KAAK4oB,WAAWpB,GAAG,KAAO5gB,EAAS,KADsD4gB,EAK7F,GAAIxnB,KAAK4oB,WAAWpB,KAAO5gB,EAAU,CACnC5G,KAAK8oB,OAAS,EACd9oB,KAAK4oB,WAAW1lB,OAAOskB,EAAG,GAE1B,IAASsC,EAAI,GAAOzC,GAAGrnB,KAAK6oB,YAAajiB,EAAUyjB,IAAaP,EAAI9pB,KAAK6oB,YAAY3nB,QAC/ElB,KAAK6oB,YAAYiB,GAAG,KAAOljB,EAAS,KADqDkjB,EAGtF,GAAI9pB,KAAK6oB,YAAYiB,KAAOljB,EAEjC,OADA5G,KAAK6oB,YAAY3lB,OAAO4mB,EAAG,GAvMzB,EA8MV,OA/MY,GAiPhBf,GAAMqC,WAAa,SAAUnK,EAAG4I,GAC9B,GAAI5I,EAAIjhB,KAAK2oB,IAAK,CAChB,GAAI3oB,KAAK4Y,KAGP,GAFIkR,EAAI9pB,KAAK4Y,KAAKwS,WAAWnK,EAAG4I,GAG9B,OAAOC,EAIX,OAAOJ,GAAgB1pB,KAAK4oB,WAAY3H,EAAG4I,GACtC,GAAI5I,EAAIjhB,KAAK2oB,IAAK,CAErB,IAAImB,EADN,GAAI9pB,KAAK8gB,MAGP,GAFIgJ,EAAI9pB,KAAK8gB,MAAMsK,WAAWnK,EAAG4I,GAG/B,OAAOC,EAIX,OAAOC,GAAiB/pB,KAAK6oB,YAAa5H,EAAG4I,GAE7C,OAAOI,GAAYjqB,KAAK4oB,WAAYiB,IAIxCd,GAAMsC,cAAgB,SAAUrB,EAAIJ,EAAIC,GAEpC,IAQIC,EATN,GAAIE,EAAKhqB,KAAK2oB,KAAO3oB,KAAK4Y,OACpBkR,EAAI9pB,KAAK4Y,KAAKyS,cAAcrB,EAAIJ,EAAIC,IAGtC,OAAOC,EAIX,GAAIF,EAAK5pB,KAAK2oB,KAAO3oB,KAAK8gB,QACpBgJ,EAAI9pB,KAAK8gB,MAAMuK,cAAcrB,EAAIJ,EAAIC,IAGvC,OAAOC,EAIX,OAAIF,EAAK5pB,KAAK2oB,IACLe,GAAgB1pB,KAAK4oB,WAAYgB,EAAIC,GACnCG,EAAKhqB,KAAK2oB,IACZoB,GAAiB/pB,KAAK6oB,YAAamB,EAAIH,GAEvCI,GAAYjqB,KAAK4oB,WAAYiB,IAsExC,IAAIyB,GAAST,GAAa7B,UAE1BsC,GAAON,OAAS,SAAUpkB,GACpB5G,KAAK8qB,KACP9qB,KAAK8qB,KAAKE,OAAOpkB,GAEjB5G,KAAK8qB,KAAO,IAAIpC,GAAiB9hB,EAAS,GAAI,KAAM,KAAM,CAACA,GAAW,CAACA,KAI3E0kB,GAAOJ,OAAS,SAAUtkB,GACxB,GAAI5G,KAAK8qB,KAAM,CACb,IAAIhB,EAAI9pB,KAAK8qB,KAAKI,OAAOtkB,GAMzB,OAvXQ,IAmXJkjB,IACF9pB,KAAK8qB,KAAO,MAtXF,IAyXLhB,EAGT,OAAO,GAGTwB,GAAOF,WAAa,SAAU7C,EAAGsB,GAC/B,GAAI7pB,KAAK8qB,KACP,OAAO9qB,KAAK8qB,KAAKM,WAAW7C,EAAGsB,IAInCyB,GAAOD,cAAgB,SAAUrB,EAAIJ,EAAIC,GACvC,GAAIG,GAAMJ,GAAM5pB,KAAK8qB,KACnB,OAAO9qB,KAAK8qB,KAAKO,cAAcrB,EAAIJ,EAAIC,IAI3C9gB,OAAOoD,eAAemf,GAAQ,QAAS,CACrCrM,IAAK,WACH,OAAIjf,KAAK8qB,KACA9qB,KAAK8qB,KAAKhC,MAGZ,KAGX/f,OAAOoD,eAAemf,GAAQ,YAAa,CACzCrM,IAAK,WACH,OAAIjf,KAAK8qB,KACA9qB,KAAK8qB,KAAK1B,UAAU,IAGtB,MC1ZX,ICDI,GAAQ,GDCR,GAEJ,WACE,SAASmC,ID0ZI,IAAuBnC,ECzZlC,IAAgBppB,KAAMurB,GAEtB,IAAgBvrB,KAAM,iBAAkB,IAExC,IAAgBA,KAAM,gBDsZnBopB,GAAkC,IAArBA,EAAUloB,OAIrB,IAAI2pB,GAAavB,GAAmBF,IAHlC,IAAIyB,GAAa,OCrZxB,IAAgB7qB,KAAM,WAAY,IAwEpC,OArEA,IAAaurB,EAAe,CAAC,CAC3BjnB,IAAK,sBACLC,MAAO,SAA6BhB,EAAW6K,EAAaod,GAC1D,IAAIC,EAAsBloB,EAAYvD,KAAK8oB,MAC3C,OAAO9oB,KAAK0rB,kBAAoB3lB,KAAKoX,KAAKsO,EAAsBrd,GAAeod,IAGhF,CACDlnB,IAAK,QACLC,MAAO,SAAenF,EAAW4X,EAAc2U,GAC7C,IAAInf,EAAQxM,KAEZA,KAAK4rB,cAAcP,cAAcjsB,EAAWA,EAAY4X,GAAc,SAAU1T,GAC9E,IAAIkB,EAAQ,KAAelB,EAAM,GAC7BuV,EAAMrU,EAAM,GAEZE,GADIF,EAAM,GACFA,EAAM,IAElB,OAAOmnB,EAAejnB,EAAO8H,EAAMqf,SAASnnB,GAAQmU,QAGvD,CACDvU,IAAK,cACLC,MAAO,SAAqBG,EAAOkU,EAAMC,EAAKpZ,GAC5CO,KAAK4rB,cAAcZ,OAAO,CAACnS,EAAKA,EAAMpZ,EAAQiF,IAE9C1E,KAAK6rB,SAASnnB,GAASkU,EACvB,IAAIkT,EAAgB9rB,KAAK+rB,eACrBC,EAAeF,EAAclT,GAG/BkT,EAAclT,QADK7X,IAAjBirB,EACoBnT,EAAMpZ,EAENsG,KAAKC,IAAIgmB,EAAcnT,EAAMpZ,KAGtD,CACD6E,IAAK,QACL2a,IAAK,WACH,OAAOjf,KAAK4rB,cAAc9C,QAE3B,CACDxkB,IAAK,qBACL2a,IAAK,WACH,IAAI6M,EAAgB9rB,KAAK+rB,eACrB/mB,EAAO,EAEX,IAAK,IAAI/D,KAAK6qB,EAAe,CAC3B,IAAIrsB,EAASqsB,EAAc7qB,GAC3B+D,EAAgB,IAATA,EAAavF,EAASsG,KAAKE,IAAIjB,EAAMvF,GAG9C,OAAOuF,IAER,CACDV,IAAK,oBACL2a,IAAK,WACH,IAAI6M,EAAgB9rB,KAAK+rB,eACrB/mB,EAAO,EAEX,IAAK,IAAI/D,KAAK6qB,EAAe,CAC3B,IAAIrsB,EAASqsB,EAAc7qB,GAC3B+D,EAAOe,KAAKC,IAAIhB,EAAMvF,GAGxB,OAAOuF,MAIJumB,EAhFT,GCDA,SAAS,GAAQjgB,EAAQC,GAAkB,IAAIzC,EAAOC,OAAOD,KAAKwC,GAAS,GAAIvC,OAAOyC,sBAAuB,CAAE,IAAIC,EAAU1C,OAAOyC,sBAAsBF,GAAaC,IAAgBE,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAO5C,OAAO6C,yBAAyBN,EAAQK,GAAKE,eAAgB/C,EAAK/F,KAAK+I,MAAMhD,EAAM2C,GAAY,OAAO3C,EAE9U,SAAS,GAAcjJ,GAAU,IAAK,IAAIoB,EAAI,EAAGA,EAAIyH,UAAUxH,OAAQD,IAAK,CAAE,IAAI+K,EAAyB,MAAhBtD,UAAUzH,GAAayH,UAAUzH,GAAK,GAAQA,EAAI,EAAK,GAAQ+K,GAAQ,GAAM3L,SAAQ,SAAUiE,GAAO,IAAgBzE,EAAQyE,EAAK0H,EAAO1H,OAAsByE,OAAOkD,0BAA6BlD,OAAOmD,iBAAiBrM,EAAQkJ,OAAOkD,0BAA0BD,IAAmB,GAAQA,GAAQ3L,SAAQ,SAAUiE,GAAOyE,OAAOoD,eAAetM,EAAQyE,EAAKyE,OAAO6C,yBAAyBI,EAAQ1H,OAAe,OAAOzE,EAO7f,IAoCI,IAAW,GAAQ,GAEvB,SAAUwM,GAGR,SAAS4f,IACP,IAAIhT,EAEAzM,EAEJ,IAAgBxM,KAAMisB,GAEtB,IAAK,IAAI/S,EAAOxQ,UAAUxH,OAAQiY,EAAO,IAAIjQ,MAAMgQ,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,GAAQ1Q,UAAU0Q,GAiEzB,OA9DA5M,EAAQ,IAA2BxM,MAAOiZ,EAAmB,IAAgBgT,IAAU3rB,KAAKwL,MAAMmN,EAAkB,CAACjZ,MAAM4E,OAAOuU,KAElI,IAAgB,IAAuB3M,GAAQ,QAAS,CACtDG,aAAa,EACbvN,UAAW,IAGb,IAAgB,IAAuBoN,GAAQ,mCAA+B,GAE9E,IAAgB,IAAuBA,GAAQ,gCAAiC,MAEhF,IAAgB,IAAuBA,GAAQ,+BAAgC,MAE/E,IAAgB,IAAuBA,GAAQ,iBAAkB,IAAI,IAErE,IAAgB,IAAuBA,GAAQ,cAAe,MAE9D,IAAgB,IAAuBA,GAAQ,sBAAuB,MAEtE,IAAgB,IAAuBA,GAAQ,aAAc,MAE7D,IAAgB,IAAuBA,GAAQ,qBAAsB,MAErE,IAAgB,IAAuBA,GAAQ,qCAAqC,WAClFA,EAAME,SAAS,CACbC,aAAa,OAIjB,IAAgB,IAAuBH,GAAQ,6BAA6B,SAAUuB,GACpFvB,EAAMwB,oBAAsBD,KAG9B,IAAgB,IAAuBvB,GAAQ,aAAa,SAAUyB,GACpE,IAAIxO,EAAS+M,EAAMD,MAAM9M,OACrBysB,EAAiBje,EAAMke,cAAc/sB,UAKrCA,EAAY2G,KAAKE,IAAIF,KAAKC,IAAI,EAAGwG,EAAM4f,2BAA6B3sB,GAASysB,GAG7EA,IAAmB9sB,IAKvBoN,EAAM6f,4BAMF7f,EAAMoC,MAAMxP,YAAcA,GAC5BoN,EAAME,SAAS,CACbC,aAAa,EACbvN,UAAWA,QAKVoN,EAsQT,OAjVA,IAAUyf,EAAS5f,GA8EnB,IAAa4f,EAAS,CAAC,CACrB3nB,IAAK,qBACLC,MAAO,WACLvE,KAAKssB,eAAiB,IAAI,GAC1BtsB,KAAK6R,gBAGN,CACDvN,IAAK,gCACLC,MAAO,SAAuCjB,GAC5C,IAAIoB,EAAQpB,EAAK+M,SAE0B,OAAvCrQ,KAAKusB,+BACPvsB,KAAKusB,8BAAgC7nB,EACrC1E,KAAKwsB,6BAA+B9nB,IAEpC1E,KAAKusB,8BAAgCxmB,KAAKE,IAAIjG,KAAKusB,8BAA+B7nB,GAClF1E,KAAKwsB,6BAA+BzmB,KAAKC,IAAIhG,KAAKwsB,6BAA8B9nB,MAGnF,CACDJ,IAAK,yBACLC,MAAO,WACL,IAAIyR,EAAYhW,KAAKssB,eAAexD,MAAQ,EAC5C9oB,KAAKssB,eAAiB,IAAI,GAE1BtsB,KAAKysB,uBAAuB,EAAGzW,GAE/BhW,KAAK6R,gBAEN,CACDvN,IAAK,oBACLC,MAAO,WACLvE,KAAK0sB,2BAEL1sB,KAAK2sB,0BAEL3sB,KAAK4sB,mCAEN,CACDtoB,IAAK,qBACLC,MAAO,SAA4BkO,EAAWN,GAC5CnS,KAAK0sB,2BAEL1sB,KAAK2sB,0BAEL3sB,KAAK4sB,iCAED5sB,KAAKuM,MAAMnN,YAAcqT,EAAUrT,WACrCY,KAAKqsB,8BAGR,CACD/nB,IAAK,uBACLC,MAAO,WACDvE,KAAK6sB,6BACP,EAAuB7sB,KAAK6sB,+BAG/B,CACDvoB,IAAK,SACLC,MAAO,WACL,IA2BIyR,EA3BAtD,EAAS1S,KAET4Q,EAAc5Q,KAAKuM,MACnBsE,EAAaD,EAAYC,WACzBtN,EAAYqN,EAAYrN,UACxBupB,EAAoBlc,EAAYkc,kBAChC9X,EAAepE,EAAYoE,aAC3BlV,EAAY8Q,EAAY9Q,UACxBL,EAASmR,EAAYnR,OACrBjB,EAAKoS,EAAYpS,GACjBwf,EAAYpN,EAAYoN,UACxB+O,EAAmBnc,EAAYmc,iBAC/B3Z,EAAOxC,EAAYwC,KACnB9T,EAAQsR,EAAYtR,MACpB+T,EAAWzC,EAAYyC,SACvB9T,EAAQqR,EAAYrR,MACpBytB,EAAepc,EAAYoc,aAC3Bpa,EAAc5S,KAAK4O,MACnBjC,EAAciG,EAAYjG,YAC1BvN,EAAYwT,EAAYxT,UACxB0a,EAAW,GAEXmT,EAAsBjtB,KAAKosB,2BAE3Bc,EAAqBltB,KAAKssB,eAAeY,mBACzCC,EAAoBntB,KAAKssB,eAAexD,MACxC/S,EAAa,EA0BjB,GAvBA/V,KAAKssB,eAAec,MAAMrnB,KAAKC,IAAI,EAAG5G,EAAY2tB,GAAmBttB,EAA4B,EAAnBstB,GAAsB,SAAUroB,EAAOkU,EAAMC,GACzH,IAAIwU,OAEqB,IAAdrX,GACTD,EAAarR,EACbsR,EAAYtR,IAEZqR,EAAahQ,KAAKE,IAAI8P,EAAYrR,GAClCsR,EAAYjQ,KAAKC,IAAIgQ,EAAWtR,IAGlCoV,EAAS/W,KAAKiS,EAAa,CACzBtQ,MAAOA,EACPiI,YAAaA,EACbrI,IAAK0Z,EAAUtZ,GACf8R,OAAQ9D,EACRpT,OAAQ+tB,EAAS,CACf5tB,OAAQqtB,EAAkB1Q,UAAU1X,IACnC,IAAgB2oB,EAAyB,QAAjBL,EAAyB,OAAS,QAASpU,GAAO,IAAgByU,EAAQ,WAAY,YAAa,IAAgBA,EAAQ,MAAOxU,GAAM,IAAgBwU,EAAQ,QAASP,EAAkBzQ,SAAS3X,IAAS2oB,SAKxOH,EAAqB9tB,EAAYK,EAASstB,GAAoBI,EAAoB5pB,EAGpF,IAFA,IAAI+pB,EAAYvnB,KAAKE,IAAI1C,EAAY4pB,EAAmBpnB,KAAKoX,MAAM/d,EAAYK,EAASstB,EAAmBG,GAAsBJ,EAAkBvS,cAAgBhb,EAAQutB,EAAkBtS,eAEpL0L,EAASiH,EAAmBjH,EAASiH,EAAoBG,EAAWpH,IAC3ElQ,EAAYkQ,EACZpM,EAAS/W,KAAKiS,EAAa,CACzBtQ,MAAOwhB,EACPvZ,YAAaA,EACbrI,IAAK0Z,EAAUkI,GACf1P,OAAQxW,KACRV,MAAO,CACLC,MAAOutB,EAAkBzQ,SAAS6J,OAQ1C,OAFAlmB,KAAKutB,YAAcxX,EACnB/V,KAAKwtB,WAAaxX,EACX,gBAAoB,MAAO,CAChCjI,IAAK/N,KAAKyU,0BACV,aAAczU,KAAKuM,MAAM,cACzBzM,UAAW,kBAAK,4BAA6BA,GAC7CtB,GAAIA,EACJkW,SAAU1U,KAAK2U,UACfvB,KAAMA,EACN9T,MAAO,GAAc,CACnBmU,UAAW,aACXC,UAAW,MACXjU,OAAQoR,EAAa,OAASpR,EAC9B2U,UAAW,SACXC,UAAW4Y,EAAsBxtB,EAAS,SAAW,OACrDkC,SAAU,WACVpC,MAAOA,EACPoU,wBAAyB,QACzBC,WAAY,aACXtU,GACH+T,SAAUA,GACT,gBAAoB,MAAO,CAC5BvT,UAAW,kDACXR,MAAO,CACLC,MAAO,OACPE,OAAQwtB,EACRrY,SAAU,OACVC,UAAWoY,EACXnY,SAAU,SACVC,cAAepI,EAAc,OAAS,GACtChL,SAAU,aAEXmY,MAEJ,CACDxV,IAAK,2BACLC,MAAO,WACL,GAAkD,iBAAvCvE,KAAKusB,8BAA4C,CAC1D,IAAIxW,EAAa/V,KAAKusB,8BAClBvW,EAAYhW,KAAKwsB,6BACrBxsB,KAAKusB,8BAAgC,KACrCvsB,KAAKwsB,6BAA+B,KAEpCxsB,KAAKysB,uBAAuB1W,EAAYC,GAExChW,KAAK6R,iBAGR,CACDvN,IAAK,4BACLC,MAAO,WACL,IAAImS,EAA6B1W,KAAKuM,MAAMmK,2BAExC1W,KAAK6sB,6BACP,EAAuB7sB,KAAK6sB,6BAG9B7sB,KAAK6sB,4BAA8B,EAAwB7sB,KAAKytB,kCAAmC/W,KAEpG,CACDpS,IAAK,2BACLC,MAAO,WACL,IAAI8M,EAAerR,KAAKuM,MACpBhJ,EAAY8N,EAAa9N,UACzBupB,EAAoBzb,EAAayb,kBACjCvtB,EAAQ8R,EAAa9R,MACrBmuB,EAAuB3nB,KAAKC,IAAI,EAAGD,KAAKY,MAAMpH,EAAQutB,EAAkBtS,eAC5E,OAAOxa,KAAKssB,eAAeW,oBAAoB1pB,EAAWmqB,EAAsBZ,EAAkBvS,iBAEnG,CACDjW,IAAK,0BACLC,MAAO,WACL,IAAIiN,EAAexR,KAAKuM,MACpB9M,EAAS+R,EAAa/R,OACtBiV,EAAWlD,EAAakD,SACxBtV,EAAYY,KAAK4O,MAAMxP,UAEvBY,KAAK2tB,oBAAsBvuB,IAC7BsV,EAAS,CACPsC,aAAcvX,EACdJ,aAAcW,KAAKosB,2BACnBhtB,UAAWA,IAEbY,KAAK2tB,kBAAoBvuB,KAG5B,CACDkF,IAAK,iCACLC,MAAO,WACDvE,KAAK4tB,sBAAwB5tB,KAAKutB,aAAevtB,KAAK6tB,qBAAuB7tB,KAAKwtB,cAEpFM,EADsB9tB,KAAKuM,MAAMuhB,iBACjB,CACd/X,WAAY/V,KAAKutB,YACjBvX,UAAWhW,KAAKwtB,aAElBxtB,KAAK4tB,oBAAsB5tB,KAAKutB,YAChCvtB,KAAK6tB,mBAAqB7tB,KAAKwtB,cAGlC,CACDlpB,IAAK,yBACLC,MAAO,SAAgCwR,EAAYC,GAKjD,IAJA,IAAIhE,EAAehS,KAAKuM,MACpBugB,EAAoB9a,EAAa8a,kBACjCiB,EAAiB/b,EAAa+b,eAEzB3H,EAAUrQ,EAAYqQ,GAAWpQ,EAAWoQ,IAAW,CAC9D,IAAI4H,EAAkBD,EAAe3H,GACjCxN,EAAOoV,EAAgBpV,KACvBC,EAAMmV,EAAgBnV,IAE1B7Y,KAAKssB,eAAe2B,YAAY7H,EAASxN,EAAMC,EAAKiU,EAAkB1Q,UAAUgK,QAGlF,CAAC,CACH9hB,IAAK,2BACLC,MAAO,SAAkCmT,EAAWvF,GAClD,YAA4BpR,IAAxB2W,EAAUtY,WAA2B+S,EAAU/S,YAAcsY,EAAUtY,UAClE,CACLuN,aAAa,EACbvN,UAAWsY,EAAUtY,WAIlB,SAIJ6sB,EAlVT,CAmVE,iBAAsB,IAAgB,GAAQ,YAAqD,MAoCjG,IAmBJ,SAASiC,MAjBT,IAAgB,GAAS,eAAgB,CACvCrd,YAAY,EACZmN,UAWF,SAAkBzZ,GAChB,OAAOA,GAXPupB,gBAAiBI,GACjBxZ,SAAUwZ,GACVnB,iBAAkB,GAClB3Z,KAAM,OACNsD,2BAhaiD,IAiajDpX,MAvagB,GAwahB+T,SAAU,EACV2Z,aAAc,QAehB,mBAAS,ICzcM,ICMX,GAEJ,WACE,SAASmB,IACP,IAAI3hB,EAAQxM,KAERoG,EAASsC,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,GAEjF,IAAgB1I,KAAMmuB,GAEtB,IAAgBnuB,KAAM,0BAAsB,GAE5C,IAAgBA,KAAM,0BAAsB,GAE5C,IAAgBA,KAAM,uBAAmB,GAEzC,IAAgBA,KAAM,eAAe,SAAUsD,GAC7C,IAAIoB,EAAQpB,EAAKoB,MAEjB8H,EAAM4hB,mBAAmB9f,YAAY,CACnC5J,MAAOA,EAAQ8H,EAAM6hB,wBAIzB,IAAgBruB,KAAM,aAAa,SAAUwE,GAC3C,IAAIE,EAAQF,EAAME,MAElB8H,EAAM4hB,mBAAmB1f,UAAU,CACjChK,MAAOA,EAAQ8H,EAAM8hB,qBAIzB,IAAIxB,EAAoB1mB,EAAO0mB,kBAC3ByB,EAAwBnoB,EAAOooB,kBAC/BA,OAA8C,IAA1BD,EAAmC,EAAIA,EAC3DE,EAAwBroB,EAAOsoB,eAC/BA,OAA2C,IAA1BD,EAAmC,EAAIA,EAC5DzuB,KAAKouB,mBAAqBtB,EAC1B9sB,KAAKquB,mBAAqBG,EAC1BxuB,KAAKsuB,gBAAkBI,EA0DzB,OAvDA,IAAaP,EAA4B,CAAC,CACxC7pB,IAAK,QACLC,MAAO,SAAe8L,EAAUF,GAC9BnQ,KAAKouB,mBAAmBO,MAAMte,EAAWrQ,KAAKsuB,gBAAiBne,EAAcnQ,KAAKquB,sBAEnF,CACD/pB,IAAK,WACLC,MAAO,WACLvE,KAAKouB,mBAAmBQ,aAEzB,CACDtqB,IAAK,iBACLC,MAAO,WACL,OAAOvE,KAAKouB,mBAAmBhY,mBAEhC,CACD9R,IAAK,gBACLC,MAAO,WACL,OAAOvE,KAAKouB,mBAAmB9X,kBAEhC,CACDhS,IAAK,YACLC,MAAO,SAAmB8L,GACxB,IAAIF,EAAczH,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,EACtF,OAAO1I,KAAKouB,mBAAmBhS,UAAU/L,EAAWrQ,KAAKsuB,gBAAiBne,EAAcnQ,KAAKquB,sBAE9F,CACD/pB,IAAK,WACLC,MAAO,SAAkB8L,GACvB,IAAIF,EAAczH,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,EACtF,OAAO1I,KAAKouB,mBAAmB/R,SAAShM,EAAWrQ,KAAKsuB,gBAAiBne,EAAcnQ,KAAKquB,sBAE7F,CACD/pB,IAAK,MACLC,MAAO,SAAa8L,GAClB,IAAIF,EAAczH,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,EACtF,OAAO1I,KAAKouB,mBAAmB/X,IAAIhG,EAAWrQ,KAAKsuB,gBAAiBne,EAAcnQ,KAAKquB,sBAExF,CACD/pB,IAAK,MACLC,MAAO,SAAa8L,EAAUF,EAAa5Q,EAAOE,GAChDO,KAAKouB,mBAAmB9R,IAAIjM,EAAWrQ,KAAKsuB,gBAAiBne,EAAcnQ,KAAKquB,mBAAoB9uB,EAAOE,KAE5G,CACD6E,IAAK,gBACL2a,IAAK,WACH,OAAOjf,KAAKouB,mBAAmB7T,gBAEhC,CACDjW,IAAK,eACL2a,IAAK,WACH,OAAOjf,KAAKouB,mBAAmB5T,iBAI5B2T,EA/FT,GCAA,SAAS,GAAQ7iB,EAAQC,GAAkB,IAAIzC,EAAOC,OAAOD,KAAKwC,GAAS,GAAIvC,OAAOyC,sBAAuB,CAAE,IAAIC,EAAU1C,OAAOyC,sBAAsBF,GAAaC,IAAgBE,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAO5C,OAAO6C,yBAAyBN,EAAQK,GAAKE,eAAgB/C,EAAK/F,KAAK+I,MAAMhD,EAAM2C,GAAY,OAAO3C,EAE9U,SAAS,GAAcjJ,GAAU,IAAK,IAAIoB,EAAI,EAAGA,EAAIyH,UAAUxH,OAAQD,IAAK,CAAE,IAAI+K,EAAyB,MAAhBtD,UAAUzH,GAAayH,UAAUzH,GAAK,GAAQA,EAAI,EAAK,GAAQ+K,GAAQ,GAAM3L,SAAQ,SAAUiE,GAAO,IAAgBzE,EAAQyE,EAAK0H,EAAO1H,OAAsByE,OAAOkD,0BAA6BlD,OAAOmD,iBAAiBrM,EAAQkJ,OAAOkD,0BAA0BD,IAAmB,GAAQA,GAAQ3L,SAAQ,SAAUiE,GAAOyE,OAAOoD,eAAetM,EAAQyE,EAAKyE,OAAO6C,yBAAyBI,EAAQ1H,OAAe,OAAOzE,EAO7f,IASI,GAEJ,SAAUwM,GAGR,SAASwiB,EAAUtiB,EAAOuW,GACxB,IAAItW,EAEJ,IAAgBxM,KAAM6uB,GAEtBriB,EAAQ,IAA2BxM,KAAM,IAAgB6uB,GAAWvuB,KAAKN,KAAMuM,EAAOuW,IAEtF,IAAgB,IAAuBtW,GAAQ,QAAS,CACtDtN,WAAY,EACZE,UAAW,EACXmQ,cAAe,EACfuf,yBAAyB,EACzBC,uBAAuB,IAGzB,IAAgB,IAAuBviB,GAAQ,iCAAkC,MAEjF,IAAgB,IAAuBA,GAAQ,8BAA+B,MAE9E,IAAgB,IAAuBA,GAAQ,sBAAsB,SAAUuB,GAC7EvB,EAAMwiB,gBAAkBjhB,KAG1B,IAAgB,IAAuBvB,GAAQ,uBAAuB,SAAUuB,GAC9EvB,EAAMyiB,iBAAmBlhB,KAG3B,IAAgB,IAAuBvB,GAAQ,+BAA+B,SAAUlJ,GACtF,IAAI+M,EAAW/M,EAAK+M,SAChB6e,EAAO,IAAyB5rB,EAAM,CAAC,aAEvCsN,EAAcpE,EAAMD,MACpByI,EAAepE,EAAYoE,aAC3Bma,EAAgBve,EAAYue,cAGhC,OAAI9e,IAFWO,EAAYnC,SAEC0gB,EACnB,gBAAoB,MAAO,CAChC7qB,IAAK4qB,EAAK5qB,IACVhF,MAAO,GAAc,GAAI4vB,EAAK5vB,MAAO,CACnCG,OAtDgB,OA0DbuV,EAAa,GAAc,GAAIka,EAAM,CAC1C1Y,OAAQ,IAAuBhK,GAC/B6D,SAAUA,EAAW8e,QAK3B,IAAgB,IAAuB3iB,GAAQ,gCAAgC,SAAUhI,GACvF,IAAI2L,EAAc3L,EAAM2L,YACpBE,EAAW7L,EAAM6L,SACjB6e,EAAO,IAAyB1qB,EAAO,CAAC,cAAe,aAEvD6M,EAAe7E,EAAMD,MACrByI,EAAe3D,EAAa2D,aAC5Boa,EAAmB/d,EAAa+d,iBAChCD,EAAgB9d,EAAa8d,cACjC,OAAOna,EAAa,GAAc,GAAIka,EAAM,CAC1C/e,YAAaA,EAAcif,EAC3B5Y,OAAQ,IAAuBhK,GAC/B6D,SAAUA,EAAW8e,QAIzB,IAAgB,IAAuB3iB,GAAQ,6BAA6B,SAAUpH,GACpF,IAAI+K,EAAc/K,EAAM+K,YACpB+e,EAAO,IAAyB9pB,EAAO,CAAC,gBAExCoM,EAAehF,EAAMD,MACrByI,EAAexD,EAAawD,aAC5B5G,EAAcoD,EAAapD,YAC3BghB,EAAmB5d,EAAa4d,iBAEpC,OAAIjf,IAAgB/B,EAAcghB,EACzB,gBAAoB,MAAO,CAChC9qB,IAAK4qB,EAAK5qB,IACVhF,MAAO,GAAc,GAAI4vB,EAAK5vB,MAAO,CACnCC,MA9FgB,OAkGbyV,EAAa,GAAc,GAAIka,EAAM,CAC1C/e,YAAaA,EAAcif,EAC3B5Y,OAAQ,IAAuBhK,SAKrC,IAAgB,IAAuBA,GAAQ,yBAAyB,SAAUtE,GAChF,IAAIxD,EAAQwD,EAAMxD,MACdsN,EAAexF,EAAMD,MACrB6B,EAAc4D,EAAa5D,YAC3BghB,EAAmBpd,EAAaod,iBAChC9gB,EAAc0D,EAAa1D,YAC3BsE,EAAcpG,EAAMoC,MACpBW,EAAgBqD,EAAYrD,cAMhC,OAL8BqD,EAAYkc,yBAKXpqB,IAAU0J,EAAcghB,EAC9C7f,EAGqB,mBAAhBjB,EAA6BA,EAAY,CACrD5J,MAAOA,EAAQ0qB,IACZ9gB,KAGP,IAAgB,IAAuB9B,GAAQ,aAAa,SAAU6iB,GACpE,IAAInwB,EAAamwB,EAAWnwB,WACxBE,EAAYiwB,EAAWjwB,UAE3BoN,EAAME,SAAS,CACbxN,WAAYA,EACZE,UAAWA,IAGb,IAAIsV,EAAWlI,EAAMD,MAAMmI,SAEvBA,GACFA,EAAS2a,MAIb,IAAgB,IAAuB7iB,GAAQ,8BAA8B,SAAUnE,GACrF,IAAIgP,EAAahP,EAAMgP,WACnBrS,EAAOqD,EAAMrD,KACbsS,EAAWjP,EAAMiP,SACjBhE,EAAe9G,EAAMoC,MACrBkgB,EAA0Bxb,EAAawb,wBACvCC,EAAwBzb,EAAayb,sBAEzC,GAAI1X,IAAeyX,GAA2BxX,IAAayX,EAAuB,CAChFviB,EAAME,SAAS,CACb6C,cAAevK,EACf8pB,wBAAyBzX,EACzB0X,sBAAuBzX,IAGzB,IAAIF,EAA4B5K,EAAMD,MAAM6K,0BAEH,mBAA9BA,GACTA,EAA0B,CACxBC,WAAYA,EACZrS,KAAMA,EACNsS,SAAUA,QAMlB,IAAgB,IAAuB9K,GAAQ,iBAAiB,SAAU6iB,GACxE,IAAInwB,EAAamwB,EAAWnwB,WAE5BsN,EAAMmI,UAAU,CACdzV,WAAYA,EACZE,UAAWoN,EAAMoC,MAAMxP,eAI3B,IAAgB,IAAuBoN,GAAQ,gBAAgB,SAAU6iB,GACvE,IAAIjwB,EAAYiwB,EAAWjwB,UAE3BoN,EAAMmI,UAAU,CACdvV,UAAWA,EACXF,WAAYsN,EAAMoC,MAAM1P,gBAI5B,IAAgB,IAAuBsN,GAAQ,wBAAwB,SAAUlE,GAC/E,IAAI5D,EAAQ4D,EAAM5D,MACdiO,EAAenG,EAAMD,MACrB4iB,EAAgBxc,EAAawc,cAC7B1gB,EAAWkE,EAAalE,SACxBC,EAAYiE,EAAajE,UACzB+R,EAAejU,EAAMoC,MACrBW,EAAgBkR,EAAalR,cAMjC,OAL4BkR,EAAasO,uBAKZrqB,IAAU+J,EAAW0gB,EACzC5f,EAGmB,mBAAdb,EAA2BA,EAAU,CACjDhK,MAAOA,EAAQyqB,IACZzgB,KAGP,IAAgB,IAAuBlC,GAAQ,mBAAmB,SAAUuB,GAC1EvB,EAAM8iB,aAAevhB,KAGvB,IAAgB,IAAuBvB,GAAQ,oBAAoB,SAAUuB,GAC3EvB,EAAM+iB,cAAgBxhB,KAGxB,IAAImH,EAA2B3I,EAAM2I,yBACjCsa,EAAoBjjB,EAAM6iB,iBAC1BK,EAAiBljB,EAAM4iB,cAsB3B,OApBA3iB,EAAMkjB,6BAA4B,GAE9Bxa,IACF1I,EAAMmjB,wCAA0CF,EAAiB,EAAI,IAAI,GAA2B,CAClG3C,kBAAmB5X,EACnBsZ,kBAAmB,EACnBE,eAAgBe,IACbva,EACL1I,EAAMojB,yCAA2CJ,EAAoB,GAAKC,EAAiB,EAAI,IAAI,GAA2B,CAC5H3C,kBAAmB5X,EACnBsZ,kBAAmBgB,EACnBd,eAAgBe,IACbva,EACL1I,EAAMqjB,sCAAwCL,EAAoB,EAAI,IAAI,GAA2B,CACnG1C,kBAAmB5X,EACnBsZ,kBAAmBgB,EACnBd,eAAgB,IACbxZ,GAGA1I,EAmgBT,OAzuBA,IAAUqiB,EAAWxiB,GAyOrB,IAAawiB,EAAW,CAAC,CACvBvqB,IAAK,mBACLC,MAAO,WACLvE,KAAKgvB,iBAAmBhvB,KAAKgvB,gBAAgBnd,cAC7C7R,KAAKivB,kBAAoBjvB,KAAKivB,iBAAiBpd,cAC/C7R,KAAKsvB,cAAgBtvB,KAAKsvB,aAAazd,cACvC7R,KAAKuvB,eAAiBvvB,KAAKuvB,cAAc1d,gBAI1C,CACDvN,IAAK,gCACLC,MAAO,WACL,IAAIgE,EAAQG,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,GAC5EonB,EAAoBvnB,EAAM4H,YAC1BA,OAAoC,IAAtB2f,EAA+B,EAAIA,EACjDC,EAAiBxnB,EAAM8H,SACvBA,OAA8B,IAAnB0f,EAA4B,EAAIA,EAE/C/vB,KAAKmR,+BAAgF,iBAAxCnR,KAAKmR,+BAA8CpL,KAAKE,IAAIjG,KAAKmR,+BAAgChB,GAAeA,EAC7JnQ,KAAKoR,4BAA0E,iBAArCpR,KAAKoR,4BAA2CrL,KAAKE,IAAIjG,KAAKoR,4BAA6Bf,GAAYA,IAIlJ,CACD/L,IAAK,kBACLC,MAAO,WACLvE,KAAKgvB,iBAAmBhvB,KAAKgvB,gBAAgBlI,kBAC7C9mB,KAAKivB,kBAAoBjvB,KAAKivB,iBAAiBnI,kBAC/C9mB,KAAKsvB,cAAgBtvB,KAAKsvB,aAAaxI,kBACvC9mB,KAAKuvB,eAAiBvvB,KAAKuvB,cAAczI,oBAI1C,CACDxiB,IAAK,oBACLC,MAAO,WACL,IAAIgT,EAAQ7O,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,GAC5EsnB,EAAoBzY,EAAMpH,YAC1BA,OAAoC,IAAtB6f,EAA+B,EAAIA,EACjDC,EAAiB1Y,EAAMlH,SACvBA,OAA8B,IAAnB4f,EAA4B,EAAIA,EAE3Cnd,EAAe9S,KAAKuM,MACpB6iB,EAAmBtc,EAAasc,iBAChCD,EAAgBrc,EAAaqc,cAC7Be,EAAsBnqB,KAAKC,IAAI,EAAGmK,EAAcif,GAChDe,EAAmBpqB,KAAKC,IAAI,EAAGqK,EAAW8e,GAC9CnvB,KAAKgvB,iBAAmBhvB,KAAKgvB,gBAAgBpY,kBAAkB,CAC7DzG,YAAaA,EACbE,SAAU8f,IAEZnwB,KAAKivB,kBAAoBjvB,KAAKivB,iBAAiBrY,kBAAkB,CAC/DzG,YAAa+f,EACb7f,SAAU8f,IAEZnwB,KAAKsvB,cAAgBtvB,KAAKsvB,aAAa1Y,kBAAkB,CACvDzG,YAAaA,EACbE,SAAUA,IAEZrQ,KAAKuvB,eAAiBvvB,KAAKuvB,cAAc3Y,kBAAkB,CACzDzG,YAAa+f,EACb7f,SAAUA,IAEZrQ,KAAKowB,eAAiB,KACtBpwB,KAAKqwB,eAAiB,KAEtBrwB,KAAK0vB,6BAA4B,KAElC,CACDprB,IAAK,oBACLC,MAAO,WACL,IAAI+rB,EAAetwB,KAAKuM,MACpBrN,EAAaoxB,EAAapxB,WAC1BE,EAAYkxB,EAAalxB,UAE7B,GAAIF,EAAa,GAAKE,EAAY,EAAG,CACnC,IAAI6R,EAAW,GAEX/R,EAAa,IACf+R,EAAS/R,WAAaA,GAGpBE,EAAY,IACd6R,EAAS7R,UAAYA,GAGvBY,KAAK0M,SAASuE,GAGhBjR,KAAKkS,+BAEN,CACD5N,IAAK,qBACLC,MAAO,WACLvE,KAAKkS,+BAEN,CACD5N,IAAK,SACLC,MAAO,WACL,IAAIgsB,EAAevwB,KAAKuM,MACpBmI,EAAW6b,EAAa7b,SACxB7H,EAAoB0jB,EAAa1jB,kBAGjCuC,GAF4BmhB,EAAanZ,0BACxBmZ,EAAarxB,WACbqxB,EAAanhB,gBAE9BE,GADgBihB,EAAanxB,UACfmxB,EAAajhB,aAC3B4f,EAAO,IAAyBqB,EAAc,CAAC,WAAY,oBAAqB,4BAA6B,aAAc,iBAAkB,YAAa,gBAO9J,GALAvwB,KAAKwwB,oBAKoB,IAArBxwB,KAAKuM,MAAMhN,OAAqC,IAAtBS,KAAKuM,MAAM9M,OACvC,OAAO,KAIT,IAAIgxB,EAAezwB,KAAK4O,MACpB1P,EAAauxB,EAAavxB,WAC1BE,EAAYqxB,EAAarxB,UAC7B,OAAO,gBAAoB,MAAO,CAChCE,MAAOU,KAAK0wB,sBACX,gBAAoB,MAAO,CAC5BpxB,MAAOU,KAAK2wB,oBACX3wB,KAAK4wB,mBAAmB1B,GAAOlvB,KAAK6wB,oBAAoB,GAAc,GAAI3B,EAAM,CACjFxa,SAAUA,EACVxV,WAAYA,MACR,gBAAoB,MAAO,CAC/BI,MAAOU,KAAK8wB,uBACX9wB,KAAK+wB,sBAAsB,GAAc,GAAI7B,EAAM,CACpDxa,SAAUA,EACVtV,UAAWA,KACRY,KAAKgxB,uBAAuB,GAAc,GAAI9B,EAAM,CACvDxa,SAAUA,EACV7H,kBAAmBA,EACnB3N,WAAYA,EACZkQ,eAAgBA,EAChBE,YAAaA,EACblQ,UAAWA,SAGd,CACDkF,IAAK,uBACLC,MAAO,SAA8BgI,GAKnC,OAJaA,EAAM9M,OAECO,KAAKixB,kBAAkB1kB,KAI5C,CACDjI,IAAK,oBACLC,MAAO,SAA2BgI,GAChC,IAAI6iB,EAAmB7iB,EAAM6iB,iBACzB9gB,EAAc/B,EAAM+B,YAExB,GAA2B,MAAvBtO,KAAKowB,eACP,GAA2B,mBAAhB9hB,EAA4B,CAGrC,IAFA,IAAI4iB,EAAgB,EAEXxsB,EAAQ,EAAGA,EAAQ0qB,EAAkB1qB,IAC5CwsB,GAAiB5iB,EAAY,CAC3B5J,MAAOA,IAIX1E,KAAKowB,eAAiBc,OAEtBlxB,KAAKowB,eAAiB9hB,EAAc8gB,EAIxC,OAAOpvB,KAAKowB,iBAEb,CACD9rB,IAAK,qBACLC,MAAO,SAA4BgI,GAKjC,OAJYA,EAAMhN,MAEES,KAAKmxB,kBAAkB5kB,KAI5C,CACDjI,IAAK,oBACLC,MAAO,SAA2BgI,GAChC,IAAI4iB,EAAgB5iB,EAAM4iB,cACtBzgB,EAAYnC,EAAMmC,UAEtB,GAA2B,MAAvB1O,KAAKqwB,eACP,GAAyB,mBAAd3hB,EAA0B,CAGnC,IAFA,IAAI0iB,EAAgB,EAEX1sB,EAAQ,EAAGA,EAAQyqB,EAAezqB,IACzC0sB,GAAiB1iB,EAAU,CACzBhK,MAAOA,IAIX1E,KAAKqwB,eAAiBe,OAEtBpxB,KAAKqwB,eAAiB3hB,EAAYygB,EAItC,OAAOnvB,KAAKqwB,iBAEb,CACD/rB,IAAK,6BACLC,MAAO,WACL,GAAmD,iBAAxCvE,KAAKmR,+BAA6C,CAC3D,IAAIhB,EAAcnQ,KAAKmR,+BACnBd,EAAWrQ,KAAKoR,4BACpBpR,KAAKmR,+BAAiC,KACtCnR,KAAKoR,4BAA8B,KACnCpR,KAAK4W,kBAAkB,CACrBzG,YAAaA,EACbE,SAAUA,IAEZrQ,KAAK6R,iBAQR,CACDvN,IAAK,8BACLC,MAAO,SAAqC8sB,GAC1C,IAAIC,EAAetxB,KAAKuM,MACpB+B,EAAcgjB,EAAahjB,YAC3BijB,EAA0BD,EAAaC,wBACvCC,EAAuBF,EAAaE,qBACpC/xB,EAAS6xB,EAAa7xB,OACtB2vB,EAAmBkC,EAAalC,iBAChCD,EAAgBmC,EAAanC,cAC7BzgB,EAAY4iB,EAAa5iB,UACzBpP,EAAQgyB,EAAahyB,MACrBmyB,EAAsBH,EAAaG,oBACnCC,EAAuBJ,EAAaI,qBACpCC,EAAmBL,EAAaK,iBAChCC,EAAoBN,EAAaM,kBACjCryB,EAAQ+xB,EAAa/xB,MACrBsyB,EAAaR,GAAY5xB,IAAWO,KAAK8xB,qBAAuBvyB,IAAUS,KAAK+xB,mBAC/EC,EAAiBX,GAAY/iB,IAAgBtO,KAAKiyB,0BAA4B7C,IAAqBpvB,KAAKkyB,8BACxGC,EAAgBd,GAAYlC,IAAkBnvB,KAAKoyB,4BAA8B1jB,IAAc1O,KAAKqyB,wBAEpGhB,GAAYQ,GAAcvyB,IAAUU,KAAKsyB,sBAC3CtyB,KAAK0wB,qBAAuB,GAAc,CACxCjxB,OAAQA,EACRqV,SAAU,UAEVvV,MAAOA,GACND,KAGD+xB,GAAYQ,GAAcM,KAC5BnyB,KAAK2wB,mBAAqB,CACxBlxB,OAAQO,KAAKixB,kBAAkBjxB,KAAKuM,OACpC5K,SAAU,WACVpC,MAAOA,GAETS,KAAK8wB,sBAAwB,CAC3BrxB,OAAQA,EAASO,KAAKixB,kBAAkBjxB,KAAKuM,OAC7CuI,SAAU,UAEVnT,SAAU,WACVpC,MAAOA,KAIP8xB,GAAYI,IAAwBzxB,KAAKuyB,oCAC3CvyB,KAAKwyB,qBAAuB,GAAc,CACxC5Z,KAAM,EACNxE,UAAW,SACXC,UAAWkd,EAA0B,OAAS,SAC9C5vB,SAAU,YACT8vB,KAGDJ,GAAYW,GAAkBN,IAAyB1xB,KAAKyyB,qCAC9DzyB,KAAK0yB,sBAAwB,GAAc,CACzC9Z,KAAM5Y,KAAKmxB,kBAAkBnxB,KAAKuM,OAClC5K,SAAU,YACT+vB,KAGDL,GAAYM,IAAqB3xB,KAAK2yB,iCACxC3yB,KAAK4yB,kBAAoB,GAAc,CACrCha,KAAM,EACNxE,UAAW,SACXC,UAAW,SACX1S,SAAU,WACVkX,IAAK,GACJ8Y,KAGDN,GAAYW,GAAkBJ,IAAsB5xB,KAAK6yB,kCAC3D7yB,KAAK8yB,mBAAqB,GAAc,CACtCla,KAAM5Y,KAAKmxB,kBAAkBnxB,KAAKuM,OAClC6H,UAAWod,EAAuB,OAAS,SAC3Cnd,UAAW,SACX1S,SAAU,WACVkX,IAAK,GACJ+Y,IAGL5xB,KAAKiyB,yBAA2B3jB,EAChCtO,KAAKkyB,8BAAgC9C,EACrCpvB,KAAKoyB,2BAA6BjD,EAClCnvB,KAAK8xB,oBAAsBryB,EAC3BO,KAAKqyB,uBAAyB3jB,EAC9B1O,KAAKsyB,mBAAqBhzB,EAC1BU,KAAKuyB,iCAAmCd,EACxCzxB,KAAKyyB,kCAAoCf,EACzC1xB,KAAK2yB,8BAAgChB,EACrC3xB,KAAK6yB,+BAAiCjB,EACtC5xB,KAAK+xB,mBAAqBxyB,IAE3B,CACD+E,IAAK,oBACLC,MAAO,WACDvE,KAAKiyB,2BAA6BjyB,KAAKuM,MAAM+B,aAAetO,KAAKkyB,gCAAkClyB,KAAKuM,MAAM6iB,mBAChHpvB,KAAKowB,eAAiB,MAGpBpwB,KAAKoyB,6BAA+BpyB,KAAKuM,MAAM4iB,eAAiBnvB,KAAKqyB,yBAA2BryB,KAAKuM,MAAMmC,YAC7G1O,KAAKqwB,eAAiB,MAGxBrwB,KAAK0vB,8BAEL1vB,KAAKiyB,yBAA2BjyB,KAAKuM,MAAM+B,YAC3CtO,KAAKkyB,8BAAgClyB,KAAKuM,MAAM6iB,iBAChDpvB,KAAKoyB,2BAA6BpyB,KAAKuM,MAAM4iB,cAC7CnvB,KAAKqyB,uBAAyBryB,KAAKuM,MAAMmC,YAE1C,CACDpK,IAAK,wBACLC,MAAO,SAA+BgI,GACpC,IAAIglB,EAA0BhlB,EAAMglB,wBAChCnC,EAAmB7iB,EAAM6iB,iBACzBD,EAAgB5iB,EAAM4iB,cACtB1gB,EAAWlC,EAAMkC,SACjBskB,EAA8BxmB,EAAMwmB,4BACpChE,EAAwB/uB,KAAK4O,MAAMmgB,sBAEvC,IAAKK,EACH,OAAO,KAGT,IAAI4D,EAAqBjE,EAAwB,EAAI,EACjDtvB,EAASO,KAAKizB,qBAAqB1mB,GACnChN,EAAQS,KAAKmxB,kBAAkB5kB,GAC/BgD,EAAgBvP,KAAK4O,MAAMmgB,sBAAwB/uB,KAAK4O,MAAMW,cAAgB,EAC9E2jB,EAAYH,EAA8BxzB,EAAQgQ,EAAgBhQ,EAElE4zB,EAAiB,gBAAoB,EAAM,IAAS,GAAI5mB,EAAO,CACjEyI,aAAchV,KAAKozB,4BACnBtzB,UAAWE,KAAKuM,MAAM8mB,wBACtBjlB,YAAaghB,EACbla,yBAA0BlV,KAAK2vB,wCAC/BlwB,OAAQA,EACRiV,SAAU6c,EAA0BvxB,KAAKszB,kBAAevyB,EACxDgN,IAAK/N,KAAKuzB,mBACV9kB,SAAU1I,KAAKC,IAAI,EAAGyI,EAAW0gB,GAAiB6D,EAClDtkB,UAAW1O,KAAKwzB,qBAChBl0B,MAAOU,KAAKwyB,qBACZnf,SAAU,KACV9T,MAAO2zB,KAGT,OAAIH,EACK,gBAAoB,MAAO,CAChCjzB,UAAW,+BACXR,MAAO,GAAc,GAAIU,KAAKwyB,qBAAsB,CAClD/yB,OAAQA,EACRF,MAAOA,EACP8U,UAAW,YAEZ8e,GAGEA,IAER,CACD7uB,IAAK,yBACLC,MAAO,SAAgCgI,GACrC,IAAI6B,EAAc7B,EAAM6B,YACpBghB,EAAmB7iB,EAAM6iB,iBACzBD,EAAgB5iB,EAAM4iB,cACtB1gB,EAAWlC,EAAMkC,SACjBW,EAAiB7C,EAAM6C,eACvBE,EAAc/C,EAAM+C,YACxB,OAAO,gBAAoB,EAAM,IAAS,GAAI/C,EAAO,CACnDyI,aAAchV,KAAKyzB,6BACnB3zB,UAAWE,KAAKuM,MAAMmnB,yBACtBtlB,YAAarI,KAAKC,IAAI,EAAGoI,EAAcghB,GACvC9gB,YAAatO,KAAK2zB,sBAClBze,yBAA0BlV,KAAK4vB,yCAC/BnwB,OAAQO,KAAKizB,qBAAqB1mB,GAClCmI,SAAU1U,KAAK2U,UACfyC,0BAA2BpX,KAAK4zB,2BAChC7lB,IAAK/N,KAAK6zB,oBACVplB,SAAU1I,KAAKC,IAAI,EAAGyI,EAAW0gB,GACjCzgB,UAAW1O,KAAKwzB,qBAChBpkB,eAAgBA,EAAiBggB,EACjC9f,YAAaA,EAAc6f,EAC3B7vB,MAAOU,KAAK0yB,sBACZnzB,MAAOS,KAAK8zB,mBAAmBvnB,QAGlC,CACDjI,IAAK,qBACLC,MAAO,SAA4BgI,GACjC,IAAI6iB,EAAmB7iB,EAAM6iB,iBACzBD,EAAgB5iB,EAAM4iB,cAE1B,OAAKC,GAAqBD,EAInB,gBAAoB,EAAM,IAAS,GAAI5iB,EAAO,CACnDzM,UAAWE,KAAKuM,MAAMwnB,qBACtB3lB,YAAaghB,EACb3vB,OAAQO,KAAKixB,kBAAkB1kB,GAC/BwB,IAAK/N,KAAKg0B,gBACVvlB,SAAU0gB,EACV7vB,MAAOU,KAAK4yB,kBACZvf,SAAU,KACV9T,MAAOS,KAAKmxB,kBAAkB5kB,MAXvB,OAcV,CACDjI,IAAK,sBACLC,MAAO,SAA6BgI,GAClC,IAAI6B,EAAc7B,EAAM6B,YACpBojB,EAAuBjlB,EAAMilB,qBAC7BpC,EAAmB7iB,EAAM6iB,iBACzBD,EAAgB5iB,EAAM4iB,cACtBjwB,EAAaqN,EAAMrN,WACnB+0B,EAA4B1nB,EAAM0nB,0BAClCC,EAAel0B,KAAK4O,MACpBkgB,EAA0BoF,EAAapF,wBACvCvf,EAAgB2kB,EAAa3kB,cAEjC,IAAK4f,EACH,OAAO,KAGT,IAAIgF,EAAwBrF,EAA0B,EAAI,EACtDrvB,EAASO,KAAKixB,kBAAkB1kB,GAChChN,EAAQS,KAAK8zB,mBAAmBvnB,GAChC6nB,EAAmBtF,EAA0Bvf,EAAgB,EAE7D8kB,EAAa50B,EACbH,EAAQU,KAAK8yB,mBAEbmB,IACFI,EAAa50B,EAAS20B,EACtB90B,EAAQ,GAAc,GAAIU,KAAK8yB,mBAAoB,CACjDla,KAAM,KAIV,IAAI0b,EAAe,gBAAoB,EAAM,IAAS,GAAI/nB,EAAO,CAC/DyI,aAAchV,KAAKu0B,0BACnBz0B,UAAWE,KAAKuM,MAAMioB,sBACtBpmB,YAAarI,KAAKC,IAAI,EAAGoI,EAAcghB,GAAoB+E,EAC3D7lB,YAAatO,KAAK2zB,sBAClBze,yBAA0BlV,KAAK6vB,sCAC/BpwB,OAAQ40B,EACR3f,SAAU8c,EAAuBxxB,KAAKy0B,mBAAgB1zB,EACtDgN,IAAK/N,KAAK00B,iBACVjmB,SAAU0gB,EACVjwB,WAAYA,EACZI,MAAOA,EACP+T,SAAU,KACV9T,MAAOA,KAGT,OAAI00B,EACK,gBAAoB,MAAO,CAChCn0B,UAAW,6BACXR,MAAO,GAAc,GAAIU,KAAK8yB,mBAAoB,CAChDrzB,OAAQA,EACRF,MAAOA,EACP6U,UAAW,YAEZkgB,GAGEA,KAEP,CAAC,CACHhwB,IAAK,2BACLC,MAAO,SAAkCmT,EAAWvF,GAClD,OAAIuF,EAAUxY,aAAeiT,EAAUjT,YAAcwY,EAAUtY,YAAc+S,EAAU/S,UAC9E,CACLF,WAAoC,MAAxBwY,EAAUxY,YAAsBwY,EAAUxY,YAAc,EAAIwY,EAAUxY,WAAaiT,EAAUjT,WACzGE,UAAkC,MAAvBsY,EAAUtY,WAAqBsY,EAAUtY,WAAa,EAAIsY,EAAUtY,UAAY+S,EAAU/S,WAIlG,SAIJyvB,EA1uBT,CA2uBE,iBAEF,IAAgB,GAAW,eAAgB,CACzCwE,wBAAyB,GACzBK,yBAA0B,GAC1BK,qBAAsB,GACtBS,sBAAuB,GACvBjD,yBAAyB,EACzBC,sBAAsB,EACtBpC,iBAAkB,EAClBD,cAAe,EACf/f,gBAAiB,EACjBE,aAAc,EACdhQ,MAAO,GACPmyB,oBAAqB,GACrBC,qBAAsB,GACtBC,iBAAkB,GAClBC,kBAAmB,GACnBqC,2BAA2B,EAC3BlB,6BAA6B,IAG/B,GAAU3R,UAiBN,GACJ,mBAAS,IACM,ICtyBX,GAEJ,SAAU/U,GAGR,SAASsoB,EAAWpoB,EAAOuW,GACzB,IAAItW,EAcJ,OAZA,IAAgBxM,KAAM20B,IAEtBnoB,EAAQ,IAA2BxM,KAAM,IAAgB20B,GAAYr0B,KAAKN,KAAMuM,EAAOuW,KACjFlU,MAAQ,CACZoI,aAAc,EACdC,YAAa,EACb5X,aAAc,EACdH,WAAY,EACZE,UAAW,EACXD,YAAa,GAEfqN,EAAMmI,UAAYnI,EAAMmI,UAAUsO,KAAK,IAAuBzW,IACvDA,EA4CT,OA7DA,IAAUmoB,EAAYtoB,GAoBtB,IAAasoB,EAAY,CAAC,CACxBrwB,IAAK,SACLC,MAAO,WACL,IAAIuV,EAAW9Z,KAAKuM,MAAMuN,SACtBlH,EAAc5S,KAAK4O,MACnBoI,EAAepE,EAAYoE,aAC3BC,EAAcrE,EAAYqE,YAC1B5X,EAAeuT,EAAYvT,aAC3BH,EAAa0T,EAAY1T,WACzBE,EAAYwT,EAAYxT,UACxBD,EAAcyT,EAAYzT,YAC9B,OAAO2a,EAAS,CACd9C,aAAcA,EACdC,YAAaA,EACbvC,SAAU1U,KAAK2U,UACftV,aAAcA,EACdH,WAAYA,EACZE,UAAWA,EACXD,YAAaA,MAGhB,CACDmF,IAAK,YACLC,MAAO,SAAmBjB,GACxB,IAAI0T,EAAe1T,EAAK0T,aACpBC,EAAc3T,EAAK2T,YACnB5X,EAAeiE,EAAKjE,aACpBH,EAAaoE,EAAKpE,WAClBE,EAAYkE,EAAKlE,UACjBD,EAAcmE,EAAKnE,YACvBa,KAAK0M,SAAS,CACZsK,aAAcA,EACdC,YAAaA,EACb5X,aAAcA,EACdH,WAAYA,EACZE,UAAWA,EACXD,YAAaA,QAKZw1B,EA9DT,CA+DE,iBAGF,GAAWvT,UAOP,GCtFW,SAASwT,GAAyBtxB,GAC/C,IAAIxD,EAAYwD,EAAKxD,UACjB+0B,EAAUvxB,EAAKuxB,QACfv1B,EAAQgE,EAAKhE,MACjB,OAAO,gBAAoB,MAAO,CAChCQ,UAAWA,EACXsT,KAAM,MACN9T,MAAOA,GACNu1B,GAELD,GAAyBxT,UAAoD,KCX7E,IAae,GAbK,CAKlB0T,IAAK,MAMLC,KAAM,QCHO,SAASC,GAAc1xB,GACpC,IAAI2xB,EAAgB3xB,EAAK2xB,cACrB9N,EAAa,kBAAK,8CAA+C,CACnE,mDAAoD8N,IAAkB,GAAcH,IACpF,oDAAqDG,IAAkB,GAAcF,OAEvF,OAAO,gBAAoB,MAAO,CAChCj1B,UAAWqnB,EACX5nB,MAAO,GACPE,OAAQ,GACRy1B,QAAS,aACRD,IAAkB,GAAcH,IAAM,gBAAoB,OAAQ,CACnE1K,EAAG,mBACA,gBAAoB,OAAQ,CAC/BA,EAAG,mBACD,gBAAoB,OAAQ,CAC9BA,EAAG,gBACH+K,KAAM,UCnBK,SAASC,GAAsB9xB,GAC5C,IAAI+xB,EAAU/xB,EAAK+xB,QACfC,EAAQhyB,EAAKgyB,MACbC,EAASjyB,EAAKiyB,OACdN,EAAgB3xB,EAAK2xB,cACrBO,EAAoBD,IAAWF,EAC/Bvb,EAAW,CAAC,gBAAoB,OAAQ,CAC1Cha,UAAW,+CACXwE,IAAK,QACLmxB,MAAwB,iBAAVH,EAAqBA,EAAQ,MAC1CA,IASH,OAPIE,GACF1b,EAAS/W,KAAK,gBAAoBiyB,GAAe,CAC/C1wB,IAAK,gBACL2wB,cAAeA,KAIZnb,ECnBM,SAAS4b,GAAmBpyB,GACzC,IAAIxD,EAAYwD,EAAKxD,UACjB+0B,EAAUvxB,EAAKuxB,QACfnwB,EAAQpB,EAAKoB,MACbJ,EAAMhB,EAAKgB,IACXqxB,EAAaryB,EAAKqyB,WAClBC,EAAmBtyB,EAAKsyB,iBACxBC,EAAgBvyB,EAAKuyB,cACrBC,EAAiBxyB,EAAKwyB,eACtBC,EAAkBzyB,EAAKyyB,gBACvBC,EAAU1yB,EAAK0yB,QACf12B,EAAQgE,EAAKhE,MACb22B,EAAY,CACd,gBAAiBvxB,EAAQ,GA0D3B,OAvDIixB,GAAcC,GAAoBC,GAAiBC,GAAkBC,KACvEE,EAAU,cAAgB,MAC1BA,EAAU5iB,SAAW,EAEjBsiB,IACFM,EAAUC,QAAU,SAAUjoB,GAC5B,OAAO0nB,EAAW,CAChB1nB,MAAOA,EACPvJ,MAAOA,EACPsxB,QAASA,MAKXJ,IACFK,EAAUE,cAAgB,SAAUloB,GAClC,OAAO2nB,EAAiB,CACtB3nB,MAAOA,EACPvJ,MAAOA,EACPsxB,QAASA,MAKXH,IACFI,EAAUG,WAAa,SAAUnoB,GAC/B,OAAO4nB,EAAc,CACnB5nB,MAAOA,EACPvJ,MAAOA,EACPsxB,QAASA,MAKXF,IACFG,EAAUI,YAAc,SAAUpoB,GAChC,OAAO6nB,EAAe,CACpB7nB,MAAOA,EACPvJ,MAAOA,EACPsxB,QAASA,MAKXD,IACFE,EAAUK,cAAgB,SAAUroB,GAClC,OAAO8nB,EAAgB,CACrB9nB,MAAOA,EACPvJ,MAAOA,EACPsxB,QAASA,OAMV,gBAAoB,MAAO,IAAS,GAAIC,EAAW,CACxDn2B,UAAWA,EACXwE,IAAKA,EACL8O,KAAM,MACN9T,MAAOA,IACLu1B,GFtDNG,GAAc5T,UAEV,GCHJgU,GAAsBhU,UAAoD,KCyD1EsU,GAAmBtU,UAAoD,KCrEvE,IAAI,GAEJ,SAAU/G,GAGR,SAASkc,IAGP,OAFA,IAAgBv2B,KAAMu2B,GAEf,IAA2Bv2B,KAAM,IAAgBu2B,GAAQzqB,MAAM9L,KAAM0I,YAG9E,OARA,IAAU6tB,EAAQlc,GAQXkc,EATT,CAUE,aClBF,SAAS,GAAQjrB,EAAQC,GAAkB,IAAIzC,EAAOC,OAAOD,KAAKwC,GAAS,GAAIvC,OAAOyC,sBAAuB,CAAE,IAAIC,EAAU1C,OAAOyC,sBAAsBF,GAAaC,IAAgBE,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAO5C,OAAO6C,yBAAyBN,EAAQK,GAAKE,eAAgB/C,EAAK/F,KAAK+I,MAAMhD,EAAM2C,GAAY,OAAO3C,EAE9U,SAAS,GAAcjJ,GAAU,IAAK,IAAIoB,EAAI,EAAGA,EAAIyH,UAAUxH,OAAQD,IAAK,CAAE,IAAI+K,EAAyB,MAAhBtD,UAAUzH,GAAayH,UAAUzH,GAAK,GAAQA,EAAI,EAAK,GAAQ+K,GAAQ,GAAM3L,SAAQ,SAAUiE,GAAO,IAAgBzE,EAAQyE,EAAK0H,EAAO1H,OAAsByE,OAAOkD,0BAA6BlD,OAAOmD,iBAAiBrM,EAAQkJ,OAAOkD,0BAA0BD,IAAmB,GAAQA,GAAQ3L,SAAQ,SAAUiE,GAAOyE,OAAOoD,eAAetM,EAAQyE,EAAKyE,OAAO6C,yBAAyBI,EAAQ1H,OAAe,OAAOzE,EDkB7f,IAAgB,GAAQ,eAAgB,CACtC22B,eEzBa,SAA+BlzB,GAC5C,IAAI+xB,EAAU/xB,EAAK+xB,QACfW,EAAU1yB,EAAK0yB,QAEnB,MAA2B,mBAAhBA,EAAQ/W,IACV+W,EAAQ/W,IAAIoW,GAEZW,EAAQX,IFmBjBrgB,aG3Ba,SAA6B1R,GAC1C,IAAImzB,EAAWnzB,EAAKmzB,SAEpB,OAAgB,MAAZA,EACK,GAEAC,OAAOD,IHsBhBE,qBAAsB,GAAc7B,IACpC8B,SAAU,EACVC,WAAY,EACZC,eAAgB1B,GAChB91B,MAAO,KAIT,GAAO8hB,UAkEH,GC/EJ,IAAI,GAEJ,SAAU/U,GAGR,SAAS0qB,EAAMxqB,GACb,IAAIC,EAaJ,OAXA,IAAgBxM,KAAM+2B,IAEtBvqB,EAAQ,IAA2BxM,KAAM,IAAgB+2B,GAAOz2B,KAAKN,KAAMuM,KACrEqC,MAAQ,CACZooB,eAAgB,GAElBxqB,EAAMyqB,cAAgBzqB,EAAMyqB,cAAchU,KAAK,IAAuBzW,IACtEA,EAAM0qB,WAAa1qB,EAAM0qB,WAAWjU,KAAK,IAAuBzW,IAChEA,EAAMmI,UAAYnI,EAAMmI,UAAUsO,KAAK,IAAuBzW,IAC9DA,EAAM0N,mBAAqB1N,EAAM0N,mBAAmB+I,KAAK,IAAuBzW,IAChFA,EAAMqP,QAAUrP,EAAMqP,QAAQoH,KAAK,IAAuBzW,IACnDA,EAygBT,OAzhBA,IAAUuqB,EAAO1qB,GAmBjB,IAAa0qB,EAAO,CAAC,CACnBzyB,IAAK,kBACLC,MAAO,WACDvE,KAAKsM,MACPtM,KAAKsM,KAAKuF,gBAKb,CACDvN,IAAK,kBACLC,MAAO,SAAyBjB,GAC9B,IAAI2M,EAAY3M,EAAK2M,UACjBvL,EAAQpB,EAAKoB,MAEjB,OAAI1E,KAAKsM,KACqBtM,KAAKsM,KAAKua,iBAAiB,CACrD5W,UAAWA,EACXI,SAAU3L,IAE0BtF,UAKjC,IAIR,CACDkF,IAAK,gCACLC,MAAO,SAAuCC,GAC5C,IAAI2L,EAAc3L,EAAM2L,YACpBE,EAAW7L,EAAM6L,SAEjBrQ,KAAKsM,MACPtM,KAAKsM,KAAKiR,8BAA8B,CACtClN,SAAUA,EACVF,YAAaA,MAMlB,CACD7L,IAAK,iBACLC,MAAO,WACDvE,KAAKsM,MACPtM,KAAKsM,KAAKwa,oBAKb,CACDxiB,IAAK,oBACLC,MAAO,WACL,IAAIa,EAAQsD,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,GAC5EyuB,EAAoB/xB,EAAM+K,YAC1BA,OAAoC,IAAtBgnB,EAA+B,EAAIA,EACjDC,EAAiBhyB,EAAMiL,SACvBA,OAA8B,IAAnB+mB,EAA4B,EAAIA,EAE3Cp3B,KAAKsM,MACPtM,KAAKsM,KAAKsK,kBAAkB,CAC1BvG,SAAUA,EACVF,YAAaA,MAMlB,CACD7L,IAAK,sBACLC,MAAO,WACL,IAAIG,EAAQgE,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,EAE5E1I,KAAKsM,MACPtM,KAAKsM,KAAKsK,kBAAkB,CAC1BvG,SAAU3L,MAMf,CACDJ,IAAK,mBACLC,MAAO,WACL,IAAInF,EAAYsJ,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,EAEhF1I,KAAKsM,MACPtM,KAAKsM,KAAK2a,iBAAiB,CACzB7nB,UAAWA,MAMhB,CACDkF,IAAK,cACLC,MAAO,WACL,IAAIG,EAAQgE,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,EAE5E1I,KAAKsM,MACPtM,KAAKsM,KAAKgT,aAAa,CACrBnP,YAAa,EACbE,SAAU3L,MAIf,CACDJ,IAAK,oBACLC,MAAO,WACL,GAAIvE,KAAKsM,KAAM,CACb,IAAI+qB,EAAQ,uBAAYr3B,KAAKsM,MAEzB2K,EAAcogB,EAAMpgB,aAAe,EAEvC,OADkBogB,EAAM73B,aAAe,GAClByX,EAGvB,OAAO,IAER,CACD3S,IAAK,oBACLC,MAAO,WACLvE,KAAKs3B,uBAEN,CACDhzB,IAAK,qBACLC,MAAO,WACLvE,KAAKs3B,uBAEN,CACDhzB,IAAK,SACLC,MAAO,WACL,IAAImO,EAAS1S,KAET4Q,EAAc5Q,KAAKuM,MACnBuN,EAAWlJ,EAAYkJ,SACvBha,EAAY8Q,EAAY9Q,UACxBy3B,EAAgB3mB,EAAY2mB,cAC5BC,EAAgB5mB,EAAY4mB,cAC5BhkB,EAAY5C,EAAY4C,UACxBikB,EAAe7mB,EAAY6mB,aAC3BC,EAAoB9mB,EAAY8mB,kBAChCj4B,EAASmR,EAAYnR,OACrBjB,EAAKoS,EAAYpS,GACjB0oB,EAAiBtW,EAAYsW,eAC7ByQ,EAAe/mB,EAAY+mB,aAC3BC,EAAWhnB,EAAYgnB,SACvB9zB,EAAgB8M,EAAY9M,cAC5BxE,EAAQsR,EAAYtR,MACpBC,EAAQqR,EAAYrR,MACpBy3B,EAAiBh3B,KAAK4O,MAAMooB,eAC5Ba,EAAsBN,EAAgB93B,EAASA,EAASg4B,EACxDK,EAAmC,mBAAjBH,EAA8BA,EAAa,CAC/DjzB,OAAQ,IACLizB,EACDI,EAAqC,mBAAbH,EAA0BA,EAAS,CAC7DlzB,OAAQ,IACLkzB,EAaL,OAXA53B,KAAKg4B,oBAAsB,GAC3B,WAAeC,QAAQne,GAAUzZ,SAAQ,SAAU63B,EAAQxzB,GACzD,IAAIyzB,EAAazlB,EAAO0lB,uBAAuBF,EAAQA,EAAO3rB,MAAMjN,OAEpEoT,EAAOslB,oBAAoBtzB,GAAS,GAAc,CAChDoQ,SAAU,UACTqjB,MAKE,gBAAoB,MAAO,CAChC,aAAcn4B,KAAKuM,MAAM,cACzB,kBAAmBvM,KAAKuM,MAAM,mBAC9B,gBAAiB,WAAe0rB,QAAQne,GAAU5Y,OAClD,gBAAiBlB,KAAKuM,MAAMkC,SAC5B3O,UAAW,kBAAK,0BAA2BA,GAC3CtB,GAAIA,EACJ4U,KAAM,OACN9T,MAAOA,IACLi4B,GAAiBG,EAAkB,CACrC53B,UAAW,kBAAK,qCAAsCg4B,GACtDjD,QAAS70B,KAAKq4B,oBACd/4B,MAAO,GAAc,CACnBG,OAAQg4B,EACR3iB,SAAU,SACViG,aAAcic,EACdz3B,MAAOA,GACNw4B,KACD,gBAAoB,EAAM,IAAS,GAAI/3B,KAAKuM,MAAO,CACrD,gBAAiB,KACjBwG,oBAAoB,EACpBjT,UAAW,kBAAK,gCAAiC03B,GACjDxiB,aAAchV,KAAKk3B,WACnB5oB,YAAa/O,EACb6O,YAAa,EACb3O,OAAQo4B,EACRr5B,QAAIuC,EACJoS,kBAAmB+T,EACnBxS,SAAU1U,KAAK2U,UACf9H,kBAAmB7M,KAAKka,mBACxBnM,IAAK/N,KAAK6b,QACVzI,KAAM,WACN4jB,eAAgBA,EAChB1nB,YAAaxL,EACbxE,MAAO,GAAc,GAAIkU,EAAW,CAClCY,UAAW,iBAIhB,CACD9P,IAAK,gBACLC,MAAO,SAAuB2D,GAC5B,IAAIgwB,EAAShwB,EAAMgwB,OACf/nB,EAAcjI,EAAMiI,YACpBxD,EAAczE,EAAMyE,YACpB6J,EAAStO,EAAMsO,OACfwf,EAAU9tB,EAAM8tB,QAChB3lB,EAAWnI,EAAMmI,SACjBioB,EAAgBt4B,KAAKuM,MAAM+rB,cAC3BC,EAAgBL,EAAO3rB,MACvBiqB,EAAiB+B,EAAc/B,eAC/BxhB,EAAeujB,EAAcvjB,aAC7BlV,EAAYy4B,EAAcz4B,UAC1B04B,EAAaD,EAAcC,WAC3BnD,EAAUkD,EAAclD,QACxB72B,EAAK+5B,EAAc/5B,GAMnBua,EAAe/D,EAAa,CAC9ByhB,SANaD,EAAe,CAC5BgC,WAAYA,EACZnD,QAASA,EACTW,QAASA,IAITwC,WAAYA,EACZroB,YAAaA,EACbklB,QAASA,EACT1oB,YAAaA,EACb6J,OAAQA,EACRwf,QAASA,EACT3lB,SAAUA,IAWR/Q,EAAQU,KAAKg4B,oBAAoB7nB,GACjCslB,EAAgC,iBAAjB1c,EAA4BA,EAAe,KAI9D,OAAO,gBAAoB,MAAO,CAChC,gBAAiB5I,EAAc,EAC/B,mBAAoB3R,EACpBsB,UAAW,kBAAK,qCAAsCA,GACtDwE,IAAK,MAAQ+L,EAAR,OAAiCF,EACtC+lB,QAlBY,SAAiBjoB,GAC7BqqB,GAAiBA,EAAc,CAC7BE,WAAYA,EACZnD,QAASA,EACTpnB,MAAOA,KAeTmF,KAAM,WACN9T,MAAOA,EACPm2B,MAAOA,GACN1c,KAEJ,CACDzU,IAAK,gBACLC,MAAO,SAAuB8D,GAC5B,IAgCIowB,EAAeC,EAAiBC,EAAgBC,EAAgBC,EAhChEX,EAAS7vB,EAAM6vB,OACfxzB,EAAQ2D,EAAM3D,MACd2M,EAAerR,KAAKuM,MACpBusB,EAAkBznB,EAAaynB,gBAC/BC,EAAc1nB,EAAa0nB,YAC3BC,EAAgB3nB,EAAa2nB,cAC7BzO,EAAOlZ,EAAakZ,KACpBgL,EAASlkB,EAAakkB,OACtBN,EAAgB5jB,EAAa4jB,cAC7BgE,EAAiBf,EAAO3rB,MACxBisB,EAAaS,EAAeT,WAC5BnD,EAAU4D,EAAe5D,QACzBsB,EAAuBsC,EAAetC,qBACtCuC,EAAcD,EAAeC,YAC7BpC,EAAiBmC,EAAenC,eAChCt4B,EAAKy6B,EAAez6B,GACpB82B,EAAQ2D,EAAe3D,MACvB6D,GAAeD,GAAe3O,EAC9BpD,EAAa,kBAAK,wCAAyC2R,EAAiBZ,EAAO3rB,MAAMusB,gBAAiB,CAC5GM,8CAA+CD,IAG7C75B,EAAQU,KAAKo4B,uBAAuBF,EAAQ,GAAc,GAAIa,EAAa,GAAIb,EAAO3rB,MAAMwsB,cAE5FM,EAAiBvC,EAAe,CAClC0B,WAAYA,EACZnD,QAASA,EACT6D,YAAaA,EACb5D,MAAOA,EACPC,OAAQA,EACRN,cAAeA,IAIjB,GAAIkE,GAAeH,EAAe,CAEhC,IAGIM,EAHkB/D,IAAWF,EAGQsB,EAAuB1B,IAAkB,GAAcF,KAAO,GAAcD,IAAM,GAAcC,KAErImB,EAAU,SAAiBjoB,GAC7BkrB,GAAe5O,EAAK,CAClBoM,qBAAsBA,EACtB1oB,MAAOA,EACPsnB,OAAQF,EACRJ,cAAeqE,IAEjBN,GAAiBA,EAAc,CAC7BR,WAAYA,EACZnD,QAASA,EACTpnB,MAAOA,KAUX4qB,EAAkBX,EAAO3rB,MAAM,eAAiB+oB,GAASD,EACzDuD,EAAiB,OACjBD,EAAiB,EACjBF,EAAgBvC,EAChBwC,EAVgB,SAAmBzqB,GACf,UAAdA,EAAM3J,KAAiC,MAAd2J,EAAM3J,KACjC4xB,EAAQjoB,IAkBd,OAPIsnB,IAAWF,IACbuD,EAAiB3D,IAAkB,GAAcH,IAAM,YAAc,cAMhE,gBAAoB,MAAO,CAChC,aAAc+D,EACd,YAAaD,EACb94B,UAAWqnB,EACX3oB,GAAIA,EACJ8F,IAAK,aAAeI,EACpBwxB,QAASuC,EACTze,UAAW0e,EACXtlB,KAAM,eACN9T,MAAOA,EACP+T,SAAUslB,GACTU,KAEJ,CACD/0B,IAAK,aACLC,MAAO,SAAoB+D,GACzB,IAAIuO,EAAS7W,KAET0E,EAAQ4D,EAAM+H,SACd1D,EAAcrE,EAAMqE,YACpBrI,EAAMgE,EAAMhE,IACZkS,EAASlO,EAAMkO,OACflX,EAAQgJ,EAAMhJ,MACdkS,EAAexR,KAAKuM,MACpBuN,EAAWtI,EAAasI,SACxB6b,EAAankB,EAAamkB,WAC1BC,EAAmBpkB,EAAaokB,iBAChCG,EAAkBvkB,EAAaukB,gBAC/BD,EAAiBtkB,EAAaskB,eAC9BD,EAAgBrkB,EAAaqkB,cAC7B8B,EAAenmB,EAAammB,aAC5B4B,EAAY/nB,EAAa+nB,UACzB7S,EAAclV,EAAakV,YAC3BkR,EAAWpmB,EAAaomB,SACxBZ,EAAiBh3B,KAAK4O,MAAMooB,eAC5Bc,EAAmC,mBAAjBH,EAA8BA,EAAa,CAC/DjzB,MAAOA,IACJizB,EACDI,EAAqC,mBAAbH,EAA0BA,EAAS,CAC7DlzB,MAAOA,IACJkzB,EACD5B,EAAUuD,EAAU,CACtB70B,MAAOA,IAELmwB,EAAU,WAAeoD,QAAQne,GAAUkI,KAAI,SAAUkW,EAAQ/nB,GACnE,OAAO0G,EAAOogB,cAAc,CAC1BiB,OAAQA,EACR/nB,YAAaA,EACbxD,YAAaA,EACb6J,OAAQA,EACRwf,QAASA,EACT3lB,SAAU3L,EACVsyB,eAAgBA,OAGhBl3B,EAAY,kBAAK,+BAAgCg4B,GAEjD0B,EAAiB,GAAc,GAAIl6B,EAAO,CAC5CG,OAAQO,KAAKy5B,cAAc/0B,GAC3BoQ,SAAU,SACViG,aAAcic,GACbe,GAEH,OAAOrR,EAAY,CACjB5mB,UAAWA,EACX+0B,QAASA,EACTnwB,MAAOA,EACPiI,YAAaA,EACbrI,IAAKA,EACLqxB,WAAYA,EACZC,iBAAkBA,EAClBG,gBAAiBA,EACjBD,eAAgBA,EAChBD,cAAeA,EACfG,QAASA,EACT12B,MAAOk6B,MAOV,CACDl1B,IAAK,yBACLC,MAAO,SAAgC2zB,GACrC,IAAIwB,EAAchxB,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK,GAClFixB,EAAY,GAAG/0B,OAAOszB,EAAO3rB,MAAMqqB,SAAU,KAAKhyB,OAAOszB,EAAO3rB,MAAMsqB,WAAY,KAAKjyB,OAAOszB,EAAO3rB,MAAMhN,MAAO,MAElHD,EAAQ,GAAc,GAAIo6B,EAAa,CACzCE,KAAMD,EACNE,OAAQF,EACRG,WAAYH,IAWd,OARIzB,EAAO3rB,MAAMqI,WACftV,EAAMsV,SAAWsjB,EAAO3rB,MAAMqI,UAG5BsjB,EAAO3rB,MAAM2R,WACf5e,EAAM4e,SAAWga,EAAO3rB,MAAM2R,UAGzB5e,IAER,CACDgF,IAAK,oBACLC,MAAO,WACL,IAAIw1B,EAAS/5B,KAETgS,EAAehS,KAAKuM,MACpBuN,EAAW9H,EAAa8H,SAG5B,OAFoB9H,EAAaulB,cACL,GAAK,WAAeU,QAAQne,IAC3CkI,KAAI,SAAUkW,EAAQxzB,GACjC,OAAOq1B,EAAOC,cAAc,CAC1B9B,OAAQA,EACRxzB,MAAOA,SAIZ,CACDJ,IAAK,gBACLC,MAAO,SAAuB8L,GAC5B,IAAI3B,EAAY1O,KAAKuM,MAAMmC,UAC3B,MAA4B,mBAAdA,EAA2BA,EAAU,CACjDhK,MAAO2L,IACJ3B,IAEN,CACDpK,IAAK,YACLC,MAAO,SAAmBgE,GACxB,IAAIyO,EAAezO,EAAMyO,aACrB3X,EAAekJ,EAAMlJ,aACrBD,EAAYmJ,EAAMnJ,WAEtBsV,EADe1U,KAAKuM,MAAMmI,UACjB,CACPsC,aAAcA,EACd3X,aAAcA,EACdD,UAAWA,MAGd,CACDkF,IAAK,qBACLC,MAAO,SAA4BgT,GACjC,IAAIhK,EAAwBgK,EAAMhK,sBAC9BE,EAAuB8J,EAAM9J,qBAC7BE,EAAgB4J,EAAM5J,cACtBE,EAAe0J,EAAM1J,cAEzBmX,EADqBhlB,KAAKuM,MAAMyY,gBACjB,CACb9O,mBAAoB3I,EACpB4I,kBAAmB1I,EACnBsI,WAAYpI,EACZqI,UAAWnI,MAGd,CACDvJ,IAAK,UACLC,MAAO,SAAiBwJ,GACtB/N,KAAKsM,KAAOyB,IAEb,CACDzJ,IAAK,qBACLC,MAAO,WACL,IAAIyyB,EAAiBh3B,KAAKi6B,oBAC1Bj6B,KAAK0M,SAAS,CACZsqB,eAAgBA,QAKfD,EA1hBT,CA2hBE,iBAEF,IAAgB,GAAO,eAAgB,CACrCQ,eAAe,EACfxf,iBAAkB,GAClB0f,aAAc,EACdsB,YAAa,GACb7R,eAAgB,WACd,OAAO,MAETlC,eAAgB,WACd,OAAO,MAETtQ,SAAU,WACR,OAAO,MAETU,sBAAuB,EACvBC,iBAAkB,GAClBqR,YAAagP,GACbgC,kBAAmB9C,GACnBgD,SAAU,GACV5tB,kBAAmB,OACnBlG,eAAgB,EAChBxE,MAAO,KAIT,GAAM8hB,UAoNF,GG7xBW,ICTX8Y,GAAmB,GACnBC,GAA4B,KAC5BC,GAAgC,KAEpC,SAASC,KACHD,KACFA,GAAgC,KAE5Bx8B,SAAS08B,MAAqC,MAA7BH,KACnBv8B,SAAS08B,KAAKh7B,MAAMyV,cAAgBolB,IAGtCA,GAA4B,MAIhC,SAASI,KACPF,KACAH,GAAiB75B,SAAQ,SAAUm6B,GACjC,OAAOA,EAASC,wBAgBpB,SAASC,GAAezsB,GAClBA,EAAMke,gBAAkB1uB,QAAuC,MAA7B08B,IAAqCv8B,SAAS08B,OAClFH,GAA4Bv8B,SAAS08B,KAAKh7B,MAAMyV,cAChDnX,SAAS08B,KAAKh7B,MAAMyV,cAAgB,QAfxC,WACMqlB,IACF,EAAuBA,IAGzB,IAAIO,EAAiB,EACrBT,GAAiB75B,SAAQ,SAAUm6B,GACjCG,EAAiB50B,KAAKC,IAAI20B,EAAgBH,EAASjuB,MAAMmK,+BAE3D0jB,GAAgC,EAAwBG,GAAuCI,GAS/FC,GACAV,GAAiB75B,SAAQ,SAAUm6B,GAC7BA,EAASjuB,MAAMsuB,gBAAkB5sB,EAAMke,eACzCqO,EAASM,+BAKR,SAASC,GAAuBxV,EAAW7mB,GAC3Cw7B,GAAiB7wB,MAAK,SAAUmxB,GACnC,OAAOA,EAASjuB,MAAMsuB,gBAAkBn8B,MAExCA,EAAQmE,iBAAiB,SAAU63B,IAGrCR,GAAiBn3B,KAAKwiB,GAEjB,SAASyV,GAAyBzV,EAAW7mB,IAClDw7B,GAAmBA,GAAiBxuB,QAAO,SAAU8uB,GACnD,OAAOA,IAAajV,MAGArkB,SACpBxC,EAAQyE,oBAAoB,SAAUu3B,IAElCN,KACF,EAAuBA,IACvBC,OChEN,ICGI,GAAQ,GDHRY,GAAW,SAAkBv8B,GAC/B,OAAOA,IAAYjB,QAGjBy9B,GAAiB,SAAwBx8B,GAC3C,OAAOA,EAAQy8B,yBAGV,SAASC,GAAcP,EAAetuB,GAC3C,GAAKsuB,EAKE,IAAII,GAASJ,GAAgB,CAClC,IAAIx9B,EAAUI,OACV49B,EAAch+B,EAAQg+B,YACtBC,EAAaj+B,EAAQi+B,WACzB,MAAO,CACL77B,OAA+B,iBAAhB47B,EAA2BA,EAAc,EACxD97B,MAA6B,iBAAf+7B,EAA0BA,EAAa,GAGvD,OAAOJ,GAAeL,GAbtB,MAAO,CACLp7B,OAAQ8M,EAAMgvB,aACdh8B,MAAOgN,EAAMivB,aAqBZ,SAASC,GAAkB/8B,EAASg9B,GACzC,GAAIT,GAASS,IAAc99B,SAAS+9B,gBAAiB,CACnD,IAAIC,EAAmBh+B,SAAS+9B,gBAC5BE,EAAcX,GAAex8B,GAC7Bo9B,EAAgBZ,GAAeU,GACnC,MAAO,CACL/iB,IAAKgjB,EAAYhjB,IAAMijB,EAAcjjB,IACrCD,KAAMijB,EAAYjjB,KAAOkjB,EAAcljB,MAGzC,IAAI7O,EAAegyB,GAAgBL,GAE/BM,EAAed,GAAex8B,GAE9Bu9B,EAAiBf,GAAeQ,GAEpC,MAAO,CACL7iB,IAAKmjB,EAAanjB,IAAM9O,EAAa8O,IAAMojB,EAAepjB,IAC1DD,KAAMojB,EAAapjB,KAAO7O,EAAa6O,KAAOqjB,EAAerjB,MAS5D,SAASmjB,GAAgBr9B,GAC9B,OAAIu8B,GAASv8B,IAAYd,SAAS+9B,gBACzB,CACL9iB,IAAK,YAAapb,OAASA,OAAOy+B,QAAUt+B,SAAS+9B,gBAAgBv8B,UACrEwZ,KAAM,YAAanb,OAASA,OAAO0+B,QAAUv+B,SAAS+9B,gBAAgBz8B,YAGjE,CACL2Z,IAAKna,EAAQU,UACbwZ,KAAMla,EAAQQ,YChEpB,SAAS,GAAQoM,EAAQC,GAAkB,IAAIzC,EAAOC,OAAOD,KAAKwC,GAAS,GAAIvC,OAAOyC,sBAAuB,CAAE,IAAIC,EAAU1C,OAAOyC,sBAAsBF,GAAaC,IAAgBE,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAO5C,OAAO6C,yBAAyBN,EAAQK,GAAKE,eAAgB/C,EAAK/F,KAAK+I,MAAMhD,EAAM2C,GAAY,OAAO3C,EAE9U,SAAS,GAAcjJ,GAAU,IAAK,IAAIoB,EAAI,EAAGA,EAAIyH,UAAUxH,OAAQD,IAAK,CAAE,IAAI+K,EAAyB,MAAhBtD,UAAUzH,GAAayH,UAAUzH,GAAK,GAAQA,EAAI,EAAK,GAAQ+K,GAAQ,GAAM3L,SAAQ,SAAUiE,GAAO,IAAgBzE,EAAQyE,EAAK0H,EAAO1H,OAAsByE,OAAOkD,0BAA6BlD,OAAOmD,iBAAiBrM,EAAQkJ,OAAOkD,0BAA0BD,IAAmB,GAAQA,GAAQ3L,SAAQ,SAAUiE,GAAOyE,OAAOoD,eAAetM,EAAQyE,EAAKyE,OAAO6C,yBAAyBI,EAAQ1H,OAAe,OAAOzE,EAYtf,IAEHu8B,GAAY,WACd,MAAyB,oBAAX3+B,OAAyBA,YAASsD,GAG9C,IAAkB,GAAQ,GAE9B,SAAUsL,GAGR,SAASgwB,IACP,IAAIpjB,EAEAzM,EAEJ,IAAgBxM,KAAMq8B,GAEtB,IAAK,IAAInjB,EAAOxQ,UAAUxH,OAAQiY,EAAO,IAAIjQ,MAAMgQ,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,GAAQ1Q,UAAU0Q,GAuGzB,OApGA5M,EAAQ,IAA2BxM,MAAOiZ,EAAmB,IAAgBojB,IAAiB/7B,KAAKwL,MAAMmN,EAAkB,CAACjZ,MAAM4E,OAAOuU,KAEzI,IAAgB,IAAuB3M,GAAQ,UAAW4vB,MAE1D,IAAgB,IAAuB5vB,GAAQ,cAAc,GAE7D,IAAgB,IAAuBA,GAAQ,mBAAoB,GAEnE,IAAgB,IAAuBA,GAAQ,oBAAqB,GAEpE,IAAgB,IAAuBA,GAAQ,4BAAwB,GAEvE,IAAgB,IAAuBA,GAAQ,cAAU,GAEzD,IAAgB,IAAuBA,GAAQ,QAAS,GAAc,GAAI4uB,GAAc5uB,EAAMD,MAAMsuB,cAAeruB,EAAMD,OAAQ,CAC/HI,aAAa,EACbzN,WAAY,EACZE,UAAW,KAGb,IAAgB,IAAuBoN,GAAQ,kBAAkB,SAAU9N,IACrEA,GAAaA,aAAmB6d,SAClCC,QAAQC,KAAK,qEAGfjQ,EAAMkQ,OAAShe,EAEf8N,EAAM8vB,oBAGR,IAAgB,IAAuB9vB,GAAQ,kBAAkB,SAAUlJ,GACzE,IAAIlE,EAAYkE,EAAKlE,UAErB,GAAIoN,EAAMoC,MAAMxP,YAAcA,EAA9B,CAIA,IAAIy7B,EAAgBruB,EAAMD,MAAMsuB,cAE5BA,IACoC,mBAA3BA,EAAc0B,SACvB1B,EAAc0B,SAAS,EAAGn9B,EAAYoN,EAAMgwB,kBAE5C3B,EAAcz7B,UAAYA,EAAYoN,EAAMgwB,sBAKlD,IAAgB,IAAuBhwB,GAAQ,2BAA2B,SAAU9N,GAC9EA,IAAYjB,OACdA,OAAOoF,iBAAiB,SAAU2J,EAAMkP,WAAW,GAEnDlP,EAAMiP,qBAAqBna,kBAAkB5C,EAAS8N,EAAMkP,cAIhE,IAAgB,IAAuBlP,GAAQ,6BAA6B,SAAU9N,GAChFA,IAAYjB,OACdA,OAAO0F,oBAAoB,SAAUqJ,EAAMkP,WAAW,GAC7Chd,GACT8N,EAAMiP,qBAAqBzY,qBAAqBtE,EAAS8N,EAAMkP,cAInE,IAAgB,IAAuBlP,GAAQ,aAAa,WAC1DA,EAAM8vB,oBAGR,IAAgB,IAAuB9vB,GAAQ,6BAA6B,WAC1E,GAAKA,EAAMiwB,WAAX,CAIA,IAAI/nB,EAAWlI,EAAMD,MAAMmI,SACvBmmB,EAAgBruB,EAAMD,MAAMsuB,cAEhC,GAAIA,EAAe,CACjB,IAAI9wB,EAAegyB,GAAgBlB,GAC/B37B,EAAa6G,KAAKC,IAAI,EAAG+D,EAAa6O,KAAOpM,EAAMkwB,mBACnDt9B,EAAY2G,KAAKC,IAAI,EAAG+D,EAAa8O,IAAMrM,EAAMgwB,kBAErDhwB,EAAME,SAAS,CACbC,aAAa,EACbzN,WAAYA,EACZE,UAAWA,IAGbsV,EAAS,CACPxV,WAAYA,EACZE,UAAWA,SAKjB,IAAgB,IAAuBoN,GAAQ,sBAAsB,WACnEA,EAAME,SAAS,CACbC,aAAa,OAIVH,EAkGT,OAnNA,IAAU6vB,EAAgBhwB,GAoH1B,IAAagwB,EAAgB,CAAC,CAC5B/3B,IAAK,iBACLC,MAAO,WACL,IAAIs2B,EAAgBnyB,UAAUxH,OAAS,QAAsBH,IAAjB2H,UAAU,GAAmBA,UAAU,GAAK1I,KAAKuM,MAAMsuB,cAC/FlgB,EAAW3a,KAAKuM,MAAMoO,SACtB/H,EAAc5S,KAAK4O,MACnBnP,EAASmT,EAAYnT,OACrBF,EAAQqT,EAAYrT,MACpBo9B,EAAW38B,KAAK0c,QAAU,eAAqB1c,MAEnD,GAAI28B,aAAoBpgB,SAAWse,EAAe,CAChD,IAAI91B,EAAS02B,GAAkBkB,EAAU9B,GACzC76B,KAAKw8B,iBAAmBz3B,EAAO8T,IAC/B7Y,KAAK08B,kBAAoB33B,EAAO6T,KAGlC,IAAIgkB,EAAaxB,GAAcP,EAAe76B,KAAKuM,OAE/C9M,IAAWm9B,EAAWn9B,QAAUF,IAAUq9B,EAAWr9B,QACvDS,KAAK0M,SAAS,CACZjN,OAAQm9B,EAAWn9B,OACnBF,MAAOq9B,EAAWr9B,QAEpBob,EAAS,CACPlb,OAAQm9B,EAAWn9B,OACnBF,MAAOq9B,EAAWr9B,WAIvB,CACD+E,IAAK,oBACLC,MAAO,WACL,IAAIs2B,EAAgB76B,KAAKuM,MAAMsuB,cAC/B76B,KAAKyb,qBAAuB,cAC5Bzb,KAAKs8B,eAAezB,GAEhBA,IACFE,GAAuB/6B,KAAM66B,GAE7B76B,KAAK68B,wBAAwBhC,IAG/B76B,KAAKy8B,YAAa,IAEnB,CACDn4B,IAAK,qBACLC,MAAO,SAA4BkO,EAAWN,GAC5C,IAAI0oB,EAAgB76B,KAAKuM,MAAMsuB,cAC3BiC,EAAoBrqB,EAAUooB,cAE9BiC,IAAsBjC,GAAsC,MAArBiC,GAA8C,MAAjBjC,IACtE76B,KAAKs8B,eAAezB,GACpBG,GAAyBh7B,KAAM88B,GAC/B/B,GAAuB/6B,KAAM66B,GAE7B76B,KAAK+8B,0BAA0BD,GAE/B98B,KAAK68B,wBAAwBhC,MAGhC,CACDv2B,IAAK,uBACLC,MAAO,WACL,IAAIs2B,EAAgB76B,KAAKuM,MAAMsuB,cAE3BA,IACFG,GAAyBh7B,KAAM66B,GAE/B76B,KAAK+8B,0BAA0BlC,IAGjC76B,KAAKy8B,YAAa,IAEnB,CACDn4B,IAAK,SACLC,MAAO,WACL,IAAIuV,EAAW9Z,KAAKuM,MAAMuN,SACtBxG,EAAetT,KAAK4O,MACpBjC,EAAc2G,EAAa3G,YAC3BvN,EAAYkU,EAAalU,UACzBF,EAAaoU,EAAapU,WAC1BO,EAAS6T,EAAa7T,OACtBF,EAAQ+T,EAAa/T,MACzB,OAAOua,EAAS,CACdkjB,cAAeh9B,KAAKi9B,eACpBngB,cAAe9c,KAAK+c,eACpBtd,OAAQA,EACRkN,YAAaA,EACbzN,WAAYA,EACZE,UAAWA,EACXG,MAAOA,QAKN88B,EApNT,CAqNE,iBAAsB,IAAgB,GAAQ,YAAqD,MA6BjG,IAEJ,IAAgB,GAAgB,eAAgB,CAC9C1hB,SAAU,aACVjG,SAAU,aACVgC,2BA/PgC,IAgQhCmkB,cAAeuB,KACfb,aAAc,EACdC,YAAa", "file": "chunks/chunk.83.js", "sourcesContent": ["/**\n * Detect Element Resize.\n * https://github.com/sdecima/javascript-detect-element-resize\n * Sebastian Decima\n *\n * Forked from version 0.5.3; includes the following modifications:\n * 1) Guard against unsafe 'window' and 'document' references (to support SSR).\n * 2) Defer initialization code via a top-level function wrapper (to support SSR).\n * 3) Avoid unnecessary reflows by not measuring size for scroll events bubbling from children.\n * 4) Add nonce for style element.\n * 5) Added support for injecting custom window object\n **/\nexport default function createDetectElementResize(nonce, hostWindow) {\n  // Check `document` and `window` in case of server-side rendering\n  var _window;\n\n  if (typeof hostWindow !== 'undefined') {\n    _window = hostWindow;\n  } else if (typeof window !== 'undefined') {\n    _window = window;\n  } else if (typeof self !== 'undefined') {\n    _window = self;\n  } else {\n    _window = global;\n  }\n\n  var attachEvent = typeof _window.document !== 'undefined' && _window.document.attachEvent;\n\n  if (!attachEvent) {\n    var requestFrame = function () {\n      var raf = _window.requestAnimationFrame || _window.mozRequestAnimationFrame || _window.webkitRequestAnimationFrame || function (fn) {\n        return _window.setTimeout(fn, 20);\n      };\n\n      return function (fn) {\n        return raf(fn);\n      };\n    }();\n\n    var cancelFrame = function () {\n      var cancel = _window.cancelAnimationFrame || _window.mozCancelAnimationFrame || _window.webkitCancelAnimationFrame || _window.clearTimeout;\n      return function (id) {\n        return cancel(id);\n      };\n    }();\n\n    var resetTriggers = function resetTriggers(element) {\n      var triggers = element.__resizeTriggers__,\n          expand = triggers.firstElementChild,\n          contract = triggers.lastElementChild,\n          expandChild = expand.firstElementChild;\n      contract.scrollLeft = contract.scrollWidth;\n      contract.scrollTop = contract.scrollHeight;\n      expandChild.style.width = expand.offsetWidth + 1 + 'px';\n      expandChild.style.height = expand.offsetHeight + 1 + 'px';\n      expand.scrollLeft = expand.scrollWidth;\n      expand.scrollTop = expand.scrollHeight;\n    };\n\n    var checkTriggers = function checkTriggers(element) {\n      return element.offsetWidth != element.__resizeLast__.width || element.offsetHeight != element.__resizeLast__.height;\n    };\n\n    var scrollListener = function scrollListener(e) {\n      // Don't measure (which forces) reflow for scrolls that happen inside of children!\n      if (e.target.className && typeof e.target.className.indexOf === 'function' && e.target.className.indexOf('contract-trigger') < 0 && e.target.className.indexOf('expand-trigger') < 0) {\n        return;\n      }\n\n      var element = this;\n      resetTriggers(this);\n\n      if (this.__resizeRAF__) {\n        cancelFrame(this.__resizeRAF__);\n      }\n\n      this.__resizeRAF__ = requestFrame(function () {\n        if (checkTriggers(element)) {\n          element.__resizeLast__.width = element.offsetWidth;\n          element.__resizeLast__.height = element.offsetHeight;\n\n          element.__resizeListeners__.forEach(function (fn) {\n            fn.call(element, e);\n          });\n        }\n      });\n    };\n    /* Detect CSS Animations support to detect element display/re-attach */\n\n\n    var animation = false,\n        keyframeprefix = '',\n        animationstartevent = 'animationstart',\n        domPrefixes = 'Webkit Moz O ms'.split(' '),\n        startEvents = 'webkitAnimationStart animationstart oAnimationStart MSAnimationStart'.split(' '),\n        pfx = '';\n    {\n      var elm = _window.document.createElement('fakeelement');\n\n      if (elm.style.animationName !== undefined) {\n        animation = true;\n      }\n\n      if (animation === false) {\n        for (var i = 0; i < domPrefixes.length; i++) {\n          if (elm.style[domPrefixes[i] + 'AnimationName'] !== undefined) {\n            pfx = domPrefixes[i];\n            keyframeprefix = '-' + pfx.toLowerCase() + '-';\n            animationstartevent = startEvents[i];\n            animation = true;\n            break;\n          }\n        }\n      }\n    }\n    var animationName = 'resizeanim';\n    var animationKeyframes = '@' + keyframeprefix + 'keyframes ' + animationName + ' { from { opacity: 0; } to { opacity: 0; } } ';\n    var animationStyle = keyframeprefix + 'animation: 1ms ' + animationName + '; ';\n  }\n\n  var createStyles = function createStyles(doc) {\n    if (!doc.getElementById('detectElementResize')) {\n      //opacity:0 works around a chrome bug https://code.google.com/p/chromium/issues/detail?id=286360\n      var css = (animationKeyframes ? animationKeyframes : '') + '.resize-triggers { ' + (animationStyle ? animationStyle : '') + 'visibility: hidden; opacity: 0; } ' + '.resize-triggers, .resize-triggers > div, .contract-trigger:before { content: \" \"; display: block; position: absolute; top: 0; left: 0; height: 100%; width: 100%; overflow: hidden; z-index: -1; } .resize-triggers > div { background: #eee; overflow: auto; } .contract-trigger:before { width: 200%; height: 200%; }',\n          head = doc.head || doc.getElementsByTagName('head')[0],\n          style = doc.createElement('style');\n      style.id = 'detectElementResize';\n      style.type = 'text/css';\n\n      if (nonce != null) {\n        style.setAttribute('nonce', nonce);\n      }\n\n      if (style.styleSheet) {\n        style.styleSheet.cssText = css;\n      } else {\n        style.appendChild(doc.createTextNode(css));\n      }\n\n      head.appendChild(style);\n    }\n  };\n\n  var addResizeListener = function addResizeListener(element, fn) {\n    if (attachEvent) {\n      element.attachEvent('onresize', fn);\n    } else {\n      if (!element.__resizeTriggers__) {\n        var doc = element.ownerDocument;\n\n        var elementStyle = _window.getComputedStyle(element);\n\n        if (elementStyle && elementStyle.position == 'static') {\n          element.style.position = 'relative';\n        }\n\n        createStyles(doc);\n        element.__resizeLast__ = {};\n        element.__resizeListeners__ = [];\n        (element.__resizeTriggers__ = doc.createElement('div')).className = 'resize-triggers';\n        var resizeTriggersHtml = '<div class=\"expand-trigger\"><div></div></div>' + '<div class=\"contract-trigger\"></div>';\n\n        if (window.trustedTypes) {\n          var staticPolicy = trustedTypes.createPolicy('react-virtualized-auto-sizer', {\n            createHTML: function createHTML() {\n              return resizeTriggersHtml;\n            }\n          });\n          element.__resizeTriggers__.innerHTML = staticPolicy.createHTML('');\n        } else {\n          element.__resizeTriggers__.innerHTML = resizeTriggersHtml;\n        }\n\n        element.appendChild(element.__resizeTriggers__);\n        resetTriggers(element);\n        element.addEventListener('scroll', scrollListener, true);\n        /* Listen for a css animation to detect element display/re-attach */\n\n        if (animationstartevent) {\n          element.__resizeTriggers__.__animationListener__ = function animationListener(e) {\n            if (e.animationName == animationName) {\n              resetTriggers(element);\n            }\n          };\n\n          element.__resizeTriggers__.addEventListener(animationstartevent, element.__resizeTriggers__.__animationListener__);\n        }\n      }\n\n      element.__resizeListeners__.push(fn);\n    }\n  };\n\n  var removeResizeListener = function removeResizeListener(element, fn) {\n    if (attachEvent) {\n      element.detachEvent('onresize', fn);\n    } else {\n      element.__resizeListeners__.splice(element.__resizeListeners__.indexOf(fn), 1);\n\n      if (!element.__resizeListeners__.length) {\n        element.removeEventListener('scroll', scrollListener, true);\n\n        if (element.__resizeTriggers__.__animationListener__) {\n          element.__resizeTriggers__.removeEventListener(animationstartevent, element.__resizeTriggers__.__animationListener__);\n\n          element.__resizeTriggers__.__animationListener__ = null;\n        }\n\n        try {\n          element.__resizeTriggers__ = !element.removeChild(element.__resizeTriggers__);\n        } catch (e) {// Preact compat; see developit/preact-compat/issues/228\n        }\n      }\n    }\n  };\n\n  return {\n    addResizeListener: addResizeListener,\n    removeResizeListener: removeResizeListener\n  };\n}", "/**\n * Helper method that determines when to recalculate row or column metadata.\n */\nexport default function calculateSizeAndPositionDataAndUpdateScrollOffset(_ref) {\n  var cellCount = _ref.cellCount,\n      cellSize = _ref.cellSize,\n      computeMetadataCallback = _ref.computeMetadataCallback,\n      computeMetadataCallbackProps = _ref.computeMetadataCallbackProps,\n      nextCellsCount = _ref.nextCellsCount,\n      nextCellSize = _ref.nextCellSize,\n      nextScrollToIndex = _ref.nextScrollToIndex,\n      scrollToIndex = _ref.scrollToIndex,\n      updateScrollOffsetForScrollToIndex = _ref.updateScrollOffsetForScrollToIndex;\n\n  // Don't compare cell sizes if they are functions because inline functions would cause infinite loops.\n  // In that event users should use the manual recompute methods to inform of changes.\n  if (cellCount !== nextCellsCount || (typeof cellSize === 'number' || typeof nextCellSize === 'number') && cellSize !== nextCellSize) {\n    computeMetadataCallback(computeMetadataCallbackProps); // Updated cell metadata may have hidden the previous scrolled-to item.\n    // In this case we should also update the scrollTop to ensure it stays visible.\n\n    if (scrollToIndex >= 0 && scrollToIndex === nextScrollToIndex) {\n      updateScrollOffsetForScrollToIndex();\n    }\n  }\n}", "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\n\n/**\n * Just-in-time calculates and caches size and position information for a collection of cells.\n */\nvar CellSizeAndPositionManager =\n/*#__PURE__*/\nfunction () {\n  // Cache of size and position data for cells, mapped by cell index.\n  // Note that invalid values may exist in this map so only rely on cells up to this._lastMeasuredIndex\n  // Measurements for cells up to this index can be trusted; cells afterward should be estimated.\n  // Used in deferred mode to track which cells have been queued for measurement.\n  function CellSizeAndPositionManager(_ref) {\n    var cellCount = _ref.cellCount,\n        cellSizeGetter = _ref.cellSizeGetter,\n        estimatedCellSize = _ref.estimatedCellSize;\n\n    _classCallCheck(this, CellSizeAndPositionManager);\n\n    _defineProperty(this, \"_cellSizeAndPositionData\", {});\n\n    _defineProperty(this, \"_lastMeasuredIndex\", -1);\n\n    _defineProperty(this, \"_lastBatchedIndex\", -1);\n\n    _defineProperty(this, \"_cellCount\", void 0);\n\n    _defineProperty(this, \"_cellSizeGetter\", void 0);\n\n    _defineProperty(this, \"_estimatedCellSize\", void 0);\n\n    this._cellSizeGetter = cellSizeGetter;\n    this._cellCount = cellCount;\n    this._estimatedCellSize = estimatedCellSize;\n  }\n\n  _createClass(CellSizeAndPositionManager, [{\n    key: \"areOffsetsAdjusted\",\n    value: function areOffsetsAdjusted() {\n      return false;\n    }\n  }, {\n    key: \"configure\",\n    value: function configure(_ref2) {\n      var cellCount = _ref2.cellCount,\n          estimatedCellSize = _ref2.estimatedCellSize,\n          cellSizeGetter = _ref2.cellSizeGetter;\n      this._cellCount = cellCount;\n      this._estimatedCellSize = estimatedCellSize;\n      this._cellSizeGetter = cellSizeGetter;\n    }\n  }, {\n    key: \"getCellCount\",\n    value: function getCellCount() {\n      return this._cellCount;\n    }\n  }, {\n    key: \"getEstimatedCellSize\",\n    value: function getEstimatedCellSize() {\n      return this._estimatedCellSize;\n    }\n  }, {\n    key: \"getLastMeasuredIndex\",\n    value: function getLastMeasuredIndex() {\n      return this._lastMeasuredIndex;\n    }\n  }, {\n    key: \"getOffsetAdjustment\",\n    value: function getOffsetAdjustment() {\n      return 0;\n    }\n    /**\n     * This method returns the size and position for the cell at the specified index.\n     * It just-in-time calculates (or used cached values) for cells leading up to the index.\n     */\n\n  }, {\n    key: \"getSizeAndPositionOfCell\",\n    value: function getSizeAndPositionOfCell(index) {\n      if (index < 0 || index >= this._cellCount) {\n        throw Error(\"Requested index \".concat(index, \" is outside of range 0..\").concat(this._cellCount));\n      }\n\n      if (index > this._lastMeasuredIndex) {\n        var lastMeasuredCellSizeAndPosition = this.getSizeAndPositionOfLastMeasuredCell();\n        var offset = lastMeasuredCellSizeAndPosition.offset + lastMeasuredCellSizeAndPosition.size;\n\n        for (var i = this._lastMeasuredIndex + 1; i <= index; i++) {\n          var size = this._cellSizeGetter({\n            index: i\n          }); // undefined or NaN probably means a logic error in the size getter.\n          // null means we're using CellMeasurer and haven't yet measured a given index.\n\n\n          if (size === undefined || isNaN(size)) {\n            throw Error(\"Invalid size returned for cell \".concat(i, \" of value \").concat(size));\n          } else if (size === null) {\n            this._cellSizeAndPositionData[i] = {\n              offset: offset,\n              size: 0\n            };\n            this._lastBatchedIndex = index;\n          } else {\n            this._cellSizeAndPositionData[i] = {\n              offset: offset,\n              size: size\n            };\n            offset += size;\n            this._lastMeasuredIndex = index;\n          }\n        }\n      }\n\n      return this._cellSizeAndPositionData[index];\n    }\n  }, {\n    key: \"getSizeAndPositionOfLastMeasuredCell\",\n    value: function getSizeAndPositionOfLastMeasuredCell() {\n      return this._lastMeasuredIndex >= 0 ? this._cellSizeAndPositionData[this._lastMeasuredIndex] : {\n        offset: 0,\n        size: 0\n      };\n    }\n    /**\n     * Total size of all cells being measured.\n     * This value will be completely estimated initially.\n     * As cells are measured, the estimate will be updated.\n     */\n\n  }, {\n    key: \"getTotalSize\",\n    value: function getTotalSize() {\n      var lastMeasuredCellSizeAndPosition = this.getSizeAndPositionOfLastMeasuredCell();\n      var totalSizeOfMeasuredCells = lastMeasuredCellSizeAndPosition.offset + lastMeasuredCellSizeAndPosition.size;\n      var numUnmeasuredCells = this._cellCount - this._lastMeasuredIndex - 1;\n      var totalSizeOfUnmeasuredCells = numUnmeasuredCells * this._estimatedCellSize;\n      return totalSizeOfMeasuredCells + totalSizeOfUnmeasuredCells;\n    }\n    /**\n     * Determines a new offset that ensures a certain cell is visible, given the current offset.\n     * If the cell is already visible then the current offset will be returned.\n     * If the current offset is too great or small, it will be adjusted just enough to ensure the specified index is visible.\n     *\n     * @param align Desired alignment within container; one of \"auto\" (default), \"start\", or \"end\"\n     * @param containerSize Size (width or height) of the container viewport\n     * @param currentOffset Container's current (x or y) offset\n     * @param totalSize Total size (width or height) of all cells\n     * @return Offset to use to ensure the specified cell is visible\n     */\n\n  }, {\n    key: \"getUpdatedOffsetForIndex\",\n    value: function getUpdatedOffsetForIndex(_ref3) {\n      var _ref3$align = _ref3.align,\n          align = _ref3$align === void 0 ? 'auto' : _ref3$align,\n          containerSize = _ref3.containerSize,\n          currentOffset = _ref3.currentOffset,\n          targetIndex = _ref3.targetIndex;\n\n      if (containerSize <= 0) {\n        return 0;\n      }\n\n      var datum = this.getSizeAndPositionOfCell(targetIndex);\n      var maxOffset = datum.offset;\n      var minOffset = maxOffset - containerSize + datum.size;\n      var idealOffset;\n\n      switch (align) {\n        case 'start':\n          idealOffset = maxOffset;\n          break;\n\n        case 'end':\n          idealOffset = minOffset;\n          break;\n\n        case 'center':\n          idealOffset = maxOffset - (containerSize - datum.size) / 2;\n          break;\n\n        default:\n          idealOffset = Math.max(minOffset, Math.min(maxOffset, currentOffset));\n          break;\n      }\n\n      var totalSize = this.getTotalSize();\n      return Math.max(0, Math.min(totalSize - containerSize, idealOffset));\n    }\n  }, {\n    key: \"getVisibleCellRange\",\n    value: function getVisibleCellRange(params) {\n      var containerSize = params.containerSize,\n          offset = params.offset;\n      var totalSize = this.getTotalSize();\n\n      if (totalSize === 0) {\n        return {};\n      }\n\n      var maxOffset = offset + containerSize;\n\n      var start = this._findNearestCell(offset);\n\n      var datum = this.getSizeAndPositionOfCell(start);\n      offset = datum.offset + datum.size;\n      var stop = start;\n\n      while (offset < maxOffset && stop < this._cellCount - 1) {\n        stop++;\n        offset += this.getSizeAndPositionOfCell(stop).size;\n      }\n\n      return {\n        start: start,\n        stop: stop\n      };\n    }\n    /**\n     * Clear all cached values for cells after the specified index.\n     * This method should be called for any cell that has changed its size.\n     * It will not immediately perform any calculations; they'll be performed the next time getSizeAndPositionOfCell() is called.\n     */\n\n  }, {\n    key: \"resetCell\",\n    value: function resetCell(index) {\n      this._lastMeasuredIndex = Math.min(this._lastMeasuredIndex, index - 1);\n    }\n  }, {\n    key: \"_binarySearch\",\n    value: function _binarySearch(high, low, offset) {\n      while (low <= high) {\n        var middle = low + Math.floor((high - low) / 2);\n        var currentOffset = this.getSizeAndPositionOfCell(middle).offset;\n\n        if (currentOffset === offset) {\n          return middle;\n        } else if (currentOffset < offset) {\n          low = middle + 1;\n        } else if (currentOffset > offset) {\n          high = middle - 1;\n        }\n      }\n\n      if (low > 0) {\n        return low - 1;\n      } else {\n        return 0;\n      }\n    }\n  }, {\n    key: \"_exponentialSearch\",\n    value: function _exponentialSearch(index, offset) {\n      var interval = 1;\n\n      while (index < this._cellCount && this.getSizeAndPositionOfCell(index).offset < offset) {\n        index += interval;\n        interval *= 2;\n      }\n\n      return this._binarySearch(Math.min(index, this._cellCount - 1), Math.floor(index / 2), offset);\n    }\n    /**\n     * Searches for the cell (index) nearest the specified offset.\n     *\n     * If no exact match is found the next lowest cell index will be returned.\n     * This allows partially visible cells (with offsets just before/above the fold) to be visible.\n     */\n\n  }, {\n    key: \"_findNearestCell\",\n    value: function _findNearestCell(offset) {\n      if (isNaN(offset)) {\n        throw Error(\"Invalid offset \".concat(offset, \" specified\"));\n      } // Our search algorithms find the nearest match at or below the specified offset.\n      // So make sure the offset is at least 0 or no match will be found.\n\n\n      offset = Math.max(0, offset);\n      var lastMeasuredCellSizeAndPosition = this.getSizeAndPositionOfLastMeasuredCell();\n      var lastMeasuredIndex = Math.max(0, this._lastMeasuredIndex);\n\n      if (lastMeasuredCellSizeAndPosition.offset >= offset) {\n        // If we've already measured cells within this range just use a binary search as it's faster.\n        return this._binarySearch(lastMeasuredIndex, 0, offset);\n      } else {\n        // If we haven't yet measured this high, fallback to an exponential search with an inner binary search.\n        // The exponential search avoids pre-computing sizes for the full set of cells as a binary search would.\n        // The overall complexity for this approach is O(log n).\n        return this._exponentialSearch(lastMeasuredIndex, offset);\n      }\n    }\n  }]);\n\n  return CellSizeAndPositionManager;\n}();\n\nexport { CellSizeAndPositionManager as default };\nimport { bpfrpt_proptype_Alignment } from \"../types\";\nimport { bpfrpt_proptype_CellSizeGetter } from \"../types\";\nimport { bpfrpt_proptype_VisibleCellRange } from \"../types\";", "var DEFAULT_MAX_ELEMENT_SIZE = 1500000;\nvar CHROME_MAX_ELEMENT_SIZE = 1.67771e7;\n\nvar isBrowser = function isBrowser() {\n  return typeof window !== 'undefined';\n};\n\nvar isChrome = function isChrome() {\n  return !!window.chrome;\n};\n\nexport var getMaxElementSize = function getMaxElementSize() {\n  if (isBrowser()) {\n    if (isChrome()) {\n      return CHROME_MAX_ELEMENT_SIZE;\n    }\n  }\n\n  return DEFAULT_MAX_ELEMENT_SIZE;\n};", "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport CellSizeAndPositionManager from './CellSizeAndPositionManager';\nimport { getMaxElementSize } from './maxElementSize.js';\n\n/**\n * Extends CellSizeAndPositionManager and adds scaling behavior for lists that are too large to fit within a browser's native limits.\n */\nvar ScalingCellSizeAndPositionManager =\n/*#__PURE__*/\nfunction () {\n  function ScalingCellSizeAndPositionManager(_ref) {\n    var _ref$maxScrollSize = _ref.maxScrollSize,\n        maxScrollSize = _ref$maxScrollSize === void 0 ? getMaxElementSize() : _ref$maxScrollSize,\n        params = _objectWithoutProperties(_ref, [\"maxScrollSize\"]);\n\n    _classCallCheck(this, ScalingCellSizeAndPositionManager);\n\n    _defineProperty(this, \"_cellSizeAndPositionManager\", void 0);\n\n    _defineProperty(this, \"_maxScrollSize\", void 0);\n\n    // Favor composition over inheritance to simplify IE10 support\n    this._cellSizeAndPositionManager = new CellSizeAndPositionManager(params);\n    this._maxScrollSize = maxScrollSize;\n  }\n\n  _createClass(ScalingCellSizeAndPositionManager, [{\n    key: \"areOffsetsAdjusted\",\n    value: function areOffsetsAdjusted() {\n      return this._cellSizeAndPositionManager.getTotalSize() > this._maxScrollSize;\n    }\n  }, {\n    key: \"configure\",\n    value: function configure(params) {\n      this._cellSizeAndPositionManager.configure(params);\n    }\n  }, {\n    key: \"getCellCount\",\n    value: function getCellCount() {\n      return this._cellSizeAndPositionManager.getCellCount();\n    }\n  }, {\n    key: \"getEstimatedCellSize\",\n    value: function getEstimatedCellSize() {\n      return this._cellSizeAndPositionManager.getEstimatedCellSize();\n    }\n  }, {\n    key: \"getLastMeasuredIndex\",\n    value: function getLastMeasuredIndex() {\n      return this._cellSizeAndPositionManager.getLastMeasuredIndex();\n    }\n    /**\n     * Number of pixels a cell at the given position (offset) should be shifted in order to fit within the scaled container.\n     * The offset passed to this function is scaled (safe) as well.\n     */\n\n  }, {\n    key: \"getOffsetAdjustment\",\n    value: function getOffsetAdjustment(_ref2) {\n      var containerSize = _ref2.containerSize,\n          offset = _ref2.offset;\n\n      var totalSize = this._cellSizeAndPositionManager.getTotalSize();\n\n      var safeTotalSize = this.getTotalSize();\n\n      var offsetPercentage = this._getOffsetPercentage({\n        containerSize: containerSize,\n        offset: offset,\n        totalSize: safeTotalSize\n      });\n\n      return Math.round(offsetPercentage * (safeTotalSize - totalSize));\n    }\n  }, {\n    key: \"getSizeAndPositionOfCell\",\n    value: function getSizeAndPositionOfCell(index) {\n      return this._cellSizeAndPositionManager.getSizeAndPositionOfCell(index);\n    }\n  }, {\n    key: \"getSizeAndPositionOfLastMeasuredCell\",\n    value: function getSizeAndPositionOfLastMeasuredCell() {\n      return this._cellSizeAndPositionManager.getSizeAndPositionOfLastMeasuredCell();\n    }\n    /** See CellSizeAndPositionManager#getTotalSize */\n\n  }, {\n    key: \"getTotalSize\",\n    value: function getTotalSize() {\n      return Math.min(this._maxScrollSize, this._cellSizeAndPositionManager.getTotalSize());\n    }\n    /** See CellSizeAndPositionManager#getUpdatedOffsetForIndex */\n\n  }, {\n    key: \"getUpdatedOffsetForIndex\",\n    value: function getUpdatedOffsetForIndex(_ref3) {\n      var _ref3$align = _ref3.align,\n          align = _ref3$align === void 0 ? 'auto' : _ref3$align,\n          containerSize = _ref3.containerSize,\n          currentOffset = _ref3.currentOffset,\n          targetIndex = _ref3.targetIndex;\n      currentOffset = this._safeOffsetToOffset({\n        containerSize: containerSize,\n        offset: currentOffset\n      });\n\n      var offset = this._cellSizeAndPositionManager.getUpdatedOffsetForIndex({\n        align: align,\n        containerSize: containerSize,\n        currentOffset: currentOffset,\n        targetIndex: targetIndex\n      });\n\n      return this._offsetToSafeOffset({\n        containerSize: containerSize,\n        offset: offset\n      });\n    }\n    /** See CellSizeAndPositionManager#getVisibleCellRange */\n\n  }, {\n    key: \"getVisibleCellRange\",\n    value: function getVisibleCellRange(_ref4) {\n      var containerSize = _ref4.containerSize,\n          offset = _ref4.offset;\n      offset = this._safeOffsetToOffset({\n        containerSize: containerSize,\n        offset: offset\n      });\n      return this._cellSizeAndPositionManager.getVisibleCellRange({\n        containerSize: containerSize,\n        offset: offset\n      });\n    }\n  }, {\n    key: \"resetCell\",\n    value: function resetCell(index) {\n      this._cellSizeAndPositionManager.resetCell(index);\n    }\n  }, {\n    key: \"_getOffsetPercentage\",\n    value: function _getOffsetPercentage(_ref5) {\n      var containerSize = _ref5.containerSize,\n          offset = _ref5.offset,\n          totalSize = _ref5.totalSize;\n      return totalSize <= containerSize ? 0 : offset / (totalSize - containerSize);\n    }\n  }, {\n    key: \"_offsetToSafeOffset\",\n    value: function _offsetToSafeOffset(_ref6) {\n      var containerSize = _ref6.containerSize,\n          offset = _ref6.offset;\n\n      var totalSize = this._cellSizeAndPositionManager.getTotalSize();\n\n      var safeTotalSize = this.getTotalSize();\n\n      if (totalSize === safeTotalSize) {\n        return offset;\n      } else {\n        var offsetPercentage = this._getOffsetPercentage({\n          containerSize: containerSize,\n          offset: offset,\n          totalSize: totalSize\n        });\n\n        return Math.round(offsetPercentage * (safeTotalSize - containerSize));\n      }\n    }\n  }, {\n    key: \"_safeOffsetToOffset\",\n    value: function _safeOffsetToOffset(_ref7) {\n      var containerSize = _ref7.containerSize,\n          offset = _ref7.offset;\n\n      var totalSize = this._cellSizeAndPositionManager.getTotalSize();\n\n      var safeTotalSize = this.getTotalSize();\n\n      if (totalSize === safeTotalSize) {\n        return offset;\n      } else {\n        var offsetPercentage = this._getOffsetPercentage({\n          containerSize: containerSize,\n          offset: offset,\n          totalSize: safeTotalSize\n        });\n\n        return Math.round(offsetPercentage * (totalSize - containerSize));\n      }\n    }\n  }]);\n\n  return ScalingCellSizeAndPositionManager;\n}();\n\nexport { ScalingCellSizeAndPositionManager as default };\nimport { bpfrpt_proptype_Alignment } from \"../types\";\nimport { bpfrpt_proptype_CellSizeGetter } from \"../types\";\nimport { bpfrpt_proptype_VisibleCellRange } from \"../types\";", "/**\n * Helper utility that updates the specified callback whenever any of the specified indices have changed.\n */\nexport default function createCallbackMemoizer() {\n  var requireAllKeys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  var cachedIndices = {};\n  return function (_ref) {\n    var callback = _ref.callback,\n        indices = _ref.indices;\n    var keys = Object.keys(indices);\n    var allInitialized = !requireAllKeys || keys.every(function (key) {\n      var value = indices[key];\n      return Array.isArray(value) ? value.length > 0 : value >= 0;\n    });\n    var indexChanged = keys.length !== Object.keys(cachedIndices).length || keys.some(function (key) {\n      var cachedValue = cachedIndices[key];\n      var value = indices[key];\n      return Array.isArray(value) ? cachedValue.join(',') !== value.join(',') : cachedValue !== value;\n    });\n    cachedIndices = indices;\n\n    if (allInitialized && indexChanged) {\n      callback(indices);\n    }\n  };\n}", "import ScalingCellSizeAndPositionManager from './ScalingCellSizeAndPositionManager.js';\n/**\n * Helper function that determines when to update scroll offsets to ensure that a scroll-to-index remains visible.\n * This function also ensures that the scroll ofset isn't past the last column/row of cells.\n */\n\nexport default function updateScrollIndexHelper(_ref) {\n  var cellSize = _ref.cellSize,\n      cellSizeAndPositionManager = _ref.cellSizeAndPositionManager,\n      previousCellsCount = _ref.previousCellsCount,\n      previousCellSize = _ref.previousCellSize,\n      previousScrollToAlignment = _ref.previousScrollToAlignment,\n      previousScrollToIndex = _ref.previousScrollToIndex,\n      previousSize = _ref.previousSize,\n      scrollOffset = _ref.scrollOffset,\n      scrollToAlignment = _ref.scrollToAlignment,\n      scrollToIndex = _ref.scrollToIndex,\n      size = _ref.size,\n      sizeJustIncreasedFromZero = _ref.sizeJustIncreasedFromZero,\n      updateScrollIndexCallback = _ref.updateScrollIndexCallback;\n  var cellCount = cellSizeAndPositionManager.getCellCount();\n  var hasScrollToIndex = scrollToIndex >= 0 && scrollToIndex < cellCount;\n  var sizeHasChanged = size !== previousSize || sizeJustIncreasedFromZero || !previousCellSize || typeof cellSize === 'number' && cellSize !== previousCellSize; // If we have a new scroll target OR if height/row-height has changed,\n  // We should ensure that the scroll target is visible.\n\n  if (hasScrollToIndex && (sizeHasChanged || scrollToAlignment !== previousScrollToAlignment || scrollToIndex !== previousScrollToIndex)) {\n    updateScrollIndexCallback(scrollToIndex); // If we don't have a selected item but list size or number of children have decreased,\n    // Make sure we aren't scrolled too far past the current content.\n  } else if (!hasScrollToIndex && cellCount > 0 && (size < previousSize || cellCount < previousCellsCount)) {\n    // We need to ensure that the current scroll offset is still within the collection's range.\n    // To do this, we don't need to measure everything; CellMeasurer would perform poorly.\n    // Just check to make sure we're still okay.\n    // Only adjust the scroll position if we've scrolled below the last set of rows.\n    if (scrollOffset > cellSizeAndPositionManager.getTotalSize() - size) {\n      updateScrollIndexCallback(cellCount - 1);\n    }\n  }\n}\nimport { bpfrpt_proptype_Alignment } from \"../types\";\nimport { bpfrpt_proptype_CellSize } from \"../types\";", "// Properly handle server-side rendering.\nvar win;\n\nif (typeof window !== 'undefined') {\n  win = window;\n} else if (typeof self !== 'undefined') {\n  win = self;\n} else {\n  win = {};\n} // requestAnimationFrame() shim by <PERSON> Irish\n// http://paulirish.com/2011/requestanimationframe-for-smart-animating/\n\n\nvar request = win.requestAnimationFrame || win.webkitRequestAnimationFrame || win.mozRequestAnimationFrame || win.oRequestAnimationFrame || win.msRequestAnimationFrame || function (callback) {\n  return win.setTimeout(callback, 1000 / 60);\n};\n\nvar cancel = win.cancelAnimationFrame || win.webkitCancelAnimationFrame || win.mozCancelAnimationFrame || win.oCancelAnimationFrame || win.msCancelAnimationFrame || function (id) {\n  win.clearTimeout(id);\n};\n\nexport var raf = request;\nexport var caf = cancel;", "import _extends from \"@babel/runtime/helpers/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\n\nvar _class, _temp;\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport calculateSizeAndPositionDataAndUpdateScrollOffset from './utils/calculateSizeAndPositionDataAndUpdateScrollOffset';\nimport ScalingCellSizeAndPositionManager from './utils/ScalingCellSizeAndPositionManager';\nimport createCallbackMemoizer from '../utils/createCallbackMemoizer';\nimport defaultOverscanIndicesGetter, { SCROLL_DIRECTION_BACKWARD, SCROLL_DIRECTION_FORWARD } from './defaultOverscanIndicesGetter';\nimport updateScrollIndexHelper from './utils/updateScrollIndexHelper';\nimport defaultCellRangeRenderer from './defaultCellRangeRenderer';\nimport scrollbarSize from 'dom-helpers/scrollbarSize';\nimport { polyfill } from 'react-lifecycles-compat';\nimport { requestAnimationTimeout, cancelAnimationTimeout } from '../utils/requestAnimationTimeout';\n/**\n * Specifies the number of milliseconds during which to disable pointer events while a scroll is in progress.\n * This improves performance and makes scrolling smoother.\n */\n\nexport var DEFAULT_SCROLLING_RESET_TIME_INTERVAL = 150;\n/**\n * Controls whether the Grid updates the DOM element's scrollLeft/scrollTop based on the current state or just observes it.\n * This prevents Grid from interrupting mouse-wheel animations (see issue #2).\n */\n\nvar SCROLL_POSITION_CHANGE_REASONS = {\n  OBSERVED: 'observed',\n  REQUESTED: 'requested'\n};\n\nvar renderNull = function renderNull() {\n  return null;\n};\n\n/**\n * Renders tabular data with virtualization along the vertical and horizontal axes.\n * Row heights and column widths must be known ahead of time and specified as properties.\n */\nvar Grid = (_temp = _class =\n/*#__PURE__*/\nfunction (_React$PureComponent) {\n  _inherits(Grid, _React$PureComponent);\n\n  // Invokes onSectionRendered callback only when start/stop row or column indices change\n  function Grid(props) {\n    var _this;\n\n    _classCallCheck(this, Grid);\n\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(Grid).call(this, props));\n\n    _defineProperty(_assertThisInitialized(_this), \"_onGridRenderedMemoizer\", createCallbackMemoizer());\n\n    _defineProperty(_assertThisInitialized(_this), \"_onScrollMemoizer\", createCallbackMemoizer(false));\n\n    _defineProperty(_assertThisInitialized(_this), \"_deferredInvalidateColumnIndex\", null);\n\n    _defineProperty(_assertThisInitialized(_this), \"_deferredInvalidateRowIndex\", null);\n\n    _defineProperty(_assertThisInitialized(_this), \"_recomputeScrollLeftFlag\", false);\n\n    _defineProperty(_assertThisInitialized(_this), \"_recomputeScrollTopFlag\", false);\n\n    _defineProperty(_assertThisInitialized(_this), \"_horizontalScrollBarSize\", 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_verticalScrollBarSize\", 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_scrollbarPresenceChanged\", false);\n\n    _defineProperty(_assertThisInitialized(_this), \"_scrollingContainer\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_childrenToDisplay\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_columnStartIndex\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_columnStopIndex\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_rowStartIndex\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_rowStopIndex\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_renderedColumnStartIndex\", 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_renderedColumnStopIndex\", 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_renderedRowStartIndex\", 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_renderedRowStopIndex\", 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_initialScrollTop\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_initialScrollLeft\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_disablePointerEventsTimeoutId\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_styleCache\", {});\n\n    _defineProperty(_assertThisInitialized(_this), \"_cellCache\", {});\n\n    _defineProperty(_assertThisInitialized(_this), \"_debounceScrollEndedCallback\", function () {\n      _this._disablePointerEventsTimeoutId = null; // isScrolling is used to determine if we reset styleCache\n\n      _this.setState({\n        isScrolling: false,\n        needToResetStyleCache: false\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_invokeOnGridRenderedHelper\", function () {\n      var onSectionRendered = _this.props.onSectionRendered;\n\n      _this._onGridRenderedMemoizer({\n        callback: onSectionRendered,\n        indices: {\n          columnOverscanStartIndex: _this._columnStartIndex,\n          columnOverscanStopIndex: _this._columnStopIndex,\n          columnStartIndex: _this._renderedColumnStartIndex,\n          columnStopIndex: _this._renderedColumnStopIndex,\n          rowOverscanStartIndex: _this._rowStartIndex,\n          rowOverscanStopIndex: _this._rowStopIndex,\n          rowStartIndex: _this._renderedRowStartIndex,\n          rowStopIndex: _this._renderedRowStopIndex\n        }\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_setScrollingContainerRef\", function (ref) {\n      _this._scrollingContainer = ref;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_onScroll\", function (event) {\n      // In certain edge-cases React dispatches an onScroll event with an invalid target.scrollLeft / target.scrollTop.\n      // This invalid event can be detected by comparing event.target to this component's scrollable DOM element.\n      // See issue #404 for more information.\n      if (event.target === _this._scrollingContainer) {\n        _this.handleScrollEvent(event.target);\n      }\n    });\n\n    var columnSizeAndPositionManager = new ScalingCellSizeAndPositionManager({\n      cellCount: props.columnCount,\n      cellSizeGetter: function cellSizeGetter(params) {\n        return Grid._wrapSizeGetter(props.columnWidth)(params);\n      },\n      estimatedCellSize: Grid._getEstimatedColumnSize(props)\n    });\n    var rowSizeAndPositionManager = new ScalingCellSizeAndPositionManager({\n      cellCount: props.rowCount,\n      cellSizeGetter: function cellSizeGetter(params) {\n        return Grid._wrapSizeGetter(props.rowHeight)(params);\n      },\n      estimatedCellSize: Grid._getEstimatedRowSize(props)\n    });\n    _this.state = {\n      instanceProps: {\n        columnSizeAndPositionManager: columnSizeAndPositionManager,\n        rowSizeAndPositionManager: rowSizeAndPositionManager,\n        prevColumnWidth: props.columnWidth,\n        prevRowHeight: props.rowHeight,\n        prevColumnCount: props.columnCount,\n        prevRowCount: props.rowCount,\n        prevIsScrolling: props.isScrolling === true,\n        prevScrollToColumn: props.scrollToColumn,\n        prevScrollToRow: props.scrollToRow,\n        scrollbarSize: 0,\n        scrollbarSizeMeasured: false\n      },\n      isScrolling: false,\n      scrollDirectionHorizontal: SCROLL_DIRECTION_FORWARD,\n      scrollDirectionVertical: SCROLL_DIRECTION_FORWARD,\n      scrollLeft: 0,\n      scrollTop: 0,\n      scrollPositionChangeReason: null,\n      needToResetStyleCache: false\n    };\n\n    if (props.scrollToRow > 0) {\n      _this._initialScrollTop = _this._getCalculatedScrollTop(props, _this.state);\n    }\n\n    if (props.scrollToColumn > 0) {\n      _this._initialScrollLeft = _this._getCalculatedScrollLeft(props, _this.state);\n    }\n\n    return _this;\n  }\n  /**\n   * Gets offsets for a given cell and alignment.\n   */\n\n\n  _createClass(Grid, [{\n    key: \"getOffsetForCell\",\n    value: function getOffsetForCell() {\n      var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref$alignment = _ref.alignment,\n          alignment = _ref$alignment === void 0 ? this.props.scrollToAlignment : _ref$alignment,\n          _ref$columnIndex = _ref.columnIndex,\n          columnIndex = _ref$columnIndex === void 0 ? this.props.scrollToColumn : _ref$columnIndex,\n          _ref$rowIndex = _ref.rowIndex,\n          rowIndex = _ref$rowIndex === void 0 ? this.props.scrollToRow : _ref$rowIndex;\n\n      var offsetProps = _objectSpread({}, this.props, {\n        scrollToAlignment: alignment,\n        scrollToColumn: columnIndex,\n        scrollToRow: rowIndex\n      });\n\n      return {\n        scrollLeft: this._getCalculatedScrollLeft(offsetProps),\n        scrollTop: this._getCalculatedScrollTop(offsetProps)\n      };\n    }\n    /**\n     * Gets estimated total rows' height.\n     */\n\n  }, {\n    key: \"getTotalRowsHeight\",\n    value: function getTotalRowsHeight() {\n      return this.state.instanceProps.rowSizeAndPositionManager.getTotalSize();\n    }\n    /**\n     * Gets estimated total columns' width.\n     */\n\n  }, {\n    key: \"getTotalColumnsWidth\",\n    value: function getTotalColumnsWidth() {\n      return this.state.instanceProps.columnSizeAndPositionManager.getTotalSize();\n    }\n    /**\n     * This method handles a scroll event originating from an external scroll control.\n     * It's an advanced method and should probably not be used unless you're implementing a custom scroll-bar solution.\n     */\n\n  }, {\n    key: \"handleScrollEvent\",\n    value: function handleScrollEvent(_ref2) {\n      var _ref2$scrollLeft = _ref2.scrollLeft,\n          scrollLeftParam = _ref2$scrollLeft === void 0 ? 0 : _ref2$scrollLeft,\n          _ref2$scrollTop = _ref2.scrollTop,\n          scrollTopParam = _ref2$scrollTop === void 0 ? 0 : _ref2$scrollTop;\n\n      // On iOS, we can arrive at negative offsets by swiping past the start.\n      // To prevent flicker here, we make playing in the negative offset zone cause nothing to happen.\n      if (scrollTopParam < 0) {\n        return;\n      } // Prevent pointer events from interrupting a smooth scroll\n\n\n      this._debounceScrollEnded();\n\n      var _this$props = this.props,\n          autoHeight = _this$props.autoHeight,\n          autoWidth = _this$props.autoWidth,\n          height = _this$props.height,\n          width = _this$props.width;\n      var instanceProps = this.state.instanceProps; // When this component is shrunk drastically, React dispatches a series of back-to-back scroll events,\n      // Gradually converging on a scrollTop that is within the bounds of the new, smaller height.\n      // This causes a series of rapid renders that is slow for long lists.\n      // We can avoid that by doing some simple bounds checking to ensure that scroll offsets never exceed their bounds.\n\n      var scrollbarSize = instanceProps.scrollbarSize;\n      var totalRowsHeight = instanceProps.rowSizeAndPositionManager.getTotalSize();\n      var totalColumnsWidth = instanceProps.columnSizeAndPositionManager.getTotalSize();\n      var scrollLeft = Math.min(Math.max(0, totalColumnsWidth - width + scrollbarSize), scrollLeftParam);\n      var scrollTop = Math.min(Math.max(0, totalRowsHeight - height + scrollbarSize), scrollTopParam); // Certain devices (like Apple touchpad) rapid-fire duplicate events.\n      // Don't force a re-render if this is the case.\n      // The mouse may move faster then the animation frame does.\n      // Use requestAnimationFrame to avoid over-updating.\n\n      if (this.state.scrollLeft !== scrollLeft || this.state.scrollTop !== scrollTop) {\n        // Track scrolling direction so we can more efficiently overscan rows to reduce empty space around the edges while scrolling.\n        // Don't change direction for an axis unless scroll offset has changed.\n        var scrollDirectionHorizontal = scrollLeft !== this.state.scrollLeft ? scrollLeft > this.state.scrollLeft ? SCROLL_DIRECTION_FORWARD : SCROLL_DIRECTION_BACKWARD : this.state.scrollDirectionHorizontal;\n        var scrollDirectionVertical = scrollTop !== this.state.scrollTop ? scrollTop > this.state.scrollTop ? SCROLL_DIRECTION_FORWARD : SCROLL_DIRECTION_BACKWARD : this.state.scrollDirectionVertical;\n        var newState = {\n          isScrolling: true,\n          scrollDirectionHorizontal: scrollDirectionHorizontal,\n          scrollDirectionVertical: scrollDirectionVertical,\n          scrollPositionChangeReason: SCROLL_POSITION_CHANGE_REASONS.OBSERVED\n        };\n\n        if (!autoHeight) {\n          newState.scrollTop = scrollTop;\n        }\n\n        if (!autoWidth) {\n          newState.scrollLeft = scrollLeft;\n        }\n\n        newState.needToResetStyleCache = false;\n        this.setState(newState);\n      }\n\n      this._invokeOnScrollMemoizer({\n        scrollLeft: scrollLeft,\n        scrollTop: scrollTop,\n        totalColumnsWidth: totalColumnsWidth,\n        totalRowsHeight: totalRowsHeight\n      });\n    }\n    /**\n     * Invalidate Grid size and recompute visible cells.\n     * This is a deferred wrapper for recomputeGridSize().\n     * It sets a flag to be evaluated on cDM/cDU to avoid unnecessary renders.\n     * This method is intended for advanced use-cases like CellMeasurer.\n     */\n    // @TODO (bvaughn) Add automated test coverage for this.\n\n  }, {\n    key: \"invalidateCellSizeAfterRender\",\n    value: function invalidateCellSizeAfterRender(_ref3) {\n      var columnIndex = _ref3.columnIndex,\n          rowIndex = _ref3.rowIndex;\n      this._deferredInvalidateColumnIndex = typeof this._deferredInvalidateColumnIndex === 'number' ? Math.min(this._deferredInvalidateColumnIndex, columnIndex) : columnIndex;\n      this._deferredInvalidateRowIndex = typeof this._deferredInvalidateRowIndex === 'number' ? Math.min(this._deferredInvalidateRowIndex, rowIndex) : rowIndex;\n    }\n    /**\n     * Pre-measure all columns and rows in a Grid.\n     * Typically cells are only measured as needed and estimated sizes are used for cells that have not yet been measured.\n     * This method ensures that the next call to getTotalSize() returns an exact size (as opposed to just an estimated one).\n     */\n\n  }, {\n    key: \"measureAllCells\",\n    value: function measureAllCells() {\n      var _this$props2 = this.props,\n          columnCount = _this$props2.columnCount,\n          rowCount = _this$props2.rowCount;\n      var instanceProps = this.state.instanceProps;\n      instanceProps.columnSizeAndPositionManager.getSizeAndPositionOfCell(columnCount - 1);\n      instanceProps.rowSizeAndPositionManager.getSizeAndPositionOfCell(rowCount - 1);\n    }\n    /**\n     * Forced recompute of row heights and column widths.\n     * This function should be called if dynamic column or row sizes have changed but nothing else has.\n     * Since Grid only receives :columnCount and :rowCount it has no way of detecting when the underlying data changes.\n     */\n\n  }, {\n    key: \"recomputeGridSize\",\n    value: function recomputeGridSize() {\n      var _ref4 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref4$columnIndex = _ref4.columnIndex,\n          columnIndex = _ref4$columnIndex === void 0 ? 0 : _ref4$columnIndex,\n          _ref4$rowIndex = _ref4.rowIndex,\n          rowIndex = _ref4$rowIndex === void 0 ? 0 : _ref4$rowIndex;\n\n      var _this$props3 = this.props,\n          scrollToColumn = _this$props3.scrollToColumn,\n          scrollToRow = _this$props3.scrollToRow;\n      var instanceProps = this.state.instanceProps;\n      instanceProps.columnSizeAndPositionManager.resetCell(columnIndex);\n      instanceProps.rowSizeAndPositionManager.resetCell(rowIndex); // Cell sizes may be determined by a function property.\n      // In this case the cDU handler can't know if they changed.\n      // Store this flag to let the next cDU pass know it needs to recompute the scroll offset.\n\n      this._recomputeScrollLeftFlag = scrollToColumn >= 0 && (this.state.scrollDirectionHorizontal === SCROLL_DIRECTION_FORWARD ? columnIndex <= scrollToColumn : columnIndex >= scrollToColumn);\n      this._recomputeScrollTopFlag = scrollToRow >= 0 && (this.state.scrollDirectionVertical === SCROLL_DIRECTION_FORWARD ? rowIndex <= scrollToRow : rowIndex >= scrollToRow); // Clear cell cache in case we are scrolling;\n      // Invalid row heights likely mean invalid cached content as well.\n\n      this._styleCache = {};\n      this._cellCache = {};\n      this.forceUpdate();\n    }\n    /**\n     * Ensure column and row are visible.\n     */\n\n  }, {\n    key: \"scrollToCell\",\n    value: function scrollToCell(_ref5) {\n      var columnIndex = _ref5.columnIndex,\n          rowIndex = _ref5.rowIndex;\n      var columnCount = this.props.columnCount;\n      var props = this.props; // Don't adjust scroll offset for single-column grids (eg List, Table).\n      // This can cause a funky scroll offset because of the vertical scrollbar width.\n\n      if (columnCount > 1 && columnIndex !== undefined) {\n        this._updateScrollLeftForScrollToColumn(_objectSpread({}, props, {\n          scrollToColumn: columnIndex\n        }));\n      }\n\n      if (rowIndex !== undefined) {\n        this._updateScrollTopForScrollToRow(_objectSpread({}, props, {\n          scrollToRow: rowIndex\n        }));\n      }\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this$props4 = this.props,\n          getScrollbarSize = _this$props4.getScrollbarSize,\n          height = _this$props4.height,\n          scrollLeft = _this$props4.scrollLeft,\n          scrollToColumn = _this$props4.scrollToColumn,\n          scrollTop = _this$props4.scrollTop,\n          scrollToRow = _this$props4.scrollToRow,\n          width = _this$props4.width;\n      var instanceProps = this.state.instanceProps; // Reset initial offsets to be ignored in browser\n\n      this._initialScrollTop = 0;\n      this._initialScrollLeft = 0; // If cell sizes have been invalidated (eg we are using CellMeasurer) then reset cached positions.\n      // We must do this at the start of the method as we may calculate and update scroll position below.\n\n      this._handleInvalidatedGridSize(); // If this component was first rendered server-side, scrollbar size will be undefined.\n      // In that event we need to remeasure.\n\n\n      if (!instanceProps.scrollbarSizeMeasured) {\n        this.setState(function (prevState) {\n          var stateUpdate = _objectSpread({}, prevState, {\n            needToResetStyleCache: false\n          });\n\n          stateUpdate.instanceProps.scrollbarSize = getScrollbarSize();\n          stateUpdate.instanceProps.scrollbarSizeMeasured = true;\n          return stateUpdate;\n        });\n      }\n\n      if (typeof scrollLeft === 'number' && scrollLeft >= 0 || typeof scrollTop === 'number' && scrollTop >= 0) {\n        var stateUpdate = Grid._getScrollToPositionStateUpdate({\n          prevState: this.state,\n          scrollLeft: scrollLeft,\n          scrollTop: scrollTop\n        });\n\n        if (stateUpdate) {\n          stateUpdate.needToResetStyleCache = false;\n          this.setState(stateUpdate);\n        }\n      } // refs don't work in `react-test-renderer`\n\n\n      if (this._scrollingContainer) {\n        // setting the ref's scrollLeft and scrollTop.\n        // Somehow in MultiGrid the main grid doesn't trigger a update on mount.\n        if (this._scrollingContainer.scrollLeft !== this.state.scrollLeft) {\n          this._scrollingContainer.scrollLeft = this.state.scrollLeft;\n        }\n\n        if (this._scrollingContainer.scrollTop !== this.state.scrollTop) {\n          this._scrollingContainer.scrollTop = this.state.scrollTop;\n        }\n      } // Don't update scroll offset if the size is 0; we don't render any cells in this case.\n      // Setting a state may cause us to later thing we've updated the offce when we haven't.\n\n\n      var sizeIsBiggerThanZero = height > 0 && width > 0;\n\n      if (scrollToColumn >= 0 && sizeIsBiggerThanZero) {\n        this._updateScrollLeftForScrollToColumn();\n      }\n\n      if (scrollToRow >= 0 && sizeIsBiggerThanZero) {\n        this._updateScrollTopForScrollToRow();\n      } // Update onRowsRendered callback\n\n\n      this._invokeOnGridRenderedHelper(); // Initialize onScroll callback\n\n\n      this._invokeOnScrollMemoizer({\n        scrollLeft: scrollLeft || 0,\n        scrollTop: scrollTop || 0,\n        totalColumnsWidth: instanceProps.columnSizeAndPositionManager.getTotalSize(),\n        totalRowsHeight: instanceProps.rowSizeAndPositionManager.getTotalSize()\n      });\n\n      this._maybeCallOnScrollbarPresenceChange();\n    }\n    /**\n     * @private\n     * This method updates scrollLeft/scrollTop in state for the following conditions:\n     * 1) New scroll-to-cell props have been set\n     */\n\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps, prevState) {\n      var _this2 = this;\n\n      var _this$props5 = this.props,\n          autoHeight = _this$props5.autoHeight,\n          autoWidth = _this$props5.autoWidth,\n          columnCount = _this$props5.columnCount,\n          height = _this$props5.height,\n          rowCount = _this$props5.rowCount,\n          scrollToAlignment = _this$props5.scrollToAlignment,\n          scrollToColumn = _this$props5.scrollToColumn,\n          scrollToRow = _this$props5.scrollToRow,\n          width = _this$props5.width;\n      var _this$state = this.state,\n          scrollLeft = _this$state.scrollLeft,\n          scrollPositionChangeReason = _this$state.scrollPositionChangeReason,\n          scrollTop = _this$state.scrollTop,\n          instanceProps = _this$state.instanceProps; // If cell sizes have been invalidated (eg we are using CellMeasurer) then reset cached positions.\n      // We must do this at the start of the method as we may calculate and update scroll position below.\n\n      this._handleInvalidatedGridSize(); // Handle edge case where column or row count has only just increased over 0.\n      // In this case we may have to restore a previously-specified scroll offset.\n      // For more info see bvaughn/react-virtualized/issues/218\n\n\n      var columnOrRowCountJustIncreasedFromZero = columnCount > 0 && prevProps.columnCount === 0 || rowCount > 0 && prevProps.rowCount === 0; // Make sure requested changes to :scrollLeft or :scrollTop get applied.\n      // Assigning to scrollLeft/scrollTop tells the browser to interrupt any running scroll animations,\n      // And to discard any pending async changes to the scroll position that may have happened in the meantime (e.g. on a separate scrolling thread).\n      // So we only set these when we require an adjustment of the scroll position.\n      // See issue #2 for more information.\n\n      if (scrollPositionChangeReason === SCROLL_POSITION_CHANGE_REASONS.REQUESTED) {\n        // @TRICKY :autoHeight and :autoWidth properties instructs Grid to leave :scrollTop and :scrollLeft management to an external HOC (eg WindowScroller).\n        // In this case we should avoid checking scrollingContainer.scrollTop and scrollingContainer.scrollLeft since it forces layout/flow.\n        if (!autoWidth && scrollLeft >= 0 && (scrollLeft !== this._scrollingContainer.scrollLeft || columnOrRowCountJustIncreasedFromZero)) {\n          this._scrollingContainer.scrollLeft = scrollLeft;\n        }\n\n        if (!autoHeight && scrollTop >= 0 && (scrollTop !== this._scrollingContainer.scrollTop || columnOrRowCountJustIncreasedFromZero)) {\n          this._scrollingContainer.scrollTop = scrollTop;\n        }\n      } // Special case where the previous size was 0:\n      // In this case we don't show any windowed cells at all.\n      // So we should always recalculate offset afterwards.\n\n\n      var sizeJustIncreasedFromZero = (prevProps.width === 0 || prevProps.height === 0) && height > 0 && width > 0; // Update scroll offsets if the current :scrollToColumn or :scrollToRow values requires it\n      // @TODO Do we also need this check or can the one in componentWillUpdate() suffice?\n\n      if (this._recomputeScrollLeftFlag) {\n        this._recomputeScrollLeftFlag = false;\n\n        this._updateScrollLeftForScrollToColumn(this.props);\n      } else {\n        updateScrollIndexHelper({\n          cellSizeAndPositionManager: instanceProps.columnSizeAndPositionManager,\n          previousCellsCount: prevProps.columnCount,\n          previousCellSize: prevProps.columnWidth,\n          previousScrollToAlignment: prevProps.scrollToAlignment,\n          previousScrollToIndex: prevProps.scrollToColumn,\n          previousSize: prevProps.width,\n          scrollOffset: scrollLeft,\n          scrollToAlignment: scrollToAlignment,\n          scrollToIndex: scrollToColumn,\n          size: width,\n          sizeJustIncreasedFromZero: sizeJustIncreasedFromZero,\n          updateScrollIndexCallback: function updateScrollIndexCallback() {\n            return _this2._updateScrollLeftForScrollToColumn(_this2.props);\n          }\n        });\n      }\n\n      if (this._recomputeScrollTopFlag) {\n        this._recomputeScrollTopFlag = false;\n\n        this._updateScrollTopForScrollToRow(this.props);\n      } else {\n        updateScrollIndexHelper({\n          cellSizeAndPositionManager: instanceProps.rowSizeAndPositionManager,\n          previousCellsCount: prevProps.rowCount,\n          previousCellSize: prevProps.rowHeight,\n          previousScrollToAlignment: prevProps.scrollToAlignment,\n          previousScrollToIndex: prevProps.scrollToRow,\n          previousSize: prevProps.height,\n          scrollOffset: scrollTop,\n          scrollToAlignment: scrollToAlignment,\n          scrollToIndex: scrollToRow,\n          size: height,\n          sizeJustIncreasedFromZero: sizeJustIncreasedFromZero,\n          updateScrollIndexCallback: function updateScrollIndexCallback() {\n            return _this2._updateScrollTopForScrollToRow(_this2.props);\n          }\n        });\n      } // Update onRowsRendered callback if start/stop indices have changed\n\n\n      this._invokeOnGridRenderedHelper(); // Changes to :scrollLeft or :scrollTop should also notify :onScroll listeners\n\n\n      if (scrollLeft !== prevState.scrollLeft || scrollTop !== prevState.scrollTop) {\n        var totalRowsHeight = instanceProps.rowSizeAndPositionManager.getTotalSize();\n        var totalColumnsWidth = instanceProps.columnSizeAndPositionManager.getTotalSize();\n\n        this._invokeOnScrollMemoizer({\n          scrollLeft: scrollLeft,\n          scrollTop: scrollTop,\n          totalColumnsWidth: totalColumnsWidth,\n          totalRowsHeight: totalRowsHeight\n        });\n      }\n\n      this._maybeCallOnScrollbarPresenceChange();\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      if (this._disablePointerEventsTimeoutId) {\n        cancelAnimationTimeout(this._disablePointerEventsTimeoutId);\n      }\n    }\n    /**\n     * This method updates scrollLeft/scrollTop in state for the following conditions:\n     * 1) Empty content (0 rows or columns)\n     * 2) New scroll props overriding the current state\n     * 3) Cells-count or cells-size has changed, making previous scroll offsets invalid\n     */\n\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props6 = this.props,\n          autoContainerWidth = _this$props6.autoContainerWidth,\n          autoHeight = _this$props6.autoHeight,\n          autoWidth = _this$props6.autoWidth,\n          className = _this$props6.className,\n          containerProps = _this$props6.containerProps,\n          containerRole = _this$props6.containerRole,\n          containerStyle = _this$props6.containerStyle,\n          height = _this$props6.height,\n          id = _this$props6.id,\n          noContentRenderer = _this$props6.noContentRenderer,\n          role = _this$props6.role,\n          style = _this$props6.style,\n          tabIndex = _this$props6.tabIndex,\n          width = _this$props6.width;\n      var _this$state2 = this.state,\n          instanceProps = _this$state2.instanceProps,\n          needToResetStyleCache = _this$state2.needToResetStyleCache;\n\n      var isScrolling = this._isScrolling();\n\n      var gridStyle = {\n        boxSizing: 'border-box',\n        direction: 'ltr',\n        height: autoHeight ? 'auto' : height,\n        position: 'relative',\n        width: autoWidth ? 'auto' : width,\n        WebkitOverflowScrolling: 'touch',\n        willChange: 'transform'\n      };\n\n      if (needToResetStyleCache) {\n        this._styleCache = {};\n      } // calculate _styleCache here\n      // if state.isScrolling (not from _isScrolling) then reset\n\n\n      if (!this.state.isScrolling) {\n        this._resetStyleCache();\n      } // calculate children to render here\n\n\n      this._calculateChildrenToRender(this.props, this.state);\n\n      var totalColumnsWidth = instanceProps.columnSizeAndPositionManager.getTotalSize();\n      var totalRowsHeight = instanceProps.rowSizeAndPositionManager.getTotalSize(); // Force browser to hide scrollbars when we know they aren't necessary.\n      // Otherwise once scrollbars appear they may not disappear again.\n      // For more info see issue #116\n\n      var verticalScrollBarSize = totalRowsHeight > height ? instanceProps.scrollbarSize : 0;\n      var horizontalScrollBarSize = totalColumnsWidth > width ? instanceProps.scrollbarSize : 0;\n\n      if (horizontalScrollBarSize !== this._horizontalScrollBarSize || verticalScrollBarSize !== this._verticalScrollBarSize) {\n        this._horizontalScrollBarSize = horizontalScrollBarSize;\n        this._verticalScrollBarSize = verticalScrollBarSize;\n        this._scrollbarPresenceChanged = true;\n      } // Also explicitly init styles to 'auto' if scrollbars are required.\n      // This works around an obscure edge case where external CSS styles have not yet been loaded,\n      // But an initial scroll index of offset is set as an external prop.\n      // Without this style, Grid would render the correct range of cells but would NOT update its internal offset.\n      // This was originally reported via clauderic/react-infinite-calendar/issues/23\n\n\n      gridStyle.overflowX = totalColumnsWidth + verticalScrollBarSize <= width ? 'hidden' : 'auto';\n      gridStyle.overflowY = totalRowsHeight + horizontalScrollBarSize <= height ? 'hidden' : 'auto';\n      var childrenToDisplay = this._childrenToDisplay;\n      var showNoContentRenderer = childrenToDisplay.length === 0 && height > 0 && width > 0;\n      return React.createElement(\"div\", _extends({\n        ref: this._setScrollingContainerRef\n      }, containerProps, {\n        \"aria-label\": this.props['aria-label'],\n        \"aria-readonly\": this.props['aria-readonly'],\n        className: clsx('ReactVirtualized__Grid', className),\n        id: id,\n        onScroll: this._onScroll,\n        role: role,\n        style: _objectSpread({}, gridStyle, {}, style),\n        tabIndex: tabIndex\n      }), childrenToDisplay.length > 0 && React.createElement(\"div\", {\n        className: \"ReactVirtualized__Grid__innerScrollContainer\",\n        role: containerRole,\n        style: _objectSpread({\n          width: autoContainerWidth ? 'auto' : totalColumnsWidth,\n          height: totalRowsHeight,\n          maxWidth: totalColumnsWidth,\n          maxHeight: totalRowsHeight,\n          overflow: 'hidden',\n          pointerEvents: isScrolling ? 'none' : '',\n          position: 'relative'\n        }, containerStyle)\n      }, childrenToDisplay), showNoContentRenderer && noContentRenderer());\n    }\n    /* ---------------------------- Helper methods ---------------------------- */\n\n  }, {\n    key: \"_calculateChildrenToRender\",\n    value: function _calculateChildrenToRender() {\n      var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.props;\n      var state = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.state;\n      var cellRenderer = props.cellRenderer,\n          cellRangeRenderer = props.cellRangeRenderer,\n          columnCount = props.columnCount,\n          deferredMeasurementCache = props.deferredMeasurementCache,\n          height = props.height,\n          overscanColumnCount = props.overscanColumnCount,\n          overscanIndicesGetter = props.overscanIndicesGetter,\n          overscanRowCount = props.overscanRowCount,\n          rowCount = props.rowCount,\n          width = props.width,\n          isScrollingOptOut = props.isScrollingOptOut;\n      var scrollDirectionHorizontal = state.scrollDirectionHorizontal,\n          scrollDirectionVertical = state.scrollDirectionVertical,\n          instanceProps = state.instanceProps;\n      var scrollTop = this._initialScrollTop > 0 ? this._initialScrollTop : state.scrollTop;\n      var scrollLeft = this._initialScrollLeft > 0 ? this._initialScrollLeft : state.scrollLeft;\n\n      var isScrolling = this._isScrolling(props, state);\n\n      this._childrenToDisplay = []; // Render only enough columns and rows to cover the visible area of the grid.\n\n      if (height > 0 && width > 0) {\n        var visibleColumnIndices = instanceProps.columnSizeAndPositionManager.getVisibleCellRange({\n          containerSize: width,\n          offset: scrollLeft\n        });\n        var visibleRowIndices = instanceProps.rowSizeAndPositionManager.getVisibleCellRange({\n          containerSize: height,\n          offset: scrollTop\n        });\n        var horizontalOffsetAdjustment = instanceProps.columnSizeAndPositionManager.getOffsetAdjustment({\n          containerSize: width,\n          offset: scrollLeft\n        });\n        var verticalOffsetAdjustment = instanceProps.rowSizeAndPositionManager.getOffsetAdjustment({\n          containerSize: height,\n          offset: scrollTop\n        }); // Store for _invokeOnGridRenderedHelper()\n\n        this._renderedColumnStartIndex = visibleColumnIndices.start;\n        this._renderedColumnStopIndex = visibleColumnIndices.stop;\n        this._renderedRowStartIndex = visibleRowIndices.start;\n        this._renderedRowStopIndex = visibleRowIndices.stop;\n        var overscanColumnIndices = overscanIndicesGetter({\n          direction: 'horizontal',\n          cellCount: columnCount,\n          overscanCellsCount: overscanColumnCount,\n          scrollDirection: scrollDirectionHorizontal,\n          startIndex: typeof visibleColumnIndices.start === 'number' ? visibleColumnIndices.start : 0,\n          stopIndex: typeof visibleColumnIndices.stop === 'number' ? visibleColumnIndices.stop : -1\n        });\n        var overscanRowIndices = overscanIndicesGetter({\n          direction: 'vertical',\n          cellCount: rowCount,\n          overscanCellsCount: overscanRowCount,\n          scrollDirection: scrollDirectionVertical,\n          startIndex: typeof visibleRowIndices.start === 'number' ? visibleRowIndices.start : 0,\n          stopIndex: typeof visibleRowIndices.stop === 'number' ? visibleRowIndices.stop : -1\n        }); // Store for _invokeOnGridRenderedHelper()\n\n        var columnStartIndex = overscanColumnIndices.overscanStartIndex;\n        var columnStopIndex = overscanColumnIndices.overscanStopIndex;\n        var rowStartIndex = overscanRowIndices.overscanStartIndex;\n        var rowStopIndex = overscanRowIndices.overscanStopIndex; // Advanced use-cases (eg CellMeasurer) require batched measurements to determine accurate sizes.\n\n        if (deferredMeasurementCache) {\n          // If rows have a dynamic height, scan the rows we are about to render.\n          // If any have not yet been measured, then we need to render all columns initially,\n          // Because the height of the row is equal to the tallest cell within that row,\n          // (And so we can't know the height without measuring all column-cells first).\n          if (!deferredMeasurementCache.hasFixedHeight()) {\n            for (var rowIndex = rowStartIndex; rowIndex <= rowStopIndex; rowIndex++) {\n              if (!deferredMeasurementCache.has(rowIndex, 0)) {\n                columnStartIndex = 0;\n                columnStopIndex = columnCount - 1;\n                break;\n              }\n            }\n          } // If columns have a dynamic width, scan the columns we are about to render.\n          // If any have not yet been measured, then we need to render all rows initially,\n          // Because the width of the column is equal to the widest cell within that column,\n          // (And so we can't know the width without measuring all row-cells first).\n\n\n          if (!deferredMeasurementCache.hasFixedWidth()) {\n            for (var columnIndex = columnStartIndex; columnIndex <= columnStopIndex; columnIndex++) {\n              if (!deferredMeasurementCache.has(0, columnIndex)) {\n                rowStartIndex = 0;\n                rowStopIndex = rowCount - 1;\n                break;\n              }\n            }\n          }\n        }\n\n        this._childrenToDisplay = cellRangeRenderer({\n          cellCache: this._cellCache,\n          cellRenderer: cellRenderer,\n          columnSizeAndPositionManager: instanceProps.columnSizeAndPositionManager,\n          columnStartIndex: columnStartIndex,\n          columnStopIndex: columnStopIndex,\n          deferredMeasurementCache: deferredMeasurementCache,\n          horizontalOffsetAdjustment: horizontalOffsetAdjustment,\n          isScrolling: isScrolling,\n          isScrollingOptOut: isScrollingOptOut,\n          parent: this,\n          rowSizeAndPositionManager: instanceProps.rowSizeAndPositionManager,\n          rowStartIndex: rowStartIndex,\n          rowStopIndex: rowStopIndex,\n          scrollLeft: scrollLeft,\n          scrollTop: scrollTop,\n          styleCache: this._styleCache,\n          verticalOffsetAdjustment: verticalOffsetAdjustment,\n          visibleColumnIndices: visibleColumnIndices,\n          visibleRowIndices: visibleRowIndices\n        }); // update the indices\n\n        this._columnStartIndex = columnStartIndex;\n        this._columnStopIndex = columnStopIndex;\n        this._rowStartIndex = rowStartIndex;\n        this._rowStopIndex = rowStopIndex;\n      }\n    }\n    /**\n     * Sets an :isScrolling flag for a small window of time.\n     * This flag is used to disable pointer events on the scrollable portion of the Grid.\n     * This prevents jerky/stuttery mouse-wheel scrolling.\n     */\n\n  }, {\n    key: \"_debounceScrollEnded\",\n    value: function _debounceScrollEnded() {\n      var scrollingResetTimeInterval = this.props.scrollingResetTimeInterval;\n\n      if (this._disablePointerEventsTimeoutId) {\n        cancelAnimationTimeout(this._disablePointerEventsTimeoutId);\n      }\n\n      this._disablePointerEventsTimeoutId = requestAnimationTimeout(this._debounceScrollEndedCallback, scrollingResetTimeInterval);\n    }\n  }, {\n    key: \"_handleInvalidatedGridSize\",\n\n    /**\n     * Check for batched CellMeasurer size invalidations.\n     * This will occur the first time one or more previously unmeasured cells are rendered.\n     */\n    value: function _handleInvalidatedGridSize() {\n      if (typeof this._deferredInvalidateColumnIndex === 'number' && typeof this._deferredInvalidateRowIndex === 'number') {\n        var columnIndex = this._deferredInvalidateColumnIndex;\n        var rowIndex = this._deferredInvalidateRowIndex;\n        this._deferredInvalidateColumnIndex = null;\n        this._deferredInvalidateRowIndex = null;\n        this.recomputeGridSize({\n          columnIndex: columnIndex,\n          rowIndex: rowIndex\n        });\n      }\n    }\n  }, {\n    key: \"_invokeOnScrollMemoizer\",\n    value: function _invokeOnScrollMemoizer(_ref6) {\n      var _this3 = this;\n\n      var scrollLeft = _ref6.scrollLeft,\n          scrollTop = _ref6.scrollTop,\n          totalColumnsWidth = _ref6.totalColumnsWidth,\n          totalRowsHeight = _ref6.totalRowsHeight;\n\n      this._onScrollMemoizer({\n        callback: function callback(_ref7) {\n          var scrollLeft = _ref7.scrollLeft,\n              scrollTop = _ref7.scrollTop;\n          var _this3$props = _this3.props,\n              height = _this3$props.height,\n              onScroll = _this3$props.onScroll,\n              width = _this3$props.width;\n          onScroll({\n            clientHeight: height,\n            clientWidth: width,\n            scrollHeight: totalRowsHeight,\n            scrollLeft: scrollLeft,\n            scrollTop: scrollTop,\n            scrollWidth: totalColumnsWidth\n          });\n        },\n        indices: {\n          scrollLeft: scrollLeft,\n          scrollTop: scrollTop\n        }\n      });\n    }\n  }, {\n    key: \"_isScrolling\",\n    value: function _isScrolling() {\n      var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.props;\n      var state = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.state;\n      // If isScrolling is defined in props, use it to override the value in state\n      // This is a performance optimization for WindowScroller + Grid\n      return Object.hasOwnProperty.call(props, 'isScrolling') ? Boolean(props.isScrolling) : Boolean(state.isScrolling);\n    }\n  }, {\n    key: \"_maybeCallOnScrollbarPresenceChange\",\n    value: function _maybeCallOnScrollbarPresenceChange() {\n      if (this._scrollbarPresenceChanged) {\n        var onScrollbarPresenceChange = this.props.onScrollbarPresenceChange;\n        this._scrollbarPresenceChanged = false;\n        onScrollbarPresenceChange({\n          horizontal: this._horizontalScrollBarSize > 0,\n          size: this.state.instanceProps.scrollbarSize,\n          vertical: this._verticalScrollBarSize > 0\n        });\n      }\n    }\n  }, {\n    key: \"scrollToPosition\",\n\n    /**\n     * Scroll to the specified offset(s).\n     * Useful for animating position changes.\n     */\n    value: function scrollToPosition(_ref8) {\n      var scrollLeft = _ref8.scrollLeft,\n          scrollTop = _ref8.scrollTop;\n\n      var stateUpdate = Grid._getScrollToPositionStateUpdate({\n        prevState: this.state,\n        scrollLeft: scrollLeft,\n        scrollTop: scrollTop\n      });\n\n      if (stateUpdate) {\n        stateUpdate.needToResetStyleCache = false;\n        this.setState(stateUpdate);\n      }\n    }\n  }, {\n    key: \"_getCalculatedScrollLeft\",\n    value: function _getCalculatedScrollLeft() {\n      var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.props;\n      var state = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.state;\n      return Grid._getCalculatedScrollLeft(props, state);\n    }\n  }, {\n    key: \"_updateScrollLeftForScrollToColumn\",\n    value: function _updateScrollLeftForScrollToColumn() {\n      var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.props;\n      var state = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.state;\n\n      var stateUpdate = Grid._getScrollLeftForScrollToColumnStateUpdate(props, state);\n\n      if (stateUpdate) {\n        stateUpdate.needToResetStyleCache = false;\n        this.setState(stateUpdate);\n      }\n    }\n  }, {\n    key: \"_getCalculatedScrollTop\",\n    value: function _getCalculatedScrollTop() {\n      var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.props;\n      var state = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.state;\n      return Grid._getCalculatedScrollTop(props, state);\n    }\n  }, {\n    key: \"_resetStyleCache\",\n    value: function _resetStyleCache() {\n      var styleCache = this._styleCache;\n      var cellCache = this._cellCache;\n      var isScrollingOptOut = this.props.isScrollingOptOut; // Reset cell and style caches once scrolling stops.\n      // This makes Grid simpler to use (since cells commonly change).\n      // And it keeps the caches from growing too large.\n      // Performance is most sensitive when a user is scrolling.\n      // Don't clear visible cells from cellCache if isScrollingOptOut is specified.\n      // This keeps the cellCache to a resonable size.\n\n      this._cellCache = {};\n      this._styleCache = {}; // Copy over the visible cell styles so avoid unnecessary re-render.\n\n      for (var rowIndex = this._rowStartIndex; rowIndex <= this._rowStopIndex; rowIndex++) {\n        for (var columnIndex = this._columnStartIndex; columnIndex <= this._columnStopIndex; columnIndex++) {\n          var key = \"\".concat(rowIndex, \"-\").concat(columnIndex);\n          this._styleCache[key] = styleCache[key];\n\n          if (isScrollingOptOut) {\n            this._cellCache[key] = cellCache[key];\n          }\n        }\n      }\n    }\n  }, {\n    key: \"_updateScrollTopForScrollToRow\",\n    value: function _updateScrollTopForScrollToRow() {\n      var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.props;\n      var state = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.state;\n\n      var stateUpdate = Grid._getScrollTopForScrollToRowStateUpdate(props, state);\n\n      if (stateUpdate) {\n        stateUpdate.needToResetStyleCache = false;\n        this.setState(stateUpdate);\n      }\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      var newState = {};\n\n      if (nextProps.columnCount === 0 && prevState.scrollLeft !== 0 || nextProps.rowCount === 0 && prevState.scrollTop !== 0) {\n        newState.scrollLeft = 0;\n        newState.scrollTop = 0; // only use scroll{Left,Top} from props if scrollTo{Column,Row} isn't specified\n        // scrollTo{Column,Row} should override scroll{Left,Top}\n      } else if (nextProps.scrollLeft !== prevState.scrollLeft && nextProps.scrollToColumn < 0 || nextProps.scrollTop !== prevState.scrollTop && nextProps.scrollToRow < 0) {\n        Object.assign(newState, Grid._getScrollToPositionStateUpdate({\n          prevState: prevState,\n          scrollLeft: nextProps.scrollLeft,\n          scrollTop: nextProps.scrollTop\n        }));\n      }\n\n      var instanceProps = prevState.instanceProps; // Initially we should not clearStyleCache\n\n      newState.needToResetStyleCache = false;\n\n      if (nextProps.columnWidth !== instanceProps.prevColumnWidth || nextProps.rowHeight !== instanceProps.prevRowHeight) {\n        // Reset cache. set it to {} in render\n        newState.needToResetStyleCache = true;\n      }\n\n      instanceProps.columnSizeAndPositionManager.configure({\n        cellCount: nextProps.columnCount,\n        estimatedCellSize: Grid._getEstimatedColumnSize(nextProps),\n        cellSizeGetter: Grid._wrapSizeGetter(nextProps.columnWidth)\n      });\n      instanceProps.rowSizeAndPositionManager.configure({\n        cellCount: nextProps.rowCount,\n        estimatedCellSize: Grid._getEstimatedRowSize(nextProps),\n        cellSizeGetter: Grid._wrapSizeGetter(nextProps.rowHeight)\n      });\n\n      if (instanceProps.prevColumnCount === 0 || instanceProps.prevRowCount === 0) {\n        instanceProps.prevColumnCount = 0;\n        instanceProps.prevRowCount = 0;\n      } // If scrolling is controlled outside this component, clear cache when scrolling stops\n\n\n      if (nextProps.autoHeight && nextProps.isScrolling === false && instanceProps.prevIsScrolling === true) {\n        Object.assign(newState, {\n          isScrolling: false\n        });\n      }\n\n      var maybeStateA;\n      var maybeStateB;\n      calculateSizeAndPositionDataAndUpdateScrollOffset({\n        cellCount: instanceProps.prevColumnCount,\n        cellSize: typeof instanceProps.prevColumnWidth === 'number' ? instanceProps.prevColumnWidth : null,\n        computeMetadataCallback: function computeMetadataCallback() {\n          return instanceProps.columnSizeAndPositionManager.resetCell(0);\n        },\n        computeMetadataCallbackProps: nextProps,\n        nextCellsCount: nextProps.columnCount,\n        nextCellSize: typeof nextProps.columnWidth === 'number' ? nextProps.columnWidth : null,\n        nextScrollToIndex: nextProps.scrollToColumn,\n        scrollToIndex: instanceProps.prevScrollToColumn,\n        updateScrollOffsetForScrollToIndex: function updateScrollOffsetForScrollToIndex() {\n          maybeStateA = Grid._getScrollLeftForScrollToColumnStateUpdate(nextProps, prevState);\n        }\n      });\n      calculateSizeAndPositionDataAndUpdateScrollOffset({\n        cellCount: instanceProps.prevRowCount,\n        cellSize: typeof instanceProps.prevRowHeight === 'number' ? instanceProps.prevRowHeight : null,\n        computeMetadataCallback: function computeMetadataCallback() {\n          return instanceProps.rowSizeAndPositionManager.resetCell(0);\n        },\n        computeMetadataCallbackProps: nextProps,\n        nextCellsCount: nextProps.rowCount,\n        nextCellSize: typeof nextProps.rowHeight === 'number' ? nextProps.rowHeight : null,\n        nextScrollToIndex: nextProps.scrollToRow,\n        scrollToIndex: instanceProps.prevScrollToRow,\n        updateScrollOffsetForScrollToIndex: function updateScrollOffsetForScrollToIndex() {\n          maybeStateB = Grid._getScrollTopForScrollToRowStateUpdate(nextProps, prevState);\n        }\n      });\n      instanceProps.prevColumnCount = nextProps.columnCount;\n      instanceProps.prevColumnWidth = nextProps.columnWidth;\n      instanceProps.prevIsScrolling = nextProps.isScrolling === true;\n      instanceProps.prevRowCount = nextProps.rowCount;\n      instanceProps.prevRowHeight = nextProps.rowHeight;\n      instanceProps.prevScrollToColumn = nextProps.scrollToColumn;\n      instanceProps.prevScrollToRow = nextProps.scrollToRow; // getting scrollBarSize (moved from componentWillMount)\n\n      instanceProps.scrollbarSize = nextProps.getScrollbarSize();\n\n      if (instanceProps.scrollbarSize === undefined) {\n        instanceProps.scrollbarSizeMeasured = false;\n        instanceProps.scrollbarSize = 0;\n      } else {\n        instanceProps.scrollbarSizeMeasured = true;\n      }\n\n      newState.instanceProps = instanceProps;\n      return _objectSpread({}, newState, {}, maybeStateA, {}, maybeStateB);\n    }\n  }, {\n    key: \"_getEstimatedColumnSize\",\n    value: function _getEstimatedColumnSize(props) {\n      return typeof props.columnWidth === 'number' ? props.columnWidth : props.estimatedColumnSize;\n    }\n  }, {\n    key: \"_getEstimatedRowSize\",\n    value: function _getEstimatedRowSize(props) {\n      return typeof props.rowHeight === 'number' ? props.rowHeight : props.estimatedRowSize;\n    }\n  }, {\n    key: \"_getScrollToPositionStateUpdate\",\n\n    /**\n     * Get the updated state after scrolling to\n     * scrollLeft and scrollTop\n     */\n    value: function _getScrollToPositionStateUpdate(_ref9) {\n      var prevState = _ref9.prevState,\n          scrollLeft = _ref9.scrollLeft,\n          scrollTop = _ref9.scrollTop;\n      var newState = {\n        scrollPositionChangeReason: SCROLL_POSITION_CHANGE_REASONS.REQUESTED\n      };\n\n      if (typeof scrollLeft === 'number' && scrollLeft >= 0) {\n        newState.scrollDirectionHorizontal = scrollLeft > prevState.scrollLeft ? SCROLL_DIRECTION_FORWARD : SCROLL_DIRECTION_BACKWARD;\n        newState.scrollLeft = scrollLeft;\n      }\n\n      if (typeof scrollTop === 'number' && scrollTop >= 0) {\n        newState.scrollDirectionVertical = scrollTop > prevState.scrollTop ? SCROLL_DIRECTION_FORWARD : SCROLL_DIRECTION_BACKWARD;\n        newState.scrollTop = scrollTop;\n      }\n\n      if (typeof scrollLeft === 'number' && scrollLeft >= 0 && scrollLeft !== prevState.scrollLeft || typeof scrollTop === 'number' && scrollTop >= 0 && scrollTop !== prevState.scrollTop) {\n        return newState;\n      }\n\n      return {};\n    }\n  }, {\n    key: \"_wrapSizeGetter\",\n    value: function _wrapSizeGetter(value) {\n      return typeof value === 'function' ? value : function () {\n        return value;\n      };\n    }\n  }, {\n    key: \"_getCalculatedScrollLeft\",\n    value: function _getCalculatedScrollLeft(nextProps, prevState) {\n      var columnCount = nextProps.columnCount,\n          height = nextProps.height,\n          scrollToAlignment = nextProps.scrollToAlignment,\n          scrollToColumn = nextProps.scrollToColumn,\n          width = nextProps.width;\n      var scrollLeft = prevState.scrollLeft,\n          instanceProps = prevState.instanceProps;\n\n      if (columnCount > 0) {\n        var finalColumn = columnCount - 1;\n        var targetIndex = scrollToColumn < 0 ? finalColumn : Math.min(finalColumn, scrollToColumn);\n        var totalRowsHeight = instanceProps.rowSizeAndPositionManager.getTotalSize();\n        var scrollBarSize = instanceProps.scrollbarSizeMeasured && totalRowsHeight > height ? instanceProps.scrollbarSize : 0;\n        return instanceProps.columnSizeAndPositionManager.getUpdatedOffsetForIndex({\n          align: scrollToAlignment,\n          containerSize: width - scrollBarSize,\n          currentOffset: scrollLeft,\n          targetIndex: targetIndex\n        });\n      }\n\n      return 0;\n    }\n  }, {\n    key: \"_getScrollLeftForScrollToColumnStateUpdate\",\n    value: function _getScrollLeftForScrollToColumnStateUpdate(nextProps, prevState) {\n      var scrollLeft = prevState.scrollLeft;\n\n      var calculatedScrollLeft = Grid._getCalculatedScrollLeft(nextProps, prevState);\n\n      if (typeof calculatedScrollLeft === 'number' && calculatedScrollLeft >= 0 && scrollLeft !== calculatedScrollLeft) {\n        return Grid._getScrollToPositionStateUpdate({\n          prevState: prevState,\n          scrollLeft: calculatedScrollLeft,\n          scrollTop: -1\n        });\n      }\n\n      return {};\n    }\n  }, {\n    key: \"_getCalculatedScrollTop\",\n    value: function _getCalculatedScrollTop(nextProps, prevState) {\n      var height = nextProps.height,\n          rowCount = nextProps.rowCount,\n          scrollToAlignment = nextProps.scrollToAlignment,\n          scrollToRow = nextProps.scrollToRow,\n          width = nextProps.width;\n      var scrollTop = prevState.scrollTop,\n          instanceProps = prevState.instanceProps;\n\n      if (rowCount > 0) {\n        var finalRow = rowCount - 1;\n        var targetIndex = scrollToRow < 0 ? finalRow : Math.min(finalRow, scrollToRow);\n        var totalColumnsWidth = instanceProps.columnSizeAndPositionManager.getTotalSize();\n        var scrollBarSize = instanceProps.scrollbarSizeMeasured && totalColumnsWidth > width ? instanceProps.scrollbarSize : 0;\n        return instanceProps.rowSizeAndPositionManager.getUpdatedOffsetForIndex({\n          align: scrollToAlignment,\n          containerSize: height - scrollBarSize,\n          currentOffset: scrollTop,\n          targetIndex: targetIndex\n        });\n      }\n\n      return 0;\n    }\n  }, {\n    key: \"_getScrollTopForScrollToRowStateUpdate\",\n    value: function _getScrollTopForScrollToRowStateUpdate(nextProps, prevState) {\n      var scrollTop = prevState.scrollTop;\n\n      var calculatedScrollTop = Grid._getCalculatedScrollTop(nextProps, prevState);\n\n      if (typeof calculatedScrollTop === 'number' && calculatedScrollTop >= 0 && scrollTop !== calculatedScrollTop) {\n        return Grid._getScrollToPositionStateUpdate({\n          prevState: prevState,\n          scrollLeft: -1,\n          scrollTop: calculatedScrollTop\n        });\n      }\n\n      return {};\n    }\n  }]);\n\n  return Grid;\n}(React.PureComponent), _defineProperty(_class, \"propTypes\", process.env.NODE_ENV === 'production' ? null : {\n  \"aria-label\": PropTypes.string.isRequired,\n  \"aria-readonly\": PropTypes.bool,\n\n  /**\n   * Set the width of the inner scrollable container to 'auto'.\n   * This is useful for single-column Grids to ensure that the column doesn't extend below a vertical scrollbar.\n   */\n  \"autoContainerWidth\": PropTypes.bool.isRequired,\n\n  /**\n   * Removes fixed height from the scrollingContainer so that the total height of rows can stretch the window.\n   * Intended for use with WindowScroller\n   */\n  \"autoHeight\": PropTypes.bool.isRequired,\n\n  /**\n   * Removes fixed width from the scrollingContainer so that the total width of rows can stretch the window.\n   * Intended for use with WindowScroller\n   */\n  \"autoWidth\": PropTypes.bool.isRequired,\n\n  /** Responsible for rendering a cell given an row and column index.  */\n  \"cellRenderer\": function cellRenderer() {\n    return (typeof bpfrpt_proptype_CellRenderer === \"function\" ? bpfrpt_proptype_CellRenderer.isRequired ? bpfrpt_proptype_CellRenderer.isRequired : bpfrpt_proptype_CellRenderer : PropTypes.shape(bpfrpt_proptype_CellRenderer).isRequired).apply(this, arguments);\n  },\n\n  /** Responsible for rendering a group of cells given their index ranges.  */\n  \"cellRangeRenderer\": function cellRangeRenderer() {\n    return (typeof bpfrpt_proptype_CellRangeRenderer === \"function\" ? bpfrpt_proptype_CellRangeRenderer.isRequired ? bpfrpt_proptype_CellRangeRenderer.isRequired : bpfrpt_proptype_CellRangeRenderer : PropTypes.shape(bpfrpt_proptype_CellRangeRenderer).isRequired).apply(this, arguments);\n  },\n\n  /** Optional custom CSS class name to attach to root Grid element.  */\n  \"className\": PropTypes.string,\n\n  /** Number of columns in grid.  */\n  \"columnCount\": PropTypes.number.isRequired,\n\n  /** Either a fixed column width (number) or a function that returns the width of a column given its index.  */\n  \"columnWidth\": function columnWidth() {\n    return (typeof bpfrpt_proptype_CellSize === \"function\" ? bpfrpt_proptype_CellSize.isRequired ? bpfrpt_proptype_CellSize.isRequired : bpfrpt_proptype_CellSize : PropTypes.shape(bpfrpt_proptype_CellSize).isRequired).apply(this, arguments);\n  },\n\n  /** Unfiltered props for the Grid container. */\n  \"containerProps\": PropTypes.object,\n\n  /** ARIA role for the cell-container.  */\n  \"containerRole\": PropTypes.string.isRequired,\n\n  /** Optional inline style applied to inner cell-container */\n  \"containerStyle\": PropTypes.object.isRequired,\n\n  /**\n   * If CellMeasurer is used to measure this Grid's children, this should be a pointer to its CellMeasurerCache.\n   * A shared CellMeasurerCache reference enables Grid and CellMeasurer to share measurement data.\n   */\n  \"deferredMeasurementCache\": PropTypes.object,\n\n  /**\n   * Used to estimate the total width of a Grid before all of its columns have actually been measured.\n   * The estimated total width is adjusted as columns are rendered.\n   */\n  \"estimatedColumnSize\": PropTypes.number.isRequired,\n\n  /**\n   * Used to estimate the total height of a Grid before all of its rows have actually been measured.\n   * The estimated total height is adjusted as rows are rendered.\n   */\n  \"estimatedRowSize\": PropTypes.number.isRequired,\n\n  /** Exposed for testing purposes only.  */\n  \"getScrollbarSize\": PropTypes.func.isRequired,\n\n  /** Height of Grid; this property determines the number of visible (vs virtualized) rows.  */\n  \"height\": PropTypes.number.isRequired,\n\n  /** Optional custom id to attach to root Grid element.  */\n  \"id\": PropTypes.string,\n\n  /**\n   * Override internal is-scrolling state tracking.\n   * This property is primarily intended for use with the WindowScroller component.\n   */\n  \"isScrolling\": PropTypes.bool,\n\n  /**\n   * Opt-out of isScrolling param passed to cellRangeRenderer.\n   * To avoid the extra render when scroll stops.\n   */\n  \"isScrollingOptOut\": PropTypes.bool.isRequired,\n\n  /** Optional renderer to be used in place of rows when either :rowCount or :columnCount is 0.  */\n  \"noContentRenderer\": function noContentRenderer() {\n    return (typeof bpfrpt_proptype_NoContentRenderer === \"function\" ? bpfrpt_proptype_NoContentRenderer.isRequired ? bpfrpt_proptype_NoContentRenderer.isRequired : bpfrpt_proptype_NoContentRenderer : PropTypes.shape(bpfrpt_proptype_NoContentRenderer).isRequired).apply(this, arguments);\n  },\n\n  /**\n   * Callback invoked whenever the scroll offset changes within the inner scrollable region.\n   * This callback can be used to sync scrolling between lists, tables, or grids.\n   */\n  \"onScroll\": PropTypes.func.isRequired,\n\n  /**\n   * Called whenever a horizontal or vertical scrollbar is added or removed.\n   * This prop is not intended for end-user use;\n   * It is used by MultiGrid to support fixed-row/fixed-column scroll syncing.\n   */\n  \"onScrollbarPresenceChange\": PropTypes.func.isRequired,\n\n  /** Callback invoked with information about the section of the Grid that was just rendered.  */\n  \"onSectionRendered\": PropTypes.func.isRequired,\n\n  /**\n   * Number of columns to render before/after the visible section of the grid.\n   * These columns can help for smoother scrolling on touch devices or browsers that send scroll events infrequently.\n   */\n  \"overscanColumnCount\": PropTypes.number.isRequired,\n\n  /**\n   * Calculates the number of cells to overscan before and after a specified range.\n   * This function ensures that overscanning doesn't exceed the available cells.\n   */\n  \"overscanIndicesGetter\": function overscanIndicesGetter() {\n    return (typeof bpfrpt_proptype_OverscanIndicesGetter === \"function\" ? bpfrpt_proptype_OverscanIndicesGetter.isRequired ? bpfrpt_proptype_OverscanIndicesGetter.isRequired : bpfrpt_proptype_OverscanIndicesGetter : PropTypes.shape(bpfrpt_proptype_OverscanIndicesGetter).isRequired).apply(this, arguments);\n  },\n\n  /**\n   * Number of rows to render above/below the visible section of the grid.\n   * These rows can help for smoother scrolling on touch devices or browsers that send scroll events infrequently.\n   */\n  \"overscanRowCount\": PropTypes.number.isRequired,\n\n  /** ARIA role for the grid element.  */\n  \"role\": PropTypes.string.isRequired,\n\n  /**\n   * Either a fixed row height (number) or a function that returns the height of a row given its index.\n   * Should implement the following interface: ({ index: number }): number\n   */\n  \"rowHeight\": function rowHeight() {\n    return (typeof bpfrpt_proptype_CellSize === \"function\" ? bpfrpt_proptype_CellSize.isRequired ? bpfrpt_proptype_CellSize.isRequired : bpfrpt_proptype_CellSize : PropTypes.shape(bpfrpt_proptype_CellSize).isRequired).apply(this, arguments);\n  },\n\n  /** Number of rows in grid.  */\n  \"rowCount\": PropTypes.number.isRequired,\n\n  /** Wait this amount of time after the last scroll event before resetting Grid `pointer-events`. */\n  \"scrollingResetTimeInterval\": PropTypes.number.isRequired,\n\n  /** Horizontal offset. */\n  \"scrollLeft\": PropTypes.number,\n\n  /**\n   * Controls scroll-to-cell behavior of the Grid.\n   * The default (\"auto\") scrolls the least amount possible to ensure that the specified cell is fully visible.\n   * Use \"start\" to align cells to the top/left of the Grid and \"end\" to align bottom/right.\n   */\n  \"scrollToAlignment\": function scrollToAlignment() {\n    return (typeof bpfrpt_proptype_Alignment === \"function\" ? bpfrpt_proptype_Alignment.isRequired ? bpfrpt_proptype_Alignment.isRequired : bpfrpt_proptype_Alignment : PropTypes.shape(bpfrpt_proptype_Alignment).isRequired).apply(this, arguments);\n  },\n\n  /** Column index to ensure visible (by forcefully scrolling if necessary) */\n  \"scrollToColumn\": PropTypes.number.isRequired,\n\n  /** Vertical offset. */\n  \"scrollTop\": PropTypes.number,\n\n  /** Row index to ensure visible (by forcefully scrolling if necessary) */\n  \"scrollToRow\": PropTypes.number.isRequired,\n\n  /** Optional inline style */\n  \"style\": PropTypes.object.isRequired,\n\n  /** Tab index for focus */\n  \"tabIndex\": PropTypes.number,\n\n  /** Width of Grid; this property determines the number of visible (vs virtualized) columns.  */\n  \"width\": PropTypes.number.isRequired\n}), _temp);\n\n_defineProperty(Grid, \"defaultProps\", {\n  'aria-label': 'grid',\n  'aria-readonly': true,\n  autoContainerWidth: false,\n  autoHeight: false,\n  autoWidth: false,\n  cellRangeRenderer: defaultCellRangeRenderer,\n  containerRole: 'rowgroup',\n  containerStyle: {},\n  estimatedColumnSize: 100,\n  estimatedRowSize: 30,\n  getScrollbarSize: scrollbarSize,\n  noContentRenderer: renderNull,\n  onScroll: function onScroll() {},\n  onScrollbarPresenceChange: function onScrollbarPresenceChange() {},\n  onSectionRendered: function onSectionRendered() {},\n  overscanColumnCount: 0,\n  overscanIndicesGetter: defaultOverscanIndicesGetter,\n  overscanRowCount: 10,\n  role: 'grid',\n  scrollingResetTimeInterval: DEFAULT_SCROLLING_RESET_TIME_INTERVAL,\n  scrollToAlignment: 'auto',\n  scrollToColumn: -1,\n  scrollToRow: -1,\n  style: {},\n  tabIndex: 0,\n  isScrollingOptOut: false\n});\n\npolyfill(Grid);\nexport default Grid;\nimport { bpfrpt_proptype_CellRenderer } from \"./types\";\nimport { bpfrpt_proptype_CellRangeRenderer } from \"./types\";\nimport { bpfrpt_proptype_CellPosition } from \"./types\";\nimport { bpfrpt_proptype_CellSize } from \"./types\";\nimport { bpfrpt_proptype_CellSizeGetter } from \"./types\";\nimport { bpfrpt_proptype_NoContentRenderer } from \"./types\";\nimport { bpfrpt_proptype_Scroll } from \"./types\";\nimport { bpfrpt_proptype_ScrollbarPresenceChange } from \"./types\";\nimport { bpfrpt_proptype_RenderedSection } from \"./types\";\nimport { bpfrpt_proptype_OverscanIndicesGetter } from \"./types\";\nimport { bpfrpt_proptype_Alignment } from \"./types\";\nimport { bpfrpt_proptype_CellCache } from \"./types\";\nimport { bpfrpt_proptype_StyleCache } from \"./types\";\nimport { bpfrpt_proptype_AnimationTimeoutId } from \"../utils/requestAnimationTimeout\";\nimport PropTypes from \"prop-types\";", "import { caf, raf } from './animationFrame';\nvar bpfrpt_proptype_AnimationTimeoutId = process.env.NODE_ENV === 'production' ? null : {\n  \"id\": PropTypes.number.isRequired\n};\nexport var cancelAnimationTimeout = function cancelAnimationTimeout(frame) {\n  return caf(frame.id);\n};\n/**\n * Recursively calls requestAnimationFrame until a specified delay has been met or exceeded.\n * When the delay time has been reached the function you're timing out will be called.\n *\n * Credit: <PERSON> (https://gist.github.com/joelambert/1002116#file-requesttimeout-js)\n */\n\nexport var requestAnimationTimeout = function requestAnimationTimeout(callback, delay) {\n  var start; // wait for end of processing current event handler, because event handler may be long\n\n  Promise.resolve().then(function () {\n    start = Date.now();\n  });\n\n  var timeout = function timeout() {\n    if (Date.now() - start >= delay) {\n      callback.call();\n    } else {\n      frame.id = raf(timeout);\n    }\n  };\n\n  var frame = {\n    id: raf(timeout)\n  };\n  return frame;\n};\nimport PropTypes from \"prop-types\";\nexport { bpfrpt_proptype_AnimationTimeoutId };", "export var SCROLL_DIRECTION_BACKWARD = -1;\nexport var SCROLL_DIRECTION_FORWARD = 1;\nexport var SCROLL_DIRECTION_HORIZONTAL = 'horizontal';\nexport var SCROLL_DIRECTION_VERTICAL = 'vertical';\n/**\n * Calculates the number of cells to overscan before and after a specified range.\n * This function ensures that overscanning doesn't exceed the available cells.\n */\n\nexport default function defaultOverscanIndicesGetter(_ref) {\n  var cellCount = _ref.cellCount,\n      overscanCellsCount = _ref.overscanCellsCount,\n      scrollDirection = _ref.scrollDirection,\n      startIndex = _ref.startIndex,\n      stopIndex = _ref.stopIndex;\n\n  if (scrollDirection === SCROLL_DIRECTION_FORWARD) {\n    return {\n      overscanStartIndex: Math.max(0, startIndex),\n      overscanStopIndex: Math.min(cellCount - 1, stopIndex + overscanCellsCount)\n    };\n  } else {\n    return {\n      overscanStartIndex: Math.max(0, startIndex - overscanCellsCount),\n      overscanStopIndex: Math.min(cellCount - 1, stopIndex)\n    };\n  }\n}\nimport { bpfrpt_proptype_OverscanIndicesGetterParams } from \"./types\";\nimport { bpfrpt_proptype_OverscanIndices } from \"./types\";", "/**\n * Default implementation of cellRangeRenderer used by Grid.\n * This renderer supports cell-caching while the user is scrolling.\n */\nexport default function defaultCellRangeRenderer(_ref) {\n  var cellCache = _ref.cellCache,\n      cellRenderer = _ref.cellRenderer,\n      columnSizeAndPositionManager = _ref.columnSizeAndPositionManager,\n      columnStartIndex = _ref.columnStartIndex,\n      columnStopIndex = _ref.columnStopIndex,\n      deferredMeasurementCache = _ref.deferredMeasurementCache,\n      horizontalOffsetAdjustment = _ref.horizontalOffsetAdjustment,\n      isScrolling = _ref.isScrolling,\n      isScrollingOptOut = _ref.isScrollingOptOut,\n      parent = _ref.parent,\n      rowSizeAndPositionManager = _ref.rowSizeAndPositionManager,\n      rowStartIndex = _ref.rowStartIndex,\n      rowStopIndex = _ref.rowStopIndex,\n      styleCache = _ref.styleCache,\n      verticalOffsetAdjustment = _ref.verticalOffsetAdjustment,\n      visibleColumnIndices = _ref.visibleColumnIndices,\n      visibleRowIndices = _ref.visibleRowIndices;\n  var renderedCells = []; // Browsers have native size limits for elements (eg Chrome 33M pixels, IE 1.5M pixes).\n  // User cannot scroll beyond these size limitations.\n  // In order to work around this, ScalingCellSizeAndPositionManager compresses offsets.\n  // We should never cache styles for compressed offsets though as this can lead to bugs.\n  // See issue #576 for more.\n\n  var areOffsetsAdjusted = columnSizeAndPositionManager.areOffsetsAdjusted() || rowSizeAndPositionManager.areOffsetsAdjusted();\n  var canCacheStyle = !isScrolling && !areOffsetsAdjusted;\n\n  for (var rowIndex = rowStartIndex; rowIndex <= rowStopIndex; rowIndex++) {\n    var rowDatum = rowSizeAndPositionManager.getSizeAndPositionOfCell(rowIndex);\n\n    for (var columnIndex = columnStartIndex; columnIndex <= columnStopIndex; columnIndex++) {\n      var columnDatum = columnSizeAndPositionManager.getSizeAndPositionOfCell(columnIndex);\n      var isVisible = columnIndex >= visibleColumnIndices.start && columnIndex <= visibleColumnIndices.stop && rowIndex >= visibleRowIndices.start && rowIndex <= visibleRowIndices.stop;\n      var key = \"\".concat(rowIndex, \"-\").concat(columnIndex);\n      var style = void 0; // Cache style objects so shallow-compare doesn't re-render unnecessarily.\n\n      if (canCacheStyle && styleCache[key]) {\n        style = styleCache[key];\n      } else {\n        // In deferred mode, cells will be initially rendered before we know their size.\n        // Don't interfere with CellMeasurer's measurements by setting an invalid size.\n        if (deferredMeasurementCache && !deferredMeasurementCache.has(rowIndex, columnIndex)) {\n          // Position not-yet-measured cells at top/left 0,0,\n          // And give them width/height of 'auto' so they can grow larger than the parent Grid if necessary.\n          // Positioning them further to the right/bottom influences their measured size.\n          style = {\n            height: 'auto',\n            left: 0,\n            position: 'absolute',\n            top: 0,\n            width: 'auto'\n          };\n        } else {\n          style = {\n            height: rowDatum.size,\n            left: columnDatum.offset + horizontalOffsetAdjustment,\n            position: 'absolute',\n            top: rowDatum.offset + verticalOffsetAdjustment,\n            width: columnDatum.size\n          };\n          styleCache[key] = style;\n        }\n      }\n\n      var cellRendererParams = {\n        columnIndex: columnIndex,\n        isScrolling: isScrolling,\n        isVisible: isVisible,\n        key: key,\n        parent: parent,\n        rowIndex: rowIndex,\n        style: style\n      };\n      var renderedCell = void 0; // Avoid re-creating cells while scrolling.\n      // This can lead to the same cell being created many times and can cause performance issues for \"heavy\" cells.\n      // If a scroll is in progress- cache and reuse cells.\n      // This cache will be thrown away once scrolling completes.\n      // However if we are scaling scroll positions and sizes, we should also avoid caching.\n      // This is because the offset changes slightly as scroll position changes and caching leads to stale values.\n      // For more info refer to issue #395\n      //\n      // If isScrollingOptOut is specified, we always cache cells.\n      // For more info refer to issue #1028\n\n      if ((isScrollingOptOut || isScrolling) && !horizontalOffsetAdjustment && !verticalOffsetAdjustment) {\n        if (!cellCache[key]) {\n          cellCache[key] = cellRenderer(cellRendererParams);\n        }\n\n        renderedCell = cellCache[key]; // If the user is no longer scrolling, don't cache cells.\n        // This makes dynamic cell content difficult for users and would also lead to a heavier memory footprint.\n      } else {\n        renderedCell = cellRenderer(cellRendererParams);\n      }\n\n      if (renderedCell == null || renderedCell === false) {\n        continue;\n      }\n\n      if (process.env.NODE_ENV !== 'production') {\n        warnAboutMissingStyle(parent, renderedCell);\n      }\n\n      renderedCells.push(renderedCell);\n    }\n  }\n\n  return renderedCells;\n}\n\nfunction warnAboutMissingStyle(parent, renderedCell) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (renderedCell) {\n      // If the direct child is a CellMeasurer, then we should check its child\n      // See issue #611\n      if (renderedCell.type && renderedCell.type.__internalCellMeasurerFlag) {\n        renderedCell = renderedCell.props.children;\n      }\n\n      if (renderedCell && renderedCell.props && renderedCell.props.style === undefined && parent.__warnedAboutMissingStyle !== true) {\n        parent.__warnedAboutMissingStyle = true;\n        console.warn('Rendered cell should include style property for positioning.');\n      }\n    }\n  }\n}\n\nimport { bpfrpt_proptype_CellRangeRendererParams } from \"./types\";", "export var SCROLL_DIRECTION_BACKWARD = -1;\nexport var SCROLL_DIRECTION_FORWARD = 1;\nexport var SCROLL_DIRECTION_HORIZONTAL = 'horizontal';\nexport var SCROLL_DIRECTION_VERTICAL = 'vertical';\n/**\n * Calculates the number of cells to overscan before and after a specified range.\n * This function ensures that overscanning doesn't exceed the available cells.\n */\n\nexport default function defaultOverscanIndicesGetter(_ref) {\n  var cellCount = _ref.cellCount,\n      overscanCellsCount = _ref.overscanCellsCount,\n      scrollDirection = _ref.scrollDirection,\n      startIndex = _ref.startIndex,\n      stopIndex = _ref.stopIndex;\n  // Make sure we render at least 1 cell extra before and after (except near boundaries)\n  // This is necessary in order to support keyboard navigation (TAB/SHIFT+TAB) in some cases\n  // For more info see issues #625\n  overscanCellsCount = Math.max(1, overscanCellsCount);\n\n  if (scrollDirection === SCROLL_DIRECTION_FORWARD) {\n    return {\n      overscanStartIndex: Math.max(0, startIndex - 1),\n      overscanStopIndex: Math.min(cellCount - 1, stopIndex + overscanCellsCount)\n    };\n  } else {\n    return {\n      overscanStartIndex: Math.max(0, startIndex - overscanCellsCount),\n      overscanStopIndex: Math.min(cellCount - 1, stopIndex + 1)\n    };\n  }\n}\nimport { bpfrpt_proptype_OverscanIndicesGetterParams } from \"./types\";\nimport { bpfrpt_proptype_OverscanIndices } from \"./types\";", "var bpfrpt_proptype_ScrollIndices = process.env.NODE_ENV === 'production' ? null : {\n  \"scrollToColumn\": PropTypes.number.isRequired,\n  \"scrollToRow\": PropTypes.number.isRequired\n};\nimport PropTypes from \"prop-types\";\nexport { bpfrpt_proptype_ScrollIndices };", "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\n\nvar _class, _temp;\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nimport * as React from 'react';\nimport { polyfill } from 'react-lifecycles-compat';\n/**\n * This HOC decorates a virtualized component and responds to arrow-key events by scrolling one row or column at a time.\n */\n\nvar ArrowKeyStepper = (_temp = _class =\n/*#__PURE__*/\nfunction (_React$PureComponent) {\n  _inherits(ArrowKeyStepper, _React$PureComponent);\n\n  function ArrowKeyStepper() {\n    var _getPrototypeOf2;\n\n    var _this;\n\n    _classCallCheck(this, ArrowKeyStepper);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _possibleConstructorReturn(this, (_getPrototypeOf2 = _getPrototypeOf(ArrowKeyStepper)).call.apply(_getPrototypeOf2, [this].concat(args)));\n\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      scrollToColumn: 0,\n      scrollToRow: 0,\n      instanceProps: {\n        prevScrollToColumn: 0,\n        prevScrollToRow: 0\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_columnStartIndex\", 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_columnStopIndex\", 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_rowStartIndex\", 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_rowStopIndex\", 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_onKeyDown\", function (event) {\n      var _this$props = _this.props,\n          columnCount = _this$props.columnCount,\n          disabled = _this$props.disabled,\n          mode = _this$props.mode,\n          rowCount = _this$props.rowCount;\n\n      if (disabled) {\n        return;\n      }\n\n      var _this$_getScrollState = _this._getScrollState(),\n          scrollToColumnPrevious = _this$_getScrollState.scrollToColumn,\n          scrollToRowPrevious = _this$_getScrollState.scrollToRow;\n\n      var _this$_getScrollState2 = _this._getScrollState(),\n          scrollToColumn = _this$_getScrollState2.scrollToColumn,\n          scrollToRow = _this$_getScrollState2.scrollToRow; // The above cases all prevent default event event behavior.\n      // This is to keep the grid from scrolling after the snap-to update.\n\n\n      switch (event.key) {\n        case 'ArrowDown':\n          scrollToRow = mode === 'cells' ? Math.min(scrollToRow + 1, rowCount - 1) : Math.min(_this._rowStopIndex + 1, rowCount - 1);\n          break;\n\n        case 'ArrowLeft':\n          scrollToColumn = mode === 'cells' ? Math.max(scrollToColumn - 1, 0) : Math.max(_this._columnStartIndex - 1, 0);\n          break;\n\n        case 'ArrowRight':\n          scrollToColumn = mode === 'cells' ? Math.min(scrollToColumn + 1, columnCount - 1) : Math.min(_this._columnStopIndex + 1, columnCount - 1);\n          break;\n\n        case 'ArrowUp':\n          scrollToRow = mode === 'cells' ? Math.max(scrollToRow - 1, 0) : Math.max(_this._rowStartIndex - 1, 0);\n          break;\n      }\n\n      if (scrollToColumn !== scrollToColumnPrevious || scrollToRow !== scrollToRowPrevious) {\n        event.preventDefault();\n\n        _this._updateScrollState({\n          scrollToColumn: scrollToColumn,\n          scrollToRow: scrollToRow\n        });\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_onSectionRendered\", function (_ref) {\n      var columnStartIndex = _ref.columnStartIndex,\n          columnStopIndex = _ref.columnStopIndex,\n          rowStartIndex = _ref.rowStartIndex,\n          rowStopIndex = _ref.rowStopIndex;\n      _this._columnStartIndex = columnStartIndex;\n      _this._columnStopIndex = columnStopIndex;\n      _this._rowStartIndex = rowStartIndex;\n      _this._rowStopIndex = rowStopIndex;\n    });\n\n    return _this;\n  }\n\n  _createClass(ArrowKeyStepper, [{\n    key: \"setScrollIndexes\",\n    value: function setScrollIndexes(_ref2) {\n      var scrollToColumn = _ref2.scrollToColumn,\n          scrollToRow = _ref2.scrollToRow;\n      this.setState({\n        scrollToRow: scrollToRow,\n        scrollToColumn: scrollToColumn\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props2 = this.props,\n          className = _this$props2.className,\n          children = _this$props2.children;\n\n      var _this$_getScrollState3 = this._getScrollState(),\n          scrollToColumn = _this$_getScrollState3.scrollToColumn,\n          scrollToRow = _this$_getScrollState3.scrollToRow;\n\n      return React.createElement(\"div\", {\n        className: className,\n        onKeyDown: this._onKeyDown\n      }, children({\n        onSectionRendered: this._onSectionRendered,\n        scrollToColumn: scrollToColumn,\n        scrollToRow: scrollToRow\n      }));\n    }\n  }, {\n    key: \"_getScrollState\",\n    value: function _getScrollState() {\n      return this.props.isControlled ? this.props : this.state;\n    }\n  }, {\n    key: \"_updateScrollState\",\n    value: function _updateScrollState(_ref3) {\n      var scrollToColumn = _ref3.scrollToColumn,\n          scrollToRow = _ref3.scrollToRow;\n      var _this$props3 = this.props,\n          isControlled = _this$props3.isControlled,\n          onScrollToChange = _this$props3.onScrollToChange;\n\n      if (typeof onScrollToChange === 'function') {\n        onScrollToChange({\n          scrollToColumn: scrollToColumn,\n          scrollToRow: scrollToRow\n        });\n      }\n\n      if (!isControlled) {\n        this.setState({\n          scrollToColumn: scrollToColumn,\n          scrollToRow: scrollToRow\n        });\n      }\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.isControlled) {\n        return {};\n      }\n\n      if (nextProps.scrollToColumn !== prevState.instanceProps.prevScrollToColumn || nextProps.scrollToRow !== prevState.instanceProps.prevScrollToRow) {\n        return _objectSpread({}, prevState, {\n          scrollToColumn: nextProps.scrollToColumn,\n          scrollToRow: nextProps.scrollToRow,\n          instanceProps: {\n            prevScrollToColumn: nextProps.scrollToColumn,\n            prevScrollToRow: nextProps.scrollToRow\n          }\n        });\n      }\n\n      return {};\n    }\n  }]);\n\n  return ArrowKeyStepper;\n}(React.PureComponent), _defineProperty(_class, \"propTypes\", process.env.NODE_ENV === 'production' ? null : {\n  \"children\": PropTypes.func.isRequired,\n  \"className\": PropTypes.string,\n  \"columnCount\": PropTypes.number.isRequired,\n  \"disabled\": PropTypes.bool.isRequired,\n  \"isControlled\": PropTypes.bool.isRequired,\n  \"mode\": PropTypes.oneOf([\"cells\", \"edges\"]).isRequired,\n  \"onScrollToChange\": PropTypes.func,\n  \"rowCount\": PropTypes.number.isRequired,\n  \"scrollToColumn\": PropTypes.number.isRequired,\n  \"scrollToRow\": PropTypes.number.isRequired\n}), _temp);\n\n_defineProperty(ArrowKeyStepper, \"defaultProps\", {\n  disabled: false,\n  isControlled: false,\n  mode: 'edges',\n  scrollToColumn: 0,\n  scrollToRow: 0\n});\n\npolyfill(ArrowKeyStepper);\nexport default ArrowKeyStepper;\nimport { bpfrpt_proptype_RenderedSection } from \"../Grid\";\nimport { bpfrpt_proptype_ScrollIndices } from \"./types\";\nimport PropTypes from \"prop-types\";", "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\n\nvar _class, _temp;\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nimport * as React from 'react';\nimport createDetectElementResize from '../vendor/detectElementResize';\nvar AutoSizer = (_temp = _class =\n/*#__PURE__*/\nfunction (_React$Component) {\n  _inherits(AutoSizer, _React$Component);\n\n  function AutoSizer() {\n    var _getPrototypeOf2;\n\n    var _this;\n\n    _classCallCheck(this, AutoSizer);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _possibleConstructorReturn(this, (_getPrototypeOf2 = _getPrototypeOf(AutoSizer)).call.apply(_getPrototypeOf2, [this].concat(args)));\n\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      height: _this.props.defaultHeight || 0,\n      width: _this.props.defaultWidth || 0\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_parentNode\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_autoSizer\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_window\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_detectElementResize\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_onResize\", function () {\n      var _this$props = _this.props,\n          disableHeight = _this$props.disableHeight,\n          disableWidth = _this$props.disableWidth,\n          onResize = _this$props.onResize;\n\n      if (_this._parentNode) {\n        // Guard against AutoSizer component being removed from the DOM immediately after being added.\n        // This can result in invalid style values which can result in NaN values if we don't handle them.\n        // See issue #150 for more context.\n        var height = _this._parentNode.offsetHeight || 0;\n        var width = _this._parentNode.offsetWidth || 0;\n        var win = _this._window || window;\n        var style = win.getComputedStyle(_this._parentNode) || {};\n        var paddingLeft = parseInt(style.paddingLeft, 10) || 0;\n        var paddingRight = parseInt(style.paddingRight, 10) || 0;\n        var paddingTop = parseInt(style.paddingTop, 10) || 0;\n        var paddingBottom = parseInt(style.paddingBottom, 10) || 0;\n        var newHeight = height - paddingTop - paddingBottom;\n        var newWidth = width - paddingLeft - paddingRight;\n\n        if (!disableHeight && _this.state.height !== newHeight || !disableWidth && _this.state.width !== newWidth) {\n          _this.setState({\n            height: height - paddingTop - paddingBottom,\n            width: width - paddingLeft - paddingRight\n          });\n\n          onResize({\n            height: height,\n            width: width\n          });\n        }\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_setRef\", function (autoSizer) {\n      _this._autoSizer = autoSizer;\n    });\n\n    return _this;\n  }\n\n  _createClass(AutoSizer, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var nonce = this.props.nonce;\n\n      if (this._autoSizer && this._autoSizer.parentNode && this._autoSizer.parentNode.ownerDocument && this._autoSizer.parentNode.ownerDocument.defaultView && this._autoSizer.parentNode instanceof this._autoSizer.parentNode.ownerDocument.defaultView.HTMLElement) {\n        // Delay access of parentNode until mount.\n        // This handles edge-cases where the component has already been unmounted before its ref has been set,\n        // As well as libraries like react-lite which have a slightly different lifecycle.\n        this._parentNode = this._autoSizer.parentNode;\n        this._window = this._autoSizer.parentNode.ownerDocument.defaultView; // Defer requiring resize handler in order to support server-side rendering.\n        // See issue #41\n\n        this._detectElementResize = createDetectElementResize(nonce, this._window);\n\n        this._detectElementResize.addResizeListener(this._parentNode, this._onResize);\n\n        this._onResize();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      if (this._detectElementResize && this._parentNode) {\n        this._detectElementResize.removeResizeListener(this._parentNode, this._onResize);\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props2 = this.props,\n          children = _this$props2.children,\n          className = _this$props2.className,\n          disableHeight = _this$props2.disableHeight,\n          disableWidth = _this$props2.disableWidth,\n          style = _this$props2.style;\n      var _this$state = this.state,\n          height = _this$state.height,\n          width = _this$state.width; // Outer div should not force width/height since that may prevent containers from shrinking.\n      // Inner component should overflow and use calculated width/height.\n      // See issue #68 for more information.\n\n      var outerStyle = {\n        overflow: 'visible'\n      };\n      var childParams = {};\n\n      if (!disableHeight) {\n        outerStyle.height = 0;\n        childParams.height = height;\n      }\n\n      if (!disableWidth) {\n        outerStyle.width = 0;\n        childParams.width = width;\n      }\n      /**\n       * TODO: Avoid rendering children before the initial measurements have been collected.\n       * At best this would just be wasting cycles.\n       * Add this check into version 10 though as it could break too many ref callbacks in version 9.\n       * Note that if default width/height props were provided this would still work with SSR.\n      if (\n        height !== 0 &&\n        width !== 0\n      ) {\n        child = children({ height, width })\n      }\n      */\n\n\n      return React.createElement(\"div\", {\n        className: className,\n        ref: this._setRef,\n        style: _objectSpread({}, outerStyle, {}, style)\n      }, children(childParams));\n    }\n  }]);\n\n  return AutoSizer;\n}(React.Component), _defineProperty(_class, \"propTypes\", process.env.NODE_ENV === 'production' ? null : {\n  /** Function responsible for rendering children.*/\n  \"children\": PropTypes.func.isRequired,\n\n  /** Optional custom CSS class name to attach to root AutoSizer element.  */\n  \"className\": PropTypes.string,\n\n  /** Default height to use for initial render; useful for SSR */\n  \"defaultHeight\": PropTypes.number,\n\n  /** Default width to use for initial render; useful for SSR */\n  \"defaultWidth\": PropTypes.number,\n\n  /** Disable dynamic :height property */\n  \"disableHeight\": PropTypes.bool.isRequired,\n\n  /** Disable dynamic :width property */\n  \"disableWidth\": PropTypes.bool.isRequired,\n\n  /** Nonce of the inlined stylesheet for Content Security Policy */\n  \"nonce\": PropTypes.string,\n\n  /** Callback to be invoked on-resize */\n  \"onResize\": PropTypes.func.isRequired,\n\n  /** Optional inline style */\n  \"style\": PropTypes.object\n}), _temp);\n\n_defineProperty(AutoSizer, \"defaultProps\", {\n  onResize: function onResize() {},\n  disableHeight: false,\n  disableWidth: false,\n  style: {}\n});\n\nexport { AutoSizer as default };\nimport PropTypes from \"prop-types\";", "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\n\nvar _class, _temp;\n\nimport * as React from 'react';\nimport { findDOMNode } from 'react-dom';\n\n/**\n * Wraps a cell and measures its rendered content.\n * Measurements are stored in a per-cell cache.\n * Cached-content is not be re-measured.\n */\nvar CellMeasurer = (_temp = _class =\n/*#__PURE__*/\nfunction (_React$PureComponent) {\n  _inherits(CellMeasurer, _React$PureComponent);\n\n  function CellMeasurer() {\n    var _getPrototypeOf2;\n\n    var _this;\n\n    _classCallCheck(this, CellMeasurer);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _possibleConstructorReturn(this, (_getPrototypeOf2 = _getPrototypeOf(CellMeasurer)).call.apply(_getPrototypeOf2, [this].concat(args)));\n\n    _defineProperty(_assertThisInitialized(_this), \"_child\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_measure\", function () {\n      var _this$props = _this.props,\n          cache = _this$props.cache,\n          _this$props$columnInd = _this$props.columnIndex,\n          columnIndex = _this$props$columnInd === void 0 ? 0 : _this$props$columnInd,\n          parent = _this$props.parent,\n          _this$props$rowIndex = _this$props.rowIndex,\n          rowIndex = _this$props$rowIndex === void 0 ? _this.props.index || 0 : _this$props$rowIndex;\n\n      var _this$_getCellMeasure = _this._getCellMeasurements(),\n          height = _this$_getCellMeasure.height,\n          width = _this$_getCellMeasure.width;\n\n      if (height !== cache.getHeight(rowIndex, columnIndex) || width !== cache.getWidth(rowIndex, columnIndex)) {\n        cache.set(rowIndex, columnIndex, width, height);\n\n        if (parent && typeof parent.recomputeGridSize === 'function') {\n          parent.recomputeGridSize({\n            columnIndex: columnIndex,\n            rowIndex: rowIndex\n          });\n        }\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_registerChild\", function (element) {\n      if (element && !(element instanceof Element)) {\n        console.warn('CellMeasurer registerChild expects to be passed Element or null');\n      }\n\n      _this._child = element;\n\n      if (element) {\n        _this._maybeMeasureCell();\n      }\n    });\n\n    return _this;\n  }\n\n  _createClass(CellMeasurer, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this._maybeMeasureCell();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this._maybeMeasureCell();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var children = this.props.children;\n      return typeof children === 'function' ? children({\n        measure: this._measure,\n        registerChild: this._registerChild\n      }) : children;\n    }\n  }, {\n    key: \"_getCellMeasurements\",\n    value: function _getCellMeasurements() {\n      var cache = this.props.cache;\n      var node = this._child || findDOMNode(this); // TODO Check for a bad combination of fixedWidth and missing numeric width or vice versa with height\n\n      if (node && node.ownerDocument && node.ownerDocument.defaultView && node instanceof node.ownerDocument.defaultView.HTMLElement) {\n        var styleWidth = node.style.width;\n        var styleHeight = node.style.height; // If we are re-measuring a cell that has already been measured,\n        // It will have a hard-coded width/height from the previous measurement.\n        // The fact that we are measuring indicates this measurement is probably stale,\n        // So explicitly clear it out (eg set to \"auto\") so we can recalculate.\n        // See issue #593 for more info.\n        // Even if we are measuring initially- if we're inside of a MultiGrid component,\n        // Explicitly clear width/height before measuring to avoid being tainted by another Grid.\n        // eg top/left Grid renders before bottom/right Grid\n        // Since the CellMeasurerCache is shared between them this taints derived cell size values.\n\n        if (!cache.hasFixedWidth()) {\n          node.style.width = 'auto';\n        }\n\n        if (!cache.hasFixedHeight()) {\n          node.style.height = 'auto';\n        }\n\n        var height = Math.ceil(node.offsetHeight);\n        var width = Math.ceil(node.offsetWidth); // Reset after measuring to avoid breaking styles; see #660\n\n        if (styleWidth) {\n          node.style.width = styleWidth;\n        }\n\n        if (styleHeight) {\n          node.style.height = styleHeight;\n        }\n\n        return {\n          height: height,\n          width: width\n        };\n      } else {\n        return {\n          height: 0,\n          width: 0\n        };\n      }\n    }\n  }, {\n    key: \"_maybeMeasureCell\",\n    value: function _maybeMeasureCell() {\n      var _this$props2 = this.props,\n          cache = _this$props2.cache,\n          _this$props2$columnIn = _this$props2.columnIndex,\n          columnIndex = _this$props2$columnIn === void 0 ? 0 : _this$props2$columnIn,\n          parent = _this$props2.parent,\n          _this$props2$rowIndex = _this$props2.rowIndex,\n          rowIndex = _this$props2$rowIndex === void 0 ? this.props.index || 0 : _this$props2$rowIndex;\n\n      if (!cache.has(rowIndex, columnIndex)) {\n        var _this$_getCellMeasure2 = this._getCellMeasurements(),\n            height = _this$_getCellMeasure2.height,\n            width = _this$_getCellMeasure2.width;\n\n        cache.set(rowIndex, columnIndex, width, height); // If size has changed, let Grid know to re-render.\n\n        if (parent && typeof parent.invalidateCellSizeAfterRender === 'function') {\n          parent.invalidateCellSizeAfterRender({\n            columnIndex: columnIndex,\n            rowIndex: rowIndex\n          });\n        }\n      }\n    }\n  }]);\n\n  return CellMeasurer;\n}(React.PureComponent), _defineProperty(_class, \"propTypes\", process.env.NODE_ENV === 'production' ? null : {\n  \"cache\": function cache() {\n    return (typeof bpfrpt_proptype_CellMeasureCache === \"function\" ? bpfrpt_proptype_CellMeasureCache.isRequired ? bpfrpt_proptype_CellMeasureCache.isRequired : bpfrpt_proptype_CellMeasureCache : PropTypes.shape(bpfrpt_proptype_CellMeasureCache).isRequired).apply(this, arguments);\n  },\n  \"children\": PropTypes.oneOfType([PropTypes.func, PropTypes.node]).isRequired,\n  \"columnIndex\": PropTypes.number,\n  \"index\": PropTypes.number,\n  \"parent\": PropTypes.shape({\n    invalidateCellSizeAfterRender: PropTypes.func,\n    recomputeGridSize: PropTypes.func\n  }).isRequired,\n  \"rowIndex\": PropTypes.number\n}), _temp); // Used for DEV mode warning check\n\n_defineProperty(CellMeasurer, \"__internalCellMeasurerFlag\", false);\n\nexport { CellMeasurer as default };\n\nif (process.env.NODE_ENV !== 'production') {\n  CellMeasurer.__internalCellMeasurerFlag = true;\n}\n\nimport { bpfrpt_proptype_CellMeasureCache } from \"./types\";\nimport PropTypes from \"prop-types\";", "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nexport var DEFAULT_HEIGHT = 30;\nexport var DEFAULT_WIDTH = 100; // Enables more intelligent mapping of a given column and row index to an item ID.\n// This prevents a cell cache from being invalidated when its parent collection is modified.\n\n/**\n * Caches measurements for a given cell.\n */\nvar CellMeasurerCache =\n/*#__PURE__*/\nfunction () {\n  function CellMeasurerCache() {\n    var _this = this;\n\n    var params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n    _classCallCheck(this, CellMeasurerCache);\n\n    _defineProperty(this, \"_cellHeightCache\", {});\n\n    _defineProperty(this, \"_cellWidthCache\", {});\n\n    _defineProperty(this, \"_columnWidthCache\", {});\n\n    _defineProperty(this, \"_rowHeightCache\", {});\n\n    _defineProperty(this, \"_defaultHeight\", void 0);\n\n    _defineProperty(this, \"_defaultWidth\", void 0);\n\n    _defineProperty(this, \"_minHeight\", void 0);\n\n    _defineProperty(this, \"_minWidth\", void 0);\n\n    _defineProperty(this, \"_keyMapper\", void 0);\n\n    _defineProperty(this, \"_hasFixedHeight\", void 0);\n\n    _defineProperty(this, \"_hasFixedWidth\", void 0);\n\n    _defineProperty(this, \"_columnCount\", 0);\n\n    _defineProperty(this, \"_rowCount\", 0);\n\n    _defineProperty(this, \"columnWidth\", function (_ref) {\n      var index = _ref.index;\n\n      var key = _this._keyMapper(0, index);\n\n      return _this._columnWidthCache[key] !== undefined ? _this._columnWidthCache[key] : _this._defaultWidth;\n    });\n\n    _defineProperty(this, \"rowHeight\", function (_ref2) {\n      var index = _ref2.index;\n\n      var key = _this._keyMapper(index, 0);\n\n      return _this._rowHeightCache[key] !== undefined ? _this._rowHeightCache[key] : _this._defaultHeight;\n    });\n\n    var defaultHeight = params.defaultHeight,\n        defaultWidth = params.defaultWidth,\n        fixedHeight = params.fixedHeight,\n        fixedWidth = params.fixedWidth,\n        keyMapper = params.keyMapper,\n        minHeight = params.minHeight,\n        minWidth = params.minWidth;\n    this._hasFixedHeight = fixedHeight === true;\n    this._hasFixedWidth = fixedWidth === true;\n    this._minHeight = minHeight || 0;\n    this._minWidth = minWidth || 0;\n    this._keyMapper = keyMapper || defaultKeyMapper;\n    this._defaultHeight = Math.max(this._minHeight, typeof defaultHeight === 'number' ? defaultHeight : DEFAULT_HEIGHT);\n    this._defaultWidth = Math.max(this._minWidth, typeof defaultWidth === 'number' ? defaultWidth : DEFAULT_WIDTH);\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (this._hasFixedHeight === false && this._hasFixedWidth === false) {\n        console.warn(\"CellMeasurerCache should only measure a cell's width or height. \" + 'You have configured CellMeasurerCache to measure both. ' + 'This will result in poor performance.');\n      }\n\n      if (this._hasFixedHeight === false && this._defaultHeight === 0) {\n        console.warn('Fixed height CellMeasurerCache should specify a :defaultHeight greater than 0. ' + 'Failing to do so will lead to unnecessary layout and poor performance.');\n      }\n\n      if (this._hasFixedWidth === false && this._defaultWidth === 0) {\n        console.warn('Fixed width CellMeasurerCache should specify a :defaultWidth greater than 0. ' + 'Failing to do so will lead to unnecessary layout and poor performance.');\n      }\n    }\n  }\n\n  _createClass(CellMeasurerCache, [{\n    key: \"clear\",\n    value: function clear(rowIndex) {\n      var columnIndex = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n\n      var key = this._keyMapper(rowIndex, columnIndex);\n\n      delete this._cellHeightCache[key];\n      delete this._cellWidthCache[key];\n\n      this._updateCachedColumnAndRowSizes(rowIndex, columnIndex);\n    }\n  }, {\n    key: \"clearAll\",\n    value: function clearAll() {\n      this._cellHeightCache = {};\n      this._cellWidthCache = {};\n      this._columnWidthCache = {};\n      this._rowHeightCache = {};\n      this._rowCount = 0;\n      this._columnCount = 0;\n    }\n  }, {\n    key: \"hasFixedHeight\",\n    value: function hasFixedHeight() {\n      return this._hasFixedHeight;\n    }\n  }, {\n    key: \"hasFixedWidth\",\n    value: function hasFixedWidth() {\n      return this._hasFixedWidth;\n    }\n  }, {\n    key: \"getHeight\",\n    value: function getHeight(rowIndex) {\n      var columnIndex = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n\n      if (this._hasFixedHeight) {\n        return this._defaultHeight;\n      } else {\n        var _key = this._keyMapper(rowIndex, columnIndex);\n\n        return this._cellHeightCache[_key] !== undefined ? Math.max(this._minHeight, this._cellHeightCache[_key]) : this._defaultHeight;\n      }\n    }\n  }, {\n    key: \"getWidth\",\n    value: function getWidth(rowIndex) {\n      var columnIndex = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n\n      if (this._hasFixedWidth) {\n        return this._defaultWidth;\n      } else {\n        var _key2 = this._keyMapper(rowIndex, columnIndex);\n\n        return this._cellWidthCache[_key2] !== undefined ? Math.max(this._minWidth, this._cellWidthCache[_key2]) : this._defaultWidth;\n      }\n    }\n  }, {\n    key: \"has\",\n    value: function has(rowIndex) {\n      var columnIndex = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n\n      var key = this._keyMapper(rowIndex, columnIndex);\n\n      return this._cellHeightCache[key] !== undefined;\n    }\n  }, {\n    key: \"set\",\n    value: function set(rowIndex, columnIndex, width, height) {\n      var key = this._keyMapper(rowIndex, columnIndex);\n\n      if (columnIndex >= this._columnCount) {\n        this._columnCount = columnIndex + 1;\n      }\n\n      if (rowIndex >= this._rowCount) {\n        this._rowCount = rowIndex + 1;\n      } // Size is cached per cell so we don't have to re-measure if cells are re-ordered.\n\n\n      this._cellHeightCache[key] = height;\n      this._cellWidthCache[key] = width;\n\n      this._updateCachedColumnAndRowSizes(rowIndex, columnIndex);\n    }\n  }, {\n    key: \"_updateCachedColumnAndRowSizes\",\n    value: function _updateCachedColumnAndRowSizes(rowIndex, columnIndex) {\n      // :columnWidth and :rowHeight are derived based on all cells in a column/row.\n      // Pre-cache these derived values for faster lookup later.\n      // Reads are expected to occur more frequently than writes in this case.\n      // Only update non-fixed dimensions though to avoid doing unnecessary work.\n      if (!this._hasFixedWidth) {\n        var columnWidth = 0;\n\n        for (var i = 0; i < this._rowCount; i++) {\n          columnWidth = Math.max(columnWidth, this.getWidth(i, columnIndex));\n        }\n\n        var columnKey = this._keyMapper(0, columnIndex);\n\n        this._columnWidthCache[columnKey] = columnWidth;\n      }\n\n      if (!this._hasFixedHeight) {\n        var rowHeight = 0;\n\n        for (var _i = 0; _i < this._columnCount; _i++) {\n          rowHeight = Math.max(rowHeight, this.getHeight(rowIndex, _i));\n        }\n\n        var rowKey = this._keyMapper(rowIndex, 0);\n\n        this._rowHeightCache[rowKey] = rowHeight;\n      }\n    }\n  }, {\n    key: \"defaultHeight\",\n    get: function get() {\n      return this._defaultHeight;\n    }\n  }, {\n    key: \"defaultWidth\",\n    get: function get() {\n      return this._defaultWidth;\n    }\n  }]);\n\n  return CellMeasurerCache;\n}();\n\nexport { CellMeasurerCache as default };\n\nfunction defaultKeyMapper(rowIndex, columnIndex) {\n  return \"\".concat(rowIndex, \"-\").concat(columnIndex);\n}\n\nimport { bpfrpt_proptype_CellMeasureCache } from \"./types\";", "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport { polyfill } from 'react-lifecycles-compat';\nimport createCallbackMemoizer from '../utils/createCallbackMemoizer';\nimport getScrollbarSize from 'dom-helpers/scrollbarSize'; // @TODO Merge Collection and CollectionView\n\n/**\n * Specifies the number of milliseconds during which to disable pointer events while a scroll is in progress.\n * This improves performance and makes scrolling smoother.\n */\n\nvar IS_SCROLLING_TIMEOUT = 150;\n/**\n * Controls whether the Grid updates the DOM element's scrollLeft/scrollTop based on the current state or just observes it.\n * This prevents Grid from interrupting mouse-wheel animations (see issue #2).\n */\n\nvar SCROLL_POSITION_CHANGE_REASONS = {\n  OBSERVED: 'observed',\n  REQUESTED: 'requested'\n};\n/**\n * Monitors changes in properties (eg. cellCount) and state (eg. scroll offsets) to determine when rendering needs to occur.\n * This component does not render any visible content itself; it defers to the specified :cellLayoutManager.\n */\n\nvar CollectionView =\n/*#__PURE__*/\nfunction (_React$PureComponent) {\n  _inherits(CollectionView, _React$PureComponent);\n\n  // Invokes callbacks only when their values have changed.\n  function CollectionView() {\n    var _getPrototypeOf2;\n\n    var _this;\n\n    _classCallCheck(this, CollectionView);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _possibleConstructorReturn(this, (_getPrototypeOf2 = _getPrototypeOf(CollectionView)).call.apply(_getPrototypeOf2, [this].concat(args))); // If this component is being rendered server-side, getScrollbarSize() will return undefined.\n    // We handle this case in componentDidMount()\n\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      isScrolling: false,\n      scrollLeft: 0,\n      scrollTop: 0\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_calculateSizeAndPositionDataOnNextUpdate\", false);\n\n    _defineProperty(_assertThisInitialized(_this), \"_onSectionRenderedMemoizer\", createCallbackMemoizer());\n\n    _defineProperty(_assertThisInitialized(_this), \"_onScrollMemoizer\", createCallbackMemoizer(false));\n\n    _defineProperty(_assertThisInitialized(_this), \"_invokeOnSectionRenderedHelper\", function () {\n      var _this$props = _this.props,\n          cellLayoutManager = _this$props.cellLayoutManager,\n          onSectionRendered = _this$props.onSectionRendered;\n\n      _this._onSectionRenderedMemoizer({\n        callback: onSectionRendered,\n        indices: {\n          indices: cellLayoutManager.getLastRenderedIndices()\n        }\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_setScrollingContainerRef\", function (ref) {\n      _this._scrollingContainer = ref;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_updateScrollPositionForScrollToCell\", function () {\n      var _this$props2 = _this.props,\n          cellLayoutManager = _this$props2.cellLayoutManager,\n          height = _this$props2.height,\n          scrollToAlignment = _this$props2.scrollToAlignment,\n          scrollToCell = _this$props2.scrollToCell,\n          width = _this$props2.width;\n      var _this$state = _this.state,\n          scrollLeft = _this$state.scrollLeft,\n          scrollTop = _this$state.scrollTop;\n\n      if (scrollToCell >= 0) {\n        var scrollPosition = cellLayoutManager.getScrollPositionForCell({\n          align: scrollToAlignment,\n          cellIndex: scrollToCell,\n          height: height,\n          scrollLeft: scrollLeft,\n          scrollTop: scrollTop,\n          width: width\n        });\n\n        if (scrollPosition.scrollLeft !== scrollLeft || scrollPosition.scrollTop !== scrollTop) {\n          _this._setScrollPosition(scrollPosition);\n        }\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_onScroll\", function (event) {\n      // In certain edge-cases React dispatches an onScroll event with an invalid target.scrollLeft / target.scrollTop.\n      // This invalid event can be detected by comparing event.target to this component's scrollable DOM element.\n      // See issue #404 for more information.\n      if (event.target !== _this._scrollingContainer) {\n        return;\n      } // Prevent pointer events from interrupting a smooth scroll\n\n\n      _this._enablePointerEventsAfterDelay(); // When this component is shrunk drastically, React dispatches a series of back-to-back scroll events,\n      // Gradually converging on a scrollTop that is within the bounds of the new, smaller height.\n      // This causes a series of rapid renders that is slow for long lists.\n      // We can avoid that by doing some simple bounds checking to ensure that scrollTop never exceeds the total height.\n\n\n      var _this$props3 = _this.props,\n          cellLayoutManager = _this$props3.cellLayoutManager,\n          height = _this$props3.height,\n          isScrollingChange = _this$props3.isScrollingChange,\n          width = _this$props3.width;\n      var scrollbarSize = _this._scrollbarSize;\n\n      var _cellLayoutManager$ge = cellLayoutManager.getTotalSize(),\n          totalHeight = _cellLayoutManager$ge.height,\n          totalWidth = _cellLayoutManager$ge.width;\n\n      var scrollLeft = Math.max(0, Math.min(totalWidth - width + scrollbarSize, event.target.scrollLeft));\n      var scrollTop = Math.max(0, Math.min(totalHeight - height + scrollbarSize, event.target.scrollTop)); // Certain devices (like Apple touchpad) rapid-fire duplicate events.\n      // Don't force a re-render if this is the case.\n      // The mouse may move faster then the animation frame does.\n      // Use requestAnimationFrame to avoid over-updating.\n\n      if (_this.state.scrollLeft !== scrollLeft || _this.state.scrollTop !== scrollTop) {\n        // Browsers with cancelable scroll events (eg. Firefox) interrupt scrolling animations if scrollTop/scrollLeft is set.\n        // Other browsers (eg. Safari) don't scroll as well without the help under certain conditions (DOM or style changes during scrolling).\n        // All things considered, this seems to be the best current work around that I'm aware of.\n        // For more information see https://github.com/bvaughn/react-virtualized/pull/124\n        var scrollPositionChangeReason = event.cancelable ? SCROLL_POSITION_CHANGE_REASONS.OBSERVED : SCROLL_POSITION_CHANGE_REASONS.REQUESTED; // Synchronously set :isScrolling the first time (since _setNextState will reschedule its animation frame each time it's called)\n\n        if (!_this.state.isScrolling) {\n          isScrollingChange(true);\n        }\n\n        _this.setState({\n          isScrolling: true,\n          scrollLeft: scrollLeft,\n          scrollPositionChangeReason: scrollPositionChangeReason,\n          scrollTop: scrollTop\n        });\n      }\n\n      _this._invokeOnScrollMemoizer({\n        scrollLeft: scrollLeft,\n        scrollTop: scrollTop,\n        totalWidth: totalWidth,\n        totalHeight: totalHeight\n      });\n    });\n\n    _this._scrollbarSize = getScrollbarSize();\n\n    if (_this._scrollbarSize === undefined) {\n      _this._scrollbarSizeMeasured = false;\n      _this._scrollbarSize = 0;\n    } else {\n      _this._scrollbarSizeMeasured = true;\n    }\n\n    return _this;\n  }\n  /**\n   * Forced recompute of cell sizes and positions.\n   * This function should be called if cell sizes have changed but nothing else has.\n   * Since cell positions are calculated by callbacks, the collection view has no way of detecting when the underlying data has changed.\n   */\n\n\n  _createClass(CollectionView, [{\n    key: \"recomputeCellSizesAndPositions\",\n    value: function recomputeCellSizesAndPositions() {\n      this._calculateSizeAndPositionDataOnNextUpdate = true;\n      this.forceUpdate();\n    }\n    /* ---------------------------- Component lifecycle methods ---------------------------- */\n\n    /**\n     * @private\n     * This method updates scrollLeft/scrollTop in state for the following conditions:\n     * 1) Empty content (0 rows or columns)\n     * 2) New scroll props overriding the current state\n     * 3) Cells-count or cells-size has changed, making previous scroll offsets invalid\n     */\n\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this$props4 = this.props,\n          cellLayoutManager = _this$props4.cellLayoutManager,\n          scrollLeft = _this$props4.scrollLeft,\n          scrollToCell = _this$props4.scrollToCell,\n          scrollTop = _this$props4.scrollTop; // If this component was first rendered server-side, scrollbar size will be undefined.\n      // In that event we need to remeasure.\n\n      if (!this._scrollbarSizeMeasured) {\n        this._scrollbarSize = getScrollbarSize();\n        this._scrollbarSizeMeasured = true;\n        this.setState({});\n      }\n\n      if (scrollToCell >= 0) {\n        this._updateScrollPositionForScrollToCell();\n      } else if (scrollLeft >= 0 || scrollTop >= 0) {\n        this._setScrollPosition({\n          scrollLeft: scrollLeft,\n          scrollTop: scrollTop\n        });\n      } // Update onSectionRendered callback.\n\n\n      this._invokeOnSectionRenderedHelper();\n\n      var _cellLayoutManager$ge2 = cellLayoutManager.getTotalSize(),\n          totalHeight = _cellLayoutManager$ge2.height,\n          totalWidth = _cellLayoutManager$ge2.width; // Initialize onScroll callback.\n\n\n      this._invokeOnScrollMemoizer({\n        scrollLeft: scrollLeft || 0,\n        scrollTop: scrollTop || 0,\n        totalHeight: totalHeight,\n        totalWidth: totalWidth\n      });\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps, prevState) {\n      var _this$props5 = this.props,\n          height = _this$props5.height,\n          scrollToAlignment = _this$props5.scrollToAlignment,\n          scrollToCell = _this$props5.scrollToCell,\n          width = _this$props5.width;\n      var _this$state2 = this.state,\n          scrollLeft = _this$state2.scrollLeft,\n          scrollPositionChangeReason = _this$state2.scrollPositionChangeReason,\n          scrollTop = _this$state2.scrollTop; // Make sure requested changes to :scrollLeft or :scrollTop get applied.\n      // Assigning to scrollLeft/scrollTop tells the browser to interrupt any running scroll animations,\n      // And to discard any pending async changes to the scroll position that may have happened in the meantime (e.g. on a separate scrolling thread).\n      // So we only set these when we require an adjustment of the scroll position.\n      // See issue #2 for more information.\n\n      if (scrollPositionChangeReason === SCROLL_POSITION_CHANGE_REASONS.REQUESTED) {\n        if (scrollLeft >= 0 && scrollLeft !== prevState.scrollLeft && scrollLeft !== this._scrollingContainer.scrollLeft) {\n          this._scrollingContainer.scrollLeft = scrollLeft;\n        }\n\n        if (scrollTop >= 0 && scrollTop !== prevState.scrollTop && scrollTop !== this._scrollingContainer.scrollTop) {\n          this._scrollingContainer.scrollTop = scrollTop;\n        }\n      } // Update scroll offsets if the current :scrollToCell values requires it\n\n\n      if (height !== prevProps.height || scrollToAlignment !== prevProps.scrollToAlignment || scrollToCell !== prevProps.scrollToCell || width !== prevProps.width) {\n        this._updateScrollPositionForScrollToCell();\n      } // Update onRowsRendered callback if start/stop indices have changed\n\n\n      this._invokeOnSectionRenderedHelper();\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      if (this._disablePointerEventsTimeoutId) {\n        clearTimeout(this._disablePointerEventsTimeoutId);\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props6 = this.props,\n          autoHeight = _this$props6.autoHeight,\n          cellCount = _this$props6.cellCount,\n          cellLayoutManager = _this$props6.cellLayoutManager,\n          className = _this$props6.className,\n          height = _this$props6.height,\n          horizontalOverscanSize = _this$props6.horizontalOverscanSize,\n          id = _this$props6.id,\n          noContentRenderer = _this$props6.noContentRenderer,\n          style = _this$props6.style,\n          verticalOverscanSize = _this$props6.verticalOverscanSize,\n          width = _this$props6.width;\n      var _this$state3 = this.state,\n          isScrolling = _this$state3.isScrolling,\n          scrollLeft = _this$state3.scrollLeft,\n          scrollTop = _this$state3.scrollTop; // Memoization reset\n\n      if (this._lastRenderedCellCount !== cellCount || this._lastRenderedCellLayoutManager !== cellLayoutManager || this._calculateSizeAndPositionDataOnNextUpdate) {\n        this._lastRenderedCellCount = cellCount;\n        this._lastRenderedCellLayoutManager = cellLayoutManager;\n        this._calculateSizeAndPositionDataOnNextUpdate = false;\n        cellLayoutManager.calculateSizeAndPositionData();\n      }\n\n      var _cellLayoutManager$ge3 = cellLayoutManager.getTotalSize(),\n          totalHeight = _cellLayoutManager$ge3.height,\n          totalWidth = _cellLayoutManager$ge3.width; // Safely expand the rendered area by the specified overscan amount\n\n\n      var left = Math.max(0, scrollLeft - horizontalOverscanSize);\n      var top = Math.max(0, scrollTop - verticalOverscanSize);\n      var right = Math.min(totalWidth, scrollLeft + width + horizontalOverscanSize);\n      var bottom = Math.min(totalHeight, scrollTop + height + verticalOverscanSize);\n      var childrenToDisplay = height > 0 && width > 0 ? cellLayoutManager.cellRenderers({\n        height: bottom - top,\n        isScrolling: isScrolling,\n        width: right - left,\n        x: left,\n        y: top\n      }) : [];\n      var collectionStyle = {\n        boxSizing: 'border-box',\n        direction: 'ltr',\n        height: autoHeight ? 'auto' : height,\n        position: 'relative',\n        WebkitOverflowScrolling: 'touch',\n        width: width,\n        willChange: 'transform'\n      }; // Force browser to hide scrollbars when we know they aren't necessary.\n      // Otherwise once scrollbars appear they may not disappear again.\n      // For more info see issue #116\n\n      var verticalScrollBarSize = totalHeight > height ? this._scrollbarSize : 0;\n      var horizontalScrollBarSize = totalWidth > width ? this._scrollbarSize : 0; // Also explicitly init styles to 'auto' if scrollbars are required.\n      // This works around an obscure edge case where external CSS styles have not yet been loaded,\n      // But an initial scroll index of offset is set as an external prop.\n      // Without this style, Grid would render the correct range of cells but would NOT update its internal offset.\n      // This was originally reported via clauderic/react-infinite-calendar/issues/23\n\n      collectionStyle.overflowX = totalWidth + verticalScrollBarSize <= width ? 'hidden' : 'auto';\n      collectionStyle.overflowY = totalHeight + horizontalScrollBarSize <= height ? 'hidden' : 'auto';\n      return React.createElement(\"div\", {\n        ref: this._setScrollingContainerRef,\n        \"aria-label\": this.props['aria-label'],\n        className: clsx('ReactVirtualized__Collection', className),\n        id: id,\n        onScroll: this._onScroll,\n        role: \"grid\",\n        style: _objectSpread({}, collectionStyle, {}, style),\n        tabIndex: 0\n      }, cellCount > 0 && React.createElement(\"div\", {\n        className: \"ReactVirtualized__Collection__innerScrollContainer\",\n        style: {\n          height: totalHeight,\n          maxHeight: totalHeight,\n          maxWidth: totalWidth,\n          overflow: 'hidden',\n          pointerEvents: isScrolling ? 'none' : '',\n          width: totalWidth\n        }\n      }, childrenToDisplay), cellCount === 0 && noContentRenderer());\n    }\n    /* ---------------------------- Helper methods ---------------------------- */\n\n    /**\n     * Sets an :isScrolling flag for a small window of time.\n     * This flag is used to disable pointer events on the scrollable portion of the Collection.\n     * This prevents jerky/stuttery mouse-wheel scrolling.\n     */\n\n  }, {\n    key: \"_enablePointerEventsAfterDelay\",\n    value: function _enablePointerEventsAfterDelay() {\n      var _this2 = this;\n\n      if (this._disablePointerEventsTimeoutId) {\n        clearTimeout(this._disablePointerEventsTimeoutId);\n      }\n\n      this._disablePointerEventsTimeoutId = setTimeout(function () {\n        var isScrollingChange = _this2.props.isScrollingChange;\n        isScrollingChange(false);\n        _this2._disablePointerEventsTimeoutId = null;\n\n        _this2.setState({\n          isScrolling: false\n        });\n      }, IS_SCROLLING_TIMEOUT);\n    }\n  }, {\n    key: \"_invokeOnScrollMemoizer\",\n    value: function _invokeOnScrollMemoizer(_ref) {\n      var _this3 = this;\n\n      var scrollLeft = _ref.scrollLeft,\n          scrollTop = _ref.scrollTop,\n          totalHeight = _ref.totalHeight,\n          totalWidth = _ref.totalWidth;\n\n      this._onScrollMemoizer({\n        callback: function callback(_ref2) {\n          var scrollLeft = _ref2.scrollLeft,\n              scrollTop = _ref2.scrollTop;\n          var _this3$props = _this3.props,\n              height = _this3$props.height,\n              onScroll = _this3$props.onScroll,\n              width = _this3$props.width;\n          onScroll({\n            clientHeight: height,\n            clientWidth: width,\n            scrollHeight: totalHeight,\n            scrollLeft: scrollLeft,\n            scrollTop: scrollTop,\n            scrollWidth: totalWidth\n          });\n        },\n        indices: {\n          scrollLeft: scrollLeft,\n          scrollTop: scrollTop\n        }\n      });\n    }\n  }, {\n    key: \"_setScrollPosition\",\n    value: function _setScrollPosition(_ref3) {\n      var scrollLeft = _ref3.scrollLeft,\n          scrollTop = _ref3.scrollTop;\n      var newState = {\n        scrollPositionChangeReason: SCROLL_POSITION_CHANGE_REASONS.REQUESTED\n      };\n\n      if (scrollLeft >= 0) {\n        newState.scrollLeft = scrollLeft;\n      }\n\n      if (scrollTop >= 0) {\n        newState.scrollTop = scrollTop;\n      }\n\n      if (scrollLeft >= 0 && scrollLeft !== this.state.scrollLeft || scrollTop >= 0 && scrollTop !== this.state.scrollTop) {\n        this.setState(newState);\n      }\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.cellCount === 0 && (prevState.scrollLeft !== 0 || prevState.scrollTop !== 0)) {\n        return {\n          scrollLeft: 0,\n          scrollTop: 0,\n          scrollPositionChangeReason: SCROLL_POSITION_CHANGE_REASONS.REQUESTED\n        };\n      } else if (nextProps.scrollLeft !== prevState.scrollLeft || nextProps.scrollTop !== prevState.scrollTop) {\n        return {\n          scrollLeft: nextProps.scrollLeft != null ? nextProps.scrollLeft : prevState.scrollLeft,\n          scrollTop: nextProps.scrollTop != null ? nextProps.scrollTop : prevState.scrollTop,\n          scrollPositionChangeReason: SCROLL_POSITION_CHANGE_REASONS.REQUESTED\n        };\n      }\n\n      return null;\n    }\n  }]);\n\n  return CollectionView;\n}(React.PureComponent);\n\n_defineProperty(CollectionView, \"defaultProps\", {\n  'aria-label': 'grid',\n  horizontalOverscanSize: 0,\n  noContentRenderer: function noContentRenderer() {\n    return null;\n  },\n  onScroll: function onScroll() {\n    return null;\n  },\n  onSectionRendered: function onSectionRendered() {\n    return null;\n  },\n  scrollToAlignment: 'auto',\n  scrollToCell: -1,\n  style: {},\n  verticalOverscanSize: 0\n});\n\nCollectionView.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  'aria-label': PropTypes.string,\n\n  /**\n   * Removes fixed height from the scrollingContainer so that the total height\n   * of rows can stretch the window. Intended for use with WindowScroller\n   */\n  autoHeight: PropTypes.bool,\n\n  /**\n   * Number of cells in collection.\n   */\n  cellCount: PropTypes.number.isRequired,\n\n  /**\n   * Calculates cell sizes and positions and manages rendering the appropriate cells given a specified window.\n   */\n  cellLayoutManager: PropTypes.object.isRequired,\n\n  /**\n   * Optional custom CSS class name to attach to root Collection element.\n   */\n  className: PropTypes.string,\n\n  /**\n   * Height of Collection; this property determines the number of visible (vs virtualized) rows.\n   */\n  height: PropTypes.number.isRequired,\n\n  /**\n   * Optional custom id to attach to root Collection element.\n   */\n  id: PropTypes.string,\n\n  /**\n   * Enables the `Collection` to horiontally \"overscan\" its content similar to how `Grid` does.\n   * This can reduce flicker around the edges when a user scrolls quickly.\n   */\n  horizontalOverscanSize: PropTypes.number.isRequired,\n  isScrollingChange: PropTypes.func,\n\n  /**\n   * Optional renderer to be used in place of rows when either :rowCount or :cellCount is 0.\n   */\n  noContentRenderer: PropTypes.func.isRequired,\n\n  /**\n   * Callback invoked whenever the scroll offset changes within the inner scrollable region.\n   * This callback can be used to sync scrolling between lists, tables, or grids.\n   * ({ clientHeight, clientWidth, scrollHeight, scrollLeft, scrollTop, scrollWidth }): void\n   */\n  onScroll: PropTypes.func.isRequired,\n\n  /**\n   * Callback invoked with information about the section of the Collection that was just rendered.\n   * This callback is passed a named :indices parameter which is an Array of the most recently rendered section indices.\n   */\n  onSectionRendered: PropTypes.func.isRequired,\n\n  /**\n   * Horizontal offset.\n   */\n  scrollLeft: PropTypes.number,\n\n  /**\n   * Controls scroll-to-cell behavior of the Grid.\n   * The default (\"auto\") scrolls the least amount possible to ensure that the specified cell is fully visible.\n   * Use \"start\" to align cells to the top/left of the Grid and \"end\" to align bottom/right.\n   */\n  scrollToAlignment: PropTypes.oneOf(['auto', 'end', 'start', 'center']).isRequired,\n\n  /**\n   * Cell index to ensure visible (by forcefully scrolling if necessary).\n   */\n  scrollToCell: PropTypes.number.isRequired,\n\n  /**\n   * Vertical offset.\n   */\n  scrollTop: PropTypes.number,\n\n  /**\n   * Optional custom inline style to attach to root Collection element.\n   */\n  style: PropTypes.object,\n\n  /**\n   * Enables the `Collection` to vertically \"overscan\" its content similar to how `Grid` does.\n   * This can reduce flicker around the edges when a user scrolls quickly.\n   */\n  verticalOverscanSize: PropTypes.number.isRequired,\n\n  /**\n   * Width of Collection; this property determines the number of visible (vs virtualized) columns.\n   */\n  width: PropTypes.number.isRequired\n} : {};\npolyfill(CollectionView);\nexport default CollectionView;", "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\n\n/**\n * A section of the Window.\n * Window Sections are used to group nearby cells.\n * This enables us to more quickly determine which cells to display in a given region of the Window.\n * Sections have a fixed size and contain 0 to many cells (tracked by their indices).\n */\nvar Section =\n/*#__PURE__*/\nfunction () {\n  function Section(_ref) {\n    var height = _ref.height,\n        width = _ref.width,\n        x = _ref.x,\n        y = _ref.y;\n\n    _classCallCheck(this, Section);\n\n    this.height = height;\n    this.width = width;\n    this.x = x;\n    this.y = y;\n    this._indexMap = {};\n    this._indices = [];\n  }\n  /** Add a cell to this section. */\n\n\n  _createClass(Section, [{\n    key: \"addCellIndex\",\n    value: function addCellIndex(_ref2) {\n      var index = _ref2.index;\n\n      if (!this._indexMap[index]) {\n        this._indexMap[index] = true;\n\n        this._indices.push(index);\n      }\n    }\n    /** Get all cell indices that have been added to this section. */\n\n  }, {\n    key: \"getCellIndices\",\n    value: function getCellIndices() {\n      return this._indices;\n    }\n    /** Intended for debugger/test purposes only */\n\n  }, {\n    key: \"toString\",\n    value: function toString() {\n      return \"\".concat(this.x, \",\").concat(this.y, \" \").concat(this.width, \"x\").concat(this.height);\n    }\n  }]);\n\n  return Section;\n}();\n\nexport { Section as default };\nimport { bpfrpt_proptype_Index } from \"./types\";\nimport { bpfrpt_proptype_SizeAndPositionInfo } from \"./types\";", "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\n\n/**\n * Window Sections are used to group nearby cells.\n * This enables us to more quickly determine which cells to display in a given region of the Window.\n * \n */\nimport Section from './Section';\nvar SECTION_SIZE = 100;\n\n/**\n * Contains 0 to many Sections.\n * Grows (and adds Sections) dynamically as cells are registered.\n * Automatically adds cells to the appropriate Section(s).\n */\nvar SectionManager =\n/*#__PURE__*/\nfunction () {\n  function SectionManager() {\n    var sectionSize = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : SECTION_SIZE;\n\n    _classCallCheck(this, SectionManager);\n\n    this._sectionSize = sectionSize;\n    this._cellMetadata = [];\n    this._sections = {};\n  }\n  /**\n   * Gets all cell indices contained in the specified region.\n   * A region may encompass 1 or more Sections.\n   */\n\n\n  _createClass(SectionManager, [{\n    key: \"getCellIndices\",\n    value: function getCellIndices(_ref) {\n      var height = _ref.height,\n          width = _ref.width,\n          x = _ref.x,\n          y = _ref.y;\n      var indices = {};\n      this.getSections({\n        height: height,\n        width: width,\n        x: x,\n        y: y\n      }).forEach(function (section) {\n        return section.getCellIndices().forEach(function (index) {\n          indices[index] = index;\n        });\n      }); // Object keys are strings; this function returns numbers\n\n      return Object.keys(indices).map(function (index) {\n        return indices[index];\n      });\n    }\n    /** Get size and position information for the cell specified. */\n\n  }, {\n    key: \"getCellMetadata\",\n    value: function getCellMetadata(_ref2) {\n      var index = _ref2.index;\n      return this._cellMetadata[index];\n    }\n    /** Get all Sections overlapping the specified region. */\n\n  }, {\n    key: \"getSections\",\n    value: function getSections(_ref3) {\n      var height = _ref3.height,\n          width = _ref3.width,\n          x = _ref3.x,\n          y = _ref3.y;\n      var sectionXStart = Math.floor(x / this._sectionSize);\n      var sectionXStop = Math.floor((x + width - 1) / this._sectionSize);\n      var sectionYStart = Math.floor(y / this._sectionSize);\n      var sectionYStop = Math.floor((y + height - 1) / this._sectionSize);\n      var sections = [];\n\n      for (var sectionX = sectionXStart; sectionX <= sectionXStop; sectionX++) {\n        for (var sectionY = sectionYStart; sectionY <= sectionYStop; sectionY++) {\n          var key = \"\".concat(sectionX, \".\").concat(sectionY);\n\n          if (!this._sections[key]) {\n            this._sections[key] = new Section({\n              height: this._sectionSize,\n              width: this._sectionSize,\n              x: sectionX * this._sectionSize,\n              y: sectionY * this._sectionSize\n            });\n          }\n\n          sections.push(this._sections[key]);\n        }\n      }\n\n      return sections;\n    }\n    /** Total number of Sections based on the currently registered cells. */\n\n  }, {\n    key: \"getTotalSectionCount\",\n    value: function getTotalSectionCount() {\n      return Object.keys(this._sections).length;\n    }\n    /** Intended for debugger/test purposes only */\n\n  }, {\n    key: \"toString\",\n    value: function toString() {\n      var _this = this;\n\n      return Object.keys(this._sections).map(function (index) {\n        return _this._sections[index].toString();\n      });\n    }\n    /** Adds a cell to the appropriate Sections and registers it metadata for later retrievable. */\n\n  }, {\n    key: \"registerCell\",\n    value: function registerCell(_ref4) {\n      var cellMetadatum = _ref4.cellMetadatum,\n          index = _ref4.index;\n      this._cellMetadata[index] = cellMetadatum;\n      this.getSections(cellMetadatum).forEach(function (section) {\n        return section.addCellIndex({\n          index: index\n        });\n      });\n    }\n  }]);\n\n  return SectionManager;\n}();\n\nexport { SectionManager as default };\nimport { bpfrpt_proptype_Index } from \"./types\";\nimport { bpfrpt_proptype_SizeAndPositionInfo } from \"./types\";", "/**\n * Determines a new offset that ensures a certain cell is visible, given the current offset.\n * If the cell is already visible then the current offset will be returned.\n * If the current offset is too great or small, it will be adjusted just enough to ensure the specified index is visible.\n *\n * @param align Desired alignment within container; one of \"auto\" (default), \"start\", or \"end\"\n * @param cellOffset Offset (x or y) position for cell\n * @param cellSize Size (width or height) of cell\n * @param containerSize Total size (width or height) of the container\n * @param currentOffset Container's current (x or y) offset\n * @return Offset to use to ensure the specified cell is visible\n */\nexport default function getUpdatedOffsetForIndex(_ref) {\n  var _ref$align = _ref.align,\n      align = _ref$align === void 0 ? 'auto' : _ref$align,\n      cellOffset = _ref.cellOffset,\n      cellSize = _ref.cellSize,\n      containerSize = _ref.containerSize,\n      currentOffset = _ref.currentOffset;\n  var maxOffset = cellOffset;\n  var minOffset = maxOffset - containerSize + cellSize;\n\n  switch (align) {\n    case 'start':\n      return maxOffset;\n\n    case 'end':\n      return minOffset;\n\n    case 'center':\n      return maxOffset - (containerSize - cellSize) / 2;\n\n    default:\n      return Math.max(minOffset, Math.min(maxOffset, currentOffset));\n  }\n}", "import _extends from \"@babel/runtime/helpers/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport CollectionView from './CollectionView';\nimport _calculateSizeAndPositionData from './utils/calculateSizeAndPositionData';\nimport getUpdatedOffsetForIndex from '../utils/getUpdatedOffsetForIndex';\n\n/**\n * Renders scattered or non-linear data.\n * Unlike Grid, which renders checkerboard data, Collection can render arbitrarily positioned- even overlapping- data.\n */\nvar Collection =\n/*#__PURE__*/\nfunction (_React$PureComponent) {\n  _inherits(Collection, _React$PureComponent);\n\n  function Collection(props, context) {\n    var _this;\n\n    _classCallCheck(this, Collection);\n\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(Collection).call(this, props, context));\n    _this._cellMetadata = [];\n    _this._lastRenderedCellIndices = []; // Cell cache during scroll (for performance)\n\n    _this._cellCache = [];\n    _this._isScrollingChange = _this._isScrollingChange.bind(_assertThisInitialized(_this));\n    _this._setCollectionViewRef = _this._setCollectionViewRef.bind(_assertThisInitialized(_this));\n    return _this;\n  }\n\n  _createClass(Collection, [{\n    key: \"forceUpdate\",\n    value: function forceUpdate() {\n      if (this._collectionView !== undefined) {\n        this._collectionView.forceUpdate();\n      }\n    }\n    /** See Collection#recomputeCellSizesAndPositions */\n\n  }, {\n    key: \"recomputeCellSizesAndPositions\",\n    value: function recomputeCellSizesAndPositions() {\n      this._cellCache = [];\n\n      this._collectionView.recomputeCellSizesAndPositions();\n    }\n    /** React lifecycle methods */\n\n  }, {\n    key: \"render\",\n    value: function render() {\n      var props = _extends({}, this.props);\n\n      return React.createElement(CollectionView, _extends({\n        cellLayoutManager: this,\n        isScrollingChange: this._isScrollingChange,\n        ref: this._setCollectionViewRef\n      }, props));\n    }\n    /** CellLayoutManager interface */\n\n  }, {\n    key: \"calculateSizeAndPositionData\",\n    value: function calculateSizeAndPositionData() {\n      var _this$props = this.props,\n          cellCount = _this$props.cellCount,\n          cellSizeAndPositionGetter = _this$props.cellSizeAndPositionGetter,\n          sectionSize = _this$props.sectionSize;\n\n      var data = _calculateSizeAndPositionData({\n        cellCount: cellCount,\n        cellSizeAndPositionGetter: cellSizeAndPositionGetter,\n        sectionSize: sectionSize\n      });\n\n      this._cellMetadata = data.cellMetadata;\n      this._sectionManager = data.sectionManager;\n      this._height = data.height;\n      this._width = data.width;\n    }\n    /**\n     * Returns the most recently rendered set of cell indices.\n     */\n\n  }, {\n    key: \"getLastRenderedIndices\",\n    value: function getLastRenderedIndices() {\n      return this._lastRenderedCellIndices;\n    }\n    /**\n     * Calculates the minimum amount of change from the current scroll position to ensure the specified cell is (fully) visible.\n     */\n\n  }, {\n    key: \"getScrollPositionForCell\",\n    value: function getScrollPositionForCell(_ref) {\n      var align = _ref.align,\n          cellIndex = _ref.cellIndex,\n          height = _ref.height,\n          scrollLeft = _ref.scrollLeft,\n          scrollTop = _ref.scrollTop,\n          width = _ref.width;\n      var cellCount = this.props.cellCount;\n\n      if (cellIndex >= 0 && cellIndex < cellCount) {\n        var cellMetadata = this._cellMetadata[cellIndex];\n        scrollLeft = getUpdatedOffsetForIndex({\n          align: align,\n          cellOffset: cellMetadata.x,\n          cellSize: cellMetadata.width,\n          containerSize: width,\n          currentOffset: scrollLeft,\n          targetIndex: cellIndex\n        });\n        scrollTop = getUpdatedOffsetForIndex({\n          align: align,\n          cellOffset: cellMetadata.y,\n          cellSize: cellMetadata.height,\n          containerSize: height,\n          currentOffset: scrollTop,\n          targetIndex: cellIndex\n        });\n      }\n\n      return {\n        scrollLeft: scrollLeft,\n        scrollTop: scrollTop\n      };\n    }\n  }, {\n    key: \"getTotalSize\",\n    value: function getTotalSize() {\n      return {\n        height: this._height,\n        width: this._width\n      };\n    }\n  }, {\n    key: \"cellRenderers\",\n    value: function cellRenderers(_ref2) {\n      var _this2 = this;\n\n      var height = _ref2.height,\n          isScrolling = _ref2.isScrolling,\n          width = _ref2.width,\n          x = _ref2.x,\n          y = _ref2.y;\n      var _this$props2 = this.props,\n          cellGroupRenderer = _this$props2.cellGroupRenderer,\n          cellRenderer = _this$props2.cellRenderer; // Store for later calls to getLastRenderedIndices()\n\n      this._lastRenderedCellIndices = this._sectionManager.getCellIndices({\n        height: height,\n        width: width,\n        x: x,\n        y: y\n      });\n      return cellGroupRenderer({\n        cellCache: this._cellCache,\n        cellRenderer: cellRenderer,\n        cellSizeAndPositionGetter: function cellSizeAndPositionGetter(_ref3) {\n          var index = _ref3.index;\n          return _this2._sectionManager.getCellMetadata({\n            index: index\n          });\n        },\n        indices: this._lastRenderedCellIndices,\n        isScrolling: isScrolling\n      });\n    }\n  }, {\n    key: \"_isScrollingChange\",\n    value: function _isScrollingChange(isScrolling) {\n      if (!isScrolling) {\n        this._cellCache = [];\n      }\n    }\n  }, {\n    key: \"_setCollectionViewRef\",\n    value: function _setCollectionViewRef(ref) {\n      this._collectionView = ref;\n    }\n  }]);\n\n  return Collection;\n}(React.PureComponent);\n\n_defineProperty(Collection, \"defaultProps\", {\n  'aria-label': 'grid',\n  cellGroupRenderer: defaultCellGroupRenderer\n});\n\nexport { Collection as default };\nCollection.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  'aria-label': PropTypes.string,\n\n  /**\n   * Number of cells in Collection.\n   */\n  cellCount: PropTypes.number.isRequired,\n\n  /**\n   * Responsible for rendering a group of cells given their indices.\n   * Should implement the following interface: ({\n   *   cellSizeAndPositionGetter:Function,\n   *   indices: Array<number>,\n   *   cellRenderer: Function\n   * }): Array<PropTypes.node>\n   */\n  cellGroupRenderer: PropTypes.func.isRequired,\n\n  /**\n   * Responsible for rendering a cell given an row and column index.\n   * Should implement the following interface: ({ index: number, key: string, style: object }): PropTypes.element\n   */\n  cellRenderer: PropTypes.func.isRequired,\n\n  /**\n   * Callback responsible for returning size and offset/position information for a given cell (index).\n   * ({ index: number }): { height: number, width: number, x: number, y: number }\n   */\n  cellSizeAndPositionGetter: PropTypes.func.isRequired,\n\n  /**\n   * Optionally override the size of the sections a Collection's cells are split into.\n   */\n  sectionSize: PropTypes.number\n} : {};\n\nfunction defaultCellGroupRenderer(_ref4) {\n  var cellCache = _ref4.cellCache,\n      cellRenderer = _ref4.cellRenderer,\n      cellSizeAndPositionGetter = _ref4.cellSizeAndPositionGetter,\n      indices = _ref4.indices,\n      isScrolling = _ref4.isScrolling;\n  return indices.map(function (index) {\n    var cellMetadata = cellSizeAndPositionGetter({\n      index: index\n    });\n    var cellRendererProps = {\n      index: index,\n      isScrolling: isScrolling,\n      key: index,\n      style: {\n        height: cellMetadata.height,\n        left: cellMetadata.x,\n        position: 'absolute',\n        top: cellMetadata.y,\n        width: cellMetadata.width\n      }\n    }; // Avoid re-creating cells while scrolling.\n    // This can lead to the same cell being created many times and can cause performance issues for \"heavy\" cells.\n    // If a scroll is in progress- cache and reuse cells.\n    // This cache will be thrown away once scrolling complets.\n\n    if (isScrolling) {\n      if (!(index in cellCache)) {\n        cellCache[index] = cellRenderer(cellRendererProps);\n      }\n\n      return cellCache[index];\n    } else {\n      return cellRenderer(cellRendererProps);\n    }\n  }).filter(function (renderedCell) {\n    return !!renderedCell;\n  });\n}\n\nimport { bpfrpt_proptype_ScrollPosition } from \"./types\";\nimport { bpfrpt_proptype_SizeInfo } from \"./types\";", "import SectionManager from '../SectionManager';\nexport default function calculateSizeAndPositionData(_ref) {\n  var cellCount = _ref.cellCount,\n      cellSizeAndPositionGetter = _ref.cellSizeAndPositionGetter,\n      sectionSize = _ref.sectionSize;\n  var cellMetadata = [];\n  var sectionManager = new SectionManager(sectionSize);\n  var height = 0;\n  var width = 0;\n\n  for (var index = 0; index < cellCount; index++) {\n    var cellMetadatum = cellSizeAndPositionGetter({\n      index: index\n    });\n\n    if (cellMetadatum.height == null || isNaN(cellMetadatum.height) || cellMetadatum.width == null || isNaN(cellMetadatum.width) || cellMetadatum.x == null || isNaN(cellMetadatum.x) || cellMetadatum.y == null || isNaN(cellMetadatum.y)) {\n      throw Error(\"Invalid metadata returned for cell \".concat(index, \":\\n        x:\").concat(cellMetadatum.x, \", y:\").concat(cellMetadatum.y, \", width:\").concat(cellMetadatum.width, \", height:\").concat(cellMetadatum.height));\n    }\n\n    height = Math.max(height, cellMetadatum.y + cellMetadatum.height);\n    width = Math.max(width, cellMetadatum.x + cellMetadatum.width);\n    cellMetadata[index] = cellMetadatum;\n    sectionManager.registerCell({\n      cellMetadatum: cellMetadatum,\n      index: index\n    });\n  }\n\n  return {\n    cellMetadata: cellMetadata,\n    height: height,\n    sectionManager: sectionManager,\n    width: width\n  };\n}", "import Collection from './Collection';\nexport default Collection;\nexport { Collection };", "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\n/**\n * High-order component that auto-calculates column-widths for `Grid` cells.\n */\n\nvar ColumnSizer =\n/*#__PURE__*/\nfunction (_React$PureComponent) {\n  _inherits(ColumnSizer, _React$PureComponent);\n\n  function ColumnSizer(props, context) {\n    var _this;\n\n    _classCallCheck(this, ColumnSizer);\n\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(ColumnSizer).call(this, props, context));\n    _this._registerChild = _this._registerChild.bind(_assertThisInitialized(_this));\n    return _this;\n  }\n\n  _createClass(ColumnSizer, [{\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var _this$props = this.props,\n          columnMaxWidth = _this$props.columnMaxWidth,\n          columnMinWidth = _this$props.columnMinWidth,\n          columnCount = _this$props.columnCount,\n          width = _this$props.width;\n\n      if (columnMaxWidth !== prevProps.columnMaxWidth || columnMinWidth !== prevProps.columnMinWidth || columnCount !== prevProps.columnCount || width !== prevProps.width) {\n        if (this._registeredChild) {\n          this._registeredChild.recomputeGridSize();\n        }\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props2 = this.props,\n          children = _this$props2.children,\n          columnMaxWidth = _this$props2.columnMaxWidth,\n          columnMinWidth = _this$props2.columnMinWidth,\n          columnCount = _this$props2.columnCount,\n          width = _this$props2.width;\n      var safeColumnMinWidth = columnMinWidth || 1;\n      var safeColumnMaxWidth = columnMaxWidth ? Math.min(columnMaxWidth, width) : width;\n      var columnWidth = width / columnCount;\n      columnWidth = Math.max(safeColumnMinWidth, columnWidth);\n      columnWidth = Math.min(safeColumnMaxWidth, columnWidth);\n      columnWidth = Math.floor(columnWidth);\n      var adjustedWidth = Math.min(width, columnWidth * columnCount);\n      return children({\n        adjustedWidth: adjustedWidth,\n        columnWidth: columnWidth,\n        getColumnWidth: function getColumnWidth() {\n          return columnWidth;\n        },\n        registerChild: this._registerChild\n      });\n    }\n  }, {\n    key: \"_registerChild\",\n    value: function _registerChild(child) {\n      if (child && typeof child.recomputeGridSize !== 'function') {\n        throw Error('Unexpected child type registered; only Grid/MultiGrid children are supported.');\n      }\n\n      this._registeredChild = child;\n\n      if (this._registeredChild) {\n        this._registeredChild.recomputeGridSize();\n      }\n    }\n  }]);\n\n  return ColumnSizer;\n}(React.PureComponent);\n\nexport { ColumnSizer as default };\nColumnSizer.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  /**\n   * Function responsible for rendering a virtualized Grid.\n   * This function should implement the following signature:\n   * ({ adjustedWidth, getColumnWidth, registerChild }) => PropTypes.element\n   *\n   * The specified :getColumnWidth function should be passed to the Grid's :columnWidth property.\n   * The :registerChild should be passed to the Grid's :ref property.\n   * The :adjustedWidth property is optional; it reflects the lesser of the overall width or the width of all columns.\n   */\n  children: PropTypes.func.isRequired,\n\n  /** Optional maximum allowed column width */\n  columnMaxWidth: PropTypes.number,\n\n  /** Optional minimum allowed column width */\n  columnMinWidth: PropTypes.number,\n\n  /** Number of columns in Grid or Table child */\n  columnCount: PropTypes.number.isRequired,\n\n  /** Width of Grid or Table child */\n  width: PropTypes.number.isRequired\n} : {};", "import ColumnSizer from './ColumnSizer';\nexport default ColumnSizer;\nexport { ColumnSizer };", "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport createCallbackMemoizer from '../utils/createCallbackMemoizer';\n/**\n * Higher-order component that manages lazy-loading for \"infinite\" data.\n * This component decorates a virtual component and just-in-time prefetches rows as a user scrolls.\n * It is intended as a convenience component; fork it if you'd like finer-grained control over data-loading.\n */\n\nvar InfiniteLoader =\n/*#__PURE__*/\nfunction (_React$PureComponent) {\n  _inherits(InfiniteLoader, _React$PureComponent);\n\n  function InfiniteLoader(props, context) {\n    var _this;\n\n    _classCallCheck(this, InfiniteLoader);\n\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(InfiniteLoader).call(this, props, context));\n    _this._loadMoreRowsMemoizer = createCallbackMemoizer();\n    _this._onRowsRendered = _this._onRowsRendered.bind(_assertThisInitialized(_this));\n    _this._registerChild = _this._registerChild.bind(_assertThisInitialized(_this));\n    return _this;\n  }\n\n  _createClass(InfiniteLoader, [{\n    key: \"resetLoadMoreRowsCache\",\n    value: function resetLoadMoreRowsCache(autoReload) {\n      this._loadMoreRowsMemoizer = createCallbackMemoizer();\n\n      if (autoReload) {\n        this._doStuff(this._lastRenderedStartIndex, this._lastRenderedStopIndex);\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var children = this.props.children;\n      return children({\n        onRowsRendered: this._onRowsRendered,\n        registerChild: this._registerChild\n      });\n    }\n  }, {\n    key: \"_loadUnloadedRanges\",\n    value: function _loadUnloadedRanges(unloadedRanges) {\n      var _this2 = this;\n\n      var loadMoreRows = this.props.loadMoreRows;\n      unloadedRanges.forEach(function (unloadedRange) {\n        var promise = loadMoreRows(unloadedRange);\n\n        if (promise) {\n          promise.then(function () {\n            // Refresh the visible rows if any of them have just been loaded.\n            // Otherwise they will remain in their unloaded visual state.\n            if (isRangeVisible({\n              lastRenderedStartIndex: _this2._lastRenderedStartIndex,\n              lastRenderedStopIndex: _this2._lastRenderedStopIndex,\n              startIndex: unloadedRange.startIndex,\n              stopIndex: unloadedRange.stopIndex\n            })) {\n              if (_this2._registeredChild) {\n                forceUpdateReactVirtualizedComponent(_this2._registeredChild, _this2._lastRenderedStartIndex);\n              }\n            }\n          });\n        }\n      });\n    }\n  }, {\n    key: \"_onRowsRendered\",\n    value: function _onRowsRendered(_ref) {\n      var startIndex = _ref.startIndex,\n          stopIndex = _ref.stopIndex;\n      this._lastRenderedStartIndex = startIndex;\n      this._lastRenderedStopIndex = stopIndex;\n\n      this._doStuff(startIndex, stopIndex);\n    }\n  }, {\n    key: \"_doStuff\",\n    value: function _doStuff(startIndex, stopIndex) {\n      var _ref2,\n          _this3 = this;\n\n      var _this$props = this.props,\n          isRowLoaded = _this$props.isRowLoaded,\n          minimumBatchSize = _this$props.minimumBatchSize,\n          rowCount = _this$props.rowCount,\n          threshold = _this$props.threshold;\n      var unloadedRanges = scanForUnloadedRanges({\n        isRowLoaded: isRowLoaded,\n        minimumBatchSize: minimumBatchSize,\n        rowCount: rowCount,\n        startIndex: Math.max(0, startIndex - threshold),\n        stopIndex: Math.min(rowCount - 1, stopIndex + threshold)\n      }); // For memoize comparison\n\n      var squashedUnloadedRanges = (_ref2 = []).concat.apply(_ref2, _toConsumableArray(unloadedRanges.map(function (_ref3) {\n        var startIndex = _ref3.startIndex,\n            stopIndex = _ref3.stopIndex;\n        return [startIndex, stopIndex];\n      })));\n\n      this._loadMoreRowsMemoizer({\n        callback: function callback() {\n          _this3._loadUnloadedRanges(unloadedRanges);\n        },\n        indices: {\n          squashedUnloadedRanges: squashedUnloadedRanges\n        }\n      });\n    }\n  }, {\n    key: \"_registerChild\",\n    value: function _registerChild(registeredChild) {\n      this._registeredChild = registeredChild;\n    }\n  }]);\n\n  return InfiniteLoader;\n}(React.PureComponent);\n/**\n * Determines if the specified start/stop range is visible based on the most recently rendered range.\n */\n\n\n_defineProperty(InfiniteLoader, \"defaultProps\", {\n  minimumBatchSize: 10,\n  rowCount: 0,\n  threshold: 15\n});\n\nexport { InfiniteLoader as default };\nInfiniteLoader.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  /**\n   * Function responsible for rendering a virtualized component.\n   * This function should implement the following signature:\n   * ({ onRowsRendered, registerChild }) => PropTypes.element\n   *\n   * The specified :onRowsRendered function should be passed through to the child's :onRowsRendered property.\n   * The :registerChild callback should be set as the virtualized component's :ref.\n   */\n  children: PropTypes.func.isRequired,\n\n  /**\n   * Function responsible for tracking the loaded state of each row.\n   * It should implement the following signature: ({ index: number }): boolean\n   */\n  isRowLoaded: PropTypes.func.isRequired,\n\n  /**\n   * Callback to be invoked when more rows must be loaded.\n   * It should implement the following signature: ({ startIndex, stopIndex }): Promise\n   * The returned Promise should be resolved once row data has finished loading.\n   * It will be used to determine when to refresh the list with the newly-loaded data.\n   * This callback may be called multiple times in reaction to a single scroll event.\n   */\n  loadMoreRows: PropTypes.func.isRequired,\n\n  /**\n   * Minimum number of rows to be loaded at a time.\n   * This property can be used to batch requests to reduce HTTP requests.\n   */\n  minimumBatchSize: PropTypes.number.isRequired,\n\n  /**\n   * Number of rows in list; can be arbitrary high number if actual number is unknown.\n   */\n  rowCount: PropTypes.number.isRequired,\n\n  /**\n   * Threshold at which to pre-fetch data.\n   * A threshold X means that data will start loading when a user scrolls within X rows.\n   * This value defaults to 15.\n   */\n  threshold: PropTypes.number.isRequired\n} : {};\nexport function isRangeVisible(_ref4) {\n  var lastRenderedStartIndex = _ref4.lastRenderedStartIndex,\n      lastRenderedStopIndex = _ref4.lastRenderedStopIndex,\n      startIndex = _ref4.startIndex,\n      stopIndex = _ref4.stopIndex;\n  return !(startIndex > lastRenderedStopIndex || stopIndex < lastRenderedStartIndex);\n}\n/**\n * Returns all of the ranges within a larger range that contain unloaded rows.\n */\n\nexport function scanForUnloadedRanges(_ref5) {\n  var isRowLoaded = _ref5.isRowLoaded,\n      minimumBatchSize = _ref5.minimumBatchSize,\n      rowCount = _ref5.rowCount,\n      startIndex = _ref5.startIndex,\n      stopIndex = _ref5.stopIndex;\n  var unloadedRanges = [];\n  var rangeStartIndex = null;\n  var rangeStopIndex = null;\n\n  for (var index = startIndex; index <= stopIndex; index++) {\n    var loaded = isRowLoaded({\n      index: index\n    });\n\n    if (!loaded) {\n      rangeStopIndex = index;\n\n      if (rangeStartIndex === null) {\n        rangeStartIndex = index;\n      }\n    } else if (rangeStopIndex !== null) {\n      unloadedRanges.push({\n        startIndex: rangeStartIndex,\n        stopIndex: rangeStopIndex\n      });\n      rangeStartIndex = rangeStopIndex = null;\n    }\n  } // If :rangeStopIndex is not null it means we haven't ran out of unloaded rows.\n  // Scan forward to try filling our :minimumBatchSize.\n\n\n  if (rangeStopIndex !== null) {\n    var potentialStopIndex = Math.min(Math.max(rangeStopIndex, rangeStartIndex + minimumBatchSize - 1), rowCount - 1);\n\n    for (var _index = rangeStopIndex + 1; _index <= potentialStopIndex; _index++) {\n      if (!isRowLoaded({\n        index: _index\n      })) {\n        rangeStopIndex = _index;\n      } else {\n        break;\n      }\n    }\n\n    unloadedRanges.push({\n      startIndex: rangeStartIndex,\n      stopIndex: rangeStopIndex\n    });\n  } // Check to see if our first range ended prematurely.\n  // In this case we should scan backwards to try filling our :minimumBatchSize.\n\n\n  if (unloadedRanges.length) {\n    var firstUnloadedRange = unloadedRanges[0];\n\n    while (firstUnloadedRange.stopIndex - firstUnloadedRange.startIndex + 1 < minimumBatchSize && firstUnloadedRange.startIndex > 0) {\n      var _index2 = firstUnloadedRange.startIndex - 1;\n\n      if (!isRowLoaded({\n        index: _index2\n      })) {\n        firstUnloadedRange.startIndex = _index2;\n      } else {\n        break;\n      }\n    }\n  }\n\n  return unloadedRanges;\n}\n/**\n * Since RV components use shallowCompare we need to force a render (even though props haven't changed).\n * However InfiniteLoader may wrap a Grid or it may wrap a Table or List.\n * In the first case the built-in React forceUpdate() method is sufficient to force a re-render,\n * But in the latter cases we need to use the RV-specific forceUpdateGrid() method.\n * Else the inner Grid will not be re-rendered and visuals may be stale.\n *\n * Additionally, while a Grid is scrolling the cells can be cached,\n * So it's important to invalidate that cache by recalculating sizes\n * before forcing a rerender.\n */\n\nexport function forceUpdateReactVirtualizedComponent(component) {\n  var currentIndex = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var recomputeSize = typeof component.recomputeGridSize === 'function' ? component.recomputeGridSize : component.recomputeRowHeights;\n\n  if (recomputeSize) {\n    recomputeSize.call(component, currentIndex);\n  } else {\n    component.forceUpdate();\n  }\n}", "import InfiniteLoader from './InfiniteLoader';\nexport default InfiniteLoader;\nexport { InfiniteLoader };", "import _extends from \"@babel/runtime/helpers/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\n\nvar _class, _temp;\n\nimport Grid, { accessibilityOverscanIndicesGetter } from '../Grid';\nimport * as React from 'react';\nimport clsx from 'clsx';\n/**\n * It is inefficient to create and manage a large list of DOM elements within a scrolling container\n * if only a few of those elements are visible. The primary purpose of this component is to improve\n * performance by only rendering the DOM nodes that a user is able to see based on their current\n * scroll position.\n *\n * This component renders a virtualized list of elements with either fixed or dynamic heights.\n */\n\nvar List = (_temp = _class =\n/*#__PURE__*/\nfunction (_React$PureComponent) {\n  _inherits(List, _React$PureComponent);\n\n  function List() {\n    var _getPrototypeOf2;\n\n    var _this;\n\n    _classCallCheck(this, List);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _possibleConstructorReturn(this, (_getPrototypeOf2 = _getPrototypeOf(List)).call.apply(_getPrototypeOf2, [this].concat(args)));\n\n    _defineProperty(_assertThisInitialized(_this), \"Grid\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_cellRenderer\", function (_ref) {\n      var parent = _ref.parent,\n          rowIndex = _ref.rowIndex,\n          style = _ref.style,\n          isScrolling = _ref.isScrolling,\n          isVisible = _ref.isVisible,\n          key = _ref.key;\n      var rowRenderer = _this.props.rowRenderer; // TRICKY The style object is sometimes cached by Grid.\n      // This prevents new style objects from bypassing shallowCompare().\n      // However as of React 16, style props are auto-frozen (at least in dev mode)\n      // Check to make sure we can still modify the style before proceeding.\n      // https://github.com/facebook/react/commit/977357765b44af8ff0cfea327866861073095c12#commitcomment-20648713\n\n      var widthDescriptor = Object.getOwnPropertyDescriptor(style, 'width');\n\n      if (widthDescriptor && widthDescriptor.writable) {\n        // By default, List cells should be 100% width.\n        // This prevents them from flowing under a scrollbar (if present).\n        style.width = '100%';\n      }\n\n      return rowRenderer({\n        index: rowIndex,\n        style: style,\n        isScrolling: isScrolling,\n        isVisible: isVisible,\n        key: key,\n        parent: parent\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_setRef\", function (ref) {\n      _this.Grid = ref;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_onScroll\", function (_ref2) {\n      var clientHeight = _ref2.clientHeight,\n          scrollHeight = _ref2.scrollHeight,\n          scrollTop = _ref2.scrollTop;\n      var onScroll = _this.props.onScroll;\n      onScroll({\n        clientHeight: clientHeight,\n        scrollHeight: scrollHeight,\n        scrollTop: scrollTop\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_onSectionRendered\", function (_ref3) {\n      var rowOverscanStartIndex = _ref3.rowOverscanStartIndex,\n          rowOverscanStopIndex = _ref3.rowOverscanStopIndex,\n          rowStartIndex = _ref3.rowStartIndex,\n          rowStopIndex = _ref3.rowStopIndex;\n      var onRowsRendered = _this.props.onRowsRendered;\n      onRowsRendered({\n        overscanStartIndex: rowOverscanStartIndex,\n        overscanStopIndex: rowOverscanStopIndex,\n        startIndex: rowStartIndex,\n        stopIndex: rowStopIndex\n      });\n    });\n\n    return _this;\n  }\n\n  _createClass(List, [{\n    key: \"forceUpdateGrid\",\n    value: function forceUpdateGrid() {\n      if (this.Grid) {\n        this.Grid.forceUpdate();\n      }\n    }\n    /** See Grid#getOffsetForCell */\n\n  }, {\n    key: \"getOffsetForRow\",\n    value: function getOffsetForRow(_ref4) {\n      var alignment = _ref4.alignment,\n          index = _ref4.index;\n\n      if (this.Grid) {\n        var _this$Grid$getOffsetF = this.Grid.getOffsetForCell({\n          alignment: alignment,\n          rowIndex: index,\n          columnIndex: 0\n        }),\n            scrollTop = _this$Grid$getOffsetF.scrollTop;\n\n        return scrollTop;\n      }\n\n      return 0;\n    }\n    /** CellMeasurer compatibility */\n\n  }, {\n    key: \"invalidateCellSizeAfterRender\",\n    value: function invalidateCellSizeAfterRender(_ref5) {\n      var columnIndex = _ref5.columnIndex,\n          rowIndex = _ref5.rowIndex;\n\n      if (this.Grid) {\n        this.Grid.invalidateCellSizeAfterRender({\n          rowIndex: rowIndex,\n          columnIndex: columnIndex\n        });\n      }\n    }\n    /** See Grid#measureAllCells */\n\n  }, {\n    key: \"measureAllRows\",\n    value: function measureAllRows() {\n      if (this.Grid) {\n        this.Grid.measureAllCells();\n      }\n    }\n    /** CellMeasurer compatibility */\n\n  }, {\n    key: \"recomputeGridSize\",\n    value: function recomputeGridSize() {\n      var _ref6 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref6$columnIndex = _ref6.columnIndex,\n          columnIndex = _ref6$columnIndex === void 0 ? 0 : _ref6$columnIndex,\n          _ref6$rowIndex = _ref6.rowIndex,\n          rowIndex = _ref6$rowIndex === void 0 ? 0 : _ref6$rowIndex;\n\n      if (this.Grid) {\n        this.Grid.recomputeGridSize({\n          rowIndex: rowIndex,\n          columnIndex: columnIndex\n        });\n      }\n    }\n    /** See Grid#recomputeGridSize */\n\n  }, {\n    key: \"recomputeRowHeights\",\n    value: function recomputeRowHeights() {\n      var index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n\n      if (this.Grid) {\n        this.Grid.recomputeGridSize({\n          rowIndex: index,\n          columnIndex: 0\n        });\n      }\n    }\n    /** See Grid#scrollToPosition */\n\n  }, {\n    key: \"scrollToPosition\",\n    value: function scrollToPosition() {\n      var scrollTop = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n\n      if (this.Grid) {\n        this.Grid.scrollToPosition({\n          scrollTop: scrollTop\n        });\n      }\n    }\n    /** See Grid#scrollToCell */\n\n  }, {\n    key: \"scrollToRow\",\n    value: function scrollToRow() {\n      var index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n\n      if (this.Grid) {\n        this.Grid.scrollToCell({\n          columnIndex: 0,\n          rowIndex: index\n        });\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n          className = _this$props.className,\n          noRowsRenderer = _this$props.noRowsRenderer,\n          scrollToIndex = _this$props.scrollToIndex,\n          width = _this$props.width;\n      var classNames = clsx('ReactVirtualized__List', className);\n      return React.createElement(Grid, _extends({}, this.props, {\n        autoContainerWidth: true,\n        cellRenderer: this._cellRenderer,\n        className: classNames,\n        columnWidth: width,\n        columnCount: 1,\n        noContentRenderer: noRowsRenderer,\n        onScroll: this._onScroll,\n        onSectionRendered: this._onSectionRendered,\n        ref: this._setRef,\n        scrollToRow: scrollToIndex\n      }));\n    }\n  }]);\n\n  return List;\n}(React.PureComponent), _defineProperty(_class, \"propTypes\", process.env.NODE_ENV === 'production' ? null : {\n  \"aria-label\": PropTypes.string,\n\n  /**\n   * Removes fixed height from the scrollingContainer so that the total height\n   * of rows can stretch the window. Intended for use with WindowScroller\n   */\n  \"autoHeight\": PropTypes.bool.isRequired,\n\n  /** Optional CSS class name */\n  \"className\": PropTypes.string,\n\n  /**\n   * Used to estimate the total height of a List before all of its rows have actually been measured.\n   * The estimated total height is adjusted as rows are rendered.\n   */\n  \"estimatedRowSize\": PropTypes.number.isRequired,\n\n  /** Height constraint for list (determines how many actual rows are rendered) */\n  \"height\": PropTypes.number.isRequired,\n\n  /** Optional renderer to be used in place of rows when rowCount is 0 */\n  \"noRowsRenderer\": function noRowsRenderer() {\n    return (typeof bpfrpt_proptype_NoContentRenderer === \"function\" ? bpfrpt_proptype_NoContentRenderer.isRequired ? bpfrpt_proptype_NoContentRenderer.isRequired : bpfrpt_proptype_NoContentRenderer : PropTypes.shape(bpfrpt_proptype_NoContentRenderer).isRequired).apply(this, arguments);\n  },\n\n  /** Callback invoked with information about the slice of rows that were just rendered.  */\n  \"onRowsRendered\": PropTypes.func.isRequired,\n\n  /**\n   * Callback invoked whenever the scroll offset changes within the inner scrollable region.\n   * This callback can be used to sync scrolling between lists, tables, or grids.\n   */\n  \"onScroll\": PropTypes.func.isRequired,\n\n  /** See Grid#overscanIndicesGetter */\n  \"overscanIndicesGetter\": function overscanIndicesGetter() {\n    return (typeof bpfrpt_proptype_OverscanIndicesGetter === \"function\" ? bpfrpt_proptype_OverscanIndicesGetter.isRequired ? bpfrpt_proptype_OverscanIndicesGetter.isRequired : bpfrpt_proptype_OverscanIndicesGetter : PropTypes.shape(bpfrpt_proptype_OverscanIndicesGetter).isRequired).apply(this, arguments);\n  },\n\n  /**\n   * Number of rows to render above/below the visible bounds of the list.\n   * These rows can help for smoother scrolling on touch devices.\n   */\n  \"overscanRowCount\": PropTypes.number.isRequired,\n\n  /** Either a fixed row height (number) or a function that returns the height of a row given its index.  */\n  \"rowHeight\": function rowHeight() {\n    return (typeof bpfrpt_proptype_CellSize === \"function\" ? bpfrpt_proptype_CellSize.isRequired ? bpfrpt_proptype_CellSize.isRequired : bpfrpt_proptype_CellSize : PropTypes.shape(bpfrpt_proptype_CellSize).isRequired).apply(this, arguments);\n  },\n\n  /** Responsible for rendering a row given an index; ({ index: number }): node */\n  \"rowRenderer\": function rowRenderer() {\n    return (typeof bpfrpt_proptype_RowRenderer === \"function\" ? bpfrpt_proptype_RowRenderer.isRequired ? bpfrpt_proptype_RowRenderer.isRequired : bpfrpt_proptype_RowRenderer : PropTypes.shape(bpfrpt_proptype_RowRenderer).isRequired).apply(this, arguments);\n  },\n\n  /** Number of rows in list. */\n  \"rowCount\": PropTypes.number.isRequired,\n\n  /** See Grid#scrollToAlignment */\n  \"scrollToAlignment\": function scrollToAlignment() {\n    return (typeof bpfrpt_proptype_Alignment === \"function\" ? bpfrpt_proptype_Alignment.isRequired ? bpfrpt_proptype_Alignment.isRequired : bpfrpt_proptype_Alignment : PropTypes.shape(bpfrpt_proptype_Alignment).isRequired).apply(this, arguments);\n  },\n\n  /** Row index to ensure visible (by forcefully scrolling if necessary) */\n  \"scrollToIndex\": PropTypes.number.isRequired,\n\n  /** Vertical offset. */\n  \"scrollTop\": PropTypes.number,\n\n  /** Optional inline style */\n  \"style\": PropTypes.object.isRequired,\n\n  /** Tab index for focus */\n  \"tabIndex\": PropTypes.number,\n\n  /** Width of list */\n  \"width\": PropTypes.number.isRequired\n}), _temp);\n\n_defineProperty(List, \"defaultProps\", {\n  autoHeight: false,\n  estimatedRowSize: 30,\n  onScroll: function onScroll() {},\n  noRowsRenderer: function noRowsRenderer() {\n    return null;\n  },\n  onRowsRendered: function onRowsRendered() {},\n  overscanIndicesGetter: accessibilityOverscanIndicesGetter,\n  overscanRowCount: 10,\n  scrollToAlignment: 'auto',\n  scrollToIndex: -1,\n  style: {}\n});\n\nexport { List as default };\nimport { bpfrpt_proptype_NoContentRenderer } from \"../Grid\";\nimport { bpfrpt_proptype_Alignment } from \"../Grid\";\nimport { bpfrpt_proptype_CellSize } from \"../Grid\";\nimport { bpfrpt_proptype_CellPosition } from \"../Grid\";\nimport { bpfrpt_proptype_OverscanIndicesGetter } from \"../Grid\";\nimport { bpfrpt_proptype_RenderedSection } from \"../Grid\";\nimport { bpfrpt_proptype_CellRendererParams } from \"../Grid\";\nimport { bpfrpt_proptype_Scroll as bpfrpt_proptype_GridScroll } from \"../Grid\";\nimport { bpfrpt_proptype_RowRenderer } from \"./types\";\nimport { bpfrpt_proptype_RenderedRows } from \"./types\";\nimport { bpfrpt_proptype_Scroll } from \"./types\";\nimport PropTypes from \"prop-types\";", "/**\n * Binary Search Bounds\n * https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/binary-search-bounds\n * <PERSON><PERSON><PERSON>\n *\n * Inlined because of Content Security Policy issue caused by the use of `new Function(...)` syntax.\n * Issue reported here: https://github.com/mikolal<PERSON>enko/binary-search-bounds/issues/5\n **/\nfunction _GEA(a, l, h, y) {\n  var i = h + 1;\n\n  while (l <= h) {\n    var m = l + h >>> 1,\n        x = a[m];\n\n    if (x >= y) {\n      i = m;\n      h = m - 1;\n    } else {\n      l = m + 1;\n    }\n  }\n\n  return i;\n}\n\nfunction _GEP(a, l, h, y, c) {\n  var i = h + 1;\n\n  while (l <= h) {\n    var m = l + h >>> 1,\n        x = a[m];\n\n    if (c(x, y) >= 0) {\n      i = m;\n      h = m - 1;\n    } else {\n      l = m + 1;\n    }\n  }\n\n  return i;\n}\n\nfunction dispatchBsearchGE(a, y, c, l, h) {\n  if (typeof c === 'function') {\n    return _GEP(a, l === void 0 ? 0 : l | 0, h === void 0 ? a.length - 1 : h | 0, y, c);\n  } else {\n    return _GEA(a, c === void 0 ? 0 : c | 0, l === void 0 ? a.length - 1 : l | 0, y);\n  }\n}\n\nfunction _GTA(a, l, h, y) {\n  var i = h + 1;\n\n  while (l <= h) {\n    var m = l + h >>> 1,\n        x = a[m];\n\n    if (x > y) {\n      i = m;\n      h = m - 1;\n    } else {\n      l = m + 1;\n    }\n  }\n\n  return i;\n}\n\nfunction _GTP(a, l, h, y, c) {\n  var i = h + 1;\n\n  while (l <= h) {\n    var m = l + h >>> 1,\n        x = a[m];\n\n    if (c(x, y) > 0) {\n      i = m;\n      h = m - 1;\n    } else {\n      l = m + 1;\n    }\n  }\n\n  return i;\n}\n\nfunction dispatchBsearchGT(a, y, c, l, h) {\n  if (typeof c === 'function') {\n    return _GTP(a, l === void 0 ? 0 : l | 0, h === void 0 ? a.length - 1 : h | 0, y, c);\n  } else {\n    return _GTA(a, c === void 0 ? 0 : c | 0, l === void 0 ? a.length - 1 : l | 0, y);\n  }\n}\n\nfunction _LTA(a, l, h, y) {\n  var i = l - 1;\n\n  while (l <= h) {\n    var m = l + h >>> 1,\n        x = a[m];\n\n    if (x < y) {\n      i = m;\n      l = m + 1;\n    } else {\n      h = m - 1;\n    }\n  }\n\n  return i;\n}\n\nfunction _LTP(a, l, h, y, c) {\n  var i = l - 1;\n\n  while (l <= h) {\n    var m = l + h >>> 1,\n        x = a[m];\n\n    if (c(x, y) < 0) {\n      i = m;\n      l = m + 1;\n    } else {\n      h = m - 1;\n    }\n  }\n\n  return i;\n}\n\nfunction dispatchBsearchLT(a, y, c, l, h) {\n  if (typeof c === 'function') {\n    return _LTP(a, l === void 0 ? 0 : l | 0, h === void 0 ? a.length - 1 : h | 0, y, c);\n  } else {\n    return _LTA(a, c === void 0 ? 0 : c | 0, l === void 0 ? a.length - 1 : l | 0, y);\n  }\n}\n\nfunction _LEA(a, l, h, y) {\n  var i = l - 1;\n\n  while (l <= h) {\n    var m = l + h >>> 1,\n        x = a[m];\n\n    if (x <= y) {\n      i = m;\n      l = m + 1;\n    } else {\n      h = m - 1;\n    }\n  }\n\n  return i;\n}\n\nfunction _LEP(a, l, h, y, c) {\n  var i = l - 1;\n\n  while (l <= h) {\n    var m = l + h >>> 1,\n        x = a[m];\n\n    if (c(x, y) <= 0) {\n      i = m;\n      l = m + 1;\n    } else {\n      h = m - 1;\n    }\n  }\n\n  return i;\n}\n\nfunction dispatchBsearchLE(a, y, c, l, h) {\n  if (typeof c === 'function') {\n    return _LEP(a, l === void 0 ? 0 : l | 0, h === void 0 ? a.length - 1 : h | 0, y, c);\n  } else {\n    return _LEA(a, c === void 0 ? 0 : c | 0, l === void 0 ? a.length - 1 : l | 0, y);\n  }\n}\n\nfunction _EQA(a, l, h, y) {\n  l - 1;\n\n  while (l <= h) {\n    var m = l + h >>> 1,\n        x = a[m];\n\n    if (x === y) {\n      return m;\n    } else if (x <= y) {\n      l = m + 1;\n    } else {\n      h = m - 1;\n    }\n  }\n\n  return -1;\n}\n\nfunction _EQP(a, l, h, y, c) {\n  l - 1;\n\n  while (l <= h) {\n    var m = l + h >>> 1,\n        x = a[m];\n    var p = c(x, y);\n\n    if (p === 0) {\n      return m;\n    } else if (p <= 0) {\n      l = m + 1;\n    } else {\n      h = m - 1;\n    }\n  }\n\n  return -1;\n}\n\nfunction dispatchBsearchEQ(a, y, c, l, h) {\n  if (typeof c === 'function') {\n    return _EQP(a, l === void 0 ? 0 : l | 0, h === void 0 ? a.length - 1 : h | 0, y, c);\n  } else {\n    return _EQA(a, c === void 0 ? 0 : c | 0, l === void 0 ? a.length - 1 : l | 0, y);\n  }\n}\n\nexport default {\n  ge: dispatchBsearchGE,\n  gt: dispatchBsearchGT,\n  lt: dispatchBsearchLT,\n  le: dispatchBsearchLE,\n  eq: dispatchBsearchEQ\n};", "/**\n * Binary Search Bounds\n * https://github.com/miko<PERSON><PERSON><PERSON>/interval-tree-1d\n * <PERSON><PERSON><PERSON>\n *\n * Inlined because of Content Security Policy issue caused by the use of `new Function(...)` syntax in an upstream dependency.\n * Issue reported here: https://github.com/miko<PERSON><PERSON><PERSON>/binary-search-bounds/issues/5\n **/\nimport bounds from './binarySearchBounds';\nvar NOT_FOUND = 0;\nvar SUCCESS = 1;\nvar EMPTY = 2;\n\nfunction IntervalTreeNode(mid, left, right, leftPoints, rightPoints) {\n  this.mid = mid;\n  this.left = left;\n  this.right = right;\n  this.leftPoints = leftPoints;\n  this.rightPoints = rightPoints;\n  this.count = (left ? left.count : 0) + (right ? right.count : 0) + leftPoints.length;\n}\n\nvar proto = IntervalTreeNode.prototype;\n\nfunction copy(a, b) {\n  a.mid = b.mid;\n  a.left = b.left;\n  a.right = b.right;\n  a.leftPoints = b.leftPoints;\n  a.rightPoints = b.rightPoints;\n  a.count = b.count;\n}\n\nfunction rebuild(node, intervals) {\n  var ntree = createIntervalTree(intervals);\n  node.mid = ntree.mid;\n  node.left = ntree.left;\n  node.right = ntree.right;\n  node.leftPoints = ntree.leftPoints;\n  node.rightPoints = ntree.rightPoints;\n  node.count = ntree.count;\n}\n\nfunction rebuildWithInterval(node, interval) {\n  var intervals = node.intervals([]);\n  intervals.push(interval);\n  rebuild(node, intervals);\n}\n\nfunction rebuildWithoutInterval(node, interval) {\n  var intervals = node.intervals([]);\n  var idx = intervals.indexOf(interval);\n\n  if (idx < 0) {\n    return NOT_FOUND;\n  }\n\n  intervals.splice(idx, 1);\n  rebuild(node, intervals);\n  return SUCCESS;\n}\n\nproto.intervals = function (result) {\n  result.push.apply(result, this.leftPoints);\n\n  if (this.left) {\n    this.left.intervals(result);\n  }\n\n  if (this.right) {\n    this.right.intervals(result);\n  }\n\n  return result;\n};\n\nproto.insert = function (interval) {\n  var weight = this.count - this.leftPoints.length;\n  this.count += 1;\n\n  if (interval[1] < this.mid) {\n    if (this.left) {\n      if (4 * (this.left.count + 1) > 3 * (weight + 1)) {\n        rebuildWithInterval(this, interval);\n      } else {\n        this.left.insert(interval);\n      }\n    } else {\n      this.left = createIntervalTree([interval]);\n    }\n  } else if (interval[0] > this.mid) {\n    if (this.right) {\n      if (4 * (this.right.count + 1) > 3 * (weight + 1)) {\n        rebuildWithInterval(this, interval);\n      } else {\n        this.right.insert(interval);\n      }\n    } else {\n      this.right = createIntervalTree([interval]);\n    }\n  } else {\n    var l = bounds.ge(this.leftPoints, interval, compareBegin);\n    var r = bounds.ge(this.rightPoints, interval, compareEnd);\n    this.leftPoints.splice(l, 0, interval);\n    this.rightPoints.splice(r, 0, interval);\n  }\n};\n\nproto.remove = function (interval) {\n  var weight = this.count - this.leftPoints;\n\n  if (interval[1] < this.mid) {\n    if (!this.left) {\n      return NOT_FOUND;\n    }\n\n    var rw = this.right ? this.right.count : 0;\n\n    if (4 * rw > 3 * (weight - 1)) {\n      return rebuildWithoutInterval(this, interval);\n    }\n\n    var r = this.left.remove(interval);\n\n    if (r === EMPTY) {\n      this.left = null;\n      this.count -= 1;\n      return SUCCESS;\n    } else if (r === SUCCESS) {\n      this.count -= 1;\n    }\n\n    return r;\n  } else if (interval[0] > this.mid) {\n    if (!this.right) {\n      return NOT_FOUND;\n    }\n\n    var lw = this.left ? this.left.count : 0;\n\n    if (4 * lw > 3 * (weight - 1)) {\n      return rebuildWithoutInterval(this, interval);\n    }\n\n    var r = this.right.remove(interval);\n\n    if (r === EMPTY) {\n      this.right = null;\n      this.count -= 1;\n      return SUCCESS;\n    } else if (r === SUCCESS) {\n      this.count -= 1;\n    }\n\n    return r;\n  } else {\n    if (this.count === 1) {\n      if (this.leftPoints[0] === interval) {\n        return EMPTY;\n      } else {\n        return NOT_FOUND;\n      }\n    }\n\n    if (this.leftPoints.length === 1 && this.leftPoints[0] === interval) {\n      if (this.left && this.right) {\n        var p = this;\n        var n = this.left;\n\n        while (n.right) {\n          p = n;\n          n = n.right;\n        }\n\n        if (p === this) {\n          n.right = this.right;\n        } else {\n          var l = this.left;\n          var r = this.right;\n          p.count -= n.count;\n          p.right = n.left;\n          n.left = l;\n          n.right = r;\n        }\n\n        copy(this, n);\n        this.count = (this.left ? this.left.count : 0) + (this.right ? this.right.count : 0) + this.leftPoints.length;\n      } else if (this.left) {\n        copy(this, this.left);\n      } else {\n        copy(this, this.right);\n      }\n\n      return SUCCESS;\n    }\n\n    for (var l = bounds.ge(this.leftPoints, interval, compareBegin); l < this.leftPoints.length; ++l) {\n      if (this.leftPoints[l][0] !== interval[0]) {\n        break;\n      }\n\n      if (this.leftPoints[l] === interval) {\n        this.count -= 1;\n        this.leftPoints.splice(l, 1);\n\n        for (var r = bounds.ge(this.rightPoints, interval, compareEnd); r < this.rightPoints.length; ++r) {\n          if (this.rightPoints[r][1] !== interval[1]) {\n            break;\n          } else if (this.rightPoints[r] === interval) {\n            this.rightPoints.splice(r, 1);\n            return SUCCESS;\n          }\n        }\n      }\n    }\n\n    return NOT_FOUND;\n  }\n};\n\nfunction reportLeftRange(arr, hi, cb) {\n  for (var i = 0; i < arr.length && arr[i][0] <= hi; ++i) {\n    var r = cb(arr[i]);\n\n    if (r) {\n      return r;\n    }\n  }\n}\n\nfunction reportRightRange(arr, lo, cb) {\n  for (var i = arr.length - 1; i >= 0 && arr[i][1] >= lo; --i) {\n    var r = cb(arr[i]);\n\n    if (r) {\n      return r;\n    }\n  }\n}\n\nfunction reportRange(arr, cb) {\n  for (var i = 0; i < arr.length; ++i) {\n    var r = cb(arr[i]);\n\n    if (r) {\n      return r;\n    }\n  }\n}\n\nproto.queryPoint = function (x, cb) {\n  if (x < this.mid) {\n    if (this.left) {\n      var r = this.left.queryPoint(x, cb);\n\n      if (r) {\n        return r;\n      }\n    }\n\n    return reportLeftRange(this.leftPoints, x, cb);\n  } else if (x > this.mid) {\n    if (this.right) {\n      var r = this.right.queryPoint(x, cb);\n\n      if (r) {\n        return r;\n      }\n    }\n\n    return reportRightRange(this.rightPoints, x, cb);\n  } else {\n    return reportRange(this.leftPoints, cb);\n  }\n};\n\nproto.queryInterval = function (lo, hi, cb) {\n  if (lo < this.mid && this.left) {\n    var r = this.left.queryInterval(lo, hi, cb);\n\n    if (r) {\n      return r;\n    }\n  }\n\n  if (hi > this.mid && this.right) {\n    var r = this.right.queryInterval(lo, hi, cb);\n\n    if (r) {\n      return r;\n    }\n  }\n\n  if (hi < this.mid) {\n    return reportLeftRange(this.leftPoints, hi, cb);\n  } else if (lo > this.mid) {\n    return reportRightRange(this.rightPoints, lo, cb);\n  } else {\n    return reportRange(this.leftPoints, cb);\n  }\n};\n\nfunction compareNumbers(a, b) {\n  return a - b;\n}\n\nfunction compareBegin(a, b) {\n  var d = a[0] - b[0];\n\n  if (d) {\n    return d;\n  }\n\n  return a[1] - b[1];\n}\n\nfunction compareEnd(a, b) {\n  var d = a[1] - b[1];\n\n  if (d) {\n    return d;\n  }\n\n  return a[0] - b[0];\n}\n\nfunction createIntervalTree(intervals) {\n  if (intervals.length === 0) {\n    return null;\n  }\n\n  var pts = [];\n\n  for (var i = 0; i < intervals.length; ++i) {\n    pts.push(intervals[i][0], intervals[i][1]);\n  }\n\n  pts.sort(compareNumbers);\n  var mid = pts[pts.length >> 1];\n  var leftIntervals = [];\n  var rightIntervals = [];\n  var centerIntervals = [];\n\n  for (var i = 0; i < intervals.length; ++i) {\n    var s = intervals[i];\n\n    if (s[1] < mid) {\n      leftIntervals.push(s);\n    } else if (mid < s[0]) {\n      rightIntervals.push(s);\n    } else {\n      centerIntervals.push(s);\n    }\n  } //Split center intervals\n\n\n  var leftPoints = centerIntervals;\n  var rightPoints = centerIntervals.slice();\n  leftPoints.sort(compareBegin);\n  rightPoints.sort(compareEnd);\n  return new IntervalTreeNode(mid, createIntervalTree(leftIntervals), createIntervalTree(rightIntervals), leftPoints, rightPoints);\n} //User friendly wrapper that makes it possible to support empty trees\n\n\nfunction IntervalTree(root) {\n  this.root = root;\n}\n\nvar tproto = IntervalTree.prototype;\n\ntproto.insert = function (interval) {\n  if (this.root) {\n    this.root.insert(interval);\n  } else {\n    this.root = new IntervalTreeNode(interval[0], null, null, [interval], [interval]);\n  }\n};\n\ntproto.remove = function (interval) {\n  if (this.root) {\n    var r = this.root.remove(interval);\n\n    if (r === EMPTY) {\n      this.root = null;\n    }\n\n    return r !== NOT_FOUND;\n  }\n\n  return false;\n};\n\ntproto.queryPoint = function (p, cb) {\n  if (this.root) {\n    return this.root.queryPoint(p, cb);\n  }\n};\n\ntproto.queryInterval = function (lo, hi, cb) {\n  if (lo <= hi && this.root) {\n    return this.root.queryInterval(lo, hi, cb);\n  }\n};\n\nObject.defineProperty(tproto, 'count', {\n  get: function get() {\n    if (this.root) {\n      return this.root.count;\n    }\n\n    return 0;\n  }\n});\nObject.defineProperty(tproto, 'intervals', {\n  get: function get() {\n    if (this.root) {\n      return this.root.intervals([]);\n    }\n\n    return [];\n  }\n});\nexport default function createWrapper(intervals) {\n  if (!intervals || intervals.length === 0) {\n    return new IntervalTree(null);\n  }\n\n  return new IntervalTree(createIntervalTree(intervals));\n}", "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport createIntervalTree from '../vendor/intervalTree';\n\n// Position cache requirements:\n//   O(log(n)) lookup of cells to render for a given viewport size\n//   O(1) lookup of shortest measured column (so we know when to enter phase 1)\nvar PositionCache =\n/*#__PURE__*/\nfunction () {\n  function PositionCache() {\n    _classCallCheck(this, PositionCache);\n\n    _defineProperty(this, \"_columnSizeMap\", {});\n\n    _defineProperty(this, \"_intervalTree\", createIntervalTree());\n\n    _defineProperty(this, \"_leftMap\", {});\n  }\n\n  _createClass(PositionCache, [{\n    key: \"estimateTotalHeight\",\n    value: function estimateTotalHeight(cellCount, columnCount, defaultCellHeight) {\n      var unmeasuredCellCount = cellCount - this.count;\n      return this.tallestColumnSize + Math.ceil(unmeasuredCellCount / columnCount) * defaultCellHeight;\n    } // Render all cells visible within the viewport range defined.\n\n  }, {\n    key: \"range\",\n    value: function range(scrollTop, clientHeight, renderCallback) {\n      var _this = this;\n\n      this._intervalTree.queryInterval(scrollTop, scrollTop + clientHeight, function (_ref) {\n        var _ref2 = _slicedToArray(_ref, 3),\n            top = _ref2[0],\n            _ = _ref2[1],\n            index = _ref2[2];\n\n        return renderCallback(index, _this._leftMap[index], top);\n      });\n    }\n  }, {\n    key: \"setPosition\",\n    value: function setPosition(index, left, top, height) {\n      this._intervalTree.insert([top, top + height, index]);\n\n      this._leftMap[index] = left;\n      var columnSizeMap = this._columnSizeMap;\n      var columnHeight = columnSizeMap[left];\n\n      if (columnHeight === undefined) {\n        columnSizeMap[left] = top + height;\n      } else {\n        columnSizeMap[left] = Math.max(columnHeight, top + height);\n      }\n    }\n  }, {\n    key: \"count\",\n    get: function get() {\n      return this._intervalTree.count;\n    }\n  }, {\n    key: \"shortestColumnSize\",\n    get: function get() {\n      var columnSizeMap = this._columnSizeMap;\n      var size = 0;\n\n      for (var i in columnSizeMap) {\n        var height = columnSizeMap[i];\n        size = size === 0 ? height : Math.min(size, height);\n      }\n\n      return size;\n    }\n  }, {\n    key: \"tallestColumnSize\",\n    get: function get() {\n      var columnSizeMap = this._columnSizeMap;\n      var size = 0;\n\n      for (var i in columnSizeMap) {\n        var height = columnSizeMap[i];\n        size = Math.max(size, height);\n      }\n\n      return size;\n    }\n  }]);\n\n  return PositionCache;\n}();\n\nexport { PositionCache as default };", "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\n\nvar _class, _temp;\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nimport clsx from 'clsx';\nimport * as React from 'react';\nimport { polyfill } from 'react-lifecycles-compat';\nimport PositionCache from './PositionCache';\nimport { requestAnimationTimeout, cancelAnimationTimeout } from '../utils/requestAnimationTimeout';\nvar emptyObject = {};\n/**\n * Specifies the number of miliseconds during which to disable pointer events while a scroll is in progress.\n * This improves performance and makes scrolling smoother.\n */\n\nexport var DEFAULT_SCROLLING_RESET_TIME_INTERVAL = 150;\n/**\n * This component efficiently displays arbitrarily positioned cells using windowing techniques.\n * Cell position is determined by an injected `cellPositioner` property.\n * Windowing is vertical; this component does not support horizontal scrolling.\n *\n * Rendering occurs in two phases:\n * 1) First pass uses estimated cell sizes (provided by the cache) to determine how many cells to measure in a batch.\n *    Batch size is chosen using a fast, naive layout algorithm that stacks images in order until the viewport has been filled.\n *    After measurement is complete (componentDidMount or componentDidUpdate) this component evaluates positioned cells\n *    in order to determine if another measurement pass is required (eg if actual cell sizes were less than estimated sizes).\n *    All measurements are permanently cached (keyed by `keyMapper`) for performance purposes.\n * 2) Second pass uses the external `cellPositioner` to layout cells.\n *    At this time the positioner has access to cached size measurements for all cells.\n *    The positions it returns are cached by Masonry for fast access later.\n *    Phase one is repeated if the user scrolls beyond the current layout's bounds.\n *    If the layout is invalidated due to eg a resize, cached positions can be cleared using `recomputeCellPositions()`.\n *\n * Animation constraints:\n *   Simple animations are supported (eg translate/slide into place on initial reveal).\n *   More complex animations are not (eg flying from one position to another on resize).\n *\n * Layout constraints:\n *   This component supports multi-column layout.\n *   The height of each item may vary.\n *   The width of each item must not exceed the width of the column it is \"in\".\n *   The left position of all items within a column must align.\n *   (Items may not span multiple columns.)\n */\n\nvar Masonry = (_temp = _class =\n/*#__PURE__*/\nfunction (_React$PureComponent) {\n  _inherits(Masonry, _React$PureComponent);\n\n  function Masonry() {\n    var _getPrototypeOf2;\n\n    var _this;\n\n    _classCallCheck(this, Masonry);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _possibleConstructorReturn(this, (_getPrototypeOf2 = _getPrototypeOf(Masonry)).call.apply(_getPrototypeOf2, [this].concat(args)));\n\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      isScrolling: false,\n      scrollTop: 0\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_debounceResetIsScrollingId\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_invalidateOnUpdateStartIndex\", null);\n\n    _defineProperty(_assertThisInitialized(_this), \"_invalidateOnUpdateStopIndex\", null);\n\n    _defineProperty(_assertThisInitialized(_this), \"_positionCache\", new PositionCache());\n\n    _defineProperty(_assertThisInitialized(_this), \"_startIndex\", null);\n\n    _defineProperty(_assertThisInitialized(_this), \"_startIndexMemoized\", null);\n\n    _defineProperty(_assertThisInitialized(_this), \"_stopIndex\", null);\n\n    _defineProperty(_assertThisInitialized(_this), \"_stopIndexMemoized\", null);\n\n    _defineProperty(_assertThisInitialized(_this), \"_debounceResetIsScrollingCallback\", function () {\n      _this.setState({\n        isScrolling: false\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_setScrollingContainerRef\", function (ref) {\n      _this._scrollingContainer = ref;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_onScroll\", function (event) {\n      var height = _this.props.height;\n      var eventScrollTop = event.currentTarget.scrollTop; // When this component is shrunk drastically, React dispatches a series of back-to-back scroll events,\n      // Gradually converging on a scrollTop that is within the bounds of the new, smaller height.\n      // This causes a series of rapid renders that is slow for long lists.\n      // We can avoid that by doing some simple bounds checking to ensure that scroll offsets never exceed their bounds.\n\n      var scrollTop = Math.min(Math.max(0, _this._getEstimatedTotalHeight() - height), eventScrollTop); // On iOS, we can arrive at negative offsets by swiping past the start or end.\n      // Avoid re-rendering in this case as it can cause problems; see #532 for more.\n\n      if (eventScrollTop !== scrollTop) {\n        return;\n      } // Prevent pointer events from interrupting a smooth scroll\n\n\n      _this._debounceResetIsScrolling(); // Certain devices (like Apple touchpad) rapid-fire duplicate events.\n      // Don't force a re-render if this is the case.\n      // The mouse may move faster then the animation frame does.\n      // Use requestAnimationFrame to avoid over-updating.\n\n\n      if (_this.state.scrollTop !== scrollTop) {\n        _this.setState({\n          isScrolling: true,\n          scrollTop: scrollTop\n        });\n      }\n    });\n\n    return _this;\n  }\n\n  _createClass(Masonry, [{\n    key: \"clearCellPositions\",\n    value: function clearCellPositions() {\n      this._positionCache = new PositionCache();\n      this.forceUpdate();\n    } // HACK This method signature was intended for Grid\n\n  }, {\n    key: \"invalidateCellSizeAfterRender\",\n    value: function invalidateCellSizeAfterRender(_ref) {\n      var index = _ref.rowIndex;\n\n      if (this._invalidateOnUpdateStartIndex === null) {\n        this._invalidateOnUpdateStartIndex = index;\n        this._invalidateOnUpdateStopIndex = index;\n      } else {\n        this._invalidateOnUpdateStartIndex = Math.min(this._invalidateOnUpdateStartIndex, index);\n        this._invalidateOnUpdateStopIndex = Math.max(this._invalidateOnUpdateStopIndex, index);\n      }\n    }\n  }, {\n    key: \"recomputeCellPositions\",\n    value: function recomputeCellPositions() {\n      var stopIndex = this._positionCache.count - 1;\n      this._positionCache = new PositionCache();\n\n      this._populatePositionCache(0, stopIndex);\n\n      this.forceUpdate();\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this._checkInvalidateOnUpdate();\n\n      this._invokeOnScrollCallback();\n\n      this._invokeOnCellsRenderedCallback();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps, prevState) {\n      this._checkInvalidateOnUpdate();\n\n      this._invokeOnScrollCallback();\n\n      this._invokeOnCellsRenderedCallback();\n\n      if (this.props.scrollTop !== prevProps.scrollTop) {\n        this._debounceResetIsScrolling();\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      if (this._debounceResetIsScrollingId) {\n        cancelAnimationTimeout(this._debounceResetIsScrollingId);\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n\n      var _this$props = this.props,\n          autoHeight = _this$props.autoHeight,\n          cellCount = _this$props.cellCount,\n          cellMeasurerCache = _this$props.cellMeasurerCache,\n          cellRenderer = _this$props.cellRenderer,\n          className = _this$props.className,\n          height = _this$props.height,\n          id = _this$props.id,\n          keyMapper = _this$props.keyMapper,\n          overscanByPixels = _this$props.overscanByPixels,\n          role = _this$props.role,\n          style = _this$props.style,\n          tabIndex = _this$props.tabIndex,\n          width = _this$props.width,\n          rowDirection = _this$props.rowDirection;\n      var _this$state = this.state,\n          isScrolling = _this$state.isScrolling,\n          scrollTop = _this$state.scrollTop;\n      var children = [];\n\n      var estimateTotalHeight = this._getEstimatedTotalHeight();\n\n      var shortestColumnSize = this._positionCache.shortestColumnSize;\n      var measuredCellCount = this._positionCache.count;\n      var startIndex = 0;\n      var stopIndex;\n\n      this._positionCache.range(Math.max(0, scrollTop - overscanByPixels), height + overscanByPixels * 2, function (index, left, top) {\n        var _style;\n\n        if (typeof stopIndex === 'undefined') {\n          startIndex = index;\n          stopIndex = index;\n        } else {\n          startIndex = Math.min(startIndex, index);\n          stopIndex = Math.max(stopIndex, index);\n        }\n\n        children.push(cellRenderer({\n          index: index,\n          isScrolling: isScrolling,\n          key: keyMapper(index),\n          parent: _this2,\n          style: (_style = {\n            height: cellMeasurerCache.getHeight(index)\n          }, _defineProperty(_style, rowDirection === 'ltr' ? 'left' : 'right', left), _defineProperty(_style, \"position\", 'absolute'), _defineProperty(_style, \"top\", top), _defineProperty(_style, \"width\", cellMeasurerCache.getWidth(index)), _style)\n        }));\n      }); // We need to measure additional cells for this layout\n\n\n      if (shortestColumnSize < scrollTop + height + overscanByPixels && measuredCellCount < cellCount) {\n        var batchSize = Math.min(cellCount - measuredCellCount, Math.ceil((scrollTop + height + overscanByPixels - shortestColumnSize) / cellMeasurerCache.defaultHeight * width / cellMeasurerCache.defaultWidth));\n\n        for (var _index = measuredCellCount; _index < measuredCellCount + batchSize; _index++) {\n          stopIndex = _index;\n          children.push(cellRenderer({\n            index: _index,\n            isScrolling: isScrolling,\n            key: keyMapper(_index),\n            parent: this,\n            style: {\n              width: cellMeasurerCache.getWidth(_index)\n            }\n          }));\n        }\n      }\n\n      this._startIndex = startIndex;\n      this._stopIndex = stopIndex;\n      return React.createElement(\"div\", {\n        ref: this._setScrollingContainerRef,\n        \"aria-label\": this.props['aria-label'],\n        className: clsx('ReactVirtualized__Masonry', className),\n        id: id,\n        onScroll: this._onScroll,\n        role: role,\n        style: _objectSpread({\n          boxSizing: 'border-box',\n          direction: 'ltr',\n          height: autoHeight ? 'auto' : height,\n          overflowX: 'hidden',\n          overflowY: estimateTotalHeight < height ? 'hidden' : 'auto',\n          position: 'relative',\n          width: width,\n          WebkitOverflowScrolling: 'touch',\n          willChange: 'transform'\n        }, style),\n        tabIndex: tabIndex\n      }, React.createElement(\"div\", {\n        className: \"ReactVirtualized__Masonry__innerScrollContainer\",\n        style: {\n          width: '100%',\n          height: estimateTotalHeight,\n          maxWidth: '100%',\n          maxHeight: estimateTotalHeight,\n          overflow: 'hidden',\n          pointerEvents: isScrolling ? 'none' : '',\n          position: 'relative'\n        }\n      }, children));\n    }\n  }, {\n    key: \"_checkInvalidateOnUpdate\",\n    value: function _checkInvalidateOnUpdate() {\n      if (typeof this._invalidateOnUpdateStartIndex === 'number') {\n        var startIndex = this._invalidateOnUpdateStartIndex;\n        var stopIndex = this._invalidateOnUpdateStopIndex;\n        this._invalidateOnUpdateStartIndex = null;\n        this._invalidateOnUpdateStopIndex = null; // Query external layout logic for position of newly-measured cells\n\n        this._populatePositionCache(startIndex, stopIndex);\n\n        this.forceUpdate();\n      }\n    }\n  }, {\n    key: \"_debounceResetIsScrolling\",\n    value: function _debounceResetIsScrolling() {\n      var scrollingResetTimeInterval = this.props.scrollingResetTimeInterval;\n\n      if (this._debounceResetIsScrollingId) {\n        cancelAnimationTimeout(this._debounceResetIsScrollingId);\n      }\n\n      this._debounceResetIsScrollingId = requestAnimationTimeout(this._debounceResetIsScrollingCallback, scrollingResetTimeInterval);\n    }\n  }, {\n    key: \"_getEstimatedTotalHeight\",\n    value: function _getEstimatedTotalHeight() {\n      var _this$props2 = this.props,\n          cellCount = _this$props2.cellCount,\n          cellMeasurerCache = _this$props2.cellMeasurerCache,\n          width = _this$props2.width;\n      var estimatedColumnCount = Math.max(1, Math.floor(width / cellMeasurerCache.defaultWidth));\n      return this._positionCache.estimateTotalHeight(cellCount, estimatedColumnCount, cellMeasurerCache.defaultHeight);\n    }\n  }, {\n    key: \"_invokeOnScrollCallback\",\n    value: function _invokeOnScrollCallback() {\n      var _this$props3 = this.props,\n          height = _this$props3.height,\n          onScroll = _this$props3.onScroll;\n      var scrollTop = this.state.scrollTop;\n\n      if (this._onScrollMemoized !== scrollTop) {\n        onScroll({\n          clientHeight: height,\n          scrollHeight: this._getEstimatedTotalHeight(),\n          scrollTop: scrollTop\n        });\n        this._onScrollMemoized = scrollTop;\n      }\n    }\n  }, {\n    key: \"_invokeOnCellsRenderedCallback\",\n    value: function _invokeOnCellsRenderedCallback() {\n      if (this._startIndexMemoized !== this._startIndex || this._stopIndexMemoized !== this._stopIndex) {\n        var onCellsRendered = this.props.onCellsRendered;\n        onCellsRendered({\n          startIndex: this._startIndex,\n          stopIndex: this._stopIndex\n        });\n        this._startIndexMemoized = this._startIndex;\n        this._stopIndexMemoized = this._stopIndex;\n      }\n    }\n  }, {\n    key: \"_populatePositionCache\",\n    value: function _populatePositionCache(startIndex, stopIndex) {\n      var _this$props4 = this.props,\n          cellMeasurerCache = _this$props4.cellMeasurerCache,\n          cellPositioner = _this$props4.cellPositioner;\n\n      for (var _index2 = startIndex; _index2 <= stopIndex; _index2++) {\n        var _cellPositioner = cellPositioner(_index2),\n            left = _cellPositioner.left,\n            top = _cellPositioner.top;\n\n        this._positionCache.setPosition(_index2, left, top, cellMeasurerCache.getHeight(_index2));\n      }\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.scrollTop !== undefined && prevState.scrollTop !== nextProps.scrollTop) {\n        return {\n          isScrolling: true,\n          scrollTop: nextProps.scrollTop\n        };\n      }\n\n      return null;\n    }\n  }]);\n\n  return Masonry;\n}(React.PureComponent), _defineProperty(_class, \"propTypes\", process.env.NODE_ENV === 'production' ? null : {\n  \"autoHeight\": PropTypes.bool.isRequired,\n  \"cellCount\": PropTypes.number.isRequired,\n  \"cellMeasurerCache\": function cellMeasurerCache() {\n    return (typeof CellMeasurerCache === \"function\" ? PropTypes.instanceOf(CellMeasurerCache).isRequired : PropTypes.any.isRequired).apply(this, arguments);\n  },\n  \"cellPositioner\": function cellPositioner() {\n    return (typeof Positioner === \"function\" ? PropTypes.instanceOf(Positioner).isRequired : PropTypes.any.isRequired).apply(this, arguments);\n  },\n  \"cellRenderer\": function cellRenderer() {\n    return (typeof CellRenderer === \"function\" ? PropTypes.instanceOf(CellRenderer).isRequired : PropTypes.any.isRequired).apply(this, arguments);\n  },\n  \"className\": PropTypes.string,\n  \"height\": PropTypes.number.isRequired,\n  \"id\": PropTypes.string,\n  \"keyMapper\": function keyMapper() {\n    return (typeof KeyMapper === \"function\" ? PropTypes.instanceOf(KeyMapper).isRequired : PropTypes.any.isRequired).apply(this, arguments);\n  },\n  \"onCellsRendered\": function onCellsRendered() {\n    return (typeof OnCellsRenderedCallback === \"function\" ? PropTypes.instanceOf(OnCellsRenderedCallback) : PropTypes.any).apply(this, arguments);\n  },\n  \"onScroll\": function onScroll() {\n    return (typeof OnScrollCallback === \"function\" ? PropTypes.instanceOf(OnScrollCallback) : PropTypes.any).apply(this, arguments);\n  },\n  \"overscanByPixels\": PropTypes.number.isRequired,\n  \"role\": PropTypes.string.isRequired,\n  \"scrollingResetTimeInterval\": PropTypes.number.isRequired,\n  \"style\": function style(props, propName, componentName) {\n    if (!Object.prototype.hasOwnProperty.call(props, propName)) {\n      throw new Error(\"Prop `\".concat(propName, \"` has type 'any' or 'mixed', but was not provided to `\").concat(componentName, \"`. Pass undefined or any other value.\"));\n    }\n  },\n  \"tabIndex\": PropTypes.number.isRequired,\n  \"width\": PropTypes.number.isRequired,\n  \"rowDirection\": PropTypes.string.isRequired,\n  \"scrollTop\": PropTypes.number\n}), _temp);\n\n_defineProperty(Masonry, \"defaultProps\", {\n  autoHeight: false,\n  keyMapper: identity,\n  onCellsRendered: noop,\n  onScroll: noop,\n  overscanByPixels: 20,\n  role: 'grid',\n  scrollingResetTimeInterval: DEFAULT_SCROLLING_RESET_TIME_INTERVAL,\n  style: emptyObject,\n  tabIndex: 0,\n  rowDirection: 'ltr'\n});\n\nfunction identity(value) {\n  return value;\n}\n\nfunction noop() {}\n\nvar bpfrpt_proptype_CellMeasurerCache = process.env.NODE_ENV === 'production' ? null : {\n  \"defaultHeight\": PropTypes.number.isRequired,\n  \"defaultWidth\": PropTypes.number.isRequired,\n  \"getHeight\": PropTypes.func.isRequired,\n  \"getWidth\": PropTypes.func.isRequired\n};\npolyfill(Masonry);\nexport default Masonry;\nvar bpfrpt_proptype_Positioner = process.env.NODE_ENV === 'production' ? null : PropTypes.func;\nimport { bpfrpt_proptype_AnimationTimeoutId } from \"../utils/requestAnimationTimeout\";\nimport PropTypes from \"prop-types\";\nexport { bpfrpt_proptype_CellMeasurerCache };\nexport { bpfrpt_proptype_Positioner };", "import createCellPositioner from './createCellPositioner';\nimport Masonry from './Masonry';\nexport default Masonry;\nexport { createCellPositioner, Masonry };", "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport { CellMeasurerCache } from '../CellMeasurer';\n\n/**\n * Caches measurements for a given cell.\n */\nvar CellMeasurerCacheDecorator =\n/*#__PURE__*/\nfunction () {\n  function CellMeasurerCacheDecorator() {\n    var _this = this;\n\n    var params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n    _classCallCheck(this, CellMeasurerCacheDecorator);\n\n    _defineProperty(this, \"_cellMeasurerCache\", void 0);\n\n    _defineProperty(this, \"_columnIndexOffset\", void 0);\n\n    _defineProperty(this, \"_rowIndexOffset\", void 0);\n\n    _defineProperty(this, \"columnWidth\", function (_ref) {\n      var index = _ref.index;\n\n      _this._cellMeasurerCache.columnWidth({\n        index: index + _this._columnIndexOffset\n      });\n    });\n\n    _defineProperty(this, \"rowHeight\", function (_ref2) {\n      var index = _ref2.index;\n\n      _this._cellMeasurerCache.rowHeight({\n        index: index + _this._rowIndexOffset\n      });\n    });\n\n    var cellMeasurerCache = params.cellMeasurerCache,\n        _params$columnIndexOf = params.columnIndexOffset,\n        columnIndexOffset = _params$columnIndexOf === void 0 ? 0 : _params$columnIndexOf,\n        _params$rowIndexOffse = params.rowIndexOffset,\n        rowIndexOffset = _params$rowIndexOffse === void 0 ? 0 : _params$rowIndexOffse;\n    this._cellMeasurerCache = cellMeasurerCache;\n    this._columnIndexOffset = columnIndexOffset;\n    this._rowIndexOffset = rowIndexOffset;\n  }\n\n  _createClass(CellMeasurerCacheDecorator, [{\n    key: \"clear\",\n    value: function clear(rowIndex, columnIndex) {\n      this._cellMeasurerCache.clear(rowIndex + this._rowIndexOffset, columnIndex + this._columnIndexOffset);\n    }\n  }, {\n    key: \"clearAll\",\n    value: function clearAll() {\n      this._cellMeasurerCache.clearAll();\n    }\n  }, {\n    key: \"hasFixedHeight\",\n    value: function hasFixedHeight() {\n      return this._cellMeasurerCache.hasFixedHeight();\n    }\n  }, {\n    key: \"hasFixedWidth\",\n    value: function hasFixedWidth() {\n      return this._cellMeasurerCache.hasFixedWidth();\n    }\n  }, {\n    key: \"getHeight\",\n    value: function getHeight(rowIndex) {\n      var columnIndex = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n      return this._cellMeasurerCache.getHeight(rowIndex + this._rowIndexOffset, columnIndex + this._columnIndexOffset);\n    }\n  }, {\n    key: \"getWidth\",\n    value: function getWidth(rowIndex) {\n      var columnIndex = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n      return this._cellMeasurerCache.getWidth(rowIndex + this._rowIndexOffset, columnIndex + this._columnIndexOffset);\n    }\n  }, {\n    key: \"has\",\n    value: function has(rowIndex) {\n      var columnIndex = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n      return this._cellMeasurerCache.has(rowIndex + this._rowIndexOffset, columnIndex + this._columnIndexOffset);\n    }\n  }, {\n    key: \"set\",\n    value: function set(rowIndex, columnIndex, width, height) {\n      this._cellMeasurerCache.set(rowIndex + this._rowIndexOffset, columnIndex + this._columnIndexOffset, width, height);\n    }\n  }, {\n    key: \"defaultHeight\",\n    get: function get() {\n      return this._cellMeasurerCache.defaultHeight;\n    }\n  }, {\n    key: \"defaultWidth\",\n    get: function get() {\n      return this._cellMeasurerCache.defaultWidth;\n    }\n  }]);\n\n  return CellMeasurerCacheDecorator;\n}();\n\nexport { CellMeasurerCacheDecorator as default };", "import _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport { polyfill } from 'react-lifecycles-compat';\nimport CellMeasurerCacheDecorator from './CellMeasurerCacheDecorator';\nimport Grid from '../Grid';\nvar SCROLLBAR_SIZE_BUFFER = 20;\n/**\n * Renders 1, 2, or 4 Grids depending on configuration.\n * A main (body) Grid will always be rendered.\n * Optionally, 1-2 Grids for sticky header rows will also be rendered.\n * If no sticky columns, only 1 sticky header Grid will be rendered.\n * If sticky columns, 2 sticky header Grids will be rendered.\n */\n\nvar MultiGrid =\n/*#__PURE__*/\nfunction (_React$PureComponent) {\n  _inherits(MultiGrid, _React$PureComponent);\n\n  function MultiGrid(props, context) {\n    var _this;\n\n    _classCallCheck(this, MultiGrid);\n\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(MultiGrid).call(this, props, context));\n\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      scrollLeft: 0,\n      scrollTop: 0,\n      scrollbarSize: 0,\n      showHorizontalScrollbar: false,\n      showVerticalScrollbar: false\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_deferredInvalidateColumnIndex\", null);\n\n    _defineProperty(_assertThisInitialized(_this), \"_deferredInvalidateRowIndex\", null);\n\n    _defineProperty(_assertThisInitialized(_this), \"_bottomLeftGridRef\", function (ref) {\n      _this._bottomLeftGrid = ref;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_bottomRightGridRef\", function (ref) {\n      _this._bottomRightGrid = ref;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_cellRendererBottomLeftGrid\", function (_ref) {\n      var rowIndex = _ref.rowIndex,\n          rest = _objectWithoutProperties(_ref, [\"rowIndex\"]);\n\n      var _this$props = _this.props,\n          cellRenderer = _this$props.cellRenderer,\n          fixedRowCount = _this$props.fixedRowCount,\n          rowCount = _this$props.rowCount;\n\n      if (rowIndex === rowCount - fixedRowCount) {\n        return React.createElement(\"div\", {\n          key: rest.key,\n          style: _objectSpread({}, rest.style, {\n            height: SCROLLBAR_SIZE_BUFFER\n          })\n        });\n      } else {\n        return cellRenderer(_objectSpread({}, rest, {\n          parent: _assertThisInitialized(_this),\n          rowIndex: rowIndex + fixedRowCount\n        }));\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_cellRendererBottomRightGrid\", function (_ref2) {\n      var columnIndex = _ref2.columnIndex,\n          rowIndex = _ref2.rowIndex,\n          rest = _objectWithoutProperties(_ref2, [\"columnIndex\", \"rowIndex\"]);\n\n      var _this$props2 = _this.props,\n          cellRenderer = _this$props2.cellRenderer,\n          fixedColumnCount = _this$props2.fixedColumnCount,\n          fixedRowCount = _this$props2.fixedRowCount;\n      return cellRenderer(_objectSpread({}, rest, {\n        columnIndex: columnIndex + fixedColumnCount,\n        parent: _assertThisInitialized(_this),\n        rowIndex: rowIndex + fixedRowCount\n      }));\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_cellRendererTopRightGrid\", function (_ref3) {\n      var columnIndex = _ref3.columnIndex,\n          rest = _objectWithoutProperties(_ref3, [\"columnIndex\"]);\n\n      var _this$props3 = _this.props,\n          cellRenderer = _this$props3.cellRenderer,\n          columnCount = _this$props3.columnCount,\n          fixedColumnCount = _this$props3.fixedColumnCount;\n\n      if (columnIndex === columnCount - fixedColumnCount) {\n        return React.createElement(\"div\", {\n          key: rest.key,\n          style: _objectSpread({}, rest.style, {\n            width: SCROLLBAR_SIZE_BUFFER\n          })\n        });\n      } else {\n        return cellRenderer(_objectSpread({}, rest, {\n          columnIndex: columnIndex + fixedColumnCount,\n          parent: _assertThisInitialized(_this)\n        }));\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_columnWidthRightGrid\", function (_ref4) {\n      var index = _ref4.index;\n      var _this$props4 = _this.props,\n          columnCount = _this$props4.columnCount,\n          fixedColumnCount = _this$props4.fixedColumnCount,\n          columnWidth = _this$props4.columnWidth;\n      var _this$state = _this.state,\n          scrollbarSize = _this$state.scrollbarSize,\n          showHorizontalScrollbar = _this$state.showHorizontalScrollbar; // An extra cell is added to the count\n      // This gives the smaller Grid extra room for offset,\n      // In case the main (bottom right) Grid has a scrollbar\n      // If no scrollbar, the extra space is overflow:hidden anyway\n\n      if (showHorizontalScrollbar && index === columnCount - fixedColumnCount) {\n        return scrollbarSize;\n      }\n\n      return typeof columnWidth === 'function' ? columnWidth({\n        index: index + fixedColumnCount\n      }) : columnWidth;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_onScroll\", function (scrollInfo) {\n      var scrollLeft = scrollInfo.scrollLeft,\n          scrollTop = scrollInfo.scrollTop;\n\n      _this.setState({\n        scrollLeft: scrollLeft,\n        scrollTop: scrollTop\n      });\n\n      var onScroll = _this.props.onScroll;\n\n      if (onScroll) {\n        onScroll(scrollInfo);\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_onScrollbarPresenceChange\", function (_ref5) {\n      var horizontal = _ref5.horizontal,\n          size = _ref5.size,\n          vertical = _ref5.vertical;\n      var _this$state2 = _this.state,\n          showHorizontalScrollbar = _this$state2.showHorizontalScrollbar,\n          showVerticalScrollbar = _this$state2.showVerticalScrollbar;\n\n      if (horizontal !== showHorizontalScrollbar || vertical !== showVerticalScrollbar) {\n        _this.setState({\n          scrollbarSize: size,\n          showHorizontalScrollbar: horizontal,\n          showVerticalScrollbar: vertical\n        });\n\n        var onScrollbarPresenceChange = _this.props.onScrollbarPresenceChange;\n\n        if (typeof onScrollbarPresenceChange === 'function') {\n          onScrollbarPresenceChange({\n            horizontal: horizontal,\n            size: size,\n            vertical: vertical\n          });\n        }\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_onScrollLeft\", function (scrollInfo) {\n      var scrollLeft = scrollInfo.scrollLeft;\n\n      _this._onScroll({\n        scrollLeft: scrollLeft,\n        scrollTop: _this.state.scrollTop\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_onScrollTop\", function (scrollInfo) {\n      var scrollTop = scrollInfo.scrollTop;\n\n      _this._onScroll({\n        scrollTop: scrollTop,\n        scrollLeft: _this.state.scrollLeft\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_rowHeightBottomGrid\", function (_ref6) {\n      var index = _ref6.index;\n      var _this$props5 = _this.props,\n          fixedRowCount = _this$props5.fixedRowCount,\n          rowCount = _this$props5.rowCount,\n          rowHeight = _this$props5.rowHeight;\n      var _this$state3 = _this.state,\n          scrollbarSize = _this$state3.scrollbarSize,\n          showVerticalScrollbar = _this$state3.showVerticalScrollbar; // An extra cell is added to the count\n      // This gives the smaller Grid extra room for offset,\n      // In case the main (bottom right) Grid has a scrollbar\n      // If no scrollbar, the extra space is overflow:hidden anyway\n\n      if (showVerticalScrollbar && index === rowCount - fixedRowCount) {\n        return scrollbarSize;\n      }\n\n      return typeof rowHeight === 'function' ? rowHeight({\n        index: index + fixedRowCount\n      }) : rowHeight;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_topLeftGridRef\", function (ref) {\n      _this._topLeftGrid = ref;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_topRightGridRef\", function (ref) {\n      _this._topRightGrid = ref;\n    });\n\n    var deferredMeasurementCache = props.deferredMeasurementCache,\n        _fixedColumnCount = props.fixedColumnCount,\n        _fixedRowCount = props.fixedRowCount;\n\n    _this._maybeCalculateCachedStyles(true);\n\n    if (deferredMeasurementCache) {\n      _this._deferredMeasurementCacheBottomLeftGrid = _fixedRowCount > 0 ? new CellMeasurerCacheDecorator({\n        cellMeasurerCache: deferredMeasurementCache,\n        columnIndexOffset: 0,\n        rowIndexOffset: _fixedRowCount\n      }) : deferredMeasurementCache;\n      _this._deferredMeasurementCacheBottomRightGrid = _fixedColumnCount > 0 || _fixedRowCount > 0 ? new CellMeasurerCacheDecorator({\n        cellMeasurerCache: deferredMeasurementCache,\n        columnIndexOffset: _fixedColumnCount,\n        rowIndexOffset: _fixedRowCount\n      }) : deferredMeasurementCache;\n      _this._deferredMeasurementCacheTopRightGrid = _fixedColumnCount > 0 ? new CellMeasurerCacheDecorator({\n        cellMeasurerCache: deferredMeasurementCache,\n        columnIndexOffset: _fixedColumnCount,\n        rowIndexOffset: 0\n      }) : deferredMeasurementCache;\n    }\n\n    return _this;\n  }\n\n  _createClass(MultiGrid, [{\n    key: \"forceUpdateGrids\",\n    value: function forceUpdateGrids() {\n      this._bottomLeftGrid && this._bottomLeftGrid.forceUpdate();\n      this._bottomRightGrid && this._bottomRightGrid.forceUpdate();\n      this._topLeftGrid && this._topLeftGrid.forceUpdate();\n      this._topRightGrid && this._topRightGrid.forceUpdate();\n    }\n    /** See Grid#invalidateCellSizeAfterRender */\n\n  }, {\n    key: \"invalidateCellSizeAfterRender\",\n    value: function invalidateCellSizeAfterRender() {\n      var _ref7 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref7$columnIndex = _ref7.columnIndex,\n          columnIndex = _ref7$columnIndex === void 0 ? 0 : _ref7$columnIndex,\n          _ref7$rowIndex = _ref7.rowIndex,\n          rowIndex = _ref7$rowIndex === void 0 ? 0 : _ref7$rowIndex;\n\n      this._deferredInvalidateColumnIndex = typeof this._deferredInvalidateColumnIndex === 'number' ? Math.min(this._deferredInvalidateColumnIndex, columnIndex) : columnIndex;\n      this._deferredInvalidateRowIndex = typeof this._deferredInvalidateRowIndex === 'number' ? Math.min(this._deferredInvalidateRowIndex, rowIndex) : rowIndex;\n    }\n    /** See Grid#measureAllCells */\n\n  }, {\n    key: \"measureAllCells\",\n    value: function measureAllCells() {\n      this._bottomLeftGrid && this._bottomLeftGrid.measureAllCells();\n      this._bottomRightGrid && this._bottomRightGrid.measureAllCells();\n      this._topLeftGrid && this._topLeftGrid.measureAllCells();\n      this._topRightGrid && this._topRightGrid.measureAllCells();\n    }\n    /** See Grid#recomputeGridSize */\n\n  }, {\n    key: \"recomputeGridSize\",\n    value: function recomputeGridSize() {\n      var _ref8 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref8$columnIndex = _ref8.columnIndex,\n          columnIndex = _ref8$columnIndex === void 0 ? 0 : _ref8$columnIndex,\n          _ref8$rowIndex = _ref8.rowIndex,\n          rowIndex = _ref8$rowIndex === void 0 ? 0 : _ref8$rowIndex;\n\n      var _this$props6 = this.props,\n          fixedColumnCount = _this$props6.fixedColumnCount,\n          fixedRowCount = _this$props6.fixedRowCount;\n      var adjustedColumnIndex = Math.max(0, columnIndex - fixedColumnCount);\n      var adjustedRowIndex = Math.max(0, rowIndex - fixedRowCount);\n      this._bottomLeftGrid && this._bottomLeftGrid.recomputeGridSize({\n        columnIndex: columnIndex,\n        rowIndex: adjustedRowIndex\n      });\n      this._bottomRightGrid && this._bottomRightGrid.recomputeGridSize({\n        columnIndex: adjustedColumnIndex,\n        rowIndex: adjustedRowIndex\n      });\n      this._topLeftGrid && this._topLeftGrid.recomputeGridSize({\n        columnIndex: columnIndex,\n        rowIndex: rowIndex\n      });\n      this._topRightGrid && this._topRightGrid.recomputeGridSize({\n        columnIndex: adjustedColumnIndex,\n        rowIndex: rowIndex\n      });\n      this._leftGridWidth = null;\n      this._topGridHeight = null;\n\n      this._maybeCalculateCachedStyles(true);\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this$props7 = this.props,\n          scrollLeft = _this$props7.scrollLeft,\n          scrollTop = _this$props7.scrollTop;\n\n      if (scrollLeft > 0 || scrollTop > 0) {\n        var newState = {};\n\n        if (scrollLeft > 0) {\n          newState.scrollLeft = scrollLeft;\n        }\n\n        if (scrollTop > 0) {\n          newState.scrollTop = scrollTop;\n        }\n\n        this.setState(newState);\n      }\n\n      this._handleInvalidatedGridSize();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this._handleInvalidatedGridSize();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props8 = this.props,\n          onScroll = _this$props8.onScroll,\n          onSectionRendered = _this$props8.onSectionRendered,\n          onScrollbarPresenceChange = _this$props8.onScrollbarPresenceChange,\n          scrollLeftProp = _this$props8.scrollLeft,\n          scrollToColumn = _this$props8.scrollToColumn,\n          scrollTopProp = _this$props8.scrollTop,\n          scrollToRow = _this$props8.scrollToRow,\n          rest = _objectWithoutProperties(_this$props8, [\"onScroll\", \"onSectionRendered\", \"onScrollbarPresenceChange\", \"scrollLeft\", \"scrollToColumn\", \"scrollTop\", \"scrollToRow\"]);\n\n      this._prepareForRender(); // Don't render any of our Grids if there are no cells.\n      // This mirrors what Grid does,\n      // And prevents us from recording inaccurage measurements when used with CellMeasurer.\n\n\n      if (this.props.width === 0 || this.props.height === 0) {\n        return null;\n      } // scrollTop and scrollLeft props are explicitly filtered out and ignored\n\n\n      var _this$state4 = this.state,\n          scrollLeft = _this$state4.scrollLeft,\n          scrollTop = _this$state4.scrollTop;\n      return React.createElement(\"div\", {\n        style: this._containerOuterStyle\n      }, React.createElement(\"div\", {\n        style: this._containerTopStyle\n      }, this._renderTopLeftGrid(rest), this._renderTopRightGrid(_objectSpread({}, rest, {\n        onScroll: onScroll,\n        scrollLeft: scrollLeft\n      }))), React.createElement(\"div\", {\n        style: this._containerBottomStyle\n      }, this._renderBottomLeftGrid(_objectSpread({}, rest, {\n        onScroll: onScroll,\n        scrollTop: scrollTop\n      })), this._renderBottomRightGrid(_objectSpread({}, rest, {\n        onScroll: onScroll,\n        onSectionRendered: onSectionRendered,\n        scrollLeft: scrollLeft,\n        scrollToColumn: scrollToColumn,\n        scrollToRow: scrollToRow,\n        scrollTop: scrollTop\n      }))));\n    }\n  }, {\n    key: \"_getBottomGridHeight\",\n    value: function _getBottomGridHeight(props) {\n      var height = props.height;\n\n      var topGridHeight = this._getTopGridHeight(props);\n\n      return height - topGridHeight;\n    }\n  }, {\n    key: \"_getLeftGridWidth\",\n    value: function _getLeftGridWidth(props) {\n      var fixedColumnCount = props.fixedColumnCount,\n          columnWidth = props.columnWidth;\n\n      if (this._leftGridWidth == null) {\n        if (typeof columnWidth === 'function') {\n          var leftGridWidth = 0;\n\n          for (var index = 0; index < fixedColumnCount; index++) {\n            leftGridWidth += columnWidth({\n              index: index\n            });\n          }\n\n          this._leftGridWidth = leftGridWidth;\n        } else {\n          this._leftGridWidth = columnWidth * fixedColumnCount;\n        }\n      }\n\n      return this._leftGridWidth;\n    }\n  }, {\n    key: \"_getRightGridWidth\",\n    value: function _getRightGridWidth(props) {\n      var width = props.width;\n\n      var leftGridWidth = this._getLeftGridWidth(props);\n\n      return width - leftGridWidth;\n    }\n  }, {\n    key: \"_getTopGridHeight\",\n    value: function _getTopGridHeight(props) {\n      var fixedRowCount = props.fixedRowCount,\n          rowHeight = props.rowHeight;\n\n      if (this._topGridHeight == null) {\n        if (typeof rowHeight === 'function') {\n          var topGridHeight = 0;\n\n          for (var index = 0; index < fixedRowCount; index++) {\n            topGridHeight += rowHeight({\n              index: index\n            });\n          }\n\n          this._topGridHeight = topGridHeight;\n        } else {\n          this._topGridHeight = rowHeight * fixedRowCount;\n        }\n      }\n\n      return this._topGridHeight;\n    }\n  }, {\n    key: \"_handleInvalidatedGridSize\",\n    value: function _handleInvalidatedGridSize() {\n      if (typeof this._deferredInvalidateColumnIndex === 'number') {\n        var columnIndex = this._deferredInvalidateColumnIndex;\n        var rowIndex = this._deferredInvalidateRowIndex;\n        this._deferredInvalidateColumnIndex = null;\n        this._deferredInvalidateRowIndex = null;\n        this.recomputeGridSize({\n          columnIndex: columnIndex,\n          rowIndex: rowIndex\n        });\n        this.forceUpdate();\n      }\n    }\n    /**\n     * Avoid recreating inline styles each render; this bypasses Grid's shallowCompare.\n     * This method recalculates styles only when specific props change.\n     */\n\n  }, {\n    key: \"_maybeCalculateCachedStyles\",\n    value: function _maybeCalculateCachedStyles(resetAll) {\n      var _this$props9 = this.props,\n          columnWidth = _this$props9.columnWidth,\n          enableFixedColumnScroll = _this$props9.enableFixedColumnScroll,\n          enableFixedRowScroll = _this$props9.enableFixedRowScroll,\n          height = _this$props9.height,\n          fixedColumnCount = _this$props9.fixedColumnCount,\n          fixedRowCount = _this$props9.fixedRowCount,\n          rowHeight = _this$props9.rowHeight,\n          style = _this$props9.style,\n          styleBottomLeftGrid = _this$props9.styleBottomLeftGrid,\n          styleBottomRightGrid = _this$props9.styleBottomRightGrid,\n          styleTopLeftGrid = _this$props9.styleTopLeftGrid,\n          styleTopRightGrid = _this$props9.styleTopRightGrid,\n          width = _this$props9.width;\n      var sizeChange = resetAll || height !== this._lastRenderedHeight || width !== this._lastRenderedWidth;\n      var leftSizeChange = resetAll || columnWidth !== this._lastRenderedColumnWidth || fixedColumnCount !== this._lastRenderedFixedColumnCount;\n      var topSizeChange = resetAll || fixedRowCount !== this._lastRenderedFixedRowCount || rowHeight !== this._lastRenderedRowHeight;\n\n      if (resetAll || sizeChange || style !== this._lastRenderedStyle) {\n        this._containerOuterStyle = _objectSpread({\n          height: height,\n          overflow: 'visible',\n          // Let :focus outline show through\n          width: width\n        }, style);\n      }\n\n      if (resetAll || sizeChange || topSizeChange) {\n        this._containerTopStyle = {\n          height: this._getTopGridHeight(this.props),\n          position: 'relative',\n          width: width\n        };\n        this._containerBottomStyle = {\n          height: height - this._getTopGridHeight(this.props),\n          overflow: 'visible',\n          // Let :focus outline show through\n          position: 'relative',\n          width: width\n        };\n      }\n\n      if (resetAll || styleBottomLeftGrid !== this._lastRenderedStyleBottomLeftGrid) {\n        this._bottomLeftGridStyle = _objectSpread({\n          left: 0,\n          overflowX: 'hidden',\n          overflowY: enableFixedColumnScroll ? 'auto' : 'hidden',\n          position: 'absolute'\n        }, styleBottomLeftGrid);\n      }\n\n      if (resetAll || leftSizeChange || styleBottomRightGrid !== this._lastRenderedStyleBottomRightGrid) {\n        this._bottomRightGridStyle = _objectSpread({\n          left: this._getLeftGridWidth(this.props),\n          position: 'absolute'\n        }, styleBottomRightGrid);\n      }\n\n      if (resetAll || styleTopLeftGrid !== this._lastRenderedStyleTopLeftGrid) {\n        this._topLeftGridStyle = _objectSpread({\n          left: 0,\n          overflowX: 'hidden',\n          overflowY: 'hidden',\n          position: 'absolute',\n          top: 0\n        }, styleTopLeftGrid);\n      }\n\n      if (resetAll || leftSizeChange || styleTopRightGrid !== this._lastRenderedStyleTopRightGrid) {\n        this._topRightGridStyle = _objectSpread({\n          left: this._getLeftGridWidth(this.props),\n          overflowX: enableFixedRowScroll ? 'auto' : 'hidden',\n          overflowY: 'hidden',\n          position: 'absolute',\n          top: 0\n        }, styleTopRightGrid);\n      }\n\n      this._lastRenderedColumnWidth = columnWidth;\n      this._lastRenderedFixedColumnCount = fixedColumnCount;\n      this._lastRenderedFixedRowCount = fixedRowCount;\n      this._lastRenderedHeight = height;\n      this._lastRenderedRowHeight = rowHeight;\n      this._lastRenderedStyle = style;\n      this._lastRenderedStyleBottomLeftGrid = styleBottomLeftGrid;\n      this._lastRenderedStyleBottomRightGrid = styleBottomRightGrid;\n      this._lastRenderedStyleTopLeftGrid = styleTopLeftGrid;\n      this._lastRenderedStyleTopRightGrid = styleTopRightGrid;\n      this._lastRenderedWidth = width;\n    }\n  }, {\n    key: \"_prepareForRender\",\n    value: function _prepareForRender() {\n      if (this._lastRenderedColumnWidth !== this.props.columnWidth || this._lastRenderedFixedColumnCount !== this.props.fixedColumnCount) {\n        this._leftGridWidth = null;\n      }\n\n      if (this._lastRenderedFixedRowCount !== this.props.fixedRowCount || this._lastRenderedRowHeight !== this.props.rowHeight) {\n        this._topGridHeight = null;\n      }\n\n      this._maybeCalculateCachedStyles();\n\n      this._lastRenderedColumnWidth = this.props.columnWidth;\n      this._lastRenderedFixedColumnCount = this.props.fixedColumnCount;\n      this._lastRenderedFixedRowCount = this.props.fixedRowCount;\n      this._lastRenderedRowHeight = this.props.rowHeight;\n    }\n  }, {\n    key: \"_renderBottomLeftGrid\",\n    value: function _renderBottomLeftGrid(props) {\n      var enableFixedColumnScroll = props.enableFixedColumnScroll,\n          fixedColumnCount = props.fixedColumnCount,\n          fixedRowCount = props.fixedRowCount,\n          rowCount = props.rowCount,\n          hideBottomLeftGridScrollbar = props.hideBottomLeftGridScrollbar;\n      var showVerticalScrollbar = this.state.showVerticalScrollbar;\n\n      if (!fixedColumnCount) {\n        return null;\n      }\n\n      var additionalRowCount = showVerticalScrollbar ? 1 : 0,\n          height = this._getBottomGridHeight(props),\n          width = this._getLeftGridWidth(props),\n          scrollbarSize = this.state.showVerticalScrollbar ? this.state.scrollbarSize : 0,\n          gridWidth = hideBottomLeftGridScrollbar ? width + scrollbarSize : width;\n\n      var bottomLeftGrid = React.createElement(Grid, _extends({}, props, {\n        cellRenderer: this._cellRendererBottomLeftGrid,\n        className: this.props.classNameBottomLeftGrid,\n        columnCount: fixedColumnCount,\n        deferredMeasurementCache: this._deferredMeasurementCacheBottomLeftGrid,\n        height: height,\n        onScroll: enableFixedColumnScroll ? this._onScrollTop : undefined,\n        ref: this._bottomLeftGridRef,\n        rowCount: Math.max(0, rowCount - fixedRowCount) + additionalRowCount,\n        rowHeight: this._rowHeightBottomGrid,\n        style: this._bottomLeftGridStyle,\n        tabIndex: null,\n        width: gridWidth\n      }));\n\n      if (hideBottomLeftGridScrollbar) {\n        return React.createElement(\"div\", {\n          className: \"BottomLeftGrid_ScrollWrapper\",\n          style: _objectSpread({}, this._bottomLeftGridStyle, {\n            height: height,\n            width: width,\n            overflowY: 'hidden'\n          })\n        }, bottomLeftGrid);\n      }\n\n      return bottomLeftGrid;\n    }\n  }, {\n    key: \"_renderBottomRightGrid\",\n    value: function _renderBottomRightGrid(props) {\n      var columnCount = props.columnCount,\n          fixedColumnCount = props.fixedColumnCount,\n          fixedRowCount = props.fixedRowCount,\n          rowCount = props.rowCount,\n          scrollToColumn = props.scrollToColumn,\n          scrollToRow = props.scrollToRow;\n      return React.createElement(Grid, _extends({}, props, {\n        cellRenderer: this._cellRendererBottomRightGrid,\n        className: this.props.classNameBottomRightGrid,\n        columnCount: Math.max(0, columnCount - fixedColumnCount),\n        columnWidth: this._columnWidthRightGrid,\n        deferredMeasurementCache: this._deferredMeasurementCacheBottomRightGrid,\n        height: this._getBottomGridHeight(props),\n        onScroll: this._onScroll,\n        onScrollbarPresenceChange: this._onScrollbarPresenceChange,\n        ref: this._bottomRightGridRef,\n        rowCount: Math.max(0, rowCount - fixedRowCount),\n        rowHeight: this._rowHeightBottomGrid,\n        scrollToColumn: scrollToColumn - fixedColumnCount,\n        scrollToRow: scrollToRow - fixedRowCount,\n        style: this._bottomRightGridStyle,\n        width: this._getRightGridWidth(props)\n      }));\n    }\n  }, {\n    key: \"_renderTopLeftGrid\",\n    value: function _renderTopLeftGrid(props) {\n      var fixedColumnCount = props.fixedColumnCount,\n          fixedRowCount = props.fixedRowCount;\n\n      if (!fixedColumnCount || !fixedRowCount) {\n        return null;\n      }\n\n      return React.createElement(Grid, _extends({}, props, {\n        className: this.props.classNameTopLeftGrid,\n        columnCount: fixedColumnCount,\n        height: this._getTopGridHeight(props),\n        ref: this._topLeftGridRef,\n        rowCount: fixedRowCount,\n        style: this._topLeftGridStyle,\n        tabIndex: null,\n        width: this._getLeftGridWidth(props)\n      }));\n    }\n  }, {\n    key: \"_renderTopRightGrid\",\n    value: function _renderTopRightGrid(props) {\n      var columnCount = props.columnCount,\n          enableFixedRowScroll = props.enableFixedRowScroll,\n          fixedColumnCount = props.fixedColumnCount,\n          fixedRowCount = props.fixedRowCount,\n          scrollLeft = props.scrollLeft,\n          hideTopRightGridScrollbar = props.hideTopRightGridScrollbar;\n      var _this$state5 = this.state,\n          showHorizontalScrollbar = _this$state5.showHorizontalScrollbar,\n          scrollbarSize = _this$state5.scrollbarSize;\n\n      if (!fixedRowCount) {\n        return null;\n      }\n\n      var additionalColumnCount = showHorizontalScrollbar ? 1 : 0,\n          height = this._getTopGridHeight(props),\n          width = this._getRightGridWidth(props),\n          additionalHeight = showHorizontalScrollbar ? scrollbarSize : 0;\n\n      var gridHeight = height,\n          style = this._topRightGridStyle;\n\n      if (hideTopRightGridScrollbar) {\n        gridHeight = height + additionalHeight;\n        style = _objectSpread({}, this._topRightGridStyle, {\n          left: 0\n        });\n      }\n\n      var topRightGrid = React.createElement(Grid, _extends({}, props, {\n        cellRenderer: this._cellRendererTopRightGrid,\n        className: this.props.classNameTopRightGrid,\n        columnCount: Math.max(0, columnCount - fixedColumnCount) + additionalColumnCount,\n        columnWidth: this._columnWidthRightGrid,\n        deferredMeasurementCache: this._deferredMeasurementCacheTopRightGrid,\n        height: gridHeight,\n        onScroll: enableFixedRowScroll ? this._onScrollLeft : undefined,\n        ref: this._topRightGridRef,\n        rowCount: fixedRowCount,\n        scrollLeft: scrollLeft,\n        style: style,\n        tabIndex: null,\n        width: width\n      }));\n\n      if (hideTopRightGridScrollbar) {\n        return React.createElement(\"div\", {\n          className: \"TopRightGrid_ScrollWrapper\",\n          style: _objectSpread({}, this._topRightGridStyle, {\n            height: height,\n            width: width,\n            overflowX: 'hidden'\n          })\n        }, topRightGrid);\n      }\n\n      return topRightGrid;\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.scrollLeft !== prevState.scrollLeft || nextProps.scrollTop !== prevState.scrollTop) {\n        return {\n          scrollLeft: nextProps.scrollLeft != null && nextProps.scrollLeft >= 0 ? nextProps.scrollLeft : prevState.scrollLeft,\n          scrollTop: nextProps.scrollTop != null && nextProps.scrollTop >= 0 ? nextProps.scrollTop : prevState.scrollTop\n        };\n      }\n\n      return null;\n    }\n  }]);\n\n  return MultiGrid;\n}(React.PureComponent);\n\n_defineProperty(MultiGrid, \"defaultProps\", {\n  classNameBottomLeftGrid: '',\n  classNameBottomRightGrid: '',\n  classNameTopLeftGrid: '',\n  classNameTopRightGrid: '',\n  enableFixedColumnScroll: false,\n  enableFixedRowScroll: false,\n  fixedColumnCount: 0,\n  fixedRowCount: 0,\n  scrollToColumn: -1,\n  scrollToRow: -1,\n  style: {},\n  styleBottomLeftGrid: {},\n  styleBottomRightGrid: {},\n  styleTopLeftGrid: {},\n  styleTopRightGrid: {},\n  hideTopRightGridScrollbar: false,\n  hideBottomLeftGridScrollbar: false\n});\n\nMultiGrid.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  classNameBottomLeftGrid: PropTypes.string.isRequired,\n  classNameBottomRightGrid: PropTypes.string.isRequired,\n  classNameTopLeftGrid: PropTypes.string.isRequired,\n  classNameTopRightGrid: PropTypes.string.isRequired,\n  enableFixedColumnScroll: PropTypes.bool.isRequired,\n  enableFixedRowScroll: PropTypes.bool.isRequired,\n  fixedColumnCount: PropTypes.number.isRequired,\n  fixedRowCount: PropTypes.number.isRequired,\n  onScrollbarPresenceChange: PropTypes.func,\n  style: PropTypes.object.isRequired,\n  styleBottomLeftGrid: PropTypes.object.isRequired,\n  styleBottomRightGrid: PropTypes.object.isRequired,\n  styleTopLeftGrid: PropTypes.object.isRequired,\n  styleTopRightGrid: PropTypes.object.isRequired,\n  hideTopRightGridScrollbar: PropTypes.bool,\n  hideBottomLeftGridScrollbar: PropTypes.bool\n} : {};\npolyfill(MultiGrid);\nexport default MultiGrid;", "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\n/**\n * HOC that simplifies the process of synchronizing scrolling between two or more virtualized components.\n */\n\nvar ScrollSync =\n/*#__PURE__*/\nfunction (_React$PureComponent) {\n  _inherits(ScrollSync, _React$PureComponent);\n\n  function ScrollSync(props, context) {\n    var _this;\n\n    _classCallCheck(this, ScrollSync);\n\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(ScrollSync).call(this, props, context));\n    _this.state = {\n      clientHeight: 0,\n      clientWidth: 0,\n      scrollHeight: 0,\n      scrollLeft: 0,\n      scrollTop: 0,\n      scrollWidth: 0\n    };\n    _this._onScroll = _this._onScroll.bind(_assertThisInitialized(_this));\n    return _this;\n  }\n\n  _createClass(ScrollSync, [{\n    key: \"render\",\n    value: function render() {\n      var children = this.props.children;\n      var _this$state = this.state,\n          clientHeight = _this$state.clientHeight,\n          clientWidth = _this$state.clientWidth,\n          scrollHeight = _this$state.scrollHeight,\n          scrollLeft = _this$state.scrollLeft,\n          scrollTop = _this$state.scrollTop,\n          scrollWidth = _this$state.scrollWidth;\n      return children({\n        clientHeight: clientHeight,\n        clientWidth: clientWidth,\n        onScroll: this._onScroll,\n        scrollHeight: scrollHeight,\n        scrollLeft: scrollLeft,\n        scrollTop: scrollTop,\n        scrollWidth: scrollWidth\n      });\n    }\n  }, {\n    key: \"_onScroll\",\n    value: function _onScroll(_ref) {\n      var clientHeight = _ref.clientHeight,\n          clientWidth = _ref.clientWidth,\n          scrollHeight = _ref.scrollHeight,\n          scrollLeft = _ref.scrollLeft,\n          scrollTop = _ref.scrollTop,\n          scrollWidth = _ref.scrollWidth;\n      this.setState({\n        clientHeight: clientHeight,\n        clientWidth: clientWidth,\n        scrollHeight: scrollHeight,\n        scrollLeft: scrollLeft,\n        scrollTop: scrollTop,\n        scrollWidth: scrollWidth\n      });\n    }\n  }]);\n\n  return ScrollSync;\n}(React.PureComponent);\n\nexport { ScrollSync as default };\nScrollSync.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  /**\n   * Function responsible for rendering 2 or more virtualized components.\n   * This function should implement the following signature:\n   * ({ onScroll, scrollLeft, scrollTop }) => PropTypes.element\n   */\n  children: PropTypes.func.isRequired\n} : {};", "import * as React from 'react';\nexport default function defaultHeaderRowRenderer(_ref) {\n  var className = _ref.className,\n      columns = _ref.columns,\n      style = _ref.style;\n  return React.createElement(\"div\", {\n    className: className,\n    role: \"row\",\n    style: style\n  }, columns);\n}\ndefaultHeaderRowRenderer.propTypes = process.env.NODE_ENV === 'production' ? null : bpfrpt_proptype_HeaderRowRendererParams === PropTypes.any ? {} : bpfrpt_proptype_HeaderRowRendererParams;\nimport { bpfrpt_proptype_HeaderRowRendererParams } from \"./types\";\nimport PropTypes from \"prop-types\";", "var SortDirection = {\n  /**\n   * Sort items in ascending order.\n   * This means arranging from the lowest value to the highest (e.g. a-z, 0-9).\n   */\n  ASC: 'ASC',\n\n  /**\n   * Sort items in descending order.\n   * This means arranging from the highest value to the lowest (e.g. z-a, 9-0).\n   */\n  DESC: 'DESC'\n};\nexport default SortDirection;", "import clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport SortDirection from './SortDirection';\n/**\n * Displayed beside a header to indicate that a Table is currently sorted by this column.\n */\n\nexport default function SortIndicator(_ref) {\n  var sortDirection = _ref.sortDirection;\n  var classNames = clsx('ReactVirtualized__Table__sortableHeaderIcon', {\n    'ReactVirtualized__Table__sortableHeaderIcon--ASC': sortDirection === SortDirection.ASC,\n    'ReactVirtualized__Table__sortableHeaderIcon--DESC': sortDirection === SortDirection.DESC\n  });\n  return React.createElement(\"svg\", {\n    className: classNames,\n    width: 18,\n    height: 18,\n    viewBox: \"0 0 24 24\"\n  }, sortDirection === SortDirection.ASC ? React.createElement(\"path\", {\n    d: \"M7 14l5-5 5 5z\"\n  }) : React.createElement(\"path\", {\n    d: \"M7 10l5 5 5-5z\"\n  }), React.createElement(\"path\", {\n    d: \"M0 0h24v24H0z\",\n    fill: \"none\"\n  }));\n}\nSortIndicator.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  sortDirection: PropTypes.oneOf([SortDirection.ASC, SortDirection.DESC])\n} : {};", "import * as React from 'react';\nimport SortIndicator from './SortIndicator';\n\n/**\n * Default table header renderer.\n */\nexport default function defaultHeaderRenderer(_ref) {\n  var dataKey = _ref.dataKey,\n      label = _ref.label,\n      sortBy = _ref.sortBy,\n      sortDirection = _ref.sortDirection;\n  var showSortIndicator = sortBy === dataKey;\n  var children = [React.createElement(\"span\", {\n    className: \"ReactVirtualized__Table__headerTruncatedText\",\n    key: \"label\",\n    title: typeof label === 'string' ? label : null\n  }, label)];\n\n  if (showSortIndicator) {\n    children.push(React.createElement(SortIndicator, {\n      key: \"SortIndicator\",\n      sortDirection: sortDirection\n    }));\n  }\n\n  return children;\n}\ndefaultHeaderRenderer.propTypes = process.env.NODE_ENV === 'production' ? null : bpfrpt_proptype_HeaderRendererParams === PropTypes.any ? {} : bpfrpt_proptype_HeaderRendererParams;\nimport { bpfrpt_proptype_HeaderRendererParams } from \"./types\";\nimport PropTypes from \"prop-types\";", "import _extends from \"@babel/runtime/helpers/extends\";\nimport * as React from 'react';\n\n/**\n * Default row renderer for Table.\n */\nexport default function defaultRowRenderer(_ref) {\n  var className = _ref.className,\n      columns = _ref.columns,\n      index = _ref.index,\n      key = _ref.key,\n      onRowClick = _ref.onRowClick,\n      onRowDoubleClick = _ref.onRowDoubleClick,\n      onRowMouseOut = _ref.onRowMouseOut,\n      onRowMouseOver = _ref.onRowMouseOver,\n      onRowRightClick = _ref.onRowRightClick,\n      rowData = _ref.rowData,\n      style = _ref.style;\n  var a11yProps = {\n    'aria-rowindex': index + 1\n  };\n\n  if (onRowClick || onRowDoubleClick || onRowMouseOut || onRowMouseOver || onRowRightClick) {\n    a11yProps['aria-label'] = 'row';\n    a11yProps.tabIndex = 0;\n\n    if (onRowClick) {\n      a11yProps.onClick = function (event) {\n        return onRowClick({\n          event: event,\n          index: index,\n          rowData: rowData\n        });\n      };\n    }\n\n    if (onRowDoubleClick) {\n      a11yProps.onDoubleClick = function (event) {\n        return onRowDoubleClick({\n          event: event,\n          index: index,\n          rowData: rowData\n        });\n      };\n    }\n\n    if (onRowMouseOut) {\n      a11yProps.onMouseOut = function (event) {\n        return onRowMouseOut({\n          event: event,\n          index: index,\n          rowData: rowData\n        });\n      };\n    }\n\n    if (onRowMouseOver) {\n      a11yProps.onMouseOver = function (event) {\n        return onRowMouseOver({\n          event: event,\n          index: index,\n          rowData: rowData\n        });\n      };\n    }\n\n    if (onRowRightClick) {\n      a11yProps.onContextMenu = function (event) {\n        return onRowRightClick({\n          event: event,\n          index: index,\n          rowData: rowData\n        });\n      };\n    }\n  }\n\n  return React.createElement(\"div\", _extends({}, a11yProps, {\n    className: className,\n    key: key,\n    role: \"row\",\n    style: style\n  }), columns);\n}\ndefaultRowRenderer.propTypes = process.env.NODE_ENV === 'production' ? null : bpfrpt_proptype_RowRendererParams === PropTypes.any ? {} : bpfrpt_proptype_RowRendererParams;\nimport { bpfrpt_proptype_RowRendererParams } from \"./types\";\nimport PropTypes from \"prop-types\";", "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport defaultHeaderRenderer from './defaultHeaderRenderer';\nimport defaultCellRenderer from './defaultCellRenderer';\nimport defaultCellDataGetter from './defaultCellDataGetter';\nimport SortDirection from './SortDirection';\n/**\n * Describes the header and cell contents of a table column.\n */\n\nvar Column =\n/*#__PURE__*/\nfunction (_React$Component) {\n  _inherits(Column, _React$Component);\n\n  function Column() {\n    _classCallCheck(this, Column);\n\n    return _possibleConstructorReturn(this, _getPrototypeOf(Column).apply(this, arguments));\n  }\n\n  return Column;\n}(React.Component);\n\n_defineProperty(Column, \"defaultProps\", {\n  cellDataGetter: defaultCellDataGetter,\n  cellRenderer: defaultCellRenderer,\n  defaultSortDirection: SortDirection.ASC,\n  flexGrow: 0,\n  flexShrink: 1,\n  headerRenderer: defaultHeaderRenderer,\n  style: {}\n});\n\nexport { Column as default };\nColumn.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  /** Optional aria-label value to set on the column header */\n  'aria-label': PropTypes.string,\n\n  /**\n   * Callback responsible for returning a cell's data, given its :dataKey\n   * ({ columnData: any, dataKey: string, rowData: any }): any\n   */\n  cellDataGetter: PropTypes.func,\n\n  /**\n   * Callback responsible for rendering a cell's contents.\n   * ({ cellData: any, columnData: any, dataKey: string, rowData: any, rowIndex: number }): node\n   */\n  cellRenderer: PropTypes.func,\n\n  /** Optional CSS class to apply to cell */\n  className: PropTypes.string,\n\n  /** Optional additional data passed to this column's :cellDataGetter */\n  columnData: PropTypes.object,\n\n  /** Uniquely identifies the row-data attribute corresponding to this cell */\n  dataKey: PropTypes.any.isRequired,\n\n  /** Optional direction to be used when clicked the first time */\n  defaultSortDirection: PropTypes.oneOf([SortDirection.ASC, SortDirection.DESC]),\n\n  /** If sort is enabled for the table at large, disable it for this column */\n  disableSort: PropTypes.bool,\n\n  /** Flex grow style; defaults to 0 */\n  flexGrow: PropTypes.number,\n\n  /** Flex shrink style; defaults to 1 */\n  flexShrink: PropTypes.number,\n\n  /** Optional CSS class to apply to this column's header */\n  headerClassName: PropTypes.string,\n\n  /**\n   * Optional callback responsible for rendering a column header contents.\n   * ({ columnData: object, dataKey: string, disableSort: boolean, label: node, sortBy: string, sortDirection: string }): PropTypes.node\n   */\n  headerRenderer: PropTypes.func.isRequired,\n\n  /** Optional inline style to apply to this column's header */\n  headerStyle: PropTypes.object,\n\n  /** Optional id to set on the column header */\n  id: PropTypes.string,\n\n  /** Header label for this column */\n  label: PropTypes.node,\n\n  /** Maximum width of column; this property will only be used if :flexGrow is > 0. */\n  maxWidth: PropTypes.number,\n\n  /** Minimum width of column. */\n  minWidth: PropTypes.number,\n\n  /** Optional inline style to apply to cell */\n  style: PropTypes.object,\n\n  /** Flex basis (width) for this column; This value can grow or shrink based on :flexGrow and :flexShrink properties. */\n  width: PropTypes.number.isRequired\n} : {};", "import _extends from \"@babel/runtime/helpers/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nimport clsx from 'clsx';\nimport Column from './Column';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport { findDOMNode } from 'react-dom';\nimport Grid, { accessibilityOverscanIndicesGetter } from '../Grid';\nimport defaultRowRenderer from './defaultRowRenderer';\nimport defaultHeaderRowRenderer from './defaultHeaderRowRenderer';\nimport SortDirection from './SortDirection';\n/**\n * Table component with fixed headers and virtualized rows for improved performance with large data sets.\n * This component expects explicit width, height, and padding parameters.\n */\n\nvar Table =\n/*#__PURE__*/\nfunction (_React$PureComponent) {\n  _inherits(Table, _React$PureComponent);\n\n  function Table(props) {\n    var _this;\n\n    _classCallCheck(this, Table);\n\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(Table).call(this, props));\n    _this.state = {\n      scrollbarWidth: 0\n    };\n    _this._createColumn = _this._createColumn.bind(_assertThisInitialized(_this));\n    _this._createRow = _this._createRow.bind(_assertThisInitialized(_this));\n    _this._onScroll = _this._onScroll.bind(_assertThisInitialized(_this));\n    _this._onSectionRendered = _this._onSectionRendered.bind(_assertThisInitialized(_this));\n    _this._setRef = _this._setRef.bind(_assertThisInitialized(_this));\n    return _this;\n  }\n\n  _createClass(Table, [{\n    key: \"forceUpdateGrid\",\n    value: function forceUpdateGrid() {\n      if (this.Grid) {\n        this.Grid.forceUpdate();\n      }\n    }\n    /** See Grid#getOffsetForCell */\n\n  }, {\n    key: \"getOffsetForRow\",\n    value: function getOffsetForRow(_ref) {\n      var alignment = _ref.alignment,\n          index = _ref.index;\n\n      if (this.Grid) {\n        var _this$Grid$getOffsetF = this.Grid.getOffsetForCell({\n          alignment: alignment,\n          rowIndex: index\n        }),\n            scrollTop = _this$Grid$getOffsetF.scrollTop;\n\n        return scrollTop;\n      }\n\n      return 0;\n    }\n    /** CellMeasurer compatibility */\n\n  }, {\n    key: \"invalidateCellSizeAfterRender\",\n    value: function invalidateCellSizeAfterRender(_ref2) {\n      var columnIndex = _ref2.columnIndex,\n          rowIndex = _ref2.rowIndex;\n\n      if (this.Grid) {\n        this.Grid.invalidateCellSizeAfterRender({\n          rowIndex: rowIndex,\n          columnIndex: columnIndex\n        });\n      }\n    }\n    /** See Grid#measureAllCells */\n\n  }, {\n    key: \"measureAllRows\",\n    value: function measureAllRows() {\n      if (this.Grid) {\n        this.Grid.measureAllCells();\n      }\n    }\n    /** CellMeasurer compatibility */\n\n  }, {\n    key: \"recomputeGridSize\",\n    value: function recomputeGridSize() {\n      var _ref3 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref3$columnIndex = _ref3.columnIndex,\n          columnIndex = _ref3$columnIndex === void 0 ? 0 : _ref3$columnIndex,\n          _ref3$rowIndex = _ref3.rowIndex,\n          rowIndex = _ref3$rowIndex === void 0 ? 0 : _ref3$rowIndex;\n\n      if (this.Grid) {\n        this.Grid.recomputeGridSize({\n          rowIndex: rowIndex,\n          columnIndex: columnIndex\n        });\n      }\n    }\n    /** See Grid#recomputeGridSize */\n\n  }, {\n    key: \"recomputeRowHeights\",\n    value: function recomputeRowHeights() {\n      var index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n\n      if (this.Grid) {\n        this.Grid.recomputeGridSize({\n          rowIndex: index\n        });\n      }\n    }\n    /** See Grid#scrollToPosition */\n\n  }, {\n    key: \"scrollToPosition\",\n    value: function scrollToPosition() {\n      var scrollTop = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n\n      if (this.Grid) {\n        this.Grid.scrollToPosition({\n          scrollTop: scrollTop\n        });\n      }\n    }\n    /** See Grid#scrollToCell */\n\n  }, {\n    key: \"scrollToRow\",\n    value: function scrollToRow() {\n      var index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n\n      if (this.Grid) {\n        this.Grid.scrollToCell({\n          columnIndex: 0,\n          rowIndex: index\n        });\n      }\n    }\n  }, {\n    key: \"getScrollbarWidth\",\n    value: function getScrollbarWidth() {\n      if (this.Grid) {\n        var _Grid = findDOMNode(this.Grid);\n\n        var clientWidth = _Grid.clientWidth || 0;\n        var offsetWidth = _Grid.offsetWidth || 0;\n        return offsetWidth - clientWidth;\n      }\n\n      return 0;\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this._setScrollbarWidth();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this._setScrollbarWidth();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n\n      var _this$props = this.props,\n          children = _this$props.children,\n          className = _this$props.className,\n          disableHeader = _this$props.disableHeader,\n          gridClassName = _this$props.gridClassName,\n          gridStyle = _this$props.gridStyle,\n          headerHeight = _this$props.headerHeight,\n          headerRowRenderer = _this$props.headerRowRenderer,\n          height = _this$props.height,\n          id = _this$props.id,\n          noRowsRenderer = _this$props.noRowsRenderer,\n          rowClassName = _this$props.rowClassName,\n          rowStyle = _this$props.rowStyle,\n          scrollToIndex = _this$props.scrollToIndex,\n          style = _this$props.style,\n          width = _this$props.width;\n      var scrollbarWidth = this.state.scrollbarWidth;\n      var availableRowsHeight = disableHeader ? height : height - headerHeight;\n      var rowClass = typeof rowClassName === 'function' ? rowClassName({\n        index: -1\n      }) : rowClassName;\n      var rowStyleObject = typeof rowStyle === 'function' ? rowStyle({\n        index: -1\n      }) : rowStyle; // Precompute and cache column styles before rendering rows and columns to speed things up\n\n      this._cachedColumnStyles = [];\n      React.Children.toArray(children).forEach(function (column, index) {\n        var flexStyles = _this2._getFlexStyleForColumn(column, column.props.style);\n\n        _this2._cachedColumnStyles[index] = _objectSpread({\n          overflow: 'hidden'\n        }, flexStyles);\n      }); // Note that we specify :rowCount, :scrollbarWidth, :sortBy, and :sortDirection as properties on Grid even though these have nothing to do with Grid.\n      // This is done because Grid is a pure component and won't update unless its properties or state has changed.\n      // Any property that should trigger a re-render of Grid then is specified here to avoid a stale display.\n\n      return React.createElement(\"div\", {\n        \"aria-label\": this.props['aria-label'],\n        \"aria-labelledby\": this.props['aria-labelledby'],\n        \"aria-colcount\": React.Children.toArray(children).length,\n        \"aria-rowcount\": this.props.rowCount,\n        className: clsx('ReactVirtualized__Table', className),\n        id: id,\n        role: \"grid\",\n        style: style\n      }, !disableHeader && headerRowRenderer({\n        className: clsx('ReactVirtualized__Table__headerRow', rowClass),\n        columns: this._getHeaderColumns(),\n        style: _objectSpread({\n          height: headerHeight,\n          overflow: 'hidden',\n          paddingRight: scrollbarWidth,\n          width: width\n        }, rowStyleObject)\n      }), React.createElement(Grid, _extends({}, this.props, {\n        \"aria-readonly\": null,\n        autoContainerWidth: true,\n        className: clsx('ReactVirtualized__Table__Grid', gridClassName),\n        cellRenderer: this._createRow,\n        columnWidth: width,\n        columnCount: 1,\n        height: availableRowsHeight,\n        id: undefined,\n        noContentRenderer: noRowsRenderer,\n        onScroll: this._onScroll,\n        onSectionRendered: this._onSectionRendered,\n        ref: this._setRef,\n        role: \"rowgroup\",\n        scrollbarWidth: scrollbarWidth,\n        scrollToRow: scrollToIndex,\n        style: _objectSpread({}, gridStyle, {\n          overflowX: 'hidden'\n        })\n      })));\n    }\n  }, {\n    key: \"_createColumn\",\n    value: function _createColumn(_ref4) {\n      var column = _ref4.column,\n          columnIndex = _ref4.columnIndex,\n          isScrolling = _ref4.isScrolling,\n          parent = _ref4.parent,\n          rowData = _ref4.rowData,\n          rowIndex = _ref4.rowIndex;\n      var onColumnClick = this.props.onColumnClick;\n      var _column$props = column.props,\n          cellDataGetter = _column$props.cellDataGetter,\n          cellRenderer = _column$props.cellRenderer,\n          className = _column$props.className,\n          columnData = _column$props.columnData,\n          dataKey = _column$props.dataKey,\n          id = _column$props.id;\n      var cellData = cellDataGetter({\n        columnData: columnData,\n        dataKey: dataKey,\n        rowData: rowData\n      });\n      var renderedCell = cellRenderer({\n        cellData: cellData,\n        columnData: columnData,\n        columnIndex: columnIndex,\n        dataKey: dataKey,\n        isScrolling: isScrolling,\n        parent: parent,\n        rowData: rowData,\n        rowIndex: rowIndex\n      });\n\n      var onClick = function onClick(event) {\n        onColumnClick && onColumnClick({\n          columnData: columnData,\n          dataKey: dataKey,\n          event: event\n        });\n      };\n\n      var style = this._cachedColumnStyles[columnIndex];\n      var title = typeof renderedCell === 'string' ? renderedCell : null; // Avoid using object-spread syntax with multiple objects here,\n      // Since it results in an extra method call to 'babel-runtime/helpers/extends'\n      // See PR https://github.com/bvaughn/react-virtualized/pull/942\n\n      return React.createElement(\"div\", {\n        \"aria-colindex\": columnIndex + 1,\n        \"aria-describedby\": id,\n        className: clsx('ReactVirtualized__Table__rowColumn', className),\n        key: 'Row' + rowIndex + '-' + 'Col' + columnIndex,\n        onClick: onClick,\n        role: \"gridcell\",\n        style: style,\n        title: title\n      }, renderedCell);\n    }\n  }, {\n    key: \"_createHeader\",\n    value: function _createHeader(_ref5) {\n      var column = _ref5.column,\n          index = _ref5.index;\n      var _this$props2 = this.props,\n          headerClassName = _this$props2.headerClassName,\n          headerStyle = _this$props2.headerStyle,\n          onHeaderClick = _this$props2.onHeaderClick,\n          sort = _this$props2.sort,\n          sortBy = _this$props2.sortBy,\n          sortDirection = _this$props2.sortDirection;\n      var _column$props2 = column.props,\n          columnData = _column$props2.columnData,\n          dataKey = _column$props2.dataKey,\n          defaultSortDirection = _column$props2.defaultSortDirection,\n          disableSort = _column$props2.disableSort,\n          headerRenderer = _column$props2.headerRenderer,\n          id = _column$props2.id,\n          label = _column$props2.label;\n      var sortEnabled = !disableSort && sort;\n      var classNames = clsx('ReactVirtualized__Table__headerColumn', headerClassName, column.props.headerClassName, {\n        ReactVirtualized__Table__sortableHeaderColumn: sortEnabled\n      });\n\n      var style = this._getFlexStyleForColumn(column, _objectSpread({}, headerStyle, {}, column.props.headerStyle));\n\n      var renderedHeader = headerRenderer({\n        columnData: columnData,\n        dataKey: dataKey,\n        disableSort: disableSort,\n        label: label,\n        sortBy: sortBy,\n        sortDirection: sortDirection\n      });\n      var headerOnClick, headerOnKeyDown, headerTabIndex, headerAriaSort, headerAriaLabel;\n\n      if (sortEnabled || onHeaderClick) {\n        // If this is a sortable header, clicking it should update the table data's sorting.\n        var isFirstTimeSort = sortBy !== dataKey; // If this is the firstTime sort of this column, use the column default sort order.\n        // Otherwise, invert the direction of the sort.\n\n        var newSortDirection = isFirstTimeSort ? defaultSortDirection : sortDirection === SortDirection.DESC ? SortDirection.ASC : SortDirection.DESC;\n\n        var onClick = function onClick(event) {\n          sortEnabled && sort({\n            defaultSortDirection: defaultSortDirection,\n            event: event,\n            sortBy: dataKey,\n            sortDirection: newSortDirection\n          });\n          onHeaderClick && onHeaderClick({\n            columnData: columnData,\n            dataKey: dataKey,\n            event: event\n          });\n        };\n\n        var onKeyDown = function onKeyDown(event) {\n          if (event.key === 'Enter' || event.key === ' ') {\n            onClick(event);\n          }\n        };\n\n        headerAriaLabel = column.props['aria-label'] || label || dataKey;\n        headerAriaSort = 'none';\n        headerTabIndex = 0;\n        headerOnClick = onClick;\n        headerOnKeyDown = onKeyDown;\n      }\n\n      if (sortBy === dataKey) {\n        headerAriaSort = sortDirection === SortDirection.ASC ? 'ascending' : 'descending';\n      } // Avoid using object-spread syntax with multiple objects here,\n      // Since it results in an extra method call to 'babel-runtime/helpers/extends'\n      // See PR https://github.com/bvaughn/react-virtualized/pull/942\n\n\n      return React.createElement(\"div\", {\n        \"aria-label\": headerAriaLabel,\n        \"aria-sort\": headerAriaSort,\n        className: classNames,\n        id: id,\n        key: 'Header-Col' + index,\n        onClick: headerOnClick,\n        onKeyDown: headerOnKeyDown,\n        role: \"columnheader\",\n        style: style,\n        tabIndex: headerTabIndex\n      }, renderedHeader);\n    }\n  }, {\n    key: \"_createRow\",\n    value: function _createRow(_ref6) {\n      var _this3 = this;\n\n      var index = _ref6.rowIndex,\n          isScrolling = _ref6.isScrolling,\n          key = _ref6.key,\n          parent = _ref6.parent,\n          style = _ref6.style;\n      var _this$props3 = this.props,\n          children = _this$props3.children,\n          onRowClick = _this$props3.onRowClick,\n          onRowDoubleClick = _this$props3.onRowDoubleClick,\n          onRowRightClick = _this$props3.onRowRightClick,\n          onRowMouseOver = _this$props3.onRowMouseOver,\n          onRowMouseOut = _this$props3.onRowMouseOut,\n          rowClassName = _this$props3.rowClassName,\n          rowGetter = _this$props3.rowGetter,\n          rowRenderer = _this$props3.rowRenderer,\n          rowStyle = _this$props3.rowStyle;\n      var scrollbarWidth = this.state.scrollbarWidth;\n      var rowClass = typeof rowClassName === 'function' ? rowClassName({\n        index: index\n      }) : rowClassName;\n      var rowStyleObject = typeof rowStyle === 'function' ? rowStyle({\n        index: index\n      }) : rowStyle;\n      var rowData = rowGetter({\n        index: index\n      });\n      var columns = React.Children.toArray(children).map(function (column, columnIndex) {\n        return _this3._createColumn({\n          column: column,\n          columnIndex: columnIndex,\n          isScrolling: isScrolling,\n          parent: parent,\n          rowData: rowData,\n          rowIndex: index,\n          scrollbarWidth: scrollbarWidth\n        });\n      });\n      var className = clsx('ReactVirtualized__Table__row', rowClass);\n\n      var flattenedStyle = _objectSpread({}, style, {\n        height: this._getRowHeight(index),\n        overflow: 'hidden',\n        paddingRight: scrollbarWidth\n      }, rowStyleObject);\n\n      return rowRenderer({\n        className: className,\n        columns: columns,\n        index: index,\n        isScrolling: isScrolling,\n        key: key,\n        onRowClick: onRowClick,\n        onRowDoubleClick: onRowDoubleClick,\n        onRowRightClick: onRowRightClick,\n        onRowMouseOver: onRowMouseOver,\n        onRowMouseOut: onRowMouseOut,\n        rowData: rowData,\n        style: flattenedStyle\n      });\n    }\n    /**\n     * Determines the flex-shrink, flex-grow, and width values for a cell (header or column).\n     */\n\n  }, {\n    key: \"_getFlexStyleForColumn\",\n    value: function _getFlexStyleForColumn(column) {\n      var customStyle = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var flexValue = \"\".concat(column.props.flexGrow, \" \").concat(column.props.flexShrink, \" \").concat(column.props.width, \"px\");\n\n      var style = _objectSpread({}, customStyle, {\n        flex: flexValue,\n        msFlex: flexValue,\n        WebkitFlex: flexValue\n      });\n\n      if (column.props.maxWidth) {\n        style.maxWidth = column.props.maxWidth;\n      }\n\n      if (column.props.minWidth) {\n        style.minWidth = column.props.minWidth;\n      }\n\n      return style;\n    }\n  }, {\n    key: \"_getHeaderColumns\",\n    value: function _getHeaderColumns() {\n      var _this4 = this;\n\n      var _this$props4 = this.props,\n          children = _this$props4.children,\n          disableHeader = _this$props4.disableHeader;\n      var items = disableHeader ? [] : React.Children.toArray(children);\n      return items.map(function (column, index) {\n        return _this4._createHeader({\n          column: column,\n          index: index\n        });\n      });\n    }\n  }, {\n    key: \"_getRowHeight\",\n    value: function _getRowHeight(rowIndex) {\n      var rowHeight = this.props.rowHeight;\n      return typeof rowHeight === 'function' ? rowHeight({\n        index: rowIndex\n      }) : rowHeight;\n    }\n  }, {\n    key: \"_onScroll\",\n    value: function _onScroll(_ref7) {\n      var clientHeight = _ref7.clientHeight,\n          scrollHeight = _ref7.scrollHeight,\n          scrollTop = _ref7.scrollTop;\n      var onScroll = this.props.onScroll;\n      onScroll({\n        clientHeight: clientHeight,\n        scrollHeight: scrollHeight,\n        scrollTop: scrollTop\n      });\n    }\n  }, {\n    key: \"_onSectionRendered\",\n    value: function _onSectionRendered(_ref8) {\n      var rowOverscanStartIndex = _ref8.rowOverscanStartIndex,\n          rowOverscanStopIndex = _ref8.rowOverscanStopIndex,\n          rowStartIndex = _ref8.rowStartIndex,\n          rowStopIndex = _ref8.rowStopIndex;\n      var onRowsRendered = this.props.onRowsRendered;\n      onRowsRendered({\n        overscanStartIndex: rowOverscanStartIndex,\n        overscanStopIndex: rowOverscanStopIndex,\n        startIndex: rowStartIndex,\n        stopIndex: rowStopIndex\n      });\n    }\n  }, {\n    key: \"_setRef\",\n    value: function _setRef(ref) {\n      this.Grid = ref;\n    }\n  }, {\n    key: \"_setScrollbarWidth\",\n    value: function _setScrollbarWidth() {\n      var scrollbarWidth = this.getScrollbarWidth();\n      this.setState({\n        scrollbarWidth: scrollbarWidth\n      });\n    }\n  }]);\n\n  return Table;\n}(React.PureComponent);\n\n_defineProperty(Table, \"defaultProps\", {\n  disableHeader: false,\n  estimatedRowSize: 30,\n  headerHeight: 0,\n  headerStyle: {},\n  noRowsRenderer: function noRowsRenderer() {\n    return null;\n  },\n  onRowsRendered: function onRowsRendered() {\n    return null;\n  },\n  onScroll: function onScroll() {\n    return null;\n  },\n  overscanIndicesGetter: accessibilityOverscanIndicesGetter,\n  overscanRowCount: 10,\n  rowRenderer: defaultRowRenderer,\n  headerRowRenderer: defaultHeaderRowRenderer,\n  rowStyle: {},\n  scrollToAlignment: 'auto',\n  scrollToIndex: -1,\n  style: {}\n});\n\nexport { Table as default };\nTable.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  /** This is just set on the grid top element. */\n  'aria-label': PropTypes.string,\n\n  /** This is just set on the grid top element. */\n  'aria-labelledby': PropTypes.string,\n\n  /**\n   * Removes fixed height from the scrollingContainer so that the total height\n   * of rows can stretch the window. Intended for use with WindowScroller\n   */\n  autoHeight: PropTypes.bool,\n\n  /** One or more Columns describing the data displayed in this row */\n  children: function children(props) {\n    var children = React.Children.toArray(props.children);\n\n    for (var i = 0; i < children.length; i++) {\n      var childType = children[i].type;\n\n      if (childType !== Column && !(childType.prototype instanceof Column)) {\n        return new Error('Table only accepts children of type Column');\n      }\n    }\n  },\n\n  /** Optional CSS class name */\n  className: PropTypes.string,\n\n  /** Disable rendering the header at all */\n  disableHeader: PropTypes.bool,\n\n  /**\n   * Used to estimate the total height of a Table before all of its rows have actually been measured.\n   * The estimated total height is adjusted as rows are rendered.\n   */\n  estimatedRowSize: PropTypes.number.isRequired,\n\n  /** Optional custom CSS class name to attach to inner Grid element. */\n  gridClassName: PropTypes.string,\n\n  /** Optional inline style to attach to inner Grid element. */\n  gridStyle: PropTypes.object,\n\n  /** Optional CSS class to apply to all column headers */\n  headerClassName: PropTypes.string,\n\n  /** Fixed height of header row */\n  headerHeight: PropTypes.number.isRequired,\n\n  /**\n   * Responsible for rendering a table row given an array of columns:\n   * Should implement the following interface: ({\n   *   className: string,\n   *   columns: any[],\n   *   style: any\n   * }): PropTypes.node\n   */\n  headerRowRenderer: PropTypes.func,\n\n  /** Optional custom inline style to attach to table header columns. */\n  headerStyle: PropTypes.object,\n\n  /** Fixed/available height for out DOM element */\n  height: PropTypes.number.isRequired,\n\n  /** Optional id */\n  id: PropTypes.string,\n\n  /** Optional renderer to be used in place of table body rows when rowCount is 0 */\n  noRowsRenderer: PropTypes.func,\n\n  /**\n   * Optional callback when a column is clicked.\n   * ({ columnData: any, dataKey: string }): void\n   */\n  onColumnClick: PropTypes.func,\n\n  /**\n   * Optional callback when a column's header is clicked.\n   * ({ columnData: any, dataKey: string }): void\n   */\n  onHeaderClick: PropTypes.func,\n\n  /**\n   * Callback invoked when a user clicks on a table row.\n   * ({ index: number }): void\n   */\n  onRowClick: PropTypes.func,\n\n  /**\n   * Callback invoked when a user double-clicks on a table row.\n   * ({ index: number }): void\n   */\n  onRowDoubleClick: PropTypes.func,\n\n  /**\n   * Callback invoked when the mouse leaves a table row.\n   * ({ index: number }): void\n   */\n  onRowMouseOut: PropTypes.func,\n\n  /**\n   * Callback invoked when a user moves the mouse over a table row.\n   * ({ index: number }): void\n   */\n  onRowMouseOver: PropTypes.func,\n\n  /**\n   * Callback invoked when a user right-clicks on a table row.\n   * ({ index: number }): void\n   */\n  onRowRightClick: PropTypes.func,\n\n  /**\n   * Callback invoked with information about the slice of rows that were just rendered.\n   * ({ startIndex, stopIndex }): void\n   */\n  onRowsRendered: PropTypes.func,\n\n  /**\n   * Callback invoked whenever the scroll offset changes within the inner scrollable region.\n   * This callback can be used to sync scrolling between lists, tables, or grids.\n   * ({ clientHeight, scrollHeight, scrollTop }): void\n   */\n  onScroll: PropTypes.func.isRequired,\n\n  /** See Grid#overscanIndicesGetter */\n  overscanIndicesGetter: PropTypes.func.isRequired,\n\n  /**\n   * Number of rows to render above/below the visible bounds of the list.\n   * These rows can help for smoother scrolling on touch devices.\n   */\n  overscanRowCount: PropTypes.number.isRequired,\n\n  /**\n   * Optional CSS class to apply to all table rows (including the header row).\n   * This property can be a CSS class name (string) or a function that returns a class name.\n   * If a function is provided its signature should be: ({ index: number }): string\n   */\n  rowClassName: PropTypes.oneOfType([PropTypes.string, PropTypes.func]),\n\n  /**\n   * Callback responsible for returning a data row given an index.\n   * ({ index: number }): any\n   */\n  rowGetter: PropTypes.func.isRequired,\n\n  /**\n   * Either a fixed row height (number) or a function that returns the height of a row given its index.\n   * ({ index: number }): number\n   */\n  rowHeight: PropTypes.oneOfType([PropTypes.number, PropTypes.func]).isRequired,\n\n  /** Number of rows in table. */\n  rowCount: PropTypes.number.isRequired,\n\n  /**\n   * Responsible for rendering a table row given an array of columns:\n   * Should implement the following interface: ({\n   *   className: string,\n   *   columns: Array,\n   *   index: number,\n   *   isScrolling: boolean,\n   *   onRowClick: ?Function,\n   *   onRowDoubleClick: ?Function,\n   *   onRowMouseOver: ?Function,\n   *   onRowMouseOut: ?Function,\n   *   rowData: any,\n   *   style: any\n   * }): PropTypes.node\n   */\n  rowRenderer: PropTypes.func,\n\n  /** Optional custom inline style to attach to table rows. */\n  rowStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.func]).isRequired,\n\n  /** See Grid#scrollToAlignment */\n  scrollToAlignment: PropTypes.oneOf(['auto', 'end', 'start', 'center']).isRequired,\n\n  /** Row index to ensure visible (by forcefully scrolling if necessary) */\n  scrollToIndex: PropTypes.number.isRequired,\n\n  /** Vertical offset. */\n  scrollTop: PropTypes.number,\n\n  /**\n   * Sort function to be called if a sortable header is clicked.\n   * Should implement the following interface: ({\n   *   defaultSortDirection: 'ASC' | 'DESC',\n   *   event: MouseEvent,\n   *   sortBy: string,\n   *   sortDirection: SortDirection\n   * }): void\n   */\n  sort: PropTypes.func,\n\n  /** Table data is currently sorted by this :dataKey (if it is sorted at all) */\n  sortBy: PropTypes.string,\n\n  /** Table data is currently sorted in this direction (if it is sorted at all) */\n  sortDirection: PropTypes.oneOf([SortDirection.ASC, SortDirection.DESC]),\n\n  /** Optional inline style */\n  style: PropTypes.object,\n\n  /** Tab index for focus */\n  tabIndex: PropTypes.number,\n\n  /** Width of list */\n  width: PropTypes.number.isRequired\n} : {};\nimport { bpfrpt_proptype_CellPosition } from \"../Grid\";", "/**\n * Default accessor for returning a cell value for a given attribute.\n * This function expects to operate on either a vanilla Object or an Immutable Map.\n * You should override the column's cellDataGetter if your data is some other type of object.\n */\nexport default function defaultCellDataGetter(_ref) {\n  var dataKey = _ref.dataKey,\n      rowData = _ref.rowData;\n\n  if (typeof rowData.get === 'function') {\n    return rowData.get(dataKey);\n  } else {\n    return rowData[dataKey];\n  }\n}\nimport { bpfrpt_proptype_CellDataGetterParams } from \"./types\";", "/**\n * Default cell renderer that displays an attribute as a simple string\n * You should override the column's cellRenderer if your data is some other type of object.\n */\nexport default function defaultCellRenderer(_ref) {\n  var cellData = _ref.cellData;\n\n  if (cellData == null) {\n    return '';\n  } else {\n    return String(cellData);\n  }\n}\nimport { bpfrpt_proptype_CellRendererParams } from \"./types\";", "import createMultiSort from './createMultiSort';\nimport defaultCellDataGetter from './defaultCellDataGetter';\nimport defaultCell<PERSON>enderer from './defaultCellRenderer';\nimport defaultHeaderRowRenderer from './defaultHeaderRowRenderer.js';\nimport defaultHeader<PERSON>enderer from './defaultHeaderRenderer';\nimport defaultRowRenderer from './defaultRowRenderer';\nimport Column from './Column';\nimport SortDirection from './SortDirection';\nimport SortIndicator from './SortIndicator';\nimport Table from './Table';\nexport default Table;\nexport { createMultiSort, defaultCellDataGetter, defaultCellRenderer, defaultHeaderRowRenderer, defaultHeaderRenderer, defaultRowRenderer, Column, SortDirection, SortIndicator, Table };", "import { requestAnimationTimeout, cancelAnimationTimeout } from '../../utils/requestAnimationTimeout';\nvar mountedInstances = [];\nvar originalBodyPointerEvents = null;\nvar disablePointerEventsTimeoutId = null;\n\nfunction enablePointerEventsIfDisabled() {\n  if (disablePointerEventsTimeoutId) {\n    disablePointerEventsTimeoutId = null;\n\n    if (document.body && originalBodyPointerEvents != null) {\n      document.body.style.pointerEvents = originalBodyPointerEvents;\n    }\n\n    originalBodyPointerEvents = null;\n  }\n}\n\nfunction enablePointerEventsAfterDelayCallback() {\n  enablePointerEventsIfDisabled();\n  mountedInstances.forEach(function (instance) {\n    return instance.__resetIsScrolling();\n  });\n}\n\nfunction enablePointerEventsAfterDelay() {\n  if (disablePointerEventsTimeoutId) {\n    cancelAnimationTimeout(disablePointerEventsTimeoutId);\n  }\n\n  var maximumTimeout = 0;\n  mountedInstances.forEach(function (instance) {\n    maximumTimeout = Math.max(maximumTimeout, instance.props.scrollingResetTimeInterval);\n  });\n  disablePointerEventsTimeoutId = requestAnimationTimeout(enablePointerEventsAfterDelayCallback, maximumTimeout);\n}\n\nfunction onScrollWindow(event) {\n  if (event.currentTarget === window && originalBodyPointerEvents == null && document.body) {\n    originalBodyPointerEvents = document.body.style.pointerEvents;\n    document.body.style.pointerEvents = 'none';\n  }\n\n  enablePointerEventsAfterDelay();\n  mountedInstances.forEach(function (instance) {\n    if (instance.props.scrollElement === event.currentTarget) {\n      instance.__handleWindowScrollEvent();\n    }\n  });\n}\n\nexport function registerScrollListener(component, element) {\n  if (!mountedInstances.some(function (instance) {\n    return instance.props.scrollElement === element;\n  })) {\n    element.addEventListener('scroll', onScrollWindow);\n  }\n\n  mountedInstances.push(component);\n}\nexport function unregisterScrollListener(component, element) {\n  mountedInstances = mountedInstances.filter(function (instance) {\n    return instance !== component;\n  });\n\n  if (!mountedInstances.length) {\n    element.removeEventListener('scroll', onScrollWindow);\n\n    if (disablePointerEventsTimeoutId) {\n      cancelAnimationTimeout(disablePointerEventsTimeoutId);\n      enablePointerEventsIfDisabled();\n    }\n  }\n}\nimport { bpfrpt_proptype_WindowScroller } from \"../WindowScroller.js\";", "/**\n * Gets the dimensions of the element, accounting for API differences between\n * `window` and other DOM elements.\n */\n// TODO Move this into WindowScroller and import from there\nvar isWindow = function isWindow(element) {\n  return element === window;\n};\n\nvar getBoundingBox = function getBoundingBox(element) {\n  return element.getBoundingClientRect();\n};\n\nexport function getDimensions(scrollElement, props) {\n  if (!scrollElement) {\n    return {\n      height: props.serverHeight,\n      width: props.serverWidth\n    };\n  } else if (isWindow(scrollElement)) {\n    var _window = window,\n        innerHeight = _window.innerHeight,\n        innerWidth = _window.innerWidth;\n    return {\n      height: typeof innerHeight === 'number' ? innerHeight : 0,\n      width: typeof innerWidth === 'number' ? innerWidth : 0\n    };\n  } else {\n    return getBoundingBox(scrollElement);\n  }\n}\n/**\n * Gets the vertical and horizontal position of an element within its scroll container.\n * Elements that have been “scrolled past” return negative values.\n * Handles edge-case where a user is navigating back (history) from an already-scrolled page.\n * In this case the body’s top or left position will be a negative number and this element’s top or left will be increased (by that amount).\n */\n\nexport function getPositionOffset(element, container) {\n  if (isWindow(container) && document.documentElement) {\n    var containerElement = document.documentElement;\n    var elementRect = getBoundingBox(element);\n    var containerRect = getBoundingBox(containerElement);\n    return {\n      top: elementRect.top - containerRect.top,\n      left: elementRect.left - containerRect.left\n    };\n  } else {\n    var scrollOffset = getScrollOffset(container);\n\n    var _elementRect = getBoundingBox(element);\n\n    var _containerRect = getBoundingBox(container);\n\n    return {\n      top: _elementRect.top + scrollOffset.top - _containerRect.top,\n      left: _elementRect.left + scrollOffset.left - _containerRect.left\n    };\n  }\n}\n/**\n * Gets the vertical and horizontal scroll amount of the element, accounting for IE compatibility\n * and API differences between `window` and other DOM elements.\n */\n\nexport function getScrollOffset(element) {\n  if (isWindow(element) && document.documentElement) {\n    return {\n      top: 'scrollY' in window ? window.scrollY : document.documentElement.scrollTop,\n      left: 'scrollX' in window ? window.scrollX : document.documentElement.scrollLeft\n    };\n  } else {\n    return {\n      top: element.scrollTop,\n      left: element.scrollLeft\n    };\n  }\n}", "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\n\nvar _class, _temp;\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { registerScrollListener, unregisterScrollListener } from './utils/onScroll';\nimport { getDimensions, getPositionOffset, getScrollOffset } from './utils/dimensions';\nimport createDetectElementResize from '../vendor/detectElementResize';\n\n/**\n * Specifies the number of miliseconds during which to disable pointer events while a scroll is in progress.\n * This improves performance and makes scrolling smoother.\n */\nexport var IS_SCROLLING_TIMEOUT = 150;\n\nvar getWindow = function getWindow() {\n  return typeof window !== 'undefined' ? window : undefined;\n};\n\nvar WindowScroller = (_temp = _class =\n/*#__PURE__*/\nfunction (_React$PureComponent) {\n  _inherits(WindowScroller, _React$PureComponent);\n\n  function WindowScroller() {\n    var _getPrototypeOf2;\n\n    var _this;\n\n    _classCallCheck(this, WindowScroller);\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _possibleConstructorReturn(this, (_getPrototypeOf2 = _getPrototypeOf(WindowScroller)).call.apply(_getPrototypeOf2, [this].concat(args)));\n\n    _defineProperty(_assertThisInitialized(_this), \"_window\", getWindow());\n\n    _defineProperty(_assertThisInitialized(_this), \"_isMounted\", false);\n\n    _defineProperty(_assertThisInitialized(_this), \"_positionFromTop\", 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_positionFromLeft\", 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_detectElementResize\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"_child\", void 0);\n\n    _defineProperty(_assertThisInitialized(_this), \"state\", _objectSpread({}, getDimensions(_this.props.scrollElement, _this.props), {\n      isScrolling: false,\n      scrollLeft: 0,\n      scrollTop: 0\n    }));\n\n    _defineProperty(_assertThisInitialized(_this), \"_registerChild\", function (element) {\n      if (element && !(element instanceof Element)) {\n        console.warn('WindowScroller registerChild expects to be passed Element or null');\n      }\n\n      _this._child = element;\n\n      _this.updatePosition();\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_onChildScroll\", function (_ref) {\n      var scrollTop = _ref.scrollTop;\n\n      if (_this.state.scrollTop === scrollTop) {\n        return;\n      }\n\n      var scrollElement = _this.props.scrollElement;\n\n      if (scrollElement) {\n        if (typeof scrollElement.scrollTo === 'function') {\n          scrollElement.scrollTo(0, scrollTop + _this._positionFromTop);\n        } else {\n          scrollElement.scrollTop = scrollTop + _this._positionFromTop;\n        }\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_registerResizeListener\", function (element) {\n      if (element === window) {\n        window.addEventListener('resize', _this._onResize, false);\n      } else {\n        _this._detectElementResize.addResizeListener(element, _this._onResize);\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_unregisterResizeListener\", function (element) {\n      if (element === window) {\n        window.removeEventListener('resize', _this._onResize, false);\n      } else if (element) {\n        _this._detectElementResize.removeResizeListener(element, _this._onResize);\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_onResize\", function () {\n      _this.updatePosition();\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"__handleWindowScrollEvent\", function () {\n      if (!_this._isMounted) {\n        return;\n      }\n\n      var onScroll = _this.props.onScroll;\n      var scrollElement = _this.props.scrollElement;\n\n      if (scrollElement) {\n        var scrollOffset = getScrollOffset(scrollElement);\n        var scrollLeft = Math.max(0, scrollOffset.left - _this._positionFromLeft);\n        var scrollTop = Math.max(0, scrollOffset.top - _this._positionFromTop);\n\n        _this.setState({\n          isScrolling: true,\n          scrollLeft: scrollLeft,\n          scrollTop: scrollTop\n        });\n\n        onScroll({\n          scrollLeft: scrollLeft,\n          scrollTop: scrollTop\n        });\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"__resetIsScrolling\", function () {\n      _this.setState({\n        isScrolling: false\n      });\n    });\n\n    return _this;\n  }\n\n  _createClass(WindowScroller, [{\n    key: \"updatePosition\",\n    value: function updatePosition() {\n      var scrollElement = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : this.props.scrollElement;\n      var onResize = this.props.onResize;\n      var _this$state = this.state,\n          height = _this$state.height,\n          width = _this$state.width;\n      var thisNode = this._child || ReactDOM.findDOMNode(this);\n\n      if (thisNode instanceof Element && scrollElement) {\n        var offset = getPositionOffset(thisNode, scrollElement);\n        this._positionFromTop = offset.top;\n        this._positionFromLeft = offset.left;\n      }\n\n      var dimensions = getDimensions(scrollElement, this.props);\n\n      if (height !== dimensions.height || width !== dimensions.width) {\n        this.setState({\n          height: dimensions.height,\n          width: dimensions.width\n        });\n        onResize({\n          height: dimensions.height,\n          width: dimensions.width\n        });\n      }\n    }\n  }, {\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var scrollElement = this.props.scrollElement;\n      this._detectElementResize = createDetectElementResize();\n      this.updatePosition(scrollElement);\n\n      if (scrollElement) {\n        registerScrollListener(this, scrollElement);\n\n        this._registerResizeListener(scrollElement);\n      }\n\n      this._isMounted = true;\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps, prevState) {\n      var scrollElement = this.props.scrollElement;\n      var prevScrollElement = prevProps.scrollElement;\n\n      if (prevScrollElement !== scrollElement && prevScrollElement != null && scrollElement != null) {\n        this.updatePosition(scrollElement);\n        unregisterScrollListener(this, prevScrollElement);\n        registerScrollListener(this, scrollElement);\n\n        this._unregisterResizeListener(prevScrollElement);\n\n        this._registerResizeListener(scrollElement);\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      var scrollElement = this.props.scrollElement;\n\n      if (scrollElement) {\n        unregisterScrollListener(this, scrollElement);\n\n        this._unregisterResizeListener(scrollElement);\n      }\n\n      this._isMounted = false;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var children = this.props.children;\n      var _this$state2 = this.state,\n          isScrolling = _this$state2.isScrolling,\n          scrollTop = _this$state2.scrollTop,\n          scrollLeft = _this$state2.scrollLeft,\n          height = _this$state2.height,\n          width = _this$state2.width;\n      return children({\n        onChildScroll: this._onChildScroll,\n        registerChild: this._registerChild,\n        height: height,\n        isScrolling: isScrolling,\n        scrollLeft: scrollLeft,\n        scrollTop: scrollTop,\n        width: width\n      });\n    }\n  }]);\n\n  return WindowScroller;\n}(React.PureComponent), _defineProperty(_class, \"propTypes\", process.env.NODE_ENV === 'production' ? null : {\n  /**\n   * Function responsible for rendering children.\n   * This function should implement the following signature:\n   * ({ height, isScrolling, scrollLeft, scrollTop, width }) => PropTypes.element\n   */\n  \"children\": PropTypes.func.isRequired,\n\n  /** Callback to be invoked on-resize: ({ height, width }) */\n  \"onResize\": PropTypes.func.isRequired,\n\n  /** Callback to be invoked on-scroll: ({ scrollLeft, scrollTop }) */\n  \"onScroll\": PropTypes.func.isRequired,\n\n  /** Element to attach scroll event listeners. Defaults to window. */\n  \"scrollElement\": PropTypes.oneOfType([PropTypes.any, function () {\n    return (typeof Element === \"function\" ? PropTypes.instanceOf(Element) : PropTypes.any).apply(this, arguments);\n  }]),\n\n  /**\n   * Wait this amount of time after the last scroll event before resetting child `pointer-events`.\n   */\n  \"scrollingResetTimeInterval\": PropTypes.number.isRequired,\n\n  /** Height used for server-side rendering */\n  \"serverHeight\": PropTypes.number.isRequired,\n\n  /** Width used for server-side rendering */\n  \"serverWidth\": PropTypes.number.isRequired\n}), _temp);\n\n_defineProperty(WindowScroller, \"defaultProps\", {\n  onResize: function onResize() {},\n  onScroll: function onScroll() {},\n  scrollingResetTimeInterval: IS_SCROLLING_TIMEOUT,\n  scrollElement: getWindow(),\n  serverHeight: 0,\n  serverWidth: 0\n});\n\nexport { WindowScroller as default };\nimport PropTypes from \"prop-types\";"], "sourceRoot": ""}