(window.webpackJsonp=window.webpackJsonp||[]).push([[60],{1827:function(t,e,n){var o=n(32),i=n(1828);"string"==typeof(i=i.__esModule?i.default:i)&&(i=[[t.i,i,""]]);var a={insert:function(t){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(t);let e;e=document.getElementsByTagName("apryse-webviewer"),e.length||(e=function t(e,n=document){const o=[];return n.querySelectorAll(e).forEach(t=>o.push(t)),n.querySelectorAll("*").forEach(n=>{n.shadowRoot&&o.push(...t(e,n.shadowRoot))}),o}("apryse-webviewer"));const n=[];for(let o=0;o<e.length;o++){const i=e[o];if(0===o)i.shadowRoot.appendChild(t),t.onload=function(){n.length>0&&n.forEach(e=>{e.innerHTML=t.innerHTML})};else{const e=t.cloneNode(!0);i.shadowRoot.appendChild(e),n.push(e)}}},singleton:!1};o(i,a);t.exports=i.locals||{}},1828:function(t,e,n){(e=t.exports=n(33)(!1)).push([t.i,".open.ContextMenuPopup{visibility:visible}.closed.ContextMenuPopup{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.ContextMenuPopup{position:absolute;z-index:70;display:flex;justify-content:center;align-items:center}.ContextMenuPopup:empty{padding:0}.ContextMenuPopup .buttons{display:flex}.ContextMenuPopup .Button{margin:4px;width:32px;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ContextMenuPopup .Button{width:42px;height:42px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ContextMenuPopup .Button{width:42px;height:42px}}.ContextMenuPopup .Button:hover{background:var(--popup-button-hover)}.ContextMenuPopup .Button:hover:disabled{background:none}.ContextMenuPopup .Button .Icon{width:18px;height:18px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ContextMenuPopup .Button .Icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ContextMenuPopup .Button .Icon{width:24px;height:24px}}.is-vertical.ContextMenuPopup .Button.main-menu-button{width:100%;border-radius:0;justify-content:flex-start;padding-left:var(--padding-small);padding-right:var(--padding-small);margin:0 0 var(--padding-tiny) 0}.is-vertical.ContextMenuPopup .Button.main-menu-button:first-child{margin-top:var(--padding-tiny)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .is-vertical.ContextMenuPopup .Button.main-menu-button{width:100%;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .is-vertical.ContextMenuPopup .Button.main-menu-button{width:100%;height:32px}}.is-vertical.ContextMenuPopup .Button.main-menu-button .Icon{margin-right:10px}.is-vertical.ContextMenuPopup .Button.main-menu-button span{white-space:nowrap}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.ContextMenuPopup{box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background);border-radius:4px}.ContextMenuPopup.is-horizontal .container{display:inherit}.ContextMenuPopup.is-vertical{flex-direction:column;align-items:flex-start}.ContextMenuPopup.isOfficeEditor .container{display:block}.ContextMenuPopup.isOfficeEditor .container .office-action-item{width:300px;padding:8px;display:flex;justify-content:space-between;cursor:pointer}.ContextMenuPopup.isOfficeEditor .container .office-action-item:hover{background-color:var(--blue-4)}.ContextMenuPopup.isOfficeEditor .container .office-action-item.disabled{cursor:default;background-color:inherit;color:var(--disabled-text)}.ContextMenuPopup.isOfficeEditor .container .office-action-item .icon-title{display:flex;align-items:center}.ContextMenuPopup.isOfficeEditor .container .office-action-item .icon-title .Icon{margin-right:10px}.ContextMenuPopup.isOfficeEditor .container .office-action-item .shortcut{display:flex;align-items:center}.ContextMenuPopup .divider{height:1px;background:var(--divider);margin-top:8px;margin-bottom:8px;width:100%}",""]),e.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},2012:function(t,e,n){"use strict";n.r(e);n(36),n(88),n(19),n(12),n(13),n(8),n(14),n(10),n(9),n(11),n(16),n(15),n(20),n(18),n(26),n(27),n(25),n(22),n(30),n(28),n(45),n(23),n(24),n(47),n(46);var o=n(0),i=n.n(o),a=n(17),r=n.n(a),c=n(176),l=n.n(c),u=n(6),s=n(296),p=n(429),d=n(84),m=n(1739),f=n(43),E=n(135),b=n(102),h=n(2),g=n(3),O=n(1),x=n(38),C=n(73),y=n(21),w=n(5),T=n(149);n(1827);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function P(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function _(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?P(Object(n),!0).forEach((function(e){k(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):P(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function k(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==v(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==v(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===v(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function j(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,i,a,r,c=[],l=!0,u=!1;try{if(a=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;l=!1}else for(;!(l=(o=a.call(n)).done)&&(c.push(o.value),c.length!==e);l=!0);}catch(t){u=!0,i=t}finally{try{if(!l&&null!=n.return&&(r=n.return(),Object(r)!==r))return}finally{if(u)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return M(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return M(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function M(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}var I=function(t){var e=t.dataElement,n=t.onClick,o=t.img,a=t.title,c=t.shortcut,l=void 0===c?"":c,s=t.disabled,d=void 0!==s&&s,m=j(Object(p.a)(),1)[0],E=Object(u.d)();return i.a.createElement("div",{className:r()("office-action-item",{disabled:d}),onClick:function(t){d||(n(),E(h.a.closeElement(w.a.CONTEXT_MENU_POPUP))),t.stopPropagation()},tabIndex:d?-1:0,"data-element":e,onKeyDown:function(t){"Enter"!==t.key||d||(n(),E(h.a.closeElement(w.a.CONTEXT_MENU_POPUP)))}},i.a.createElement("div",{className:"icon-title"},o&&i.a.createElement(f.a,{glyph:o,disabled:d}),!o&&i.a.createElement("span",{className:"Icon"}),i.a.createElement("div",null,m(a))),i.a.createElement("div",{className:"shortcut"},l))},N=function(t){var e=t.clickPosition,n=Object(u.e)((function(t){return g.a.isElementOpen(t,w.a.CONTEXT_MENU_POPUP)})),a=Object(u.e)((function(t){return g.a.isElementDisabled(t,w.a.CONTEXT_MENU_POPUP)})),c=Object(u.e)(g.a.isRightClickAnnotationPopupEnabled),f=Object(u.e)(g.a.isMultiViewerMode),v=Object(u.e)(g.a.getActiveDocumentViewerKey),P=Object(u.e)(g.a.isCursorInTable),k=Object(u.e)(g.a.isSpreadsheetEditorModeEnabled),M=Object(u.e)(g.a.getSpreadsheetEditorEditMode),N=M===T.d.VIEW_ONLY,R=j(Object(o.useState)(k&&N),2),A=R[0],S=R[1],F=j(Object(p.a)(),1)[0],B=Object(u.d)(),D=Object(u.f)(),U=j(Object(o.useState)({left:0,top:0}),2),L=U[0],H=U[1],W=Object(o.useRef)(),V=!!x.l||Object(x.k)();Object(E.a)(W,(function(){B(h.a.closeElement(w.a.CONTEXT_MENU_POPUP))})),Object(o.useEffect)((function(){n&&B(h.a.closeElements([w.a.ANNOTATION_POPUP,w.a.TEXT_POPUP,w.a.INLINE_COMMENT_POPUP]))}),[n]),Object(o.useEffect)((function(){var t=M===T.d.VIEW_ONLY;S(k&&t)}),[k,M]),Object(o.useLayoutEffect)((function(){if(!A){var t=W.current.getBoundingClientRect(),n=t.width,o=t.height,i=f?"#DocumentContainer".concat(v):".DocumentContainer",a=Object(y.a)().querySelector(i);if(a){var r=a.getBoundingClientRect(),c=X(e,r,n,o),l=c.left,u=c.top;H({left:l,top:u})}}}),[e,f,v,A]);var X=function(t,e,n,o){var i=t.left,a=t.top,r=Y(),c=r.offsetLeft,l=r.offsetTop;a-=l;return(i-=c)<e.left-c&&(i=e.left+2-c),i+n>e.right-c&&(i=e.right-n-2-c),a<e.top-l&&(a=e.top+2-l),a+o>e.bottom-l&&(a=e.bottom-o-2),{left:i,top:a}},Y=function(){var t=0,e=0;if(window.isApryseWebViewerWebComponent){var n,o=null===(n=Object(y.a)())||void 0===n?void 0:n.host,i=null==o?void 0:o.getBoundingClientRect();i&&(t=i.left,e=i.top,t+=o.scrollLeft,e+=o.scrollTop)}return{offsetLeft:t,offsetTop:e}},z=x.j?"⌘ Command":"Ctrl",q=x.j?"⌘Cmd":"Ctrl",G=function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(x.c){var e=F(t?"officeEditor.pastingTitle":"officeEditor.pastingWithoutFormatTitle"),n=F(t?"officeEditor.pastingMessage":"officeEditor.pastingWithoutFormatMessage"),o="".concat(z,t?" + V":" + Shift + V"),i=F("action.close"),a={message:"".concat(n,":\n\n").concat(o),title:e,confirmBtnText:i,onConfirm:function(){setTimeout((function(){O.a.getViewerElement().focus()}))},onCancel:function(){setTimeout((function(){O.a.getViewerElement().focus()}))}};B(h.a.showWarningMessage(a))}else O.a.getOfficeEditor().pasteText(t)};if(a)return null;var J=i.a.createElement("div",{className:r()("Popup","ContextMenuPopup",{open:n,closed:!n,isOfficeEditor:Object(C.g)(),"is-vertical":c&&!Object(C.g)(),"is-horizontal":!c&&!Object(C.g)()}),ref:W,"data-element":w.a.CONTEXT_MENU_POPUP,style:_({},L),onClick:function(){return B(h.a.closeElement(w.a.CONTEXT_MENU_POPUP))}},i.a.createElement(s.a,{locked:n&&0!==L.top&&0!==L.left},i.a.createElement("div",{className:"container"},Object(C.g)()?i.a.createElement(i.a.Fragment,null,i.a.createElement(I,{title:"action.cut",img:"icon-cut",dataElement:w.a.OFFICE_EDITOR_CUT,onClick:function(){return O.a.getOfficeEditor().cutSelectedText()},shortcut:"".concat(q,"+X"),disabled:!O.a.getOfficeEditor().isTextSelected()}),i.a.createElement(I,{title:"action.copy",img:"icon-copy",dataElement:w.a.OFFICE_EDITOR_COPY,onClick:function(){return O.a.getOfficeEditor().copySelectedText()},shortcut:"".concat(q,"+C"),disabled:!O.a.getOfficeEditor().isTextSelected()}),i.a.createElement(I,{title:"action.paste",img:"icon-paste",dataElement:w.a.OFFICE_EDITOR_PASTE,onClick:function(){return G()},shortcut:"".concat(q,"+V")}),i.a.createElement(I,{title:"action.pasteWithoutFormatting",img:"icon-paste-without-formatting",dataElement:w.a.OFFICE_EDITOR_PASTE_WITHOUT_FORMATTING,onClick:function(){return G(!1)},shortcut:"".concat(q,"+Shift+V")}),!P&&i.a.createElement(I,{title:"action.delete",img:"icon-delete-line",dataElement:w.a.OFFICE_EDITOR_DELETE,onClick:function(){return O.a.getOfficeEditor().removeSelection()},disabled:!(O.a.getOfficeEditor().isTextSelected()||O.a.getOfficeEditor().isImageSelected())}),P&&i.a.createElement(i.a.Fragment,null,i.a.createElement("div",{className:"divider"}),i.a.createElement(I,{title:"officeEditor.insertRowAbove",dataElement:w.a.OFFICE_EDITOR_INSERT_ROW_ABOVE,onClick:function(){return O.a.getOfficeEditor().insertRows(1,!0)}}),i.a.createElement(I,{title:"officeEditor.insertRowBelow",dataElement:w.a.OFFICE_EDITOR_INSERT_ROW_BELOW,onClick:function(){return O.a.getOfficeEditor().insertRows(1,!1)}}),i.a.createElement(I,{title:"officeEditor.insertColumnRight",dataElement:w.a.OFFICE_EDITOR_INSERT_COLUMN_RIGHT,onClick:function(){return O.a.getOfficeEditor().insertColumns(1,!0)}}),i.a.createElement(I,{title:"officeEditor.insertColumnLeft",dataElement:w.a.OFFICE_EDITOR_INSERT_COLUMN_LEFT,onClick:function(){return O.a.getOfficeEditor().insertColumns(1,!1)}}),i.a.createElement(I,{title:"officeEditor.deleteRow",dataElement:w.a.OFFICE_EDITOR_DELETE_ROW,onClick:function(){return O.a.getOfficeEditor().removeRows()}}),i.a.createElement(I,{title:"officeEditor.deleteColumn",dataElement:w.a.OFFICE_EDITOR_DELETE_COLUMN,onClick:function(){return O.a.getOfficeEditor().removeColumns()}}),i.a.createElement(I,{title:"officeEditor.deleteTable",dataElement:w.a.OFFICE_EDITOR_DELETE_TABLE,onClick:function(){return O.a.getOfficeEditor().removeTable()}}))):i.a.createElement(m.a,{dataElement:w.a.CONTEXT_MENU_POPUP,childrenClassName:"main-menu-button"},i.a.createElement(d.a,{className:"main-menu-button",dataElement:"panToolButton",label:c?"tool.pan":"",title:c?"":"tool.pan",img:"icon-header-pan",onClick:function(){return Object(b.a)(D,"Pan")}}),i.a.createElement(d.a,{className:"main-menu-button",dataElement:"stickyToolButton",label:c?"annotation.stickyNote":"",title:c?"":"annotation.stickyNote",img:"icon-tool-comment-line",onClick:function(){return Object(b.a)(D,"AnnotationCreateSticky")}}),i.a.createElement(d.a,{className:"main-menu-button",dataElement:"highlightToolButton",label:c?"annotation.highlight":"",title:c?"":"annotation.highlight",img:"icon-tool-highlight",onClick:function(){return Object(b.a)(D,"AnnotationCreateTextHighlight")}}),i.a.createElement(d.a,{className:"main-menu-button",dataElement:"freeHandToolButton",label:c?"annotation.freehand":"",title:c?"":"annotation.freehand",img:"icon-tool-pen-line",onClick:function(){return Object(b.a)(D,"AnnotationCreateFreeHand")}}),i.a.createElement(d.a,{className:"main-menu-button",dataElement:"freeHandHighlightToolButton",label:c?"annotation.freeHandHighlight":"",title:c?"":"annotation.freeHandHighlight",img:"icon-tool-pen-highlight",onClick:function(){return Object(b.a)(D,"AnnotationCreateFreeHandHighlight")}}),i.a.createElement(d.a,{className:"main-menu-button",dataElement:"freeTextToolButton",label:c?"annotation.freetext":"",title:c?"":"annotation.freetext",img:"icon-tool-text-free-text",onClick:function(){return Object(b.a)(D,"AnnotationCreateFreeText")}}),i.a.createElement(d.a,{className:"main-menu-button",dataElement:"markInsertTextToolButton",label:c?"annotation.markInsertText":"",title:c?"":"annotation.markInsertText",img:"ic-insert text",onClick:function(){return Object(b.a)(D,"AnnotationCreateMarkInsertText")}}),i.a.createElement(d.a,{className:"main-menu-button",dataElement:"markReplaceTextToolButton",label:c?"annotation.markReplaceText":"",title:c?"":"annotation.markReplaceText",img:"ic-replace text",onClick:function(){return Object(b.a)(D,"AnnotationCreateMarkReplaceText")}})))));return A?null:x.e||V?J:i.a.createElement(l.a,{cancel:".Button, .cell, .sliders-container svg, select, button, input"},J)},R=i.a.memo(N);e.default=R}}]);
//# sourceMappingURL=chunk.60.js.map