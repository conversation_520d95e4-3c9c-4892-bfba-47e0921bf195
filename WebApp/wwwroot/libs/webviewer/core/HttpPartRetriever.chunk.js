/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[0],{614:function(ya,ua,n){n.r(ua);n.d(ua,"ByteRangeRequest",function(){return w});var na=n(0),ma=n(1);n.n(ma);var oa=n(2),ka=n(211);ya=n(124);var ia=n(359),fa=n(110),x=n(103),y=n(358),r=n(237);n=n(537);var e=[],a=[],f=window,h=function(){return function(){this.Yq=1}}(),b;(function(aa){aa[aa.UNSENT=0]="UNSENT";aa[aa.DONE=4]="DONE"})(b||(b={}));var w=function(){function aa(ea,ba,ca,ha){var pa=this;this.url=ea;this.range=ba;this.Tf=
ca;this.withCredentials=ha;this.Gma=b;this.request=new XMLHttpRequest;this.request.open("GET",this.url,!0);f.Uint8Array&&(this.request.responseType="arraybuffer");ha&&(this.request.withCredentials=ha);z.DISABLE_RANGE_HEADER||(Object(ma.isUndefined)(ba.stop)?this.request.setRequestHeader("Range","bytes=".concat(ba.start)):this.request.setRequestHeader("Range",["bytes=",ba.start,"-",ba.stop-1].join("")));ca&&Object.keys(ca).forEach(function(la){pa.request.setRequestHeader(la,ca[la])});this.request.overrideMimeType?
this.request.overrideMimeType("text/plain; charset=x-user-defined"):this.request.setRequestHeader("Accept-Charset","x-user-defined");this.status=y.a.NOT_STARTED}aa.prototype.start=function(ea){var ba=this,ca=this.request;ca.onreadystatechange=function(){if(ba.aborted)return ba.status=y.a.ABORTED,ea({code:y.a.ABORTED});if(this.readyState===ba.Gma.DONE){ba.fM();var ha=0===window.document.URL.indexOf("file:///");200===ca.status||206===ca.status||ha&&0===ca.status?(ha=f.q$(this),ba.f0(ha,ea)):(ba.status=
y.a.ERROR,ea({code:ba.status,status:ba.status}))}};this.request.send(null);this.status=y.a.STARTED};aa.prototype.f0=function(ea,ba){this.status=y.a.SUCCESS;if(ba)return ba(!1,ea)};aa.prototype.abort=function(){this.fM();this.aborted=!0;this.request.abort()};aa.prototype.fM=function(){var ea=Object(r.c)(this.url,this.range,a);-1!==ea&&a.splice(ea,1);if(0<e.length){ea=e.shift();var ba=new aa(ea.url,ea.range,this.Tf,this.withCredentials);ea.request=ba;a.push(ea);ba.start(Object(r.d)(ea))}};aa.prototype.extend=
function(ea){var ba=Object.assign({},this,ea.prototype);ba.constructor=ea;return ba};return aa}(),z=function(aa){function ea(ba,ca,ha,pa,la){ha=aa.call(this,ba,ha,pa)||this;ha.Pm={};ha.hK=ca;ha.url=ba;ha.DISABLE_RANGE_HEADER=!1;ha.hG=w;ha.H1=3;ha.Tf=la||{};return ha}Object(na.c)(ea,aa);ea.prototype.jD=function(ba,ca,ha){var pa=-1===ba.indexOf("?")?"?":"&";switch(ha){case !1:case x.a.NEVER_CACHE:ba="".concat(ba+pa,"_=").concat(Object(ma.uniqueId)());break;case !0:case x.a.CACHE:ba="".concat(ba+pa,
"_=").concat(ca.start,",").concat(Object(ma.isUndefined)(ca.stop)?"":ca.stop)}return ba};ea.prototype.h7=function(ba,ca,ha,pa){void 0===ha&&(ha={});return new this.hG(ba,ca,ha,pa)};ea.prototype.sxa=function(ba,ca,ha,pa,la){for(var ja=0;ja<e.length;ja++)if(Object(ma.isEqual)(e[ja].range,ca)&&Object(ma.isEqual)(e[ja].url,ba))return e[ja].vj.push(pa),e[ja].BN++,null;for(ja=0;ja<a.length;ja++)if(Object(ma.isEqual)(a[ja].range,ca)&&Object(ma.isEqual)(a[ja].url,ba))return a[ja].vj.push(pa),a[ja].BN++,null;
ha={url:ba,range:ca,hK:ha,vj:[pa],BN:1};if(0===e.length&&a.length<this.H1)return a.push(ha),ha.request=this.h7(ba,ca,la,this.withCredentials),ha;e.push(ha);return null};ea.prototype.qt=function(ba,ca,ha){var pa=this.jD(ba,ca,this.hK);(ba=this.sxa(pa,ca,this.hK,ha,this.Tf))&&ba.request.start(Object(r.d)(ba));return function(){var la=Object(r.c)(pa,ca,a);if(-1!==la){var ja=--a[la].BN;0===ja&&a[la].request&&a[la].request.abort()}else la=Object(r.c)(pa,ca,e),-1!==la&&(ja=--e[la].BN,0===ja&&e.splice(la,
1))}};ea.prototype.C9=function(){return{start:-ka.a}};ea.prototype.zCa=function(){var ba=-(ka.a+ka.e);return{start:ba-ka.d,end:ba}};ea.prototype.gA=function(ba){var ca=this;this.qK=!0;var ha=ka.a;this.qt(this.url,this.C9(),function(pa,la,ja){function qa(){var ra=ca.$e.y9();ca.qt(ca.url,ra,function(sa,ta){if(sa)return Object(oa.i)("Error loading central directory: ".concat(sa)),ba(sa);ta=Object(fa.a)(ta);if(ta.length!==ra.stop-ra.start)return ba("Invalid XOD file: Zip central directory data is wrong size! Should be ".concat(ra.stop-
ra.start," but is ").concat(ta.length));ca.$e.Bea(ta);ca.bT=!0;ca.qK=!1;return ba(!1)})}if(pa)return Object(oa.i)("Error loading end header: ".concat(pa)),ba(pa,la,ja);la=Object(fa.a)(la);if(la.length!==ha)return ba("Invalid XOD file: Zip end header data is wrong size!");try{ca.$e=new ia.a(la)}catch(ra){return ba(ra)}ca.$e.OFa?ca.qt(ca.url,ca.zCa(),function(ra,sa){if(ra)return Object(oa.i)("Error loading zip64 header: ".concat(ra)),ba(ra);sa=Object(fa.a)(sa);ca.$e.tGa(sa);qa()}):qa()})};ea.prototype.j$=
function(ba){ba(Object.keys(this.$e.Fs))};ea.prototype.pZ=function(ba,ca){var ha=this;if(this.$e.P6(ba)){var pa=this.$e.$D(ba);if(pa in this.Pm){var la=this.Uj[ba];la.xy=this.Pm[pa];la.xy.Yq++;la.cancel=la.xy.cancel}else{var ja=this.$e.Qza(ba),qa=this.qt(this.url,ja,function(sa,ta){sa?(Object(oa.i)('Error loading part "'.concat(ba,'": ').concat(sa)),ha.qt(ha.url,ja,function(va,Ba){if(va)return ca(va,ba);ha.Fea(Ba,ja,pa,ba,ca)})):ha.Fea(ta,ja,pa,ba,ca)}),ra=this.Uj[ba];ra&&(ra.Sha=!0,ra.cancel=function(){ra.xy.Yq--;
0===ra.xy.Yq&&(qa(),delete ha.Pm[pa])},this.Pm[pa]=new h(pa),ra.xy=this.Pm[pa],ra.xy.cancel=ra.cancel)}}else delete this.Uj[ba],ca(Error('File not found: "'.concat(ba,'"')),ba)};ea.prototype.Fea=function(ba,ca,ha,pa,la){if(ba.length!==ca.stop-ca.start)la(Error("Part data is wrong size!"),pa);else{do{if(!this.Pm[ha])return;pa=this.Pm[ha].Yq;for(var ja=ca.Nw.length,qa=0;qa<ja;++qa){var ra=ca.Nw[qa];la(!1,ra.Iw,ba["string"===typeof ba?"substring":"subarray"](ra.start,ra.stop),this.$e.Taa(ra.Iw));ra.Iw in
this.Uj&&delete this.Uj[ra.Iw]}}while(pa!==this.Pm[ha].Yq);delete this.Pm[ha]}};ea.DISABLE_RANGE_HEADER=!1;ea.H1=3;return ea}(ya.a);(function(aa){function ea(ba,ca,ha){var pa=aa.call(this)||this,la;for(la in ba)pa[la]=ba[la];pa.bXa=ba;pa.startOffset=ca;pa.endOffset=ha;pa.h7=function(ja,qa,ra,sa){Object(ma.isUndefined)(qa.stop)?(qa.start+=pa.endOffset,qa.stop=pa.endOffset):(qa.start+=pa.startOffset,qa.stop+=pa.startOffset);ja=pa.jD(pa.url,qa,pa.hK);return new ba.hG(ja,qa,ra,sa)};return pa}Object(na.c)(ea,
aa);return ea})(z);Object(n.a)(z);Object(n.b)(z);ua["default"]=z}}]);}).call(this || window)
