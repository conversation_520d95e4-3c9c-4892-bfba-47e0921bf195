{"Provider": "Postgres", "ConnectionStrings": {"Postgres": "", "RedisMaster": "", "RedisReplica": ""}, "Zitadel": {"Url": "https://zitadel.cloud", "ClientId": "", "ClientSecret": "", "ProjectId": "", "MobileClientId": "", "CustomerRoles": [], "ProtectedOrgs": [], "ServiceAccount": {}, "JwtSecret": {}}, "AllowedHosts": "*", "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Expressions"], "MinimumLevel": "Fatal", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"formatter": {"type": "Serilog.Templates.ExpressionTemplate, Serilog.Expressions", "template": "{ {timestamp:@t, level:@l, message: if @x is null then @m else Concat(@m, ' - ', @x), ..@p } }\n"}}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"], "Destructure": [], "Properties": {"Application": "WebApp"}}, "LoggingSource": {"Url": "", "DatasourceId": "", "Username": "", "Password": "", "NamespaceWhitelist": []}, "ThumbnailMicroservice": {"Url": ""}, "FileCachingService": {"FileStoreType": ""}, "DeepZoomMicroservice": {"Url": ""}, "Vite": {"Manifest": "assets.manifest.json"}, "OpenTelemetry": {"TracingEndpoint": "", "MetricEndpoint": ""}}