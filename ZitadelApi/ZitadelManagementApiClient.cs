using System.Diagnostics.CodeAnalysis;
using Google.Protobuf.WellKnownTypes;
using Grpc.Core;
using Levelbuild.Core.FrontendDtos.User;
using Levelbuild.Core.ZitadelApiInterface;
using Levelbuild.Core.ZitadelApiInterface.Constants;
using Microsoft.IdentityModel.Tokens;
using Zitadel.Api;
using Zitadel.Credentials;
using Zitadel.Management.V1;
using Zitadel.Member.V1;
using Zitadel.User.V1;
using SearchQuery = Zitadel.User.V1.SearchQuery;
using Type = Zitadel.User.V1.Type;

namespace Levelbuild.Domain.ZitadelApi;

/// <inheritdoc cref="IZitadelManagementApiClient"/>
[ExcludeFromCodeCoverage]
public class ZitadelManagementApiClient : ZitadelApiClient<ManagementService.ManagementServiceClient>, IZitadelManagementApiClient
{
	private IList<string> _protectedOrgIds;
	private string _projectId;

	#region Init

	private ZitadelManagementApiClient(ManagementService.ManagementServiceClient client, IList<string> protectedOrgIds, string projectId) : base(client)
	{
		_protectedOrgIds = protectedOrgIds;
		_projectId = projectId;
	}

	/// <summary>
	/// Creates and returns a new instance of a Zitadel Management API client.
	/// </summary>
	/// <param name="apiUrl">URL of the target Zitadel instance.</param>
	/// <param name="serviceAccount">The ServiceAccount to use to authenticate API calls.</param>
	/// <param name="protectedOrgIds">A list that contains all org Ids that <b>must not</b> be altered by the API.</param>
	/// <param name="projectId">The Id of the Zitadel project belonging to the WebApp.</param>
	public static ZitadelManagementApiClient GetInstance(string apiUrl, ServiceAccount serviceAccount, IList<string> protectedOrgIds, string projectId)
	{
		var client = Clients.ManagementService(
			new(
				apiUrl,
				ITokenProvider.ServiceAccount(
					apiUrl,
					serviceAccount,
					new() { ApiAccess = true }
				)
			)
		);

		return new ZitadelManagementApiClient(client, protectedOrgIds, projectId);
	}

	#endregion

	#region Methods

	#region Users
	
	/// <inheritdoc />
	public User GetUser(string userId, string orgId)
	{
		var response = Client.GetUserByID(new GetUserByIDRequest()
		{
			Id = userId
		}, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});

		return response.User;
	}
	
	/// <inheritdoc />
	public IList<User> GetActiveHumanUsers(string orgId)
	{
		var response = Client.ListUsers(new ListUsersRequest()
		{
			Queries =
			{
				new SearchQuery()
				{
					StateQuery = new StateQuery()
					{
						State = UserState.Active
					},
					TypeQuery = new TypeQuery()
					{
						Type = Type.Human
					}
				}
			}
		}, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
		
		return response.Result.ToList();
	}
	
	/// <inheritdoc />
	public IList<User> GetActiveMachineUsers(string orgId)
	{
		var response = Client.ListUsers(new ListUsersRequest()
		{
			Queries =
			{
				new SearchQuery()
				{
					StateQuery = new StateQuery()
					{
						State = UserState.Active
					},
					TypeQuery = new TypeQuery()
					{
						Type = Type.Machine
					}
				}
			}
		}, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
		
		return response.Result.ToList();
	}
	
	/// <inheritdoc />
	public IList<User> GetInactiveUsers(string orgId)
	{
		var response = Client.ListUsers(new ListUsersRequest()
		{
			Queries =
			{
				new SearchQuery()
				{
					StateQuery = new StateQuery()
					{
						State = UserState.Inactive
					}
				}
			}
		}, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
		
		return response.Result.ToList();
	}
	
	/// <inheritdoc />
	public string AddHumanUser(UserDto dto, string orgId)
	{
		var response = Client.ImportHumanUser(new()
		{
			UserName = dto.Username,
			Password = dto.Password,
			PasswordChangeRequired = true,
			Email = new()
			{
				Email_ = dto.Email,
				IsEmailVerified = true
			},
			Profile = new()
			{
				DisplayName = dto.Username,
				NickName = dto.Username,
				FirstName = dto.FirstName,
				LastName = dto.LastName
			}
		}, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});

		return response.UserId;
	}
	
	/// <inheritdoc />
	public string AddMachineUser(UserDto dto, string orgId)
	{
		var response = Client.AddMachineUser(new()
		{
			UserName = dto.Username,
			Name = dto.Username,
			Description = "Machine user added by levelbuild",
			AccessTokenType = AccessTokenType.Bearer
		}, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
		
		return response.UserId;
	}

	/// <inheritdoc />
	public void UpdateHumanUser(string userId, string orgId, UserDto dto)
	{
		var userToUpdate = GetUser(userId, orgId);

		if (userToUpdate.UserName != dto.Username)
		{
			Client.UpdateUserName(new()
			{
				UserId = userToUpdate.Id,
				UserName = dto.Username
			}, new Metadata()
			{
				{ "x-zitadel-orgid", orgId }
			});
		}

		if (userToUpdate.UserName != dto.Username || userToUpdate.Human.Profile.FirstName != dto.FirstName ||
			userToUpdate.Human.Profile.LastName != dto.LastName)
		{
			Client.UpdateHumanProfile(new()
			{
				UserId = userToUpdate.Id,
				DisplayName = dto.Username,
				NickName = dto.Username,
				FirstName = dto.FirstName,
				LastName = dto.LastName
			}, new Metadata()
			{
				{ "x-zitadel-orgid", orgId }
			});
		}

		if (userToUpdate.Human.Email.Email_ != dto.Email)
		{
			Client.UpdateHumanEmail(new()
			{
				UserId = userToUpdate.Id,
				Email = dto.Email,
				IsEmailVerified = true
			}, new Metadata()
			{
				{ "x-zitadel-orgid", orgId }
			});
		}
	}
	
	/// <inheritdoc />
	public string CreatePersonalAccessToken(string userId, string orgId, DateTime expirationDate)
	{
		var response = Client.AddPersonalAccessToken(new AddPersonalAccessTokenRequest()
		{
			UserId = userId,
			ExpirationDate = expirationDate.ToTimestamp(),
		}, 
		headers: new() {
			{ "x-zitadel-orgid", orgId }
		});
		
		return response.Token;
	}
	
	/// <inheritdoc />
	public void DeactivateUser(string userId, string orgId)
	{
		if (userId.IsNullOrEmpty())
			throw new RpcException(Status.DefaultCancelled, "No user id was provided.");
		if (orgId.IsNullOrEmpty())
			throw new RpcException(Status.DefaultCancelled, "No org id was provided.");
		
		Client.DeactivateUser(new()
		{
			Id = userId
		}, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
	}
	
	/// <inheritdoc />
	public void ReactivateUser(string userId, string orgId)
	{
		if (userId.IsNullOrEmpty())
			throw new RpcException(Status.DefaultCancelled, "No user id was provided.");
		if (orgId.IsNullOrEmpty())
			throw new RpcException(Status.DefaultCancelled, "No org id was provided.");
		
		Client.ReactivateUser(new()
		{
			Id = userId
		}, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
	}
	
	/// <inheritdoc />
	public void DeleteUser(string userId, string orgId)
	{
		if (userId.IsNullOrEmpty())
			throw new RpcException(Status.DefaultCancelled, "No user id was provided.");
		if (orgId.IsNullOrEmpty())
			throw new RpcException(Status.DefaultCancelled, "No org id was provided.");
		
		Client.RemoveUser(new()
		{
			Id = userId
		}, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
	}
	
	#endregion

	#region Organisations
	
	/// <inheritdoc />
	public Zitadel.Org.V1.Org GetMyOrganisation()
	{
		var response = Client.GetMyOrg(new());
		
		return response.Org;
	}
	
	/// <inheritdoc />
	public Zitadel.Org.V1.Org GetOrganisation(string orgId)
	{
		var response = Client.GetMyOrg(new(), new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});

		return response.Org;
	}
	
	/// <inheritdoc />
	public string AddOrganisation(string name, IEnumerable<string> roles)
	{
		var addOrgResponse = Client.AddOrg(new()
		{
			Name = name
		});

		var orgId = addOrgResponse.Id;
		if (string.IsNullOrEmpty(orgId))
			throw new RpcException(Status.DefaultCancelled, "Organisation was not added to Zitadel. Something went wrong.");

		var addGrantRequest = new AddProjectGrantRequest()
		{
			ProjectId = _projectId,
			GrantedOrgId = orgId,
		};
		addGrantRequest.RoleKeys.AddRange(roles);

		Client.AddProjectGrant(addGrantRequest);

		return orgId;
	}
	
	/// <inheritdoc />
	public void UpdateOrganisation(string orgId, string name)
	{
		if (_protectedOrgIds.Contains(orgId))
			throw new RpcException(Status.DefaultCancelled, "Updating this organization is not allowed!");

		Client.UpdateOrg(new()
		{
			Name = name
		}, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
	}
	
	/// <inheritdoc />
	public void DeactivateOrganisation(string orgId)
	{
		if (orgId.IsNullOrEmpty())
			throw new RpcException(Status.DefaultCancelled, "No org id was provided.");
		
		if (_protectedOrgIds.Contains(orgId))
			throw new RpcException(Status.DefaultCancelled, "Deactivating this organization is not allowed!");

		Client.DeactivateOrg(new(), new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
	}
	
	/// <inheritdoc />
	public void ReactivateOrganisation(string orgId)
	{
		if (orgId.IsNullOrEmpty())
			throw new RpcException(Status.DefaultCancelled, "No org id was provided.");
		
		if (_protectedOrgIds.Contains(orgId))
			throw new RpcException(Status.DefaultCancelled, "Deactivating this organization is not allowed!");

		Client.ReactivateOrg(new(), new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
	}
	
	/// <inheritdoc />
	public void DeleteOrganisation(string orgId)
	{
		if (orgId.IsNullOrEmpty())
			throw new RpcException(Status.DefaultCancelled, "No org id was provided.");
		
		if (_protectedOrgIds.Contains(orgId))
			throw new RpcException(Status.DefaultCancelled, "Deactivating this organization is not allowed!");

		Client.RemoveOrg(new(), new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
	}

	#endregion

	#region Organisation Members
	
	/// <inheritdoc />
	public void AddUserToOrganisation(string userId, string orgId, string[]? roles = null)
	{
		var request = new AddOrgMemberRequest()
		{
			UserId = userId
		};
		
		if(roles != null)
			request.Roles.AddRange(roles);
		
		Client.AddOrgMember(request, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
	}
	
	/// <inheritdoc />
	public void RemoveUserFromOrganisation(string userId, string orgId)
	{
		Client.RemoveOrgMember(new()
		{
			UserId = userId
		}, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
	}

	#endregion

	#region Roles
	
	/// <inheritdoc />
	public IList<UserGrant> ListUserGrants(string orgId)
	{
		var response = Client.ListUserGrants(new()
		{
			Queries =
			{
				new UserGrantQuery()
				{
					ProjectIdQuery = new UserGrantProjectIDQuery()
					{
						ProjectId = _projectId
					}
				}
			}
		}, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
		
		return response.Result;
	}
	
	/// <inheritdoc />
	public void AddUserGrant(string userId, string orgId, IEnumerable<string> roles)
	{
		var listProjectGrantsResponse = Client.ListProjectGrants(new()
		{
			ProjectId = _projectId,
		});
		
		var orgGrant = listProjectGrantsResponse.Result.FirstOrDefault(grant => grant.GrantedOrgId == orgId);
		if (orgGrant == null)
			throw new RpcException(Status.DefaultCancelled, $"Organisation {orgId} is not authorized to add grants for the WebApp's project.");
		
		var request = new AddUserGrantRequest()
		{
			UserId = userId,
			ProjectId = _projectId,
			ProjectGrantId = orgGrant.GrantId
		};
		request.RoleKeys.AddRange(roles);

		Client.AddUserGrant(request, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
	}
	
	/// <inheritdoc />
	public void UpdateUserGrant(string userId, string orgId, IEnumerable<string> roles)
	{
		var listUserGrantsResponse = Client.ListUserGrants(new()
		{
			Queries =
			{
				new UserGrantQuery()
				{
					ProjectIdQuery = new UserGrantProjectIDQuery()
					{
						ProjectId = _projectId
					},
					UserIdQuery = new UserGrantUserIDQuery()
					{
						UserId = userId
					}
				}
			}
		}, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
		
		if (listUserGrantsResponse.Result.Count < 1)
			throw new RpcException(Status.DefaultCancelled, $"User {userId} does not have any grants that could be updated for organization {orgId}.");
		
		var request = new UpdateUserGrantRequest()
		{
			UserId = userId,
			GrantId = listUserGrantsResponse.Result[0].Id
		};
		request.RoleKeys.AddRange(roles);

		Client.UpdateUserGrant(request, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
	}
	
	/// <inheritdoc />
	public void RemoveUserGrant(string userId, string orgId)
	{
		var listUserGrantsResponse = Client.ListUserGrants(new()
		{
			Queries =
			{
				new UserGrantQuery()
				{
					ProjectIdQuery = new UserGrantProjectIDQuery()
					{
						ProjectId = _projectId
					},
					UserIdQuery = new UserGrantUserIDQuery()
					{
						UserId = userId
					}
				}
			}
		}, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
		
		if (listUserGrantsResponse.Result.Count < 1)
			throw new RpcException(Status.DefaultCancelled, $"User {userId} does not have any grants that could be updated for organization {orgId}.");
		
		var request = new RemoveUserGrantRequest()
		{
			UserId = userId,
			GrantId = listUserGrantsResponse.Result[0].Id
		};
		
		Client.RemoveUserGrant(request, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
	}
	
	/// <inheritdoc />
	public bool IsImpersonator(string userId, string orgId)
	{
		var myMember = GetMyOrgMember(userId, orgId);
		if (myMember == null)
			return false;

		return myMember.Roles.Contains(OrgRole.AdminImpersonator);
	}

	/// <inheritdoc />
	public void AddImpersonationRights(string userId, string orgId)
	{
		var myMember = GetMyOrgMember(userId, orgId);
		if (myMember == null)
		{
			AddUserToOrganisation(userId, orgId, [OrgRole.AdminImpersonator]);
			return;
		}
		
		var myRoles = myMember.Roles;
		if(myRoles.Contains(OrgRole.AdminImpersonator))
			return;
		
		myRoles.Add(OrgRole.AdminImpersonator);

		var updateRequest = new UpdateOrgMemberRequest()
		{
			UserId = userId
		};
		updateRequest.Roles.AddRange(myRoles);
		Client.UpdateOrgMember(updateRequest, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
	}

	/// <inheritdoc />
	public void RemoveImpersonationRights(string userId, string orgId)
	{
		var myMember = GetMyOrgMember(userId, orgId);
		if (myMember == null)
			return;
		
		var myRoles = myMember.Roles;
		if(!myRoles.Contains(OrgRole.AdminImpersonator))
			return;
		
		myRoles.Remove(OrgRole.AdminImpersonator);

		// No roles left? -> Remove from org!
		if (myRoles.Count == 0)
		{
			RemoveUserFromOrganisation(userId, orgId);
			return;
		}

		var updateRequest = new UpdateOrgMemberRequest()
		{
			UserId = userId
		};
		updateRequest.Roles.AddRange(myRoles);
		Client.UpdateOrgMember(updateRequest, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});
	}

	/// <summary>
	///	Retrieves the <see cref="Member"/> instance of the given user, if said user is member of the given org. Returns null if not.
	/// </summary>
	private Member? GetMyOrgMember(string userId, string orgId)
	{
		var myMemberResponse = Client.ListOrgMembers(new()
		{
			Queries =
			{
				new Zitadel.Member.V1.SearchQuery()
				{
					UserIdQuery = new UserIDQuery()
					{
						UserId = userId
					}
				}
			}
		}, new Metadata()
		{
			{ "x-zitadel-orgid", orgId }
		});

		return myMemberResponse.Result.FirstOrDefault();
	}
	
	#endregion

	#endregion
}