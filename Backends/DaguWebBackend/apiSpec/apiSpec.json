{"openapi": "3.0.1", "info": {"title": "iTWOsite REST Service", "description": "Public API Documentation", "contact": {"name": "RIB Leipzig GmbH", "email": "<EMAIL>"}, "version": "3.5.5 A"}, "servers": [{"url": "http://localhost:8282/DaguWeb/rest"}], "paths": {"/logo/{id}": {"get": {"tags": ["App Configuration"], "summary": "get a logo by its id", "operationId": "getLogo_1", "parameters": [{"name": "id", "in": "path", "description": "logo id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"image/*": {"schema": {"type": "string", "format": "binary"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "NotFound"}, "500": {"description": "InternalServerError"}}, "security": [{"bearerAuth": []}]}}, "/appConfig": {"get": {"tags": ["App Configuration"], "summary": "get the current app configuration (company theming, company URLs, global params, global filters)", "operationId": "appConfig_GET", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppConfig"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}}, "security": [{"bearerAuth": []}]}, "patch": {"tags": ["App Configuration"], "summary": "update the current app configuration (accept agb's with given hash)", "operationId": "appConfig_PATCH_1", "requestBody": {"description": "AppConfig Object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppConfig"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AppConfig"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}}, "security": [{"bearerAuth": []}]}}, "/authEndpoint": {"get": {"tags": ["Authentication"], "summary": "get the endpoint for the given oauth provider", "operationId": "authEndpoint.GET_1", "parameters": [{"name": "provider", "in": "query", "description": "which provider should be used?", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}}}, "500": {"description": "InternalServerError"}, "918": {"description": "UnsupportedProvider"}}}}, "/definitions/{definitionId}/backendWorkflowActions": {"post": {"tags": ["Data Access"], "summary": "execute one or multiple backend workflow actions", "operationId": "backendWorkflowAction_CREATE_1", "parameters": [{"name": "definitionId", "in": "path", "description": "definition id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "backend workflow action data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BackendWorkflowActions"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ElementActionResponse"}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "NotFound"}, "423": {"description": "TemporarilyBlocked"}, "500": {"description": "InternalServerError"}, "906": {"description": "MissingProperty"}}, "security": [{"bearerAuth": []}]}}, "/configVersions": {"get": {"tags": ["App Configuration"], "summary": "get all allowed config items and their current hashes (to decide which ones need to be (re)synced)", "operationId": "configVersions_LIST", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConfigVersions"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}}, "security": [{"bearerAuth": []}]}}, "/definitions/{definitionId}": {"get": {"tags": ["App Configuration"], "summary": "get a single definition by id", "operationId": "definitions_GET_1", "parameters": [{"name": "only<PERSON><PERSON><PERSON><PERSON>s", "in": "query", "description": "deliver only fields used inside the current app configuration?", "schema": {"type": "boolean"}}, {"name": "definitionId", "in": "path", "description": "definition id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Definition"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "NotFound"}, "500": {"description": "InternalServerError"}}, "security": [{"bearerAuth": []}]}}, "/definitions": {"get": {"tags": ["App Configuration"], "summary": "get all allowed definitions", "operationId": "definitions_LIST", "parameters": [{"name": "only<PERSON><PERSON><PERSON><PERSON>s", "in": "query", "description": "deliver only fields used inside the current app configuration?", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Definition"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}}, "security": [{"bearerAuth": []}]}}, "/definitions/{definitionId}/elementActions": {"post": {"tags": ["Data Access"], "summary": "create (and therefore execute) a new element action on one or multiple elements", "operationId": "elementAction_CREATE_1", "parameters": [{"name": "definitionId", "in": "path", "description": "definition id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "element action data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElementAction"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ElementActionResponse"}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "NotFound"}, "423": {"description": "TemporarilyBlocked"}, "500": {"description": "InternalServerError"}, "906": {"description": "MissingProperty"}}, "security": [{"bearerAuth": []}]}}, "/definitions/{definitionId}/elementLocks/{elementId}": {"get": {"tags": ["Data Access"], "summary": "get the current lock state of the requested element", "operationId": "locks_GET_1", "parameters": [{"name": "definitionId", "in": "path", "description": "definition id", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "elementId", "in": "path", "description": "element id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElementLock"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "NotFound"}, "500": {"description": "InternalServerError"}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Data Access"], "summary": "try to lock a specific element (only working if the element isn't locked already by another user)", "operationId": "locks_CREATE_1", "parameters": [{"name": "definitionId", "in": "path", "description": "definition id", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "elementId", "in": "path", "description": "element id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElementLock"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "NotFound"}, "423": {"description": "TemporarilyBlocked"}, "500": {"description": "InternalServerError"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Data Access"], "summary": "remove the lock for a specific element (only possible if the current lock was created by the same user or the user has admin privileges)", "operationId": "locks_DELETE_1", "parameters": [{"name": "definitionId", "in": "path", "description": "definition id", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "elementId", "in": "path", "description": "element id", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "NotFound"}, "500": {"description": "InternalServerError"}}, "security": [{"bearerAuth": []}]}}, "/definitions/{definitionId}/elements/$count": {"get": {"tags": ["Data Access"], "summary": "count elements with the help of OData filters", "operationId": "elements_COUNT_1", "parameters": [{"name": "$filter", "in": "query", "description": "OData filter string", "schema": {"type": "string"}}, {"name": "definitionId", "in": "path", "description": "definition id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "integer"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "NotFound"}, "500": {"description": "InternalServerError"}, "908": {"description": "Invalid<PERSON><PERSON>er"}, "909": {"description": "BackendConnectionFailed"}, "910": {"description": "NoReadFilterSet"}}, "security": [{"bearerAuth": []}]}}, "/definitions/{definitionId}/elements": {"get": {"tags": ["Data Access"], "summary": "query elements with the help of OData filters", "operationId": "elements_LIST_1", "parameters": [{"name": "$select", "in": "query", "description": "fieldnames to select, separated by comma<br/>(if not set, all available fields will be delivered)", "schema": {"type": "string"}}, {"name": "$filter", "in": "query", "description": "OData filter string", "schema": {"type": "string"}}, {"name": "$top", "in": "query", "description": "limit", "schema": {"type": "string"}}, {"name": "$skip", "in": "query", "description": "offset", "schema": {"type": "string"}}, {"name": "$apply", "in": "query", "description": "apply groupings and aggregations", "schema": {"type": "string"}}, {"name": "$orderby", "in": "query", "description": "fieldnames to order by, separated by comma", "schema": {"type": "string"}}, {"name": "$count", "in": "query", "description": "should the answer include a count over all matching elements?", "schema": {"type": "boolean"}}, {"name": "loadFileInfo", "in": "query", "description": "are file information needed for each element?", "schema": {"type": "boolean"}}, {"name": "only<PERSON><PERSON><PERSON><PERSON>s", "in": "query", "description": "deliver only fields used inside the current app configuration?", "schema": {"type": "boolean"}}, {"name": "loadBackendWorkflowInfo", "in": "query", "description": "are backend workflow information needed?", "schema": {"type": "boolean"}}, {"name": "definitionId", "in": "path", "description": "definition id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElementsResponse"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "NotFound"}, "500": {"description": "InternalServerError"}, "905": {"description": "UnsupportedValue"}, "908": {"description": "Invalid<PERSON><PERSON>er"}, "909": {"description": "BackendConnectionFailed"}, "910": {"description": "NoReadFilterSet"}, "911": {"description": "InvalidFieldName"}, "912": {"description": "InvalidAggregation"}, "914": {"description": "OperationFailed"}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Data Access"], "summary": "create a single element", "operationId": "elements_CREATE_1", "parameters": [{"name": "loadFileInfo", "in": "query", "description": "are file information needed as part of the result?", "schema": {"type": "boolean"}}, {"name": "definitionId", "in": "path", "description": "definition id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "element object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElementData"}}}, "required": true}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElementData"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "NotFound"}, "500": {"description": "InternalServerError"}, "906": {"description": "MissingProperty"}, "909": {"description": "BackendConnectionFailed"}, "913": {"description": "OperationBlocked"}, "914": {"description": "OperationFailed"}}, "security": [{"bearerAuth": []}]}}, "/definitions/{definitionId}/elements/{elementId}": {"get": {"tags": ["Data Access"], "summary": "get a single element by its id", "operationId": "elements_GET_1", "parameters": [{"name": "loadFileInfo", "in": "query", "description": "are file information needed?", "schema": {"type": "boolean"}}, {"name": "only<PERSON><PERSON><PERSON><PERSON>s", "in": "query", "description": "deliver only fields used inside the current app configuration?", "schema": {"type": "boolean"}}, {"name": "loadBackendWorkflowInfo", "in": "query", "description": "are backend workflow information needed?", "schema": {"type": "boolean"}}, {"name": "definitionId", "in": "path", "description": "definition id", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "elementId", "in": "path", "description": "element id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElementData"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "NotFound"}, "500": {"description": "InternalServerError"}, "909": {"description": "BackendConnectionFailed"}, "910": {"description": "NoReadFilterSet"}}, "security": [{"bearerAuth": []}]}, "patch": {"tags": ["Data Access"], "summary": "update a single element", "operationId": "elements_UPDATE_1", "parameters": [{"name": "loadFileInfo", "in": "query", "description": "are file information needed as part of the result?", "schema": {"type": "boolean"}}, {"name": "definitionId", "in": "path", "description": "definition id", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "elementId", "in": "path", "description": "element id", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "element object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElementData"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElementData"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "NotFound"}, "500": {"description": "InternalServerError"}, "906": {"description": "MissingProperty"}, "909": {"description": "BackendConnectionFailed"}, "913": {"description": "OperationBlocked"}, "914": {"description": "OperationFailed"}}, "security": [{"bearerAuth": []}]}}, "/definitions/{definitionId}/elements/{elementId}/revisions/{revisionId}": {"get": {"tags": ["Data Access"], "summary": "get a specific revision of a document", "operationId": "elements_GET_RevisionsOne_1", "parameters": [{"name": "loadFileInfo", "in": "query", "description": "are file information needed?", "schema": {"type": "boolean"}}, {"name": "definitionId", "in": "path", "description": "definition id", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "elementId", "in": "path", "description": "element id", "required": true, "schema": {"type": "string"}}, {"name": "revisionId", "in": "path", "description": "revision id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"*/*": {"schema": {"type": "integer", "format": "int64"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElementData"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "NotFound"}, "500": {"description": "InternalServerError"}, "909": {"description": "BackendConnectionFailed"}, "910": {"description": "NoReadFilterSet"}}, "security": [{"bearerAuth": []}]}}, "/definitions/{definitionId}/elements/{elementId}/revisions": {"get": {"tags": ["Data Access"], "summary": "get the available revisions of a document", "operationId": "elements_GET_RevisionsAll_1", "parameters": [{"name": "definitionId", "in": "path", "description": "definition id", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "elementId", "in": "path", "description": "element id", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"*/*": {"schema": {"type": "integer", "format": "int64"}}}}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ElementRevision"}}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "NotFound"}, "500": {"description": "InternalServerError"}, "909": {"description": "BackendConnectionFailed"}, "910": {"description": "NoReadFilterSet"}}, "security": [{"bearerAuth": []}]}}, "/definitions/{definitionId}/files/{fileId}/asHTML": {"get": {"tags": ["Data Access"], "summary": "get the rendered File as HTML (useful for excel sheets)", "operationId": "files_GETASHTML_1", "parameters": [{"name": "definitionId", "in": "path", "description": "definition id", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "fileId", "in": "path", "description": "file id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/html": {"schema": {"type": "string", "format": "binary"}}}}, "401": {"description": "Unauthorized"}, "404": {"description": "NotFound"}, "500": {"description": "InternalServerError"}, "909": {"description": "BackendConnectionFailed"}, "916": {"description": "RenderError"}}, "security": [{"bearerAuth": []}]}}, "/definitions/{definitionId}/files/{fileId}/asPDF": {"get": {"tags": ["Data Access"], "summary": "get the rendered File as PDF (useful for office files)", "operationId": "files_GETASPDF_1", "parameters": [{"name": "definitionId", "in": "path", "description": "definition id", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "fileId", "in": "path", "description": "file id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/pdf": {"schema": {"type": "string", "format": "binary"}}}}, "401": {"description": "Unauthorized"}, "404": {"description": "NotFound"}, "500": {"description": "InternalServerError"}, "909": {"description": "BackendConnectionFailed"}, "916": {"description": "RenderError"}}, "security": [{"bearerAuth": []}]}}, "/definitions/{definitionId}/files": {"post": {"tags": ["Data Access"], "summary": "upload a file to get a temporary id used to create/update elements with file", "operationId": "files_CREATE_1", "parameters": [{"name": "definitionId", "in": "path", "description": "definition id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"description": "multipart file data", "content": {"multipart/form-data": {"schema": {"type": "string", "format": "binary"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string", "example": "fileId"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}, "905": {"description": "UnsupportedValue"}, "906": {"description": "MissingProperty"}}, "security": [{"bearerAuth": []}]}}, "/definitions/{definitionId}/files/{fileId}": {"get": {"tags": ["Data Access"], "summary": "get a file by its id", "operationId": "files_GET_1", "parameters": [{"name": "definitionId", "in": "path", "description": "definition id", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "fileId", "in": "path", "description": "file id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/*": {"schema": {"type": "string", "format": "binary"}}}}, "206": {"description": "Partial Content (if Range-Header is used)"}, "401": {"description": "Unauthorized"}, "404": {"description": "NotFound"}, "500": {"description": "InternalServerError"}, "909": {"description": "BackendConnectionFailed"}}, "security": [{"bearerAuth": []}]}}, "/definitions/{definitionId}/files/{fileId}/mailContent": {"get": {"tags": ["Data Access"], "summary": "get the MailContent of a file (only working if the file is of type eml/msg/mht)", "operationId": "files_GETMAIL_1", "parameters": [{"name": "definitionId", "in": "path", "description": "definition id", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "fileId", "in": "path", "description": "file id", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/*": {"schema": {"$ref": "#/components/schemas/MailContent"}}}}, "401": {"description": "Unauthorized"}, "404": {"description": "NotFound"}, "500": {"description": "InternalServerError"}, "909": {"description": "BackendConnectionFailed"}}, "security": [{"bearerAuth": []}]}}, "/logon": {"post": {"tags": ["Authentication"], "summary": "authenticate by username and password to get a WebToken", "operationId": "logon_1", "requestBody": {"description": "login credentials", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Credentials"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebToken"}}}}, "403": {"description": "Forbidden"}, "423": {"description": "TemporarilyBlocked"}, "500": {"description": "InternalServerError"}, "900": {"description": "DeviceNotFound"}, "901": {"description": "IpNotAllowed"}, "902": {"description": "UnknownIdentity"}, "903": {"description": "IdentityNotAllowed"}, "904": {"description": "CustomerNotAllowed"}, "907": {"description": "BadCredentials"}}}}, "/logonMethods": {"get": {"tags": ["Authentication"], "summary": "get all available authentication methods", "operationId": "logonMethods_LIST", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LogonMethod"}}}}}, "500": {"description": "InternalServerError"}}}}, "/masks/{maskId}": {"get": {"tags": ["App Configuration"], "summary": "get a single mask by id", "operationId": "masks_GET_1", "parameters": [{"name": "maskId", "in": "path", "description": "mask id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Mask"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "NotFound"}, "500": {"description": "InternalServerError"}}, "security": [{"bearerAuth": []}]}}, "/masks": {"get": {"tags": ["App Configuration"], "summary": "get all allowed masks", "operationId": "masks_LIST", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Mask"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}}, "security": [{"bearerAuth": []}]}}, "/RemoteAuthCallback": {"get": {"tags": ["Authentication"], "summary": "generate a jwt and trigger a redirect to a callback url for remote sso", "operationId": "remoteAuthCallback_GET_1", "parameters": [{"name": "sso<PERSON>allback", "in": "query", "description": "Callback url", "required": true, "schema": {"type": "string"}}, {"name": "ribid", "in": "query", "description": "Unique id", "required": true, "schema": {"type": "string"}}, {"name": "platform", "in": "query", "description": "Platform", "required": true, "schema": {"type": "string"}}, {"name": "ssokey", "in": "query", "description": "SSO key", "required": true, "schema": {"type": "string"}}], "responses": {"302": {"description": "Redirect to callback url"}, "400": {"description": "Bad Request"}, "906": {"description": "Missing Property"}, "917": {"description": "Missing Configuration Property"}}}, "post": {"tags": ["Authentication"], "summary": "get the username for remote sso", "operationId": "remoteAuthCallback_POST", "requestBody": {"description": "payload previously sent to remote API", "content": {"text/plain": {"schema": {"type": "string"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoteAuth"}}}}, "400": {"description": "Bad Request"}}}}, "/syncDevice": {"post": {"tags": ["Offline Sync"], "summary": "register a new device for syncing offline data", "operationId": "syncDevice_CREATE_1", "requestBody": {"description": "SyncDevice object", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SyncDevice"}}}, "required": true}, "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncDevice"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}, "905": {"description": "UnsupportedValue"}, "906": {"description": "MissingProperty"}}, "security": [{"bearerAuth": []}]}}, "/syncDevice/{uuid}": {"get": {"tags": ["Offline Sync"], "summary": "get an existing device owned by the authorized user", "operationId": "syncDevice_GET_1", "parameters": [{"name": "uuid", "in": "path", "description": "device uuid", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncDevice"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "500": {"description": "InternalServerError"}, "900": {"description": "DeviceNotFound"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Offline Sync"], "summary": "delete an existing device owned by the authorized user", "operationId": "syncDevice_DELETE_1", "parameters": [{"name": "uuid", "in": "path", "description": "device uuid", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "900": {"description": "DeviceNotFound"}}, "security": [{"bearerAuth": []}]}, "patch": {"tags": ["Offline Sync"], "summary": "patch an existing device owned by the authorized user", "operationId": "syncDevice_UPDATE_1", "parameters": [{"name": "uuid", "in": "path", "description": "device uuid", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "SyncDevice object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncDevice"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncDevice"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "500": {"description": "InternalServerError"}, "900": {"description": "DeviceNotFound"}, "905": {"description": "UnsupportedValue"}}, "security": [{"bearerAuth": []}]}}, "/syncItems/{definitionId}": {"get": {"tags": ["Offline Sync"], "summary": "get information about the available offline items for the given definition", "operationId": "XsyncItems_GET_1", "parameters": [{"name": "definitionId", "in": "path", "description": "definition id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncItems"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "NotFound"}, "500": {"description": "InternalServerError"}, "909": {"description": "BackendConnectionFailed"}, "910": {"description": "NoReadFilterSet"}}, "security": [{"bearerAuth": []}]}}, "/syncLog": {"post": {"tags": ["Offline Sync"], "summary": "create a new synclog entry", "operationId": "syncLog_CREATE_1", "requestBody": {"description": "SyncLog object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncLog"}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}, "900": {"description": "DeviceNotFound"}}, "security": [{"bearerAuth": []}]}}, "/syncPreview/{definitionId}": {"get": {"tags": ["Offline Sync"], "summary": "get the SyncPreview for a specific definition", "operationId": "syncPreview_GET_1", "parameters": [{"name": "definitionId", "in": "path", "description": "definition id", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SyncPreview"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "NotFound"}, "500": {"description": "InternalServerError"}, "909": {"description": "BackendConnectionFailed"}, "910": {"description": "NoReadFilterSet"}}, "security": [{"bearerAuth": []}]}}, "/syncPreview": {"get": {"tags": ["Offline Sync"], "summary": "get the SyncPreview for all definitions which allow offline synchronization", "operationId": "syncPreview_LIST", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SyncPreview"}}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}, "909": {"description": "BackendConnectionFailed"}, "910": {"description": "NoReadFilterSet"}}, "security": [{"bearerAuth": []}]}}, "/userConfig": {"get": {"tags": ["App Configuration"], "summary": "get the current user configuration (userinfo, substitutions, groups, user params)", "operationId": "userConfig_GET", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserConfig"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}}, "security": [{"bearerAuth": []}]}}, "/webToken": {"get": {"tags": ["Authentication"], "summary": "get the current WebToken", "operationId": "webToken_GET", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebToken"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}}, "security": [{"bearerAuth": []}]}, "patch": {"tags": ["Authentication"], "summary": "patch the current WebToken", "operationId": "webToken_PATCH_1", "requestBody": {"description": "WebToken Object", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebToken"}}}, "required": true}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebToken"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "InternalServerError"}}, "security": [{"bearerAuth": []}]}}}, "components": {"schemas": {"AppConfig": {"type": "object", "properties": {"logoId": {"type": "integer", "format": "int64", "readOnly": true}, "logoVersion": {"type": "integer", "format": "int32", "readOnly": true}, "companyName": {"type": "string", "readOnly": true}, "showCompanyName": {"type": "boolean", "readOnly": true}, "baseColor": {"type": "string", "readOnly": true}, "corporateColor": {"type": "string", "readOnly": true}, "supportMail": {"type": "string", "readOnly": true}, "imprintUrl": {"type": "string", "readOnly": true}, "agbUrl": {"type": "string", "readOnly": true}, "agbHash": {"type": "string"}, "showAGB": {"type": "boolean", "readOnly": true}, "agbAccepted": {"type": "boolean"}, "showQRScanner": {"type": "boolean", "readOnly": true}, "showWebLink": {"type": "boolean", "readOnly": true}, "params": {"type": "object", "additionalProperties": {"type": "string"}, "readOnly": true}, "globalFilters": {"type": "array", "readOnly": true, "items": {"$ref": "#/components/schemas/GlobalFilter"}}, "dashboards": {"type": "array", "readOnly": true, "items": {"$ref": "#/components/schemas/Dashboard"}}}}, "BackendWorkflowAction": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "targetValue": {"type": "string"}, "comment": {"type": "string"}, "targetUser": {"type": "string"}, "holdDate": {"type": "string", "format": "date-time"}}}, "BackendWorkflowActions": {"type": "object", "properties": {"actions": {"type": "array", "items": {"$ref": "#/components/schemas/BackendWorkflowAction"}}}}, "BackendWorkflowStatus": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}, "ConfigVersions": {"type": "object", "properties": {"definitions": {"type": "object", "additionalProperties": {"type": "string"}}, "masks": {"type": "object", "additionalProperties": {"type": "string"}}}}, "Credentials": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}, "customerId": {"type": "integer", "format": "int64"}, "itwoCompanyId": {"type": "integer", "format": "int64"}, "itwoRoleId": {"type": "integer", "format": "int64"}, "deviceId": {"type": "string"}}, "description": "Login credentials"}, "CustomerInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "refId": {"type": "string"}, "name": {"type": "string"}, "fullName": {"type": "string"}}, "description": "Customer info"}, "Dashboard": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "iconCss": {"type": "string"}, "position": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/DashboardItem"}}}}, "DashboardItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "type": {"type": "string", "enum": ["PLACEHOLDER", "QUERYMASK"]}, "maskId": {"type": "integer", "format": "int64"}, "filterId": {"type": "integer", "format": "int64"}, "position": {"type": "integer", "format": "int32"}, "label": {"type": "string"}, "icon": {"type": "string"}, "iconColor": {"type": "string"}, "textColor": {"type": "string"}, "backgroundColor": {"type": "string"}, "imageName": {"type": "string"}, "imageData": {"type": "array", "items": {"type": "string", "format": "byte"}}, "showCount": {"type": "boolean"}, "showFullscreenCount": {"type": "boolean"}, "aggregateFunction": {"type": "string", "enum": ["NONE", "DEFAULT", "SUM", "COUNT", "AVG", "MIN", "MAX"]}, "sumFieldId": {"type": "integer", "format": "int64"}, "sumFieldUnit": {"type": "string"}, "sumFieldDivisor": {"type": "integer", "format": "int32"}, "sumFieldDecimals": {"type": "integer", "format": "int32"}, "showOnTop": {"type": "boolean"}, "showOnBottomBar": {"type": "boolean"}, "showOnMenuList": {"type": "boolean"}, "allowCreate": {"type": "boolean"}, "createLabel": {"type": "string"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/DashboardItem"}}}}, "Definition": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "hash": {"type": "string"}, "defaultMaskId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "identityFieldId": {"type": "integer", "format": "int64"}, "barcodeFieldId": {"type": "integer", "format": "int64"}, "nfcFieldId": {"type": "integer", "format": "int64"}, "defaultWorkflowId": {"type": "integer", "format": "int64"}, "workflowKeyFieldId": {"type": "integer", "format": "int64"}, "workflowStateFieldId": {"type": "integer", "format": "int64"}, "commentFieldId": {"type": "integer", "format": "int64"}, "reminderFieldId": {"type": "integer", "format": "int64"}, "inactiveFieldId": {"type": "integer", "format": "int64"}, "escalateFieldId": {"type": "integer", "format": "int64"}, "finishedFieldId": {"type": "integer", "format": "int64"}, "latitudeFieldId": {"type": "integer", "format": "int64"}, "longitudeFieldId": {"type": "integer", "format": "int64"}, "allowSync": {"type": "boolean"}, "responsible": {"type": "string"}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/Field"}}, "workflows": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowDefinition"}}, "workflowActions": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowAction"}}, "markerSources": {"type": "array", "items": {"$ref": "#/components/schemas/MarkerSource"}}, "supportsBackendWorkflows": {"type": "boolean"}}}, "ElementAction": {"type": "object", "properties": {"workflowActionId": {"type": "integer", "format": "int64"}, "comment": {"type": "string"}, "reminderDate": {"type": "string", "format": "date-time"}, "delegateUser": {"type": "string"}, "agbAccepted": {"type": "boolean"}, "elements": {"type": "array", "items": {"type": "string"}}}}, "ElementActionResponse": {"type": "object", "properties": {"elementId": {"type": "string", "readOnly": true}, "success": {"type": "boolean", "readOnly": true}, "error": {"$ref": "#/components/schemas/WebError"}}}, "ElementData": {"type": "object", "properties": {"id": {"type": "string", "readOnly": true}, "revisionId": {"type": "string", "readOnly": true}, "readonly": {"type": "boolean", "readOnly": true}, "hasFiles": {"type": "boolean", "readOnly": true}, "files": {"type": "array", "items": {"$ref": "#/components/schemas/FileInfo"}}, "currentBackendWorkflowStatus": {"$ref": "#/components/schemas/BackendWorkflowStatus"}, "backendWorkflowActions": {"type": "array", "items": {"$ref": "#/components/schemas/BackendWorkflowAction"}}, "values": {"type": "object", "additionalProperties": {"type": "object"}}}}, "ElementLock": {"type": "object", "properties": {"type": {"type": "string", "readOnly": true, "enum": ["NONE", "HARDLOCK", "SOFTLOCK"]}, "lockedBy": {"$ref": "#/components/schemas/UserInfo"}, "expires": {"type": "string", "format": "date-time", "readOnly": true}}}, "ElementRevision": {"type": "object", "properties": {"id": {"type": "string", "readOnly": true}, "revisionNumber": {"type": "integer", "format": "int32", "readOnly": true}, "date": {"type": "string", "format": "date-time", "readOnly": true}, "username": {"type": "string", "readOnly": true}, "comment": {"type": "string", "readOnly": true}}}, "ElementsResponse": {"type": "object", "properties": {"value": {"type": "array", "items": {"$ref": "#/components/schemas/ElementData"}}, "@odata.count": {"type": "integer", "format": "int32"}}, "description": "List of elements"}, "EmbeddedMask": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "name": {"type": "string"}, "pageTitel": {"type": "string"}, "createButtonLabel": {"type": "string"}, "type": {"type": "string", "enum": ["ECMMASK", "ECMHISTORY", "USERMASK", "IFRAME", "CHART", "GANTT", "MAP", "MILESTONE", "DATAPINE"]}, "mainMaskId": {"type": "integer", "format": "int64"}, "position": {"type": "integer", "format": "int32"}, "maxRows": {"type": "integer", "format": "int32"}, "iframeUrl": {"type": "string"}, "iconCss": {"type": "string"}, "iconColor": {"type": "string"}, "mainMaskColumn": {"type": "boolean"}, "startUnfolded": {"type": "boolean"}, "constraints": {"type": "array", "items": {"$ref": "#/components/schemas/EmbeddedMaskConstraint"}}, "createValues": {"type": "array", "items": {"$ref": "#/components/schemas/EmbeddedMaskValue"}}}}, "EmbeddedMaskConstraint": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "embeddedFieldId": {"type": "integer", "format": "int64"}, "compareType": {"type": "string", "enum": ["EQUAL", "NOTEQUAL", "LESSTHAN", "LESSTHANEQUALS", "GREATERTHAN", "GREATERTHANEQUALS", "LIKE", "NOTLIKE", "ILIKE", "NOTILIKE", "ISNULL", "ISNOTNULL", "BETWEEN", "UNIQUE", "NOTUNIQUE", "CHANGEDLOCAL", "CHANGEDGLOBAL", "IN", "NOTIN", "CONTAINS", "EQUALSIGNORECASE", "STARTSWITH", "ENDSWITH", "CHANGEDBYUSER", "NOTSTARTSWITH", "NOTENDSWITH", "EXISTS", "NOTEXISTS", "SOUNDEX", "NOTBETWEEN"]}, "parentFieldId": {"type": "integer", "format": "int64"}, "compareValue": {"type": "string"}, "filterGroup": {"type": "integer", "format": "int32"}, "liveUpdate": {"type": "boolean"}, "filterEmpty": {"type": "boolean"}}}, "EmbeddedMaskValue": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "targetFieldId": {"type": "integer", "format": "int64"}, "targetValue": {"type": "string"}, "calculateValue": {"type": "boolean"}}}, "Field": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "lookupFieldId": {"type": "integer", "format": "int64"}, "displayFieldId": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "type": {"type": "string", "enum": ["STRING", "INTEGER", "DOUBLE", "CURRENCY", "DATE", "TIME", "DATETIME", "BOOLEAN", "TEXT", "FULLTEXT", "AUTOINCREMENT", "LONG", "UNKNOWN"]}, "length": {"type": "integer", "format": "int32"}, "decimalPlaces": {"type": "integer", "format": "int32"}, "currencySign": {"type": "string"}, "unit": {"type": "string"}, "systemField": {"type": "boolean"}, "primaryKey": {"type": "boolean"}, "hardLinked": {"type": "boolean"}, "mandatory": {"type": "boolean"}, "unique": {"type": "boolean"}, "multi": {"type": "boolean"}, "multiSeparator": {"type": "string"}, "readonly": {"type": "boolean"}, "minValue": {"type": "number", "format": "float"}, "maxValue": {"type": "number", "format": "float"}, "constraints": {"type": "array", "items": {"$ref": "#/components/schemas/FieldConstraint"}}}}, "FieldConstraint": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "lookupFieldId": {"type": "integer", "format": "int64"}, "chooseValue": {"type": "string", "enum": ["EQUAL", "NOTEQUAL", "LESSTHAN", "LESSTHANEQUALS", "GREATERTHAN", "GREATERTHANEQUALS", "LIKE", "NOTLIKE", "ILIKE", "NOTILIKE", "ISNULL", "ISNOTNULL", "BETWEEN", "UNIQUE", "NOTUNIQUE", "CHANGEDLOCAL", "CHANGEDGLOBAL", "IN", "NOTIN", "CONTAINS", "EQUALSIGNORECASE", "STARTSWITH", "ENDSWITH", "CHANGEDBYUSER", "NOTSTARTSWITH", "NOTENDSWITH", "EXISTS", "NOTEXISTS", "SOUNDEX", "NOTBETWEEN"]}, "compareFieldId": {"type": "integer", "format": "int64"}, "compareValue": {"type": "string"}, "filterGroup": {"type": "integer", "format": "int32"}, "filterEmpty": {"type": "boolean"}}}, "FileInfo": {"type": "object", "properties": {"id": {"type": "string"}, "tempId": {"type": "string"}, "name": {"type": "string", "readOnly": true}, "contentType": {"type": "string"}, "size": {"type": "integer", "format": "int64", "readOnly": true}, "converters": {"type": "array", "items": {"type": "string", "enum": ["mailContent", "asPDF", "asHTML"]}}}}, "GlobalFilter": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "icon": {"type": "string"}, "labelOne": {"type": "string"}, "keyFieldOneId": {"type": "integer", "format": "int64"}, "searchFieldOneId": {"type": "integer", "format": "int64"}, "allowMultipleOne": {"type": "boolean"}, "displayFieldsOne": {"type": "array", "items": {"$ref": "#/components/schemas/GlobalFilterField"}}, "filterTargetsOne": {"type": "array", "items": {"type": "string"}}, "filterConstraintsOne": {"type": "array", "items": {"$ref": "#/components/schemas/GlobalFilterConstraint"}}, "labelTwo": {"type": "string"}, "keyFieldTwoId": {"type": "integer", "format": "int64"}, "searchFieldTwoId": {"type": "integer", "format": "int64"}, "allowMultipleTwo": {"type": "boolean"}, "displayFieldsTwo": {"type": "array", "items": {"$ref": "#/components/schemas/GlobalFilterField"}}, "filterTargetsTwo": {"type": "array", "items": {"type": "string"}}, "filterConstraintsTwo": {"type": "array", "items": {"$ref": "#/components/schemas/GlobalFilterConstraint"}}, "filterTwoByOneKeyFieldId": {"type": "integer", "format": "int64"}, "filterTwoByOneForeignFieldId": {"type": "integer", "format": "int64"}}}, "GlobalFilterConstraint": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "targetFieldId": {"type": "integer", "format": "int64"}, "compareType": {"type": "string", "enum": ["EQUAL", "NOTEQUAL", "LESSTHAN", "LESSTHANEQUALS", "GREATERTHAN", "GREATERTHANEQUALS", "LIKE", "NOTLIKE", "ILIKE", "NOTILIKE", "ISNULL", "ISNOTNULL", "BETWEEN", "UNIQUE", "NOTUNIQUE", "CHANGEDLOCAL", "CHANGEDGLOBAL", "IN", "NOTIN", "CONTAINS", "EQUALSIGNORECASE", "STARTSWITH", "ENDSWITH", "CHANGEDBYUSER", "NOTSTARTSWITH", "NOTENDSWITH", "EXISTS", "NOTEXISTS", "SOUNDEX", "NOTBETWEEN"]}, "compareValue": {"type": "string"}, "filterGroup": {"type": "integer", "format": "int32"}}}, "GlobalFilterField": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "ecmFieldId": {"type": "integer", "format": "int64"}, "position": {"type": "integer", "format": "int32"}, "width": {"type": "integer", "format": "int32"}, "visible": {"type": "boolean"}, "searchable": {"type": "boolean"}, "textAlign": {"type": "string", "enum": ["LEFT", "RIGHT", "CENTER", "TOP", "BOTTOM", "JUSTIFY", "MIDDLE"]}}}, "LogonMethod": {"type": "object", "properties": {"name": {"type": "string", "readOnly": true}, "type": {"type": "string", "readOnly": true, "enum": ["CREDENTIALS", "OAUTH"]}, "provider": {"type": "string", "readOnly": true, "enum": ["MICROSOFT", "GOOGLE"]}}}, "MailAddress": {"type": "object", "properties": {"name": {"type": "string"}, "mail": {"type": "string"}}}, "MailAttachment": {"type": "object", "properties": {"name": {"type": "string"}, "contentType": {"type": "string"}, "data": {"type": "array", "items": {"type": "string", "format": "byte"}}}}, "MailContent": {"type": "object", "properties": {"subject": {"type": "string"}, "text": {"type": "string"}, "html": {"type": "string"}, "priority": {"type": "string"}, "sentDate": {"type": "string", "format": "date-time"}, "from": {"$ref": "#/components/schemas/MailAddress"}, "to": {"type": "array", "items": {"$ref": "#/components/schemas/MailAddress"}}, "cc": {"type": "array", "items": {"$ref": "#/components/schemas/MailAddress"}}, "bcc": {"type": "array", "items": {"$ref": "#/components/schemas/MailAddress"}}, "appointmentStart": {"type": "string", "format": "date-time"}, "appointmentEnd": {"type": "string", "format": "date-time"}, "appointmentLocation": {"type": "string"}, "attachments": {"type": "array", "items": {"$ref": "#/components/schemas/MailAttachment"}}}}, "MarkerSource": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "name": {"type": "string"}, "title": {"type": "string"}, "type": {"type": "string", "enum": ["PIN", "AREA", "LINE"]}, "viewerType": {"type": "string", "enum": ["FILEVIEWER", "MAPVIEWER", "TILEVIEWER", "IFRAME", "CHART", "ELEMENTSTATEVIEWER", "GANTTVIEWER", "MILESTONE", "BIMVIEWER", "DATAPINE"]}, "embeddedMaskId": {"type": "integer", "format": "int64"}, "keyFieldId": {"type": "integer", "format": "int64"}, "keyValue": {"type": "string"}, "widthFieldId": {"type": "integer", "format": "int64"}, "heightFieldId": {"type": "integer", "format": "int64"}, "textFieldId": {"type": "integer", "format": "int64"}, "iconSource": {"type": "string", "enum": ["DEFAULT", "ECMFIELD", "VALUE"]}, "iconCss": {"type": "string"}, "iconFieldId": {"type": "integer", "format": "int64"}, "iconColorSource": {"type": "string", "enum": ["DEFAULT", "ECMFIELD", "VALUE"]}, "iconColor": {"type": "string"}, "iconColorFieldId": {"type": "integer", "format": "int64"}, "iconPosition": {"type": "string", "enum": ["LEFT", "RIGHT", "CENTER", "TOP", "BOTTOM", "JUSTIFY", "MIDDLE"]}, "allowCreate": {"type": "boolean"}, "allowMove": {"type": "boolean"}, "position": {"type": "integer", "format": "int32"}, "infoTitle": {"type": "string"}, "infoText": {"type": "string"}, "inactiveFieldId": {"type": "integer", "format": "int64"}, "inactiveValue": {"type": "string"}, "borderStyle": {"type": "string", "enum": ["NONE", "THIN", "MEDIUM", "DASHED", "DOTTED", "THICK", "DOUBLE", "MEDIUM_DASHED", "SOLID"]}, "borderColor": {"type": "string"}, "borderWidth": {"type": "integer", "format": "int32"}, "borderRadius": {"type": "integer", "format": "int32"}, "defaultText": {"type": "string"}, "targetWidth": {"type": "integer", "format": "int32"}, "targetHeight": {"type": "integer", "format": "int32"}, "iconSize": {"type": "integer", "format": "int32"}, "fontSize": {"type": "integer", "format": "int32"}, "fontSizeFieldId": {"type": "integer", "format": "int64"}, "areaSizeType": {"type": "string", "enum": ["DEFAULT", "ECMFIELD", "VALUE"]}, "areaBackgroundColor": {"type": "string"}, "backgroundColorFieldId": {"type": "integer", "format": "int64"}, "pageFieldId": {"type": "integer", "format": "int64"}, "allowResize": {"type": "boolean"}, "showCreateMask": {"type": "boolean"}, "isHtml": {"type": "boolean"}, "keepRatio": {"type": "boolean"}, "xFieldId": {"type": "integer", "format": "int64"}, "yFieldId": {"type": "integer", "format": "int64"}, "zFieldId": {"type": "integer", "format": "int64"}}, "deprecated": true}, "Mask": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "hash": {"type": "string"}, "name": {"type": "string"}, "displayName": {"type": "string"}, "type": {"type": "string", "enum": ["INDEX", "QUERY", "SCHEDULE"]}, "saveAction": {"type": "string", "enum": ["STAY", "NEXT", "LEAVE", "NEW", "OPEN", "SAVEALL"]}, "saveButtonLabel": {"type": "string"}, "columnWidth": {"type": "integer", "format": "int32"}, "ecmIndexId": {"type": "integer", "format": "int64"}, "onlineView": {"type": "boolean"}, "expandedView": {"type": "boolean"}, "skipReadMode": {"type": "boolean"}, "allowQRScanner": {"type": "boolean"}, "debug": {"type": "boolean"}, "identityFieldId": {"type": "integer", "format": "int64"}, "showDocumentsPanel": {"type": "boolean"}, "viewerType": {"type": "string", "deprecated": true, "enum": ["FILEVIEWER", "MAPVIEWER", "TILEVIEWER", "IFRAME", "CHART", "ELEMENTSTATEVIEWER", "GANTTVIEWER", "MILESTONE", "BIMVIEWER", "DATAPINE"]}, "markerColorSource": {"type": "string", "deprecated": true, "enum": ["DEFAULT", "ECMFIELD", "VALUE"]}, "markerColorFieldId": {"type": "integer", "format": "int64", "deprecated": true}, "markerColor": {"type": "string", "deprecated": true}, "geoSource": {"type": "string", "deprecated": true, "enum": ["ADDRESS", "LATLONG"]}, "mapPositionFieldId": {"type": "integer", "format": "int64", "deprecated": true}, "mapLatitudeFieldId": {"type": "integer", "format": "int64", "deprecated": true}, "mapLongitudeFieldId": {"type": "integer", "format": "int64", "deprecated": true}, "mapElementDetails": {"type": "string"}, "mapElementDetailsMaxHeight": {"type": "integer", "format": "int32"}, "mapElementButtonColor": {"type": "string", "deprecated": true}, "mapElementButtonTextColor": {"type": "string", "deprecated": true}, "ignoreIndexMarkers": {"type": "boolean", "deprecated": true}, "showBackendWorkflowActions": {"type": "boolean"}, "showElementStateActions": {"type": "boolean"}, "rows": {"type": "integer", "format": "int32"}, "columns": {"type": "integer", "format": "int32"}, "fillFieldsFromParentMask": {"type": "boolean"}, "hideIndexTab": {"type": "boolean"}, "showViewerFirst": {"type": "boolean"}, "iframeViewerCaption": {"type": "string", "deprecated": true}, "iframeViewerSource": {"type": "string", "deprecated": true}, "queryDestinationMaskType": {"type": "string", "enum": ["NONE", "ECMMASK", "ECMINDEXCOLUMN", "ECMMASKCOLUMN", "LINK"]}, "queryDestinationMaskId": {"type": "integer", "format": "int64"}, "queryDestinationMaskFieldId": {"type": "integer", "format": "int64"}, "queryDestinationIndexFieldId": {"type": "integer", "format": "int64"}, "queryDestinationIdFieldId": {"type": "integer", "format": "int64"}, "queryDestinationLinkFieldId": {"type": "integer", "format": "int64"}, "createDestinationMaskId": {"type": "integer", "format": "int64"}, "showTargetElementActions": {"type": "boolean"}, "limitFileTypes": {"type": "boolean"}, "allowedFileTypes": {"type": "string"}, "orderByFieldId": {"type": "integer", "format": "int64", "deprecated": true}, "orderByType": {"type": "string", "deprecated": true, "enum": ["ASC", "DESC"]}, "createWithFile": {"type": "boolean"}, "createWithoutFile": {"type": "boolean"}, "createWithCamera": {"type": "boolean"}, "createMaskLabel": {"type": "string"}, "createMaskTitle": {"type": "string"}, "autoShowFilterDialog": {"type": "boolean"}, "applyGlobalFilters": {"type": "boolean"}, "businessCardView": {"type": "boolean"}, "autoOpenSingleEntry": {"type": "boolean"}, "queryMaskSearchType": {"type": "string", "enum": ["NONE", "VISIBLE", "ALL", "ECMFIELD"]}, "searchFieldId": {"type": "integer", "format": "int64"}, "searchFieldTwoId": {"type": "integer", "format": "int64"}, "reOpenDialog": {"type": "boolean"}, "allowBatchProcessing": {"type": "boolean"}, "allowBulkCreate": {"type": "boolean"}, "showThumbnails": {"type": "boolean"}, "defaultStructureId": {"type": "integer", "format": "int64"}, "multiFileMaskId": {"type": "integer", "format": "int64", "deprecated": true}, "showStatusBar": {"type": "boolean"}, "appIconFieldId": {"type": "integer", "format": "int64"}, "appIconCaptionFieldId": {"type": "integer", "format": "int64"}, "appTitleFieldId": {"type": "integer", "format": "int64"}, "appSubTitleFieldId": {"type": "integer", "format": "int64"}, "previewIcon": {"type": "string"}, "previewIconCaption": {"type": "string"}, "previewTitle": {"type": "string"}, "previewSubTitle": {"type": "string"}, "defaultViewerId": {"type": "integer", "format": "int64"}, "viewers": {"type": "array", "items": {"$ref": "#/components/schemas/Viewer"}}, "markerSources": {"type": "array", "deprecated": true, "items": {"$ref": "#/components/schemas/MarkerSource"}}, "rowColors": {"type": "object", "additionalProperties": {"type": "string"}}, "colColors": {"type": "object", "additionalProperties": {"type": "string"}}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/MaskField"}}, "labels": {"type": "array", "items": {"$ref": "#/components/schemas/MaskLabel"}}, "buttons": {"type": "array", "items": {"$ref": "#/components/schemas/MaskButton"}}, "separators": {"type": "array", "items": {"$ref": "#/components/schemas/MaskSeparator"}}, "signatures": {"type": "array", "items": {"$ref": "#/components/schemas/MaskSignature"}}, "rules": {"type": "array", "items": {"$ref": "#/components/schemas/MaskRule"}}, "actions": {"type": "array", "items": {"$ref": "#/components/schemas/MaskAction"}}, "embeddedMasks": {"type": "array", "items": {"$ref": "#/components/schemas/EmbeddedMask"}}, "maskColumns": {"type": "array", "items": {"$ref": "#/components/schemas/MaskColumn"}}, "filterFields": {"type": "array", "items": {"$ref": "#/components/schemas/MaskFilterField"}}, "structures": {"type": "array", "items": {"$ref": "#/components/schemas/MaskStructure"}}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/MaskFilter"}}, "sortings": {"type": "array", "items": {"$ref": "#/components/schemas/MaskSorting"}}, "icons": {"type": "array", "deprecated": true, "items": {"$ref": "#/components/schemas/MaskIcon"}}, "hideCreateButton": {"type": "boolean"}, "hideEmptyRows": {"type": "boolean"}, "scheduleDisplayField": {"type": "integer", "format": "int64"}, "defaultLeftView": {"type": "integer", "format": "int64"}, "scheduleType": {"type": "string", "enum": ["CALENDAR", "DDC"]}, "scheduleReferenceIsPeriod": {"type": "boolean"}, "scheduleReferenceTargetField": {"type": "integer", "format": "int64"}, "scheduleReferenceTargetFieldTo": {"type": "integer", "format": "int64"}, "scheduleWeekendHandling": {"type": "string", "enum": ["SHOW", "HIDE", "DISABLE"]}, "calendarWeek": {"type": "boolean"}, "compactView": {"type": "boolean"}, "scheduleMaskViews": {"type": "array", "items": {"$ref": "#/components/schemas/ScheduleMaskView"}}}}, "MaskAction": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "position": {"type": "integer", "format": "int32"}, "label": {"type": "string"}, "type": {"type": "string", "enum": ["RUNSCRIPT", "OPENMASK", "OPENLINK", "TOGGLEBOOLEAN", "OPENEDITMASK", "OPENSCHEDULEMASK", "CREATEREPORT", "STOREREPORT", "CLOUDCONNECTORPUSH", "LOADFILTER", "OPENCREATEMASK", "CREATETEMPLATE", "STORETEMPLATE", "CREATEVCARD", "CREATEUNIVERSALREPORT", "STOREUNIVERSALREPORT", "STOREVCARD", "OPENMAIL", "NONE"]}, "captionSource": {"type": "string", "enum": ["DEFAULT", "ECMFIELD", "VALUE"]}, "captionFieldId": {"type": "integer", "format": "int64"}, "iconSource": {"type": "string", "enum": ["DEFAULT", "ECMFIELD", "VALUE"]}, "iconFieldId": {"type": "integer", "format": "int64"}, "iconCss": {"type": "string"}, "colorSource": {"type": "string", "enum": ["DEFAULT", "ECMFIELD", "VALUE"]}, "colorFieldId": {"type": "integer", "format": "int64"}, "color": {"type": "string"}, "vcardMappings": {"type": "array", "items": {"$ref": "#/components/schemas/MaskActionMapping"}}}}, "MaskActionMapping": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "type": {"type": "string", "enum": ["NAME", "FIRSTNAME", "ADDITIONALNAMES", "NAMEPREFIX", "NAMESUFFIX", "BDAY", "GENDER", "POSTBOX_HOME", "ADREXTENSION_HOME", "STREET_HOME", "CITY_HOME", "REGION_HOME", "POSTAL_HOME", "COUNTRY_HOME", "POSTBOX_WORK", "ADREXTENSION_WORK", "STREET_WORK", "CITY_WORK", "REGION_WORK", "POSTAL_WORK", "COUNTRY_WORK", "TEL_HOME", "TEL_WORK", "TEL_CELL", "EMAIL_HOME", "EMAIL_WORK", "TITLE", "ROLE", "ORG", "URL_HOME", "URL_WORK", "LAT", "LONG", "FAX_HOME", "FAX_WORK"]}, "fieldId": {"type": "integer", "format": "int64"}}}, "MaskButton": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "separatorId": {"type": "integer", "format": "int64"}, "maskRow": {"type": "integer", "format": "int32"}, "maskColumn": {"type": "integer", "format": "int32"}, "width": {"type": "integer", "format": "int32"}, "widthUnity": {"type": "string", "enum": ["PERCENT", "PIXEL"]}, "textAlign": {"type": "string", "enum": ["LEFT", "RIGHT", "CENTER", "TOP", "BOTTOM", "JUSTIFY", "MIDDLE"]}, "fieldAlign": {"type": "string", "enum": ["LEFT", "RIGHT", "CENTER", "TOP", "BOTTOM", "JUSTIFY", "MIDDLE"]}, "label": {"type": "string"}, "hint": {"type": "string"}, "colspan": {"type": "integer", "format": "int32"}, "rowspan": {"type": "integer", "format": "int32"}, "iconCss": {"type": "string"}, "iconColor": {"type": "string"}, "cellColor": {"type": "string"}, "backgroundColor": {"type": "string"}, "showBorder": {"type": "boolean"}, "iconPosition": {"type": "string", "enum": ["LEFT", "RIGHT", "CENTER", "TOP", "BOTTOM", "JUSTIFY", "MIDDLE"]}, "actionType": {"type": "string", "enum": ["RUNSCRIPT", "RUNUSERACTION", "RUNELEMENTACTION"]}, "targetElementAction": {"type": "string"}, "multiline": {"type": "boolean"}}}, "MaskColumn": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "position": {"type": "integer", "format": "int32"}, "textAlign": {"type": "string", "enum": ["LEFT", "RIGHT", "CENTER", "TOP", "BOTTOM", "JUSTIFY", "MIDDLE"]}, "width": {"type": "integer", "format": "int32"}, "wrapLine": {"type": "boolean"}, "label": {"type": "string"}, "ecmFieldId": {"type": "integer", "format": "int64"}, "collapsible": {"type": "boolean"}, "translate": {"type": "boolean"}, "type": {"type": "string", "enum": ["DEFAULT", "LINK", "PHONE", "MAIL", "CHECKBOX", "TOGGLE", "ICON", "COLOR", "USTID", "STEUERID", "HTML", "TEXTAREA", "BIC", "IBAN", "STEUERNR", "RVNR", "USER", "GROUP", "GEOCOORDINATE", "ADDRESS", "FORMULA", "UNIT"]}, "showLabel": {"type": "boolean"}, "unit": {"type": "string"}, "hideIfEmpty": {"type": "boolean"}, "hideIfUnique": {"type": "boolean"}, "hideIfEmbedded": {"type": "boolean"}, "visible": {"type": "boolean"}, "fulltextSearch": {"type": "boolean"}}}, "MaskField": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "separatorId": {"type": "integer", "format": "int64"}, "maskRow": {"type": "integer", "format": "int32"}, "maskColumn": {"type": "integer", "format": "int32"}, "colspan": {"type": "integer", "format": "int32"}, "rowspan": {"type": "integer", "format": "int32"}, "textAreaColumns": {"type": "integer", "format": "int32"}, "disabled": {"type": "boolean"}, "autoComplete": {"type": "boolean"}, "hybridMode": {"type": "boolean"}, "required": {"type": "boolean"}, "ecmFieldId": {"type": "integer", "format": "int64"}, "defaultValue": {"type": "string"}, "checkAgainstNst": {"type": "boolean"}, "nstSortFieldId": {"type": "integer", "format": "int64"}, "nstSortDirection": {"type": "string", "enum": ["ASC", "DESC"]}, "showSelectButtons": {"type": "boolean"}, "selectButtonDisplayFieldId": {"type": "integer", "format": "int64"}, "selectButtonOrientation": {"type": "string", "enum": ["HORIZONTAL", "VERTICAL"]}, "selectButtonLimit": {"type": "integer", "format": "int32"}, "type": {"type": "string", "enum": ["DEFAULT", "LINK", "PHONE", "MAIL", "CHECKBOX", "TOGGLE", "ICON", "COLOR", "USTID", "STEUERID", "HTML", "TEXTAREA", "BIC", "IBAN", "STEUERNR", "RVNR", "USER", "GROUP", "GEOCOORDINATE", "ADDRESS", "FORMULA", "UNIT"]}, "htmlMaxHeight": {"type": "integer", "format": "int32"}, "unit": {"type": "string"}, "translate": {"type": "boolean"}, "allowClone": {"type": "boolean"}, "textAlign": {"type": "string", "enum": ["LEFT", "RIGHT", "CENTER", "TOP", "BOTTOM", "JUSTIFY", "MIDDLE"]}, "hint": {"type": "string"}, "label": {"type": "string"}, "placeholder": {"type": "string"}, "cellColor": {"type": "string"}, "formatAsLabel": {"type": "boolean"}, "validate": {"type": "boolean"}, "minLength": {"type": "integer", "format": "int32"}, "maxLength": {"type": "integer", "format": "int32"}, "allowedSigns": {"type": "string"}, "allowUpperCharacters": {"type": "boolean"}, "allowLowerCharacters": {"type": "boolean"}, "allowNumbers": {"type": "boolean"}, "allowWhitespaces": {"type": "boolean"}, "multiline": {"type": "boolean"}, "width": {"type": "integer", "format": "int32"}, "widthUnity": {"type": "string", "enum": ["PERCENT", "PIXEL"]}, "columns": {"type": "array", "items": {"$ref": "#/components/schemas/MaskFieldColumn"}}, "constraints": {"type": "array", "items": {"$ref": "#/components/schemas/MaskFieldConstraint"}}, "autoFills": {"type": "array", "items": {"$ref": "#/components/schemas/MaskFieldAutoFill"}}, "validationConditions": {"type": "array", "items": {"$ref": "#/components/schemas/MaskFieldValidationCondition"}}, "valueRangeFrom": {"type": "number", "format": "float"}, "valueRangeTo": {"type": "number", "format": "float"}}}, "MaskFieldAutoFill": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "nstFieldId": {"type": "integer", "format": "int64"}, "targetFieldId": {"type": "integer", "format": "int64"}, "overwrite": {"type": "boolean"}, "cascadeClear": {"type": "boolean"}}}, "MaskFieldColumn": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "label": {"type": "string"}, "position": {"type": "integer", "format": "int32"}, "width": {"type": "integer", "format": "int32"}, "visible": {"type": "boolean"}, "searchable": {"type": "boolean"}, "textAlign": {"type": "string", "enum": ["LEFT", "RIGHT", "CENTER", "TOP", "BOTTOM", "JUSTIFY", "MIDDLE"]}, "ecmFieldId": {"type": "integer", "format": "int64"}, "multiline": {"type": "boolean"}, "unit": {"type": "string"}}}, "MaskFieldConstraint": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "nstMaskFieldId": {"type": "integer", "format": "int64"}, "compareType": {"type": "string", "enum": ["EQUAL", "NOTEQUAL", "LESSTHAN", "LESSTHANEQUALS", "GREATERTHAN", "GREATERTHANEQUALS", "LIKE", "NOTLIKE", "ILIKE", "NOTILIKE", "ISNULL", "ISNOTNULL", "BETWEEN", "UNIQUE", "NOTUNIQUE", "CHANGEDLOCAL", "CHANGEDGLOBAL", "IN", "NOTIN", "CONTAINS", "EQUALSIGNORECASE", "STARTSWITH", "ENDSWITH", "CHANGEDBYUSER", "NOTSTARTSWITH", "NOTENDSWITH", "EXISTS", "NOTEXISTS", "SOUNDEX", "NOTBETWEEN"]}, "parentMaskFieldId": {"type": "integer", "format": "int64"}, "compareValue": {"type": "string"}, "filterGroup": {"type": "integer", "format": "int32"}, "filterEmpty": {"type": "boolean"}}}, "MaskFieldValidationCondition": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "chooseValue": {"type": "string", "enum": ["EQUAL", "NOTEQUAL", "LESSTHAN", "LESSTHANEQUALS", "GREATERTHAN", "GREATERTHANEQUALS", "LIKE", "NOTLIKE", "ILIKE", "NOTILIKE", "ISNULL", "ISNOTNULL", "BETWEEN", "UNIQUE", "NOTUNIQUE", "CHANGEDLOCAL", "CHANGEDGLOBAL", "IN", "NOTIN", "CONTAINS", "EQUALSIGNORECASE", "STARTSWITH", "ENDSWITH", "CHANGEDBYUSER", "NOTSTARTSWITH", "NOTENDSWITH", "EXISTS", "NOTEXISTS", "SOUNDEX", "NOTBETWEEN"]}, "isRange": {"type": "boolean"}, "value": {"type": "string"}, "valueTo": {"type": "string"}, "caseSensitive": {"type": "boolean"}, "position": {"type": "integer", "format": "int32"}, "filterGroup": {"type": "integer", "format": "int32"}}}, "MaskFilter": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "name": {"type": "string"}, "autoFilter": {"type": "boolean"}, "forced": {"type": "boolean"}, "visible": {"type": "boolean"}, "applicableFor": {"type": "string", "enum": ["ALL", "MAINMASK", "EMBEDDEDMASKS", "SINGLEEMBEDDEDMASK", "OFFLINEMASK"]}, "embeddedMaskId": {"type": "integer", "format": "int64"}, "icon": {"type": "string"}, "filterValues": {"type": "array", "items": {"$ref": "#/components/schemas/MaskFilterValue"}}}}, "MaskFilterField": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "ecmFieldId": {"type": "integer", "format": "int64"}, "position": {"type": "integer", "format": "int32"}, "label": {"type": "string"}}}, "MaskFilterValue": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "ecmFieldId": {"type": "integer", "format": "int64"}, "compareType": {"type": "string", "enum": ["EQUAL", "NOTEQUAL", "LESSTHAN", "LESSTHANEQUALS", "GREATERTHAN", "GREATERTHANEQUALS", "LIKE", "NOTLIKE", "ILIKE", "NOTILIKE", "ISNULL", "ISNOTNULL", "BETWEEN", "UNIQUE", "NOTUNIQUE", "CHANGEDLOCAL", "CHANGEDGLOBAL", "IN", "NOTIN", "CONTAINS", "EQUALSIGNORECASE", "STARTSWITH", "ENDSWITH", "CHANGEDBYUSER", "NOTSTARTSWITH", "NOTENDSWITH", "EXISTS", "NOTEXISTS", "SOUNDEX", "NOTBETWEEN"]}, "value1": {"type": "string"}, "value2": {"type": "string"}, "filterGroup": {"type": "integer", "format": "int32"}}}, "MaskIcon": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "name": {"type": "string"}, "title": {"type": "string"}, "iconSource": {"type": "string", "enum": ["DEFAULT", "ECMFIELD", "VALUE"]}, "iconFieldId": {"type": "integer", "format": "int64"}, "icon": {"type": "string"}, "colorSource": {"type": "string", "enum": ["DEFAULT", "ECMFIELD", "VALUE"]}, "colorFieldId": {"type": "integer", "format": "int64"}, "color": {"type": "string"}, "rowPos": {"type": "integer", "format": "int32"}, "colPos": {"type": "integer", "format": "int32"}}, "deprecated": true}, "MaskLabel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "separatorId": {"type": "integer", "format": "int64"}, "maskFieldId": {"type": "integer", "format": "int64"}, "maskRow": {"type": "integer", "format": "int32"}, "maskColumn": {"type": "integer", "format": "int32"}, "colspan": {"type": "integer", "format": "int32"}, "rowspan": {"type": "integer", "format": "int32"}, "label": {"type": "string"}, "textAlign": {"type": "string", "enum": ["LEFT", "RIGHT", "CENTER", "TOP", "BOTTOM", "JUSTIFY", "MIDDLE"]}, "bold": {"type": "boolean"}, "underline": {"type": "boolean"}, "italic": {"type": "boolean"}, "fontSize": {"type": "integer", "format": "int32"}, "fontColor": {"type": "string"}, "multiline": {"type": "boolean"}, "width": {"type": "integer", "format": "int32"}, "widthUnity": {"type": "string", "enum": ["PERCENT", "PIXEL"]}}}, "MaskRule": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "name": {"type": "string"}, "position": {"type": "integer", "format": "int32"}, "onloadonly": {"type": "boolean"}, "onsaveonly": {"type": "boolean"}, "stopProcessing": {"type": "boolean"}, "conditions": {"type": "array", "items": {"$ref": "#/components/schemas/MaskRuleCondition"}}, "actions": {"type": "array", "items": {"$ref": "#/components/schemas/MaskRuleAction"}}}}, "MaskRuleAction": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "type": {"type": "string", "enum": ["ALERT", "SETVALUE", "SETMANDATORY", "HIDEFIELD", "HIDEFIELDROW", "DISABLEFIELD", "SHOWSECTION", "HIDESECTION", "OPENSECTION", "CLOSESECTION", "HIDEEMBEDDED", "OPENEMBEDDED", "CLOSEEMBEDDED", "HIDEACTION", "RUNSCRIPT", "HIGHLIGHT", "SETCOLOR", "HIDEINDEXCOL", "DISABLEFORM", "SETLABEL", "SETERROR", "SETBORDERCOLOR", "ADDREVISION", "DISABLEEMBEDDED", "SETWORKFLOW", "DISABLEDELETE", "DISABLECOPY", "DISABLEUPLOAD", "FORCECOMMENT", "ADDRIGHT", "SETRIGHT", "STARTWORKFLOW", "SETVALUEFROMNST", "SETCELLCOLOR", "SETLABELCOLOR", "SETCELL", "SETROW", "SAVEITEM", "SAVEITEMNEXT", "SAVEITEMCLOSE", "EDITICON", "HIDEDIV", "HIDEBUTTON", "SETBUTTONICON", "DISABLEELEMENTSTATEACTIONS", "DISABLESECTION", "HIDEELEMENTSTATEACTION", "HIDELABEL", "SHOWHINT", "CLOSEITEM", "FOCUSFIELD", "SETTMPMESSAGE", "CREATEDOCUMENT", "CREATEDOCUMENTWITHFILE", "CREATEDOCUMENTWITHPHOTO", "HIDEMARKERSOURCES", "HIDESAVEBUTTON", "CREATEDOCUMENTWITHPHOTOS", "SETSIGNATUREREADONLY", "HIDEEMBEDDEDCONTAINER", "TOGGLESECTIONSTATE", "TOGGLESECTIONVISIBILITY"]}, "style": {"type": "string", "enum": ["SETTEXTCOLOR", "SETBACKGROUNDCOLOR", "SETBORDERCOLOR", "SETTEXTNORMAL", "SETTEXTBOLD", "SETTEXTITALIC", "SETTEXTUNDERLINE", "SETPOPOVER", "SETICON", "SETICONCOLOR", "SETICONTITLE", "SETICONCLICK", "SPACING", "REMOVEICONCLICK", "HIDEICON"]}, "sourceTableId": {"type": "integer", "format": "int64"}, "sourceFieldId": {"type": "integer", "format": "int64"}, "targetFieldId": {"type": "integer", "format": "int64"}, "targetMaskId": {"type": "integer", "format": "int64"}, "targetLabelId": {"type": "integer", "format": "int64"}, "targetSeparatorId": {"type": "integer", "format": "int64"}, "targetButtonId": {"type": "integer", "format": "int64"}, "targetSignatureId": {"type": "integer", "format": "int64"}, "value": {"type": "string"}, "alertTitle": {"type": "string"}, "alertIcon": {"type": "string"}, "alertIconColor": {"type": "string"}, "alertWidth": {"type": "integer", "format": "int32"}, "priority": {"type": "integer", "format": "int32"}, "constraints": {"type": "array", "items": {"$ref": "#/components/schemas/MaskRuleActionConstraint"}}}}, "MaskRuleActionConstraint": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "filterFieldId": {"type": "integer", "format": "int64"}, "chooseValue": {"type": "string", "enum": ["EQUAL", "NOTEQUAL", "LESSTHAN", "LESSTHANEQUALS", "GREATERTHAN", "GREATERTHANEQUALS", "LIKE", "NOTLIKE", "ILIKE", "NOTILIKE", "ISNULL", "ISNOTNULL", "BETWEEN", "UNIQUE", "NOTUNIQUE", "CHANGEDLOCAL", "CHANGEDGLOBAL", "IN", "NOTIN", "CONTAINS", "EQUALSIGNORECASE", "STARTSWITH", "ENDSWITH", "CHANGEDBYUSER", "NOTSTARTSWITH", "NOTENDSWITH", "EXISTS", "NOTEXISTS", "SOUNDEX", "NOTBETWEEN"]}, "compareFieldId": {"type": "integer", "format": "int64"}, "compareValue": {"type": "string"}, "constraintTableId": {"type": "integer", "format": "int64"}, "filterGroup": {"type": "integer", "format": "int32"}, "filterEmpty": {"type": "boolean"}}}, "MaskRuleCondition": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "type": {"type": "string", "enum": ["ONMASKACTION", "ONMASKFIELDVALUE", "ONBEFOREWORKFLOWSTART", "ONCURRENTNODEID", "ONCURRENTPROCESS", "ONCURRENTUSER", "ONCURRENTUSERGROUP", "ONCOMBINEDVALUE", "ONPARENTMASK", "ONBUTTONCLICK", "ONINITVALUE", "ONACTIONCLICK", "ONSTRUCTURE", "ONHASPROCESS", "ISMANDATORY", "ONPARENTMASKFIELDVALUE", "ONMASKSEPARATORTOGGLE", "ONMAINMASK", "ONPLACEHOLDERS", "ONCONFIGPARAM", "ONUSERPARAM", "ONDOCUMENTACCESS", "ONCURRENTCUSTOMER", "ONPLAINVALUE", "ONFILENAME", "ONFILECHANGE"]}, "targetFieldId": {"type": "integer", "format": "int64"}, "targetParam": {"type": "string"}, "sourceValue": {"type": "string"}, "sourceValueType": {"type": "string", "enum": ["STRING", "INTEGER", "DOUBLE", "CURRENCY", "DATE", "TIME", "DATETIME", "BOOLEAN", "TEXT", "FULLTEXT", "AUTOINCREMENT", "LONG", "UNKNOWN"]}, "compareType": {"type": "string", "enum": ["EQUAL", "NOTEQUAL", "LESSTHAN", "LESSTHANEQUALS", "GREATERTHAN", "GREATERTHANEQUALS", "LIKE", "NOTLIKE", "ILIKE", "NOTILIKE", "ISNULL", "ISNOTNULL", "BETWEEN", "UNIQUE", "NOTUNIQUE", "CHANGEDLOCAL", "CHANGEDGLOBAL", "IN", "NOTIN", "CONTAINS", "EQUALSIGNORECASE", "STARTSWITH", "ENDSWITH", "CHANGEDBYUSER", "NOTSTARTSWITH", "NOTENDSWITH", "EXISTS", "NOTEXISTS", "SOUNDEX", "NOTBETWEEN"]}, "compareFieldId": {"type": "integer", "format": "int64"}, "compareValue": {"type": "string"}, "actionType": {"type": "string", "enum": ["CREATE", "UPDATE", "PREVIEW"]}, "filterGroup": {"type": "integer", "format": "int32"}, "compareUserId": {"type": "integer", "format": "int64"}, "compareGroupId": {"type": "string"}, "compareCustomerId": {"type": "integer", "format": "int64"}, "compareMaskId": {"type": "integer", "format": "int64"}, "compareButtonId": {"type": "integer", "format": "int64"}, "compareSeparatorId": {"type": "integer", "format": "int64"}, "targetFields": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "MaskSeparator": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "label": {"type": "string"}, "maskRow": {"type": "integer", "format": "int32"}, "showLabel": {"type": "boolean"}, "allowMinimize": {"type": "boolean"}, "startMinimized": {"type": "boolean"}, "textAlign": {"type": "string", "enum": ["LEFT", "RIGHT", "CENTER", "TOP", "BOTTOM", "JUSTIFY", "MIDDLE"]}, "hidden": {"type": "boolean"}, "color": {"type": "string"}, "cellColor": {"type": "string"}, "separatorColor": {"type": "string"}}}, "MaskSignature": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "separatorId": {"type": "integer", "format": "int64"}, "maskRow": {"type": "integer", "format": "int32"}, "maskColumn": {"type": "integer", "format": "int32"}, "colspan": {"type": "integer", "format": "int32"}, "rowspan": {"type": "integer", "format": "int32"}, "nameFieldId": {"type": "integer", "format": "int64"}, "dateFieldId": {"type": "integer", "format": "int64"}, "locationFieldId": {"type": "integer", "format": "int64"}, "required": {"type": "boolean"}}}, "MaskSorting": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "sortFieldId": {"type": "integer", "format": "int64", "readOnly": true}, "sortDirection": {"type": "string", "readOnly": true, "enum": ["ASC", "DESC"]}, "position": {"type": "integer", "format": "int32", "readOnly": true}}}, "MaskStructure": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "showCounts": {"type": "string", "enum": ["NONE", "FIRSTLEVEL", "ALLLEVELS"]}, "position": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "label": {"type": "string"}, "showIncomplete": {"type": "boolean"}, "hideSelectedCols": {"type": "boolean"}, "icon": {"type": "string"}, "iconColor": {"type": "string"}, "childNodes": {"type": "array", "items": {"$ref": "#/components/schemas/MaskStructureNode"}}}}, "MaskStructureNode": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "displaySource": {"type": "string", "deprecated": true, "enum": ["DEFAULT", "ECMFIELD", "VALUE"]}, "displayType": {"type": "string", "enum": ["DEFAULT", "ECMFIELD", "VALUE"]}, "value": {"type": "string"}, "keyFieldId": {"type": "integer", "format": "int64"}, "displayFieldId": {"type": "integer", "format": "int64"}, "combinedValue": {"type": "string"}, "icon": {"type": "string"}, "iconColor": {"type": "string"}, "position": {"type": "integer", "format": "int32"}, "sortFieldId": {"type": "integer", "format": "int64"}, "sortDirection": {"type": "string", "enum": ["ASC", "DESC"]}}}, "RemoteAuth": {"type": "object", "properties": {"LogonName": {"type": "string", "readOnly": true}, "Payload": {"type": "string", "readOnly": true}}}, "ScheduleMaskView": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "name": {"type": "string"}, "type": {"type": "string", "enum": ["COLUMNLEFT", "COLUMNRIGHT"]}, "caption": {"type": "string"}, "ecmIndexId": {"type": "integer", "format": "int64"}, "keyFieldId": {"type": "integer", "format": "int64"}, "targetFieldId": {"type": "integer", "format": "int64"}, "displayFieldId": {"type": "integer", "format": "int64"}, "groupFieldId": {"type": "integer", "format": "int64"}, "detailFieldId": {"type": "integer", "format": "int64"}, "sortFieldId": {"type": "integer", "format": "int64"}, "scheduleDisplayFieldId": {"type": "integer", "format": "int64"}, "displayIconFieldId": {"type": "integer", "format": "int64"}, "filterValues": {"type": "array", "items": {"$ref": "#/components/schemas/MaskFilterValue"}}}}, "SyncDevice": {"required": ["name", "type"], "type": "object", "properties": {"uuid": {"type": "string", "readOnly": true}, "appId": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "string", "enum": ["ANDROID", "IOS", "WINDOWSMOBILE", "CLOUDCONNECTOR", "OFFICEGATE", "REMOTEBACKEND", "SOAPUI"]}, "format": {"type": "string", "enum": ["UNKNOWN", "PHONE", "TABLET"]}, "pushToken": {"type": "string"}, "allowPush": {"type": "boolean"}}}, "SyncItems": {"type": "object", "properties": {"definitionId": {"type": "string", "readOnly": true}, "items": {"type": "object", "additionalProperties": {"type": "string"}, "readOnly": true}}}, "SyncLog": {"type": "object", "properties": {"osVersion": {"type": "string"}, "appVersion": {"type": "string"}, "deviceType": {"type": "string"}, "connectionType": {"type": "string"}, "syncTime": {"type": "string", "format": "date-time"}, "syncDuration": {"type": "integer", "format": "int32"}, "requestCount": {"type": "integer", "format": "int32"}, "warnCount": {"type": "integer", "format": "int32"}, "errorCount": {"type": "integer", "format": "int32"}, "successCount": {"type": "integer", "format": "int32"}, "elementsTotal": {"type": "integer", "format": "int32"}, "valuesTotal": {"type": "integer", "format": "int32"}, "fieldsTotal": {"type": "integer", "format": "int32"}, "masksTotal": {"type": "integer", "format": "int32"}, "requests": {"type": "array", "items": {"$ref": "#/components/schemas/SyncLogData"}}}}, "SyncLogData": {"type": "object", "properties": {"operationName": {"type": "string"}, "duration": {"type": "integer", "format": "int32"}, "serviceDuration": {"type": "integer", "format": "int32"}, "success": {"type": "boolean"}, "errorMessage": {"type": "string"}, "elementCount": {"type": "integer", "format": "int32"}}}, "SyncPreview": {"type": "object", "properties": {"definitionId": {"type": "integer", "format": "int64", "readOnly": true}, "items": {"type": "integer", "format": "int32", "readOnly": true}, "lastChanged": {"type": "string", "format": "date-time", "readOnly": true}, "requestTime": {"type": "integer", "format": "int64", "readOnly": true}, "error": {"$ref": "#/components/schemas/WebError"}}}, "UserConfig": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "displayName": {"type": "string"}, "locale": {"type": "string"}, "dateFormat": {"type": "string"}, "groups": {"type": "object", "additionalProperties": {"type": "string"}}, "substitutions": {"type": "object", "additionalProperties": {"type": "string"}}, "params": {"type": "object", "additionalProperties": {"type": "string"}}, "translations": {"type": "object", "additionalProperties": {"type": "string"}}}}, "UserInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "name": {"type": "string", "readOnly": true}, "displayName": {"type": "string", "readOnly": true}}}, "Viewer": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "name": {"type": "string"}, "position": {"type": "integer", "format": "int32"}, "type": {"type": "string", "enum": ["FILEVIEWER", "MAPVIEWER", "TILEVIEWER", "IFRAME", "CHART", "ELEMENTSTATEVIEWER", "GANTTVIEWER", "MILESTONE", "BIMVIEWER", "DATAPINE"]}, "geoSource": {"type": "string", "enum": ["ADDRESS", "LATLONG"]}, "mapPositionFieldId": {"type": "integer", "format": "int64"}, "mapLatitudeFieldId": {"type": "integer", "format": "int64"}, "mapLongitudeFieldId": {"type": "integer", "format": "int64"}, "markerColorSource": {"type": "string", "enum": ["DEFAULT", "ECMFIELD", "VALUE"]}, "markerColorFieldId": {"type": "integer", "format": "int64"}, "markerColor": {"type": "string"}, "mapElementButtonColor": {"type": "string"}, "mapElementButtonTextColor": {"type": "string"}, "multiFileMaskId": {"type": "integer", "format": "int64"}, "signatureMaskId": {"type": "integer", "format": "int64"}, "ignoreIndexMarkers": {"type": "boolean"}, "iframeViewerCaption": {"type": "string"}, "iframeViewerSource": {"type": "string"}, "tileViewerWidthFieldId": {"type": "integer", "format": "int64"}, "tileViewerHeightFieldId": {"type": "integer", "format": "int64"}, "markerSources": {"type": "array", "items": {"$ref": "#/components/schemas/MarkerSource"}}}}, "WebError": {"type": "object", "properties": {"uid": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "code": {"type": "string"}, "title": {"type": "string"}, "detail": {"type": "string"}}, "description": "Error message"}, "WebToken": {"type": "object", "properties": {"token": {"type": "string", "readOnly": true}, "validTo": {"type": "string", "format": "date-time", "readOnly": true}, "username": {"type": "string", "readOnly": true}, "customerId": {"type": "integer", "format": "int64", "nullable": true}, "customers": {"type": "array", "readOnly": true, "items": {"$ref": "#/components/schemas/CustomerInfo"}}, "serverVersion": {"type": "string", "readOnly": true}, "supportsAppVersion6": {"type": "boolean", "readOnly": true}}, "description": "Authentication Token"}, "WorkflowAction": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "name": {"type": "string"}, "namePlain": {"type": "string"}, "nameRemove": {"type": "string"}, "nameRemovePlain": {"type": "string"}, "iconCss": {"type": "string"}, "iconColor": {"type": "string"}, "iconCssRemove": {"type": "string"}, "iconColorRemove": {"type": "string"}, "targetFieldId": {"type": "integer", "format": "int64"}, "targetValue": {"type": "string"}, "position": {"type": "integer", "format": "int32"}, "type": {"type": "string", "enum": ["SETVALUE", "SETREMINDER", "DELEGATE", "ADDCOMMENT", "CREATEREPORT", "STOREREPORT", "OPENMASK", "CREATEEXCELEXPORT", "USERSELECT", "CLOUDCONNECTORPUSH", "TOGGLEVALUE", "OPENFOREIGNMASK", "TOGGLEBOOLEAN", "COLLECTVALUES", "RESETREMINDER"]}, "requiresComment": {"type": "boolean"}, "queryMaskAllowed": {"type": "boolean"}, "editMaskAllowed": {"type": "boolean"}, "ignoreMandatoryFields": {"type": "boolean"}, "evadeWriteFilters": {"type": "boolean"}, "saveAction": {"type": "string", "enum": ["STAY", "NEXT", "LEAVE", "NEW", "OPEN", "SAVEALL"]}}}, "WorkflowDefinition": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "name": {"type": "string"}, "keyValue": {"type": "string"}, "states": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowState"}}, "actions": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowAction"}}}}, "WorkflowState": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "readOnly": true}, "name": {"type": "string"}, "keyValue": {"type": "string"}, "inactive": {"type": "boolean"}, "escalated": {"type": "boolean"}, "finished": {"type": "boolean"}, "iconCss": {"type": "string"}, "iconColor": {"type": "string"}, "actions": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowAction"}}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}