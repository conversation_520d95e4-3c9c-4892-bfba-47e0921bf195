/*
 * iTWOsite REST Service
 *
 * Public API Documentation
 *
 * OpenAPI spec version: 3.5.4 E
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using System;
using System.Linq;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;
using SwaggerDateConverter = Levelbuild.Backend.DaguWebBackend.Client.Swagger.Client.SwaggerDateConverter;

namespace Levelbuild.Backend.DaguWebBackend.Client.Swagger.Model
{
	/// <summary>
	/// UserConfig
	/// </summary>
	[DataContract]
	public partial class UserConfig : IEquatable<UserConfig>, IValidatableObject
	{
		/// <summary>
		/// Initializes a new instance of the <see cref="UserConfig" /> class.
		/// </summary>
		/// <param name="id">id.</param>
		/// <param name="name">name.</param>
		/// <param name="displayName">displayName.</param>
		/// <param name="locale">locale.</param>
		/// <param name="dateFormat">dateFormat.</param>
		/// <param name="groups">groups.</param>
		/// <param name="substitutions">substitutions.</param>
		/// <param name="_params">_params.</param>
		/// <param name="translations">translations.</param>
		public UserConfig(long? id = default(long?), string name = default(string), string displayName = default(string), string locale = default(string),
						  string dateFormat = default(string), Dictionary<string, string> groups = default(Dictionary<string, string>),
						  Dictionary<string, string> substitutions = default(Dictionary<string, string>),
						  Dictionary<string, string> _params = default(Dictionary<string, string>),
						  Dictionary<string, string> translations = default(Dictionary<string, string>))
		{
			this.Id = id;
			this.Name = name;
			this.DisplayName = displayName;
			this.Locale = locale;
			this.DateFormat = dateFormat;
			this.Groups = groups;
			this.Substitutions = substitutions;
			this._Params = _params;
			this.Translations = translations;
		}

		/// <summary>
		/// Gets or Sets Id
		/// </summary>
		[DataMember(Name = "id", EmitDefaultValue = false)]
		public long? Id { get; set; }

		/// <summary>
		/// Gets or Sets Name
		/// </summary>
		[DataMember(Name = "name", EmitDefaultValue = false)]
		public string Name { get; set; }

		/// <summary>
		/// Gets or Sets DisplayName
		/// </summary>
		[DataMember(Name = "displayName", EmitDefaultValue = false)]
		public string DisplayName { get; set; }

		/// <summary>
		/// Gets or Sets Locale
		/// </summary>
		[DataMember(Name = "locale", EmitDefaultValue = false)]
		public string Locale { get; set; }

		/// <summary>
		/// Gets or Sets DateFormat
		/// </summary>
		[DataMember(Name = "dateFormat", EmitDefaultValue = false)]
		public string DateFormat { get; set; }

		/// <summary>
		/// Gets or Sets Groups
		/// </summary>
		[DataMember(Name = "groups", EmitDefaultValue = false)]
		public Dictionary<string, string> Groups { get; set; }

		/// <summary>
		/// Gets or Sets Substitutions
		/// </summary>
		[DataMember(Name = "substitutions", EmitDefaultValue = false)]
		public Dictionary<string, string> Substitutions { get; set; }

		/// <summary>
		/// Gets or Sets _Params
		/// </summary>
		[DataMember(Name = "params", EmitDefaultValue = false)]
		public Dictionary<string, string> _Params { get; set; }

		/// <summary>
		/// Gets or Sets Translations
		/// </summary>
		[DataMember(Name = "translations", EmitDefaultValue = false)]
		public Dictionary<string, string> Translations { get; set; }

		/// <summary>
		/// Returns the string presentation of the object
		/// </summary>
		/// <returns>String presentation of the object</returns>
		public override string ToString()
		{
			var sb = new StringBuilder();
			sb.Append("class UserConfig {\n");
			sb.Append("  Id: ").Append(Id).Append("\n");
			sb.Append("  Name: ").Append(Name).Append("\n");
			sb.Append("  DisplayName: ").Append(DisplayName).Append("\n");
			sb.Append("  Locale: ").Append(Locale).Append("\n");
			sb.Append("  DateFormat: ").Append(DateFormat).Append("\n");
			sb.Append("  Groups: ").Append(Groups).Append("\n");
			sb.Append("  Substitutions: ").Append(Substitutions).Append("\n");
			sb.Append("  _Params: ").Append(_Params).Append("\n");
			sb.Append("  Translations: ").Append(Translations).Append("\n");
			sb.Append("}\n");
			return sb.ToString();
		}

		/// <summary>
		/// Returns the JSON string presentation of the object
		/// </summary>
		/// <returns>JSON string presentation of the object</returns>
		public virtual string ToJson()
		{
			return JsonConvert.SerializeObject(this, Formatting.Indented);
		}

		/// <summary>
		/// Returns true if objects are equal
		/// </summary>
		/// <param name="input">Object to be compared</param>
		/// <returns>Boolean</returns>
		public override bool Equals(object input)
		{
			return this.Equals(input as UserConfig);
		}

		/// <summary>
		/// Returns true if UserConfig instances are equal
		/// </summary>
		/// <param name="input">Instance of UserConfig to be compared</param>
		/// <returns>Boolean</returns>
		public bool Equals(UserConfig input)
		{
			if (input == null)
				return false;

			return
				(
					this.Id == input.Id ||
					(this.Id != null &&
					 this.Id.Equals(input.Id))
				) &&
				(
					this.Name == input.Name ||
					(this.Name != null &&
					 this.Name.Equals(input.Name))
				) &&
				(
					this.DisplayName == input.DisplayName ||
					(this.DisplayName != null &&
					 this.DisplayName.Equals(input.DisplayName))
				) &&
				(
					this.Locale == input.Locale ||
					(this.Locale != null &&
					 this.Locale.Equals(input.Locale))
				) &&
				(
					this.DateFormat == input.DateFormat ||
					(this.DateFormat != null &&
					 this.DateFormat.Equals(input.DateFormat))
				) &&
				(
					this.Groups == input.Groups ||
					this.Groups != null &&
					input.Groups != null &&
					this.Groups.SequenceEqual(input.Groups)
				) &&
				(
					this.Substitutions == input.Substitutions ||
					this.Substitutions != null &&
					input.Substitutions != null &&
					this.Substitutions.SequenceEqual(input.Substitutions)
				) &&
				(
					this._Params == input._Params ||
					this._Params != null &&
					input._Params != null &&
					this._Params.SequenceEqual(input._Params)
				) &&
				(
					this.Translations == input.Translations ||
					this.Translations != null &&
					input.Translations != null &&
					this.Translations.SequenceEqual(input.Translations)
				);
		}

		/// <summary>
		/// Gets the hash code
		/// </summary>
		/// <returns>Hash code</returns>
		public override int GetHashCode()
		{
			unchecked // Overflow is fine, just wrap
			{
				int hashCode = 41;
				if (this.Id != null)
					hashCode = hashCode * 59 + this.Id.GetHashCode();
				if (this.Name != null)
					hashCode = hashCode * 59 + this.Name.GetHashCode();
				if (this.DisplayName != null)
					hashCode = hashCode * 59 + this.DisplayName.GetHashCode();
				if (this.Locale != null)
					hashCode = hashCode * 59 + this.Locale.GetHashCode();
				if (this.DateFormat != null)
					hashCode = hashCode * 59 + this.DateFormat.GetHashCode();
				if (this.Groups != null)
					hashCode = hashCode * 59 + this.Groups.GetHashCode();
				if (this.Substitutions != null)
					hashCode = hashCode * 59 + this.Substitutions.GetHashCode();
				if (this._Params != null)
					hashCode = hashCode * 59 + this._Params.GetHashCode();
				if (this.Translations != null)
					hashCode = hashCode * 59 + this.Translations.GetHashCode();
				return hashCode;
			}
		}

		/// <summary>
		/// To validate all properties of the instance
		/// </summary>
		/// <param name="validationContext">Validation context</param>
		/// <returns>Validation Result</returns>
		IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
		{
			yield break;
		}
	}
}