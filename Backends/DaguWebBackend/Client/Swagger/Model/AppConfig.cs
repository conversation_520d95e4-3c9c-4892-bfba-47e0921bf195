/*
 * iTWOsite REST Service
 *
 * Public API Documentation
 *
 * OpenAPI spec version: 3.5.4 E
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using System;
using System.Linq;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;
using SwaggerDateConverter = Levelbuild.Backend.DaguWebBackend.Client.Swagger.Client.SwaggerDateConverter;

namespace Levelbuild.Backend.DaguWebBackend.Client.Swagger.Model
{
	/// <summary>
	/// AppConfig
	/// </summary>
	[DataContract]
	public partial class AppConfig : IEquatable<AppConfig>, IValidatableObject
	{
		/// <summary>
		/// Initializes a new instance of the <see cref="AppConfig" /> class.
		/// </summary>
		/// <param name="agbHash">agbHash.</param>
		/// <param name="agbAccepted">agbAccepted.</param>
		public AppConfig(string agbHash = default(string), bool? agbAccepted = default(bool?))
		{
			this.AgbHash = agbHash;
			this.AgbAccepted = agbAccepted;
		}

		/// <summary>
		/// Gets or Sets LogoId
		/// </summary>
		[DataMember(Name = "logoId", EmitDefaultValue = false)]
		public long? LogoId { get; private set; }

		/// <summary>
		/// Gets or Sets LogoVersion
		/// </summary>
		[DataMember(Name = "logoVersion", EmitDefaultValue = false)]
		public int? LogoVersion { get; private set; }

		/// <summary>
		/// Gets or Sets CompanyName
		/// </summary>
		[DataMember(Name = "companyName", EmitDefaultValue = false)]
		public string CompanyName { get; private set; }

		/// <summary>
		/// Gets or Sets ShowCompanyName
		/// </summary>
		[DataMember(Name = "showCompanyName", EmitDefaultValue = false)]
		public bool? ShowCompanyName { get; private set; }

		/// <summary>
		/// Gets or Sets BaseColor
		/// </summary>
		[DataMember(Name = "baseColor", EmitDefaultValue = false)]
		public string BaseColor { get; private set; }

		/// <summary>
		/// Gets or Sets CorporateColor
		/// </summary>
		[DataMember(Name = "corporateColor", EmitDefaultValue = false)]
		public string CorporateColor { get; private set; }

		/// <summary>
		/// Gets or Sets SupportMail
		/// </summary>
		[DataMember(Name = "supportMail", EmitDefaultValue = false)]
		public string SupportMail { get; private set; }

		/// <summary>
		/// Gets or Sets ImprintUrl
		/// </summary>
		[DataMember(Name = "imprintUrl", EmitDefaultValue = false)]
		public string ImprintUrl { get; private set; }

		/// <summary>
		/// Gets or Sets AgbUrl
		/// </summary>
		[DataMember(Name = "agbUrl", EmitDefaultValue = false)]
		public string AgbUrl { get; private set; }

		/// <summary>
		/// Gets or Sets AgbHash
		/// </summary>
		[DataMember(Name = "agbHash", EmitDefaultValue = false)]
		public string AgbHash { get; set; }

		/// <summary>
		/// Gets or Sets ShowAGB
		/// </summary>
		[DataMember(Name = "showAGB", EmitDefaultValue = false)]
		public bool? ShowAGB { get; private set; }

		/// <summary>
		/// Gets or Sets AgbAccepted
		/// </summary>
		[DataMember(Name = "agbAccepted", EmitDefaultValue = false)]
		public bool? AgbAccepted { get; set; }

		/// <summary>
		/// Gets or Sets ShowQRScanner
		/// </summary>
		[DataMember(Name = "showQRScanner", EmitDefaultValue = false)]
		public bool? ShowQRScanner { get; private set; }

		/// <summary>
		/// Gets or Sets ShowWebLink
		/// </summary>
		[DataMember(Name = "showWebLink", EmitDefaultValue = false)]
		public bool? ShowWebLink { get; private set; }

		/// <summary>
		/// Gets or Sets _Params
		/// </summary>
		[DataMember(Name = "params", EmitDefaultValue = false)]
		public Dictionary<string, string> _Params { get; private set; }

		/// <summary>
		/// Gets or Sets GlobalFilters
		/// </summary>
		[DataMember(Name = "globalFilters", EmitDefaultValue = false)]
		public List<GlobalFilter> GlobalFilters { get; private set; }

		/// <summary>
		/// Gets or Sets Dashboards
		/// </summary>
		[DataMember(Name = "dashboards", EmitDefaultValue = false)]
		public List<Dashboard> Dashboards { get; private set; }

		/// <summary>
		/// Returns the string presentation of the object
		/// </summary>
		/// <returns>String presentation of the object</returns>
		public override string ToString()
		{
			var sb = new StringBuilder();
			sb.Append("class AppConfig {\n");
			sb.Append("  LogoId: ").Append(LogoId).Append("\n");
			sb.Append("  LogoVersion: ").Append(LogoVersion).Append("\n");
			sb.Append("  CompanyName: ").Append(CompanyName).Append("\n");
			sb.Append("  ShowCompanyName: ").Append(ShowCompanyName).Append("\n");
			sb.Append("  BaseColor: ").Append(BaseColor).Append("\n");
			sb.Append("  CorporateColor: ").Append(CorporateColor).Append("\n");
			sb.Append("  SupportMail: ").Append(SupportMail).Append("\n");
			sb.Append("  ImprintUrl: ").Append(ImprintUrl).Append("\n");
			sb.Append("  AgbUrl: ").Append(AgbUrl).Append("\n");
			sb.Append("  AgbHash: ").Append(AgbHash).Append("\n");
			sb.Append("  ShowAGB: ").Append(ShowAGB).Append("\n");
			sb.Append("  AgbAccepted: ").Append(AgbAccepted).Append("\n");
			sb.Append("  ShowQRScanner: ").Append(ShowQRScanner).Append("\n");
			sb.Append("  ShowWebLink: ").Append(ShowWebLink).Append("\n");
			sb.Append("  _Params: ").Append(_Params).Append("\n");
			sb.Append("  GlobalFilters: ").Append(GlobalFilters).Append("\n");
			sb.Append("  Dashboards: ").Append(Dashboards).Append("\n");
			sb.Append("}\n");
			return sb.ToString();
		}

		/// <summary>
		/// Returns the JSON string presentation of the object
		/// </summary>
		/// <returns>JSON string presentation of the object</returns>
		public virtual string ToJson()
		{
			return JsonConvert.SerializeObject(this, Formatting.Indented);
		}

		/// <summary>
		/// Returns true if objects are equal
		/// </summary>
		/// <param name="input">Object to be compared</param>
		/// <returns>Boolean</returns>
		public override bool Equals(object input)
		{
			return this.Equals(input as AppConfig);
		}

		/// <summary>
		/// Returns true if AppConfig instances are equal
		/// </summary>
		/// <param name="input">Instance of AppConfig to be compared</param>
		/// <returns>Boolean</returns>
		public bool Equals(AppConfig input)
		{
			if (input == null)
				return false;

			return
				(
					this.LogoId == input.LogoId ||
					(this.LogoId != null &&
					 this.LogoId.Equals(input.LogoId))
				) &&
				(
					this.LogoVersion == input.LogoVersion ||
					(this.LogoVersion != null &&
					 this.LogoVersion.Equals(input.LogoVersion))
				) &&
				(
					this.CompanyName == input.CompanyName ||
					(this.CompanyName != null &&
					 this.CompanyName.Equals(input.CompanyName))
				) &&
				(
					this.ShowCompanyName == input.ShowCompanyName ||
					(this.ShowCompanyName != null &&
					 this.ShowCompanyName.Equals(input.ShowCompanyName))
				) &&
				(
					this.BaseColor == input.BaseColor ||
					(this.BaseColor != null &&
					 this.BaseColor.Equals(input.BaseColor))
				) &&
				(
					this.CorporateColor == input.CorporateColor ||
					(this.CorporateColor != null &&
					 this.CorporateColor.Equals(input.CorporateColor))
				) &&
				(
					this.SupportMail == input.SupportMail ||
					(this.SupportMail != null &&
					 this.SupportMail.Equals(input.SupportMail))
				) &&
				(
					this.ImprintUrl == input.ImprintUrl ||
					(this.ImprintUrl != null &&
					 this.ImprintUrl.Equals(input.ImprintUrl))
				) &&
				(
					this.AgbUrl == input.AgbUrl ||
					(this.AgbUrl != null &&
					 this.AgbUrl.Equals(input.AgbUrl))
				) &&
				(
					this.AgbHash == input.AgbHash ||
					(this.AgbHash != null &&
					 this.AgbHash.Equals(input.AgbHash))
				) &&
				(
					this.ShowAGB == input.ShowAGB ||
					(this.ShowAGB != null &&
					 this.ShowAGB.Equals(input.ShowAGB))
				) &&
				(
					this.AgbAccepted == input.AgbAccepted ||
					(this.AgbAccepted != null &&
					 this.AgbAccepted.Equals(input.AgbAccepted))
				) &&
				(
					this.ShowQRScanner == input.ShowQRScanner ||
					(this.ShowQRScanner != null &&
					 this.ShowQRScanner.Equals(input.ShowQRScanner))
				) &&
				(
					this.ShowWebLink == input.ShowWebLink ||
					(this.ShowWebLink != null &&
					 this.ShowWebLink.Equals(input.ShowWebLink))
				) &&
				(
					this._Params == input._Params ||
					this._Params != null &&
					input._Params != null &&
					this._Params.SequenceEqual(input._Params)
				) &&
				(
					this.GlobalFilters == input.GlobalFilters ||
					this.GlobalFilters != null &&
					input.GlobalFilters != null &&
					this.GlobalFilters.SequenceEqual(input.GlobalFilters)
				) &&
				(
					this.Dashboards == input.Dashboards ||
					this.Dashboards != null &&
					input.Dashboards != null &&
					this.Dashboards.SequenceEqual(input.Dashboards)
				);
		}

		/// <summary>
		/// Gets the hash code
		/// </summary>
		/// <returns>Hash code</returns>
		public override int GetHashCode()
		{
			unchecked // Overflow is fine, just wrap
			{
				int hashCode = 41;
				if (this.LogoId != null)
					hashCode = hashCode * 59 + this.LogoId.GetHashCode();
				if (this.LogoVersion != null)
					hashCode = hashCode * 59 + this.LogoVersion.GetHashCode();
				if (this.CompanyName != null)
					hashCode = hashCode * 59 + this.CompanyName.GetHashCode();
				if (this.ShowCompanyName != null)
					hashCode = hashCode * 59 + this.ShowCompanyName.GetHashCode();
				if (this.BaseColor != null)
					hashCode = hashCode * 59 + this.BaseColor.GetHashCode();
				if (this.CorporateColor != null)
					hashCode = hashCode * 59 + this.CorporateColor.GetHashCode();
				if (this.SupportMail != null)
					hashCode = hashCode * 59 + this.SupportMail.GetHashCode();
				if (this.ImprintUrl != null)
					hashCode = hashCode * 59 + this.ImprintUrl.GetHashCode();
				if (this.AgbUrl != null)
					hashCode = hashCode * 59 + this.AgbUrl.GetHashCode();
				if (this.AgbHash != null)
					hashCode = hashCode * 59 + this.AgbHash.GetHashCode();
				if (this.ShowAGB != null)
					hashCode = hashCode * 59 + this.ShowAGB.GetHashCode();
				if (this.AgbAccepted != null)
					hashCode = hashCode * 59 + this.AgbAccepted.GetHashCode();
				if (this.ShowQRScanner != null)
					hashCode = hashCode * 59 + this.ShowQRScanner.GetHashCode();
				if (this.ShowWebLink != null)
					hashCode = hashCode * 59 + this.ShowWebLink.GetHashCode();
				if (this._Params != null)
					hashCode = hashCode * 59 + this._Params.GetHashCode();
				if (this.GlobalFilters != null)
					hashCode = hashCode * 59 + this.GlobalFilters.GetHashCode();
				if (this.Dashboards != null)
					hashCode = hashCode * 59 + this.Dashboards.GetHashCode();
				return hashCode;
			}
		}

		/// <summary>
		/// To validate all properties of the instance
		/// </summary>
		/// <param name="validationContext">Validation context</param>
		/// <returns>Validation Result</returns>
		IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
		{
			yield break;
		}
	}
}