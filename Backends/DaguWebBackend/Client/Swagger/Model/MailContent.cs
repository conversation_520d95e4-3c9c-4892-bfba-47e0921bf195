/*
 * iTWOsite REST Service
 *
 * Public API Documentation
 *
 * OpenAPI spec version: 3.5.4 E
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using System;
using System.Linq;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;
using SwaggerDateConverter = Levelbuild.Backend.DaguWebBackend.Client.Swagger.Client.SwaggerDateConverter;

namespace Levelbuild.Backend.DaguWebBackend.Client.Swagger.Model
{
	/// <summary>
	/// MailContent
	/// </summary>
	[DataContract]
	public partial class MailContent : IEquatable<MailContent>, IValidatableObject
	{
		/// <summary>
		/// Initializes a new instance of the <see cref="MailContent" /> class.
		/// </summary>
		/// <param name="subject">subject.</param>
		/// <param name="text">text.</param>
		/// <param name="html">html.</param>
		/// <param name="priority">priority.</param>
		/// <param name="sentDate">sentDate.</param>
		/// <param name="from">from.</param>
		/// <param name="to">to.</param>
		/// <param name="cc">cc.</param>
		/// <param name="bcc">bcc.</param>
		/// <param name="appointmentStart">appointmentStart.</param>
		/// <param name="appointmentEnd">appointmentEnd.</param>
		/// <param name="appointmentLocation">appointmentLocation.</param>
		/// <param name="attachments">attachments.</param>
		public MailContent(string subject = default(string), string text = default(string), string html = default(string), string priority = default(string),
						   DateTime? sentDate = default(DateTime?), MailAddress from = default(MailAddress), List<MailAddress> to = default(List<MailAddress>),
						   List<MailAddress> cc = default(List<MailAddress>), List<MailAddress> bcc = default(List<MailAddress>),
						   DateTime? appointmentStart = default(DateTime?), DateTime? appointmentEnd = default(DateTime?),
						   string appointmentLocation = default(string), List<MailAttachment> attachments = default(List<MailAttachment>))
		{
			this.Subject = subject;
			this.Text = text;
			this.Html = html;
			this.Priority = priority;
			this.SentDate = sentDate;
			this.From = from;
			this.To = to;
			this.Cc = cc;
			this.Bcc = bcc;
			this.AppointmentStart = appointmentStart;
			this.AppointmentEnd = appointmentEnd;
			this.AppointmentLocation = appointmentLocation;
			this.Attachments = attachments;
		}

		/// <summary>
		/// Gets or Sets Subject
		/// </summary>
		[DataMember(Name = "subject", EmitDefaultValue = false)]
		public string Subject { get; set; }

		/// <summary>
		/// Gets or Sets Text
		/// </summary>
		[DataMember(Name = "text", EmitDefaultValue = false)]
		public string Text { get; set; }

		/// <summary>
		/// Gets or Sets Html
		/// </summary>
		[DataMember(Name = "html", EmitDefaultValue = false)]
		public string Html { get; set; }

		/// <summary>
		/// Gets or Sets Priority
		/// </summary>
		[DataMember(Name = "priority", EmitDefaultValue = false)]
		public string Priority { get; set; }

		/// <summary>
		/// Gets or Sets SentDate
		/// </summary>
		[DataMember(Name = "sentDate", EmitDefaultValue = false)]
		public DateTime? SentDate { get; set; }

		/// <summary>
		/// Gets or Sets From
		/// </summary>
		[DataMember(Name = "from", EmitDefaultValue = false)]
		public MailAddress From { get; set; }

		/// <summary>
		/// Gets or Sets To
		/// </summary>
		[DataMember(Name = "to", EmitDefaultValue = false)]
		public List<MailAddress> To { get; set; }

		/// <summary>
		/// Gets or Sets Cc
		/// </summary>
		[DataMember(Name = "cc", EmitDefaultValue = false)]
		public List<MailAddress> Cc { get; set; }

		/// <summary>
		/// Gets or Sets Bcc
		/// </summary>
		[DataMember(Name = "bcc", EmitDefaultValue = false)]
		public List<MailAddress> Bcc { get; set; }

		/// <summary>
		/// Gets or Sets AppointmentStart
		/// </summary>
		[DataMember(Name = "appointmentStart", EmitDefaultValue = false)]
		public DateTime? AppointmentStart { get; set; }

		/// <summary>
		/// Gets or Sets AppointmentEnd
		/// </summary>
		[DataMember(Name = "appointmentEnd", EmitDefaultValue = false)]
		public DateTime? AppointmentEnd { get; set; }

		/// <summary>
		/// Gets or Sets AppointmentLocation
		/// </summary>
		[DataMember(Name = "appointmentLocation", EmitDefaultValue = false)]
		public string AppointmentLocation { get; set; }

		/// <summary>
		/// Gets or Sets Attachments
		/// </summary>
		[DataMember(Name = "attachments", EmitDefaultValue = false)]
		public List<MailAttachment> Attachments { get; set; }

		/// <summary>
		/// Returns the string presentation of the object
		/// </summary>
		/// <returns>String presentation of the object</returns>
		public override string ToString()
		{
			var sb = new StringBuilder();
			sb.Append("class MailContent {\n");
			sb.Append("  Subject: ").Append(Subject).Append("\n");
			sb.Append("  Text: ").Append(Text).Append("\n");
			sb.Append("  Html: ").Append(Html).Append("\n");
			sb.Append("  Priority: ").Append(Priority).Append("\n");
			sb.Append("  SentDate: ").Append(SentDate).Append("\n");
			sb.Append("  From: ").Append(From).Append("\n");
			sb.Append("  To: ").Append(To).Append("\n");
			sb.Append("  Cc: ").Append(Cc).Append("\n");
			sb.Append("  Bcc: ").Append(Bcc).Append("\n");
			sb.Append("  AppointmentStart: ").Append(AppointmentStart).Append("\n");
			sb.Append("  AppointmentEnd: ").Append(AppointmentEnd).Append("\n");
			sb.Append("  AppointmentLocation: ").Append(AppointmentLocation).Append("\n");
			sb.Append("  Attachments: ").Append(Attachments).Append("\n");
			sb.Append("}\n");
			return sb.ToString();
		}

		/// <summary>
		/// Returns the JSON string presentation of the object
		/// </summary>
		/// <returns>JSON string presentation of the object</returns>
		public virtual string ToJson()
		{
			return JsonConvert.SerializeObject(this, Formatting.Indented);
		}

		/// <summary>
		/// Returns true if objects are equal
		/// </summary>
		/// <param name="input">Object to be compared</param>
		/// <returns>Boolean</returns>
		public override bool Equals(object input)
		{
			return this.Equals(input as MailContent);
		}

		/// <summary>
		/// Returns true if MailContent instances are equal
		/// </summary>
		/// <param name="input">Instance of MailContent to be compared</param>
		/// <returns>Boolean</returns>
		public bool Equals(MailContent input)
		{
			if (input == null)
				return false;

			return
				(
					this.Subject == input.Subject ||
					(this.Subject != null &&
					 this.Subject.Equals(input.Subject))
				) &&
				(
					this.Text == input.Text ||
					(this.Text != null &&
					 this.Text.Equals(input.Text))
				) &&
				(
					this.Html == input.Html ||
					(this.Html != null &&
					 this.Html.Equals(input.Html))
				) &&
				(
					this.Priority == input.Priority ||
					(this.Priority != null &&
					 this.Priority.Equals(input.Priority))
				) &&
				(
					this.SentDate == input.SentDate ||
					(this.SentDate != null &&
					 this.SentDate.Equals(input.SentDate))
				) &&
				(
					this.From == input.From ||
					(this.From != null &&
					 this.From.Equals(input.From))
				) &&
				(
					this.To == input.To ||
					this.To != null &&
					input.To != null &&
					this.To.SequenceEqual(input.To)
				) &&
				(
					this.Cc == input.Cc ||
					this.Cc != null &&
					input.Cc != null &&
					this.Cc.SequenceEqual(input.Cc)
				) &&
				(
					this.Bcc == input.Bcc ||
					this.Bcc != null &&
					input.Bcc != null &&
					this.Bcc.SequenceEqual(input.Bcc)
				) &&
				(
					this.AppointmentStart == input.AppointmentStart ||
					(this.AppointmentStart != null &&
					 this.AppointmentStart.Equals(input.AppointmentStart))
				) &&
				(
					this.AppointmentEnd == input.AppointmentEnd ||
					(this.AppointmentEnd != null &&
					 this.AppointmentEnd.Equals(input.AppointmentEnd))
				) &&
				(
					this.AppointmentLocation == input.AppointmentLocation ||
					(this.AppointmentLocation != null &&
					 this.AppointmentLocation.Equals(input.AppointmentLocation))
				) &&
				(
					this.Attachments == input.Attachments ||
					this.Attachments != null &&
					input.Attachments != null &&
					this.Attachments.SequenceEqual(input.Attachments)
				);
		}

		/// <summary>
		/// Gets the hash code
		/// </summary>
		/// <returns>Hash code</returns>
		public override int GetHashCode()
		{
			unchecked // Overflow is fine, just wrap
			{
				int hashCode = 41;
				if (this.Subject != null)
					hashCode = hashCode * 59 + this.Subject.GetHashCode();
				if (this.Text != null)
					hashCode = hashCode * 59 + this.Text.GetHashCode();
				if (this.Html != null)
					hashCode = hashCode * 59 + this.Html.GetHashCode();
				if (this.Priority != null)
					hashCode = hashCode * 59 + this.Priority.GetHashCode();
				if (this.SentDate != null)
					hashCode = hashCode * 59 + this.SentDate.GetHashCode();
				if (this.From != null)
					hashCode = hashCode * 59 + this.From.GetHashCode();
				if (this.To != null)
					hashCode = hashCode * 59 + this.To.GetHashCode();
				if (this.Cc != null)
					hashCode = hashCode * 59 + this.Cc.GetHashCode();
				if (this.Bcc != null)
					hashCode = hashCode * 59 + this.Bcc.GetHashCode();
				if (this.AppointmentStart != null)
					hashCode = hashCode * 59 + this.AppointmentStart.GetHashCode();
				if (this.AppointmentEnd != null)
					hashCode = hashCode * 59 + this.AppointmentEnd.GetHashCode();
				if (this.AppointmentLocation != null)
					hashCode = hashCode * 59 + this.AppointmentLocation.GetHashCode();
				if (this.Attachments != null)
					hashCode = hashCode * 59 + this.Attachments.GetHashCode();
				return hashCode;
			}
		}

		/// <summary>
		/// To validate all properties of the instance
		/// </summary>
		/// <param name="validationContext">Validation context</param>
		/// <returns>Validation Result</returns>
		IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
		{
			yield break;
		}
	}
}