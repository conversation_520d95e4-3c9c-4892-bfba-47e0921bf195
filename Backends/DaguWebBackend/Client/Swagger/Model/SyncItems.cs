/*
 * iTWOsite REST Service
 *
 * Public API Documentation
 *
 * OpenAPI spec version: 3.5.4 E
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using System;
using System.Linq;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;
using SwaggerDateConverter = Levelbuild.Backend.DaguWebBackend.Client.Swagger.Client.SwaggerDateConverter;

namespace Levelbuild.Backend.DaguWebBackend.Client.Swagger.Model
{
	/// <summary>
	/// SyncItems
	/// </summary>
	[DataContract]
	public partial class SyncItems : IEquatable<SyncItems>, IValidatableObject
	{
		/// <summary>
		/// Initializes a new instance of the <see cref="SyncItems" /> class.
		/// </summary>
		public SyncItems()
		{
		}

		/// <summary>
		/// Gets or Sets DefinitionId
		/// </summary>
		[DataMember(Name = "definitionId", EmitDefaultValue = false)]
		public string DefinitionId { get; private set; }

		/// <summary>
		/// Gets or Sets Items
		/// </summary>
		[DataMember(Name = "items", EmitDefaultValue = false)]
		public Dictionary<string, string> Items { get; private set; }

		/// <summary>
		/// Returns the string presentation of the object
		/// </summary>
		/// <returns>String presentation of the object</returns>
		public override string ToString()
		{
			var sb = new StringBuilder();
			sb.Append("class SyncItems {\n");
			sb.Append("  DefinitionId: ").Append(DefinitionId).Append("\n");
			sb.Append("  Items: ").Append(Items).Append("\n");
			sb.Append("}\n");
			return sb.ToString();
		}

		/// <summary>
		/// Returns the JSON string presentation of the object
		/// </summary>
		/// <returns>JSON string presentation of the object</returns>
		public virtual string ToJson()
		{
			return JsonConvert.SerializeObject(this, Formatting.Indented);
		}

		/// <summary>
		/// Returns true if objects are equal
		/// </summary>
		/// <param name="input">Object to be compared</param>
		/// <returns>Boolean</returns>
		public override bool Equals(object input)
		{
			return this.Equals(input as SyncItems);
		}

		/// <summary>
		/// Returns true if SyncItems instances are equal
		/// </summary>
		/// <param name="input">Instance of SyncItems to be compared</param>
		/// <returns>Boolean</returns>
		public bool Equals(SyncItems input)
		{
			if (input == null)
				return false;

			return
				(
					this.DefinitionId == input.DefinitionId ||
					(this.DefinitionId != null &&
					 this.DefinitionId.Equals(input.DefinitionId))
				) &&
				(
					this.Items == input.Items ||
					this.Items != null &&
					input.Items != null &&
					this.Items.SequenceEqual(input.Items)
				);
		}

		/// <summary>
		/// Gets the hash code
		/// </summary>
		/// <returns>Hash code</returns>
		public override int GetHashCode()
		{
			unchecked // Overflow is fine, just wrap
			{
				int hashCode = 41;
				if (this.DefinitionId != null)
					hashCode = hashCode * 59 + this.DefinitionId.GetHashCode();
				if (this.Items != null)
					hashCode = hashCode * 59 + this.Items.GetHashCode();
				return hashCode;
			}
		}

		/// <summary>
		/// To validate all properties of the instance
		/// </summary>
		/// <param name="validationContext">Validation context</param>
		/// <returns>Validation Result</returns>
		IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
		{
			yield break;
		}
	}
}