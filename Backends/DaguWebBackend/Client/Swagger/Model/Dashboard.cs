/*
 * iTWOsite REST Service
 *
 * Public API Documentation
 *
 * OpenAPI spec version: 3.5.4 E
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using System;
using System.Linq;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;
using SwaggerDateConverter = Levelbuild.Backend.DaguWebBackend.Client.Swagger.Client.SwaggerDateConverter;

namespace Levelbuild.Backend.DaguWebBackend.Client.Swagger.Model
{
	/// <summary>
	/// Dashboard
	/// </summary>
	[DataContract]
	public partial class Dashboard : IEquatable<Dashboard>, IValidatableObject
	{
		/// <summary>
		/// Initializes a new instance of the <see cref="Dashboard" /> class.
		/// </summary>
		/// <param name="id">id.</param>
		/// <param name="name">name.</param>
		/// <param name="iconCss">iconCss.</param>
		/// <param name="position">position.</param>
		/// <param name="items">items.</param>
		public Dashboard(long? id = default(long?), string name = default(string), string iconCss = default(string), int? position = default(int?),
						 List<DashboardItem> items = default(List<DashboardItem>))
		{
			this.Id = id;
			this.Name = name;
			this.IconCss = iconCss;
			this.Position = position;
			this.Items = items;
		}

		/// <summary>
		/// Gets or Sets Id
		/// </summary>
		[DataMember(Name = "id", EmitDefaultValue = false)]
		public long? Id { get; set; }

		/// <summary>
		/// Gets or Sets Name
		/// </summary>
		[DataMember(Name = "name", EmitDefaultValue = false)]
		public string Name { get; set; }

		/// <summary>
		/// Gets or Sets IconCss
		/// </summary>
		[DataMember(Name = "iconCss", EmitDefaultValue = false)]
		public string IconCss { get; set; }

		/// <summary>
		/// Gets or Sets Position
		/// </summary>
		[DataMember(Name = "position", EmitDefaultValue = false)]
		public int? Position { get; set; }

		/// <summary>
		/// Gets or Sets Items
		/// </summary>
		[DataMember(Name = "items", EmitDefaultValue = false)]
		public List<DashboardItem> Items { get; set; }

		/// <summary>
		/// Returns the string presentation of the object
		/// </summary>
		/// <returns>String presentation of the object</returns>
		public override string ToString()
		{
			var sb = new StringBuilder();
			sb.Append("class Dashboard {\n");
			sb.Append("  Id: ").Append(Id).Append("\n");
			sb.Append("  Name: ").Append(Name).Append("\n");
			sb.Append("  IconCss: ").Append(IconCss).Append("\n");
			sb.Append("  Position: ").Append(Position).Append("\n");
			sb.Append("  Items: ").Append(Items).Append("\n");
			sb.Append("}\n");
			return sb.ToString();
		}

		/// <summary>
		/// Returns the JSON string presentation of the object
		/// </summary>
		/// <returns>JSON string presentation of the object</returns>
		public virtual string ToJson()
		{
			return JsonConvert.SerializeObject(this, Formatting.Indented);
		}

		/// <summary>
		/// Returns true if objects are equal
		/// </summary>
		/// <param name="input">Object to be compared</param>
		/// <returns>Boolean</returns>
		public override bool Equals(object input)
		{
			return this.Equals(input as Dashboard);
		}

		/// <summary>
		/// Returns true if Dashboard instances are equal
		/// </summary>
		/// <param name="input">Instance of Dashboard to be compared</param>
		/// <returns>Boolean</returns>
		public bool Equals(Dashboard input)
		{
			if (input == null)
				return false;

			return
				(
					this.Id == input.Id ||
					(this.Id != null &&
					 this.Id.Equals(input.Id))
				) &&
				(
					this.Name == input.Name ||
					(this.Name != null &&
					 this.Name.Equals(input.Name))
				) &&
				(
					this.IconCss == input.IconCss ||
					(this.IconCss != null &&
					 this.IconCss.Equals(input.IconCss))
				) &&
				(
					this.Position == input.Position ||
					(this.Position != null &&
					 this.Position.Equals(input.Position))
				) &&
				(
					this.Items == input.Items ||
					this.Items != null &&
					input.Items != null &&
					this.Items.SequenceEqual(input.Items)
				);
		}

		/// <summary>
		/// Gets the hash code
		/// </summary>
		/// <returns>Hash code</returns>
		public override int GetHashCode()
		{
			unchecked // Overflow is fine, just wrap
			{
				int hashCode = 41;
				if (this.Id != null)
					hashCode = hashCode * 59 + this.Id.GetHashCode();
				if (this.Name != null)
					hashCode = hashCode * 59 + this.Name.GetHashCode();
				if (this.IconCss != null)
					hashCode = hashCode * 59 + this.IconCss.GetHashCode();
				if (this.Position != null)
					hashCode = hashCode * 59 + this.Position.GetHashCode();
				if (this.Items != null)
					hashCode = hashCode * 59 + this.Items.GetHashCode();
				return hashCode;
			}
		}

		/// <summary>
		/// To validate all properties of the instance
		/// </summary>
		/// <param name="validationContext">Validation context</param>
		/// <returns>Validation Result</returns>
		IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
		{
			yield break;
		}
	}
}