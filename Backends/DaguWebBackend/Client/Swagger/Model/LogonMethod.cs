/*
 * iTWOsite REST Service
 *
 * Public API Documentation
 *
 * OpenAPI spec version: 3.5.4 E
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using System;
using System.Linq;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;
using SwaggerDateConverter = Levelbuild.Backend.DaguWebBackend.Client.Swagger.Client.SwaggerDateConverter;

namespace Levelbuild.Backend.DaguWebBackend.Client.Swagger.Model
{
	/// <summary>
	/// LogonMethod
	/// </summary>
	[DataContract]
	public partial class LogonMethod : IEquatable<LogonMethod>, IValidatableObject
	{
		/// <summary>
		/// Defines Type
		/// </summary>
		[JsonConverter(typeof(StringEnumConverter))]
		public enum TypeEnum
		{
			/// <summary>
			/// Enum CREDENTIALS for value: CREDENTIALS
			/// </summary>
			[EnumMember(Value = "CREDENTIALS")]
			CREDENTIALS = 1,

			/// <summary>
			/// Enum OAUTH for value: OAUTH
			/// </summary>
			[EnumMember(Value = "OAUTH")]
			OAUTH = 2
		}

		/// <summary>
		/// Gets or Sets Type
		/// </summary>
		[DataMember(Name = "type", EmitDefaultValue = false)]
		public TypeEnum? Type { get; set; }

		/// <summary>
		/// Defines Provider
		/// </summary>
		[JsonConverter(typeof(StringEnumConverter))]
		public enum ProviderEnum
		{
			/// <summary>
			/// Enum MICROSOFT for value: MICROSOFT
			/// </summary>
			[EnumMember(Value = "MICROSOFT")]
			MICROSOFT = 1,

			/// <summary>
			/// Enum GOOGLE for value: GOOGLE
			/// </summary>
			[EnumMember(Value = "GOOGLE")]
			GOOGLE = 2
		}

		/// <summary>
		/// Gets or Sets Provider
		/// </summary>
		[DataMember(Name = "provider", EmitDefaultValue = false)]
		public ProviderEnum? Provider { get; set; }

		/// <summary>
		/// Initializes a new instance of the <see cref="LogonMethod" /> class.
		/// </summary>
		public LogonMethod()
		{
		}

		/// <summary>
		/// Gets or Sets Name
		/// </summary>
		[DataMember(Name = "name", EmitDefaultValue = false)]
		public string Name { get; private set; }


		/// <summary>
		/// Returns the string presentation of the object
		/// </summary>
		/// <returns>String presentation of the object</returns>
		public override string ToString()
		{
			var sb = new StringBuilder();
			sb.Append("class LogonMethod {\n");
			sb.Append("  Name: ").Append(Name).Append("\n");
			sb.Append("  Type: ").Append(Type).Append("\n");
			sb.Append("  Provider: ").Append(Provider).Append("\n");
			sb.Append("}\n");
			return sb.ToString();
		}

		/// <summary>
		/// Returns the JSON string presentation of the object
		/// </summary>
		/// <returns>JSON string presentation of the object</returns>
		public virtual string ToJson()
		{
			return JsonConvert.SerializeObject(this, Formatting.Indented);
		}

		/// <summary>
		/// Returns true if objects are equal
		/// </summary>
		/// <param name="input">Object to be compared</param>
		/// <returns>Boolean</returns>
		public override bool Equals(object input)
		{
			return this.Equals(input as LogonMethod);
		}

		/// <summary>
		/// Returns true if LogonMethod instances are equal
		/// </summary>
		/// <param name="input">Instance of LogonMethod to be compared</param>
		/// <returns>Boolean</returns>
		public bool Equals(LogonMethod input)
		{
			if (input == null)
				return false;

			return
				(
					this.Name == input.Name ||
					(this.Name != null &&
					 this.Name.Equals(input.Name))
				) &&
				(
					this.Type == input.Type ||
					(this.Type != null &&
					 this.Type.Equals(input.Type))
				) &&
				(
					this.Provider == input.Provider ||
					(this.Provider != null &&
					 this.Provider.Equals(input.Provider))
				);
		}

		/// <summary>
		/// Gets the hash code
		/// </summary>
		/// <returns>Hash code</returns>
		public override int GetHashCode()
		{
			unchecked // Overflow is fine, just wrap
			{
				int hashCode = 41;
				if (this.Name != null)
					hashCode = hashCode * 59 + this.Name.GetHashCode();
				if (this.Type != null)
					hashCode = hashCode * 59 + this.Type.GetHashCode();
				if (this.Provider != null)
					hashCode = hashCode * 59 + this.Provider.GetHashCode();
				return hashCode;
			}
		}

		/// <summary>
		/// To validate all properties of the instance
		/// </summary>
		/// <param name="validationContext">Validation context</param>
		/// <returns>Validation Result</returns>
		IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
		{
			yield break;
		}
	}
}