/*
 * iTWOsite REST Service
 *
 * Public API Documentation
 *
 * OpenAPI spec version: 3.5.4 E
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using System;
using System.Linq;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;
using SwaggerDateConverter = Levelbuild.Backend.DaguWebBackend.Client.Swagger.Client.SwaggerDateConverter;

namespace Levelbuild.Backend.DaguWebBackend.Client.Swagger.Model
{
	/// <summary>
	/// SyncPreview
	/// </summary>
	[DataContract]
	public partial class SyncPreview : IEquatable<SyncPreview>, IValidatableObject
	{
		/// <summary>
		/// Initializes a new instance of the <see cref="SyncPreview" /> class.
		/// </summary>
		/// <param name="error">error.</param>
		public SyncPreview(WebError error = default(WebError))
		{
			this.Error = error;
		}

		/// <summary>
		/// Gets or Sets DefinitionId
		/// </summary>
		[DataMember(Name = "definitionId", EmitDefaultValue = false)]
		public long? DefinitionId { get; private set; }

		/// <summary>
		/// Gets or Sets Items
		/// </summary>
		[DataMember(Name = "items", EmitDefaultValue = false)]
		public int? Items { get; private set; }

		/// <summary>
		/// Gets or Sets LastChanged
		/// </summary>
		[DataMember(Name = "lastChanged", EmitDefaultValue = false)]
		public DateTime? LastChanged { get; private set; }

		/// <summary>
		/// Gets or Sets RequestTime
		/// </summary>
		[DataMember(Name = "requestTime", EmitDefaultValue = false)]
		public long? RequestTime { get; private set; }

		/// <summary>
		/// Gets or Sets Error
		/// </summary>
		[DataMember(Name = "error", EmitDefaultValue = false)]
		public WebError Error { get; set; }

		/// <summary>
		/// Returns the string presentation of the object
		/// </summary>
		/// <returns>String presentation of the object</returns>
		public override string ToString()
		{
			var sb = new StringBuilder();
			sb.Append("class SyncPreview {\n");
			sb.Append("  DefinitionId: ").Append(DefinitionId).Append("\n");
			sb.Append("  Items: ").Append(Items).Append("\n");
			sb.Append("  LastChanged: ").Append(LastChanged).Append("\n");
			sb.Append("  RequestTime: ").Append(RequestTime).Append("\n");
			sb.Append("  Error: ").Append(Error).Append("\n");
			sb.Append("}\n");
			return sb.ToString();
		}

		/// <summary>
		/// Returns the JSON string presentation of the object
		/// </summary>
		/// <returns>JSON string presentation of the object</returns>
		public virtual string ToJson()
		{
			return JsonConvert.SerializeObject(this, Formatting.Indented);
		}

		/// <summary>
		/// Returns true if objects are equal
		/// </summary>
		/// <param name="input">Object to be compared</param>
		/// <returns>Boolean</returns>
		public override bool Equals(object input)
		{
			return this.Equals(input as SyncPreview);
		}

		/// <summary>
		/// Returns true if SyncPreview instances are equal
		/// </summary>
		/// <param name="input">Instance of SyncPreview to be compared</param>
		/// <returns>Boolean</returns>
		public bool Equals(SyncPreview input)
		{
			if (input == null)
				return false;

			return
				(
					this.DefinitionId == input.DefinitionId ||
					(this.DefinitionId != null &&
					 this.DefinitionId.Equals(input.DefinitionId))
				) &&
				(
					this.Items == input.Items ||
					(this.Items != null &&
					 this.Items.Equals(input.Items))
				) &&
				(
					this.LastChanged == input.LastChanged ||
					(this.LastChanged != null &&
					 this.LastChanged.Equals(input.LastChanged))
				) &&
				(
					this.RequestTime == input.RequestTime ||
					(this.RequestTime != null &&
					 this.RequestTime.Equals(input.RequestTime))
				) &&
				(
					this.Error == input.Error ||
					(this.Error != null &&
					 this.Error.Equals(input.Error))
				);
		}

		/// <summary>
		/// Gets the hash code
		/// </summary>
		/// <returns>Hash code</returns>
		public override int GetHashCode()
		{
			unchecked // Overflow is fine, just wrap
			{
				int hashCode = 41;
				if (this.DefinitionId != null)
					hashCode = hashCode * 59 + this.DefinitionId.GetHashCode();
				if (this.Items != null)
					hashCode = hashCode * 59 + this.Items.GetHashCode();
				if (this.LastChanged != null)
					hashCode = hashCode * 59 + this.LastChanged.GetHashCode();
				if (this.RequestTime != null)
					hashCode = hashCode * 59 + this.RequestTime.GetHashCode();
				if (this.Error != null)
					hashCode = hashCode * 59 + this.Error.GetHashCode();
				return hashCode;
			}
		}

		/// <summary>
		/// To validate all properties of the instance
		/// </summary>
		/// <param name="validationContext">Validation context</param>
		/// <returns>Validation Result</returns>
		IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
		{
			yield break;
		}
	}
}