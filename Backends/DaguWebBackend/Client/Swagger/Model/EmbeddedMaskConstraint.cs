/*
 * iTWOsite REST Service
 *
 * Public API Documentation
 *
 * OpenAPI spec version: 3.5.4 E
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using System;
using System.Linq;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;
using SwaggerDateConverter = Levelbuild.Backend.DaguWebBackend.Client.Swagger.Client.SwaggerDateConverter;

namespace Levelbuild.Backend.DaguWebBackend.Client.Swagger.Model
{
	/// <summary>
	/// EmbeddedMaskConstraint
	/// </summary>
	[DataContract]
	public partial class EmbeddedMaskConstraint : IEquatable<EmbeddedMaskConstraint>, IValidatableObject
	{
		/// <summary>
		/// Defines CompareType
		/// </summary>
		[JsonConverter(typeof(StringEnumConverter))]
		public enum CompareTypeEnum
		{
			/// <summary>
			/// Enum EQUAL for value: EQUAL
			/// </summary>
			[EnumMember(Value = "EQUAL")]
			EQUAL = 1,

			/// <summary>
			/// Enum NOTEQUAL for value: NOTEQUAL
			/// </summary>
			[EnumMember(Value = "NOTEQUAL")]
			NOTEQUAL = 2,

			/// <summary>
			/// Enum LESSTHAN for value: LESSTHAN
			/// </summary>
			[EnumMember(Value = "LESSTHAN")]
			LESSTHAN = 3,

			/// <summary>
			/// Enum LESSTHANEQUALS for value: LESSTHANEQUALS
			/// </summary>
			[EnumMember(Value = "LESSTHANEQUALS")]
			LESSTHANEQUALS = 4,

			/// <summary>
			/// Enum GREATERTHAN for value: GREATERTHAN
			/// </summary>
			[EnumMember(Value = "GREATERTHAN")]
			GREATERTHAN = 5,

			/// <summary>
			/// Enum GREATERTHANEQUALS for value: GREATERTHANEQUALS
			/// </summary>
			[EnumMember(Value = "GREATERTHANEQUALS")]
			GREATERTHANEQUALS = 6,

			/// <summary>
			/// Enum LIKE for value: LIKE
			/// </summary>
			[EnumMember(Value = "LIKE")]
			LIKE = 7,

			/// <summary>
			/// Enum NOTLIKE for value: NOTLIKE
			/// </summary>
			[EnumMember(Value = "NOTLIKE")]
			NOTLIKE = 8,

			/// <summary>
			/// Enum ILIKE for value: ILIKE
			/// </summary>
			[EnumMember(Value = "ILIKE")]
			ILIKE = 9,

			/// <summary>
			/// Enum NOTILIKE for value: NOTILIKE
			/// </summary>
			[EnumMember(Value = "NOTILIKE")]
			NOTILIKE = 10,

			/// <summary>
			/// Enum ISNULL for value: ISNULL
			/// </summary>
			[EnumMember(Value = "ISNULL")]
			ISNULL = 11,

			/// <summary>
			/// Enum ISNOTNULL for value: ISNOTNULL
			/// </summary>
			[EnumMember(Value = "ISNOTNULL")]
			ISNOTNULL = 12,

			/// <summary>
			/// Enum BETWEEN for value: BETWEEN
			/// </summary>
			[EnumMember(Value = "BETWEEN")]
			BETWEEN = 13,

			/// <summary>
			/// Enum UNIQUE for value: UNIQUE
			/// </summary>
			[EnumMember(Value = "UNIQUE")]
			UNIQUE = 14,

			/// <summary>
			/// Enum NOTUNIQUE for value: NOTUNIQUE
			/// </summary>
			[EnumMember(Value = "NOTUNIQUE")]
			NOTUNIQUE = 15,

			/// <summary>
			/// Enum CHANGEDLOCAL for value: CHANGEDLOCAL
			/// </summary>
			[EnumMember(Value = "CHANGEDLOCAL")]
			CHANGEDLOCAL = 16,

			/// <summary>
			/// Enum CHANGEDGLOBAL for value: CHANGEDGLOBAL
			/// </summary>
			[EnumMember(Value = "CHANGEDGLOBAL")]
			CHANGEDGLOBAL = 17,

			/// <summary>
			/// Enum IN for value: IN
			/// </summary>
			[EnumMember(Value = "IN")]
			IN = 18,

			/// <summary>
			/// Enum NOTIN for value: NOTIN
			/// </summary>
			[EnumMember(Value = "NOTIN")]
			NOTIN = 19,

			/// <summary>
			/// Enum CONTAINS for value: CONTAINS
			/// </summary>
			[EnumMember(Value = "CONTAINS")]
			CONTAINS = 20,

			/// <summary>
			/// Enum EQUALSIGNORECASE for value: EQUALSIGNORECASE
			/// </summary>
			[EnumMember(Value = "EQUALSIGNORECASE")]
			EQUALSIGNORECASE = 21,

			/// <summary>
			/// Enum STARTSWITH for value: STARTSWITH
			/// </summary>
			[EnumMember(Value = "STARTSWITH")]
			STARTSWITH = 22,

			/// <summary>
			/// Enum ENDSWITH for value: ENDSWITH
			/// </summary>
			[EnumMember(Value = "ENDSWITH")]
			ENDSWITH = 23,

			/// <summary>
			/// Enum CHANGEDBYUSER for value: CHANGEDBYUSER
			/// </summary>
			[EnumMember(Value = "CHANGEDBYUSER")]
			CHANGEDBYUSER = 24,

			/// <summary>
			/// Enum NOTSTARTSWITH for value: NOTSTARTSWITH
			/// </summary>
			[EnumMember(Value = "NOTSTARTSWITH")]
			NOTSTARTSWITH = 25,

			/// <summary>
			/// Enum NOTENDSWITH for value: NOTENDSWITH
			/// </summary>
			[EnumMember(Value = "NOTENDSWITH")]
			NOTENDSWITH = 26,

			/// <summary>
			/// Enum EXISTS for value: EXISTS
			/// </summary>
			[EnumMember(Value = "EXISTS")]
			EXISTS = 27,

			/// <summary>
			/// Enum NOTEXISTS for value: NOTEXISTS
			/// </summary>
			[EnumMember(Value = "NOTEXISTS")]
			NOTEXISTS = 28,

			/// <summary>
			/// Enum SOUNDEX for value: SOUNDEX
			/// </summary>
			[EnumMember(Value = "SOUNDEX")]
			SOUNDEX = 29,

			/// <summary>
			/// Enum NOTBETWEEN for value: NOTBETWEEN
			/// </summary>
			[EnumMember(Value = "NOTBETWEEN")]
			NOTBETWEEN = 30
		}

		/// <summary>
		/// Gets or Sets CompareType
		/// </summary>
		[DataMember(Name = "compareType", EmitDefaultValue = false)]
		public CompareTypeEnum? CompareType { get; set; }

		/// <summary>
		/// Initializes a new instance of the <see cref="EmbeddedMaskConstraint" /> class.
		/// </summary>
		/// <param name="embeddedFieldId">embeddedFieldId.</param>
		/// <param name="compareType">compareType.</param>
		/// <param name="parentFieldId">parentFieldId.</param>
		/// <param name="compareValue">compareValue.</param>
		/// <param name="filterGroup">filterGroup.</param>
		/// <param name="liveUpdate">liveUpdate.</param>
		/// <param name="filterEmpty">filterEmpty.</param>
		public EmbeddedMaskConstraint(long? embeddedFieldId = default(long?), CompareTypeEnum? compareType = default(CompareTypeEnum?),
									  long? parentFieldId = default(long?), string compareValue = default(string), int? filterGroup = default(int?),
									  bool? liveUpdate = default(bool?), bool? filterEmpty = default(bool?))
		{
			this.EmbeddedFieldId = embeddedFieldId;
			this.CompareType = compareType;
			this.ParentFieldId = parentFieldId;
			this.CompareValue = compareValue;
			this.FilterGroup = filterGroup;
			this.LiveUpdate = liveUpdate;
			this.FilterEmpty = filterEmpty;
		}

		/// <summary>
		/// Gets or Sets Id
		/// </summary>
		[DataMember(Name = "id", EmitDefaultValue = false)]
		public long? Id { get; private set; }

		/// <summary>
		/// Gets or Sets EmbeddedFieldId
		/// </summary>
		[DataMember(Name = "embeddedFieldId", EmitDefaultValue = false)]
		public long? EmbeddedFieldId { get; set; }


		/// <summary>
		/// Gets or Sets ParentFieldId
		/// </summary>
		[DataMember(Name = "parentFieldId", EmitDefaultValue = false)]
		public long? ParentFieldId { get; set; }

		/// <summary>
		/// Gets or Sets CompareValue
		/// </summary>
		[DataMember(Name = "compareValue", EmitDefaultValue = false)]
		public string CompareValue { get; set; }

		/// <summary>
		/// Gets or Sets FilterGroup
		/// </summary>
		[DataMember(Name = "filterGroup", EmitDefaultValue = false)]
		public int? FilterGroup { get; set; }

		/// <summary>
		/// Gets or Sets LiveUpdate
		/// </summary>
		[DataMember(Name = "liveUpdate", EmitDefaultValue = false)]
		public bool? LiveUpdate { get; set; }

		/// <summary>
		/// Gets or Sets FilterEmpty
		/// </summary>
		[DataMember(Name = "filterEmpty", EmitDefaultValue = false)]
		public bool? FilterEmpty { get; set; }

		/// <summary>
		/// Returns the string presentation of the object
		/// </summary>
		/// <returns>String presentation of the object</returns>
		public override string ToString()
		{
			var sb = new StringBuilder();
			sb.Append("class EmbeddedMaskConstraint {\n");
			sb.Append("  Id: ").Append(Id).Append("\n");
			sb.Append("  EmbeddedFieldId: ").Append(EmbeddedFieldId).Append("\n");
			sb.Append("  CompareType: ").Append(CompareType).Append("\n");
			sb.Append("  ParentFieldId: ").Append(ParentFieldId).Append("\n");
			sb.Append("  CompareValue: ").Append(CompareValue).Append("\n");
			sb.Append("  FilterGroup: ").Append(FilterGroup).Append("\n");
			sb.Append("  LiveUpdate: ").Append(LiveUpdate).Append("\n");
			sb.Append("  FilterEmpty: ").Append(FilterEmpty).Append("\n");
			sb.Append("}\n");
			return sb.ToString();
		}

		/// <summary>
		/// Returns the JSON string presentation of the object
		/// </summary>
		/// <returns>JSON string presentation of the object</returns>
		public virtual string ToJson()
		{
			return JsonConvert.SerializeObject(this, Formatting.Indented);
		}

		/// <summary>
		/// Returns true if objects are equal
		/// </summary>
		/// <param name="input">Object to be compared</param>
		/// <returns>Boolean</returns>
		public override bool Equals(object input)
		{
			return this.Equals(input as EmbeddedMaskConstraint);
		}

		/// <summary>
		/// Returns true if EmbeddedMaskConstraint instances are equal
		/// </summary>
		/// <param name="input">Instance of EmbeddedMaskConstraint to be compared</param>
		/// <returns>Boolean</returns>
		public bool Equals(EmbeddedMaskConstraint input)
		{
			if (input == null)
				return false;

			return
				(
					this.Id == input.Id ||
					(this.Id != null &&
					 this.Id.Equals(input.Id))
				) &&
				(
					this.EmbeddedFieldId == input.EmbeddedFieldId ||
					(this.EmbeddedFieldId != null &&
					 this.EmbeddedFieldId.Equals(input.EmbeddedFieldId))
				) &&
				(
					this.CompareType == input.CompareType ||
					(this.CompareType != null &&
					 this.CompareType.Equals(input.CompareType))
				) &&
				(
					this.ParentFieldId == input.ParentFieldId ||
					(this.ParentFieldId != null &&
					 this.ParentFieldId.Equals(input.ParentFieldId))
				) &&
				(
					this.CompareValue == input.CompareValue ||
					(this.CompareValue != null &&
					 this.CompareValue.Equals(input.CompareValue))
				) &&
				(
					this.FilterGroup == input.FilterGroup ||
					(this.FilterGroup != null &&
					 this.FilterGroup.Equals(input.FilterGroup))
				) &&
				(
					this.LiveUpdate == input.LiveUpdate ||
					(this.LiveUpdate != null &&
					 this.LiveUpdate.Equals(input.LiveUpdate))
				) &&
				(
					this.FilterEmpty == input.FilterEmpty ||
					(this.FilterEmpty != null &&
					 this.FilterEmpty.Equals(input.FilterEmpty))
				);
		}

		/// <summary>
		/// Gets the hash code
		/// </summary>
		/// <returns>Hash code</returns>
		public override int GetHashCode()
		{
			unchecked // Overflow is fine, just wrap
			{
				int hashCode = 41;
				if (this.Id != null)
					hashCode = hashCode * 59 + this.Id.GetHashCode();
				if (this.EmbeddedFieldId != null)
					hashCode = hashCode * 59 + this.EmbeddedFieldId.GetHashCode();
				if (this.CompareType != null)
					hashCode = hashCode * 59 + this.CompareType.GetHashCode();
				if (this.ParentFieldId != null)
					hashCode = hashCode * 59 + this.ParentFieldId.GetHashCode();
				if (this.CompareValue != null)
					hashCode = hashCode * 59 + this.CompareValue.GetHashCode();
				if (this.FilterGroup != null)
					hashCode = hashCode * 59 + this.FilterGroup.GetHashCode();
				if (this.LiveUpdate != null)
					hashCode = hashCode * 59 + this.LiveUpdate.GetHashCode();
				if (this.FilterEmpty != null)
					hashCode = hashCode * 59 + this.FilterEmpty.GetHashCode();
				return hashCode;
			}
		}

		/// <summary>
		/// To validate all properties of the instance
		/// </summary>
		/// <param name="validationContext">Validation context</param>
		/// <returns>Validation Result</returns>
		IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
		{
			yield break;
		}
	}
}