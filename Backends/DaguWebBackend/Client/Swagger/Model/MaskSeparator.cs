/*
 * iTWOsite REST Service
 *
 * Public API Documentation
 *
 * OpenAPI spec version: 3.5.4 E
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using System;
using System.Linq;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;
using SwaggerDateConverter = Levelbuild.Backend.DaguWebBackend.Client.Swagger.Client.SwaggerDateConverter;

namespace Levelbuild.Backend.DaguWebBackend.Client.Swagger.Model
{
	/// <summary>
	/// MaskSeparator
	/// </summary>
	[DataContract]
	public partial class MaskSeparator : IEquatable<MaskSeparator>, IValidatableObject
	{
		/// <summary>
		/// Defines TextAlign
		/// </summary>
		[JsonConverter(typeof(StringEnumConverter))]
		public enum TextAlignEnum
		{
			/// <summary>
			/// Enum LEFT for value: LEFT
			/// </summary>
			[EnumMember(Value = "LEFT")]
			LEFT = 1,

			/// <summary>
			/// Enum RIGHT for value: RIGHT
			/// </summary>
			[EnumMember(Value = "RIGHT")]
			RIGHT = 2,

			/// <summary>
			/// Enum CENTER for value: CENTER
			/// </summary>
			[EnumMember(Value = "CENTER")]
			CENTER = 3,

			/// <summary>
			/// Enum TOP for value: TOP
			/// </summary>
			[EnumMember(Value = "TOP")]
			TOP = 4,

			/// <summary>
			/// Enum BOTTOM for value: BOTTOM
			/// </summary>
			[EnumMember(Value = "BOTTOM")]
			BOTTOM = 5,

			/// <summary>
			/// Enum JUSTIFY for value: JUSTIFY
			/// </summary>
			[EnumMember(Value = "JUSTIFY")]
			JUSTIFY = 6,

			/// <summary>
			/// Enum MIDDLE for value: MIDDLE
			/// </summary>
			[EnumMember(Value = "MIDDLE")]
			MIDDLE = 7
		}

		/// <summary>
		/// Gets or Sets TextAlign
		/// </summary>
		[DataMember(Name = "textAlign", EmitDefaultValue = false)]
		public TextAlignEnum? TextAlign { get; set; }

		/// <summary>
		/// Initializes a new instance of the <see cref="MaskSeparator" /> class.
		/// </summary>
		/// <param name="label">label.</param>
		/// <param name="maskRow">maskRow.</param>
		/// <param name="showLabel">showLabel.</param>
		/// <param name="allowMinimize">allowMinimize.</param>
		/// <param name="startMinimized">startMinimized.</param>
		/// <param name="textAlign">textAlign.</param>
		/// <param name="hidden">hidden.</param>
		/// <param name="color">color.</param>
		/// <param name="cellColor">cellColor.</param>
		/// <param name="separatorColor">separatorColor.</param>
		public MaskSeparator(string label = default(string), int? maskRow = default(int?), bool? showLabel = default(bool?),
							 bool? allowMinimize = default(bool?), bool? startMinimized = default(bool?), TextAlignEnum? textAlign = default(TextAlignEnum?),
							 bool? hidden = default(bool?), string color = default(string), string cellColor = default(string),
							 string separatorColor = default(string))
		{
			this.Label = label;
			this.MaskRow = maskRow;
			this.ShowLabel = showLabel;
			this.AllowMinimize = allowMinimize;
			this.StartMinimized = startMinimized;
			this.TextAlign = textAlign;
			this.Hidden = hidden;
			this.Color = color;
			this.CellColor = cellColor;
			this.SeparatorColor = separatorColor;
		}

		/// <summary>
		/// Gets or Sets Id
		/// </summary>
		[DataMember(Name = "id", EmitDefaultValue = false)]
		public long? Id { get; private set; }

		/// <summary>
		/// Gets or Sets Label
		/// </summary>
		[DataMember(Name = "label", EmitDefaultValue = false)]
		public string Label { get; set; }

		/// <summary>
		/// Gets or Sets MaskRow
		/// </summary>
		[DataMember(Name = "maskRow", EmitDefaultValue = false)]
		public int? MaskRow { get; set; }

		/// <summary>
		/// Gets or Sets ShowLabel
		/// </summary>
		[DataMember(Name = "showLabel", EmitDefaultValue = false)]
		public bool? ShowLabel { get; set; }

		/// <summary>
		/// Gets or Sets AllowMinimize
		/// </summary>
		[DataMember(Name = "allowMinimize", EmitDefaultValue = false)]
		public bool? AllowMinimize { get; set; }

		/// <summary>
		/// Gets or Sets StartMinimized
		/// </summary>
		[DataMember(Name = "startMinimized", EmitDefaultValue = false)]
		public bool? StartMinimized { get; set; }


		/// <summary>
		/// Gets or Sets Hidden
		/// </summary>
		[DataMember(Name = "hidden", EmitDefaultValue = false)]
		public bool? Hidden { get; set; }

		/// <summary>
		/// Gets or Sets Color
		/// </summary>
		[DataMember(Name = "color", EmitDefaultValue = false)]
		public string Color { get; set; }

		/// <summary>
		/// Gets or Sets CellColor
		/// </summary>
		[DataMember(Name = "cellColor", EmitDefaultValue = false)]
		public string CellColor { get; set; }

		/// <summary>
		/// Gets or Sets SeparatorColor
		/// </summary>
		[DataMember(Name = "separatorColor", EmitDefaultValue = false)]
		public string SeparatorColor { get; set; }

		/// <summary>
		/// Returns the string presentation of the object
		/// </summary>
		/// <returns>String presentation of the object</returns>
		public override string ToString()
		{
			var sb = new StringBuilder();
			sb.Append("class MaskSeparator {\n");
			sb.Append("  Id: ").Append(Id).Append("\n");
			sb.Append("  Label: ").Append(Label).Append("\n");
			sb.Append("  MaskRow: ").Append(MaskRow).Append("\n");
			sb.Append("  ShowLabel: ").Append(ShowLabel).Append("\n");
			sb.Append("  AllowMinimize: ").Append(AllowMinimize).Append("\n");
			sb.Append("  StartMinimized: ").Append(StartMinimized).Append("\n");
			sb.Append("  TextAlign: ").Append(TextAlign).Append("\n");
			sb.Append("  Hidden: ").Append(Hidden).Append("\n");
			sb.Append("  Color: ").Append(Color).Append("\n");
			sb.Append("  CellColor: ").Append(CellColor).Append("\n");
			sb.Append("  SeparatorColor: ").Append(SeparatorColor).Append("\n");
			sb.Append("}\n");
			return sb.ToString();
		}

		/// <summary>
		/// Returns the JSON string presentation of the object
		/// </summary>
		/// <returns>JSON string presentation of the object</returns>
		public virtual string ToJson()
		{
			return JsonConvert.SerializeObject(this, Formatting.Indented);
		}

		/// <summary>
		/// Returns true if objects are equal
		/// </summary>
		/// <param name="input">Object to be compared</param>
		/// <returns>Boolean</returns>
		public override bool Equals(object input)
		{
			return this.Equals(input as MaskSeparator);
		}

		/// <summary>
		/// Returns true if MaskSeparator instances are equal
		/// </summary>
		/// <param name="input">Instance of MaskSeparator to be compared</param>
		/// <returns>Boolean</returns>
		public bool Equals(MaskSeparator input)
		{
			if (input == null)
				return false;

			return
				(
					this.Id == input.Id ||
					(this.Id != null &&
					 this.Id.Equals(input.Id))
				) &&
				(
					this.Label == input.Label ||
					(this.Label != null &&
					 this.Label.Equals(input.Label))
				) &&
				(
					this.MaskRow == input.MaskRow ||
					(this.MaskRow != null &&
					 this.MaskRow.Equals(input.MaskRow))
				) &&
				(
					this.ShowLabel == input.ShowLabel ||
					(this.ShowLabel != null &&
					 this.ShowLabel.Equals(input.ShowLabel))
				) &&
				(
					this.AllowMinimize == input.AllowMinimize ||
					(this.AllowMinimize != null &&
					 this.AllowMinimize.Equals(input.AllowMinimize))
				) &&
				(
					this.StartMinimized == input.StartMinimized ||
					(this.StartMinimized != null &&
					 this.StartMinimized.Equals(input.StartMinimized))
				) &&
				(
					this.TextAlign == input.TextAlign ||
					(this.TextAlign != null &&
					 this.TextAlign.Equals(input.TextAlign))
				) &&
				(
					this.Hidden == input.Hidden ||
					(this.Hidden != null &&
					 this.Hidden.Equals(input.Hidden))
				) &&
				(
					this.Color == input.Color ||
					(this.Color != null &&
					 this.Color.Equals(input.Color))
				) &&
				(
					this.CellColor == input.CellColor ||
					(this.CellColor != null &&
					 this.CellColor.Equals(input.CellColor))
				) &&
				(
					this.SeparatorColor == input.SeparatorColor ||
					(this.SeparatorColor != null &&
					 this.SeparatorColor.Equals(input.SeparatorColor))
				);
		}

		/// <summary>
		/// Gets the hash code
		/// </summary>
		/// <returns>Hash code</returns>
		public override int GetHashCode()
		{
			unchecked // Overflow is fine, just wrap
			{
				int hashCode = 41;
				if (this.Id != null)
					hashCode = hashCode * 59 + this.Id.GetHashCode();
				if (this.Label != null)
					hashCode = hashCode * 59 + this.Label.GetHashCode();
				if (this.MaskRow != null)
					hashCode = hashCode * 59 + this.MaskRow.GetHashCode();
				if (this.ShowLabel != null)
					hashCode = hashCode * 59 + this.ShowLabel.GetHashCode();
				if (this.AllowMinimize != null)
					hashCode = hashCode * 59 + this.AllowMinimize.GetHashCode();
				if (this.StartMinimized != null)
					hashCode = hashCode * 59 + this.StartMinimized.GetHashCode();
				if (this.TextAlign != null)
					hashCode = hashCode * 59 + this.TextAlign.GetHashCode();
				if (this.Hidden != null)
					hashCode = hashCode * 59 + this.Hidden.GetHashCode();
				if (this.Color != null)
					hashCode = hashCode * 59 + this.Color.GetHashCode();
				if (this.CellColor != null)
					hashCode = hashCode * 59 + this.CellColor.GetHashCode();
				if (this.SeparatorColor != null)
					hashCode = hashCode * 59 + this.SeparatorColor.GetHashCode();
				return hashCode;
			}
		}

		/// <summary>
		/// To validate all properties of the instance
		/// </summary>
		/// <param name="validationContext">Validation context</param>
		/// <returns>Validation Result</returns>
		IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
		{
			yield break;
		}
	}
}