/*
 * iTWOsite REST Service
 *
 * Public API Documentation
 *
 * OpenAPI spec version: 3.5.4 E
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using System;
using System.Linq;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;
using SwaggerDateConverter = Levelbuild.Backend.DaguWebBackend.Client.Swagger.Client.SwaggerDateConverter;

namespace Levelbuild.Backend.DaguWebBackend.Client.Swagger.Model
{
	/// <summary>
	/// ElementActionResponse
	/// </summary>
	[DataContract]
	public partial class ElementActionResponse : IEquatable<ElementActionResponse>, IValidatableObject
	{
		/// <summary>
		/// Initializes a new instance of the <see cref="ElementActionResponse" /> class.
		/// </summary>
		/// <param name="error">error.</param>
		public ElementActionResponse(WebError error = default(WebError))
		{
			this.Error = error;
		}

		/// <summary>
		/// Gets or Sets ElementId
		/// </summary>
		[DataMember(Name = "elementId", EmitDefaultValue = false)]
		public string ElementId { get; private set; }

		/// <summary>
		/// Gets or Sets Success
		/// </summary>
		[DataMember(Name = "success", EmitDefaultValue = false)]
		public bool? Success { get; private set; }

		/// <summary>
		/// Gets or Sets Error
		/// </summary>
		[DataMember(Name = "error", EmitDefaultValue = false)]
		public WebError Error { get; set; }

		/// <summary>
		/// Returns the string presentation of the object
		/// </summary>
		/// <returns>String presentation of the object</returns>
		public override string ToString()
		{
			var sb = new StringBuilder();
			sb.Append("class ElementActionResponse {\n");
			sb.Append("  ElementId: ").Append(ElementId).Append("\n");
			sb.Append("  Success: ").Append(Success).Append("\n");
			sb.Append("  Error: ").Append(Error).Append("\n");
			sb.Append("}\n");
			return sb.ToString();
		}

		/// <summary>
		/// Returns the JSON string presentation of the object
		/// </summary>
		/// <returns>JSON string presentation of the object</returns>
		public virtual string ToJson()
		{
			return JsonConvert.SerializeObject(this, Formatting.Indented);
		}

		/// <summary>
		/// Returns true if objects are equal
		/// </summary>
		/// <param name="input">Object to be compared</param>
		/// <returns>Boolean</returns>
		public override bool Equals(object input)
		{
			return this.Equals(input as ElementActionResponse);
		}

		/// <summary>
		/// Returns true if ElementActionResponse instances are equal
		/// </summary>
		/// <param name="input">Instance of ElementActionResponse to be compared</param>
		/// <returns>Boolean</returns>
		public bool Equals(ElementActionResponse input)
		{
			if (input == null)
				return false;

			return
				(
					this.ElementId == input.ElementId ||
					(this.ElementId != null &&
					 this.ElementId.Equals(input.ElementId))
				) &&
				(
					this.Success == input.Success ||
					(this.Success != null &&
					 this.Success.Equals(input.Success))
				) &&
				(
					this.Error == input.Error ||
					(this.Error != null &&
					 this.Error.Equals(input.Error))
				);
		}

		/// <summary>
		/// Gets the hash code
		/// </summary>
		/// <returns>Hash code</returns>
		public override int GetHashCode()
		{
			unchecked // Overflow is fine, just wrap
			{
				int hashCode = 41;
				if (this.ElementId != null)
					hashCode = hashCode * 59 + this.ElementId.GetHashCode();
				if (this.Success != null)
					hashCode = hashCode * 59 + this.Success.GetHashCode();
				if (this.Error != null)
					hashCode = hashCode * 59 + this.Error.GetHashCode();
				return hashCode;
			}
		}

		/// <summary>
		/// To validate all properties of the instance
		/// </summary>
		/// <param name="validationContext">Validation context</param>
		/// <returns>Validation Result</returns>
		IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
		{
			yield break;
		}
	}
}