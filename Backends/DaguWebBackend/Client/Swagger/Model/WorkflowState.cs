/*
 * iTWOsite REST Service
 *
 * Public API Documentation
 *
 * OpenAPI spec version: 3.5.4 E
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using System;
using System.Linq;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;
using SwaggerDateConverter = Levelbuild.Backend.DaguWebBackend.Client.Swagger.Client.SwaggerDateConverter;

namespace Levelbuild.Backend.DaguWebBackend.Client.Swagger.Model
{
	/// <summary>
	/// WorkflowState
	/// </summary>
	[DataContract]
	public partial class WorkflowState : IEquatable<WorkflowState>, IValidatableObject
	{
		/// <summary>
		/// Initializes a new instance of the <see cref="WorkflowState" /> class.
		/// </summary>
		/// <param name="name">name.</param>
		/// <param name="keyValue">keyValue.</param>
		/// <param name="inactive">inactive.</param>
		/// <param name="escalated">escalated.</param>
		/// <param name="finished">finished.</param>
		/// <param name="iconCss">iconCss.</param>
		/// <param name="iconColor">iconColor.</param>
		/// <param name="actions">actions.</param>
		public WorkflowState(string name = default(string), string keyValue = default(string), bool? inactive = default(bool?),
							 bool? escalated = default(bool?), bool? finished = default(bool?), string iconCss = default(string),
							 string iconColor = default(string), List<WorkflowAction> actions = default(List<WorkflowAction>))
		{
			this.Name = name;
			this.KeyValue = keyValue;
			this.Inactive = inactive;
			this.Escalated = escalated;
			this.Finished = finished;
			this.IconCss = iconCss;
			this.IconColor = iconColor;
			this.Actions = actions;
		}

		/// <summary>
		/// Gets or Sets Id
		/// </summary>
		[DataMember(Name = "id", EmitDefaultValue = false)]
		public long? Id { get; private set; }

		/// <summary>
		/// Gets or Sets Name
		/// </summary>
		[DataMember(Name = "name", EmitDefaultValue = false)]
		public string Name { get; set; }

		/// <summary>
		/// Gets or Sets KeyValue
		/// </summary>
		[DataMember(Name = "keyValue", EmitDefaultValue = false)]
		public string KeyValue { get; set; }

		/// <summary>
		/// Gets or Sets Inactive
		/// </summary>
		[DataMember(Name = "inactive", EmitDefaultValue = false)]
		public bool? Inactive { get; set; }

		/// <summary>
		/// Gets or Sets Escalated
		/// </summary>
		[DataMember(Name = "escalated", EmitDefaultValue = false)]
		public bool? Escalated { get; set; }

		/// <summary>
		/// Gets or Sets Finished
		/// </summary>
		[DataMember(Name = "finished", EmitDefaultValue = false)]
		public bool? Finished { get; set; }

		/// <summary>
		/// Gets or Sets IconCss
		/// </summary>
		[DataMember(Name = "iconCss", EmitDefaultValue = false)]
		public string IconCss { get; set; }

		/// <summary>
		/// Gets or Sets IconColor
		/// </summary>
		[DataMember(Name = "iconColor", EmitDefaultValue = false)]
		public string IconColor { get; set; }

		/// <summary>
		/// Gets or Sets Actions
		/// </summary>
		[DataMember(Name = "actions", EmitDefaultValue = false)]
		public List<WorkflowAction> Actions { get; set; }

		/// <summary>
		/// Returns the string presentation of the object
		/// </summary>
		/// <returns>String presentation of the object</returns>
		public override string ToString()
		{
			var sb = new StringBuilder();
			sb.Append("class WorkflowState {\n");
			sb.Append("  Id: ").Append(Id).Append("\n");
			sb.Append("  Name: ").Append(Name).Append("\n");
			sb.Append("  KeyValue: ").Append(KeyValue).Append("\n");
			sb.Append("  Inactive: ").Append(Inactive).Append("\n");
			sb.Append("  Escalated: ").Append(Escalated).Append("\n");
			sb.Append("  Finished: ").Append(Finished).Append("\n");
			sb.Append("  IconCss: ").Append(IconCss).Append("\n");
			sb.Append("  IconColor: ").Append(IconColor).Append("\n");
			sb.Append("  Actions: ").Append(Actions).Append("\n");
			sb.Append("}\n");
			return sb.ToString();
		}

		/// <summary>
		/// Returns the JSON string presentation of the object
		/// </summary>
		/// <returns>JSON string presentation of the object</returns>
		public virtual string ToJson()
		{
			return JsonConvert.SerializeObject(this, Formatting.Indented);
		}

		/// <summary>
		/// Returns true if objects are equal
		/// </summary>
		/// <param name="input">Object to be compared</param>
		/// <returns>Boolean</returns>
		public override bool Equals(object input)
		{
			return this.Equals(input as WorkflowState);
		}

		/// <summary>
		/// Returns true if WorkflowState instances are equal
		/// </summary>
		/// <param name="input">Instance of WorkflowState to be compared</param>
		/// <returns>Boolean</returns>
		public bool Equals(WorkflowState input)
		{
			if (input == null)
				return false;

			return
				(
					this.Id == input.Id ||
					(this.Id != null &&
					 this.Id.Equals(input.Id))
				) &&
				(
					this.Name == input.Name ||
					(this.Name != null &&
					 this.Name.Equals(input.Name))
				) &&
				(
					this.KeyValue == input.KeyValue ||
					(this.KeyValue != null &&
					 this.KeyValue.Equals(input.KeyValue))
				) &&
				(
					this.Inactive == input.Inactive ||
					(this.Inactive != null &&
					 this.Inactive.Equals(input.Inactive))
				) &&
				(
					this.Escalated == input.Escalated ||
					(this.Escalated != null &&
					 this.Escalated.Equals(input.Escalated))
				) &&
				(
					this.Finished == input.Finished ||
					(this.Finished != null &&
					 this.Finished.Equals(input.Finished))
				) &&
				(
					this.IconCss == input.IconCss ||
					(this.IconCss != null &&
					 this.IconCss.Equals(input.IconCss))
				) &&
				(
					this.IconColor == input.IconColor ||
					(this.IconColor != null &&
					 this.IconColor.Equals(input.IconColor))
				) &&
				(
					this.Actions == input.Actions ||
					this.Actions != null &&
					input.Actions != null &&
					this.Actions.SequenceEqual(input.Actions)
				);
		}

		/// <summary>
		/// Gets the hash code
		/// </summary>
		/// <returns>Hash code</returns>
		public override int GetHashCode()
		{
			unchecked // Overflow is fine, just wrap
			{
				int hashCode = 41;
				if (this.Id != null)
					hashCode = hashCode * 59 + this.Id.GetHashCode();
				if (this.Name != null)
					hashCode = hashCode * 59 + this.Name.GetHashCode();
				if (this.KeyValue != null)
					hashCode = hashCode * 59 + this.KeyValue.GetHashCode();
				if (this.Inactive != null)
					hashCode = hashCode * 59 + this.Inactive.GetHashCode();
				if (this.Escalated != null)
					hashCode = hashCode * 59 + this.Escalated.GetHashCode();
				if (this.Finished != null)
					hashCode = hashCode * 59 + this.Finished.GetHashCode();
				if (this.IconCss != null)
					hashCode = hashCode * 59 + this.IconCss.GetHashCode();
				if (this.IconColor != null)
					hashCode = hashCode * 59 + this.IconColor.GetHashCode();
				if (this.Actions != null)
					hashCode = hashCode * 59 + this.Actions.GetHashCode();
				return hashCode;
			}
		}

		/// <summary>
		/// To validate all properties of the instance
		/// </summary>
		/// <param name="validationContext">Validation context</param>
		/// <returns>Validation Result</returns>
		IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
		{
			yield break;
		}
	}
}