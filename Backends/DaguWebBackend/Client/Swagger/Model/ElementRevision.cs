/*
 * iTWOsite REST Service
 *
 * Public API Documentation
 *
 * OpenAPI spec version: 3.5.4 E
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using System;
using System.Linq;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;
using SwaggerDateConverter = Levelbuild.Backend.DaguWebBackend.Client.Swagger.Client.SwaggerDateConverter;

namespace Levelbuild.Backend.DaguWebBackend.Client.Swagger.Model
{
	/// <summary>
	/// ElementRevision
	/// </summary>
	[DataContract]
	public partial class ElementRevision : IEquatable<ElementRevision>, IValidatableObject
	{
		/// <summary>
		/// Initializes a new instance of the <see cref="ElementRevision" /> class.
		/// </summary>
		public ElementRevision()
		{
		}

		/// <summary>
		/// Gets or Sets Id
		/// </summary>
		[DataMember(Name = "id", EmitDefaultValue = false)]
		public string Id { get; private set; }

		/// <summary>
		/// Gets or Sets RevisionNumber
		/// </summary>
		[DataMember(Name = "revisionNumber", EmitDefaultValue = false)]
		public int? RevisionNumber { get; private set; }

		/// <summary>
		/// Gets or Sets Date
		/// </summary>
		[DataMember(Name = "date", EmitDefaultValue = false)]
		public DateTime? Date { get; private set; }

		/// <summary>
		/// Gets or Sets Username
		/// </summary>
		[DataMember(Name = "username", EmitDefaultValue = false)]
		public string Username { get; private set; }

		/// <summary>
		/// Gets or Sets Comment
		/// </summary>
		[DataMember(Name = "comment", EmitDefaultValue = false)]
		public string Comment { get; private set; }

		/// <summary>
		/// Returns the string presentation of the object
		/// </summary>
		/// <returns>String presentation of the object</returns>
		public override string ToString()
		{
			var sb = new StringBuilder();
			sb.Append("class ElementRevision {\n");
			sb.Append("  Id: ").Append(Id).Append("\n");
			sb.Append("  RevisionNumber: ").Append(RevisionNumber).Append("\n");
			sb.Append("  Date: ").Append(Date).Append("\n");
			sb.Append("  Username: ").Append(Username).Append("\n");
			sb.Append("  Comment: ").Append(Comment).Append("\n");
			sb.Append("}\n");
			return sb.ToString();
		}

		/// <summary>
		/// Returns the JSON string presentation of the object
		/// </summary>
		/// <returns>JSON string presentation of the object</returns>
		public virtual string ToJson()
		{
			return JsonConvert.SerializeObject(this, Formatting.Indented);
		}

		/// <summary>
		/// Returns true if objects are equal
		/// </summary>
		/// <param name="input">Object to be compared</param>
		/// <returns>Boolean</returns>
		public override bool Equals(object input)
		{
			return this.Equals(input as ElementRevision);
		}

		/// <summary>
		/// Returns true if ElementRevision instances are equal
		/// </summary>
		/// <param name="input">Instance of ElementRevision to be compared</param>
		/// <returns>Boolean</returns>
		public bool Equals(ElementRevision input)
		{
			if (input == null)
				return false;

			return
				(
					this.Id == input.Id ||
					(this.Id != null &&
					 this.Id.Equals(input.Id))
				) &&
				(
					this.RevisionNumber == input.RevisionNumber ||
					(this.RevisionNumber != null &&
					 this.RevisionNumber.Equals(input.RevisionNumber))
				) &&
				(
					this.Date == input.Date ||
					(this.Date != null &&
					 this.Date.Equals(input.Date))
				) &&
				(
					this.Username == input.Username ||
					(this.Username != null &&
					 this.Username.Equals(input.Username))
				) &&
				(
					this.Comment == input.Comment ||
					(this.Comment != null &&
					 this.Comment.Equals(input.Comment))
				);
		}

		/// <summary>
		/// Gets the hash code
		/// </summary>
		/// <returns>Hash code</returns>
		public override int GetHashCode()
		{
			unchecked // Overflow is fine, just wrap
			{
				int hashCode = 41;
				if (this.Id != null)
					hashCode = hashCode * 59 + this.Id.GetHashCode();
				if (this.RevisionNumber != null)
					hashCode = hashCode * 59 + this.RevisionNumber.GetHashCode();
				if (this.Date != null)
					hashCode = hashCode * 59 + this.Date.GetHashCode();
				if (this.Username != null)
					hashCode = hashCode * 59 + this.Username.GetHashCode();
				if (this.Comment != null)
					hashCode = hashCode * 59 + this.Comment.GetHashCode();
				return hashCode;
			}
		}

		/// <summary>
		/// To validate all properties of the instance
		/// </summary>
		/// <param name="validationContext">Validation context</param>
		/// <returns>Validation Result</returns>
		IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
		{
			yield break;
		}
	}
}