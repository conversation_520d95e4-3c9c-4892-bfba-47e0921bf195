/*
 * iTWOsite REST Service
 *
 * Public API Documentation
 *
 * OpenAPI spec version: 3.5.4 E
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using System;
using System.Linq;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;
using SwaggerDateConverter = Levelbuild.Backend.DaguWebBackend.Client.Swagger.Client.SwaggerDateConverter;

namespace Levelbuild.Backend.DaguWebBackend.Client.Swagger.Model
{
	/// <summary>
	/// MaskField
	/// </summary>
	[DataContract]
	public partial class MaskField : IEquatable<MaskField>, IValidatableObject
	{
		/// <summary>
		/// Defines NstSortDirection
		/// </summary>
		[JsonConverter(typeof(StringEnumConverter))]
		public enum NstSortDirectionEnum
		{
			/// <summary>
			/// Enum ASC for value: ASC
			/// </summary>
			[EnumMember(Value = "ASC")]
			ASC = 1,

			/// <summary>
			/// Enum DESC for value: DESC
			/// </summary>
			[EnumMember(Value = "DESC")]
			DESC = 2
		}

		/// <summary>
		/// Gets or Sets NstSortDirection
		/// </summary>
		[DataMember(Name = "nstSortDirection", EmitDefaultValue = false)]
		public NstSortDirectionEnum? NstSortDirection { get; set; }

		/// <summary>
		/// Defines SelectButtonOrientation
		/// </summary>
		[JsonConverter(typeof(StringEnumConverter))]
		public enum SelectButtonOrientationEnum
		{
			/// <summary>
			/// Enum HORIZONTAL for value: HORIZONTAL
			/// </summary>
			[EnumMember(Value = "HORIZONTAL")]
			HORIZONTAL = 1,

			/// <summary>
			/// Enum VERTICAL for value: VERTICAL
			/// </summary>
			[EnumMember(Value = "VERTICAL")]
			VERTICAL = 2
		}

		/// <summary>
		/// Gets or Sets SelectButtonOrientation
		/// </summary>
		[DataMember(Name = "selectButtonOrientation", EmitDefaultValue = false)]
		public SelectButtonOrientationEnum? SelectButtonOrientation { get; set; }

		/// <summary>
		/// Defines Type
		/// </summary>
		[JsonConverter(typeof(StringEnumConverter))]
		public enum TypeEnum
		{
			/// <summary>
			/// Enum DEFAULT for value: DEFAULT
			/// </summary>
			[EnumMember(Value = "DEFAULT")]
			DEFAULT = 1,

			/// <summary>
			/// Enum LINK for value: LINK
			/// </summary>
			[EnumMember(Value = "LINK")]
			LINK = 2,

			/// <summary>
			/// Enum PHONE for value: PHONE
			/// </summary>
			[EnumMember(Value = "PHONE")]
			PHONE = 3,

			/// <summary>
			/// Enum MAIL for value: MAIL
			/// </summary>
			[EnumMember(Value = "MAIL")]
			MAIL = 4,

			/// <summary>
			/// Enum CHECKBOX for value: CHECKBOX
			/// </summary>
			[EnumMember(Value = "CHECKBOX")]
			CHECKBOX = 5,

			/// <summary>
			/// Enum TOGGLE for value: TOGGLE
			/// </summary>
			[EnumMember(Value = "TOGGLE")]
			TOGGLE = 6,

			/// <summary>
			/// Enum ICON for value: ICON
			/// </summary>
			[EnumMember(Value = "ICON")]
			ICON = 7,

			/// <summary>
			/// Enum COLOR for value: COLOR
			/// </summary>
			[EnumMember(Value = "COLOR")]
			COLOR = 8,

			/// <summary>
			/// Enum USTID for value: USTID
			/// </summary>
			[EnumMember(Value = "USTID")]
			USTID = 9,

			/// <summary>
			/// Enum STEUERID for value: STEUERID
			/// </summary>
			[EnumMember(Value = "STEUERID")]
			STEUERID = 10,

			/// <summary>
			/// Enum HTML for value: HTML
			/// </summary>
			[EnumMember(Value = "HTML")]
			HTML = 11,

			/// <summary>
			/// Enum TEXTAREA for value: TEXTAREA
			/// </summary>
			[EnumMember(Value = "TEXTAREA")]
			TEXTAREA = 12,

			/// <summary>
			/// Enum BIC for value: BIC
			/// </summary>
			[EnumMember(Value = "BIC")]
			BIC = 13,

			/// <summary>
			/// Enum IBAN for value: IBAN
			/// </summary>
			[EnumMember(Value = "IBAN")]
			IBAN = 14,

			/// <summary>
			/// Enum STEUERNR for value: STEUERNR
			/// </summary>
			[EnumMember(Value = "STEUERNR")]
			STEUERNR = 15,

			/// <summary>
			/// Enum RVNR for value: RVNR
			/// </summary>
			[EnumMember(Value = "RVNR")]
			RVNR = 16,

			/// <summary>
			/// Enum USER for value: USER
			/// </summary>
			[EnumMember(Value = "USER")]
			USER = 17,

			/// <summary>
			/// Enum GROUP for value: GROUP
			/// </summary>
			[EnumMember(Value = "GROUP")]
			GROUP = 18,

			/// <summary>
			/// Enum GEOCOORDINATE for value: GEOCOORDINATE
			/// </summary>
			[EnumMember(Value = "GEOCOORDINATE")]
			GEOCOORDINATE = 19,

			/// <summary>
			/// Enum ADDRESS for value: ADDRESS
			/// </summary>
			[EnumMember(Value = "ADDRESS")]
			ADDRESS = 20,

			/// <summary>
			/// Enum FORMULA for value: FORMULA
			/// </summary>
			[EnumMember(Value = "FORMULA")]
			FORMULA = 21,

			/// <summary>
			/// Enum UNIT for value: UNIT
			/// </summary>
			[EnumMember(Value = "UNIT")]
			UNIT = 22
		}

		/// <summary>
		/// Gets or Sets Type
		/// </summary>
		[DataMember(Name = "type", EmitDefaultValue = false)]
		public TypeEnum? Type { get; set; }

		/// <summary>
		/// Defines TextAlign
		/// </summary>
		[JsonConverter(typeof(StringEnumConverter))]
		public enum TextAlignEnum
		{
			/// <summary>
			/// Enum LEFT for value: LEFT
			/// </summary>
			[EnumMember(Value = "LEFT")]
			LEFT = 1,

			/// <summary>
			/// Enum RIGHT for value: RIGHT
			/// </summary>
			[EnumMember(Value = "RIGHT")]
			RIGHT = 2,

			/// <summary>
			/// Enum CENTER for value: CENTER
			/// </summary>
			[EnumMember(Value = "CENTER")]
			CENTER = 3,

			/// <summary>
			/// Enum TOP for value: TOP
			/// </summary>
			[EnumMember(Value = "TOP")]
			TOP = 4,

			/// <summary>
			/// Enum BOTTOM for value: BOTTOM
			/// </summary>
			[EnumMember(Value = "BOTTOM")]
			BOTTOM = 5,

			/// <summary>
			/// Enum JUSTIFY for value: JUSTIFY
			/// </summary>
			[EnumMember(Value = "JUSTIFY")]
			JUSTIFY = 6,

			/// <summary>
			/// Enum MIDDLE for value: MIDDLE
			/// </summary>
			[EnumMember(Value = "MIDDLE")]
			MIDDLE = 7
		}

		/// <summary>
		/// Gets or Sets TextAlign
		/// </summary>
		[DataMember(Name = "textAlign", EmitDefaultValue = false)]
		public TextAlignEnum? TextAlign { get; set; }

		/// <summary>
		/// Defines WidthUnity
		/// </summary>
		[JsonConverter(typeof(StringEnumConverter))]
		public enum WidthUnityEnum
		{
			/// <summary>
			/// Enum PERCENT for value: PERCENT
			/// </summary>
			[EnumMember(Value = "PERCENT")]
			PERCENT = 1,

			/// <summary>
			/// Enum PIXEL for value: PIXEL
			/// </summary>
			[EnumMember(Value = "PIXEL")]
			PIXEL = 2
		}

		/// <summary>
		/// Gets or Sets WidthUnity
		/// </summary>
		[DataMember(Name = "widthUnity", EmitDefaultValue = false)]
		public WidthUnityEnum? WidthUnity { get; set; }

		/// <summary>
		/// Initializes a new instance of the <see cref="MaskField" /> class.
		/// </summary>
		/// <param name="separatorId">separatorId.</param>
		/// <param name="maskRow">maskRow.</param>
		/// <param name="maskColumn">maskColumn.</param>
		/// <param name="colspan">colspan.</param>
		/// <param name="rowspan">rowspan.</param>
		/// <param name="textAreaColumns">textAreaColumns.</param>
		/// <param name="disabled">disabled.</param>
		/// <param name="autoComplete">autoComplete.</param>
		/// <param name="hybridMode">hybridMode.</param>
		/// <param name="required">required.</param>
		/// <param name="ecmFieldId">ecmFieldId.</param>
		/// <param name="defaultValue">defaultValue.</param>
		/// <param name="checkAgainstNst">checkAgainstNst.</param>
		/// <param name="nstSortFieldId">nstSortFieldId.</param>
		/// <param name="nstSortDirection">nstSortDirection.</param>
		/// <param name="showSelectButtons">showSelectButtons.</param>
		/// <param name="selectButtonDisplayFieldId">selectButtonDisplayFieldId.</param>
		/// <param name="selectButtonOrientation">selectButtonOrientation.</param>
		/// <param name="selectButtonLimit">selectButtonLimit.</param>
		/// <param name="type">type.</param>
		/// <param name="htmlMaxHeight">htmlMaxHeight.</param>
		/// <param name="unit">unit.</param>
		/// <param name="translate">translate.</param>
		/// <param name="allowClone">allowClone.</param>
		/// <param name="textAlign">textAlign.</param>
		/// <param name="hint">hint.</param>
		/// <param name="label">label.</param>
		/// <param name="placeholder">placeholder.</param>
		/// <param name="cellColor">cellColor.</param>
		/// <param name="formatAsLabel">formatAsLabel.</param>
		/// <param name="validate">validate.</param>
		/// <param name="minLength">minLength.</param>
		/// <param name="maxLength">maxLength.</param>
		/// <param name="allowedSigns">allowedSigns.</param>
		/// <param name="allowUpperCharacters">allowUpperCharacters.</param>
		/// <param name="allowLowerCharacters">allowLowerCharacters.</param>
		/// <param name="allowNumbers">allowNumbers.</param>
		/// <param name="allowWhitespaces">allowWhitespaces.</param>
		/// <param name="multiline">multiline.</param>
		/// <param name="width">width.</param>
		/// <param name="widthUnity">widthUnity.</param>
		/// <param name="columns">columns.</param>
		/// <param name="constraints">constraints.</param>
		/// <param name="autoFills">autoFills.</param>
		/// <param name="validationConditions">validationConditions.</param>
		/// <param name="valueRangeFrom">valueRangeFrom.</param>
		/// <param name="valueRangeTo">valueRangeTo.</param>
		public MaskField(long? separatorId = default(long?), int? maskRow = default(int?), int? maskColumn = default(int?), int? colspan = default(int?),
						 int? rowspan = default(int?), int? textAreaColumns = default(int?), bool? disabled = default(bool?),
						 bool? autoComplete = default(bool?), bool? hybridMode = default(bool?), bool? required = default(bool?),
						 long? ecmFieldId = default(long?), string defaultValue = default(string), bool? checkAgainstNst = default(bool?),
						 long? nstSortFieldId = default(long?), NstSortDirectionEnum? nstSortDirection = default(NstSortDirectionEnum?),
						 bool? showSelectButtons = default(bool?), long? selectButtonDisplayFieldId = default(long?),
						 SelectButtonOrientationEnum? selectButtonOrientation = default(SelectButtonOrientationEnum?), int? selectButtonLimit = default(int?),
						 TypeEnum? type = default(TypeEnum?), int? htmlMaxHeight = default(int?), string unit = default(string),
						 bool? translate = default(bool?), bool? allowClone = default(bool?), TextAlignEnum? textAlign = default(TextAlignEnum?),
						 string hint = default(string), string label = default(string), string placeholder = default(string),
						 string cellColor = default(string), bool? formatAsLabel = default(bool?), bool? validate = default(bool?),
						 int? minLength = default(int?), int? maxLength = default(int?), string allowedSigns = default(string),
						 bool? allowUpperCharacters = default(bool?), bool? allowLowerCharacters = default(bool?), bool? allowNumbers = default(bool?),
						 bool? allowWhitespaces = default(bool?), bool? multiline = default(bool?), int? width = default(int?),
						 WidthUnityEnum? widthUnity = default(WidthUnityEnum?), List<MaskFieldColumn> columns = default(List<MaskFieldColumn>),
						 List<MaskFieldConstraint> constraints = default(List<MaskFieldConstraint>),
						 List<MaskFieldAutoFill> autoFills = default(List<MaskFieldAutoFill>),
						 List<MaskFieldValidationCondition> validationConditions = default(List<MaskFieldValidationCondition>),
						 float? valueRangeFrom = default(float?), float? valueRangeTo = default(float?))
		{
			this.SeparatorId = separatorId;
			this.MaskRow = maskRow;
			this.MaskColumn = maskColumn;
			this.Colspan = colspan;
			this.Rowspan = rowspan;
			this.TextAreaColumns = textAreaColumns;
			this.Disabled = disabled;
			this.AutoComplete = autoComplete;
			this.HybridMode = hybridMode;
			this.Required = required;
			this.EcmFieldId = ecmFieldId;
			this.DefaultValue = defaultValue;
			this.CheckAgainstNst = checkAgainstNst;
			this.NstSortFieldId = nstSortFieldId;
			this.NstSortDirection = nstSortDirection;
			this.ShowSelectButtons = showSelectButtons;
			this.SelectButtonDisplayFieldId = selectButtonDisplayFieldId;
			this.SelectButtonOrientation = selectButtonOrientation;
			this.SelectButtonLimit = selectButtonLimit;
			this.Type = type;
			this.HtmlMaxHeight = htmlMaxHeight;
			this.Unit = unit;
			this.Translate = translate;
			this.AllowClone = allowClone;
			this.TextAlign = textAlign;
			this.Hint = hint;
			this.Label = label;
			this.Placeholder = placeholder;
			this.CellColor = cellColor;
			this.FormatAsLabel = formatAsLabel;
			this.Validate = validate;
			this.MinLength = minLength;
			this.MaxLength = maxLength;
			this.AllowedSigns = allowedSigns;
			this.AllowUpperCharacters = allowUpperCharacters;
			this.AllowLowerCharacters = allowLowerCharacters;
			this.AllowNumbers = allowNumbers;
			this.AllowWhitespaces = allowWhitespaces;
			this.Multiline = multiline;
			this.Width = width;
			this.WidthUnity = widthUnity;
			this.Columns = columns;
			this.Constraints = constraints;
			this.AutoFills = autoFills;
			this.ValidationConditions = validationConditions;
			this.ValueRangeFrom = valueRangeFrom;
			this.ValueRangeTo = valueRangeTo;
		}

		/// <summary>
		/// Gets or Sets Id
		/// </summary>
		[DataMember(Name = "id", EmitDefaultValue = false)]
		public long? Id { get; private set; }

		/// <summary>
		/// Gets or Sets SeparatorId
		/// </summary>
		[DataMember(Name = "separatorId", EmitDefaultValue = false)]
		public long? SeparatorId { get; set; }

		/// <summary>
		/// Gets or Sets MaskRow
		/// </summary>
		[DataMember(Name = "maskRow", EmitDefaultValue = false)]
		public int? MaskRow { get; set; }

		/// <summary>
		/// Gets or Sets MaskColumn
		/// </summary>
		[DataMember(Name = "maskColumn", EmitDefaultValue = false)]
		public int? MaskColumn { get; set; }

		/// <summary>
		/// Gets or Sets Colspan
		/// </summary>
		[DataMember(Name = "colspan", EmitDefaultValue = false)]
		public int? Colspan { get; set; }

		/// <summary>
		/// Gets or Sets Rowspan
		/// </summary>
		[DataMember(Name = "rowspan", EmitDefaultValue = false)]
		public int? Rowspan { get; set; }

		/// <summary>
		/// Gets or Sets TextAreaColumns
		/// </summary>
		[DataMember(Name = "textAreaColumns", EmitDefaultValue = false)]
		public int? TextAreaColumns { get; set; }

		/// <summary>
		/// Gets or Sets Disabled
		/// </summary>
		[DataMember(Name = "disabled", EmitDefaultValue = false)]
		public bool? Disabled { get; set; }

		/// <summary>
		/// Gets or Sets AutoComplete
		/// </summary>
		[DataMember(Name = "autoComplete", EmitDefaultValue = false)]
		public bool? AutoComplete { get; set; }

		/// <summary>
		/// Gets or Sets HybridMode
		/// </summary>
		[DataMember(Name = "hybridMode", EmitDefaultValue = false)]
		public bool? HybridMode { get; set; }

		/// <summary>
		/// Gets or Sets Required
		/// </summary>
		[DataMember(Name = "required", EmitDefaultValue = false)]
		public bool? Required { get; set; }

		/// <summary>
		/// Gets or Sets EcmFieldId
		/// </summary>
		[DataMember(Name = "ecmFieldId", EmitDefaultValue = false)]
		public long? EcmFieldId { get; set; }

		/// <summary>
		/// Gets or Sets DefaultValue
		/// </summary>
		[DataMember(Name = "defaultValue", EmitDefaultValue = false)]
		public string DefaultValue { get; set; }

		/// <summary>
		/// Gets or Sets CheckAgainstNst
		/// </summary>
		[DataMember(Name = "checkAgainstNst", EmitDefaultValue = false)]
		public bool? CheckAgainstNst { get; set; }

		/// <summary>
		/// Gets or Sets NstSortFieldId
		/// </summary>
		[DataMember(Name = "nstSortFieldId", EmitDefaultValue = false)]
		public long? NstSortFieldId { get; set; }


		/// <summary>
		/// Gets or Sets ShowSelectButtons
		/// </summary>
		[DataMember(Name = "showSelectButtons", EmitDefaultValue = false)]
		public bool? ShowSelectButtons { get; set; }

		/// <summary>
		/// Gets or Sets SelectButtonDisplayFieldId
		/// </summary>
		[DataMember(Name = "selectButtonDisplayFieldId", EmitDefaultValue = false)]
		public long? SelectButtonDisplayFieldId { get; set; }


		/// <summary>
		/// Gets or Sets SelectButtonLimit
		/// </summary>
		[DataMember(Name = "selectButtonLimit", EmitDefaultValue = false)]
		public int? SelectButtonLimit { get; set; }


		/// <summary>
		/// Gets or Sets HtmlMaxHeight
		/// </summary>
		[DataMember(Name = "htmlMaxHeight", EmitDefaultValue = false)]
		public int? HtmlMaxHeight { get; set; }

		/// <summary>
		/// Gets or Sets Unit
		/// </summary>
		[DataMember(Name = "unit", EmitDefaultValue = false)]
		public string Unit { get; set; }

		/// <summary>
		/// Gets or Sets Translate
		/// </summary>
		[DataMember(Name = "translate", EmitDefaultValue = false)]
		public bool? Translate { get; set; }

		/// <summary>
		/// Gets or Sets AllowClone
		/// </summary>
		[DataMember(Name = "allowClone", EmitDefaultValue = false)]
		public bool? AllowClone { get; set; }


		/// <summary>
		/// Gets or Sets Hint
		/// </summary>
		[DataMember(Name = "hint", EmitDefaultValue = false)]
		public string Hint { get; set; }

		/// <summary>
		/// Gets or Sets Label
		/// </summary>
		[DataMember(Name = "label", EmitDefaultValue = false)]
		public string Label { get; set; }

		/// <summary>
		/// Gets or Sets Placeholder
		/// </summary>
		[DataMember(Name = "placeholder", EmitDefaultValue = false)]
		public string Placeholder { get; set; }

		/// <summary>
		/// Gets or Sets CellColor
		/// </summary>
		[DataMember(Name = "cellColor", EmitDefaultValue = false)]
		public string CellColor { get; set; }

		/// <summary>
		/// Gets or Sets FormatAsLabel
		/// </summary>
		[DataMember(Name = "formatAsLabel", EmitDefaultValue = false)]
		public bool? FormatAsLabel { get; set; }

		/// <summary>
		/// Gets or Sets Validate
		/// </summary>
		[DataMember(Name = "validate", EmitDefaultValue = false)]
		public bool? Validate { get; set; }

		/// <summary>
		/// Gets or Sets MinLength
		/// </summary>
		[DataMember(Name = "minLength", EmitDefaultValue = false)]
		public int? MinLength { get; set; }

		/// <summary>
		/// Gets or Sets MaxLength
		/// </summary>
		[DataMember(Name = "maxLength", EmitDefaultValue = false)]
		public int? MaxLength { get; set; }

		/// <summary>
		/// Gets or Sets AllowedSigns
		/// </summary>
		[DataMember(Name = "allowedSigns", EmitDefaultValue = false)]
		public string AllowedSigns { get; set; }

		/// <summary>
		/// Gets or Sets AllowUpperCharacters
		/// </summary>
		[DataMember(Name = "allowUpperCharacters", EmitDefaultValue = false)]
		public bool? AllowUpperCharacters { get; set; }

		/// <summary>
		/// Gets or Sets AllowLowerCharacters
		/// </summary>
		[DataMember(Name = "allowLowerCharacters", EmitDefaultValue = false)]
		public bool? AllowLowerCharacters { get; set; }

		/// <summary>
		/// Gets or Sets AllowNumbers
		/// </summary>
		[DataMember(Name = "allowNumbers", EmitDefaultValue = false)]
		public bool? AllowNumbers { get; set; }

		/// <summary>
		/// Gets or Sets AllowWhitespaces
		/// </summary>
		[DataMember(Name = "allowWhitespaces", EmitDefaultValue = false)]
		public bool? AllowWhitespaces { get; set; }

		/// <summary>
		/// Gets or Sets Multiline
		/// </summary>
		[DataMember(Name = "multiline", EmitDefaultValue = false)]
		public bool? Multiline { get; set; }

		/// <summary>
		/// Gets or Sets Width
		/// </summary>
		[DataMember(Name = "width", EmitDefaultValue = false)]
		public int? Width { get; set; }


		/// <summary>
		/// Gets or Sets Columns
		/// </summary>
		[DataMember(Name = "columns", EmitDefaultValue = false)]
		public List<MaskFieldColumn> Columns { get; set; }

		/// <summary>
		/// Gets or Sets Constraints
		/// </summary>
		[DataMember(Name = "constraints", EmitDefaultValue = false)]
		public List<MaskFieldConstraint> Constraints { get; set; }

		/// <summary>
		/// Gets or Sets AutoFills
		/// </summary>
		[DataMember(Name = "autoFills", EmitDefaultValue = false)]
		public List<MaskFieldAutoFill> AutoFills { get; set; }

		/// <summary>
		/// Gets or Sets ValidationConditions
		/// </summary>
		[DataMember(Name = "validationConditions", EmitDefaultValue = false)]
		public List<MaskFieldValidationCondition> ValidationConditions { get; set; }

		/// <summary>
		/// Gets or Sets ValueRangeFrom
		/// </summary>
		[DataMember(Name = "valueRangeFrom", EmitDefaultValue = false)]
		public float? ValueRangeFrom { get; set; }

		/// <summary>
		/// Gets or Sets ValueRangeTo
		/// </summary>
		[DataMember(Name = "valueRangeTo", EmitDefaultValue = false)]
		public float? ValueRangeTo { get; set; }

		/// <summary>
		/// Returns the string presentation of the object
		/// </summary>
		/// <returns>String presentation of the object</returns>
		public override string ToString()
		{
			var sb = new StringBuilder();
			sb.Append("class MaskField {\n");
			sb.Append("  Id: ").Append(Id).Append("\n");
			sb.Append("  SeparatorId: ").Append(SeparatorId).Append("\n");
			sb.Append("  MaskRow: ").Append(MaskRow).Append("\n");
			sb.Append("  MaskColumn: ").Append(MaskColumn).Append("\n");
			sb.Append("  Colspan: ").Append(Colspan).Append("\n");
			sb.Append("  Rowspan: ").Append(Rowspan).Append("\n");
			sb.Append("  TextAreaColumns: ").Append(TextAreaColumns).Append("\n");
			sb.Append("  Disabled: ").Append(Disabled).Append("\n");
			sb.Append("  AutoComplete: ").Append(AutoComplete).Append("\n");
			sb.Append("  HybridMode: ").Append(HybridMode).Append("\n");
			sb.Append("  Required: ").Append(Required).Append("\n");
			sb.Append("  EcmFieldId: ").Append(EcmFieldId).Append("\n");
			sb.Append("  DefaultValue: ").Append(DefaultValue).Append("\n");
			sb.Append("  CheckAgainstNst: ").Append(CheckAgainstNst).Append("\n");
			sb.Append("  NstSortFieldId: ").Append(NstSortFieldId).Append("\n");
			sb.Append("  NstSortDirection: ").Append(NstSortDirection).Append("\n");
			sb.Append("  ShowSelectButtons: ").Append(ShowSelectButtons).Append("\n");
			sb.Append("  SelectButtonDisplayFieldId: ").Append(SelectButtonDisplayFieldId).Append("\n");
			sb.Append("  SelectButtonOrientation: ").Append(SelectButtonOrientation).Append("\n");
			sb.Append("  SelectButtonLimit: ").Append(SelectButtonLimit).Append("\n");
			sb.Append("  Type: ").Append(Type).Append("\n");
			sb.Append("  HtmlMaxHeight: ").Append(HtmlMaxHeight).Append("\n");
			sb.Append("  Unit: ").Append(Unit).Append("\n");
			sb.Append("  Translate: ").Append(Translate).Append("\n");
			sb.Append("  AllowClone: ").Append(AllowClone).Append("\n");
			sb.Append("  TextAlign: ").Append(TextAlign).Append("\n");
			sb.Append("  Hint: ").Append(Hint).Append("\n");
			sb.Append("  Label: ").Append(Label).Append("\n");
			sb.Append("  Placeholder: ").Append(Placeholder).Append("\n");
			sb.Append("  CellColor: ").Append(CellColor).Append("\n");
			sb.Append("  FormatAsLabel: ").Append(FormatAsLabel).Append("\n");
			sb.Append("  Validate: ").Append(Validate).Append("\n");
			sb.Append("  MinLength: ").Append(MinLength).Append("\n");
			sb.Append("  MaxLength: ").Append(MaxLength).Append("\n");
			sb.Append("  AllowedSigns: ").Append(AllowedSigns).Append("\n");
			sb.Append("  AllowUpperCharacters: ").Append(AllowUpperCharacters).Append("\n");
			sb.Append("  AllowLowerCharacters: ").Append(AllowLowerCharacters).Append("\n");
			sb.Append("  AllowNumbers: ").Append(AllowNumbers).Append("\n");
			sb.Append("  AllowWhitespaces: ").Append(AllowWhitespaces).Append("\n");
			sb.Append("  Multiline: ").Append(Multiline).Append("\n");
			sb.Append("  Width: ").Append(Width).Append("\n");
			sb.Append("  WidthUnity: ").Append(WidthUnity).Append("\n");
			sb.Append("  Columns: ").Append(Columns).Append("\n");
			sb.Append("  Constraints: ").Append(Constraints).Append("\n");
			sb.Append("  AutoFills: ").Append(AutoFills).Append("\n");
			sb.Append("  ValidationConditions: ").Append(ValidationConditions).Append("\n");
			sb.Append("  ValueRangeFrom: ").Append(ValueRangeFrom).Append("\n");
			sb.Append("  ValueRangeTo: ").Append(ValueRangeTo).Append("\n");
			sb.Append("}\n");
			return sb.ToString();
		}

		/// <summary>
		/// Returns the JSON string presentation of the object
		/// </summary>
		/// <returns>JSON string presentation of the object</returns>
		public virtual string ToJson()
		{
			return JsonConvert.SerializeObject(this, Formatting.Indented);
		}

		/// <summary>
		/// Returns true if objects are equal
		/// </summary>
		/// <param name="input">Object to be compared</param>
		/// <returns>Boolean</returns>
		public override bool Equals(object input)
		{
			return this.Equals(input as MaskField);
		}

		/// <summary>
		/// Returns true if MaskField instances are equal
		/// </summary>
		/// <param name="input">Instance of MaskField to be compared</param>
		/// <returns>Boolean</returns>
		public bool Equals(MaskField input)
		{
			if (input == null)
				return false;

			return
				(
					this.Id == input.Id ||
					(this.Id != null &&
					 this.Id.Equals(input.Id))
				) &&
				(
					this.SeparatorId == input.SeparatorId ||
					(this.SeparatorId != null &&
					 this.SeparatorId.Equals(input.SeparatorId))
				) &&
				(
					this.MaskRow == input.MaskRow ||
					(this.MaskRow != null &&
					 this.MaskRow.Equals(input.MaskRow))
				) &&
				(
					this.MaskColumn == input.MaskColumn ||
					(this.MaskColumn != null &&
					 this.MaskColumn.Equals(input.MaskColumn))
				) &&
				(
					this.Colspan == input.Colspan ||
					(this.Colspan != null &&
					 this.Colspan.Equals(input.Colspan))
				) &&
				(
					this.Rowspan == input.Rowspan ||
					(this.Rowspan != null &&
					 this.Rowspan.Equals(input.Rowspan))
				) &&
				(
					this.TextAreaColumns == input.TextAreaColumns ||
					(this.TextAreaColumns != null &&
					 this.TextAreaColumns.Equals(input.TextAreaColumns))
				) &&
				(
					this.Disabled == input.Disabled ||
					(this.Disabled != null &&
					 this.Disabled.Equals(input.Disabled))
				) &&
				(
					this.AutoComplete == input.AutoComplete ||
					(this.AutoComplete != null &&
					 this.AutoComplete.Equals(input.AutoComplete))
				) &&
				(
					this.HybridMode == input.HybridMode ||
					(this.HybridMode != null &&
					 this.HybridMode.Equals(input.HybridMode))
				) &&
				(
					this.Required == input.Required ||
					(this.Required != null &&
					 this.Required.Equals(input.Required))
				) &&
				(
					this.EcmFieldId == input.EcmFieldId ||
					(this.EcmFieldId != null &&
					 this.EcmFieldId.Equals(input.EcmFieldId))
				) &&
				(
					this.DefaultValue == input.DefaultValue ||
					(this.DefaultValue != null &&
					 this.DefaultValue.Equals(input.DefaultValue))
				) &&
				(
					this.CheckAgainstNst == input.CheckAgainstNst ||
					(this.CheckAgainstNst != null &&
					 this.CheckAgainstNst.Equals(input.CheckAgainstNst))
				) &&
				(
					this.NstSortFieldId == input.NstSortFieldId ||
					(this.NstSortFieldId != null &&
					 this.NstSortFieldId.Equals(input.NstSortFieldId))
				) &&
				(
					this.NstSortDirection == input.NstSortDirection ||
					(this.NstSortDirection != null &&
					 this.NstSortDirection.Equals(input.NstSortDirection))
				) &&
				(
					this.ShowSelectButtons == input.ShowSelectButtons ||
					(this.ShowSelectButtons != null &&
					 this.ShowSelectButtons.Equals(input.ShowSelectButtons))
				) &&
				(
					this.SelectButtonDisplayFieldId == input.SelectButtonDisplayFieldId ||
					(this.SelectButtonDisplayFieldId != null &&
					 this.SelectButtonDisplayFieldId.Equals(input.SelectButtonDisplayFieldId))
				) &&
				(
					this.SelectButtonOrientation == input.SelectButtonOrientation ||
					(this.SelectButtonOrientation != null &&
					 this.SelectButtonOrientation.Equals(input.SelectButtonOrientation))
				) &&
				(
					this.SelectButtonLimit == input.SelectButtonLimit ||
					(this.SelectButtonLimit != null &&
					 this.SelectButtonLimit.Equals(input.SelectButtonLimit))
				) &&
				(
					this.Type == input.Type ||
					(this.Type != null &&
					 this.Type.Equals(input.Type))
				) &&
				(
					this.HtmlMaxHeight == input.HtmlMaxHeight ||
					(this.HtmlMaxHeight != null &&
					 this.HtmlMaxHeight.Equals(input.HtmlMaxHeight))
				) &&
				(
					this.Unit == input.Unit ||
					(this.Unit != null &&
					 this.Unit.Equals(input.Unit))
				) &&
				(
					this.Translate == input.Translate ||
					(this.Translate != null &&
					 this.Translate.Equals(input.Translate))
				) &&
				(
					this.AllowClone == input.AllowClone ||
					(this.AllowClone != null &&
					 this.AllowClone.Equals(input.AllowClone))
				) &&
				(
					this.TextAlign == input.TextAlign ||
					(this.TextAlign != null &&
					 this.TextAlign.Equals(input.TextAlign))
				) &&
				(
					this.Hint == input.Hint ||
					(this.Hint != null &&
					 this.Hint.Equals(input.Hint))
				) &&
				(
					this.Label == input.Label ||
					(this.Label != null &&
					 this.Label.Equals(input.Label))
				) &&
				(
					this.Placeholder == input.Placeholder ||
					(this.Placeholder != null &&
					 this.Placeholder.Equals(input.Placeholder))
				) &&
				(
					this.CellColor == input.CellColor ||
					(this.CellColor != null &&
					 this.CellColor.Equals(input.CellColor))
				) &&
				(
					this.FormatAsLabel == input.FormatAsLabel ||
					(this.FormatAsLabel != null &&
					 this.FormatAsLabel.Equals(input.FormatAsLabel))
				) &&
				(
					this.Validate == input.Validate ||
					(this.Validate != null &&
					 this.Validate.Equals(input.Validate))
				) &&
				(
					this.MinLength == input.MinLength ||
					(this.MinLength != null &&
					 this.MinLength.Equals(input.MinLength))
				) &&
				(
					this.MaxLength == input.MaxLength ||
					(this.MaxLength != null &&
					 this.MaxLength.Equals(input.MaxLength))
				) &&
				(
					this.AllowedSigns == input.AllowedSigns ||
					(this.AllowedSigns != null &&
					 this.AllowedSigns.Equals(input.AllowedSigns))
				) &&
				(
					this.AllowUpperCharacters == input.AllowUpperCharacters ||
					(this.AllowUpperCharacters != null &&
					 this.AllowUpperCharacters.Equals(input.AllowUpperCharacters))
				) &&
				(
					this.AllowLowerCharacters == input.AllowLowerCharacters ||
					(this.AllowLowerCharacters != null &&
					 this.AllowLowerCharacters.Equals(input.AllowLowerCharacters))
				) &&
				(
					this.AllowNumbers == input.AllowNumbers ||
					(this.AllowNumbers != null &&
					 this.AllowNumbers.Equals(input.AllowNumbers))
				) &&
				(
					this.AllowWhitespaces == input.AllowWhitespaces ||
					(this.AllowWhitespaces != null &&
					 this.AllowWhitespaces.Equals(input.AllowWhitespaces))
				) &&
				(
					this.Multiline == input.Multiline ||
					(this.Multiline != null &&
					 this.Multiline.Equals(input.Multiline))
				) &&
				(
					this.Width == input.Width ||
					(this.Width != null &&
					 this.Width.Equals(input.Width))
				) &&
				(
					this.WidthUnity == input.WidthUnity ||
					(this.WidthUnity != null &&
					 this.WidthUnity.Equals(input.WidthUnity))
				) &&
				(
					this.Columns == input.Columns ||
					this.Columns != null &&
					input.Columns != null &&
					this.Columns.SequenceEqual(input.Columns)
				) &&
				(
					this.Constraints == input.Constraints ||
					this.Constraints != null &&
					input.Constraints != null &&
					this.Constraints.SequenceEqual(input.Constraints)
				) &&
				(
					this.AutoFills == input.AutoFills ||
					this.AutoFills != null &&
					input.AutoFills != null &&
					this.AutoFills.SequenceEqual(input.AutoFills)
				) &&
				(
					this.ValidationConditions == input.ValidationConditions ||
					this.ValidationConditions != null &&
					input.ValidationConditions != null &&
					this.ValidationConditions.SequenceEqual(input.ValidationConditions)
				) &&
				(
					this.ValueRangeFrom == input.ValueRangeFrom ||
					(this.ValueRangeFrom != null &&
					 this.ValueRangeFrom.Equals(input.ValueRangeFrom))
				) &&
				(
					this.ValueRangeTo == input.ValueRangeTo ||
					(this.ValueRangeTo != null &&
					 this.ValueRangeTo.Equals(input.ValueRangeTo))
				);
		}

		/// <summary>
		/// Gets the hash code
		/// </summary>
		/// <returns>Hash code</returns>
		public override int GetHashCode()
		{
			unchecked // Overflow is fine, just wrap
			{
				int hashCode = 41;
				if (this.Id != null)
					hashCode = hashCode * 59 + this.Id.GetHashCode();
				if (this.SeparatorId != null)
					hashCode = hashCode * 59 + this.SeparatorId.GetHashCode();
				if (this.MaskRow != null)
					hashCode = hashCode * 59 + this.MaskRow.GetHashCode();
				if (this.MaskColumn != null)
					hashCode = hashCode * 59 + this.MaskColumn.GetHashCode();
				if (this.Colspan != null)
					hashCode = hashCode * 59 + this.Colspan.GetHashCode();
				if (this.Rowspan != null)
					hashCode = hashCode * 59 + this.Rowspan.GetHashCode();
				if (this.TextAreaColumns != null)
					hashCode = hashCode * 59 + this.TextAreaColumns.GetHashCode();
				if (this.Disabled != null)
					hashCode = hashCode * 59 + this.Disabled.GetHashCode();
				if (this.AutoComplete != null)
					hashCode = hashCode * 59 + this.AutoComplete.GetHashCode();
				if (this.HybridMode != null)
					hashCode = hashCode * 59 + this.HybridMode.GetHashCode();
				if (this.Required != null)
					hashCode = hashCode * 59 + this.Required.GetHashCode();
				if (this.EcmFieldId != null)
					hashCode = hashCode * 59 + this.EcmFieldId.GetHashCode();
				if (this.DefaultValue != null)
					hashCode = hashCode * 59 + this.DefaultValue.GetHashCode();
				if (this.CheckAgainstNst != null)
					hashCode = hashCode * 59 + this.CheckAgainstNst.GetHashCode();
				if (this.NstSortFieldId != null)
					hashCode = hashCode * 59 + this.NstSortFieldId.GetHashCode();
				if (this.NstSortDirection != null)
					hashCode = hashCode * 59 + this.NstSortDirection.GetHashCode();
				if (this.ShowSelectButtons != null)
					hashCode = hashCode * 59 + this.ShowSelectButtons.GetHashCode();
				if (this.SelectButtonDisplayFieldId != null)
					hashCode = hashCode * 59 + this.SelectButtonDisplayFieldId.GetHashCode();
				if (this.SelectButtonOrientation != null)
					hashCode = hashCode * 59 + this.SelectButtonOrientation.GetHashCode();
				if (this.SelectButtonLimit != null)
					hashCode = hashCode * 59 + this.SelectButtonLimit.GetHashCode();
				if (this.Type != null)
					hashCode = hashCode * 59 + this.Type.GetHashCode();
				if (this.HtmlMaxHeight != null)
					hashCode = hashCode * 59 + this.HtmlMaxHeight.GetHashCode();
				if (this.Unit != null)
					hashCode = hashCode * 59 + this.Unit.GetHashCode();
				if (this.Translate != null)
					hashCode = hashCode * 59 + this.Translate.GetHashCode();
				if (this.AllowClone != null)
					hashCode = hashCode * 59 + this.AllowClone.GetHashCode();
				if (this.TextAlign != null)
					hashCode = hashCode * 59 + this.TextAlign.GetHashCode();
				if (this.Hint != null)
					hashCode = hashCode * 59 + this.Hint.GetHashCode();
				if (this.Label != null)
					hashCode = hashCode * 59 + this.Label.GetHashCode();
				if (this.Placeholder != null)
					hashCode = hashCode * 59 + this.Placeholder.GetHashCode();
				if (this.CellColor != null)
					hashCode = hashCode * 59 + this.CellColor.GetHashCode();
				if (this.FormatAsLabel != null)
					hashCode = hashCode * 59 + this.FormatAsLabel.GetHashCode();
				if (this.Validate != null)
					hashCode = hashCode * 59 + this.Validate.GetHashCode();
				if (this.MinLength != null)
					hashCode = hashCode * 59 + this.MinLength.GetHashCode();
				if (this.MaxLength != null)
					hashCode = hashCode * 59 + this.MaxLength.GetHashCode();
				if (this.AllowedSigns != null)
					hashCode = hashCode * 59 + this.AllowedSigns.GetHashCode();
				if (this.AllowUpperCharacters != null)
					hashCode = hashCode * 59 + this.AllowUpperCharacters.GetHashCode();
				if (this.AllowLowerCharacters != null)
					hashCode = hashCode * 59 + this.AllowLowerCharacters.GetHashCode();
				if (this.AllowNumbers != null)
					hashCode = hashCode * 59 + this.AllowNumbers.GetHashCode();
				if (this.AllowWhitespaces != null)
					hashCode = hashCode * 59 + this.AllowWhitespaces.GetHashCode();
				if (this.Multiline != null)
					hashCode = hashCode * 59 + this.Multiline.GetHashCode();
				if (this.Width != null)
					hashCode = hashCode * 59 + this.Width.GetHashCode();
				if (this.WidthUnity != null)
					hashCode = hashCode * 59 + this.WidthUnity.GetHashCode();
				if (this.Columns != null)
					hashCode = hashCode * 59 + this.Columns.GetHashCode();
				if (this.Constraints != null)
					hashCode = hashCode * 59 + this.Constraints.GetHashCode();
				if (this.AutoFills != null)
					hashCode = hashCode * 59 + this.AutoFills.GetHashCode();
				if (this.ValidationConditions != null)
					hashCode = hashCode * 59 + this.ValidationConditions.GetHashCode();
				if (this.ValueRangeFrom != null)
					hashCode = hashCode * 59 + this.ValueRangeFrom.GetHashCode();
				if (this.ValueRangeTo != null)
					hashCode = hashCode * 59 + this.ValueRangeTo.GetHashCode();
				return hashCode;
			}
		}

		/// <summary>
		/// To validate all properties of the instance
		/// </summary>
		/// <param name="validationContext">Validation context</param>
		/// <returns>Validation Result</returns>
		IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
		{
			yield break;
		}
	}
}