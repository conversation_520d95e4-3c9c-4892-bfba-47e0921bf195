/*
 * iTWOsite REST Service
 *
 * Public API Documentation
 *
 * OpenAPI spec version: 3.5.4 E
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using System;
using System.Linq;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;
using SwaggerDateConverter = Levelbuild.Backend.DaguWebBackend.Client.Swagger.Client.SwaggerDateConverter;

namespace Levelbuild.Backend.DaguWebBackend.Client.Swagger.Model
{
	/// <summary>
	/// FileInfo
	/// </summary>
	[DataContract]
	public partial class FileInfo : IEquatable<FileInfo>, IValidatableObject
	{
		/// <summary>
		/// Defines Converters
		/// </summary>
		[JsonConverter(typeof(StringEnumConverter))]
		public enum ConvertersEnum
		{
			/// <summary>
			/// Enum MailContent for value: mailContent
			/// </summary>
			[EnumMember(Value = "mailContent")]
			MailContent = 1,

			/// <summary>
			/// Enum AsPDF for value: asPDF
			/// </summary>
			[EnumMember(Value = "asPDF")]
			AsPDF = 2,

			/// <summary>
			/// Enum AsHTML for value: asHTML
			/// </summary>
			[EnumMember(Value = "asHTML")]
			AsHTML = 3
		}

		/// <summary>
		/// Gets or Sets Converters
		/// </summary>
		[DataMember(Name = "converters", EmitDefaultValue = false)]
		public List<ConvertersEnum> Converters { get; set; }

		/// <summary>
		/// Initializes a new instance of the <see cref="FileInfo" /> class.
		/// </summary>
		/// <param name="id">id.</param>
		/// <param name="tempId">tempId.</param>
		/// <param name="contentType">contentType.</param>
		/// <param name="converters">converters.</param>
		public FileInfo(string id = default(string), string tempId = default(string), string contentType = default(string),
						List<ConvertersEnum> converters = default(List<ConvertersEnum>))
		{
			this.Id = id;
			this.TempId = tempId;
			this.ContentType = contentType;
			this.Converters = converters;
		}

		/// <summary>
		/// Gets or Sets Id
		/// </summary>
		[DataMember(Name = "id", EmitDefaultValue = false)]
		public string Id { get; set; }

		/// <summary>
		/// Gets or Sets TempId
		/// </summary>
		[DataMember(Name = "tempId", EmitDefaultValue = false)]
		public string TempId { get; set; }

		/// <summary>
		/// Gets or Sets Name
		/// </summary>
		[DataMember(Name = "name", EmitDefaultValue = false)]
		public string Name { get; private set; }

		/// <summary>
		/// Gets or Sets ContentType
		/// </summary>
		[DataMember(Name = "contentType", EmitDefaultValue = false)]
		public string ContentType { get; set; }

		/// <summary>
		/// Gets or Sets Size
		/// </summary>
		[DataMember(Name = "size", EmitDefaultValue = false)]
		public long? Size { get; private set; }


		/// <summary>
		/// Returns the string presentation of the object
		/// </summary>
		/// <returns>String presentation of the object</returns>
		public override string ToString()
		{
			var sb = new StringBuilder();
			sb.Append("class FileInfo {\n");
			sb.Append("  Id: ").Append(Id).Append("\n");
			sb.Append("  TempId: ").Append(TempId).Append("\n");
			sb.Append("  Name: ").Append(Name).Append("\n");
			sb.Append("  ContentType: ").Append(ContentType).Append("\n");
			sb.Append("  Size: ").Append(Size).Append("\n");
			sb.Append("  Converters: ").Append(Converters).Append("\n");
			sb.Append("}\n");
			return sb.ToString();
		}

		/// <summary>
		/// Returns the JSON string presentation of the object
		/// </summary>
		/// <returns>JSON string presentation of the object</returns>
		public virtual string ToJson()
		{
			return JsonConvert.SerializeObject(this, Formatting.Indented);
		}

		/// <summary>
		/// Returns true if objects are equal
		/// </summary>
		/// <param name="input">Object to be compared</param>
		/// <returns>Boolean</returns>
		public override bool Equals(object input)
		{
			return this.Equals(input as FileInfo);
		}

		/// <summary>
		/// Returns true if FileInfo instances are equal
		/// </summary>
		/// <param name="input">Instance of FileInfo to be compared</param>
		/// <returns>Boolean</returns>
		public bool Equals(FileInfo input)
		{
			if (input == null)
				return false;

			return
				(
					this.Id == input.Id ||
					(this.Id != null &&
					 this.Id.Equals(input.Id))
				) &&
				(
					this.TempId == input.TempId ||
					(this.TempId != null &&
					 this.TempId.Equals(input.TempId))
				) &&
				(
					this.Name == input.Name ||
					(this.Name != null &&
					 this.Name.Equals(input.Name))
				) &&
				(
					this.ContentType == input.ContentType ||
					(this.ContentType != null &&
					 this.ContentType.Equals(input.ContentType))
				) &&
				(
					this.Size == input.Size ||
					(this.Size != null &&
					 this.Size.Equals(input.Size))
				) &&
				(
					this.Converters == input.Converters ||
					this.Converters != null &&
					input.Converters != null &&
					this.Converters.SequenceEqual(input.Converters)
				);
		}

		/// <summary>
		/// Gets the hash code
		/// </summary>
		/// <returns>Hash code</returns>
		public override int GetHashCode()
		{
			unchecked // Overflow is fine, just wrap
			{
				int hashCode = 41;
				if (this.Id != null)
					hashCode = hashCode * 59 + this.Id.GetHashCode();
				if (this.TempId != null)
					hashCode = hashCode * 59 + this.TempId.GetHashCode();
				if (this.Name != null)
					hashCode = hashCode * 59 + this.Name.GetHashCode();
				if (this.ContentType != null)
					hashCode = hashCode * 59 + this.ContentType.GetHashCode();
				if (this.Size != null)
					hashCode = hashCode * 59 + this.Size.GetHashCode();
				if (this.Converters != null)
					hashCode = hashCode * 59 + this.Converters.GetHashCode();
				return hashCode;
			}
		}

		/// <summary>
		/// To validate all properties of the instance
		/// </summary>
		/// <param name="validationContext">Validation context</param>
		/// <returns>Validation Result</returns>
		IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
		{
			yield break;
		}
	}
}