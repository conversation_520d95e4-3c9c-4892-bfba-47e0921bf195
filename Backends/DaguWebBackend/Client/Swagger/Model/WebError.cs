/*
 * iTWOsite REST Service
 *
 * Public API Documentation
 *
 * OpenAPI spec version: 3.5.4 E
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using System;
using System.Linq;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;
using SwaggerDateConverter = Levelbuild.Backend.DaguWebBackend.Client.Swagger.Client.SwaggerDateConverter;

namespace Levelbuild.Backend.DaguWebBackend.Client.Swagger.Model
{
	/// <summary>
	/// Error message
	/// </summary>
	[DataContract]
	public partial class WebError : IEquatable<WebError>, IValidatableObject
	{
		/// <summary>
		/// Initializes a new instance of the <see cref="WebError" /> class.
		/// </summary>
		/// <param name="uid">uid.</param>
		/// <param name="status">status.</param>
		/// <param name="code">code.</param>
		/// <param name="title">title.</param>
		/// <param name="detail">detail.</param>
		public WebError(string uid = default(string), int? status = default(int?), string code = default(string), string title = default(string),
						string detail = default(string))
		{
			this.Uid = uid;
			this.Status = status;
			this.Code = code;
			this.Title = title;
			this.Detail = detail;
		}

		/// <summary>
		/// Gets or Sets Uid
		/// </summary>
		[DataMember(Name = "uid", EmitDefaultValue = false)]
		public string Uid { get; set; }

		/// <summary>
		/// Gets or Sets Status
		/// </summary>
		[DataMember(Name = "status", EmitDefaultValue = false)]
		public int? Status { get; set; }

		/// <summary>
		/// Gets or Sets Code
		/// </summary>
		[DataMember(Name = "code", EmitDefaultValue = false)]
		public string Code { get; set; }

		/// <summary>
		/// Gets or Sets Title
		/// </summary>
		[DataMember(Name = "title", EmitDefaultValue = false)]
		public string Title { get; set; }

		/// <summary>
		/// Gets or Sets Detail
		/// </summary>
		[DataMember(Name = "detail", EmitDefaultValue = false)]
		public string Detail { get; set; }

		/// <summary>
		/// Returns the string presentation of the object
		/// </summary>
		/// <returns>String presentation of the object</returns>
		public override string ToString()
		{
			var sb = new StringBuilder();
			sb.Append("class WebError {\n");
			sb.Append("  Uid: ").Append(Uid).Append("\n");
			sb.Append("  Status: ").Append(Status).Append("\n");
			sb.Append("  Code: ").Append(Code).Append("\n");
			sb.Append("  Title: ").Append(Title).Append("\n");
			sb.Append("  Detail: ").Append(Detail).Append("\n");
			sb.Append("}\n");
			return sb.ToString();
		}

		/// <summary>
		/// Returns the JSON string presentation of the object
		/// </summary>
		/// <returns>JSON string presentation of the object</returns>
		public virtual string ToJson()
		{
			return JsonConvert.SerializeObject(this, Formatting.Indented);
		}

		/// <summary>
		/// Returns true if objects are equal
		/// </summary>
		/// <param name="input">Object to be compared</param>
		/// <returns>Boolean</returns>
		public override bool Equals(object input)
		{
			return this.Equals(input as WebError);
		}

		/// <summary>
		/// Returns true if WebError instances are equal
		/// </summary>
		/// <param name="input">Instance of WebError to be compared</param>
		/// <returns>Boolean</returns>
		public bool Equals(WebError input)
		{
			if (input == null)
				return false;

			return
				(
					this.Uid == input.Uid ||
					(this.Uid != null &&
					 this.Uid.Equals(input.Uid))
				) &&
				(
					this.Status == input.Status ||
					(this.Status != null &&
					 this.Status.Equals(input.Status))
				) &&
				(
					this.Code == input.Code ||
					(this.Code != null &&
					 this.Code.Equals(input.Code))
				) &&
				(
					this.Title == input.Title ||
					(this.Title != null &&
					 this.Title.Equals(input.Title))
				) &&
				(
					this.Detail == input.Detail ||
					(this.Detail != null &&
					 this.Detail.Equals(input.Detail))
				);
		}

		/// <summary>
		/// Gets the hash code
		/// </summary>
		/// <returns>Hash code</returns>
		public override int GetHashCode()
		{
			unchecked // Overflow is fine, just wrap
			{
				int hashCode = 41;
				if (this.Uid != null)
					hashCode = hashCode * 59 + this.Uid.GetHashCode();
				if (this.Status != null)
					hashCode = hashCode * 59 + this.Status.GetHashCode();
				if (this.Code != null)
					hashCode = hashCode * 59 + this.Code.GetHashCode();
				if (this.Title != null)
					hashCode = hashCode * 59 + this.Title.GetHashCode();
				if (this.Detail != null)
					hashCode = hashCode * 59 + this.Detail.GetHashCode();
				return hashCode;
			}
		}

		/// <summary>
		/// To validate all properties of the instance
		/// </summary>
		/// <param name="validationContext">Validation context</param>
		/// <returns>Validation Result</returns>
		IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
		{
			yield break;
		}
	}
}