/*
 * iTWOsite REST Service
 *
 * Public API Documentation
 *
 * OpenAPI spec version: 3.5.4 E
 * Contact: <EMAIL>
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using System;
using System.Reflection;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;

namespace Levelbuild.Backend.DaguWebBackend.Client.Swagger.Client
{
	/// <summary>
	/// <see cref="GlobalConfiguration"/> provides a compile-time extension point for globally configuring
	/// API Clients.
	/// </summary>
	/// <remarks>
	/// A customized implementation via partial class may reside in another file and may
	/// be excluded from automatic generation via a .swagger-codegen-ignore file.
	/// </remarks>
	public partial class GlobalConfiguration : Configuration
	{
	}
}